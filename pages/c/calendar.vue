<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';

import { useActivities } from '~/composables/useActivities';
import { useContacts } from '~/composables/useContacts';
import { useCurrentUser } from '~/composables/useCurrentUser';
import { useFirebase } from '~/composables/useFirebase';
import { space } from '~/composables/space';

// Import components
import CalendarWidget from '~/components/calendar/CalendarWidget.vue';
import CreateActivityModal from '~/components/calendar/CreateActivityModal.vue';
import EditActivityModal from '~/components/calendar/EditActivityModal.vue';
import EventDetailsModal from '~/components/calendar/EventDetailsModal.vue';

// Define page meta
definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// Set page title
useHead({
  title: 'Calendar - Covalonic',
});

// Composables
const { contacts, subscribeToContacts, getFullName } = useContacts();
const { activities, tasks, subscribeToActivities, createActivity, createTask } = useActivities();
const { currentUser } = useCurrentUser();

// State
const currentSpace = useState('currentSpace', () => ({}));

// Calendar data
const calendarEvents = ref<any[]>([]);
const userSubscriptions = ref<any[]>([]);
const recentActivities = ref<any[]>([]);
const isLoading = ref(true);

// Modal states
const showCreateActivityModal = ref(false);
const showEditActivityModal = ref(false);
const showEventDetailsModal = ref(false);
const selectedDate = ref<Date | null>(null);
const selectedEvent = ref<any>(null);

// Filter states
const filterType = ref('all');
const selectedMonth = ref(new Date().getMonth());
const selectedYear = ref(new Date().getFullYear());

// Event types for filtering
const eventTypes = [
  { value: 'all', label: 'All Events', icon: 'mdi:calendar', color: 'from-purple-600 to-pink-600' },
  {
    value: 'payment',
    label: 'Payments',
    icon: 'mdi:currency-usd',
    color: 'from-green-600 to-emerald-600',
  },
  { value: 'activity', label: 'Activities', icon: 'mdi:eye', color: 'from-blue-600 to-cyan-600' },
  {
    value: 'subscription',
    label: 'Subscriptions',
    icon: 'mdi:calendar-clock',
    color: 'from-orange-600 to-red-600',
  },
  { value: 'upload', label: 'Uploads', icon: 'mdi:upload', color: 'from-indigo-600 to-purple-600' },
  {
    value: 'task',
    label: 'Tasks',
    icon: 'mdi:check-circle',
    color: 'from-indigo-600 to-purple-600',
  },
  {
    value: 'meeting',
    label: 'Meetings',
    icon: 'mdi:account-group',
    color: 'from-yellow-600 to-orange-600',
  },
  { value: 'call', label: 'Calls', icon: 'mdi:phone', color: 'from-teal-600 to-blue-600' },
];

// Calendar event handlers
const handleDayClick = (date: Date) => {
  selectedDate.value = date;
  showCreateActivityModal.value = true;
};

const handleEventClick = (event: any) => {
  // Show event details modal for any event
  selectedEvent.value = event;
  showEventDetailsModal.value = true;
};

const handleActivityCreated = () => {
  showCreateActivityModal.value = false;
  selectedDate.value = null;
  // Data will be updated automatically through subscriptions
};

const handleActivityUpdated = () => {
  showEditActivityModal.value = false;
  selectedEvent.value = null;
  // Data will be updated automatically through subscriptions
};

const handleEditFromDetails = () => {
  // Close details modal and open edit modal
  showEventDetailsModal.value = false;
  if (selectedEvent.value?.editable) {
    showEditActivityModal.value = true;
  }
};

// Fetch user subscriptions
const fetchUserSubscriptions = async () => {
  try {
    const { firestore } = useFirebase();
    if (!firestore) {
      console.error('Firestore is not initialized');
      return;
    }

    const userId = currentUser.value?.uid || currentUser.value?.id;
    if (!userId) return;

    const { collection, query, where, getDocs } = await import('firebase/firestore');

    // Query for user's subscriptions
    const subscriptionsQuery = query(
      collection(firestore, 'ad-subscriptions'),
      where('user_id', '==', userId)
    );

    const subscriptionsSnapshot = await getDocs(subscriptionsQuery);
    const subscriptions: any[] = [];

    subscriptionsSnapshot.forEach(doc => {
      const data = doc.data();
      subscriptions.push({
        id: doc.id,
        ...data,
        nextPaymentDate: data.next_payment_date?.toDate ? data.next_payment_date.toDate() : null,
        startDate: data.start_date?.toDate ? data.start_date.toDate() : null,
        endDate: data.end_date?.toDate ? data.end_date.toDate() : null,
      });
    });

    userSubscriptions.value = subscriptions;

    // Convert subscriptions to calendar events
    const events = subscriptions.flatMap(sub => {
      const events: any[] = [];

      if (sub.nextPaymentDate && sub.status === 'active') {
        events.push({
          id: `payment-${sub.id}`,
          title: `Subscription Payment - ${sub.ad_spot_name || 'Ad Spot'}`,
          date: sub.nextPaymentDate,
          type: 'payment',
          theme: 'green',
          description: `$${sub.price || 0} payment due`,
          icon: 'mdi:currency-usd',
          editable: false,
        });
      }

      if (sub.endDate) {
        events.push({
          id: `end-${sub.id}`,
          title: `Subscription Ends - ${sub.ad_spot_name || 'Ad Spot'}`,
          date: sub.endDate,
          type: 'subscription',
          theme: 'red',
          description: 'Subscription expires',
          icon: 'mdi:calendar-clock',
          editable: false,
        });
      }

      if (sub.startDate) {
        events.push({
          id: `start-${sub.id}`,
          title: `Subscription Started - ${sub.ad_spot_name || 'Ad Spot'}`,
          date: sub.startDate,
          type: 'subscription',
          theme: 'blue',
          description: 'Subscription activated',
          icon: 'mdi:calendar-check',
          editable: false,
        });
      }

      return events;
    });

    calendarEvents.value = [...calendarEvents.value, ...events];
  } catch (error) {
    console.error('Error fetching subscriptions:', error);
  }
};

// Fetch CRM activities and tasks
const fetchCRMData = async () => {
  try {
    if (!currentUser.value?.uid) return;

    // Subscribe to user's activities and tasks with a callback
    subscribeToActivities({ ownerId: currentUser.value.uid }, (updatedActivities) => {
      // This callback runs whenever activities change
      updateCalendarWithActivities();
    });

    // Also call update immediately in case there's already data
    updateCalendarWithActivities();
  } catch (error) {
    console.error('Error fetching CRM data:', error);
  }
};

// Helper function to update calendar with current activities
const updateCalendarWithActivities = () => {
  // Convert activities to calendar events
  const activityEvents = activities.value.map(activity => {
    // Use scheduledDate if available, otherwise fall back to createdAt
    let displayDate = activity.createdAt;
    if ((activity as any).scheduledDate) {
      displayDate = (activity as any).scheduledDate;
    } else if (activity.type === 'meeting' && (activity as any).startTime) {
      displayDate = (activity as any).startTime;
    }
    
    return {
      id: activity.id,
      title: activity.title,
      date: displayDate,
      type: activity.type,
      theme: getActivityTheme(activity.type),
      description: activity.description || '',
      icon: getActivityIcon(activity.type),
      editable: true,
      data: activity,
    };
  });

  // Convert tasks to calendar events
  const taskEvents = tasks.value.map(task => ({
    id: task.id,
    title: task.title,
    date: task.dueDate,
    type: 'task',
    theme: getTaskTheme(task.priority),
    description: task.description || '',
    icon: 'mdi:check-circle',
    editable: true,
    data: task,
  }));

  // Update CRM events in calendar (preserve non-editable events)
  const nonEditableEvents = calendarEvents.value.filter(e => !e.editable);
  calendarEvents.value = [
    ...nonEditableEvents,
    ...activityEvents,
    ...taskEvents,
  ];
};

// Helper functions for activity/task themes
const getActivityTheme = (type: string) => {
  switch (type) {
    case 'call':
      return 'blue';
    case 'email':
      return 'purple';
    case 'meeting':
      return 'yellow';
    case 'task':
      return 'green';
    case 'note':
      return 'gray';
    default:
      return 'blue';
  }
};

const getTaskTheme = (priority: string) => {
  switch (priority) {
    case 'high':
      return 'red';
    case 'medium':
      return 'yellow';
    case 'low':
      return 'green';
    default:
      return 'blue';
  }
};

const getActivityIcon = (type: string) => {
  switch (type) {
    case 'call':
      return 'mdi:phone';
    case 'email':
      return 'mdi:email';
    case 'meeting':
      return 'mdi:account-group';
    case 'task':
      return 'mdi:check-circle';
    case 'note':
      return 'mdi:note-text';
    default:
      return 'mdi:calendar';
  }
};

// Fetch recent activity
const fetchRecentActivity = async () => {
  try {
    const { firestore } = useFirebase();
    if (!firestore) {
      console.error('Firestore is not initialized');
      return;
    }

    const { currentSpace } = space();
    const { currentUser } = useCurrentUser();
    const { collection, query, where, orderBy, limit, getDocs, Timestamp } = await import(
      'firebase/firestore'
    );

    // Ensure user is authenticated before accessing activity data
    if (!currentUser.value?.uid) {
      console.warn('User not authenticated - cannot load activity data');
      return;
    }

    // Require a valid space ID to prevent data leakage
    if (!currentSpace.value?.id) {
      console.warn('No valid space ID - cannot load activity data');
      return;
    }

    const activityQuery = query(
      collection(firestore, 'activity'),
      where('space_id', '==', currentSpace.value.id),
      orderBy('timestamp', 'desc'),
      limit(50)
    );

    const activitySnapshot = await getDocs(activityQuery);
    const activities = [];

    activitySnapshot.forEach(doc => {
      const data = doc.data();
      const activity = {
        id: doc.id,
        type: data.type || 'view',
        content: data.content || 'Unknown',
        title: data.title || 'Unknown',
        timestamp: data.timestamp instanceof Timestamp ? data.timestamp.toDate() : new Date(),
        user: {
          name: data.user?.name || 'Anonymous User',
          avatar: data.user?.avatar || null,
        },
      };
      activities.push(activity);

      // Convert to calendar event
      calendarEvents.value.push({
        id: `activity-${activity.id}`,
        title: `${activity.type === 'upload' ? 'Uploaded' : 'Viewed'} ${activity.content}`,
        date: activity.timestamp,
        type: 'activity',
        theme: activity.type === 'upload' ? 'purple' : 'blue',
        description: activity.title,
        icon: activity.type === 'upload' ? 'mdi:upload' : 'mdi:eye',
        user: activity.user,
      });
    });

    recentActivities.value = activities;
  } catch (error) {
    console.error('Error fetching recent activity:', error);
  }
};

// Load all data
const loadCalendarData = async () => {
  isLoading.value = true;
  calendarEvents.value = []; // Reset events

  try {
    // Subscribe to contacts for activity creation
    if (currentUser.value?.uid) {
      subscribeToContacts({ ownerId: currentUser.value.uid });
    }

    await Promise.all([fetchUserSubscriptions(), fetchCRMData(), fetchRecentActivity()]);
  } catch (error) {
    console.error('Error loading calendar data:', error);
  } finally {
    isLoading.value = false;
  }
};

// Filter events based on type
const filteredEvents = computed(() => {
  if (filterType.value === 'all') {
    return calendarEvents.value;
  }
  return calendarEvents.value.filter(event => event.type === filterType.value);
});

// Export calendar data
const exportCalendar = () => {
  // TODO: Implement calendar export functionality
  console.log('Exporting calendar data...');
};

// Watch for changes in activities and tasks
watch([activities, tasks], () => {
  if (currentUser.value?.uid) {
    updateCalendarWithActivities();
  }
}, { deep: true });

onMounted(() => {
  loadCalendarData();
});
</script>

<template>
  <div class="min-h-screen font-sans relative overflow-hidden">
    <!-- Dark gradient background -->
    <div class="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900"></div>

    <!-- Decorative elements -->
    <div
      class="absolute top-20 right-20 w-96 h-96 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-3xl"
    ></div>
    <div
      class="absolute bottom-40 left-20 w-80 h-80 bg-gradient-to-br from-pink-400/20 to-purple-400/20 rounded-full blur-3xl"
    ></div>
    <div
      class="absolute top-1/2 right-1/3 w-64 h-64 bg-gradient-to-br from-indigo-400/15 to-purple-400/15 rounded-full blur-2xl"
    ></div>

    <div class="relative z-10">
      <!-- Header -->
      <div
        class="bg-gradient-to-br from-gray-900/90 via-gray-800/90 to-gray-900/90 backdrop-blur-sm border-b border-white/10 shadow-2xl"
      >
        <div class="max-w-7xl mx-auto px-6 py-8">
          <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
            <div>
              <div class="flex items-center mb-4">
                <div
                  class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mr-4 shadow-lg"
                >
                  <Icon name="mdi:calendar" class="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 class="text-3xl font-bold text-white">Activity Calendar</h1>
                  <p class="text-purple-400 font-medium">Your Events & Activities Timeline</p>
                </div>
              </div>
              <p class="text-gray-300 max-w-3xl leading-relaxed">
                View all your business activities, subscription payments, and important dates in one
                comprehensive calendar.
              </p>
            </div>
            <div class="mt-6 md:mt-0">
              <button
                class="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-opacity-50 shadow-lg hover:shadow-xl flex items-center"
                @click="exportCalendar"
              >
                <Icon name="mdi:download" class="mr-2" />
                Export Calendar
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Filter Tabs -->
      <div
        class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm border-b border-white/10"
      >
        <div class="max-w-7xl mx-auto px-6 py-4">
          <div class="flex flex-wrap gap-3">
            <button
              v-for="type in eventTypes"
              :key="type.value"
              class="px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200 transform hover:scale-[1.02] flex items-center"
              :class="[
                filterType === type.value
                  ? `bg-gradient-to-r ${type.color} text-white shadow-lg`
                  : 'bg-gray-800/50 text-gray-400 hover:text-white hover:bg-gray-700/50 border border-white/10',
              ]"
              @click="filterType = type.value"
            >
              <Icon :name="type.icon" class="mr-2" />
              {{ type.label }}
            </button>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="max-w-7xl mx-auto px-6 py-8">
        <!-- Loading State -->
        <div v-if="isLoading" class="flex items-center justify-center py-16">
          <div
            class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-xl p-8 border border-white/10 text-center"
          >
            <div
              class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-400 mb-4 mx-auto"
            ></div>
            <p class="text-gray-300">Loading calendar data...</p>
          </div>
        </div>

        <!-- Calendar Component -->
        <div v-else>
          <CalendarWidget
            :events="filteredEvents"
            :activities="recentActivities"
            :subscriptions="userSubscriptions"
            @day-click="handleDayClick"
            @event-click="handleEventClick"
          />

          <!-- Monthly Summary -->
          <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div
              class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-xl p-6 border border-white/10"
            >
              <div class="flex items-center mb-4">
                <div
                  class="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center mr-3 shadow-lg"
                >
                  <Icon name="mdi:currency-usd" class="text-white" />
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-white">Upcoming Payments</h3>
                  <p class="text-sm text-gray-400">This month</p>
                </div>
              </div>
              <div class="text-2xl font-bold text-green-400">
                ${{
                  userSubscriptions
                    .filter(s => s.status === 'active')
                    .reduce((sum, s) => sum + (s.price || 0), 0)
                }}
              </div>
              <p class="text-sm text-gray-400 mt-2">
                {{ userSubscriptions.filter(s => s.status === 'active').length }} active
                subscriptions
              </p>
            </div>

            <div
              class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-xl p-6 border border-white/10"
            >
              <div class="flex items-center mb-4">
                <div
                  class="w-10 h-10 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-lg flex items-center justify-center mr-3 shadow-lg"
                >
                  <Icon name="mdi:check-circle" class="text-white" />
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-white">Active Tasks</h3>
                  <p class="text-sm text-gray-400">Pending completion</p>
                </div>
              </div>
              <div class="text-2xl font-bold text-blue-400">
                {{ tasks.filter(t => t.status === 'pending').length }}
              </div>
              <p class="text-sm text-gray-400 mt-2">CRM follow-ups</p>
            </div>

            <div
              class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-xl p-6 border border-white/10"
            >
              <div class="flex items-center mb-4">
                <div
                  class="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mr-3 shadow-lg"
                >
                  <Icon name="mdi:calendar-check" class="text-white" />
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-white">Total Events</h3>
                  <p class="text-sm text-gray-400">All time</p>
                </div>
              </div>
              <div class="text-2xl font-bold text-purple-400">
                {{ calendarEvents.length }}
              </div>
              <p class="text-sm text-gray-400 mt-2">Tracked events</p>
            </div>
          </div>

          <!-- Recent Events List -->
          <div
            class="mt-8 bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-xl border border-white/10 overflow-hidden"
          >
            <div class="p-6 border-b border-white/10">
              <h3 class="text-xl font-bold text-white">Recent Events</h3>
              <p class="text-gray-400 text-sm mt-1">Your latest activities and upcoming events</p>
            </div>

            <div class="p-6">
              <div class="space-y-4">
                <div
                  v-for="event in filteredEvents.slice(0, 10)"
                  :key="event.id"
                  class="p-4 rounded-lg bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-400/20 hover:border-purple-400/30 transition-all duration-200 cursor-pointer"
                  @click="handleEventClick(event)"
                >
                  <div class="flex items-center justify-between">
                    <div class="flex items-center">
                      <div
                        class="w-10 h-10 rounded-lg flex items-center justify-center mr-4"
                        :class="{
                          'bg-gradient-to-br from-green-500/20 to-emerald-600/20 text-green-400':
                            event.theme === 'green',
                          'bg-gradient-to-br from-blue-500/20 to-cyan-600/20 text-blue-400':
                            event.theme === 'blue',
                          'bg-gradient-to-br from-red-500/20 to-orange-600/20 text-red-400':
                            event.theme === 'red',
                          'bg-gradient-to-br from-purple-500/20 to-pink-600/20 text-purple-400':
                            event.theme === 'purple',
                          'bg-gradient-to-br from-yellow-500/20 to-amber-600/20 text-yellow-400':
                            event.theme === 'yellow',
                          'bg-gradient-to-br from-gray-500/20 to-gray-600/20 text-gray-400':
                            event.theme === 'gray',
                        }"
                      >
                        <Icon :name="event.icon || 'mdi:calendar'" class="text-xl" />
                      </div>
                      <div>
                        <p class="font-medium text-white">{{ event.title }}</p>
                        <p class="text-sm text-gray-400">{{ event.description }}</p>
                      </div>
                    </div>
                    <div class="flex items-center">
                      <div class="text-right mr-3">
                        <p class="text-sm font-medium text-purple-300">
                          {{
                            event.date.toLocaleDateString('en-US', {
                              month: 'short',
                              day: 'numeric',
                            })
                          }}
                        </p>
                        <p class="text-xs text-gray-500">
                          {{
                            event.date.toLocaleTimeString('en-US', {
                              hour: '2-digit',
                              minute: '2-digit',
                            })
                          }}
                        </p>
                      </div>
                      <Icon
                        v-if="event.editable"
                        name="mdi:pencil"
                        class="text-blue-400 w-4 h-4"
                        title="Editable"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Event Details Modal -->
    <EventDetailsModal
      :open="showEventDetailsModal"
      :event="selectedEvent"
      @close="showEventDetailsModal = false"
      @edit="handleEditFromDetails"
    />

    <!-- Activity Creation Modal -->
    <CreateActivityModal
      :open="showCreateActivityModal"
      :contacts="contacts"
      :preselected-date="selectedDate"
      @close="showCreateActivityModal = false"
      @success="handleActivityCreated"
    />

    <!-- Activity/Task Edit Modal -->
    <EditActivityModal
      :open="showEditActivityModal"
      :activity="selectedEvent"
      :contacts="contacts"
      @close="showEditActivityModal = false"
      @success="handleActivityUpdated"
      @delete="handleActivityUpdated"
    />
  </div>
</template>
