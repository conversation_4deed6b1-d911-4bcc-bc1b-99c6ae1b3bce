<template>
  <div class="min-h-screen font-sans relative overflow-hidden">
    <!-- Dark gradient background -->
    <div class="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900"></div>
    
    <!-- Decorative elements -->
    <div class="absolute top-20 right-20 w-96 h-96 bg-gradient-to-br from-red-400/20 to-orange-400/20 rounded-full blur-3xl"></div>
    <div class="absolute bottom-40 left-20 w-80 h-80 bg-gradient-to-br from-orange-400/20 to-red-400/20 rounded-full blur-3xl"></div>
    <div class="absolute top-1/2 right-1/3 w-64 h-64 bg-gradient-to-br from-pink-400/15 to-red-400/15 rounded-full blur-2xl"></div>

    <role-guard role="Admin" :show-access-denied="true" redirect-to="/c/dashboard">
      <div class="relative z-10">
        <!-- Header -->
        <div class="bg-gradient-to-br from-gray-900/90 via-gray-800/90 to-gray-900/90 backdrop-blur-sm border-b border-white/10 shadow-2xl">
          <div class="max-w-7xl mx-auto px-6 py-8">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
              <div>
                <div class="flex items-center mb-4">
                  <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-orange-600 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                    <Icon name="mdi:shield-check" class="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h1 class="text-3xl font-bold text-white">Content Moderation</h1>
                    <p class="text-orange-400 font-medium">Review & Manage User Content</p>
                  </div>
                </div>
                <p class="text-gray-300 max-w-3xl leading-relaxed">
                  Review and manage user-generated content with advanced moderation tools and automated screening.
                </p>
              </div>
              <div class="flex flex-wrap items-center gap-3 mt-6 md:mt-0">
                <NuxtLink to="/c/admin/moderation/queue" class="px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-indigo-400 focus:ring-opacity-50 shadow-lg hover:shadow-xl flex items-center">
                  <Icon name="mdi:playlist-check" class="mr-2" />
                  Review Queue
                </NuxtLink>
                <NuxtLink to="/c/admin/moderation/settings" class="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-opacity-50 shadow-lg hover:shadow-xl flex items-center">
                  <Icon name="mdi:robot" class="mr-2" />
                  Auto-Screening
                </NuxtLink>
                <NuxtLink to="/c/admin" class="px-6 py-3 bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 shadow-lg hover:shadow-xl border border-white/10 flex items-center">
                  <Icon name="mdi:arrow-left" class="mr-2" />
                  Back to Admin
                </NuxtLink>
              </div>
            </div>
          </div>
        </div>

        <div class="max-w-7xl mx-auto px-6 py-8 space-y-8">

          <!-- Loading state -->
          <div v-if="isLoading" class="flex items-center justify-center py-16">
            <div class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-xl p-8 border border-white/10 text-center">
              <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-400 mb-4 mx-auto"></div>
              <p class="text-gray-300">Loading moderation data...</p>
            </div>
          </div>

        <template v-else>
          <!-- Stats Cards -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <AdminStatCard
              title="Pending Content"
              :value="contentStats.pending"
              icon="mdi:clock-outline"
              color="yellow"
              :trend="pendingTrend"
              trend-period="vs last week"
              link-text="View pending"
              link-url="/c/admin/moderation/queue?status=pending"
            />
            <AdminStatCard
              title="Approved Content"
              :value="contentStats.approved"
              icon="mdi:check-circle"
              color="green"
              :trend="approvedTrend"
              trend-period="vs last week"
              link-text="View approved"
              link-url="/c/admin/moderation/queue?status=approved"
            />
            <AdminStatCard
              title="Rejected Content"
              :value="contentStats.rejected"
              icon="mdi:close-circle"
              color="red"
              :trend="rejectedTrend"
              trend-period="vs last week"
              link-text="View rejected"
              link-url="/c/admin/moderation/queue?status=rejected"
            />
            <AdminStatCard
              title="Flagged Content"
              :value="contentStats.flagged"
              icon="mdi:flag"
              color="orange"
              :trend="flaggedTrend"
              trend-period="vs last week"
              link-text="View flagged"
              link-url="/c/admin/moderation/queue?flagged=true"
            />
          </div>

          <!-- Content Distribution Chart -->
          <div class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-2xl border border-white/10 shadow-2xl overflow-hidden">
            <div class="p-6 border-b border-white/10">
              <h3 class="text-xl font-bold text-white flex items-center">
                <div class="w-3 h-3 bg-gradient-to-r from-red-400 to-orange-400 rounded-full mr-2"></div>
                Content Distribution
              </h3>
              <p class="text-gray-400 text-sm mt-1">Breakdown of content by type and moderation status</p>
            </div>
            <div class="p-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- Content Type Distribution -->
                <div class="bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-xl p-6 border border-white/5">
                  <h4 class="text-lg font-medium text-white mb-4 flex items-center">
                    <div class="w-3 h-3 bg-blue-400 rounded-full mr-2"></div>
                    Content by Type
                  </h4>
                  <div class="h-64">
                    <canvas id="contentTypeChart"></canvas>
                  </div>
                </div>

                <!-- Content Status Distribution -->
                <div class="bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-xl p-6 border border-white/5">
                  <h4 class="text-lg font-medium text-white mb-4 flex items-center">
                    <div class="w-3 h-3 bg-green-400 rounded-full mr-2"></div>
                    Content by Status
                  </h4>
                  <div class="h-64">
                    <canvas id="contentStatusChart"></canvas>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Recent Moderation Activity -->
          <div class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-2xl border border-white/10 shadow-2xl overflow-hidden">
            <div class="p-6 border-b border-white/10 flex justify-between items-center">
              <div>
                <h3 class="text-xl font-bold text-white flex items-center">
                  <div class="w-3 h-3 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mr-2"></div>
                  Recent Moderation Activity
                </h3>
                <p class="text-gray-400 text-sm mt-1">Latest content moderation actions and decisions</p>
              </div>
              <NuxtLink to="/c/admin/moderation/logs" class="text-sm text-purple-400 hover:text-purple-300 font-medium transition-colors duration-200">
                View all activity →
              </NuxtLink>
            </div>
            <div>
              <div v-if="recentActivity.length === 0" class="p-12 text-center">
                <Icon name="mdi:history" class="mx-auto h-16 w-16 text-gray-600 mb-4" />
                <h3 class="text-lg font-medium text-white mb-2">No recent activity</h3>
                <p class="text-gray-400">
                  Start moderating content to see activity here.
                </p>
              </div>
              <div v-else class="divide-y divide-white/10">
                <div v-for="(activity, index) in recentActivity" :key="index" class="p-6 hover:bg-white/5 transition-colors duration-200">
                  <div class="flex space-x-4">
                    <!-- Activity Icon -->
                    <div class="flex-shrink-0">
                      <div
                        class="h-12 w-12 rounded-lg flex items-center justify-center shadow-lg"
                        :class="{
                          'bg-gradient-to-br from-green-500 to-emerald-600': activity.action === 'Approved',
                          'bg-gradient-to-br from-red-500 to-pink-600': activity.action === 'Declined' || activity.action === 'Rejected',
                          'bg-gradient-to-br from-yellow-500 to-orange-600': activity.action === 'Pending',
                          'bg-gradient-to-br from-gray-500 to-gray-600': !['Approved', 'Declined', 'Rejected', 'Pending'].includes(activity.action)
                        }"
                      >
                        <Icon :name="getActivityIcon(activity.action)" class="h-6 w-6 text-white" />
                      </div>
                    </div>

                    <!-- Activity Content -->
                    <div class="flex-1">
                      <div class="flex items-center justify-between mb-2">
                        <h3 class="text-base font-semibold text-white">
                          {{ getActivityTitle(activity) }}
                        </h3>
                        <p class="text-sm text-gray-400">{{ formatDate(activity.timestamp) }}</p>
                      </div>
                      <p class="text-sm text-gray-300 mb-1">
                        {{ getActivityDescription(activity) }}
                      </p>
                      <p v-if="activity.comment" class="text-sm text-gray-400 italic p-3 bg-gray-800/50 rounded-lg border border-white/5 mt-2">
                        "{{ activity.comment }}"
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-2xl border border-white/10 shadow-2xl overflow-hidden">
            <div class="p-6 border-b border-white/10">
              <h3 class="text-xl font-bold text-white flex items-center">
                <div class="w-3 h-3 bg-gradient-to-r from-cyan-400 to-blue-400 rounded-full mr-2"></div>
                Quick Actions
              </h3>
              <p class="text-gray-400 text-sm mt-1">Common moderation tasks and shortcuts</p>
            </div>
            <div class="p-6">
              <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                <NuxtLink to="/c/admin/moderation/queue?status=pending" class="group relative bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-xl p-6 border border-white/10 hover:border-yellow-400/30 transition-all duration-200 hover:shadow-2xl transform hover:scale-[1.02]">
                  <div class="absolute inset-0 bg-gradient-to-br from-yellow-400/5 to-orange-400/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div class="relative z-10">
                    <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-lg flex items-center justify-center mb-4 shadow-lg">
                      <Icon name="mdi:clock-outline" class="h-6 w-6 text-white" />
                    </div>
                    <p class="text-base font-semibold text-white mb-1">Review Pending</p>
                    <p class="text-sm text-gray-400">{{ contentStats.pending }} items waiting</p>
                  </div>
                </NuxtLink>

                <NuxtLink to="/c/admin/moderation/queue?flagged=true" class="group relative bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-xl p-6 border border-white/10 hover:border-orange-400/30 transition-all duration-200 hover:shadow-2xl transform hover:scale-[1.02]">
                  <div class="absolute inset-0 bg-gradient-to-br from-orange-400/5 to-red-400/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div class="relative z-10">
                    <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg flex items-center justify-center mb-4 shadow-lg">
                      <Icon name="mdi:flag" class="h-6 w-6 text-white" />
                    </div>
                    <p class="text-base font-semibold text-white mb-1">Review Flagged</p>
                    <p class="text-sm text-gray-400">{{ contentStats.flagged }} flagged items</p>
                  </div>
                </NuxtLink>

                <NuxtLink to="/c/admin/moderation/queue?contentType=businesscards" class="group relative bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-xl p-6 border border-white/10 hover:border-blue-400/30 transition-all duration-200 hover:shadow-2xl transform hover:scale-[1.02]">
                  <div class="absolute inset-0 bg-gradient-to-br from-blue-400/5 to-cyan-400/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div class="relative z-10">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-lg flex items-center justify-center mb-4 shadow-lg">
                      <Icon name="mdi:card-account-details" class="h-6 w-6 text-white" />
                    </div>
                    <p class="text-base font-semibold text-white mb-1">Business Cards</p>
                    <p class="text-sm text-gray-400">{{ contentStats.contentTypeDistribution.businesscards || 0 }} cards</p>
                  </div>
                </NuxtLink>

                <NuxtLink to="/c/admin/moderation/settings" class="group relative bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-xl p-6 border border-white/10 hover:border-purple-400/30 transition-all duration-200 hover:shadow-2xl transform hover:scale-[1.02]">
                  <div class="absolute inset-0 bg-gradient-to-br from-purple-400/5 to-pink-400/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div class="relative z-10">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mb-4 shadow-lg">
                      <Icon name="mdi:cog" class="h-6 w-6 text-white" />
                    </div>
                    <p class="text-base font-semibold text-white mb-1">Moderation Settings</p>
                    <p class="text-sm text-gray-400">Configure rules & workflows</p>
                  </div>
                </NuxtLink>
              </div>
            </div>
          </div>
        </template>
        </div>
      </div>
    </role-guard>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Chart, registerables } from 'chart.js'
import moment from 'moment'
import { useContentModeration } from '~/composables/content-moderation'

Chart.register(...registerables)

definePageMeta({
  middleware: ['admin'],
  layout: 'dashboard',
})

// State
const isLoading = ref(true)
const contentStats = ref({
  total: 0,
  pending: 0,
  approved: 0,
  rejected: 0,
  flagged: 0,
  contentTypeDistribution: {} as Record<string, number>
})
const recentActivity = ref<any[]>([])

// Computed trends (placeholder values for now)
const pendingTrend = ref(5.2)
const approvedTrend = ref(12.5)
const rejectedTrend = ref(-3.8)
const flaggedTrend = ref(8.1)

// Get content moderation composable
const {
  fetchContentStats,
  contentStats: moderationStats,
  initContentModeration
} = useContentModeration()

// Format date
const formatDate = (date: any) => {
  if (!date) return ''

  try {
    // Handle Firestore Timestamp
    if (date && typeof date.toDate === 'function') {
      return moment(date.toDate()).fromNow()
    }

    return moment(date).fromNow()
  } catch (error) {
    return 'Unknown date'
  }
}

// Get activity type class
const getActivityTypeClass = (action: string) => {
  if (action === 'Approved') return 'bg-green-100'
  if (action === 'Declined' || action === 'Rejected') return 'bg-red-100'
  if (action === 'Pending') return 'bg-yellow-100'
  return 'bg-gray-100'
}

// Get activity icon
const getActivityIcon = (action: string) => {
  if (action === 'Approved') return 'mdi:check-circle'
  if (action === 'Declined' || action === 'Rejected') return 'mdi:close-circle'
  if (action === 'Pending') return 'mdi:clock-outline'
  return 'mdi:information'
}

// Get activity title
const getActivityTitle = (activity: any) => {
  const contentType = getContentTypeName(activity.contentType)

  if (activity.action === 'Approved') {
    return `${contentType} Approved`
  }

  if (activity.action === 'Declined' || activity.action === 'Rejected') {
    return `${contentType} Rejected`
  }

  if (activity.action === 'Pending') {
    return `${contentType} Marked as Pending`
  }

  return `${contentType} Moderated`
}

// Get activity description
const getActivityDescription = (activity: any) => {
  const moderator = activity.moderatorEmail || 'an administrator'
  return `Moderated by ${moderator}`
}

// Get content type name
const getContentTypeName = (type: string) => {
  switch (type) {
    case 'businesscards':
      return 'Business Card'
    case 'flyers':
      return 'Flyer'
    case 'specials':
      return 'Special'
    case 'items':
      return 'Item for Sale'
    default:
      return 'Content'
  }
}

// Fetch recent moderation activity
const fetchRecentActivity = async () => {
  try {
    const { getCollection } = database()

    // Get moderation logs
    const logs = await getCollection('moderation_logs')

    if (logs && logs.length > 0) {
      // Sort by timestamp (newest first)
      const sortedLogs = logs.sort((a: any, b: any) => {
        const dateA = a.timestamp instanceof Date ? a.timestamp : new Date(a.timestamp)
        const dateB = b.timestamp instanceof Date ? b.timestamp : new Date(b.timestamp)
        return dateB.getTime() - dateA.getTime()
      })

      // Get the 5 most recent logs
      recentActivity.value = sortedLogs.slice(0, 5)
    }
  } catch (error) {
    console.error('Error fetching recent activity:', error)
  }
}

// Initialize charts
const initCharts = () => {
  // Content Type Chart
  const contentTypeCtx = document.getElementById('contentTypeChart') as HTMLCanvasElement
  if (contentTypeCtx) {
    new Chart(contentTypeCtx, {
      type: 'doughnut',
      data: {
        labels: [
          'Business Cards',
          'Flyers',
          'Specials',
          'Items for Sale'
        ],
        datasets: [{
          data: [
            contentStats.value.contentTypeDistribution.businesscards || 0,
            contentStats.value.contentTypeDistribution.flyers || 0,
            contentStats.value.contentTypeDistribution.specials || 0,
            contentStats.value.contentTypeDistribution.items || 0
          ],
          backgroundColor: [
            '#3B82F6', // blue
            '#8B5CF6', // purple
            '#10B981', // green
            '#F59E0B'  // orange
          ],
          hoverOffset: 4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom'
          }
        }
      }
    })
  }

  // Content Status Chart
  const contentStatusCtx = document.getElementById('contentStatusChart') as HTMLCanvasElement
  if (contentStatusCtx) {
    new Chart(contentStatusCtx, {
      type: 'doughnut',
      data: {
        labels: [
          'Pending',
          'Approved',
          'Rejected',
          'Flagged'
        ],
        datasets: [{
          data: [
            contentStats.value.pending,
            contentStats.value.approved,
            contentStats.value.rejected,
            contentStats.value.flagged
          ],
          backgroundColor: [
            '#FBBF24', // yellow
            '#10B981', // green
            '#EF4444', // red
            '#F97316'  // orange
          ],
          hoverOffset: 4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom'
          }
        }
      }
    })
  }
}

// Initialize page
const initPage = async () => {
  isLoading.value = true

  try {
    // Initialize content moderation
    initContentModeration()

    // Fetch content stats
    await fetchContentStats()

    // Update local stats from composable
    contentStats.value = { ...moderationStats.value }

    // Fetch recent activity
    await fetchRecentActivity()

    // Initialize charts
    setTimeout(() => {
      initCharts()
    }, 100)
  } catch (error) {
    console.error('Error initializing page:', error)
  } finally {
    isLoading.value = false
  }
}

// On component mount
onMounted(() => {
  initPage()
})
</script>