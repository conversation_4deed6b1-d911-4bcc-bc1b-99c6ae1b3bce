<template>
  <div class="demo-page">
    <!-- Header -->
    <div class="page-header">
      <h1 class="page-title">Business Card Processing Demo</h1>
      <p class="page-description">
        Upload a business card image and watch the form auto-fill with extracted information using Vertex AI.
      </p>
    </div>

    <!-- Demo Section -->
    <div class="demo-section">
      <BusinessCardForm
        @submit="handleFormSubmit"
        @auto-fill="handleAutoFill"
        @error="handleError"
      />
    </div>

    <!-- Usage Instructions -->
    <div class="instructions-section">
      <h2>How to Use</h2>
      <div class="instructions-grid">
        <div class="instruction-card">
          <div class="instruction-icon">
            <Icon name="material-symbols:upload" />
          </div>
          <h3>1. Upload Image</h3>
          <p>Drag and drop a business card image or click to select a file. Supports JPEG, PNG, and WebP formats.</p>
        </div>

        <div class="instruction-card">
          <div class="instruction-icon">
            <Icon name="material-symbols:auto-awesome" />
          </div>
          <h3>2. AI Processing</h3>
          <p>Our Vertex AI system analyzes the image and extracts contact information with high accuracy.</p>
        </div>

        <div class="instruction-card">
          <div class="instruction-icon">
            <Icon name="material-symbols:edit-document" />
          </div>
          <h3>3. Auto-Fill Form</h3>
          <p>The form automatically populates with extracted data. Review and edit as needed before saving.</p>
        </div>
      </div>
    </div>

    <!-- Features Section -->
    <div class="features-section">
      <h2>Features</h2>
      <div class="features-grid">
        <div class="feature-card">
          <Icon name="material-symbols:psychology" class="feature-icon" />
          <h3>AI-Powered Extraction</h3>
          <p>Uses Google's Gemini 1.5 Flash model for accurate text recognition and data extraction.</p>
        </div>

        <div class="feature-card">
          <Icon name="material-symbols:language" class="feature-icon" />
          <h3>Multi-Language Support</h3>
          <p>Processes business cards in multiple languages with intelligent field recognition.</p>
        </div>

        <div class="feature-card">
          <Icon name="material-symbols:speed" class="feature-icon" />
          <h3>Fast Processing</h3>
          <p>Optimized for speed with typical processing times under 3 seconds.</p>
        </div>

        <div class="feature-card">
          <Icon name="material-symbols:verified" class="feature-icon" />
          <h3>High Accuracy</h3>
          <p>Advanced preprocessing and validation ensure reliable data extraction.</p>
        </div>

        <div class="feature-card">
          <Icon name="material-symbols:auto-fix-high" class="feature-icon" />
          <h3>Smart Auto-Fill</h3>
          <p>Intelligently maps extracted data to form fields with visual feedback.</p>
        </div>

        <div class="feature-card">
          <Icon name="material-symbols:error-outline" class="feature-icon" />
          <h3>Error Handling</h3>
          <p>Comprehensive error handling with clear feedback and recovery options.</p>
        </div>
      </div>
    </div>

    <!-- Integration Example -->
    <div class="integration-section">
      <h2>Integration Example</h2>
      <p>Here's how to integrate business card processing into your own forms:</p>
      
      <div class="code-example">
        <pre><code>&lt;template&gt;
  &lt;BusinessCardForm
    :initial-data="existingData"
    :custom-field-mapping="fieldMapping"
    @submit="handleSubmit"
    @auto-fill="handleAutoFill"
    @error="handleError"
  /&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { useBusinessCardProcessor } from '~/composables/useBusinessCardProcessor'

const { processBusinessCard, autoFillForm } = useBusinessCardProcessor()

// Custom field mapping for your form
const fieldMapping = {
  fullName: 'fullName',
  companyName: 'company',
  position: 'jobTitle',
  emailAddress: 'email',
  phoneNumber: 'phone'
}

const handleSubmit = (data) => {
  // Handle form submission
  console.log('Form submitted:', data)
}

const handleAutoFill = (data) => {
  // Handle auto-fill event
  console.log('Form auto-filled:', data)
}
&lt;/script&gt;</code></pre>
      </div>
    </div>

    <!-- API Usage -->
    <div class="api-section">
      <h2>Direct API Usage</h2>
      <p>You can also use the business card processor directly in your code:</p>
      
      <div class="code-example">
        <pre><code>import { useBusinessCardProcessor } from '~/composables/useBusinessCardProcessor'

const { processBusinessCard, autoFillForm } = useBusinessCardProcessor()

// Process a business card image
const processCard = async (imageFile) => {
  try {
    const result = await processBusinessCard(imageFile, {
      enhancementLevel: 'standard',
      returnRawText: false
    })
    
    if (result.success) {
      console.log('Extracted data:', result.data)
      console.log('Confidence:', result.confidence)
      
      // Auto-fill your form
      const formData = { /* your form data */ }
      autoFillForm(formData)
    }
  } catch (error) {
    console.error('Processing failed:', error.message)
  }
}</code></pre>
      </div>
    </div>

    <!-- Success/Error Messages -->
    <div v-if="message" class="message" :class="messageType">
      <Icon :name="messageType === 'success' ? 'material-symbols:check-circle' : 'material-symbols:error'" />
      <span>{{ message }}</span>
      <button @click="clearMessage" class="message-close">×</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// Page metadata
useHead({
  title: 'Business Card Processing Demo',
  meta: [
    { name: 'description', content: 'Demonstrate AI-powered business card processing with automatic form filling using Vertex AI.' }
  ]
})

// Component state
const message = ref('')
const messageType = ref<'success' | 'error'>('success')

// Event handlers
const handleFormSubmit = (data: Record<string, any>) => {
  console.log('Form submitted:', data)
  
  // Simulate saving the contact
  setTimeout(() => {
    message.value = 'Contact saved successfully!'
    messageType.value = 'success'
    
    // Clear message after 5 seconds
    setTimeout(() => {
      message.value = ''
    }, 5000)
  }, 500)
}

const handleAutoFill = (data: Record<string, any>) => {
  console.log('Form auto-filled:', data)
  
  message.value = 'Form auto-filled with extracted business card data!'
  messageType.value = 'success'
  
  // Clear message after 3 seconds
  setTimeout(() => {
    message.value = ''
  }, 3000)
}

const handleError = (error: string) => {
  console.error('Processing error:', error)
  
  message.value = error
  messageType.value = 'error'
}

const clearMessage = () => {
  message.value = ''
}
</script>

<style scoped>
.demo-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.page-header {
  text-align: center;
  margin-bottom: 3rem;
}

.page-title {
  font-size: 3rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 1rem;
}

.page-description {
  font-size: 1.25rem;
  color: #6b7280;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.demo-section {
  margin-bottom: 4rem;
}

.instructions-section,
.features-section,
.integration-section,
.api-section {
  margin-bottom: 4rem;
}

.instructions-section h2,
.features-section h2,
.integration-section h2,
.api-section h2 {
  font-size: 2rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 2rem;
  text-align: center;
}

.instructions-grid,
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.instruction-card,
.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.instruction-card:hover,
.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.instruction-icon,
.feature-icon {
  font-size: 3rem;
  color: #3b82f6;
  margin-bottom: 1rem;
}

.instruction-card h3,
.feature-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
}

.instruction-card p,
.feature-card p {
  color: #6b7280;
  line-height: 1.6;
}

.code-example {
  background: #1f2937;
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-top: 1rem;
  overflow-x: auto;
}

.code-example pre {
  margin: 0;
}

.code-example code {
  color: #e5e7eb;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

.message {
  position: fixed;
  top: 2rem;
  right: 2rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  z-index: 1000;
  animation: slideIn 0.3s ease-out;
}

.message.success {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  color: #166534;
}

.message.error {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
}

.message-close {
  margin-left: 0.5rem;
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: inherit;
  opacity: 0.7;
}

.message-close:hover {
  opacity: 1;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .demo-page {
    padding: 1rem;
  }
  
  .page-title {
    font-size: 2rem;
  }
  
  .instructions-grid,
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .message {
    top: 1rem;
    right: 1rem;
    left: 1rem;
  }
}
</style>
