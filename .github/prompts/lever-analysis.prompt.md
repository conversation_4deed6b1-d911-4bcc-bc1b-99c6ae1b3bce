# LEVER Framework Analysis Prompt

Analyze the following codestart -> /workflows:feature-start for LEVER framework compliance:

## Analysis Steps
1. **Leverage Assessment**
   - What existing code could be reused?
   - Are there similar patterns already implemented?

2. **Extension Opportunities**
   - Which base classes/utilities could be extended?
   - What inheritance is missed?

3. **Verification Coverage**
   - Test coverage percentage?
   - Are edge cases tested?

4. **Duplication Check**
   - Any repeated logic?
   - Similar functionality elsewhere?

5. **Complexity Evaluation**
   - Can this be simplified?
   - Unnecessary abstractions?

Provide specific examples and actionable improvements.