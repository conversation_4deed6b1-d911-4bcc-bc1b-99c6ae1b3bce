# Feature Implementation Prompt

Implement the requested feature following PIB-METHOD standards:

## Pre-Implementation Checklist
- [ ] Search codebase for similar features
- [ ] Identify ALL reusable components  
- [ ] Find base classes to extend
- [ ] Plan test strategy

## Implementation Template
```javascript
// 1. Extend existing base class
class Feature extends BaseClass {
  constructor() {
    super(); // Reuse parent initialization
  }
  
  // 2. Implement with error handling
  async method() {
    try {
      // Reuse existing utilities
      return await this.existingMethod();
    } catch (error) {
      // Use standard error handling
      return this.handleError(error);
    }
  }
}

// 3. Include tests
describe('Feature', () => {
  // Use existing test patterns
});
```

Remember: Leverage > Create