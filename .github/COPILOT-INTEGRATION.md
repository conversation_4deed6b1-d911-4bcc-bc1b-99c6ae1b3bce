# GitHub Copilot Integration for PIB-METHOD

## Overview
This document explains the correct GitHub Copilot configuration for PIB-METHOD, following the official VS Code documentation structure.

## Directory Structure

```
PIB-METHOD/
├── .github/
│   ├── copilot-instructions.md    # Main repository instructions
│   ├── instructions/               # Scoped instruction files
│   │   ├── analysis.instructions.md
│   │   ├── implementation.instructions.md
│   │   ├── testing.instructions.md
│   │   └── review.instructions.md
│   ├── prompts/                    # Reusable prompt templates
│   │   ├── lever-analysis.prompt.md
│   │   └── feature-implementation.prompt.md
│   └── workflows/                  # GitHub Actions (standard location)
│
├── .vscode/
│   └── settings.json              # VS Code Copilot configuration
│
└── .claude/
    └── agents/                    # Claude Code sub-agents
```

## How It Works

### 1. Main Instructions
The `.github/copilot-instructions.md` file contains repository-wide guidelines that apply to all Copilot interactions.

### 2. Scoped Instructions
Files in `.github/instructions/` use the `applyTo` frontmatter to target specific file patterns:
```markdown
---
applyTo: "**/*.{js,ts}"
---
# JavaScript/TypeScript specific instructions
```

### 3. Prompt Templates
Reusable prompts in `.githubenhancer -> /quick:prompt-enhancers/` can be referenced in chat for common tasks.

### 4. VS Code Settings
The `.vscode/settings.json` configures:
- Instruction file locations
- Mode-specific instructions
- Prompt file locations

## Usage Examples

### Code Generation
```
"Implement user authentication" 
# Copilot will use implementation.instructions.md
```

### Code Review
```
"Review this code for LEVER compliance"
# Copilot will use review.instructions.md
```

### Testing
```
"Generate tests for this module"
# Copilot will use testing.instructions.md
```

## Key Differences from Initial Implementation

### ❌ Incorrect (What we had)
- `.github/copilot/chat-participants.md` 
- `.github/copilot/modes/*.mode.md`
- `.github/copilothelp -> /quick:workflow-helps/`

### ✅ Correct (Current structure)
- `.github/copilot-instructions.md`
- `.github/instructions/*.instructions.md`
- `.githubenhancer -> /quick:prompt-enhancers/*.prompt.md`
- `.githubhelp -> /quick:workflow-helps/` (standard GitHub location)

## Configuration Settings

### Essential VS Code Settings
```json
{
  "github.copilot.chat.codeGeneration.useInstructionFiles": true,
  "chat.instructionsFilesLocations": {
    ".github": true,
    ".github/instructions": true
  },
  "chat.promptFilesLocations": {
    ".githubenhancer -> /quick:prompt-enhancers": true
  }
}
```

## LEVER Framework Integration

All instructions enforce the LEVER principles:
- **L**everage: Reuse existing code
- **E**xtend: Extend rather than recreate
- **V**erify: Comprehensive testing
- **E**liminate: No duplication
- **R**educe: Minimal complexity

## Chat Interaction Patterns

While GitHub Copilot doesn't have explicit "chat participants" like Claude Code, you can achieve similar behavior through:

1. **Contextual Keywords**: Start prompts with action words
   - "Analyze..." → Triggers analysis behavior
   - "Implement..." → Triggers implementation patterns
   - "Review..." → Triggers review scoring
   - "Test..." → Triggers test generation

2. **File Context**: The `applyTo` patterns automatically apply relevant instructions based on the file you're working in

3. **Explicit Instructions**: Reference specific behaviors in your prompts

## Integration with Claude Code

Both systems now share:
- Same LEVER compliance requirements
- Same quality standards (4/5 minimum)
- Compatible file structures
- Unified development workflows

The main difference is invocation:
- **Claude Code**: Uses explicit sub-agents via Task tool
- **GitHub Copilot**: Uses contextual instructions and keywords

## Quick Reference

| Task | Claude Code | GitHub Copilot |
|------|-------------|----------------|
| Research | `Use analyst sub-agent` | `"Analyze existing patterns for..."` |
| Implementation | `Use dev-agent sub-agent` | `"Implement feature using LEVER..."` |
| Review | `Use code-reviewer sub-agent` | `"Review code for LEVER compliance"` |
| Testing | `Use qa-tester sub-agent` | `"Generate comprehensive tests"` |

Both achieve the same high-quality, LEVER-compliant results!