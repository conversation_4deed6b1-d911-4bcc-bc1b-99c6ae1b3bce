# GitHub Copilot Instructions for PIB-METHOD

## Overview
This file defines project-wide instructions for GitHub Copilot to ensure consistency with PIB-METHOD standards and the LEVER framework. These instructions are automatically included in all Copilot interactions within this repository.

## 🚀 NEW: Automatic Task Management
GitHub Copilot now automatically breaks down complex requests into numbered task lists with progress tracking and LEVER scoring. Simply use keywords like "implement", "build", "fix", or "refactor" and watch tasks appear! See [COPILOT-TASK-MANAGEMENT.md](./COPILOT-TASK-MANAGEMENT.md) for details.

## Chat Participants (PIB Agents)
Use these agent mentions for specialized assistance:
- `@architect` - System design and architecture decisions
- `@dev` - Implementation with LEVER compliance
- `@qa` - Testing and quality assurance
- `@analyst` - Research and best practices

When using GitHub Copilot Chat, these keywords also trigger specialized behavior:
- For research tasks: Start with "analyze" or "research" keywords
- For architecture: Use "design" or "architect" keywords  
- For implementation: Use "implement" or "code" keywords
- For testing: Use "test" or "qa" keywords
- For reviews: Use "review" or "check" keywords

## Q-LEVER Framework Compliance

### Core Philosophy
"Ask first, then code. The best code is no code. The second best code is code that already exists and works."

**ALWAYS follow Q-LEVER principles in this order:**
- **Q**uestion assumptions and clarify requirements FIRST
- **L**everage existing patterns and code
- **E**xtend before creating new
- **V**erify through comprehensive testing
- **E**liminate duplication at all costs
- **R**educe complexity continuously

### Question Phase (MANDATORY FIRST STEP)
⚠️ **CRITICAL: SEARCH CODEBASE BEFORE ASKING QUESTIONS**

Before writing ANY code or asking questions:
1. **SEARCH THE CODEBASE FIRST** using context variables (#codebase, #selection, #file)
2. **Identify technical stack** from package.json, configs, existing code
3. **Find existing patterns** to leverage and extend
4. **ONLY THEN ask questions** about business requirements not visible in code

### What to Search vs What to Ask

#### 🔍 SEARCH CODEBASE FOR (Never ask about):
- Framework/technology stack (React, Express, etc.)
- Database type (PostgreSQL, MongoDB, etc.)
- Authentication system (JWT, OAuth, etc.)
- UI library (Material-UI, Tailwind, etc.)
- Testing setup (Jest, Cypress, etc.)
- Existing similar components/features
- Code patterns and architecture

#### ❓ ASK QUESTIONS ABOUT (Not in codebase):
- Business requirements and scope
- User experience expectations  
- Feature priorities and constraints
- External integrations
- Performance/security requirements
- User roles and permissions
- Success criteria

### Smart Question Examples

#### ✅ GOOD (After codebase analysis):
- "I found your JWT auth system - should this feature integrate with the existing middleware?"
- "I see Material-UI components - should I follow the existing design patterns?"
- "Your User model has roles - which roles need access to this feature?"

#### ❌ BAD (Ask codebase first):
- "What database are you using?"
- "What framework should we use?"
- "Do you have authentication?"

## Coding Standards

### General Guidelines
- Search for existing implementations before writing new code
- Prefer extending existing functions/classes over creating new ones
- Use descriptive variable and function names
- Include TypeScript types for all JavaScript code
- Follow functional programming principles where appropriate
- Implement error-first callbacks and proper error handling

### File Structure
- Place all PIB-specific files in appropriate directories
- Follow kebab-case for file names: `feature-name.md`
- Use camelCase for variables and functions
- Use PascalCase for classes and interfaces
- Use UPPER_SNAKE_CASE for constants

### Code Patterns
- Use async/await over callbacks
- Implement proper error boundaries
- Always validate inputs before processing
- Return early from functions when possible
- Use object destructuring for cleaner code

### Testing Requirements
- Write tests for all new functionality
- Aim for >80% code coverage
- Test edge cases and error conditions
- Use descriptive test names that explain the behavior

### Documentation
- Include JSDoc comments for all public functions
- Update README.md when adding new features
- Document complex algorithms inline
- Keep comments concise and meaningful

## PIB-METHOD Specific Guidelines

### Workflow Integration
- Follow PIB-METHOD task workflows
- Ensure all code changes pass quality gates
- Implement changes incrementally with proper validation
- Use appropriate PIB agents for specialized tasks

### MCP Tool Usage
- Leverage Context7 for library documentation
- Use Perplexity for research tasks
- Apply Playwright for E2E testing
- Utilize Firecrawl for web content extraction

### Git Practices
- Use semantic commit messages: `feat:`, `fix:`, `docs:`, `refactor:`
- Keep commits atomic and focused
- Write meaningful commit descriptions
- Never commit sensitive data or credentials

## Forbidden Practices
- DO NOT create new code without checking for existing implementations
- DO NOT duplicate functionality that already exists
- DO NOT use console.log in production code
- DO NOT commit without running linters and tests
- DO NOT implement features without understanding requirements
- DO NOT use deprecated libraries or patterns
- DO NOT create files without proper documentation

## Integration with Claude Code
When working alongside Claude Code agents:
- Maintain consistency with `.claude> /core:agents/` definitions
- Follow the same quality standards and LEVER compliance
- Use similar patterns for error handling and logging
- Ensure seamless handoff between Copilot and Claude Code workflows

## Project Context
- **Framework**: PIB-METHOD with LEVER compliance
- **Architecture**: Multi-agent orchestration system
- **Key Technologies**: Node.js, TypeScript, Markdown
- **Testing**: Comprehensive testing with quality gates
- **Documentation**: Extensive inline and external documentation

## Example Patterns

### API Endpoint Creation
```javascript
// ALWAYS check for existing endpoint patterns first
// Extend base controller if available
class UserController extends BaseController {
  async getUser(req, res) {
    try {
      // Validate input first
      const { error, value } = validateUserId(req.params.id);
      if (error) return this.badRequest(res, error);
      
      // Use existing service methods
      const user = await this.userService.findById(value.id);
      if (!user) return this.notFound(res, 'User not found');
      
      return this.success(res, user);
    } catch (error) {
      return this.serverError(res, error);
    }
  }
}
```

### React Component Pattern
```typescript
// Leverage existing component patterns
interface UserCardProps {
  user: User;
  onEdit?: (user: User) => void;
}

// Extend base components when possible
export const UserCard: React.FC<UserCardProps> = ({ user, onEdit }) => {
  // Use existing hooks and utilities
  const { formatDate } = useFormatters();
  const { hasPermission } = useAuth();
  
  return (
    <Card className="user-card">
      <CardHeader title={user.name} />
      <CardBody>
        <p>Joined: {formatDate(user.createdAt)}</p>
        {hasPermission('user:edit') && onEdit && (
          <Button onClick={() => onEdit(user)}>Edit</Button>
        )}
      </CardBody>
    </Card>
  );
};
```

Remember: Always strive for code reuse, clarity, and maintainability following the LEVER framework.
