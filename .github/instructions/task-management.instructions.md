---
applyTo: "**"
priority: 100
---

# PIB-METHOD Task Management Protocol for GitHub Copilot

## Automatic Task Decomposition

When handling ANY complex request (more than 2 distinct actions required):

### 1. Initial Task Breakdown Phase

ALWAYS start by breaking down the request into a numbered task list:

```
I'll help you with [request]. Let me break this down into manageable tasks:

□ 1. [First task - usually research/analysis]
□ 2. [Second task - usually design/planning]
□ 3. [Third task - usually implementation]
□ 4. [Additional tasks as needed]
□ 5. [Final task - usually testing/validation]

Starting with task 1...
```

### 2. Progress Tracking Protocol

As you work through tasks, update the status:
- ✅ Completed tasks
- 🔄 In-progress task (only ONE at a time)
- ⏸️ Paused/blocked tasks
- □ Pending tasks

Example:
```
Progress Update:
✅ 1. Analyzed existing authentication patterns
🔄 2. Designing JWT implementation
□ 3. Implement user registration
□ 4. Create login/logout endpoints
□ 5. Add comprehensive tests
```

### 3. Q-LEVER Integration

For EACH task, apply Q-LEVER principles:

```
Task 2: Implement feature X
Q: What existing patterns can we leverage?
L: Found similar implementation in [file:line]
E: Extending BaseController instead of creating new
V: Will verify with unit tests
E: Eliminating duplicate validation logic
R: Reduced from 100 lines to 30 lines

LEVER Score: 4.5/5 ✅
```

### 4. Task Types and Templates

#### Feature Implementation Tasks
```
□ 1. Question requirements and clarify scope
□ 2. Research existing patterns (#codebase search)
□ 3. Design solution architecture
□ 4. Implement core functionality
□ 5. Add error handling
□ 6. Write tests (>80% coverage)
□ 7. LEVER compliance review
□ 8. Documentation update
```

#### Bug Fix Tasks
```
□ 1. Reproduce the issue
□ 2. Identify root cause
□ 3. Research similar fixes
□ 4. Implement fix (extend existing patterns)
□ 5. Test fix thoroughly
□ 6. Verify no regressions
□ 7. Update related documentation
```

#### Refactoring Tasks
```
□ 1. Analyze current implementation
□ 2. Identify improvement opportunities
□ 3. Apply LEVER principles
□ 4. Refactor incrementally
□ 5. Maintain test coverage
□ 6. Performance validation
□ 7. Code review preparation
```

### 5. Context Variables for Tasks

Use these context variables to enhance task management:
- `#codebase` - Search for existing patterns
- `#selection` - Current code selection
- `#problems` - Current problems in workspace
- `#file` - Current file context
- `#terminal` - Terminal output context

### 6. Task Handoff Protocol

When switching between tasks or sessions:

```
Task Handoff Summary:
- Completed: Tasks 1-3 ✅
- Current: Task 4 (50% complete)
- Remaining: Tasks 5-7
- Blockers: None
- Next Action: Continue implementing [specific feature]
- LEVER Score: 4.2/5

To continue, say: "Continue from task 4"
```

### 7. Automatic Task Triggers

Automatically create task lists when user says:
- "implement", "build", "create", "develop"
- "fix", "debug", "resolve", "troubleshoot"
- "refactor", "optimize", "improve", "enhance"
- "test", "validate", "verify", "check"
- Any request with multiple requirements

### 8. Integration with PIB Agents

Map tasks to appropriate PIB agents:

```
Task Assignment:
□ 1. Research best practices → @analyst
□ 2. Design architecture → @architect
□ 3. Implement solution → @dev
□ 4. Create tests → @qa
```

### 9. Task Persistence

Maintain task context across the conversation:
- Reference previous tasks by number
- Update progress incrementally
- Provide cumulative LEVER scores
- Track time estimates if requested

### 10. Smart Suggestions

After completing each task, suggest next actions:

```
✅ Task 3 complete!

Suggested next actions:
1. Continue with task 4 (implement error handling)
2. Run tests on completed functionality
3. Review LEVER compliance (current: 4.1/5)
4. Commit current progress

What would you like to do next?
```

## Special Instructions for Complex Projects

For projects with 10+ tasks:
1. Group into phases (Planning, Implementation, Testing, Deployment)
2. Show phase-level progress
3. Provide phase summaries
4. Allow phase-specific focus

## LEVER Scoring Integration

Every task completion should include:
- Individual task LEVER score
- Cumulative project LEVER score
- Specific improvements to increase score
- Reuse metrics (% of leveraged code)

## Error Recovery

If a task fails:
```
⚠️ Task 3 encountered an issue: [error description]

Recovery options:
1. Retry with different approach
2. Skip and mark as blocked
3. Break into subtasks
4. Request user guidance

Current LEVER compliance maintained at 4.2/5
```

## Task Export Format

For sync with Claude Code or documentation:

```markdown
## PIB Task List Export
Date: [timestamp]
Project: [project name]
LEVER Score: [score]/5

### Completed Tasks
- [x] Task 1: Description (LEVER: 4.5/5)
- [x] Task 2: Description (LEVER: 4.3/5)

### Pending Tasks
- [ ] Task 3: Description
- [ ] Task 4: Description

### Blocked Tasks
- [ ] ~~Task 5: Description~~ (Blocker: [reason])
```

Remember: ALWAYS break down complex requests into tasks. This provides clarity, tracks progress, and ensures comprehensive implementation following PIB-METHOD standards.