---
applyTo: "**"
priority: 150
---

# PIB-METHOD Intelligent Planning Protocol

## CRITICAL: Search Codebase BEFORE Asking Questions

When handling planning requests (planning sessions, task clarification, etc.):

### 1. MANDATORY First Step: Codebase Research

Before asking ANY questions, ALWAYS search the codebase using context variables:

```
Let me first understand your current setup by analyzing the codebase...

Analyzing #codebase for:
- Framework/technology stack
- Database configuration  
- Authentication patterns
- API structure
- Testing setup
- Build/deployment configuration
- UI/component patterns
- State management approach
```

### 2. What to Find vs. What to Ask

#### SEARCH THE CODEBASE FOR (Never ask about these):
- **Framework/Tech Stack**: React, Vue, Express, Django, etc.
- **Database**: PostgreSQL, MongoDB, MySQL, etc.
- **Authentication**: JWT, OAuth, sessions, etc.
- **API Style**: REST, GraphQL, etc.
- **UI Library**: Material-UI, <PERSON>lwind, Boots<PERSON>p, etc.
- **State Management**: Redux, Zustand, Context API, etc.
- **Testing Framework**: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, etc.
- **Build Tools**: Webpack, Vite, Next.js, etc.
- **Package Manager**: npm, yarn, pnpm, etc.
- **Code Patterns**: Existing component structure, naming conventions

#### ASK QUESTIONS ABOUT (Things not in codebase):
- **Business Requirements**: What should the feature do?
- **User Experience**: How should users interact with it?
- **Scope**: What's included/excluded from this task?
- **Priority**: What's most important vs. nice-to-have?
- **Constraints**: Timeline, budget, team limitations
- **External Integrations**: Third-party services not visible in code
- **Performance Requirements**: Speed, scale expectations
- **Security Requirements**: Specific compliance needs
- **User Types**: Who will use this and their permissions
- **Success Metrics**: How to measure completion

### 3. Planning Session Protocol

```
I'll help you plan [task]. Let me start by analyzing your current codebase...

🔍 Codebase Analysis:
✅ Found: React 18 with TypeScript
✅ Found: Express.js backend with PostgreSQL  
✅ Found: JWT authentication already implemented
✅ Found: Material-UI component library
✅ Found: Jest testing setup
✅ Found: Existing user management in /src/auth/

Based on your current setup, I have some questions about the requirements:

❓ 1. [Specific business requirement question]
❓ 2. [User experience question]
❓ 3. [Scope/priority question]
```

### 4. Smart Question Examples

#### ❌ BAD Questions (Ask codebase first):
- "What database are you using?"
- "What framework is this built with?"
- "Do you have authentication already?"
- "What UI library should we use?"

#### ✅ GOOD Questions (After codebase analysis):
- "Should the new feature integrate with your existing JWT auth system?"
- "I see you're using Material-UI - should the new components follow your existing design patterns in /src/components/?"
- "Your current user model has roles - which roles should have access to this feature?"
- "I found existing tests in Jest - should I follow the same testing patterns for the new feature?"

### 5. Context Variable Usage

Use these systematically for codebase analysis:
- `#codebase` - Search entire codebase for patterns
- `#selection` - If user has code selected, analyze it
- `#problems` - Check for existing issues to avoid
- `#file` - Current file context if relevant

### 6. Analysis Report Template

Always provide a summary of findings before asking questions:

```markdown
## Codebase Analysis Summary

### Technology Stack
- Frontend: [Framework + version]
- Backend: [API framework + database]  
- Authentication: [Current auth system]
- UI: [Component library/styling]
- Testing: [Test framework]

### Existing Patterns Found
- [Relevant existing patterns for the task]
- [Reusable components/utilities]
- [Similar implementations to leverage]

### Integration Points
- [How new feature connects to existing code]
- [Dependencies to consider]
- [Potential conflicts to avoid]

## Requirements Questions

Based on this analysis, I need clarification on:
1. [Business requirement question]
2. [User experience question]  
3. [Scope question]
```

### 7. LEVER Integration with Analysis

Apply LEVER principles during analysis:

```
LEVER Analysis:
L - Leverage: Found existing auth service at /src/auth/AuthService.js
E - Extend: Can extend BaseComponent pattern from /src/components/Base.tsx  
V - Verify: Existing Jest tests in /src/__tests__/ to follow
E - Eliminate: Found duplicate validation in forms - can consolidate
R - Reduce: Current patterns show preference for functional components

Preliminary LEVER Score: 4.2/5 (strong existing patterns to build on)
```

### 8. Error Recovery

If codebase search fails or returns unclear results:

```
I attempted to analyze your codebase but need some technical context:

⚠️ Could not clearly identify:
- [Specific technical aspect]

Rather than making assumptions, could you clarify:
- [Targeted technical question based on what was unclear]

This will help me provide a better plan that works with your existing setup.
```

### 9. Planning Efficiency Rules

- **Search First**: Always use context variables before asking
- **Be Specific**: Questions should be about business requirements, not tech stack
- **Leverage Analysis**: Use findings to inform better questions
- **Avoid Redundancy**: Don't ask about things clearly visible in code
- **Focus on Gaps**: Ask about information truly missing from codebase

### 10. Quality Gates

Before presenting any plan, verify:
- [ ] Codebase analysis performed using context variables
- [ ] Technology stack identified from code, not questions
- [ ] Existing patterns documented for leverage
- [ ] Questions focus on business requirements only
- [ ] Plan builds on existing codebase patterns
- [ ] LEVER principles applied to analysis

## Remember: The codebase is your first source of truth. Only ask questions about what you genuinely cannot determine from the code itself.