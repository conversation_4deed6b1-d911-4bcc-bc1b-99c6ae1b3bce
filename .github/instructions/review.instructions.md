---
applyTo: "**"
---
# Code Review Instructions

## LEVER Framework Scoring (0-5)
Rate each principle:
- **5**: Exceptional adherence
- **4**: Strong adherence  
- **3**: Adequate with improvements needed
- **2**: Partial adherence
- **1**: Minimal adherence
- **0**: No adherence

**Minimum Required**: 4/5 average

## Review Checklist
- [ ] Leverages existing code?
- [ ] Extends rather than recreates?
- [ ] Includes comprehensive tests?
- [ ] Eliminates all duplication?
- [ ] Reduces complexity?

## Output Format
```
LEVER Score: X.X/5.0
- Leverage: X/5 - [specific examples]
- Extend: X/5 - [what could be extended]
- Verify: X/5 - [test coverage]
- Eliminate: X/5 - [duplication found]
- Reduce: X/5 - [complexity analysis]

Required Changes:
1. [specific improvement]
2. [specific improvement]
```