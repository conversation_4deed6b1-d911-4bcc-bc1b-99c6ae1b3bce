---
applyTo: "**/*.{test,spec}.{js,ts,jsx,tsx}"
---
# Testing Instructions

## Test Requirements
- Minimum 80% code coverage
- Test all edge cases
- Include error scenarios
- Use existing test utilities and patterns

## Test Structure
```javascript
describe('Feature', () => {
  // Reuse test setup utilities
  beforeEach(() => useExistingTestSetup());
  
  it('should follow existing test patterns', () => {
    // Arrange - use test builders
    // Act - single action
    // Assert - clear expectations
  });
  
  it('should handle errors properly', () => {
    // Always test error paths
  });
});
```

## LEVER Compliance Testing
- Verify no duplicate functionality
- Ensure proper inheritance
- Check complexity metrics