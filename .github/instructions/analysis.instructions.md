---
applyTo: "**"
---
# Analysis & Research Instructions

When asked to analyze or research:
1. First search the codebase for existing similar functionality
2. Document all patterns found before suggesting new approaches
3. Consider multiple sources and validate information
4. Provide comprehensive analysis with pros/cons
5. Always check for deprecated patterns or libraries

## Research Process
- Internal codebase analysis first
- External best practices second
- Security implications always
- Performance considerations mandatory

Remember: The best solution often already exists in the codebase.