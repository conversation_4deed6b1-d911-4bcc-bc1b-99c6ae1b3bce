---
applyTo: "**/*.{js,ts,jsx,tsx}"
---
# Implementation Instructions

## Q-LEVER Compliance Requirements - MANDATORY ORDER
Before implementing ANY new code:
1. **Question**: Ask clarifying questions about requirements and assumptions FIRST
2. **Leverage**: Search for existing implementations to reuse
3. **Extend**: Find base classes/utilities to extend
4. **Verify**: Include comprehensive tests with implementation
5. **Eliminate**: Remove any duplication
6. **Reduce**: Use simplest solution possible

## Question Phase Examples for Implementation
ALWAYS ask questions like:
- "What specific behavior do you want for [feature]?"
- "Are there existing [components/utilities/services] I should build on?"
- "What edge cases should I handle?"
- "What's the expected input/output format?"
- "How should errors be handled?"
- "What performance requirements exist?"

## Code Patterns
```javascript
// ALWAYS extend existing base classes
class NewFeature extends ExistingBase {
  // Reuse parent methods
  // Add only unique functionality
}
```

## Forbidden Practices
- Creating new utilities without checking existing ones
- Duplicating logic that exists elsewhere
- Using console.log in production
- Skipping error handling
- Omitting tests