# PIB-METHOD Unified AI System

## Overview
PIB-METHOD now supports both **Claude Code** and **GitHub Copilot** through a unified architecture that maintains consistency, quality, and LEVER framework compliance across both AI assistants.

## Quick Start

### Using Claude Code
```bash
# Start a feature
start -> help -> /quick:workflow-helps:feature-start-start user-auth

# Run development workflow
> help -> /quick:workflow-helps:dev-command "implement JWT authentication"

# The system automatically orchestrates:
# analyst → architect → dev → qa → reviewer
```

### Using GitHub Copilot
```bash
# In VS Code with Copilot Chat:
@pib-analyst research JWT auth patterns in #codebase
@pib-dev implement authentication using existing patterns
@pib-reviewer check LEVER compliance
```

## Architecture

### Shared Foundation
Both systems use:
- **LEVER Framework**: Mandatory compliance (minimum 4/5 score)
- **Common Instructions**: `.github/copilot-instructions.md`
- **Unified Prompts**: `.github/copilotenhancer -> /quick:prompt-enhancers/`
- **Consistent Workflows**: `.github/copilothelp -> /quick:workflow-helps/`

### Agent/Participant Mapping

| Function | Claude Code Sub-Agent | GitHub Copilot Participant |
|----------|----------------------|---------------------------|
| Research | `analyst` | `@pib-analyst` |
| Architecture | `architect` | `@pib-architect` |
| Development | `dev-agent` | `@pib-dev` |
| Code Review | `code-reviewer` | `@pib-reviewer` |
| Testing | `qa-tester` | `@pib-qa` |
| Platform | `platform-engineer` | `@pib-platform` |
| Orchestration | `orchestrator` | `@pib-orchestrator` |

### Instruction Scopes (GitHub Copilot)
- **Analysis**: Research and pattern discovery (`analysis.instructions.md`)
- **Implementation**: LEVER-compliant development (`implementation.instructions.md`)
- **Testing**: Comprehensive test generation (`testing.instructions.md`)
- **Review**: Strict code review with scoring (`review.instructions.md`)
- **Global**: Repository-wide standards (`copilot-instructions.md`)

## File Structure

```
PIB-METHOD/
├── .claude/
│   ├── agents/                    # Claude Code sub-agents
│   ├── hooks/                     # Workflow automation
│   └── settings.json              # Claude Code config
│
├── .github/
│   ├── copilot-instructions.md   # Main Copilot instructions
│   ├── instructions/              # Scoped instruction files
│   │   ├── analysis.instructions.md
│   │   ├── implementation.instructions.md
│   │   ├── testing.instructions.md
│   │   └── review.instructions.md
│   ├── prompts/                   # Reusable prompt templates
│   │   ├── lever-analysis.prompt.md
│   │   └── feature-implementation.prompt.md
│   └── workflows/                 # GitHub Actions workflows
│
├── .vscode/
│   └── settings.json              # VS Code Copilot settings
│
└── pib-agent/
    └── personas-archived-20250725/  # Legacy personas (archived)
```

## LEVER Framework Enforcement

Both systems enforce:
1. **Leverage**: Search existing code first
2. **Extend**: Extend rather than create
3. **Verify**: Comprehensive testing
4. **Eliminate**: No duplication
5. **Reduce**: Minimal complexity

### Scoring System
- 0-5 points per principle
- Minimum average: 4/5
- Automatic rejection below threshold

## MCP Tool Integration

| Tool | Purpose | Available To |
|------|---------|--------------|
| Context7 | Library documentation | All except analyst |
| Perplexity | Web research | Analyst only |
| Firecrawl | Web scraping | Analyst only |
| Playwright | E2E testing | QA tester only |

## Workflow Example: Adding User Authentication

### Option 1: Claude Code (Automated)
```bash
> help -> /quick:workflow-helps:dev-command "implement JWT-based user authentication with refresh tokens"
# System automatically:
# 1. Researches existing auth patterns
# 2. Designs LEVER-compliant solution
# 3. Implements with pattern reuse
# 4. Generates comprehensive tests
# 5. Reviews with LEVER scoring
```

### Option 2: GitHub Copilot (Manual)
```bash
# Step 1: Research
@pib-analyst analyze existing authentication in #codebase

# Step 2: Plan
Toggle mode to "Planning"
Create JWT auth implementation plan

# Step 3: Implement
@pib-dev implement auth extending BaseAuthController

# Step 4: Test
Toggle mode to "Testing"
Generate auth module tests

# Step 5: Review
@pib-reviewer validate LEVER compliance
```

## VS Code Configuration

The `.vscode/settings.json` is pre-configured with:
```json
{
  "github.copilot.enable": true,
  "github.copilot.chat.codeGeneration.useInstructionFiles": true,
  "chat.promptFiles": true,
  "chat.promptFilesLocations": {
    ".githubenhancer -> /quick:prompt-enhancers": true
  },
  "chat.instructionsFilesLocations": {
    ".github": true,
    ".github/instructions": true
  }
}
```

## Best Practices

### When to Use Claude Code
- Complex multi-step features
- Automated workflow orchestration
- When you want hands-off development
- Parallel worktree development

### When to Use GitHub Copilot
- Quick code completions
- Interactive development
- Specific focused tasks
- When working in VS Code

### Seamless Integration
1. Both systems share the same standards
2. Code from either passes the same quality gates
3. Workflows are interchangeable
4. Documentation works for both

## Quality Assurance

### Automated Checks
- LEVER score calculation
- Test coverage verification
- Pattern compliance validation
- Security scanning

### Manual Reviews
- Architecture alignment
- Business logic correctness
- Performance considerations
- User experience impact

## Migration from Legacy System

The persona files have been archived to `pib-agent/personas-archived-20250725/`. The new system provides:
- Better tool integration
- Consistent quality standards
- Unified workflows
- Enhanced automation

## Getting Started

1. **Update VS Code settings** with the configuration above
2. **Review shared instructions** in `.github/copilot-instructions.md`
3. **Choose your tool** based on the task at hand
4. **Follow the workflows** for consistent results

## Support

- **Claude Code Issues**: Check `.claude/hooks/` for automation
- **GitHub Copilot Issues**: Verify VS Code settings
- **Quality Issues**: Review LEVER scores and compliance
- **Integration Issues**: Check `unified-config.json`

---

*PIB-METHOD: Where AI assistants work together for exceptional code quality*