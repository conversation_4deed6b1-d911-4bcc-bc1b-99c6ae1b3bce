name: Deploy to Production (Main Branch)

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v1
        with:
          service_account_key: ${{ secrets.GCP_SA_KEY }}
          project_id: partners-in-biz-85059
          
      - name: Configure Docker for GCR
        run: gcloud auth configure-docker
        
      - name: Build for Production
        run: |
          docker build \
            --build-arg FIREBASE_DATABASE_ID=default \
            --build-arg GIT_BRANCH=main \
            -t gcr.io/partners-in-biz-85059/covalonic:${{ github.sha }} \
            -t gcr.io/partners-in-biz-85059/covalonic:latest \
            .
            
      - name: Push to GCR
        run: |
          docker push gcr.io/partners-in-biz-85059/covalonic:${{ github.sha }}
          docker push gcr.io/partners-in-biz-85059/covalonic:latest
          
      - name: Deploy to Cloud Run (Production)
        run: |
          gcloud run deploy covalonic \
            --image gcr.io/partners-in-biz-85059/covalonic:${{ github.sha }} \
            --region us-central1 \
            --platform managed \
            --allow-unauthenticated \
            --set-env-vars "FIREBASE_DATABASE_ID=default,GIT_BRANCH=main,NODE_ENV=production" \
            --memory 512Mi \
            --cpu 1 \
            --timeout 300 \
            --max-instances 10