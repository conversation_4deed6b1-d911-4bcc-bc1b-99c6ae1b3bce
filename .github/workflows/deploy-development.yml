name: Deploy to Development

on:
  push:
    branches:
      - development

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v1
        with:
          service_account_key: ${{ secrets.GCP_SA_KEY }}
          project_id: covalonic-7127a
          
      - name: Configure Docker for GCR
        run: gcloud auth configure-docker
        
      - name: Build for Development
        run: |
          docker build \
            --build-arg FIREBASE_DATABASE_ID=development \
            --build-arg GIT_BRANCH=development \
            -t gcr.io/covalonic-7127a/covalonic-dev:${{ github.sha }} \
            -t gcr.io/covalonic-7127a/covalonic-dev:latest \
            .
            
      - name: Push to GCR
        run: |
          docker push gcr.io/covalonic-7127a/covalonic-dev:${{ github.sha }}
          docker push gcr.io/covalonic-7127a/covalonic-dev:latest
          
      - name: Deploy to Cloud Run (Development)
        run: |
          gcloud run deploy covalonic-dev \
            --image gcr.io/covalonic-7127a/covalonic-dev:${{ github.sha }} \
            --region us-central1 \
            --platform managed \
            --allow-unauthenticated \
            --set-env-vars "FIREBASE_DATABASE_ID=development,GIT_BRANCH=development,NODE_ENV=production" \
            --memory 512Mi \
            --cpu 1 \
            --timeout 300 \
            --max-instances 10