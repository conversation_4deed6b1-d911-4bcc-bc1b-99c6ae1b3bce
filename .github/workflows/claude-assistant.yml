name: <PERSON> Assistant

on:
  issue_comment:
    types: [created, edited]
  pull_request_review_comment:
    types: [created, edited]
  pull_request_review:
    types: [submitted]
  issues:
    types: [opened, edited]
  pull_request:
    types: [opened, edited, synchronize]

jobs:
  claude-response:
    # Only run if comment/description contains @claude mention
    if: >
      (github.event_name == 'issue_comment' && contains(github.event.comment.body, '@claude')) ||
      (github.event_name == 'pull_request_review_comment' && contains(github.event.comment.body, '@claude')) ||
      (github.event_name == 'pull_request_review' && contains(github.event.review.body, '@claude')) ||
      (github.event_name == 'issues' && contains(github.event.issue.body, '@claude')) ||
      (github.event_name == 'pull_request' && contains(github.event.pull_request.body, '@claude'))
    
    runs-on: ubuntu-latest
    
    # Timeout after 10 minutes to prevent runaway processes
    timeout-minutes: 10
    
    permissions:
      contents: read
      issues: write
      pull-requests: write
      checks: read
      actions: read
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          # Fetch full history for better context
          fetch-depth: 0
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Claude Code Action
        uses: anthropics/claude-code-action@beta
        with:
          # Primary authentication method
          anthropic_api_key: ${{ secrets.ANTHROPIC_API_KEY }}
          github_token: ${{ secrets.GITHUB_TOKEN }}
          
          # PIB-METHOD Configuration
          trigger_phrase: "@claude"
          max_tokens: 4096
          temperature: 0.1
          
          # PIB-METHOD Features
          pib_method_enabled: true
          lever_compliance: true
          pattern_recognition: true
          quality_gates: true
          
          # Context Enhancement
          include_context_files: |
            CLAUDE.md
            README.md
            package.json
            .github/copilot-instructions.md
          
          # LEVER Framework Integration
          lever_scoring: true
          min_lever_score: 4
          auto_reject_low_scores: true
          
          # MCP Tools Integration (if available)
          enable_mcp_tools: true
          context7_enabled: true
          perplexity_enabled: false  # Analyst agent only
          
          # Response Configuration
          response_format: "markdown"
          include_reasoning: true
          suggest_tests: true
          check_security: true
          
          # Rate Limiting
          max_requests_per_hour: 50
          cooldown_minutes: 2
          
          # Error Handling
          retry_attempts: 3
          fallback_on_error: true
          
        env:
          # Additional environment variables
          PIB_METHOD_VERSION: "3.0"
          LEVER_FRAMEWORK_ENABLED: "true"
          GITHUB_WORKSPACE: ${{ github.workspace }}
      
      - name: Add PIB-METHOD Context
        if: success()
        run: |
          echo "## PIB-METHOD Integration" >> $GITHUB_STEP_SUMMARY
          echo "This response was generated using PIB-METHOD standards:" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ LEVER Framework compliance enforced" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Pattern recognition enabled" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Quality gates active" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Security checks included" >> $GITHUB_STEP_SUMMARY
      
      - name: Handle Errors
        if: failure()
        uses: actions/github-script@v7
        with:
          script: |
            const issue_number = context.issue.number;
            const repo = context.repo;
            
            if (issue_number) {
              await github.rest.issues.createComment({
                ...repo,
                issue_number: issue_number,
                body: `❌ **Claude Assistant Error**
                
                I encountered an error while processing your request. This could be due to:
                
                - API rate limits
                - Invalid API key configuration  
                - Network connectivity issues
                - Malformed request
                
                **Troubleshooting Steps:**
                1. Check the [Actions tab](${context.serverUrl}/${repo.owner}/${repo.repo}/actions) for detailed error logs
                2. Verify the \`ANTHROPIC_API_KEY\` secret is configured correctly
                3. Ensure the request follows PIB-METHOD guidelines
                4. Try again in a few minutes if rate limited
                
                For setup issues, run: \`/setup-claude-actions --verify\`
                
                *This is an automated error response from the PIB-METHOD Claude Assistant.*`
              });
            }

  # Separate job for usage tracking and analytics  
  track-usage:
    if: always()
    runs-on: ubuntu-latest
    needs: claude-response
    
    steps:
      - name: Track Claude Usage
        uses: actions/github-script@v7
        with:
          script: |
            // Log usage for analytics (optional)
            console.log('Claude Assistant invoked:', {
              repository: context.repo,
              event: context.eventName,
              actor: context.actor,
              timestamp: new Date().toISOString()
            });

  # Job to update PIB-METHOD metrics (optional)
  update-metrics:
    if: success()
    runs-on: ubuntu-latest
    needs: claude-response
    
    steps:
      - name: Update PIB Metrics
        run: |
          echo "Updating PIB-METHOD metrics..."
          # Could integrate with analytics service
          # curl -X POST "$METRICS_ENDPOINT" -d "claude_request_completed"