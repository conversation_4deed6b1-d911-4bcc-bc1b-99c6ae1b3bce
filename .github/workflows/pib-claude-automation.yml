name: PIB-METHOD Claude Code Automation

on:
  issues:
    types: [opened, labeled]
  pull_request:
    types: [opened, synchronize]
  issue_comment:
    types: [created]

jobs:
  pib-automation:
    if: contains(github.event.comment.body, '@claude') || contains(github.event.issue.labels.*.name, 'claude') || contains(github.event.issue.labels.*.name, 'pib-dev')
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Setup Claude Code Environment
      run: |
        # Install Claude Code CLI
        curl -fsSL https://claude.ai/download/cli | sh
        
    - name: PIB-METHOD Context Setup
      run: |
        # Ensure PIB context is available
        if [ ! -f "CLAUDE.md" ]; then
          echo "# PIB-METHOD Claude Code Integration" > CLAUDE.md
          echo "" >> CLAUDE.md
          echo "## LEVER Framework Compliance Required" >> CLAUDE.md
          echo "All implementations must achieve minimum LEVER score of 4/5:" >> CLAUDE.md
          echo "- **L**everage: Reuse existing patterns and libraries" >> CLAUDE.md
          echo "- **E**xtend: Extend existing functionality over new creation" >> CLAUDE.md
          echo "- **V**erify: Implement comprehensive testing and validation" >> CLAUDE.md
          echo "- **E**liminate: Remove code duplication" >> CLAUDE.md
          echo "- **R**educe: Minimize complexity while maintaining functionality" >> CLAUDE.md
          echo "" >> CLAUDE.md
          echo "## Sub-Agent Architecture" >> CLAUDE.md
          echo "Use Claude Code sub-agents for specialized tasks:" >> CLAUDE.md
          echo "- dev-agent: Development implementation" >> CLAUDE.md
          echo "- code-reviewer: Quality assurance with LEVER scoring" >> CLAUDE.md
          echo "- qa-tester: Comprehensive testing with Playwright" >> CLAUDE.md
          echo "- architect: System design and technology selection" >> CLAUDE.md
        fi
        
    - name: Issue to PR Automation
      if: github.event_name == 'issues' && (contains(github.event.issue.labels.*.name, 'claude') || contains(github.event.issue.labels.*.name, 'pib-dev'))
      env:
        ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      run: |
        # Extract issue details
        ISSUE_TITLE="${{ github.event.issue.title }}"
        ISSUE_BODY="${{ github.event.issue.body }}"
        ISSUE_NUMBER="${{ github.event.issue.number }}"
        
        # Create branch name
        BRANCH_NAME="pib-feature/issue-${ISSUE_NUMBER}-$(echo "$ISSUE_TITLE" | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9]/-/g' | sed 's/--*/-/g' | sed 's/-$//')"
        
        # Setup git
        git config --global user.name "PIB-METHOD Bot"
        git config --global user.email "<EMAIL>"
        
        # Create and switch to new branch
        git checkout -b "$BRANCH_NAME"
        
        # Use Claude Code to implement the feature
        echo "Implementing: $ISSUE_TITLE" > implementation_request.md
        echo "" >> implementation_request.md
        echo "## Requirements" >> implementation_request.md
        echo "$ISSUE_BODY" >> implementation_request.md
        echo "" >> implementation_request.md
        echo "## PIB-METHOD Instructions" >> implementation_request.md
        echo "> Use the dev-agent sub-agent to implement this feature with full LEVER framework compliance (minimum 4/5 score)" >> implementation_request.md
        echo "> Use the code-reviewer sub-agent to validate implementation quality" >> implementation_request.md
        echo "> Use the qa-tester sub-agent to create comprehensive tests" >> implementation_request.md
        
        # Note: In a real implementation, you would integrate with Claude Code CLI here
        # For now, create the structure for manual Claude Code execution
        
        # Commit and push
        git add .
        git commit -m "PIB-METHOD: Implement $ISSUE_TITLE

- Auto-generated from issue #${ISSUE_NUMBER}
- Requires LEVER framework compliance validation
- Sub-agent coordination enabled

🤖 Generated with PIB-METHOD Claude Code Integration"
        
        git push origin "$BRANCH_NAME"
        
        # Create PR
        gh pr create \
          --title "PIB-METHOD: $ISSUE_TITLE" \
          --body "$(cat <<EOF
## Auto-Implementation from Issue #${ISSUE_NUMBER}

### PIB-METHOD Workflow
- [ ] LEVER Framework Compliance (minimum 4/5 score)
- [ ] dev-agent implementation completed
- [ ] code-reviewer validation passed
- [ ] qa-tester comprehensive testing
- [ ] Documentation updated

### Implementation Request
$ISSUE_BODY

### Sub-Agent Coordination
This PR implements the feature using PIB-METHOD sub-agent architecture:
1. **Analysis**: Requirements extracted from issue
2. **Implementation**: dev-agent with LEVER compliance
3. **Quality Assurance**: code-reviewer validation
4. **Testing**: qa-tester comprehensive coverage

### Manual Steps Required
1. Run Claude Code in this branch
2. Execute: \`> Use implementation_request.md to guide feature development\`
3. Ensure LEVER compliance score ≥ 4/5
4. Complete all PIB-METHOD quality gates

---
🤖 Auto-generated by PIB-METHOD Claude Code Integration
Closes #${ISSUE_NUMBER}
EOF
)" \
          --assignee "${{ github.actor }}" \
          --label "pib-method,auto-generated,needs-claude"

    - name: PR LEVER Compliance Check
      if: github.event_name == 'pull_request'
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      run: |
        # Check for LEVER compliance indicators in PR
        PR_BODY="${{ github.event.pull_request.body }}"
        PR_DIFF=$(gh pr diff ${{ github.event.pull_request.number }})
        
        # Create compliance checklist
        COMPLIANCE_REPORT="## PIB-METHOD LEVER Compliance Check\n\n"
        
        # Check for LEVER indicators
        LEVER_SCORE=0
        
        if echo "$PR_DIFF" | grep -qi "leverage\|reuse\|existing"; then
          COMPLIANCE_REPORT+="\n- ✅ **Leverage**: Evidence of reusing existing patterns"
          LEVER_SCORE=$((LEVER_SCORE + 1))
        else
          COMPLIANCE_REPORT+="\n- ❌ **Leverage**: No evidence of pattern reuse"
        fi
        
        if echo "$PR_DIFF" | grep -qi "extend\|enhance\|improve"; then
          COMPLIANCE_REPORT+="\n- ✅ **Extend**: Evidence of extending existing functionality"
          LEVER_SCORE=$((LEVER_SCORE + 1))
        else
          COMPLIANCE_REPORT+="\n- ❌ **Extend**: No evidence of extending existing code"
        fi
        
        if echo "$PR_DIFF" | grep -qi "test\|spec\|\.test\.\|\.spec\."; then
          COMPLIANCE_REPORT+="\n- ✅ **Verify**: Testing implemented"
          LEVER_SCORE=$((LEVER_SCORE + 1))
        else
          COMPLIANCE_REPORT+="\n- ❌ **Verify**: No testing evidence found"
        fi
        
        if echo "$PR_DIFF" | grep -qi "remove\|delete\|cleanup"; then
          COMPLIANCE_REPORT+="\n- ✅ **Eliminate**: Evidence of code cleanup"
          LEVER_SCORE=$((LEVER_SCORE + 1))
        else
          COMPLIANCE_REPORT+="\n- ❓ **Eliminate**: No duplication removal evidence"
        fi
        
        if [ $(echo "$PR_DIFF" | wc -l) -lt 200 ]; then
          COMPLIANCE_REPORT+="\n- ✅ **Reduce**: Implementation appears concise"
          LEVER_SCORE=$((LEVER_SCORE + 1))
        else
          COMPLIANCE_REPORT+="\n- ❓ **Reduce**: Large implementation - verify necessity"
        fi
        
        COMPLIANCE_REPORT+="\n\n**LEVER Score**: ${LEVER_SCORE}/5"
        
        if [ $LEVER_SCORE -ge 4 ]; then
          COMPLIANCE_REPORT+="\n\n✅ **LEVER Compliance**: PASSED (≥4/5)"
        else
          COMPLIANCE_REPORT+="\n\n❌ **LEVER Compliance**: FAILED (<4/5) - Manual review required"
        fi
        
        COMPLIANCE_REPORT+="\n\n*Automated analysis by PIB-METHOD. Final validation should be done by code-reviewer sub-agent.*"
        
        # Post comment with compliance check
        gh pr comment ${{ github.event.pull_request.number }} --body "$COMPLIANCE_REPORT"

    - name: Claude Code Mention Handler
      if: github.event_name == 'issue_comment' && contains(github.event.comment.body, '@claude')
      env:
        ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      run: |
        # Extract the request after @claude mention
        COMMENT_BODY="${{ github.event.comment.body }}"
        CLAUDE_REQUEST=$(echo "$COMMENT_BODY" | sed -n 's/.*@claude[[:space:]]*\(.*\)/\1/p')
        
        # Create context for Claude Code
        echo "## GitHub Context" > claude_context.md
        echo "**Repository**: ${{ github.repository }}" >> claude_context.md
        echo "**Issue/PR**: #${{ github.event.issue.number || github.event.pull_request.number }}" >> claude_context.md
        echo "**Requested by**: @${{ github.event.comment.user.login }}" >> claude_context.md
        echo "" >> claude_context.md
        echo "## Request" >> claude_context.md
        echo "$CLAUDE_REQUEST" >> claude_context.md
        echo "" >> claude_context.md
        echo "## PIB-METHOD Instructions" >> claude_context.md
        echo "> Use appropriate sub-agents for this request" >> claude_context.md
        echo "> Ensure LEVER framework compliance if code changes are involved" >> claude_context.md
        echo "> Provide implementation guidance following PIB-METHOD standards" >> claude_context.md
        
        # Post guidance comment
        gh pr comment ${{ github.event.issue.number || github.event.pull_request.number }} --body "$(cat <<EOF
## Claude Code Integration Ready

Your request has been prepared for Claude Code execution:

\`\`\`markdown
$CLAUDE_REQUEST
\`\`\`

### To Execute:
1. **Checkout this repository locally**
2. **Run Claude Code in the repository root**
3. **Use the request**: \`> $(echo "$CLAUDE_REQUEST")\`

### PIB-METHOD Sub-Agents Available:
- \`dev-agent\` - Development implementation
- \`code-reviewer\` - Quality validation with LEVER scoring  
- \`qa-tester\` - Comprehensive testing
- \`architect\` - System design
- \`analyst\` - Research and analysis

### Quality Requirements:
- ✅ LEVER Framework compliance (minimum 4/5 score)
- ✅ Comprehensive testing if code changes
- ✅ Documentation updates as needed

---
🤖 PIB-METHOD GitHub Actions Integration
EOF
)"