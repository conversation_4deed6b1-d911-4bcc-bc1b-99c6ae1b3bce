{"name": "PIB Development Workflow", "description": "Q-LEVER compliant development workflow with automatic task management", "version": "1.0.0", "triggers": ["implement", "build", "create", "develop", "code"], "variables": {"leverScore": 0, "tasksCompleted": [], "currentTask": null, "codebaseSearched": false}, "stages": [{"id": "question", "name": "Question Phase", "type": "interactive", "actions": [{"type": "prompt", "message": "Let me understand what we're building:", "questions": ["What is the main goal of this implementation?", "Are there any specific constraints or requirements?", "What existing patterns should I consider?", "What's the expected timeline?"]}, {"type": "search", "context": "#codebase", "query": "${user.requirement}", "saveAs": "existingPatterns"}]}, {"id": "planning", "name": "Task Planning", "type": "automatic", "actions": [{"type": "decompose", "input": "${user.requirement}", "template": ["Analyze requirements and constraints", "Search for existing patterns", "Design solution architecture", "Implement core functionality", "Add error handling", "Write comprehensive tests", "LEVER compliance review", "Documentation update"], "output": "taskList"}, {"type": "display", "format": "checklist", "data": "${taskList}"}]}, {"id": "leverage", "name": "Leverage Phase", "type": "automatic", "actions": [{"type": "analyze", "input": "${existingPatterns}", "criteria": ["Similar functionality", "Reusable components", "Base classes to extend", "Utility functions", "Configuration patterns"], "output": "reuseOpportunities"}, {"type": "score", "metric": "leverage", "value": "${reuseOpportunities.count > 0 ? 5 : 2}", "updateTotal": true}]}, {"id": "implementation", "name": "Implementation Phase", "type": "iterative", "foreach": "${taskList}", "actions": [{"type": "updateStatus", "task": "${current}", "status": "in_progress"}, {"type": "implement", "task": "${current}", "constraints": ["Reuse existing patterns where possible", "Follow project conventions", "Include error handling", "Add inline documentation for complex logic"]}, {"type": "validate", "checks": ["No syntax errors", "Follows naming conventions", "Error handling present", "No obvious duplication"]}, {"type": "updateStatus", "task": "${current}", "status": "completed"}, {"type": "displayProgress", "showLeverScore": true}]}, {"id": "verification", "name": "Verification Phase", "type": "automatic", "actions": [{"type": "generateTests", "coverage": "80%", "types": ["unit", "integration"], "framework": "auto-detect"}, {"type": "runTests", "stopOnFailure": false, "output": "testResults"}, {"type": "score", "metric": "verification", "value": "${testResults.coverage >= 80 ? 5 : 3}", "updateTotal": true}]}, {"id": "elimination", "name": "Elimination Phase", "type": "automatic", "actions": [{"type": "detectDuplication", "threshold": 10, "output": "duplicates"}, {"type": "refactor", "targets": "${duplicates}", "strategy": "extract-common"}, {"type": "score", "metric": "elimination", "value": "${duplicates.length == 0 ? 5 : 3}", "updateTotal": true}]}, {"id": "reduction", "name": "Reduction Phase", "type": "automatic", "actions": [{"type": "analyzeComplexity", "metrics": ["cyclomatic", "cognitive"], "output": "complexity"}, {"type": "simplify", "targets": "${complexity.high}", "strategies": ["extract-method", "early-return", "reduce-nesting", "use-guard-clauses"]}, {"type": "score", "metric": "reduction", "value": "${complexity.average < 10 ? 5 : 3}", "updateTotal": true}]}, {"id": "review", "name": "LEVER Review", "type": "summary", "actions": [{"type": "calculateScore", "formula": "(leverage + extend + verify + eliminate + reduce) / 5", "output": "finalScore"}, {"type": "generateReport", "template": "## LEVER Compliance Report\n\n**Final Score**: ${finalScore}/5\n\n### Breakdown:\n- **L**everage: ${leverage}/5 - ${reuseOpportunities.summary}\n- **E**xtend: ${extend}/5 - ${extensions.summary}\n- **V**erify: ${verify}/5 - Coverage: ${testResults.coverage}%\n- **E**liminate: ${eliminate}/5 - Duplicates removed: ${duplicates.removed}\n- **R**educe: ${reduce}/5 - Complexity: ${complexity.final}\n\n### Tasks Completed:\n${taskList.completed}\n\n### Next Steps:\n${suggestions}"}, {"type": "requireApproval", "condition": "${finalScore < 4}", "message": "LEVER score is below 4/5. Would you like me to improve the implementation?"}]}], "contextEnhancers": [{"variable": "#codebase", "search": "always", "priority": "high"}, {"variable": "#problems", "fix": "before-implementation", "priority": "high"}, {"variable": "#terminal", "monitor": "test-execution", "priority": "medium"}], "outputFormat": {"progressTracking": true, "showChecklist": true, "leverScoreVisible": true, "autoSuggestNext": true}}