@tailwind base;
@tailwind components;
@tailwind utilities;
@layer base {
  :root {
    --color-test: 255 115 179;

    --color-primary: 130  170  227;
    --color-primary_focus: 145  216  228;
    --color-primary_content: 255  255  255;
    --color-primary_focus_content: 255  255  255;

    --color-primarys: 0 114 255;
    --color-primarys_focus: 145  216  228;
    --color-primarys_content: 255  255  255;
    --color-primarys_focus_content: 255  255  255;

    --color-secondary: 0  230  10;
    --color-secondary_focus: 0 255 155;
    --color-secondary_content: 250 250 250;
    --color-secondary_focus_content: 255  255  255;

    --color-tertiary: 22 223 156;
    --color-tertiary_focus: 10 169 116;
    --color-tertiary_content: 250 250 250;
    --color-tertiary_focus_content: 255  255  255;

    --color-accent: 0 213 255;
    --color-accent_focus: 64 204 231;
    --color-accent_content: 250 250 250;
    --color-accent_focus_content: 217 227 237;

    --color-neutral: 240 240 240;
    --color-neutral_focus: 227 227 227;
    --color-neutral_content: 78 75 75;
    --color-neutral_focus_content: 78 75 75;

    --color-base_100: 255 255 255;
    --color-base_200: 245 245 255;
    --color-base_300: 245 245 245;
    --color-base_content: 89 89 131;
    --color-base_200_content: 89 89 131;
    --color-base_300_content: 89 89 131;


    --color-base_dark_100: 3 7 9;/* gray-800 */
    --color-base_dark_200: 12 17 20; /* gray-700 */
    --color-base_dark_300: 33 33 33; /* base_dark_300 */
    --color-base_dark_content: 152 165 192;
    --color-base_dark_200_content: 156  163  175;
    --color-base_dark_300_content: 156  163  175;


    --color-info: 87 168 188;
    --color-info_focus: 87 168 188;
    --color-info_content: 255 255 255;
    --color-info_content_focus: 255 255 255;


    --color-success: 28 173 26;
    --color-success_focus: 28 173 26;
    --color-success_content: 255 255 255;
    --color-success_content_focus: 255 255 255;


    --color-warning: 255 213 0;
    --color-warning_focus: 255 213 0;
    --color-warning_content: 255 255 255;
    --color-warning_content_focus: 255 255 255;


    --color-error: 255 0 0;
    --color-error_focus: 255 0 0;
    --color-error_content: 255 255 255;
    --color-error_content_focus: 255 255 255;


    /* ... */
  }
}
/* For Webkit-base_100 browsers (Chrome  Safari and Opera) */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* For IE  Edge and Firefox */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.no-click{pointer-events:none}
.allow-click{pointer-events:auto}

/* Mobile responsiveness fixes */
@media (max-width: 768px) {
  /* Ensure proper mobile viewport handling */
  html, body {
    overflow-x: hidden;
    width: 100%;
    position: relative;
  }

  /* Fix analytics dashboard mobile issues */
  .analytics-dashboard {
    padding: 1rem;
    overflow-x: hidden;
  }

  /* Ensure modals are properly sized on mobile */
  .o_modal {
    padding: 0.5rem;
    max-height: 100vh;
    overflow-y: auto;
  }

  /* Fix chart containers on mobile */
  .chart-container {
    overflow-x: auto;
    min-height: 200px;
  }

  /* Ensure grid layouts stack properly on mobile */
  .grid {
    grid-template-columns: 1fr !important;
  }

  /* Fix sidebar issues on mobile */
  .sidebar-mobile {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar-mobile.open {
    transform: translateX(0);
  }
}

/* Enhanced rotation support for mobile devices */
@media screen and (orientation: landscape) and (max-width: 1024px) {
  /* Ensure proper viewport handling in landscape */
  html, body {
    height: 100vh;
    width: 100vw;
    overflow-x: hidden;
  }

  .mobile-landscape {
    height: 100vh;
    overflow-y: auto;
  }

  /* Adjust main content for landscape */
  #app {
    padding-top: 60px !important; /* Reduce top padding in landscape */
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  /* Adjust sidebar for landscape */
  .sidebar-compact {
    width: 60px !important; /* Make sidebar narrower in landscape */
  }

  /* Adjust navbar height for landscape */
  nav {
    height: 60px;
    padding: 0.5rem 1rem;
  }

  /* Ensure modals fit in landscape */
  .o_modal {
    max-height: 90vh;
    margin: 1rem;
  }

  /* Adjust form layouts for landscape */
  .form-container {
    max-height: 80vh;
    overflow-y: auto;
  }

  /* Make charts responsive in landscape */
  .chart-container {
    height: 250px !important;
    overflow: hidden;
  }
}

@media screen and (orientation: portrait) and (max-width: 768px) {
  /* Portrait-specific optimizations */
  html, body {
    height: 100vh;
    width: 100vw;
    overflow-x: hidden;
  }

  /* Ensure proper spacing in portrait */
  #app {
    padding-top: 72px;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  /* Full-width forms in portrait */
  .form-container {
    width: 100%;
    padding: 1rem;
  }
}

/* Orientation-specific classes */
.mobile-landscape {
  /* Landscape-specific styles applied via JavaScript */
}

.mobile-portrait {
  /* Portrait-specific styles applied via JavaScript */
}

/* Smooth transitions for orientation changes */
@media screen and (max-width: 1024px) {
  * {
    transition: padding 0.3s ease, margin 0.3s ease, height 0.3s ease;
  }

  /* Prevent zoom on input focus */
  input, textarea, select {
    font-size: 16px !important;
  }
}

/* Touch-friendly interface improvements */
@media (hover: none) and (pointer: coarse) {
  /* Increase touch targets */
  button, .btn, .clickable {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improve scrolling on touch devices */
  .scrollable {
    -webkit-overflow-scrolling: touch;
  }

  /* Better touch feedback */
  button:active, .btn:active, .clickable:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }
}

.my-actions { margin: 0 2em; }
.order-1 { order: 1; }
.order-2 { order: 2; }
.order-3 { order: 3; }

.m-input-tags {
  border: none !important
}

.m-phone-number-input {
  border: none !important
}

.right-gap {
  margin-right: auto;
}
.w-1\/7 {
    width: 14.2657143%;
  }

    /*Sidebar compact*/
    @media (min-width: 768px) {
      .sidebar-compact {
        z-index:200;
      }
      .sidebar-compact .hidden-compact {
        visibility:hidden;
        height:0px;
        width:0px;
        opacity:0;
        transition: visibility 0s, opacity 1.5s linear;
      }
      .sidebar-compact .hidden-compact span {
        display:none;
      }
      .flatpickr-day.sidebar-compact .hidden-compact span {
        visibility: hidden;
      }
      .sidebar-compact .logo-compact {
        visibility:visible;
        height:auto;
        width:auto;
        opacity:1;
      }
      .sidebar-compact ul li ul, .sidebar-compact a span {
        display:none;
      }
      .flatpickr-day.sidebar-compact ul li ul, .flatpickr-day.sidebar-compact a span {
        visibility: hidden;
      }
      .sidebar-compact:hover {
        width: 16rem;
      }
      .sidebar-compact:hover .hidden-compact {
        visibility:visible;
        height:auto;
        width:auto;
        opacity:1;
      }
      .sidebar-compact:hover .hidden-compact span {
        display:inline-block;
      }
      .sidebar-compact:hover ul li ul, .sidebar-compact:hover .box-banner {
        display:block;
      }
      .sidebar-compact:hover a span {
        display:inline-block;
      }
      .sidebar-compact:hover .logo-compact {
        visibility:hidden;
        height:0px;
        width:0px;
        opacity:0;
        transition: visibility 0s, opacity 1.5s linear;
      }
      .sidebar-compact .box-banner {
        display:none;
      }
      .flatpickr-day.sidebar-compact .box-banner {
        visibility: hidden;
      }
    }

  
  /* dropzone  */
  .dropzone, .dropzone * {
    box-sizing: border-box;
  }
  .dropzone {
    min-height: 150px;
    border: 2px solid rgba(0, 0, 0, 0.3);
    background: white;
    padding: 20px 20px;
  }
  .dropzone.dz-clickable {
    cursor: pointer;
  }
  .dropzone.dz-clickable * {
    cursor: default;
  }
  .dropzone.dz-clickable .dz-message, .dropzone.dz-clickable .dz-message * {
    cursor: pointer;
  }
  .dropzone.dz-started .dz-message {
    display: none;
  }
  .dropzone.dz-drag-hover {
    border-style: solid;
  }
  .dropzone.dz-drag-hover .dz-message {
    opacity: 0.5;
  }
  .dropzone .dz-message {
    text-align: center;
    margin: 2em 0;
  }
  .dropzone .dz-message .dz-button {
    background: none;
    color: inherit;
    border: none;
    padding: 0;
    font: inherit;
    cursor: pointer;
    outline: inherit;
  }
  .dropzone .dz-preview {
    position: relative;
    display: inline-block;
    vertical-align: top;
    margin: 16px;
    min-height: 100px;
  }
  .dropzone .dz-preview:hover {
    z-index: 1000;
  }
  .dropzone .dz-preview:hover .dz-details {
    opacity: 1;
  }
  .dropzone .dz-preview.dz-file-preview .dz-image {
    border-radius: 20px;
    background: #999;
    background: linear-gradient(to bottom, #eee, #ddd);
  }
  .dropzone .dz-preview.dz-file-preview .dz-details {
    opacity: 1;
  }
  .dropzone .dz-preview.dz-image-preview {
    background: white;
  }
  .dropzone .dz-preview.dz-image-preview .dz-details {
    transition: opacity 0.2s linear;
  }
  .dropzone .dz-preview .dz-remove {
    font-size: 14px;
    text-align: center;
    display: block;
    cursor: pointer;
    border: none;
  }
  .dropzone .dz-preview .dz-remove:hover {
    text-decoration: underline;
  }
  .dropzone .dz-preview:hover .dz-details {
    opacity: 1;
  }
  .dropzone .dz-preview .dz-details {
    z-index: 20;
    position: absolute;
    top: 0;
    opacity: 0;
    font-size: 13px;
    min-width: 100%;
    max-width: 100%;
    padding: 2em 1em;
    text-align: center;
    color: rgba(0, 0, 0, 0.9);
    line-height: 150%;
  }
  [dir="ltr"] .dropzone .dz-preview .dz-details {
    left: 0;
  }
  [dir="rtl"] .dropzone .dz-preview .dz-details {
    right: 0;
  }
  .dropzone .dz-preview .dz-details .dz-size {
    margin-bottom: 1em;
    font-size: 16px;
  }
  .dropzone .dz-preview .dz-details .dz-filename {
    white-space: nowrap;
  }
  .dropzone .dz-preview .dz-details .dz-filename:hover span {
    border: 1px solid rgba(200, 200, 200, 0.8);
    background-color: rgba(255, 255, 255, 0.8);
  }
  .dropzone .dz-preview .dz-details .dz-filename:not(:hover) {
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .dropzone .dz-preview .dz-details .dz-filename:not(:hover) span {
    border: 1px solid transparent;
  }
  .dropzone .dz-preview .dz-details .dz-filename span, .dropzone .dz-preview .dz-details .dz-size span {
    background-color: rgba(255, 255, 255, 0.4);
    padding: 0 0.4em;
    border-radius: 3px;
  }
  .dropzone .dz-preview:hover .dz-image img {
    transform: scale(1.05, 1.05);
    filter: blur(8px);
  }
  .dropzone .dz-preview .dz-image {
    border-radius: 20px;
    overflow: hidden;
    width: 120px;
    height: 120px;
    position: relative;
    display: block;
    z-index: 10;
  }
  .dropzone .dz-preview .dz-image img {
    display: block;
  }
  .dropzone .dz-preview.dz-success .dz-success-mark {
    animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1);
  }
  .dropzone .dz-preview.dz-error .dz-error-mark {
    opacity: 1;
    animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1);
  }
  .dropzone .dz-preview .dz-success-mark, .dropzone .dz-preview .dz-error-mark {
    pointer-events: none;
    opacity: 0;
    z-index: 500;
    position: absolute;
    display: block;
    top: 50%;
  }
  [dir="ltr"] .dropzone .dz-preview .dz-success-mark,[dir="ltr"] .dropzone .dz-preview .dz-error-mark {
    left: 50%;
  }
  [dir="rtl"] .dropzone .dz-preview .dz-success-mark,[dir="rtl"] .dropzone .dz-preview .dz-error-mark {
    right: 50%;
  }
  .dropzone .dz-preview .dz-success-mark svg, .dropzone .dz-preview .dz-error-mark svg {
    display: block;
    width: 54px;
    height: 54px;
  }
  .dropzone .dz-preview.dz-processing .dz-progress {
    opacity: 1;
    transition: all 0.2s linear;
  }
  .dropzone .dz-preview.dz-complete .dz-progress {
    opacity: 0;
    transition: opacity 0.4s ease-in;
  }
  .dropzone .dz-preview:not(.dz-processing) .dz-progress {
    animation: pulse 6s ease infinite;
  }
  .dropzone .dz-preview .dz-progress {
    opacity: 1;
    z-index: 1000;
    pointer-events: none;
    position: absolute;
    height: 16px;
    top: 50%;
    margin-top: -8px;
    width: 80px;
    background: rgba(255, 255, 255, 0.9);
    transform: scale(1);
    border-radius: 8px;
    overflow: hidden;
  }
  [dir="ltr"] .dropzone .dz-preview .dz-progress {
    left: 50%;
    margin-left: -40px;
  }
  [dir="rtl"] .dropzone .dz-preview .dz-progress {
    right: 50%;
    margin-right: -40px;
  }
  .dropzone .dz-preview .dz-progress .dz-upload {
    background: #333;
    background: linear-gradient(to bottom, #666, #444);
    position: absolute;
    top: 0;
    bottom: 0;
    width: 0;
    transition: width 300ms ease-in-out;
  }
  [dir="ltr"] .dropzone .dz-preview .dz-progress .dz-upload {
    left: 0;
  }
  [dir="rtl"] .dropzone .dz-preview .dz-progress .dz-upload {
    right: 0;
  }
  .dropzone .dz-preview.dz-error .dz-error-message {
    display: block;
  }
  .dropzone .dz-preview.dz-error:hover .dz-error-message {
    opacity: 1;
    pointer-events: auto;
  }
  .dropzone .dz-preview .dz-error-message {
    pointer-events: none;
    z-index: 1000;
    position: absolute;
    display: block;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 8px;
    font-size: 13px;
    top: 130px;
    width: 140px;
    background: #be2626;
    background: linear-gradient(to bottom, #be2626, #a92222);
    padding: 0.5em 1.2em;
    color: white;
  }
  [dir="ltr"] .dropzone .dz-preview .dz-error-message {
    left: -10px;
  }
  [dir="rtl"] .dropzone .dz-preview .dz-error-message {
    right: -10px;
  }
  .dropzone .dz-preview .dz-error-message:after {
    content: '';
    position: absolute;
    top: -6px;
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid #be2626;
  }
  
  /*multiple upload*/
  .multiple-dropzone {
    border: #e6e6e6 1px solid;
  }
  .multiple-dropzone .dz-message {
    margin-left:0px;
    margin-right:0px;
    margin-top:0.5rem;
    margin-bottom:0.5rem;
  }
  .multiple-dropzone.dz-started .dz-message {
    display: inline-block;
    width: 100px;
    transition: 0.2s;
  }
  .multiple-dropzone.dz-started .dz-message .pre-upload {
    display: none;
  }
  .multiple-dropzone .after-upload {
    display: none;
  }
  .multiple-dropzone.dz-started .after-upload {
    display: block;
    border-radius: 20px;
    border: #ddd 2px dashed;
    font-size: 70px;
    color: #444;
    padding: 1rem;
    line-height: 100%;
  }
  .multiple-dropzone .dz-preview .dz-remove {
    padding:0.5rem;
  }
  .multiple-dropzone .dz-preview .dz-error-message {
    top: 140px;
  }
  .multiple-dropzone .dz-preview {
    margin: 10px;
  }
  .multiple-dropzone .dz-preview .dz-image {
    border: #ddd 1px solid;
    width: 100px;
    height: 100px;
  }
  
  /*single upload*/
  .single-dropzone {
    border: #e6e6e6 1px solid;
    width:100%;
  }
  .single-dropzone .dz-message {
    margin-left:0px;
    margin-right:0px;
    margin-top:0.5rem;
    margin-bottom:0.5rem;
  }
  .single-dropzone .dz-preview {
    display: flex;
    justify-content: center;
    flex-direction: column;
  }
  .single-dropzone .dz-preview .dz-image {
    margin: 0 auto;
  }
  .single-dropzone .dz-preview .dz-error-message {
    top: 150px;
  }
  [dir="ltr"] .single-dropzone .dz-preview .dz-error-message {
    left: 40%;
  }
  [dir="rtl"] .single-dropzone .dz-preview .dz-error-message {
    right: 40%;
  }
  .dark .multiple-dropzone, .dark .single-dropzone {
    background: #1e1f48;
    border: #7c8086 1px solid;
  }
  .dark .multiple-dropzone.dz-started .after-upload, .dark .single-dropzone.dz-started .after-upload {
    border: #7c8086 2px dashed;
  }
  .dark .multiple-dropzone .dz-preview .dz-image, .dark .single-dropzone .dz-preview .dz-image {
    border: #7c8086 1px solid;
  }
  .dark .multiple-dropzone .dz-preview.dz-image-preview, .dark .single-dropzone .dz-preview.dz-image-preview {
    background: #1e1f48;
  }
  @media (max-width: 568px) {
    .multiple-dropzone.dz-started .dz-message {
      width: 40%;
    }
  }

  #EmailView {
    overflow: hidden;
 }
  #EmailView .logo-section {
    min-width: 250px;
 }
  #EmailView .help-icon-gray {
    border: 2px solid #848484;
 }
  #EmailView .input-width {
    max-width: 700px;
 }
  #EmailView .custom-gray-color {
    background-color: #e8e8e8;
 }
  #EmailView .side-menu {
    width: 300px;
 }
  #EmailView .side-menu-item {
    width: 250px;
 }
  #NewMessageSection {
    overflow: hidden;
    width: 560px;
    height: 570px;
 }

 #MessageRow .truncate-from {
  width: 170px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
#MessageRow .truncate-subject {
  max-width: 200px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
#MessageRow .truncate-body {
  max-width: 400px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

    
.o_container{ @apply mx-auto max-w-7xl;}
.o_label{ @apply inline-block mb-2;}
.o_label_small{ @apply text-xs font-semibold;}
.o_label_large{ @apply px-1 text-lg font-semibold;}
.o_pill{ @apply flex items-center content-center px-1 text-xs rounded-full bg-primarys text-primarys_content;}
.o_input{ @apply relative w-full px-4 py-2 overflow-x-auto leading-5 text-base_content bg-base_100 border border-base_300 rounded focus:outline-none focus:border-gray-400 focus:ring-0 dark:text-base_dark_200_content dark:bg-base_dark_200 dark:border-base_dark_200 dark:focus:border-gray-600}
.o_input2 { @apply relative w-full px-4 py-2 overflow-x-auto leading-5 text-base_content bg-base_300 border rounded focus:outline-none focus:border-gray-400 focus:ring-0 dark:text-base_dark_300_content dark:bg-base_dark_100 dark:border-base_dark_100 dark:focus:border-gray-600}
.o_input_no_border{ @apply relative w-full px-4 py-2 overflow-x-auto leading-5 text-base_content bg-base_100 rounded focus:outline-none focus:border-gray-400 focus:ring-0 dark:text-base_dark_200_content dark:bg-base_dark_200 dark:border-base_dark_200 dark:focus:border-gray-600}
.o_input_small{ @apply relative px-2 py-0 overflow-x-auto text-xs leading-5 border rounded text-base_content bg-base_100 border-base_300 focus:outline-none focus:border-gray-400 focus:ring-0 dark:text-base_dark_200_content dark:bg-base_dark_200 dark:border-base_dark_200 dark:focus:border-gray-600;}
.o_gradient{ @apply bg-gradient-to-tl hover:bg-gradient-to-br from-primarys to-primarys_focus text-primarys_content;}
.o_btn{ @apply inline-block w-full px-6 py-3 mb-0 text-xs font-bold text-center uppercase align-middle transition-all border-0 rounded-lg shadow-lg cursor-pointer text-base_100 shadow-base_dark_300;}
.o_btn_thin{ @apply flex items-center w-full px-3 mb-0 text-xs font-bold text-center uppercase rounded-lg shadow-lg cursor-pointer hover:text-primarys dark:text-base_100 hover:bg-gray-600 dark:shadow-base_dark_300;}
.o_btn_primarys{ @apply inline-block px-4 py-1 rounded cursor-pointer bg-primarys text-primarys_content hover:bg-primarys_focus disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50;}
.o_btn_primarys_border{ @apply inline-block px-4 py-1 border rounded cursor-pointer border-primarys text-primarys hover:bg-primarys_focus disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50;}
.o_btn_secondary{ @apply inline-block px-4 py-1 rounded cursor-pointer bg-secondary text-secondary_content hover:bg-secondary_focus disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50;}
.o_btn_secondary_border{ @apply inline-block px-4 py-1 border rounded cursor-pointer border-secondary text-secondary hover:bg-secondary_focus disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50;}
.o_btn_tertiary{ @apply inline-block px-4 py-1 rounded cursor-pointer bg-accent text-accent_content hover:bg-accent_focus disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50;}
.o_btn_tertiary_border{ @apply inline-block px-4 py-1 border rounded cursor-pointer border-accent text-accent hover:bg-accent_focus disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50;}
.o_btn_neutral{ @apply inline-block px-4 py-1 rounded cursor-pointer bg-neutral text-neutral_content hover:bg-neutral_focus disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50;}
.o_btn_neutral_border{ @apply inline-block px-4 py-1 border rounded cursor-pointer border-neutral text-neutral hover:bg-neutral_focus disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50;}
.o_btn_base_100{ @apply inline-block px-4 py-1 rounded cursor-pointer bg-base_100 text-base_content hover:bg-base_200 disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50;}
.o_btn_base_100_border{ @apply inline-block px-4 py-1 border rounded cursor-pointer border-base_100 text-base_100 hover:bg-base_200 disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50;}
.o_btn_accent{ @apply inline-block px-4 py-1 rounded cursor-pointer bg-accent text-accent_content hover:bg-accent_focus disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50;}
.o_btn_accent_border{ @apply inline-block px-4 py-1 border rounded cursor-pointer border-accent text-accent hover:bg-accent_focus hover:text-accent_content disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50;}
.o_btn_warning{ @apply inline-block px-4 py-1 rounded cursor-pointer bg-warning text-warning_content hover:bg-warning hover:text-base_100 disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50;}
.o_btn_warning_border{ @apply inline-block px-4 py-1 border rounded cursor-pointer border-warning text-warning hover:bg-warning hover:text-warning_content disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50;}
.o_btn_info{ @apply inline-block px-4 py-1 rounded cursor-pointer bg-info text-info_content hover:bg-info disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50;}
.o_btn_info_border{ @apply inline-block px-4 py-1 border rounded cursor-pointer hover:text-base_100 border-info text-info hover:bg-info disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50;}
.o_btn_icon{ @apply inline-block p-1 transition duration-200 ease-in-out rounded-full shadow-lg cursor-pointer select-none dark:shadow-base_dark_300 hover:border-b ;}
.o_btn_icon_square{ @apply inline-block p-1 transition duration-200 ease-in-out rounded-lg shadow-lg cursor-pointer select-none dark:shadow-base_dark_300 hover:border-y;}
.o_btn_icon_square_small{ @apply inline-block text-xs transition duration-200 ease-in-out rounded-lg shadow-lg cursor-pointer select-none dark:shadow-base_dark_300 hover:shadow-gray-600 bg-base_100 hover:bg-primarys hover:text-base_100;}
.text_area { @apply relative w-full px-4 py-2 overflow-x-auto leading-5 text-base_dark_100 bg-base_100 border border-base_300 rounded focus:outline-none focus:border-gray-400 focus:ring-0 dark:text-base_300 dark:bg-base_dark_200 dark:border-base_dark_200 dark:focus:border-gray-600} 
.o_checkbox{ @apply mb-0 text-xs font-bold text-center uppercase align-middle transition-all border rounded-lg shadow cursor-pointer border-neutral_focus text-base_100 checked:bg-blue-600;}
.o_card{ @apply p-4 rounded-lg shadow-lg dark:shadow-base_dark_300 theme_100;}
.o_card_soft{ @apply p-4 rounded-lg shadow-xl dark:shadow-base_dark_300 theme_200;}
.o_modal{ @apply fixed top-0 left-0 right-0 z-50 flex justify-center p-3 mx-auto overflow-x-hidden overflow-y-auto rounded-lg shadow-xl scrollbar-hide hover:shadow-base_dark_200 md:inset-0;}
.o_notification{ @apply fixed bottom-0 right-0 z-50 flex items-center justify-center w-48 h-12 mb-5 mr-5 transition duration-300 ease-in-out delay-700 rounded text-base_100;}
.theme_primarys{ @apply text-base font-normal text-primarys_content bg-primarys;}
.theme_primarys_focus{ @apply bg-primarys_focus text-primarys_content;}
.theme_secondary{ @apply bg-secondary text-secondary_content;}
.theme_secondary_focus{ @apply bg-secondary_focus text-secondary_content;}
.theme_success{ @apply bg-success text-success_content;}
.theme_accent{ @apply bg-accent text-accent_content;}
.theme_accent_focus{ @apply bg-accent_focus text-accent_content;}
.theme_neutral{ @apply bg-neutral text-neutral_content;}
.theme_neutral_focus{ @apply bg-neutral_focus text-neutral_content;}
.theme_base_100{ @apply text-gray-600 bg-base_300 dark:text-gray-400 dark:bg-base_dark_100;}
.theme_base_200{ @apply text-gray-600 dark:text-gray-400 dark:bg-base_dark_200;}
.theme_100{ @apply text-base_content bg-base_100 dark:text-base_dark_content dark:bg-base_dark_100;}
.theme_200{ @apply text-base_200_content bg-base_200 dark:text-base_dark_200_content dark:bg-base_dark_200;}
.theme_300{ @apply text-base_300_content bg-base_300 dark:text-base_dark_300_content dark:bg-base_dark_300 dark:bg-opacity-40;}
.theme_300_bg{ @apply bg-base_300 dark:bg-base_dark_300 dark:bg-opacity-40;}
.table_base_100{ @apply text-gray-600 bg-gray-200 dark:text-gray-400 dark:bg-base_dark_200 hover:bg-gray-600 hover:text-base_dark_100;}
.table_base_200{ @apply text-gray-600 bg-base_300 dark:text-gray-400 dark:bg-base_dark_100 hover:bg-gray-600 hover:text-base_dark_100;}



