/* Custom Quill Editor Styles */

/* Common styles for both themes */
.quill-editor {
  font-family: 'Nunito', sans-serif;
}

.quill-editor .ql-container {
  font-family: 'Nunito', sans-serif;
  font-size: 1rem;
  height: auto;
  min-height: 120px;
}

/* Snow Theme (with toolbar) */
.quill-editor.ql-snow {
  border: none !important;
}

.quill-editor .ql-toolbar.ql-snow {
  border: 1px solid #e5e7eb;
  border-bottom: none;
  border-top-left-radius: 0.375rem;
  border-top-right-radius: 0.375rem;
  background-color: #f9fafb;
  padding: 0.5rem;
}

.quill-editor .ql-container.ql-snow {
  border: 1px solid #e5e7eb;
  border-bottom-left-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
  background-color: #ffffff;
}

/* Toolbar buttons */
.quill-editor .ql-toolbar.ql-snow .ql-formats {
  margin-right: 12px;
}

.quill-editor .ql-toolbar.ql-snow button {
  width: 28px;
  height: 28px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
}

.quill-editor .ql-toolbar.ql-snow button:hover {
  background-color: #e5e7eb;
}

.quill-editor .ql-toolbar.ql-snow button.ql-active,
.quill-editor .ql-toolbar.ql-snow .ql-picker-label.ql-active,
.quill-editor .ql-toolbar.ql-snow .ql-picker-item.ql-selected {
  color: #0072ff !important;
}

.quill-editor .ql-toolbar.ql-snow button:focus {
  outline: 2px solid rgba(0, 114, 255, 0.5);
  outline-offset: 2px;
}

.quill-editor .ql-toolbar.ql-snow .ql-stroke {
  stroke: #6b7280;
}

.quill-editor .ql-toolbar.ql-snow .ql-fill {
  fill: #6b7280;
}

.quill-editor .ql-toolbar.ql-snow button.ql-active .ql-stroke,
.quill-editor .ql-toolbar.ql-snow .ql-picker-label.ql-active .ql-stroke {
  stroke: #0072ff !important;
}

.quill-editor .ql-toolbar.ql-snow button.ql-active .ql-fill,
.quill-editor .ql-toolbar.ql-snow .ql-picker-label.ql-active .ql-fill {
  fill: #0072ff !important;
}

/* Dropdown menus */
.quill-editor .ql-toolbar.ql-snow .ql-picker {
  height: 28px;
}

.quill-editor .ql-toolbar.ql-snow .ql-picker-label {
  padding: 0 5px;
  border: 1px solid transparent;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  height: 28px;
}

.quill-editor .ql-toolbar.ql-snow .ql-picker-label:hover {
  background-color: #e5e7eb;
}

.quill-editor .ql-toolbar.ql-snow .ql-picker-options {
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
  padding: 5px;
}

.quill-editor .ql-toolbar.ql-snow .ql-picker-item {
  padding: 5px;
  border-radius: 0.25rem;
}

.quill-editor .ql-toolbar.ql-snow .ql-picker-item:hover {
  background-color: #f3f4f6;
  color: #0072ff;
}

/* Bubble Theme (minimal) */
.quill-editor.ql-bubble .ql-container {
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  background-color: #ffffff;
}

.quill-editor.ql-bubble .ql-editor {
  padding: 1rem;
}

.quill-editor.ql-bubble .ql-tooltip {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-radius: 0.375rem;
  padding: 0.5rem;
}

.quill-editor.ql-bubble .ql-tooltip-arrow {
  border-bottom: 6px solid #e5e7eb;
}

.quill-editor.ql-bubble .ql-tooltip input[type=text] {
  border: 1px solid #e5e7eb;
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
  margin-right: 0.5rem;
}

.quill-editor.ql-bubble .ql-tooltip a.ql-action,
.quill-editor.ql-bubble .ql-tooltip a.ql-remove {
  color: #0072ff;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.quill-editor.ql-bubble .ql-tooltip a.ql-action:hover,
.quill-editor.ql-bubble .ql-tooltip a.ql-remove:hover {
  background-color: #e5e7eb;
}

/* Focus states */
.quill-editor-wrapper.focused .ql-container.ql-snow,
.quill-editor-wrapper.focused .ql-container.ql-bubble {
  border-color: #0072ff !important;
  box-shadow: 0 0 0 3px rgba(0, 114, 255, 0.2);
}

.quill-editor-wrapper.focused .ql-toolbar.ql-snow {
  border-color: #0072ff !important;
}

/* Dark mode */
.dark .quill-editor .ql-toolbar.ql-snow {
  background-color: #1f2937;
  border-color: #374151;
}

.dark .quill-editor .ql-container.ql-snow,
.dark .quill-editor.ql-bubble .ql-container {
  background-color: #1f2937;
  border-color: #374151;
  color: #e5e7eb;
}

.dark .quill-editor .ql-editor {
  color: #e5e7eb;
}

.dark .quill-editor .ql-toolbar.ql-snow .ql-stroke {
  stroke: #9ca3af;
}

.dark .quill-editor .ql-toolbar.ql-snow .ql-fill {
  fill: #9ca3af;
}

.dark .quill-editor .ql-toolbar.ql-snow button:hover,
.dark .quill-editor .ql-toolbar.ql-snow .ql-picker-label:hover {
  background-color: #374151;
}

.dark .quill-editor .ql-toolbar.ql-snow .ql-picker-options {
  background-color: #1f2937;
  border-color: #374151;
}

.dark .quill-editor .ql-toolbar.ql-snow .ql-picker-item:hover {
  background-color: #374151;
  color: #82aae3;
}

.dark .quill-editor .ql-toolbar.ql-snow button.ql-active,
.dark .quill-editor .ql-toolbar.ql-snow .ql-picker-label.ql-active,
.dark .quill-editor .ql-toolbar.ql-snow .ql-picker-item.ql-selected {
  color: #82aae3 !important;
}

.dark .quill-editor .ql-toolbar.ql-snow button.ql-active .ql-stroke,
.dark .quill-editor .ql-toolbar.ql-snow .ql-picker-label.ql-active .ql-stroke {
  stroke: #82aae3 !important;
}

.dark .quill-editor .ql-toolbar.ql-snow button.ql-active .ql-fill,
.dark .quill-editor .ql-toolbar.ql-snow .ql-picker-label.ql-active .ql-fill {
  fill: #82aae3 !important;
}

/* Bubble theme dark mode */
.dark .quill-editor.ql-bubble .ql-tooltip {
  background-color: #1f2937;
  border-color: #374151;
}

.dark .quill-editor.ql-bubble .ql-tooltip-arrow {
  border-bottom: 6px solid #374151;
}

.dark .quill-editor.ql-bubble .ql-tooltip input[type=text] {
  background-color: #374151;
  border-color: #4b5563;
  color: #e5e7eb;
}

.dark .quill-editor.ql-bubble .ql-tooltip a.ql-action,
.dark .quill-editor.ql-bubble .ql-tooltip a.ql-remove {
  color: #82aae3;
}

.dark .quill-editor.ql-bubble .ql-tooltip a.ql-action:hover,
.dark .quill-editor.ql-bubble .ql-tooltip a.ql-remove:hover {
  background-color: #374151;
}

/* Focus states in dark mode */
.dark .quill-editor-wrapper.focused .ql-container.ql-snow,
.dark .quill-editor-wrapper.focused .ql-container.ql-bubble {
  border-color: #82aae3 !important;
  box-shadow: 0 0 0 3px rgba(130, 170, 227, 0.2);
}

.dark .quill-editor-wrapper.focused .ql-toolbar.ql-snow {
  border-color: #82aae3 !important;
}
