{"form": {"Flyers": {"title": "Flyers", "filters": {"index": "omni", "collection": "flyers", "queryBy": "", "route_add": "/c/flyers-create", "description": "Flyers", "areas": ["Flyers Information"], "filterBy": "Touched Records, Untouched Records, Record Action, Related Records Action"}, "data": [{"label": "id", "val": "id", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Created At", "val": "created_at", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Updated at", "val": "updated_at", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "User ownership", "val": "user_own", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Users with access", "val": "user_access", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Space ownership", "val": "space_own", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Space with access", "val": "space_access", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Home Page", "val": "home", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "checkbox", "form": true, "table": true, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": false, "optionsUrl": "plain", "options": [], "component": "FormsInputsType", "action": "forms-actions-type"}, {"label": "Name", "val": "name", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": true, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": false, "optionsUrl": "plain", "options": [], "component": "FormsInputsType", "action": "forms-actions-type"}, {"label": "Tags (comma seperated)", "val": "tags", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": true, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": false, "optionsUrl": "plain", "options": [], "component": "FormsInputsTags", "action": "forms-actions-tags"}, {"label": "Description", "val": "description", "value": "", "class": "col-span-2", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": false, "optionsUrl": "plain", "options": [], "component": "FormsInputsQuill", "action": "forms-actions-quill"}, {"label": "Flyer", "val": "image", "value": "", "class": "col-span-2", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": true, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "Image", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": false, "optionsUrl": "plain", "component": "FormsInputsUpload", "action": "forms-actions-docs"}, {"label": "Status", "val": "status", "value": "Pending", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": true, "small_table": false, "small_form": false, "editable": false, "card": false, "card_type": "Image", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": false, "optionsUrl": "plain", "component": "FormsInputsSelect", "action": "forms-actions-select", "options": ["Approved", "Pending", "Declined"]}]}, "Specials": {"title": "Specials", "filters": {"index": "omni", "collection": "specials", "queryBy": "", "route_add": "/c/specials-create", "description": "Specials", "areas": ["Specials Information"], "filterBy": "Touched Records, Untouched Records, Record Action, Related Records Action"}, "data": [{"label": "id", "val": "id", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Created At", "val": "created_at", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Updated at", "val": "updated_at", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "User ownership", "val": "user_own", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Users with access", "val": "user_access", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Space ownership", "val": "space_own", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Space with access", "val": "space_access", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Name", "val": "name", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": true, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": false, "optionsUrl": "plain", "options": [], "component": "FormsInputsType", "action": "forms-actions-type"}, {"label": "Tags (comma seperated)", "val": "tags", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": true, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": false, "optionsUrl": "plain", "options": [], "component": "FormsInputsTags", "action": "forms-actions-tags"}, {"label": "Description", "val": "description", "value": "", "class": "col-span-2", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": false, "optionsUrl": "plain", "options": [], "component": "FormsInputsQuill", "action": "forms-actions-quill"}, {"label": "Special", "val": "image", "value": "", "class": "col-span-2", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": true, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "Image", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": false, "optionsUrl": "plain", "component": "FormsInputsUpload", "action": "forms-actions-docs"}, {"label": "Status", "val": "status", "value": "Pending", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": true, "small_table": false, "small_form": false, "editable": false, "card": false, "card_type": "Image", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": false, "optionsUrl": "plain", "component": "FormsInputsSelect", "action": "forms-actions-select", "options": ["Approved", "Pending", "Declined"]}]}, "Items": {"title": "Items", "filters": {"index": "omni", "collection": "items", "queryBy": "", "route_add": "/c/items-create", "description": "Items", "areas": ["Items Information"], "filterBy": "Touched Records, Untouched Records, Record Action, Related Records Action"}, "data": [{"label": "id", "val": "id", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Created At", "val": "created_at", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Updated at", "val": "updated_at", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "User ownership", "val": "user_own", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Users with access", "val": "user_access", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Space ownership", "val": "space_own", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Space with access", "val": "space_access", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Title", "val": "title", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": true, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": false, "optionsUrl": "plain", "options": [], "component": "FormsInputsType", "action": "forms-actions-type"}, {"label": "Tags (comma seperated)", "val": "tags", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": true, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": false, "optionsUrl": "plain", "options": [], "component": "FormsInputsTags", "action": "forms-actions-tags"}, {"label": "Asking Price", "val": "price", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "number", "form": true, "table": true, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": false, "optionsUrl": "plain", "options": [], "component": "FormsInputsType", "action": "forms-actions-type"}, {"label": "Listed By", "val": "selling_by", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": true, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": false, "optionsUrl": "plain", "options": ["Owner", "Friend", "Family Member", "Business", "Consignment", "Other"], "component": "FormsInputsSelect", "action": "forms-actions-select"}, {"label": "Pickup/Delivery/Shipping Options", "val": "pickup_delivery", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": true, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": false, "optionsUrl": "plain", "options": ["Local Pickup Only", "Shipping Available", "Free Shipping", "Delivery Available"], "component": "FormsInputsSelect", "action": "forms-actions-select"}, {"label": "Availability", "val": "availability", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": true, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": false, "optionsUrl": "plain", "options": ["Immediately Available", "Pre-Order", "Limited Availability", "Backorder", "Seasonal"], "component": "FormsInputsSelect", "action": "forms-actions-select"}, {"label": "Description", "val": "description", "value": "", "class": "col-span-2", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": false, "optionsUrl": "plain", "options": [], "component": "FormsInputsQuill", "action": "forms-actions-quill"}, {"label": "Condition", "val": "condition", "value": "", "class": "col-span-2", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": false, "optionsUrl": "plain", "options": [], "component": "FormsInputsQuill", "action": "forms-actions-quill"}, {"label": "Location", "val": "location", "value": "", "class": "col-span-2", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": false, "optionsUrl": "plain", "options": [], "component": "GoogleAutocomplete", "action": "forms-actions-quill"}, {"label": "Special", "val": "image", "value": "", "class": "col-span-2", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": true, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "Image", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": true, "optionsUrl": "plain", "component": "FormsInputsUpload", "action": "forms-actions-docs"}, {"label": "Status", "val": "status", "value": "Pending", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": true, "small_table": false, "small_form": false, "editable": false, "card": false, "card_type": "Image", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": false, "optionsUrl": "plain", "component": "FormsInputsSelect", "action": "forms-actions-select", "options": ["Approved", "Pending", "Declined"]}]}, "Businesscards": {"title": "Business Cards", "filters": {"index": "omni", "collection": "businesscards", "queryBy": "", "route_add": "/c/businesscards-create", "description": "Businesscards", "areas": ["Flyers Information"], "filterBy": "Touched Records, Untouched Records, Record Action, Related Records Action"}, "data": [{"label": "id", "val": "id", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Created At", "val": "created_at", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Updated at", "val": "updated_at", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "User ownership", "val": "user_own", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Users with access", "val": "user_access", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Space ownership", "val": "space_own", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Space with access", "val": "space_access", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Name", "val": "name", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": true, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": false, "optionsUrl": "plain", "options": [], "component": "FormsInputsType", "action": "forms-actions-type"}, {"label": "Tags (comma seperated)", "val": "tags", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": true, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": false, "optionsUrl": "plain", "options": [], "component": "FormsInputsTags", "action": "forms-actions-tags"}, {"label": "Description", "val": "description", "value": "", "class": "col-span-2", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": false, "optionsUrl": "plain", "options": [], "component": "FormsInputsQuill", "action": "forms-actions-quill"}, {"label": "Business Card", "val": "image", "value": "", "class": "col-span-2", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": true, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "Image", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": false, "optionsUrl": "plain", "component": "FormsInputsUpload", "action": "forms-actions-docs"}, {"label": "Status", "val": "status", "value": "Pending", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": true, "small_table": false, "small_form": false, "editable": false, "card": false, "card_type": "Image", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": false, "optionsUrl": "plain", "component": "FormsInputsSelect", "action": "forms-actions-select", "options": ["Approved", "Pending", "Declined"]}]}, "Requests": {"title": "Requests", "filters": {"index": "omni", "collection": "requests", "queryBy": "", "description": "Requests", "route_add": "/o/crm-requests-create", "areas": ["Request Information", "Request Description"], "filterBy": "Touched Records, Untouched Records, Record Action, Related Records Action"}, "data": [{"label": "id", "val": "id", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Created At", "val": "created_at", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Updated at", "val": "updated_at", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "User ownership", "val": "user_own", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Users with access", "val": "user_access", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Space ownership", "val": "space_own", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Space with access", "val": "space_access", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Subject", "val": "subject", "value": "", "class": "col-span-2", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": true, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "Heading", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": false, "optionsUrl": "plain", "options": [], "component": "FormsInputsType", "action": "forms-actions-type"}, {"label": "Description", "val": "description", "value": "", "class": "col-span-2", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": true, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 2, "quick": false, "multi": false, "optionsUrl": "plain", "options": [], "component": "FormsInputsQuill", "action": "forms-actions-quill"}, {"label": "Links to Documentations", "val": "links", "value": [""], "class": "col-span-2", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": true, "small_table": false, "small_form": false, "editable": true, "card": false, "card_type": "", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 2, "quick": false, "multi": false, "optionsUrl": "plain", "options": [], "component": "FormsInputsArray", "action": "forms-actions-array"}, {"label": "Status", "val": "status", "value": "Pending", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": true, "small_table": false, "small_form": false, "editable": false, "card": false, "card_type": "Image", "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "multi": false, "optionsUrl": "plain", "component": "FormsInputsSelect", "action": "forms-actions-select", "options": ["Approved", "Pending", "Declined"]}]}}}