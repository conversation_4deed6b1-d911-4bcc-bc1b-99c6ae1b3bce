{"title": "Account", "filters": {"index": "omni", "collection": "spaces", "queryBy": "", "description": "Account", "areas": ["Account Information"], "filterBy": "Touched Records, Untouched Records, Record Action, Related Records Action"}, "data": [{"label": "id", "val": "id", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "small_table": false, "small_form": false, "editable": true, "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Created At", "val": "created_at", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "editable": true, "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Updated at", "val": "updated_at", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "editable": true, "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "User ownership", "val": "user_own", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "editable": true, "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Users with access", "val": "user_access", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "editable": true, "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Space ownership", "val": "space_own", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "editable": true, "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Space with access", "val": "space_access", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "editable": true, "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Name", "val": "name", "placeholder": "Name", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": true, "editable": true, "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": true, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Name", "val": "name", "placeholder": "Name", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": true, "editable": true, "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": true, "component": "FormsInputsOpen", "action": "FormsActionsType"}, {"label": "Email", "val": "email", "placeholder": "Email", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "email", "form": true, "table": true, "editable": true, "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": true, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Phone", "val": "phone", "placeholder": "Phone", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "phone", "form": true, "table": true, "editable": true, "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsPhone", "action": "FormsActionsPhone"}, {"label": "Mobile", "val": "mobile", "placeholder": "Mobile", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "phone", "form": false, "table": true, "editable": true, "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsPhone", "action": "FormsActionsPhone", "precondition": [{"condition": ["space_type", "==", "Family"]}]}, {"label": "Mobile", "val": "mobile", "placeholder": "Mobile", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "phone", "form": false, "table": true, "editable": true, "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsPhone", "action": "FormsActionsPhone", "precondition": [{"condition": ["space_type", "==", "Individual"]}]}, {"label": "Type", "val": "type", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": false, "editable": true, "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsSelect", "action": "FormsActionsSelect", "optionsUrl": "workspace_types"}, {"label": "Industry", "val": "industry", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": false, "editable": true, "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsSelect", "action": "FormsActionsSelect", "optionsUrl": "industry"}, {"label": "Logo", "val": "logo", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": false, "editable": true, "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsUpload", "action": "FormsActionsUpload", "optionsUrl": "industry"}, {"label": "Description", "val": "description", "placeholder": "Description", "value": "", "class": "col-span-2", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": true, "table": false, "editable": true, "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsQuill", "action": "FormsActionsQuill"}, {"label": "Address", "val": "address", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": true, "editable": true, "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "City", "val": "city", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": true, "editable": true, "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "State", "val": "state", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": true, "editable": true, "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Zip", "val": "zip", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": true, "editable": true, "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "Country", "val": "country", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": true, "editable": true, "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "website", "val": "website", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": true, "editable": true, "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "facebook", "val": "facebook", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": true, "editable": true, "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "twitter", "val": "twitter", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": true, "editable": true, "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "instagram", "val": "instagram", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": true, "editable": true, "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "linkedin", "val": "linkedin", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": true, "editable": true, "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}, {"label": "github", "val": "github", "value": "", "class": "", "class_input": "o_input", "class_label": "o_label_small", "type": "text", "form": false, "table": true, "editable": true, "required": true, "readonly": false, "unique": false, "hidden": false, "disabled": false, "area": 1, "quick": false, "component": "FormsInputsType", "action": "FormsActionsType"}]}