# Warp Terminal MCP Server Setup Guide

This guide will help you configure Warp Terminal to use the PIB-METHOD MCP server for enhanced AI capabilities.

## Prerequisites

1. **Warp Terminal**: Latest version with MCP support
2. **Node.js**: Version 18 or higher
3. **PIB-METHOD**: Synced to your project via `sync-pib-complete.sh`

## Step 1: Build the MCP Server

```bash
# Navigate to your project's Warp MCP server directory
cd .warp/mcp-server

# Install dependencies
npm install

# Build the server
npm run build

# Verify build
ls -la dist/server.js
```

## Step 2: Configure Warp MCP Settings

### Option A: Via Warp Settings UI

1. Open Warp Terminal
2. Press `Cmd+,` (Mac) or `Ctrl+,` (Windows/Linux) to open settings
3. Navigate to "AI" → "MCP Servers"
4. Click "Add MCP Server"
5. Configure:
   - **Name**: `pib-method`
   - **Command**: `node`
   - **Args**: `["/absolute/path/to/your/project/.warp/mcp-server/dist/server.js"]`
   - **Description**: `PIB-METHOD development workflows and LEVER framework`

### Option B: Via Configuration File

Add to your Warp configuration file (`~/.warp/config.json`):

```json
{
  "mcp_servers": {
    "pib-method": {
      "command": "node",
      "args": ["/absolute/path/to/your/project/.warp/mcp-server/dist/server.js"],
      "description": "PIB-METHOD development workflows and LEVER framework"
    }
  }
}
```

## Step 3: Import Workflows

1. Open Warp Terminal
2. Press `Ctrl+Shift+R` (or `Cmd+Shift+R` on Mac)
3. Click "Import Workflows"
4. Navigate to `.warp/workflows/` directory
5. Select all `.yml` files or import by category:
   - `development/` - Core development workflows
   - `planning/` - Q-LEVER planning sessions
   - `quality/` - Code review and compliance
   - `architecture/` - System design workflows

## Step 4: Import Warp Drive Rules

1. Open Warp Drive (in Warp Terminal)
2. Navigate to "Rules" section
3. Click "Import Rules"
4. Select rule files from `.warp/drive/rules/`:
   - `lever/` - LEVER framework compliance rules
   - `quality/` - Code quality enforcement
   - `security/` - Security best practices
   - `workflow/` - Development process rules

## Step 5: Test Integration

### Test MCP Server

In Warp Agent Mode (`Cmd+I` / `Ctrl+I`):

```
Use PIB-METHOD to implement user authentication
```

### Test Workflows

Using workflows (`Ctrl+Shift+R`):

1. Search for "PIB Dev Command"
2. Fill in parameters:
   - **Task**: "create login component"
   - **Priority**: "medium"
   - **Focus**: "security"
3. Execute workflow

### Test Rules

Rules will automatically trigger based on your activities:

- Starting new implementation → Question phase rule
- Creating similar code → Leverage existing rule
- Completing work → LEVER compliance check

## Step 6: Verify Setup

Run the following checks:

```bash
# Check MCP server is built
ls -la .warp/mcp-server/dist/server.js

# Test MCP server directly
node .warp/mcp-server/dist/server.js --help

# Check workflows are available
ls -la .warp/workflows/development/
```

In Warp Agent Mode, test MCP tools:

```
List available PIB-METHOD tools
```

Expected tools:
- `pib_dev_command`
- `pib_planning_session`
- `pib_code_review`
- `pib_feature_start`
- `pib_architect_design`
- `pib_lever_check`

## Troubleshooting

### MCP Server Won't Start

1. Check Node.js version: `node --version` (should be 18+)
2. Rebuild server: `cd .warp/mcp-server && npm run build`
3. Check permissions: `chmod +x dist/server.js`
4. Verify absolute path in Warp configuration

### Workflows Not Appearing

1. Ensure YAML files are valid: `yamllint .warp/workflows/*.yml`
2. Check file permissions: `chmod 644 .warp/workflows/*.yml`
3. Restart Warp Terminal
4. Re-import workflows

### Rules Not Triggering

1. Verify rule files are properly formatted
2. Check rule trigger patterns match your activity
3. Ensure Warp Drive rules are enabled
4. Review rule logs in Warp settings

### MCP Tools Not Available in Agent Mode

1. Confirm MCP server is configured and running
2. Check Warp Agent Mode settings
3. Verify MCP server logs for errors
4. Restart Warp Terminal to reload configuration

## Advanced Configuration

### Custom Project Paths

For projects in different locations, update the MCP server configuration:

```json
{
  "mcp_servers": {
    "pib-method-project1": {
      "command": "node",
      "args": ["/path/to/project1/.warp/mcp-server/dist/server.js"]
    },
    "pib-method-project2": {
      "command": "node", 
      "args": ["/path/to/project2/.warp/mcp-server/dist/server.js"]
    }
  }
}
```

### Team Configuration

For team-wide deployment:

1. Share Warp configuration template
2. Use relative paths where possible
3. Document team-specific rule customizations
4. Set up shared Warp Drive workspace

## Integration with Other AI Systems

PIB-METHOD now works across three AI systems:

- **Claude Code**: Use slash commands and hooks as usual
- **GitHub Copilot**: Instructions and prompts work in VS Code
- **Warp Terminal**: MCP server provides same functionality in terminal

All three systems maintain consistent LEVER framework compliance and quality standards.

## Support

If you encounter issues:

1. Check the PIB-METHOD documentation
2. Review MCP server logs
3. Verify your Warp Terminal version supports MCP
4. Test with a simple workflow first

The PIB-METHOD triple AI system integration provides consistent development workflows regardless of which AI assistant you're using.