{"name": "pib-method-mcp-server", "version": "1.0.0", "description": "PIB-METHOD Model Context Protocol Server for Warp Terminal Integration", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "ts-node src/server.ts", "watch": "tsc --watch"}, "dependencies": {"@modelcontextprotocol/sdk": "^0.6.0", "fs-extra": "^11.2.0", "path": "^0.12.7", "yaml": "^2.4.1"}, "devDependencies": {"@types/node": "^20.11.24", "@types/fs-extra": "^11.0.4", "typescript": "^5.3.3", "ts-node": "^10.9.2"}, "keywords": ["pib-method", "mcp", "warp", "ai", "development", "workflow"], "author": "PIB-METHOD", "license": "MIT"}