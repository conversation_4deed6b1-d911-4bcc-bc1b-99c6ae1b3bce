# PIB-METHOD MCP Server

Model Context Protocol (MCP) server that provides PIB-METHOD workflows and tools to Warp Terminal's Agent Mode.

## Overview

This MCP server exposes PIB-METHOD's powerful development workflows as tools that can be used by Warp Terminal's AI assistant. It enables the same functionality available in <PERSON>'s slash commands to be accessible directly in the terminal through natural language.

## Features

- **6 Core Tools**: All essential PIB-METHOD workflows as MCP tools
- **Smart Routing**: Automatic complexity detection and agent selection
- **LEVER Compliance**: Built-in quality framework validation
- **Project Context**: Automatic technology detection and context awareness
- **State Management**: Integration with PIB-METHOD state systems

## Available Tools

### Development Tools

#### `pib_dev_command`
Intelligent development workflow with automatic complexity detection.

**Parameters:**
- `task_description` (required): Development task to implement
- `priority`: Task priority (low/medium/high/critical)
- `focus`: Development focus (general/security/performance/ui/api)
- `agent_override`: Force specific agent (optional)
- `use_plan`: Use existing planning session ID (optional)

**Example:**
```
Use PIB dev command to implement user authentication with OAuth support
```

#### `pib_code_review`
Automated code review with LEVER framework compliance checking.

**Parameters:**
- `files` (required): Array of file paths to review
- `focus`: Review focus (general/security/performance/maintainability/lever)
- `severity`: Minimum issue severity (low/medium/high)

**Example:**
```
Review these files for LEVER compliance: src/auth.ts, src/api/users.ts
```

#### `pib_feature_start`
Create isolated git worktree for parallel feature development.

**Parameters:**
- `feature_name` (required): Name of the feature to develop
- `description`: Brief description of the feature
- `base_branch`: Base branch (default: main)
- `parent_directory`: Parent directory for worktree
- `planning_session`: Use existing planning session ID

**Example:**
```
Create feature branch for payment processing system
```

### Planning Tools

#### `pib_planning_session`
Interactive Q-LEVER planning session with clarifying questions.

**Parameters:**
- `task_description` (required): Task to plan
- `scope`: Task complexity hint (small/medium/large)
- `ai_preference`: Preferred AI assistant (claude/copilot/auto)

**Example:**
```
Start planning session for implementing dashboard analytics
```

### Architecture Tools

#### `pib_architect_design`
Technical architecture and system design with LEVER framework compliance.

**Parameters:**
- `project_description` (required): Project or feature to architect  
- `design_type`: Type of design (system/module/api/database/frontend/infrastructure)
- `complexity`: Complexity level (simple/moderate/complex/enterprise)
- `existing_systems`: Existing systems to integrate with
- `constraints`: Technical constraints or requirements

**Example:**
```
Design system architecture for e-commerce platform with real-time inventory
```

### Quality Tools

#### `pib_lever_check`
Validate LEVER framework compliance for code, plans, or implementations.

**Parameters:**
- `target` (required): What to check (file path, session ID, or description)
- `check_type`: Type of check (code/plan/implementation/architecture)
- `minimum_score`: Minimum acceptable LEVER score (default: 4.0)
- `detailed_feedback`: Provide detailed feedback (default: true)

**Example:**
```
Check LEVER compliance for the authentication implementation
```

## Installation

### Prerequisites
- Node.js 18 or higher
- TypeScript
- PIB-METHOD project structure

### Setup
```bash
# Navigate to MCP server directory
cd .warp/mcp-server

# Install dependencies
npm install

# Build the server
npm run build

# Verify installation
node dist/server.js --help
```

## Configuration

### Warp Terminal Setup

Add to your Warp configuration (`~/.warp/config.json`):

```json
{
  "mcp_servers": {
    "pib-method": {
      "command": "node",
      "args": ["/absolute/path/to/your/project/.warp/mcp-server/dist/server.js"],
      "description": "PIB-METHOD development workflows and LEVER framework"
    }
  }
}
```

### Environment Variables

The MCP server respects these environment variables:

- `PIB_PROJECT_ROOT`: Override project root directory
- `PIB_LEVER_MINIMUM`: Override minimum LEVER score (default: 4.0)
- `PIB_DEBUG`: Enable debug logging

## Usage

### In Warp Agent Mode

Press `Cmd+I` (Mac) or `Ctrl+I` (Windows/Linux) to enter Agent Mode, then use natural language:

#### Development
```
"Use PIB dev command to implement user registration with email validation"
"Create a new feature branch for the shopping cart functionality"
"Review the authentication code for security issues"
```

#### Planning
```
"Start a PIB planning session for the notification system"
"Plan the implementation of real-time chat features"
```

#### Architecture
```
"Design the database architecture for user management"
"Create system architecture for the payment processing module"
```

#### Quality
```
"Check LEVER compliance for the recent authentication changes"
"Validate the code quality of src/components/Login.tsx"
```

### Direct Tool Invocation

You can also mention specific PIB-METHOD tools by name:

```
"Use the PIB planning session tool to plan the API redesign"
"Run PIB code review on the files I just modified"
"Check PIB LEVER compliance for this implementation"
```

## Development

### Project Structure
```
src/
├── server.ts           # Main MCP server
├── tools/              # Tool implementations
│   ├── dev-command.ts
│   ├── planning-session.ts
│   ├── code-review.ts
│   ├── feature-start.ts
│   ├── architect-design.ts
│   └── lever-check.ts
└── utils/              # Utility functions
```

### Adding New Tools

1. Create new tool file in `src/tools/`
2. Implement the tool interface:
   ```typescript
   export class MyNewTool {
     name = 'my_new_tool';
     description = 'Description of what this tool does';
     inputSchema = { /* JSON schema */ };
     
     async execute(args: any) {
       // Tool implementation
     }
   }
   ```
3. Register tool in `src/server.ts`
4. Rebuild: `npm run build`

### Debugging

Enable debug logging:
```bash
PIB_DEBUG=1 node dist/server.js
```

View MCP communication:
```bash
# Run server directly to see input/output
node dist/server.js
```

## Integration with PIB-METHOD

### State Management
The MCP server integrates with PIB-METHOD's state systems:
- Planning session storage
- Worktree index management
- LEVER compliance tracking
- Context engineering evolution

### Quality Gates
All tools enforce PIB-METHOD quality standards:
- Minimum LEVER score of 4.0
- Comprehensive testing requirements
- Code review compliance
- Security best practices

### Sync Integration
The MCP server is automatically deployed via `sync-pib-complete.sh`:
- Server code synced to all projects
- Dependencies installed automatically
- Build process integrated into sync

## Troubleshooting

### Server Won't Start
1. Check Node.js version: `node --version` (should be 18+)
2. Verify build: `ls -la dist/server.js`
3. Check dependencies: `npm install`
4. Rebuild: `npm run build`

### Tools Not Available in Warp
1. Verify MCP server configuration in Warp
2. Check server logs for errors
3. Restart Warp Terminal
4. Test server directly: `node dist/server.js`

### Incorrect Tool Responses
1. Check tool input parameters
2. Verify project context (in correct directory)
3. Enable debug logging: `PIB_DEBUG=1`
4. Check PIB-METHOD state files

### Performance Issues
1. Tools are designed to be lightweight
2. Caching is implemented where appropriate
3. Complex operations are delegated to appropriate agents
4. Consider splitting large requests into smaller ones

## Architecture

### MCP Protocol
The server implements the Model Context Protocol specification:
- **Transport**: Standard I/O communication
- **Tools**: Expose PIB-METHOD workflows as callable tools
- **Error Handling**: Proper MCP error responses
- **Logging**: Structured logging for debugging

### Integration Points
- **File System**: Direct access to project files and PIB state
- **Git**: Worktree management and branch operations
- **Process Execution**: Shell command execution for builds/tests
- **JSON Storage**: PIB-METHOD state and configuration files

### Security
- **Sandboxing**: Operations limited to project directory
- **Input Validation**: All tool inputs validated
- **Error Boundaries**: Proper error handling and recovery
- **Logging**: Security-relevant events logged

## Contributing

1. Follow PIB-METHOD coding standards
2. Maintain LEVER framework compliance (4.0+ score)
3. Add comprehensive tests for new tools
4. Update documentation for changes
5. Test with actual Warp Terminal integration

## License

Part of the PIB-METHOD system. See main project license.