#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  McpError,
  ErrorCode,
} from '@modelcontextprotocol/sdk/types.js';

import { PIBDevCommand } from './tools/dev-command.js';
import { PIBPlanningSession } from './tools/planning-session.js';
import { PIBCodeReview } from './tools/code-review.js';
import { PIBFeatureStart } from './tools/feature-start.js';
import { PIBArchitectDesign } from './tools/architect-design.js';
import { PIBLeverCheck } from './tools/lever-check.js';

/**
 * PIB-METHOD MCP Server
 * 
 * Provides PIB-METHOD workflows and tools to Warp Terminal's Agent Mode
 * via the Model Context Protocol (MCP).
 */
class PIBMethodMCPServer {
  private server: Server;
  private tools: Map<string, any>;

  constructor() {
    this.server = new Server(
      {
        name: 'pib-method-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.tools = new Map();
    this.setupTools();
    this.setupHandlers();
  }

  private setupTools() {
    // Core PIB-METHOD tools
    const tools = [
      new PIBDevCommand(),
      new PIBPlanningSession(),
      new PIBCodeReview(),
      new PIBFeatureStart(),
      new PIBArchitectDesign(),
      new PIBLeverCheck(),
    ];

    tools.forEach(tool => {
      this.tools.set(tool.name, tool);
    });
  }

  private setupHandlers() {
    // List available tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      const tools = Array.from(this.tools.values()).map(tool => ({
        name: tool.name,
        description: tool.description,
        inputSchema: tool.inputSchema,
      }));

      return { tools };
    });

    // Execute tool calls
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;
      
      const tool = this.tools.get(name);
      if (!tool) {
        throw new McpError(
          ErrorCode.MethodNotFound,
          `Tool '${name}' not found`
        );
      }

      try {
        const result = await tool.execute(args);
        return {
          content: [
            {
              type: 'text',
              text: typeof result === 'string' ? result : JSON.stringify(result, null, 2),
            },
          ],
        };
      } catch (error) {
        throw new McpError(
          ErrorCode.InternalError,
          `Tool execution failed: ${error instanceof Error ? error.message : String(error)}`
        );
      }
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    
    console.error('PIB-METHOD MCP Server running on stdio');
  }
}

// Start the server
if (require.main === module) {
  const server = new PIBMethodMCPServer();
  server.run().catch(console.error);
}

export { PIBMethodMCPServer };