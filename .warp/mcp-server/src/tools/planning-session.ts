import * as fs from 'fs-extra';
import * as path from 'path';

export class PIBPlanningSession {
  name = 'pib_planning_session';
  description = 'Start interactive Q-LEVER planning session with clarifying questions before implementation';
  
  inputSchema = {
    type: 'object',
    properties: {
      task_description: {
        type: 'string',
        description: 'Task to plan (e.g., "implement user authentication system")',
      },
      scope: {
        type: 'string',
        enum: ['small', 'medium', 'large'],
        description: 'Hint about task complexity',
        default: 'medium',
      },
      ai_preference: {
        type: 'string',
        enum: ['claude', 'copilot', 'auto'],
        description: 'Preferred AI assistant for planning',
        default: 'auto',
      },
    },
    required: ['task_description'],
  };

  async execute(args: any) {
    const { task_description, scope = 'medium', ai_preference = 'auto' } = args;
    
    try {
      // Generate unique session ID
      const sessionId = this.generateSessionId();
      
      // Create planning session
      const session = await this.createPlanningSession({
        id: sessionId,
        task: task_description,
        scope,
        ai_preference,
        created_at: new Date().toISOString(),
      });

      // Generate Q-LEVER questions
      const questions = this.generateQLeverQuestions(task_description, scope);
      
      // Save session state
      await this.savePlanningSession(session);
      
      return {
        success: true,
        session_id: sessionId,
        task: task_description,
        scope,
        status: 'question_phase',
        questions,
        next_steps: [
          'Answer the clarifying questions below',
          'Use pib_planning_answer to provide responses',
          'System will generate implementation plan',
          'Review and approve plan before implementation',
        ],
      };
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  private generateSessionId(): string {
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    const random = Math.random().toString(36).substring(2, 8);
    return `plan-${timestamp}-${random}`;
  }

  private async createPlanningSession(params: any) {
    return {
      ...params,
      phase: 'questioning',
      questions_answered: [],
      plan: null,
      approved: false,
    };
  }

  private generateQLeverQuestions(task: string, scope: string) {
    const baseQuestions = [
      {
        id: 'requirements',
        question: 'What specific functionality is needed for this task?',
        category: 'Question',
        importance: 'high',
      },
      {
        id: 'constraints',
        question: 'Are there any technical limitations, performance requirements, or constraints I should know about?',
        category: 'Question',  
        importance: 'high',
      },
      {
        id: 'integration',
        question: 'How should this integrate with existing systems or components?',
        category: 'Question',
        importance: 'medium',
      },
      {
        id: 'users',
        question: 'Who will use this feature and what are their primary use cases?',
        category: 'Question',
        importance: 'medium',
      },
      {
        id: 'success_criteria',
        question: 'How will we know when this implementation is complete and successful?',
        category: 'Question',
        importance: 'high',
      },
    ];

    // Add scope-specific questions
    if (scope === 'small') {
      baseQuestions.push({
        id: 'timeline',
        question: 'What is the expected timeline for this small task?',
        category: 'Question',
        importance: 'low',
      });
    } else if (scope === 'large') {
      baseQuestions.push({
        id: 'phases',
        question: 'Should this large task be broken into phases or deliverable milestones?',
        category: 'Question',
        importance: 'high',
      });
      baseQuestions.push({
        id: 'architecture',
        question: 'Are there architectural decisions or patterns that need to be established?',
        category: 'Question',
        importance: 'high',
      });
    }

    // Add task-specific questions based on keywords
    const taskLower = task.toLowerCase();
    
    if (taskLower.includes('auth') || taskLower.includes('login')) {
      baseQuestions.push({
        id: 'auth_methods',
        question: 'What authentication methods should be supported (email/password, OAuth, SSO, 2FA)?',
        category: 'Question',
        importance: 'high',
      });
    }
    
    if (taskLower.includes('api') || taskLower.includes('endpoint')) {
      baseQuestions.push({
        id: 'api_design',
        question: 'What are the expected API endpoints, request/response formats, and error handling requirements?',
        category: 'Question',
        importance: 'high',
      });
    }
    
    if (taskLower.includes('ui') || taskLower.includes('component')) {
      baseQuestions.push({
        id: 'ui_requirements',
        question: 'What are the UI/UX requirements, responsive design needs, and accessibility considerations?',
        category: 'Question',
        importance: 'medium',
      });
    }

    return baseQuestions;
  }

  private async savePlanningSession(session: any) {
    const sessionsDir = path.join(process.cwd(), '.claude', 'state', 'planning');
    await fs.ensureDir(sessionsDir);
    
    const sessionFile = path.join(sessionsDir, `${session.id}.json`);
    await fs.writeJson(sessionFile, session, { spaces: 2 });
    
    // Update planning index
    const indexFile = path.join(sessionsDir, 'planning-index.json');
    let index = { sessions: [] };
    
    if (await fs.pathExists(indexFile)) {
      index = await fs.readJson(indexFile);
    }
    
    index.sessions.push({
      id: session.id,
      task: session.task,
      created_at: session.created_at,
      status: 'active',
    });
    
    await fs.writeJson(indexFile, index, { spaces: 2 });
  }
}