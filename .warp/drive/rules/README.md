# PIB-METHOD Warp Drive Rules

This directory contains Warp Drive rules that implement the LEVER framework and PIB-METHOD quality standards within Warp Terminal's Agent Mode.

## LEVER Framework Rules

The LEVER framework ensures quality and consistency across all development work:

- **Q** - Question assumptions and clarify requirements FIRST
- **L** - Leverage existing patterns and code
- **E** - Extend before creating new
- **V** - Verify through comprehensive testing
- **E** - Eliminate duplication
- **R** - Reduce complexity

## Rule Categories

### `/rules/lever/`
Core LEVER framework rules that trigger quality checks and reminders.

### `/rules/quality/`
Code quality and standards enforcement rules.

### `/rules/security/`
Security best practices and vulnerability prevention rules.

### `/rules/workflow/`
Development workflow and process rules.

## How Rules Work

1. **Trigger Patterns**: Rules activate based on keywords, file types, or commands
2. **Actions**: Rules can suggest workflows, require approvals, or provide guidance
3. **Context Awareness**: Rules adapt based on project type and current activity
4. **Integration**: Rules work seamlessly with MCP server and workflows

## Import Rules

To use these rules in Warp:

1. Open Warp Drive settings
2. Import rule files from this directory
3. Enable PIB-METHOD rule set
4. Rules will automatically activate based on your development activities

## Rule Examples

### Question Phase Rule
Triggers when starting new implementations to ensure requirements are clarified first.

### Leverage Existing Rule
Suggests searching for existing solutions before creating new code.

### LEVER Compliance Rule
Validates that implementations meet minimum LEVER scores (4/5) before deployment.

## Customization

Rules can be customized for your team:

1. **Modify triggers** - Adjust keywords and patterns
2. **Add team-specific rules** - Create rules for your coding standards  
3. **Set severity levels** - Control which rules are suggestions vs requirements
4. **Enable/disable rules** - Turn specific rules on/off as needed

## Integration with PIB-METHOD

These rules work together with:
- **Claude Code**: Same LEVER standards across all AI systems
- **GitHub Copilot**: Consistent quality gates and processes
- **MCP Server**: Advanced rule logic and context awareness
- **Workflows**: Automatic workflow suggestions based on rule triggers