---
name: LEVER Compliance Validation - Quality Gate
triggers:
  keywords:
    - "ready for review"
    - "implementation complete"
    - "merge request"
    - "pull request"
  contexts:
    - "code_review"
    - "deployment"
    - "quality_check"
  git_events:
    - "pre_commit"
    - "pull_request_created"

severity: high
category: lever_framework

description: |
  Validates overall LEVER framework compliance before code can be merged or deployed.
  Ensures minimum quality standards are met across all LEVER categories.

rule_logic:
  condition: |
    Code is being submitted for review or deployment
    AND LEVER compliance has not been verified
  
  action: require_lever_check
  
  message: |
    📊 **LEVER Compliance Check Required**
    
    PIB-METHOD requires minimum LEVER score of 4.0/5.0 before proceeding:
    
    **LEVER Framework Categories:**
    - **Q**uestion: Requirements clarified and documented
    - **L**everage: Existing solutions identified and used
    - **E**xtend: Built upon existing patterns/code
    - **V**erify: Comprehensive testing completed
    - **E**liminate: Code duplication removed
    - **R**educe: Complexity minimized and manageable
    
    **Run LEVER Check:**
    ```bash
    # Check specific files
    pib-mcp-server lever-check "src/component.ts src/utils.ts"
    
    # Check entire implementation
    pib-mcp-server lever-check "./src/**/*.ts" --check-type implementation
    
    # Check architecture/plan
    pib-mcp-server lever-check "planning-session-123" --check-type plan
    ```
    
    **Quality Requirements:**
    - Overall LEVER score: ≥ 4.0/5.0
    - No category below 3.0/5.0
    - High-priority issues resolved
    - Documentation updated

suggested_workflows:
  - "PIB LEVER Check"
  - "PIB Code Review"
  - "PIB Quality Audit"

compliance_levels:
  minimum_acceptable: 4.0
  excellent: 4.5
  outstanding: 4.8

category_requirements:
  Question:
    min_score: 3.5
    requirements:
      - "Requirements documented"
      - "Assumptions validated"
      - "Success criteria defined"
  
  Leverage:
    min_score: 3.5
    requirements:
      - "Existing solutions searched"
      - "Reusable components identified"
      - "Libraries/frameworks utilized"
  
  Extend:
    min_score: 3.5
    requirements:
      - "Building on existing patterns"
      - "Extending rather than replacing"
      - "Maintaining consistency"
  
  Verify:
    min_score: 4.0
    requirements:
      - "Tests written and passing"
      - "Code review completed"
      - "Quality gates passed"
  
  Eliminate:
    min_score: 3.5
    requirements:
      - "No code duplication"
      - "Common logic extracted"
      - "Redundancy removed"
  
  Reduce:
    min_score: 3.5
    requirements:
      - "Functions focused and small"
      - "Low cyclomatic complexity"
      - "Clear, readable code"

bypass_conditions:
  - emergency_hotfix
  - legacy_code_constraints
  - external_dependency_limitations
  - user_acknowledges_technical_debt

escalation:
  failing_score_actions:
    - block_merge
    - require_remediation
    - notify_tech_lead
    - schedule_refactoring
  
  improvement_suggestions:
    - provide_specific_recommendations
    - suggest_refactoring_opportunities
    - offer_mentoring_sessions
    - create_improvement_tickets

reporting:
  metrics_tracking:
    - average_lever_scores
    - compliance_trends
    - category_weaknesses
    - team_performance
  
  dashboard_integration: true
  historical_analysis: true

integration:
  code_review_tools: true
  ci_cd_pipeline: true
  project_management: true
  quality_metrics: true