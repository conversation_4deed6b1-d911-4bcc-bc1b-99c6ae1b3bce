---
name: LEVER Question Phase - Ask First, Code Second
triggers:
  keywords:
    - "implement"
    - "create"
    - "build"
    - "develop"
    - "add feature"
    - "new"
  contexts:
    - "development"
    - "coding"
    - "implementation"
  file_patterns:
    - "*.ts"
    - "*.js"
    - "*.py"
    - "*.java"

severity: high
category: lever_framework

description: |
  Ensures the Question phase of LEVER framework is completed before any implementation.
  This rule enforces the PIB-METHOD philosophy: "Ask first, then code."

rule_logic:
  condition: |
    User is starting new implementation work without clear requirements documentation
    OR attempting to code without proper planning session
  
  action: suggest_planning
  
  message: |
    🤔 **LEVER Question Phase Required**
    
    Before implementing, let's clarify requirements following the Q-LEVER framework:
    
    **Suggested Action:**
    - Start a PIB planning session: Use "PIB Planning Session" workflow
    - OR clarify these key questions:
      • What specific functionality is needed?
      • What are the acceptance criteria?
      • How does this integrate with existing systems?
      • What constraints or limitations exist?
    
    **Why This Matters:**
    The best code is no code. The second best code is code that already exists and works.
    Proper questioning prevents rework and ensures we build the right solution.

suggested_workflows:
  - "PIB Planning Session"
  - "PIB Architect Design"

bypass_conditions:
  - planning_session_exists
  - requirements_documented
  - user_confirms_understanding

team_customization:
  required_questions:
    - "What problem does this solve?"
    - "Who will use this feature?"
    - "What are the success criteria?"
  
  documentation_requirements:
    - "User story format"
    - "Acceptance criteria"
    - "Technical constraints"

integration:
  mcp_server: true
  claude_code: true
  github_copilot: true