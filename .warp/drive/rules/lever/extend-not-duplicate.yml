---
name: <PERSON><PERSON>VE<PERSON> Extend - Build Upon, Don't Duplicate
triggers:
  keywords:
    - "duplicate"
    - "copy"
    - "similar to"
    - "like existing"
    - "create another"
  contexts:
    - "development"
    - "refactoring"
  code_patterns:
    - "repeated_logic"
    - "similar_functions"
    - "duplicate_components"

severity: medium
category: lever_framework

description: |
  Promotes extending existing code rather than duplicating functionality.
  Supports the LEVER principle of building upon what already works.

rule_logic:
  condition: |
    User is creating functionality similar to existing code
    OR duplicating logic that could be extracted and reused
  
  action: suggest_extension
  
  message: |
    🔧 **LEVER Extend - Build Upon Existing Code**
    
    Instead of duplicating functionality, let's extend what already exists:
    
    **Extension Strategies:**
    1. **Extract Common Logic**: Create shared utilities/functions
    2. **Extend Base Classes**: Add functionality to existing base components
    3. **Use Composition**: Combine existing components in new ways
    4. **Add Parameters**: Make existing functions more flexible
    
    **Code Examples:**
    ```typescript
    // Instead of duplicating:
    function validateUserEmail(email: string) { /* validation */ }
    function validateAdminEmail(email: string) { /* same validation */ }
    
    // Extend existing:
    function validateEmail(email: string, userType?: 'user' | 'admin') {
      // Extended validation logic
    }
    ```
    
    **Benefits:**
    - Reduces code duplication
    - Easier maintenance and updates
    - Consistent behavior across features
    - Better testability

suggested_workflows:
  - "PIB Code Review"
  - "PIB Refactor Analysis"

extension_patterns:
  - inheritance
  - composition
  - parameterization
  - plugin_architecture
  - mixin_patterns

refactoring_suggestions:
  - extract_common_utilities
  - create_base_classes
  - implement_interfaces
  - use_dependency_injection

bypass_conditions:
  - no_suitable_extension_points
  - performance_requirements_differ
  - security_contexts_different
  - user_acknowledges_duplication_necessary

team_guidelines:
  duplication_threshold: 3  # Alert after 3 similar implementations
  refactoring_triggers:
    - "more than 5 lines of identical code"
    - "similar function signatures"
    - "repeated business logic"

integration:
  code_analysis_tools: true
  refactoring_suggestions: true
  pattern_detection: true