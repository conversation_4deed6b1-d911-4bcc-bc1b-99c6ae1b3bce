---
name: <PERSON><PERSON><PERSON><PERSON> Leverage - Search Before Creating
triggers:
  keywords:
    - "create new"
    - "build from scratch"
    - "implement"
    - "write function"
    - "add component"
  contexts:
    - "development"
    - "coding"
  patterns:
    - "similar_functionality_exists"
    - "common_patterns_available"

severity: medium
category: lever_framework

description: |
  Encourages leveraging existing code, patterns, and solutions before creating new implementations.
  Supports the LEVER principle of reusing what already works.

rule_logic:
  condition: |
    User is creating new functionality that might already exist
    OR implementing common patterns without checking existing solutions
  
  action: suggest_search
  
  message: |
    🔍 **LEVER Leverage - Check Existing Solutions**
    
    Before creating new code, let's search for existing solutions:
    
    **Recommended Actions:**
    1. Search existing codebase for similar functionality
    2. Check team knowledge base and documentation
    3. Review established patterns and libraries
    4. Consider extending existing components
    
    **Search Suggestions:**
    ```bash
    # Search for similar functions/components
    rg "function.*{{functionality_keyword}}" --type ts
    
    # Look for existing patterns
    find . -name "*.ts" -exec grep -l "{{pattern_keyword}}" {} \;
    
    # Check team documentation
    grep -r "{{feature_keyword}}" docs/ README.md
    ```
    
    **Why This Matters:**
    Leveraging existing solutions reduces bugs, maintains consistency, and saves development time.

suggested_workflows:
  - "PIB Code Review"
  - "PIB Architecture Review"

search_categories:
  - existing_functions
  - similar_components
  - established_patterns
  - team_libraries
  - external_dependencies

bypass_conditions:
  - thorough_search_completed
  - no_existing_solutions_found
  - existing_solutions_inadequate

automation:
  auto_search_triggers:
    - "user mentions creating X"
    - "user starts implementing Y"
  
  search_scopes:
    - current_project
    - team_repositories
    - documentation
    - knowledge_base

integration:
  mcp_server: true
  search_tools: true
  documentation_system: true