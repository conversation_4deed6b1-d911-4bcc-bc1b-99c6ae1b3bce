---
name: LEVER Verify - Test Before Deploy
triggers:
  keywords:
    - "deploy"
    - "merge"
    - "ready for production"
    - "implementation complete"
    - "finished coding"
  contexts:
    - "deployment"
    - "completion"
    - "pull_request"
  git_events:
    - "pre_commit"
    - "pre_push"
    - "pull_request_created"

severity: high
category: lever_framework

description: |
  Ensures comprehensive verification through testing before deployment.
  Implements the LEVER Verify principle for quality assurance.

rule_logic:
  condition: |
    User is completing implementation without adequate testing
    OR attempting to deploy code without verification
  
  action: require_verification
  
  message: |
    ✅ **LEVER Verify - Testing Required Before Deployment**
    
    Comprehensive verification is required before this can be deployed:
    
    **Verification Checklist:**
    - [ ] Unit tests written and passing
    - [ ] Integration tests for critical paths
    - [ ] Manual testing completed
    - [ ] Code review passed
    - [ ] LEVER compliance score ≥ 4.0
    - [ ] Security scan completed
    - [ ] Performance benchmarks met
    
    **Suggested Testing Strategy:**
    ```bash
    # Run comprehensive test suite
    npm test -- --coverage --verbose
    
    # Check LEVER compliance
    pib-mcp-server lever-check "./src/**/*.ts"
    
    # Security audit
    npm audit --audit-level moderate
    
    # Performance testing
    npm run test:performance
    ```
    
    **Testing Levels:**
    1. **Unit Tests**: Individual function/component testing
    2. **Integration Tests**: Component interaction testing
    3. **End-to-End Tests**: Full user workflow testing
    4. **Performance Tests**: Load and stress testing
    5. **Security Tests**: Vulnerability scanning

suggested_workflows:
  - "PIB Code Review"
  - "PIB Test Generation"
  - "PIB Security Audit"
  - "PIB LEVER Check"

testing_requirements:
  minimum_coverage: 80
  required_test_types:
    - unit_tests
    - integration_tests
  
  quality_gates:
    - lever_score_min: 4.0
    - security_scan_passed: true
    - performance_benchmarks_met: true

bypass_conditions:
  - emergency_hotfix
  - user_accepts_risk
  - comprehensive_testing_completed
  - qa_sign_off_received

automation:
  pre_commit_hooks:
    - run_unit_tests
    - check_lever_compliance
    - security_scan
  
  ci_pipeline_requirements:
    - all_tests_pass
    - coverage_threshold_met
    - quality_gates_passed

team_standards:
  testing_pyramid:
    unit_tests: 70
    integration_tests: 20
    e2e_tests: 10
  
  documentation_required:
    - test_plans
    - coverage_reports
    - quality_metrics

integration:
  testing_frameworks: true
  ci_cd_pipeline: true
  quality_metrics: true