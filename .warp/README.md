# PIB-METHOD Warp Terminal Integration

This directory contains the Warp Terminal integration for PIB-METHOD, enabling the same powerful workflows and AI assistance across Claude Code, GitHub Copilot, and Warp Terminal.

## Directory Structure

```
.warp/
├── mcp-server/          # PIB-METHOD MCP Server
│   ├── server.ts        # Main MCP server implementation
│   ├── tools/           # MCP tool definitions
│   └── package.json     # Node.js dependencies
├── workflows/           # Warp YAML workflows (converted from slash commands)
├── drive/               # Warp Drive content
│   ├── rules/           # LEVER framework rules
│   ├── prompts/         # Sub-agent instructions
│   └── notebooks/       # Development patterns
└── config/              # Warp configuration files
```

## Integration Features

### Triple AI System Support
- **Claude Code**: Native PIB-METHOD experience via `.claude/` directory
- **GitHub Copilot**: Instructions and prompts via `.github/` directory  
- **Warp Terminal**: MCP server + YAML workflows + Warp Drive rules

### Core Capabilities
- **80+ Slash Commands**: All PIB commands available as Warp workflows
- **MCP Server**: Advanced AI tools via Model Context Protocol
- **LEVER Framework**: Quality gates and compliance checking
- **Git Worktrees**: Parallel development workflows
- **Sub-Agent Orchestration**: Intelligent task delegation

## Quick Start

### 1. Install MCP Server
```bash
cd .warp/mcp-server
npm install
npm run build
```

### 2. Configure Warp
Add PIB-METHOD MCP server to Warp settings:
```json
{
  "mcp_servers": {
    "pib-method": {
      "command": "node",
      "args": ["/path/to/.warp/mcp-server/dist/server.js"]
    }
  }
}
```

### 3. Import Workflows
```bash
# In Warp, import all PIB workflows
warp drive import .warp/workflows/
```

### 4. Load Drive Rules
```bash
# Import LEVER framework rules
warp drive import .warp/drive/rules/
```

## Usage Examples

### Development Workflow
```bash
# In Warp Agent Mode (Cmd+I / Ctrl+I)
"Use PIB-METHOD to implement user authentication with OAuth support"

# Or use workflows directly
Ctrl+Shift+R → "PIB Dev Command"
```

### Planning Session
```bash
# Start interactive planning
"Start a PIB planning session for implementing dashboard analytics"
```

### Code Review
```bash
# Automated code review with LEVER compliance
"Review this code using PIB-METHOD LEVER framework"
```

## Sync Script Integration

The `sync-pib-complete.sh` script automatically:
- Generates Warp YAML workflows from Claude commands
- Updates MCP server tool definitions
- Syncs Warp Drive rules with LEVER config
- Maintains consistency across all three AI systems

## Documentation

- [MCP Server Architecture](mcp-server/README.md)
- [Workflow Conversion Guide](workflows/README.md)
- [Warp Drive Rules](drive/rules/README.md)
- [Triple AI System Guide](../docs/warp-integration.md)