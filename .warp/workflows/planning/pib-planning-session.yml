---
name: PIB Planning Session - Interactive Q-LEVER Planning
command: |-
  pib-mcp-server planning-session "{{task_description}}" --scope={{scope}} --ai={{ai_preference}}
tags:
  - pib-method
  - planning
  - q-lever
  - requirements
description: |
  Start an interactive Q-LEVER planning session that asks clarifying questions before any
  implementation. Follows the PIB-METHOD philosophy: "Ask first, then code. The best code
  is no code. The second best code is code that already exists and works."

  The session will:
  1. Ask targeted clarifying questions based on task complexity
  2. Generate comprehensive implementation plan
  3. Provide architecture recommendations
  4. Create work specifications for development teams
  5. Ensure LEVER framework compliance from the start

arguments:
  - name: task_description
    description: Task to plan (e.g., "implement user authentication system", "create analytics dashboard")
    default_value: ~

  - name: scope
    description: Hint about task complexity to adjust questioning depth
    default_value: medium
    type: enum
    values:
      - small
      - medium
      - large

  - name: ai_preference
    description: Preferred AI assistant for planning (auto-detects best choice)
    default_value: auto
    type: enum
    values:
      - claude
      - copilot
      - auto

source_url: "https://github.com/your-org/pib-method"
author: PIB-METHOD
author_url: "https://github.com/your-org/pib-method"
shells: []