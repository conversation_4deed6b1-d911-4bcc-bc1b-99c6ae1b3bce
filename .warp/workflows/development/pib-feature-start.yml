---
name: PIB Feature Start - Isolated Git Worktree Development
command: |-
  pib-mcp-server feature-start "{{feature_name}}" --description="{{description}}" --base={{base_branch}} {{parent_dir}} {{planning_session}}
tags:
  - pib-method
  - git-worktree
  - parallel-development
  - isolation
description: |
  Create an isolated git worktree for parallel feature development with complete state isolation.
  Enables multiple features to be developed simultaneously without conflicts, each with their own
  PIB-METHOD context, conversation history, and workflow state.

  Features:
  - Complete state isolation per worktree
  - Automatic PIB configuration setup
  - Context engineering evolution chains
  - MCP tool selection and caching
  - 90-day archive retention for deleted worktrees
  - Automatic conflict resolution

arguments:
  - name: feature_name
    description: Name of the feature to develop (will be sanitized for git branch)
    default_value: ~

  - name: description
    description: Brief description of the feature for documentation
    default_value: ""

  - name: base_branch
    description: Base branch for the feature (usually main or develop)
    default_value: main

  - name: parent_dir
    description: Parent directory for worktree (optional - defaults to ../)"
    default_value: ""

  - name: planning_session
    description: Use existing planning session ID to incorporate previous analysis
    default_value: ""

source_url: "https://github.com/your-org/pib-method"
author: PIB-METHOD
author_url: "https://github.com/your-org/pib-method"
shells: []