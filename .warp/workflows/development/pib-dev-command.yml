---
name: PIB Dev Command - Intelligent Development Workflow
command: |-
  pib-mcp-server dev-command "{{task_description}}" --priority={{priority}} --focus={{focus}} {{agent_override}} {{use_plan}}
tags:
  - pib-method
  - development
  - ai-assisted
  - claude-code
description: |
  Transform development requests into intelligent, context-aware workflows using PIB-METHOD's
  smart agent routing and complexity detection. Automatically selects the best agent and approach
  based on task analysis and project context.

  Features:
  - Automatic complexity detection (simple/moderate/complex)
  - Smart agent routing (dev-agent, orchestrator)
  - Technology detection and plugin loading
  - LEVER framework compliance checking
  - Integration with existing planning sessions

arguments:
  - name: task_description
    description: Development task to implement (e.g., "implement user authentication with OAuth support")
    default_value: ~

  - name: priority
    description: Task priority level affecting resource allocation and urgency
    default_value: medium
    type: enum
    values:
      - low
      - medium
      - high
      - critical

  - name: focus
    description: Development focus area for specialized handling
    default_value: general
    type: enum
    values:
      - general
      - security
      - performance
      - ui
      - api

  - name: agent_override
    description: Force specific agent (optional) - use when you want to bypass smart routing
    default_value: ""

  - name: use_plan
    description: Use existing planning session ID to leverage previous analysis
    default_value: ""

source_url: "https://github.com/your-org/pib-method"
author: PIB-METHOD
author_url: "https://github.com/your-org/pib-method"
shells: []