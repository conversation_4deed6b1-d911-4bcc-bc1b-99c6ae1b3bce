---
name: PIB Code Review - LEVER Framework Compliance
command: |-
  pib-mcp-server code-review {{files_list}} --focus={{focus}} --severity={{severity}}
tags:
  - pib-method
  - code-review
  - lever
  - quality
description: |
  Automated code review with LEVER framework compliance checking. Analyzes code quality,
  security, performance, and maintainability while ensuring adherence to PIB-METHOD standards.

  Reviews include:
  - LEVER framework scoring (minimum 4/5 required)
  - Security vulnerability detection
  - Performance optimization opportunities
  - Code duplication analysis
  - Architecture pattern compliance
  - Comprehensive recommendations

arguments:
  - name: files_list
    description: Space-separated list of files to review (e.g., "src/auth.ts src/api/users.ts")
    default_value: ~

  - name: focus
    description: Review focus area for specialized analysis
    default_value: general
    type: enum
    values:
      - general
      - security
      - performance
      - maintainability
      - lever

  - name: severity
    description: Minimum issue severity to report
    default_value: medium
    type: enum
    values:
      - low
      - medium
      - high

source_url: "https://github.com/your-org/pib-method"
author: PIB-METHOD
author_url: "https://github.com/your-org/pib-method"
shells: []