# PIB-METHOD Warp Workflows

This directory contains Warp YAML workflows converted from PIB-METHOD slash commands, enabling the same powerful development workflows directly in Warp Terminal.

## Usage

1. **Import workflows into Warp**:
   ```bash
   # In Warp terminal
   Ctrl+Shift+R (or Cmd+Shift+R on Mac) → "Import Workflows"
   ```

2. **Use workflows**:
   ```bash
   # Access workflow menu
   Ctrl+Shift+R → Select workflow → Fill parameters → Execute
   ```

3. **Agent Mode integration**:
   ```bash
   # In Warp Agent Mode (Ctrl+I / Cmd+I)
   "Use PIB dev command to implement user authentication"
   "Start PIB planning session for dashboard feature"
   ```

## Core Workflows

### Development
- **pib-dev-command** - Intelligent development workflow with smart agent routing
- **pib-feature-start** - Create isolated git worktree for parallel development
- **pib-code-review** - Automated code review with LEVER compliance

### Planning & Architecture
- **pib-planning-session** - Interactive Q-LEVER planning with clarifying questions
- **pib-architect-design** - Technical architecture and system design

### Quality & Compliance
- **pib-lever-check** - LEVER framework compliance validation
- **pib-test-generation** - Automated test creation
- **pib-security-audit** - Security analysis and vulnerability scanning

## Workflow Categories

### `/workflows/development/`
Daily development workflows for implementation and coding tasks.

### `/workflows/planning/`
Strategic planning and requirement analysis workflows.

### `/workflows/quality/`
Code quality, testing, and compliance workflows.

### `/workflows/architecture/`
System design and architectural decision workflows.

### `/workflows/management/`
Project management and coordination workflows.

## Integration with MCP Server

All workflows can leverage the PIB-METHOD MCP server for advanced functionality:

1. **Smart routing** - Workflows automatically detect complexity and route to appropriate agents
2. **Context awareness** - MCP server provides project context and technology detection
3. **LEVER compliance** - Automatic quality gate checking
4. **State isolation** - Worktree management for parallel development

## Customization

Workflows can be customized by:

1. **Editing YAML files** - Modify parameters, commands, or descriptions
2. **Adding arguments** - Extend workflows with additional parameters
3. **Creating variants** - Copy and modify existing workflows for specific use cases
4. **Team sharing** - Import/export workflows via Warp Drive

## Sync with PIB-METHOD

Workflows are automatically generated and updated by the `sync-pib-complete.sh` script:

- Converts Claude slash commands to Warp YAML format
- Maintains consistency across all three AI systems
- Updates MCP server tool definitions
- Preserves custom modifications where possible

## Examples

### Development Workflow
```yaml
name: PIB Dev Command
command: pib-mcp-server dev-command "{{task}}" --priority={{priority}}
arguments:
  - name: task
    description: Development task to implement
  - name: priority
    description: Task priority level
    type: enum
    values: [low, medium, high]
```

### Planning Workflow
```yaml
name: PIB Planning Session
command: pib-mcp-server planning-session "{{task}}" --scope={{scope}}
arguments:
  - name: task
    description: Task to plan
  - name: scope
    description: Task complexity hint
    type: enum
    values: [small, medium, large]
```

For detailed documentation on each workflow, see the individual YAML files or use Warp's built-in workflow documentation.