---
name: PIB Architect Design - Technical Architecture & System Design
command: |-
  pib-mcp-server architect-design "{{project_description}}" --type={{design_type}} --complexity={{complexity}} {{existing_systems}} {{constraints}}
tags:
  - pib-method
  - architecture
  - system-design
  - technical-specs
description: |
  Create comprehensive technical architecture and system design with LEVER framework compliance.
  Generates detailed technical specifications, implementation roadmaps, and architecture decisions
  with rationale based on project requirements and constraints.

  Deliverables:
  - System architecture diagrams and component design
  - Technology stack recommendations with justification
  - API specifications and database schema design
  - Security architecture and scalability planning
  - Implementation phases with realistic timelines
  - LEVER compliance validation and quality gates

arguments:
  - name: project_description
    description: Project or feature to architect (e.g., "e-commerce platform with real-time inventory")
    default_value: ~

  - name: design_type
    description: Type of architecture design to focus on
    default_value: system
    type: enum
    values:
      - system
      - module
      - api
      - database
      - frontend
      - infrastructure

  - name: complexity
    description: Project complexity level affecting architectural decisions
    default_value: moderate
    type: enum
    values:
      - simple
      - moderate
      - complex
      - enterprise

  - name: existing_systems
    description: Comma-separated list of existing systems to integrate with (optional)
    default_value: ""

  - name: constraints
    description: Comma-separated list of technical constraints or requirements (optional)
    default_value: ""

source_url: "https://github.com/your-org/pib-method"
author: PIB-METHOD
author_url: "https://github.com/your-org/pib-method"
shells: []