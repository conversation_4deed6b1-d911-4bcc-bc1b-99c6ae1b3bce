<template>
  <div class="modal-overlay">
    <div class="modal-content">
      <div class="modal-header">
        <h3>{{ entity?.name || 'Entity Details' }}</h3>
        <button @click="$emit('close')" class="close-button">×</button>
      </div>

      <div v-if="loading" class="loading-content">
        <div class="loading-spinner"></div>
        <p>Loading entity details...</p>
      </div>

      <div v-else-if="entity" class="modal-body">
        <!-- Entity Overview -->
        <div class="details-section">
          <h4>Overview</h4>
          <div class="details-grid">
            <div class="detail-item">
              <span class="detail-label">Type:</span>
              <span class="detail-value">{{ entity.type }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Industry:</span>
              <span class="detail-value">{{ entity.industry || 'Not specified' }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Size:</span>
              <span class="detail-value">{{ entity.size || 'Not specified' }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Employees:</span>
              <span class="detail-value">{{ entity.employeeCount || 'Not specified' }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Founded:</span>
              <span class="detail-value">
                {{ entity.foundedDate ? new Date(entity.foundedDate).getFullYear() : 'Not specified' }}
              </span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Website:</span>
              <span class="detail-value">
                <a v-if="entity.website" :href="entity.website" target="_blank" class="website-link">
                  {{ entity.website }}
                </a>
                <span v-else>Not specified</span>
              </span>
            </div>
          </div>
          
          <div v-if="entity.description" class="description">
            <h5>Description</h5>
            <p>{{ entity.description }}</p>
          </div>
        </div>

        <!-- Address -->
        <div v-if="entity.headquarters" class="details-section">
          <h4>Headquarters</h4>
          <div class="address-display">
            <p v-if="entity.headquarters.street">{{ entity.headquarters.street }}</p>
            <p>
              <span v-if="entity.headquarters.city">{{ entity.headquarters.city }}</span>
              <span v-if="entity.headquarters.state">, {{ entity.headquarters.state }}</span>
              <span v-if="entity.headquarters.postalCode"> {{ entity.headquarters.postalCode }}</span>
            </p>
            <p v-if="entity.headquarters.country">{{ entity.headquarters.country }}</p>
          </div>
        </div>

        <!-- Tags -->
        <div v-if="entity.tags?.length" class="details-section">
          <h4>Tags</h4>
          <div class="tags-display">
            <span v-for="tag in entity.tags" :key="tag" class="tag">
              {{ tag }}
            </span>
          </div>
        </div>

        <!-- Relationships -->
        <div class="details-section">
          <h4>Relationships ({{ relationships.length }})</h4>
          <div v-if="relationships.length" class="relationships-list">
            <div v-for="rel in relationships" :key="rel.id" class="relationship-item">
              <div class="relationship-info">
                <span class="relationship-type">{{ rel.relationshipType }}</span>
                <span class="relationship-entity">
                  {{ rel.fromEntityId === entity.id ? 
                     getEntityName(rel.toEntityId) : 
                     getEntityName(rel.fromEntityId) }}
                </span>
                <span class="relationship-strength">{{ rel.strength }}/10</span>
              </div>
              <span class="relationship-status" :class="rel.status">{{ rel.status }}</span>
            </div>
          </div>
          <p v-else class="no-data">No relationships found</p>
        </div>

        <!-- Contacts -->
        <div class="details-section">
          <h4>Contacts ({{ contacts.length }})</h4>
          <div v-if="contacts.length" class="contacts-list">
            <div v-for="contact in contacts" :key="contact.id" class="contact-item">
              <div class="contact-info">
                <span class="contact-name">{{ getContactName(contact) }}</span>
                <span v-if="contact.email" class="contact-email">{{ contact.email }}</span>
              </div>
              <span class="contact-stage">{{ contact.lifecycle.stage }}</span>
            </div>
          </div>
          <p v-else class="no-data">No contacts found</p>
        </div>

        <!-- Metadata -->
        <div class="details-section">
          <h4>Details</h4>
          <div class="metadata-grid">
            <div class="detail-item">
              <span class="detail-label">Created:</span>
              <span class="detail-value">{{ formatDate(entity.createdAt) }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Updated:</span>
              <span class="detail-value">{{ formatDate(entity.updatedAt) }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Status:</span>
              <span class="detail-value status" :class="entity.status">{{ entity.status }}</span>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="error-content">
        <p>Entity not found</p>
      </div>

      <div class="modal-footer">
        <button @click="$emit('close')" class="close-button-footer">
          Close
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useBusinessEntities } from '~/composables/useBusinessEntities'
import { useContacts } from '~/composables/useContacts'
import type { BusinessEntity, CompanyRelationship } from '~/composables/useBusinessEntities'
import type { Contact } from '~/composables/useContacts'

interface Props {
  entityId: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
  entityUpdated: [entity: BusinessEntity]
}>()

const { entities, relationships: allRelationships, getEntityRelationships, getEntityContacts } = useBusinessEntities()
const { contacts: allContacts } = useContacts()

const loading = ref(true)

const entity = computed(() => entities.value.find(e => e.id === props.entityId))

const relationships = computed(() => {
  return getEntityRelationships(props.entityId)
})

const contacts = computed(() => {
  return getEntityContacts(props.entityId)
})

const getEntityName = (entityId: string): string => {
  const foundEntity = entities.value.find(e => e.id === entityId)
  return foundEntity?.name || 'Unknown Entity'
}

const getContactName = (contact: Contact): string => {
  return contact.name || `${contact.first_name || ''} ${contact.last_name || ''}`.trim() || 'Unnamed Contact'
}

const formatDate = (date: Date): string => {
  return new Date(date).toLocaleDateString()
}

onMounted(() => {
  // Simulate loading time
  setTimeout(() => {
    loading.value = false
  }, 500)
})
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  width: 100%;
  max-width: 700px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
}

.close-button {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6b7280;
  border-radius: 0.375rem;
}

.close-button:hover {
  background: #f3f4f6;
  color: #374151;
}

.loading-content,
.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  gap: 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.modal-body {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.details-section h4 {
  margin: 0 0 1rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

.details-grid,
.metadata-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detail-label {
  font-size: 0.75rem;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 500;
}

.detail-value {
  font-size: 0.875rem;
  color: #111827;
  font-weight: 500;
}

.website-link {
  color: #3b82f6;
  text-decoration: none;
}

.website-link:hover {
  text-decoration: underline;
}

.description h5 {
  margin: 1rem 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
}

.description p {
  margin: 0;
  color: #6b7280;
  line-height: 1.6;
}

.address-display p {
  margin: 0.25rem 0;
  color: #6b7280;
}

.tags-display {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  padding: 0.25rem 0.75rem;
  background: #dbeafe;
  color: #1d4ed8;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 9999px;
}

.relationships-list,
.contacts-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.relationship-item,
.contact-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 0.5rem;
}

.relationship-info,
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.relationship-type {
  font-size: 0.75rem;
  color: #3b82f6;
  font-weight: 600;
  text-transform: uppercase;
}

.relationship-entity,
.contact-name {
  font-weight: 600;
  color: #111827;
}

.relationship-strength {
  font-size: 0.875rem;
  color: #6b7280;
}

.contact-email {
  font-size: 0.875rem;
  color: #6b7280;
}

.relationship-status,
.contact-stage {
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 9999px;
  text-transform: capitalize;
}

.relationship-status.active,
.contact-stage {
  background: #dcfce7;
  color: #166534;
}

.relationship-status.inactive {
  background: #fecaca;
  color: #dc2626;
}

.status.active {
  color: #166534;
}

.status.inactive {
  color: #dc2626;
}

.no-data {
  color: #9ca3af;
  font-style: italic;
  text-align: center;
  padding: 1rem;
}

.modal-footer {
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
}

.close-button-footer {
  padding: 0.75rem 1.5rem;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.close-button-footer:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

@media (max-width: 768px) {
  .modal-content {
    margin: 0;
    border-radius: 0;
    max-height: 100vh;
  }

  .details-grid,
  .metadata-grid {
    grid-template-columns: 1fr;
  }

  .relationship-item,
  .contact-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
</style>