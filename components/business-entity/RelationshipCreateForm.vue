<template>
  <div class="modal-overlay">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Add Relationship</h3>
        <button @click="$emit('close')" class="close-button">×</button>
      </div>

      <form @submit.prevent="handleSubmit" class="form-content">
        <div class="form-section">
          <div class="form-group">
            <label for="fromEntity" class="form-label">From Company *</label>
            <select id="fromEntity" v-model="form.fromEntityId" required class="form-select">
              <option value="">Select company</option>
              <option v-for="entity in entities" :key="entity.id" :value="entity.id">
                {{ entity.name }}
              </option>
            </select>
          </div>

          <div class="form-group">
            <label for="toEntity" class="form-label">To Company *</label>
            <select id="toEntity" v-model="form.toEntityId" required class="form-select">
              <option value="">Select company</option>
              <option v-for="entity in availableToEntities" :key="entity.id" :value="entity.id">
                {{ entity.name }}
              </option>
            </select>
          </div>

          <div class="form-group">
            <label for="relationshipType" class="form-label">Relationship Type *</label>
            <select id="relationshipType" v-model="form.relationshipType" required class="form-select">
              <option value="">Select type</option>
              <option value="parent">Parent Company</option>
              <option value="subsidiary">Subsidiary</option>
              <option value="partner">Partner</option>
              <option value="competitor">Competitor</option>
              <option value="vendor">Vendor</option>
              <option value="customer">Customer</option>
              <option value="affiliate">Affiliate</option>
            </select>
          </div>

          <div class="form-group">
            <label for="strength" class="form-label">Relationship Strength (1-10)</label>
            <input
              id="strength"
              v-model.number="form.strength"
              type="range"
              min="1"
              max="10"
              class="form-range"
            />
            <div class="range-value">{{ form.strength }}/10</div>
          </div>

          <div class="form-group">
            <label for="description" class="form-label">Description</label>
            <textarea
              id="description"
              v-model="form.description"
              class="form-textarea"
              rows="3"
              placeholder="Brief description of the relationship"
            ></textarea>
          </div>

          <div class="form-group">
            <label for="startDate" class="form-label">Start Date</label>
            <input
              id="startDate"
              v-model="form.startDate"
              type="date"
              class="form-input"
            />
          </div>
        </div>

        <div class="form-actions">
          <button type="button" @click="$emit('close')" class="cancel-button">
            Cancel
          </button>
          <button type="submit" :disabled="submitting" class="submit-button">
            <span v-if="submitting">Creating...</span>
            <span v-else>Create Relationship</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useBusinessEntities } from '~/composables/useBusinessEntities'
import type { BusinessEntity, CompanyRelationship } from '~/composables/useBusinessEntities'

interface Props {
  entities: BusinessEntity[]
}

const props = defineProps<Props>()

const emit = defineEmits<{
  relationshipCreated: [relationship: CompanyRelationship]
  close: []
}>()

const { createRelationship } = useBusinessEntities()

const submitting = ref(false)

const form = ref({
  fromEntityId: '',
  toEntityId: '',
  relationshipType: '' as CompanyRelationship['relationshipType'],
  strength: 5,
  description: '',
  startDate: new Date().toISOString().split('T')[0]
})

const availableToEntities = computed(() => {
  return props.entities.filter(entity => entity.id !== form.value.fromEntityId)
})

const handleSubmit = async () => {
  submitting.value = true
  
  try {
    const relationshipData = {
      fromEntityId: form.value.fromEntityId,
      toEntityId: form.value.toEntityId,
      relationshipType: form.value.relationshipType,
      strength: form.value.strength,
      description: form.value.description || undefined,
      startDate: new Date(form.value.startDate),
      status: 'active' as const,
      metadata: {}
    }

    const created = await createRelationship(relationshipData)
    if (created) {
      emit('relationshipCreated', created)
    }
  } catch (error) {
    console.error('Failed to create relationship:', error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
/* Reusing styles from EntityCreateForm */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
}

.close-button {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6b7280;
  border-radius: 0.375rem;
}

.close-button:hover {
  background: #f3f4f6;
  color: #374151;
}

.form-content {
  padding: 1.5rem;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.form-input,
.form-select {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  resize: vertical;
  transition: border-color 0.2s;
}

.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-range {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #e5e7eb;
  outline: none;
  appearance: none;
}

.form-range::-webkit-slider-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
}

.form-range::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: none;
}

.range-value {
  text-align: center;
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.cancel-button {
  padding: 0.75rem 1.5rem;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-button:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.submit-button {
  padding: 0.75rem 1.5rem;
  border: 1px solid #3b82f6;
  background: #3b82f6;
  color: white;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.submit-button:hover:not(:disabled) {
  background: #2563eb;
  border-color: #2563eb;
}

.submit-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>