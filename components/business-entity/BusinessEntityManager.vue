<template>
  <div class="business-entity-manager">
    <!-- Header -->
    <div class="manager-header">
      <div class="header-content">
        <h1 class="page-title">Business Entity Relationships</h1>
        <p class="page-description">
          Manage company relationships, organizational hierarchies, and network opportunities
        </p>
      </div>
      
      <div class="header-actions">
        <button @click="showDiscoveryPanel = true" class="action-button discovery-button">
          <span class="button-icon">🔍</span>
          Discover Relationships
        </button>
        <button @click="showCreateEntityForm = true" class="action-button primary-button">
          <span class="button-icon">+</span>
          Add Company
        </button>
      </div>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-content">
          <div class="stat-value">{{ networkStats.totalEntities }}</div>
          <div class="stat-label">Companies</div>
        </div>
        <div class="stat-icon company-icon">🏢</div>
      </div>
      
      <div class="stat-card">
        <div class="stat-content">
          <div class="stat-value">{{ networkStats.totalRelationships }}</div>
          <div class="stat-label">Relationships</div>
        </div>
        <div class="stat-icon relationship-icon">🔗</div>
      </div>
      
      <div class="stat-card">
        <div class="stat-content">
          <div class="stat-value">{{ networkStats.companiesWithContacts }}</div>
          <div class="stat-label">Active Contacts</div>
        </div>
        <div class="stat-icon contact-icon">👥</div>
      </div>
      
      <div class="stat-card">
        <div class="stat-content">
          <div class="stat-value">{{ networkStats.averageContactsPerCompany.toFixed(1) }}</div>
          <div class="stat-label">Avg Contacts/Company</div>
        </div>
        <div class="stat-icon metric-icon">📊</div>
      </div>
    </div>

    <!-- Main Content Tabs -->
    <div class="content-tabs">
      <nav class="tab-navigation">
        <button 
          v-for="tab in tabs"
          :key="tab.id"
          @click="activeTab = tab.id"
          class="tab-button"
          :class="{ 'active': activeTab === tab.id }"
        >
          <span class="tab-icon">{{ tab.icon }}</span>
          {{ tab.label }}
        </button>
      </nav>

      <!-- Network Graph Tab -->
      <div v-if="activeTab === 'network'" class="tab-content">
        <div class="content-header">
          <h2>Network Graph</h2>
          <div class="content-actions">
            <button @click="generateNetworkGraph" class="action-button" :disabled="loading">
              <span class="button-icon">⟳</span>
              Refresh Graph
            </button>
          </div>
        </div>
        
        <NetworkGraphVisualization
          :network-data="networkGraph"
          :loading="loading"
          :selected-entity-id="selectedEntityId"
          @node-selected="onNodeSelected"
          @edge-selected="onEdgeSelected"
          @entity-details-requested="showEntityDetails"
          @relationship-edit-requested="editRelationship"
        />
      </div>

      <!-- Hierarchy Tab -->
      <div v-if="activeTab === 'hierarchy'" class="tab-content">
        <div class="content-header">
          <h2>Company Hierarchy</h2>
          <div class="content-actions">
            <button @click="refreshHierarchy" class="action-button" :disabled="loading">
              <span class="button-icon">⟳</span>
              Refresh
            </button>
          </div>
        </div>
        
        <CompanyHierarchyTree
          :hierarchy-data="hierarchyData"
          :loading="loading"
          :selected-entity-id="selectedEntityId"
          @entity-selected="onEntitySelected"
          @contact-selected="onContactSelected"
          @entity-details-requested="showEntityDetails"
          @contact-details-requested="showContactDetails"
        />
      </div>

      <!-- Companies List Tab -->
      <div v-if="activeTab === 'companies'" class="tab-content">
        <div class="content-header">
          <h2>Companies</h2>
          <div class="content-actions">
            <div class="search-box">
              <input
                v-model="searchQuery"
                type="text"
                placeholder="Search companies..."
                class="search-input"
              />
              <span class="search-icon">🔍</span>
            </div>
            <select v-model="entityTypeFilter" class="filter-select">
              <option value="all">All Types</option>
              <option value="company">Companies</option>
              <option value="organization">Organizations</option>
              <option value="institution">Institutions</option>
            </select>
          </div>
        </div>

        <div class="companies-grid">
          <div
            v-for="entity in filteredEntities"
            :key="entity.id"
            class="company-card"
            :class="{ 'selected': selectedEntityId === entity.id }"
            @click="selectEntity(entity)"
          >
            <div class="company-header">
              <div class="company-avatar">
                <span class="company-icon">{{ getEntityIcon(entity.type) }}</span>
              </div>
              <div class="company-info">
                <h3 class="company-name">{{ entity.name }}</h3>
                <p class="company-industry">{{ entity.industry || 'Unspecified' }}</p>
              </div>
              <div class="company-actions">
                <button @click.stop="showEntityDetails(entity.id)" class="icon-button">
                  <span>👁️</span>
                </button>
                <button @click.stop="editEntity(entity)" class="icon-button">
                  <span>✏️</span>
                </button>
              </div>
            </div>

            <div class="company-stats">
              <div class="stat-item">
                <span class="stat-label">Size:</span>
                <span class="stat-value">{{ entity.size || 'Unknown' }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Contacts:</span>
                <span class="stat-value">{{ getEntityContactCount(entity.id) }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Relationships:</span>
                <span class="stat-value">{{ getEntityRelationshipCount(entity.id) }}</span>
              </div>
            </div>

            <div v-if="entity.tags?.length" class="company-tags">
              <span
                v-for="tag in entity.tags.slice(0, 3)"
                :key="tag"
                class="tag"
              >
                {{ tag }}
              </span>
              <span v-if="entity.tags.length > 3" class="tag more-tags">
                +{{ entity.tags.length - 3 }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Relationships Tab -->
      <div v-if="activeTab === 'relationships'" class="tab-content">
        <div class="content-header">
          <h2>Relationships</h2>
          <div class="content-actions">
            <button @click="showCreateRelationshipForm = true" class="action-button primary-button">
              <span class="button-icon">+</span>
              Add Relationship
            </button>
          </div>
        </div>

        <div class="relationships-list">
          <div
            v-for="relationship in relationships"
            :key="relationship.id"
            class="relationship-item"
            :class="{ 'inactive': relationship.status !== 'active' }"
          >
            <div class="relationship-content">
              <div class="relationship-entities">
                <span class="entity-name">{{ getEntityName(relationship.fromEntityId) }}</span>
                <span class="relationship-arrow">→</span>
                <span class="entity-name">{{ getEntityName(relationship.toEntityId) }}</span>
              </div>
              
              <div class="relationship-details">
                <span class="relationship-type">{{ relationship.relationshipType }}</span>
                <span class="relationship-strength">Strength: {{ relationship.strength }}/10</span>
                <span class="relationship-status">{{ relationship.status }}</span>
              </div>

              <div v-if="relationship.description" class="relationship-description">
                {{ relationship.description }}
              </div>
            </div>

            <div class="relationship-actions">
              <button @click="editRelationship(relationship.id)" class="icon-button">
                <span>✏️</span>
              </button>
              <button @click="deleteRelationship(relationship.id)" class="icon-button delete">
                <span>🗑️</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Discovery Panel -->
    <div v-if="showDiscoveryPanel" class="discovery-panel">
      <div class="panel-overlay" @click="showDiscoveryPanel = false"></div>
      <div class="panel-content">
        <div class="panel-header">
          <h3>Relationship Discovery</h3>
          <button @click="showDiscoveryPanel = false" class="close-button">×</button>
        </div>

        <div class="panel-body">
          <div v-if="discoveryLoading" class="discovery-loading">
            <div class="loading-spinner"></div>
            <p>Analyzing relationships...</p>
          </div>

          <div v-else-if="discoveryResults" class="discovery-results">
            <!-- Suggested Relationships -->
            <div v-if="discoveryResults.suggestedRelationships.length" class="discovery-section">
              <h4>Suggested Relationships</h4>
              <div
                v-for="suggestion in discoveryResults.suggestedRelationships"
                :key="`${suggestion.fromEntityId}-${suggestion.toEntityId}`"
                class="suggestion-item"
              >
                <div class="suggestion-content">
                  <div class="suggestion-relationship">
                    <span>{{ getEntityName(suggestion.fromEntityId) }}</span>
                    <span class="relationship-type">{{ suggestion.type }}</span>
                    <span>{{ getEntityName(suggestion.toEntityId) }}</span>
                  </div>
                  <div class="suggestion-confidence">
                    Confidence: {{ (suggestion.confidence * 100).toFixed(0) }}%
                  </div>
                  <div class="suggestion-evidence">
                    <strong>Evidence:</strong> {{ suggestion.evidence.join(', ') }}
                  </div>
                </div>
                <button @click="createSuggestedRelationship(suggestion)" class="action-button">
                  Add
                </button>
              </div>
            </div>

            <!-- Duplicate Entities -->
            <div v-if="discoveryResults.duplicateEntities.length" class="discovery-section">
              <h4>Potential Duplicates</h4>
              <div
                v-for="duplicate in discoveryResults.duplicateEntities"
                :key="duplicate.entities.join('-')"
                class="duplicate-item"
              >
                <div class="duplicate-content">
                  <div class="duplicate-entities">
                    <span
                      v-for="entityId in duplicate.entities"
                      :key="entityId"
                      class="entity-name"
                    >
                      {{ getEntityName(entityId) }}
                    </span>
                  </div>
                  <div class="duplicate-confidence">
                    Similarity: {{ (duplicate.confidence * 100).toFixed(0) }}%
                  </div>
                  <div class="duplicate-similarities">
                    <strong>Similarities:</strong> {{ duplicate.similarities.join(', ') }}
                  </div>
                </div>
                <button @click="mergeDuplicateEntities(duplicate)" class="action-button">
                  Merge
                </button>
              </div>
            </div>

            <!-- Network Opportunities -->
            <div v-if="discoveryResults.networkOpportunities.length" class="discovery-section">
              <h4>Network Opportunities</h4>
              <div
                v-for="opportunity in discoveryResults.networkOpportunities"
                :key="opportunity.description"
                class="opportunity-item"
              >
                <div class="opportunity-content">
                  <div class="opportunity-type">{{ opportunity.type }}</div>
                  <div class="opportunity-description">{{ opportunity.description }}</div>
                  <div class="opportunity-score">Score: {{ opportunity.score.toFixed(2) }}</div>
                  <div v-if="opportunity.actionItems.length" class="opportunity-actions">
                    <strong>Action Items:</strong>
                    <ul>
                      <li v-for="item in opportunity.actionItems" :key="item">{{ item }}</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-else class="discovery-empty">
            <p>Click "Run Discovery" to analyze your business network for new opportunities.</p>
          </div>
        </div>

        <div class="panel-footer">
          <button @click="runDiscovery" class="action-button primary-button" :disabled="discoveryLoading">
            Run Discovery
          </button>
        </div>
      </div>
    </div>

    <!-- Create Entity Form -->
    <EntityCreateForm
      v-if="showCreateEntityForm"
      @entity-created="onEntityCreated"
      @close="showCreateEntityForm = false"
    />

    <!-- Create Relationship Form -->
    <RelationshipCreateForm
      v-if="showCreateRelationshipForm"
      :entities="entities"
      @relationship-created="onRelationshipCreated"
      @close="showCreateRelationshipForm = false"
    />

    <!-- Entity Details Modal -->
    <EntityDetailsModal
      v-if="showEntityDetailsModal"
      :entity-id="selectedEntityForDetails"
      @close="showEntityDetailsModal = false"
      @entity-updated="onEntityUpdated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useBusinessEntities } from '~/composables/useBusinessEntities'
import type { 
  BusinessEntity, 
  CompanyRelationship, 
  RelationshipDiscoveryResult,
  SuggestedRelationship,
  DuplicateEntity
} from '~/composables/useBusinessEntities'

// Components
import NetworkGraphVisualization from './NetworkGraphVisualization.vue'
import CompanyHierarchyTree from './CompanyHierarchyTree.vue'
import EntityCreateForm from './EntityCreateForm.vue'
import RelationshipCreateForm from './RelationshipCreateForm.vue'
import EntityDetailsModal from './EntityDetailsModal.vue'

// Composables
const {
  entities,
  relationships,
  hierarchyData,
  networkStats,
  networkGraph,
  loading,
  error,
  generateNetworkGraph,
  discoverRelationships,
  createRelationship,
  deleteBusinessEntity,
  searchEntities,
  getEntityContacts,
  getEntityRelationships
} = useBusinessEntities()

// State
const activeTab = ref('network')
const selectedEntityId = ref<string | null>(null)
const searchQuery = ref('')
const entityTypeFilter = ref('all')
const showDiscoveryPanel = ref(false)
const showCreateEntityForm = ref(false)
const showCreateRelationshipForm = ref(false)
const showEntityDetailsModal = ref(false)
const selectedEntityForDetails = ref<string | null>(null)

// Discovery state
const discoveryLoading = ref(false)
const discoveryResults = ref<RelationshipDiscoveryResult | null>(null)

// Tab configuration
const tabs = [
  { id: 'network', label: 'Network Graph', icon: '🕸️' },
  { id: 'hierarchy', label: 'Hierarchy', icon: '🌳' },
  { id: 'companies', label: 'Companies', icon: '🏢' },
  { id: 'relationships', label: 'Relationships', icon: '🔗' }
]

// Computed properties
const filteredEntities = computed(() => {
  let result = entities.value

  // Apply search filter
  if (searchQuery.value) {
    result = searchEntities(searchQuery.value)
  }

  // Apply type filter
  if (entityTypeFilter.value !== 'all') {
    result = result.filter(entity => entity.type === entityTypeFilter.value)
  }

  return result
})

// Methods
const selectEntity = (entity: BusinessEntity): void => {
  selectedEntityId.value = entity.id
}

const onNodeSelected = (node: any): void => {
  if (node.entity) {
    selectedEntityId.value = node.entity.id
  }
}

const onEdgeSelected = (edge: any): void => {
  // Handle edge selection if needed
  console.log('Edge selected:', edge)
}

const onEntitySelected = (entityId: string): void => {
  selectedEntityId.value = entityId
}

const onContactSelected = (contactId: string): void => {
  // Handle contact selection if needed
  console.log('Contact selected:', contactId)
}

const showEntityDetails = (entityId: string): void => {
  selectedEntityForDetails.value = entityId
  showEntityDetailsModal.value = true
}

const showContactDetails = (contactId: string): void => {
  // Handle showing contact details
  console.log('Show contact details:', contactId)
}

const editEntity = (entity: BusinessEntity): void => {
  showEntityDetails(entity.id)
}

const editRelationship = (relationshipId: string): void => {
  // Handle relationship editing
  console.log('Edit relationship:', relationshipId)
}

const deleteRelationship = async (relationshipId: string): Promise<void> => {
  if (confirm('Are you sure you want to delete this relationship?')) {
    // Implementation for deleting relationship
    console.log('Delete relationship:', relationshipId)
  }
}

const getEntityName = (entityId: string): string => {
  const entity = entities.value.find(e => e.id === entityId)
  return entity?.name || 'Unknown Entity'
}

const getEntityIcon = (type: BusinessEntity['type']): string => {
  const icons = {
    company: '🏢',
    organization: '🏛️',
    institution: '🎓'
  }
  return icons[type]
}

const getEntityContactCount = (entityId: string): number => {
  const contacts = getEntityContacts(entityId)
  return contacts.length
}

const getEntityRelationshipCount = (entityId: string): number => {
  const entityRelationships = getEntityRelationships(entityId)
  return entityRelationships.length
}

const refreshHierarchy = (): void => {
  // Refresh hierarchy data
  console.log('Refreshing hierarchy...')
}

const runDiscovery = async (): Promise<void> => {
  discoveryLoading.value = true
  try {
    discoveryResults.value = await discoverRelationships()
  } catch (err) {
    console.error('Discovery failed:', err)
  } finally {
    discoveryLoading.value = false
  }
}

const createSuggestedRelationship = async (suggestion: SuggestedRelationship): Promise<void> => {
  try {
    await createRelationship({
      fromEntityId: suggestion.fromEntityId,
      toEntityId: suggestion.toEntityId,
      relationshipType: suggestion.type,
      strength: Math.round(suggestion.confidence * 10),
      description: `Auto-suggested: ${suggestion.evidence.join(', ')}`,
      startDate: new Date(),
      status: 'active',
      metadata: {
        autoSuggested: true,
        confidence: suggestion.confidence,
        source: suggestion.source
      }
    })
    
    // Remove the suggestion from results
    if (discoveryResults.value) {
      discoveryResults.value.suggestedRelationships = discoveryResults.value.suggestedRelationships.filter(
        s => !(s.fromEntityId === suggestion.fromEntityId && s.toEntityId === suggestion.toEntityId)
      )
    }
  } catch (err) {
    console.error('Failed to create suggested relationship:', err)
  }
}

const mergeDuplicateEntities = async (duplicate: DuplicateEntity): Promise<void> => {
  if (confirm('Are you sure you want to merge these entities? This action cannot be undone.')) {
    // Implementation for merging duplicate entities
    console.log('Merge duplicate entities:', duplicate)
  }
}

const onEntityCreated = (entity: BusinessEntity): void => {
  showCreateEntityForm.value = false
  selectedEntityId.value = entity.id
  activeTab.value = 'companies'
}

const onRelationshipCreated = (relationship: CompanyRelationship): void => {
  showCreateRelationshipForm.value = false
  activeTab.value = 'relationships'
}

const onEntityUpdated = (entity: BusinessEntity): void => {
  showEntityDetailsModal.value = false
  // Optionally refresh the current view
}

// Lifecycle
onMounted(async () => {
  // Generate initial network graph
  await generateNetworkGraph()
})
</script>

<style scoped>
.business-entity-manager {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: #f9fafb;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 2rem;
  background: white;
  border-bottom: 1px solid #e5e7eb;
}

.header-content {
  flex: 1;
}

.page-title {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
}

.page-description {
  margin: 0;
  font-size: 1.125rem;
  color: #6b7280;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.action-button:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.primary-button {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.primary-button:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.discovery-button {
  background: #10b981;
  color: white;
  border-color: #10b981;
}

.discovery-button:hover {
  background: #059669;
  border-color: #059669;
}

.button-icon {
  font-size: 1rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  padding: 2rem;
}

.stat-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.stat-icon {
  font-size: 2rem;
  opacity: 0.7;
}

.content-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.tab-navigation {
  display: flex;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 0 2rem;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  border: none;
  background: none;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.tab-button:hover {
  color: #374151;
}

.tab-button.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
}

.tab-icon {
  font-size: 1rem;
}

.tab-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: white;
  border-bottom: 1px solid #e5e7eb;
}

.content-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
}

.content-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.search-box {
  position: relative;
}

.search-input {
  padding: 0.5rem 0.75rem 0.5rem 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  width: 250px;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

.filter-select {
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.companies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  padding: 2rem;
}

.company-card {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.2s;
}

.company-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.company-card.selected {
  border: 2px solid #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.company-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.company-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: #f3f4f6;
  border-radius: 0.75rem;
  font-size: 1.5rem;
}

.company-info {
  flex: 1;
}

.company-name {
  margin: 0 0 0.25rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.company-industry {
  margin: 0;
  font-size: 0.875rem;
  color: #6b7280;
}

.company-actions {
  display: flex;
  gap: 0.5rem;
}

.icon-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: #f3f4f6;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.icon-button:hover {
  background: #e5e7eb;
}

.icon-button.delete:hover {
  background: #fecaca;
}

.company-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-item .stat-label {
  font-size: 0.75rem;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stat-item .stat-value {
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
}

.company-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  padding: 0.25rem 0.75rem;
  background: #f3f4f6;
  color: #374151;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 9999px;
}

.more-tags {
  background: #e5e7eb;
  color: #6b7280;
}

.relationships-list {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.relationship-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.relationship-item.inactive {
  opacity: 0.6;
}

.relationship-content {
  flex: 1;
}

.relationship-entities {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.entity-name {
  font-weight: 600;
  color: #111827;
}

.relationship-arrow {
  color: #6b7280;
  font-size: 1.25rem;
}

.relationship-details {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.relationship-type {
  padding: 0.25rem 0.75rem;
  background: #dbeafe;
  color: #1d4ed8;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 9999px;
  text-transform: capitalize;
}

.relationship-strength {
  font-size: 0.875rem;
  color: #6b7280;
}

.relationship-status {
  padding: 0.25rem 0.75rem;
  background: #dcfce7;
  color: #166534;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 9999px;
  text-transform: capitalize;
}

.relationship-description {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.5rem;
}

.relationship-actions {
  display: flex;
  gap: 0.5rem;
}

.discovery-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.panel-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.panel-content {
  position: relative;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.panel-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
}

.close-button {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6b7280;
  border-radius: 0.375rem;
}

.close-button:hover {
  background: #f3f4f6;
  color: #374151;
}

.panel-body {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
}

.discovery-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 2rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.discovery-results {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.discovery-section h4 {
  margin: 0 0 1rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.suggestion-item,
.duplicate-item,
.opportunity-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 0.5rem;
  margin-bottom: 0.75rem;
}

.suggestion-content,
.duplicate-content,
.opportunity-content {
  flex: 1;
}

.suggestion-relationship,
.duplicate-entities {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.suggestion-confidence,
.duplicate-confidence {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.suggestion-evidence,
.duplicate-similarities {
  font-size: 0.875rem;
  color: #6b7280;
}

.opportunity-type {
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
  text-transform: capitalize;
}

.opportunity-description {
  margin-bottom: 0.5rem;
  color: #374151;
}

.opportunity-score {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
}

.opportunity-actions ul {
  margin: 0.5rem 0 0 1rem;
  padding: 0;
}

.opportunity-actions li {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.discovery-empty {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
}

.panel-footer {
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
}

@media (max-width: 768px) {
  .manager-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-actions {
    justify-content: stretch;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    padding: 1rem;
  }

  .tab-navigation {
    overflow-x: auto;
    padding: 0 1rem;
  }

  .content-header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .content-actions {
    flex-direction: column;
  }

  .companies-grid {
    grid-template-columns: 1fr;
    padding: 1rem;
  }

  .panel-content {
    width: 95%;
    margin: 1rem;
  }
}
</style>