<template>
  <div class="modal-overlay">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Add New Company</h3>
        <button @click="$emit('close')" class="close-button">×</button>
      </div>

      <form @submit.prevent="handleSubmit" class="form-content">
        <div class="form-grid">
          <!-- Basic Information -->
          <div class="form-section">
            <h4>Basic Information</h4>
            
            <div class="form-group">
              <label for="name" class="form-label">Company Name *</label>
              <input
                id="name"
                v-model="form.name"
                type="text"
                required
                class="form-input"
                placeholder="Enter company name"
              />
            </div>

            <div class="form-group">
              <label for="type" class="form-label">Type *</label>
              <select id="type" v-model="form.type" required class="form-select">
                <option value="">Select type</option>
                <option value="company">Company</option>
                <option value="organization">Organization</option>
                <option value="institution">Institution</option>
              </select>
            </div>

            <div class="form-group">
              <label for="industry" class="form-label">Industry</label>
              <input
                id="industry"
                v-model="form.industry"
                type="text"
                class="form-input"
                placeholder="e.g., Technology, Healthcare"
              />
            </div>

            <div class="form-group">
              <label for="description" class="form-label">Description</label>
              <textarea
                id="description"
                v-model="form.description"
                class="form-textarea"
                rows="3"
                placeholder="Brief description of the company"
              ></textarea>
            </div>
          </div>

          <!-- Company Details -->
          <div class="form-section">
            <h4>Company Details</h4>
            
            <div class="form-group">
              <label for="size" class="form-label">Size</label>
              <select id="size" v-model="form.size" class="form-select">
                <option value="">Select size</option>
                <option value="startup">Startup</option>
                <option value="small">Small (1-50 employees)</option>
                <option value="medium">Medium (51-250 employees)</option>
                <option value="large">Large (251-1000 employees)</option>
                <option value="enterprise">Enterprise (1000+ employees)</option>
              </select>
            </div>

            <div class="form-group">
              <label for="employeeCount" class="form-label">Employee Count</label>
              <input
                id="employeeCount"
                v-model.number="form.employeeCount"
                type="number"
                min="1"
                class="form-input"
                placeholder="Number of employees"
              />
            </div>

            <div class="form-group">
              <label for="revenue" class="form-label">Annual Revenue</label>
              <input
                id="revenue"
                v-model.number="form.revenue"
                type="number"
                min="0"
                step="1000"
                class="form-input"
                placeholder="Annual revenue (optional)"
              />
            </div>

            <div class="form-group">
              <label for="foundedDate" class="form-label">Founded Date</label>
              <input
                id="foundedDate"
                v-model="form.foundedDate"
                type="date"
                class="form-input"
              />
            </div>

            <div class="form-group">
              <label for="website" class="form-label">Website</label>
              <input
                id="website"
                v-model="form.website"
                type="url"
                class="form-input"
                placeholder="https://example.com"
              />
            </div>
          </div>
        </div>

        <!-- Address Section -->
        <div class="form-section">
          <h4>Headquarters Address</h4>
          <div class="address-grid">
            <div class="form-group">
              <label for="street" class="form-label">Street Address</label>
              <input
                id="street"
                v-model="form.address.street"
                type="text"
                class="form-input"
                placeholder="Street address"
              />
            </div>

            <div class="form-group">
              <label for="city" class="form-label">City</label>
              <input
                id="city"
                v-model="form.address.city"
                type="text"
                class="form-input"
                placeholder="City"
              />
            </div>

            <div class="form-group">
              <label for="state" class="form-label">State/Province</label>
              <input
                id="state"
                v-model="form.address.state"
                type="text"
                class="form-input"
                placeholder="State or Province"
              />
            </div>

            <div class="form-group">
              <label for="country" class="form-label">Country</label>
              <input
                id="country"
                v-model="form.address.country"
                type="text"
                class="form-input"
                placeholder="Country"
              />
            </div>

            <div class="form-group">
              <label for="postalCode" class="form-label">Postal Code</label>
              <input
                id="postalCode"
                v-model="form.address.postalCode"
                type="text"
                class="form-input"
                placeholder="Postal code"
              />
            </div>
          </div>
        </div>

        <!-- Tags Section -->
        <div class="form-section">
          <h4>Tags</h4>
          <div class="form-group">
            <label for="tags" class="form-label">Tags (comma separated)</label>
            <input
              id="tags"
              v-model="tagsInput"
              type="text"
              class="form-input"
              placeholder="e.g., tech, startup, b2b"
            />
            <div v-if="form.tags.length" class="tags-preview">
              <span v-for="tag in form.tags" :key="tag" class="tag">
                {{ tag }}
                <button @click="removeTag(tag)" type="button" class="tag-remove">×</button>
              </span>
            </div>
          </div>
        </div>

        <div class="form-actions">
          <button type="button" @click="$emit('close')" class="cancel-button">
            Cancel
          </button>
          <button type="submit" :disabled="submitting" class="submit-button">
            <span v-if="submitting">Creating...</span>
            <span v-else>Create Company</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useBusinessEntities } from '~/composables/useBusinessEntities'
import type { BusinessEntity } from '~/composables/useBusinessEntities'

const emit = defineEmits<{
  entityCreated: [entity: BusinessEntity]
  close: []
}>()

const { createBusinessEntity } = useBusinessEntities()

// Form state
const submitting = ref(false)
const tagsInput = ref('')

const form = ref({
  name: '',
  type: '' as BusinessEntity['type'],
  description: '',
  industry: '',
  size: '' as BusinessEntity['size'],
  website: '',
  employeeCount: undefined as number | undefined,
  revenue: undefined as number | undefined,
  foundedDate: '',
  status: 'active' as const,
  subsidiaryIds: [],
  address: {
    street: '',
    city: '',
    state: '',
    country: '',
    postalCode: ''
  },
  tags: [] as string[],
  metadata: {}
})

// Watch tags input for comma-separated values
watch(tagsInput, (newValue) => {
  if (newValue.includes(',')) {
    const tags = newValue.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
    form.value.tags = [...new Set([...form.value.tags, ...tags])]
    tagsInput.value = ''
  }
})

const removeTag = (tagToRemove: string) => {
  form.value.tags = form.value.tags.filter(tag => tag !== tagToRemove)
}

const handleSubmit = async () => {
  submitting.value = true
  
  try {
    // Add any remaining tags from input
    if (tagsInput.value.trim()) {
      const tags = tagsInput.value.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
      form.value.tags = [...new Set([...form.value.tags, ...tags])]
    }

    // Prepare entity data
    const entityData = {
      name: form.value.name,
      type: form.value.type,
      description: form.value.description || undefined,
      industry: form.value.industry || undefined,
      size: form.value.size || undefined,
      website: form.value.website || undefined,
      employeeCount: form.value.employeeCount,
      revenue: form.value.revenue,
      foundedDate: form.value.foundedDate ? new Date(form.value.foundedDate) : undefined,
      status: form.value.status,
      subsidiaryIds: form.value.subsidiaryIds,
      headquarters: hasAddress() ? form.value.address : undefined,
      tags: form.value.tags.length > 0 ? form.value.tags : undefined,
      metadata: form.value.metadata
    }

    const created = await createBusinessEntity(entityData)
    if (created) {
      emit('entityCreated', created)
    }
  } catch (error) {
    console.error('Failed to create entity:', error)
    // Handle error (show notification, etc.)
  } finally {
    submitting.value = false
  }
}

const hasAddress = (): boolean => {
  const addr = form.value.address
  return !!(addr.street || addr.city || addr.state || addr.country || addr.postalCode)
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
}

.close-button {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6b7280;
  border-radius: 0.375rem;
}

.close-button:hover {
  background: #f3f4f6;
  color: #374151;
}

.form-content {
  padding: 1.5rem;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-section h4 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

.address-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.form-input,
.form-select {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  resize: vertical;
  transition: border-color 0.2s;
}

.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.tags-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  background: #dbeafe;
  color: #1d4ed8;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 9999px;
}

.tag-remove {
  border: none;
  background: none;
  color: #1d4ed8;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: bold;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.tag-remove:hover {
  background: rgba(29, 78, 216, 0.1);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.cancel-button {
  padding: 0.75rem 1.5rem;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-button:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.submit-button {
  padding: 0.75rem 1.5rem;
  border: 1px solid #3b82f6;
  background: #3b82f6;
  color: white;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.submit-button:hover:not(:disabled) {
  background: #2563eb;
  border-color: #2563eb;
}

.submit-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .modal-content {
    margin: 0;
    border-radius: 0;
    max-height: 100vh;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .address-grid {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }
}
</style>