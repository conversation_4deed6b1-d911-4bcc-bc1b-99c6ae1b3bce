<template>
  <div class="network-graph-container">
    <!-- Controls Panel -->
    <div class="controls-panel">
      <div class="control-group">
        <label class="control-label">Layout Algorithm:</label>
        <select v-model="layoutType" @change="updateLayout" class="control-select">
          <option value="force">Force Directed</option>
          <option value="hierarchical">Hierarchical</option>
          <option value="circular">Circular</option>
          <option value="grid">Grid</option>
        </select>
      </div>

      <div class="control-group">
        <label class="control-label">Node Size:</label>
        <input 
          v-model="nodeSizeMultiplier" 
          type="range" 
          min="0.5" 
          max="2" 
          step="0.1" 
          @input="updateVisualization"
          class="control-slider"
        />
        <span class="control-value">{{ nodeSizeMultiplier }}x</span>
      </div>

      <div class="control-group">
        <label class="control-label">Show Labels:</label>
        <input 
          v-model="showLabels" 
          type="checkbox" 
          @change="updateVisualization"
          class="control-checkbox"
        />
      </div>

      <div class="control-group">
        <label class="control-label">Filter by Type:</label>
        <select v-model="entityTypeFilter" @change="updateVisualization" class="control-select">
          <option value="all">All Types</option>
          <option value="company">Companies</option>
          <option value="organization">Organizations</option>
          <option value="institution">Institutions</option>
        </select>
      </div>

      <div class="control-group">
        <button @click="resetView" class="control-button">Reset View</button>
        <button @click="exportGraph" class="control-button">Export PNG</button>
      </div>
    </div>

    <!-- Graph Container -->
    <div 
      ref="graphContainer" 
      class="graph-container"
      :class="{ 'loading': loading }"
    >
      <svg
        ref="svgElement"
        :width="dimensions.width"
        :height="dimensions.height"
        class="network-svg"
      >
        <!-- Define patterns and gradients -->
        <defs>
          <pattern id="gridPattern" width="20" height="20" patternUnits="userSpaceOnUse">
            <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#f3f4f6" stroke-width="1"/>
          </pattern>
          
          <!-- Arrowhead markers for directed edges -->
          <marker 
            id="arrowhead" 
            markerWidth="10" 
            markerHeight="7" 
            refX="9" 
            refY="3.5" 
            orient="auto"
          >
            <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
          </marker>
        </defs>

        <!-- Background grid -->
        <rect width="100%" height="100%" fill="url(#gridPattern)" />
        
        <!-- Zoom and pan group -->
        <g ref="zoomGroup" class="zoom-group">
          <!-- Links/Edges -->
          <g class="links-group">
            <g
              v-for="edge in filteredEdges"
              :key="edge.id"
              class="edge-group"
              @click="selectEdge(edge)"
            >
              <line
                :x1="edge.source.x"
                :y1="edge.source.y"
                :x2="edge.target.x"
                :y2="edge.target.y"
                :stroke="edge.color"
                :stroke-width="Math.max(1, edge.weight / 2)"
                :stroke-dasharray="edge.type === 'hierarchy' ? '5,5' : 'none'"
                marker-end="url(#arrowhead)"
                class="edge-line"
                :class="{ 
                  'selected': selectedEdge?.id === edge.id,
                  'animated': edge.animated 
                }"
              />
              
              <!-- Edge label -->
              <text
                v-if="showLabels && edge.weight > 5"
                :x="(edge.source.x + edge.target.x) / 2"
                :y="(edge.source.y + edge.target.y) / 2"
                text-anchor="middle"
                dominant-baseline="middle"
                class="edge-label"
              >
                {{ edge.relationship?.relationshipType }}
              </text>
            </g>
          </g>

          <!-- Nodes -->
          <g class="nodes-group">
            <g
              v-for="node in filteredNodes"
              :key="node.id"
              class="node-group"
              :transform="`translate(${node.x}, ${node.y})`"
              @click="selectNode(node)"
              @mouseenter="showNodeTooltip(node, $event)"
              @mouseleave="hideTooltip"
            >
              <!-- Node circle -->
              <circle
                :r="node.size * nodeSizeMultiplier"
                :fill="node.color"
                :stroke="selectedNode?.id === node.id ? '#000' : '#fff'"
                :stroke-width="selectedNode?.id === node.id ? 3 : 2"
                class="node-circle"
                :class="{ 
                  'selected': selectedNode?.id === node.id,
                  'highlighted': highlightedNodes.has(node.id)
                }"
              />
              
              <!-- Node icon/symbol based on type -->
              <text
                v-if="node.type === 'company'"
                text-anchor="middle"
                dominant-baseline="middle"
                class="node-icon"
                :font-size="node.size * nodeSizeMultiplier * 0.8"
              >
                🏢
              </text>
              
              <!-- Node label -->
              <text
                v-if="showLabels"
                :y="node.size * nodeSizeMultiplier + 16"
                text-anchor="middle"
                class="node-label"
                :font-size="Math.max(10, node.size * nodeSizeMultiplier * 0.4)"
              >
                {{ truncateText(node.name, 15) }}
              </text>

              <!-- Contact count badge -->
              <g v-if="node.metadata.contactCount > 0" class="contact-badge">
                <circle
                  :cx="node.size * nodeSizeMultiplier * 0.7"
                  :cy="-node.size * nodeSizeMultiplier * 0.7"
                  r="8"
                  fill="#ef4444"
                  stroke="#fff"
                  stroke-width="2"
                />
                <text
                  :x="node.size * nodeSizeMultiplier * 0.7"
                  :y="-node.size * nodeSizeMultiplier * 0.7"
                  text-anchor="middle"
                  dominant-baseline="middle"
                  class="badge-text"
                  font-size="10"
                  fill="white"
                >
                  {{ node.metadata.contactCount }}
                </text>
              </g>
            </g>
          </g>
        </g>
      </svg>

      <!-- Loading overlay -->
      <div v-if="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
        <p>Generating network graph...</p>
      </div>

      <!-- Tooltip -->
      <div
        v-if="tooltip.visible"
        ref="tooltipElement"
        class="tooltip"
        :style="{ left: tooltip.x + 'px', top: tooltip.y + 'px' }"
      >
        <div v-if="tooltip.type === 'node'" class="tooltip-content">
          <h4>{{ tooltip.data.name }}</h4>
          <p><strong>Type:</strong> {{ tooltip.data.type }}</p>
          <p v-if="tooltip.data.entity?.industry">
            <strong>Industry:</strong> {{ tooltip.data.entity.industry }}
          </p>
          <p v-if="tooltip.data.entity?.size">
            <strong>Size:</strong> {{ tooltip.data.entity.size }}
          </p>
          <p><strong>Contacts:</strong> {{ tooltip.data.metadata.contactCount || 0 }}</p>
          <p><strong>Connections:</strong> {{ getNodeConnections(tooltip.data.id) }}</p>
        </div>
        
        <div v-if="tooltip.type === 'edge'" class="tooltip-content">
          <h4>{{ tooltip.data.relationship?.relationshipType || 'Relationship' }}</h4>
          <p><strong>Strength:</strong> {{ tooltip.data.weight }}/10</p>
          <p v-if="tooltip.data.relationship?.description">
            {{ tooltip.data.relationship.description }}
          </p>
          <p><strong>Status:</strong> {{ tooltip.data.relationship?.status || 'active' }}</p>
        </div>
      </div>
    </div>

    <!-- Statistics Panel -->
    <div class="stats-panel">
      <h3>Network Statistics</h3>
      <div class="stats-grid">
        <div class="stat-item">
          <span class="stat-label">Entities:</span>
          <span class="stat-value">{{ filteredNodes.length }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Relationships:</span>
          <span class="stat-value">{{ filteredEdges.length }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Density:</span>
          <span class="stat-value">{{ networkDensity.toFixed(3) }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Avg Connections:</span>
          <span class="stat-value">{{ averageConnections.toFixed(1) }}</span>
        </div>
      </div>
    </div>

    <!-- Selected Item Panel -->
    <div v-if="selectedNode || selectedEdge" class="selection-panel">
      <div v-if="selectedNode" class="selected-item">
        <h3>{{ selectedNode.name }}</h3>
        <div class="item-details">
          <p><strong>Type:</strong> {{ selectedNode.type }}</p>
          <p v-if="selectedNode.entity?.industry">
            <strong>Industry:</strong> {{ selectedNode.entity.industry }}
          </p>
          <p v-if="selectedNode.entity?.employeeCount">
            <strong>Employees:</strong> {{ selectedNode.entity.employeeCount }}
          </p>
          <p><strong>Active Contacts:</strong> {{ selectedNode.metadata.contactCount || 0 }}</p>
          
          <div class="item-actions">
            <button @click="viewEntityDetails(selectedNode)" class="action-button">
              View Details
            </button>
            <button @click="highlightConnections(selectedNode)" class="action-button">
              Highlight Connections
            </button>
            <button @click="centerOnNode(selectedNode)" class="action-button">
              Center View
            </button>
          </div>
        </div>
      </div>

      <div v-if="selectedEdge" class="selected-item">
        <h3>{{ selectedEdge.relationship?.relationshipType || 'Relationship' }}</h3>
        <div class="item-details">
          <p><strong>From:</strong> {{ getNodeName(selectedEdge.source) }}</p>
          <p><strong>To:</strong> {{ getNodeName(selectedEdge.target) }}</p>
          <p><strong>Strength:</strong> {{ selectedEdge.weight }}/10</p>
          <p><strong>Type:</strong> {{ selectedEdge.type }}</p>
          
          <div class="item-actions">
            <button @click="editRelationship(selectedEdge)" class="action-button">
              Edit Relationship
            </button>
          </div>
        </div>
      </div>

      <button @click="clearSelection" class="close-button">×</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as d3 from 'd3'
import type { 
  NetworkGraph, 
  GraphNode, 
  GraphEdge, 
  BusinessEntity 
} from '~/composables/useBusinessEntities'

interface Props {
  networkData: NetworkGraph | null
  loading?: boolean
  selectedEntityId?: string | null
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  selectedEntityId: null
})

const emit = defineEmits<{
  nodeSelected: [node: GraphNode]
  edgeSelected: [edge: GraphEdge]
  entityDetailsRequested: [entityId: string]
  relationshipEditRequested: [relationshipId: string]
}>()

// Template refs
const graphContainer = ref<HTMLDivElement>()
const svgElement = ref<SVGSVGElement>()
const zoomGroup = ref<SVGGElement>()
const tooltipElement = ref<HTMLDivElement>()

// State
const dimensions = ref({ width: 800, height: 600 })
const layoutType = ref<'force' | 'hierarchical' | 'circular' | 'grid'>('force')
const nodeSizeMultiplier = ref(1)
const showLabels = ref(true)
const entityTypeFilter = ref('all')
const selectedNode = ref<GraphNode | null>(null)
const selectedEdge = ref<GraphEdge | null>(null)
const highlightedNodes = ref(new Set<string>())

// D3 simulation and zoom
let simulation: d3.Simulation<GraphNode, GraphEdge> | null = null
let zoomBehavior: d3.ZoomBehavior<SVGSVGElement, unknown> | null = null

// Tooltip state
const tooltip = ref({
  visible: false,
  x: 0,
  y: 0,
  type: 'node' as 'node' | 'edge',
  data: null as any
})

// Computed properties
const filteredNodes = computed((): GraphNode[] => {
  if (!props.networkData) return []
  
  let nodes = props.networkData.nodes
  
  if (entityTypeFilter.value !== 'all') {
    nodes = nodes.filter(node => {
      if (node.type === 'company' && node.entity) {
        return node.entity.type === entityTypeFilter.value
      }
      return true
    })
  }
  
  return nodes
})

const filteredEdges = computed((): GraphEdge[] => {
  if (!props.networkData) return []
  
  const nodeIds = new Set(filteredNodes.value.map(n => n.id))
  return props.networkData.edges.filter(edge => 
    nodeIds.has(edge.source) && nodeIds.has(edge.target)
  )
})

const networkDensity = computed((): number => {
  const nodeCount = filteredNodes.value.length
  const edgeCount = filteredEdges.value.length
  
  if (nodeCount < 2) return 0
  return (2 * edgeCount) / (nodeCount * (nodeCount - 1))
})

const averageConnections = computed((): number => {
  const nodeCount = filteredNodes.value.length
  const edgeCount = filteredEdges.value.length
  
  if (nodeCount === 0) return 0
  return (2 * edgeCount) / nodeCount
})

// Methods
const updateDimensions = (): void => {
  if (graphContainer.value) {
    const rect = graphContainer.value.getBoundingClientRect()
    dimensions.value = {
      width: rect.width,
      height: Math.max(400, rect.height)
    }
  }
}

const initializeVisualization = (): void => {
  if (!svgElement.value || !zoomGroup.value) return
  
  const svg = d3.select(svgElement.value)
  const zoomG = d3.select(zoomGroup.value)
  
  // Initialize zoom behavior
  zoomBehavior = d3.zoom<SVGSVGElement, unknown>()
    .scaleExtent([0.1, 4])
    .on('zoom', (event) => {
      zoomG.attr('transform', event.transform)
    })
  
  svg.call(zoomBehavior)
  
  // Create simulation
  createSimulation()
}

const createSimulation = (): void => {
  if (!filteredNodes.value.length) return
  
  // Stop existing simulation
  if (simulation) {
    simulation.stop()
  }
  
  // Create new simulation based on layout type
  switch (layoutType.value) {
    case 'force':
      createForceDirectedLayout()
      break
    case 'hierarchical':
      createHierarchicalLayout()
      break
    case 'circular':
      createCircularLayout()
      break
    case 'grid':
      createGridLayout()
      break
  }
}

const createForceDirectedLayout = (): void => {
  const { width, height } = dimensions.value
  
  simulation = d3.forceSimulation(filteredNodes.value)
    .force('link', d3.forceLink(filteredEdges.value)
      .id((d: any) => d.id)
      .distance(d => Math.max(50, 200 - d.weight * 10))
      .strength(0.1)
    )
    .force('charge', d3.forceManyBody()
      .strength(-300)
      .distanceMax(300)
    )
    .force('center', d3.forceCenter(width / 2, height / 2))
    .force('collision', d3.forceCollide()
      .radius(d => d.size * nodeSizeMultiplier.value + 5)
    )
    .on('tick', updatePositions)
}

const createHierarchicalLayout = (): void => {
  const { width, height } = dimensions.value
  
  // Create a simple hierarchical layout
  const levels = new Map<string, number>()
  const rootNodes = filteredNodes.value.filter(node => 
    !filteredEdges.value.some(edge => 
      edge.target === node.id && edge.type === 'hierarchy'
    )
  )
  
  // Assign levels using BFS
  const queue = rootNodes.map(node => ({ node, level: 0 }))
  while (queue.length > 0) {
    const { node, level } = queue.shift()!
    levels.set(node.id, level)
    
    filteredEdges.value
      .filter(edge => edge.source === node.id && edge.type === 'hierarchy')
      .forEach(edge => {
        const targetNode = filteredNodes.value.find(n => n.id === edge.target)
        if (targetNode && !levels.has(targetNode.id)) {
          queue.push({ node: targetNode, level: level + 1 })
        }
      })
  }
  
  // Position nodes
  const maxLevel = Math.max(...Array.from(levels.values()))
  const levelHeight = height / (maxLevel + 1)
  
  filteredNodes.value.forEach(node => {
    const level = levels.get(node.id) || 0
    node.x = width / 2 + (Math.random() - 0.5) * 200
    node.y = (level + 0.5) * levelHeight
    node.fx = node.x
    node.fy = node.y
  })
  
  simulation = d3.forceSimulation(filteredNodes.value)
    .force('link', d3.forceLink(filteredEdges.value)
      .id((d: any) => d.id)
      .distance(100)
    )
    .on('tick', updatePositions)
}

const createCircularLayout = (): void => {
  const { width, height } = dimensions.value
  const centerX = width / 2
  const centerY = height / 2
  const radius = Math.min(width, height) * 0.3
  
  filteredNodes.value.forEach((node, i) => {
    const angle = (2 * Math.PI * i) / filteredNodes.value.length
    node.x = centerX + radius * Math.cos(angle)
    node.y = centerY + radius * Math.sin(angle)
    node.fx = node.x
    node.fy = node.y
  })
  
  simulation = d3.forceSimulation(filteredNodes.value)
    .force('link', d3.forceLink(filteredEdges.value)
      .id((d: any) => d.id)
      .distance(100)
    )
    .on('tick', updatePositions)
}

const createGridLayout = (): void => {
  const { width, height } = dimensions.value
  const cols = Math.ceil(Math.sqrt(filteredNodes.value.length))
  const cellWidth = width / cols
  const cellHeight = height / Math.ceil(filteredNodes.value.length / cols)
  
  filteredNodes.value.forEach((node, i) => {
    const col = i % cols
    const row = Math.floor(i / cols)
    node.x = (col + 0.5) * cellWidth
    node.y = (row + 0.5) * cellHeight
    node.fx = node.x
    node.fy = node.y
  })
  
  simulation = d3.forceSimulation(filteredNodes.value)
    .on('tick', updatePositions)
}

const updatePositions = (): void => {
  // Trigger reactivity update
  filteredNodes.value.forEach(() => {})
  filteredEdges.value.forEach(() => {})
}

const updateLayout = (): void => {
  createSimulation()
}

const updateVisualization = (): void => {
  nextTick(() => {
    createSimulation()
  })
}

const resetView = (): void => {
  if (!zoomBehavior || !svgElement.value) return
  
  const svg = d3.select(svgElement.value)
  svg.transition()
    .duration(750)
    .call(
      zoomBehavior.transform,
      d3.zoomIdentity
    )
}

const centerOnNode = (node: GraphNode): void => {
  if (!zoomBehavior || !svgElement.value) return
  
  const { width, height } = dimensions.value
  const svg = d3.select(svgElement.value)
  
  svg.transition()
    .duration(750)
    .call(
      zoomBehavior.transform,
      d3.zoomIdentity
        .translate(width / 2, height / 2)
        .scale(1.5)
        .translate(-node.x!, -node.y!)
    )
}

const selectNode = (node: GraphNode): void => {
  selectedNode.value = node
  selectedEdge.value = null
  highlightedNodes.value.clear()
  emit('nodeSelected', node)
}

const selectEdge = (edge: GraphEdge): void => {
  selectedEdge.value = edge
  selectedNode.value = null
  highlightedNodes.value.clear()
  emit('edgeSelected', edge)
}

const clearSelection = (): void => {
  selectedNode.value = null
  selectedEdge.value = null
  highlightedNodes.value.clear()
}

const highlightConnections = (node: GraphNode): void => {
  highlightedNodes.value.clear()
  
  // Add the selected node
  highlightedNodes.value.add(node.id)
  
  // Add connected nodes
  filteredEdges.value.forEach(edge => {
    if (edge.source === node.id) {
      highlightedNodes.value.add(edge.target)
    } else if (edge.target === node.id) {
      highlightedNodes.value.add(edge.source)
    }
  })
}

const showNodeTooltip = (node: GraphNode, event: MouseEvent): void => {
  tooltip.value = {
    visible: true,
    x: event.pageX + 10,
    y: event.pageY - 10,
    type: 'node',
    data: node
  }
}

const hideTooltip = (): void => {
  tooltip.value.visible = false
}

const getNodeConnections = (nodeId: string): number => {
  return filteredEdges.value.filter(edge => 
    edge.source === nodeId || edge.target === nodeId
  ).length
}

const getNodeName = (nodeId: string): string => {
  const node = filteredNodes.value.find(n => n.id === nodeId)
  return node?.name || 'Unknown'
}

const viewEntityDetails = (node: GraphNode): void => {
  if (node.entity) {
    emit('entityDetailsRequested', node.entity.id)
  }
}

const editRelationship = (edge: GraphEdge): void => {
  if (edge.relationship) {
    emit('relationshipEditRequested', edge.relationship.id)
  }
}

const truncateText = (text: string, maxLength: number): string => {
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}

const exportGraph = (): void => {
  if (!svgElement.value) return
  
  const svgData = new XMLSerializer().serializeToString(svgElement.value)
  const canvas = document.createElement('canvas')
  canvas.width = dimensions.value.width
  canvas.height = dimensions.value.height
  
  const ctx = canvas.getContext('2d')!
  const img = new Image()
  
  img.onload = () => {
    ctx.drawImage(img, 0, 0)
    canvas.toBlob((blob) => {
      if (blob) {
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = 'network-graph.png'
        a.click()
        URL.revokeObjectURL(url)
      }
    })
  }
  
  img.src = 'data:image/svg+xml;base64,' + btoa(svgData)
}

// Lifecycle
onMounted(() => {
  updateDimensions()
  window.addEventListener('resize', updateDimensions)
  
  nextTick(() => {
    initializeVisualization()
  })
})

onUnmounted(() => {
  window.removeEventListener('resize', updateDimensions)
  if (simulation) {
    simulation.stop()
  }
})

// Watchers
watch(() => props.networkData, () => {
  nextTick(() => {
    createSimulation()
  })
}, { deep: true })

watch(() => props.selectedEntityId, (newId) => {
  if (newId) {
    const node = filteredNodes.value.find(n => n.id === newId)
    if (node) {
      selectNode(node)
      centerOnNode(node)
    }
  }
})
</script>

<style scoped>
.network-graph-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fafafa;
  border-radius: 8px;
  overflow: hidden;
}

.controls-panel {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  align-items: center;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.control-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
}

.control-select {
  padding: 0.375rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.control-slider {
  width: 80px;
}

.control-value {
  font-size: 0.75rem;
  color: #6b7280;
  min-width: 30px;
}

.control-checkbox {
  width: 16px;
  height: 16px;
}

.control-button {
  padding: 0.375rem 0.75rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.control-button:hover {
  background: #2563eb;
}

.graph-container {
  flex: 1;
  position: relative;
  min-height: 400px;
  background: white;
}

.graph-container.loading {
  pointer-events: none;
}

.network-svg {
  width: 100%;
  height: 100%;
  cursor: grab;
}

.network-svg:active {
  cursor: grabbing;
}

.edge-line {
  cursor: pointer;
  transition: stroke-width 0.2s;
}

.edge-line:hover {
  stroke-width: 3px !important;
}

.edge-line.selected {
  stroke-width: 4px !important;
  stroke: #f59e0b !important;
}

.edge-line.animated {
  stroke-dasharray: 5px;
  animation: dash 2s linear infinite;
}

@keyframes dash {
  to {
    stroke-dashoffset: -10px;
  }
}

.edge-label {
  font-size: 10px;
  fill: #6b7280;
  pointer-events: none;
}

.node-circle {
  cursor: pointer;
  transition: all 0.2s;
}

.node-circle:hover {
  stroke-width: 3px;
  filter: brightness(1.1);
}

.node-circle.selected {
  stroke: #000 !important;
  stroke-width: 3px;
}

.node-circle.highlighted {
  stroke: #f59e0b;
  stroke-width: 3px;
  filter: brightness(1.2);
}

.node-icon {
  pointer-events: none;
  user-select: none;
}

.node-label {
  fill: #374151;
  font-weight: 500;
  pointer-events: none;
  user-select: none;
}

.contact-badge {
  pointer-events: none;
}

.badge-text {
  font-weight: 600;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  max-width: 250px;
  z-index: 1000;
  pointer-events: none;
}

.tooltip-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
}

.tooltip-content p {
  margin: 0.25rem 0;
}

.stats-panel {
  padding: 1rem;
  background: white;
  border-top: 1px solid #e5e7eb;
}

.stats-panel h3 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  color: #374151;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.75rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: #f9fafb;
  border-radius: 0.375rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
}

.stat-value {
  font-weight: 600;
  color: #111827;
}

.selection-panel {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 300px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.selected-item {
  padding: 1rem;
}

.selected-item h3 {
  margin: 0 0 0.75rem 0;
  font-size: 1.125rem;
  color: #111827;
}

.item-details p {
  margin: 0.5rem 0;
  font-size: 0.875rem;
  color: #6b7280;
}

.item-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 1rem;
}

.action-button {
  padding: 0.5rem 0.75rem;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-button:hover {
  background: #e5e7eb;
}

.close-button {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: #6b7280;
}

.close-button:hover {
  color: #374151;
}

@media (max-width: 768px) {
  .controls-panel {
    flex-direction: column;
    align-items: stretch;
  }
  
  .control-group {
    justify-content: space-between;
  }
  
  .selection-panel {
    position: static;
    width: 100%;
    margin: 1rem;
    width: calc(100% - 2rem);
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>