<template>
  <div class="hierarchy-tree-container">
    <!-- Controls -->
    <div class="tree-controls">
      <div class="control-group">
        <label class="control-label">View Mode:</label>
        <select v-model="viewMode" @change="updateVisualization" class="control-select">
          <option value="tree">Tree View</option>
          <option value="org-chart">Org Chart</option>
          <option value="compact">Compact</option>
        </select>
      </div>

      <div class="control-group">
        <label class="control-label">Show Contacts:</label>
        <input 
          v-model="showContacts" 
          type="checkbox" 
          @change="updateVisualization"
          class="control-checkbox"
        />
      </div>

      <div class="control-group">
        <label class="control-label">Node Size:</label>
        <input 
          v-model="nodeScale" 
          type="range" 
          min="0.5" 
          max="2" 
          step="0.1" 
          @input="updateVisualization"
          class="control-slider"
        />
        <span class="control-value">{{ nodeScale }}x</span>
      </div>

      <div class="control-group">
        <button @click="expandAll" class="control-button">Expand All</button>
        <button @click="collapseAll" class="control-button">Collapse All</button>
        <button @click="resetView" class="control-button">Reset View</button>
      </div>
    </div>

    <!-- Tree Visualization -->
    <div 
      ref="treeContainer" 
      class="tree-container"
      :class="{ 'loading': loading }"
    >
      <svg
        ref="svgElement"
        :width="dimensions.width"
        :height="dimensions.height"
        class="hierarchy-svg"
      >
        <!-- Define patterns and gradients -->
        <defs>
          <linearGradient id="companyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
          </linearGradient>
          
          <linearGradient id="departmentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
          </linearGradient>

          <filter id="dropShadow" x="-50%" y="-50%" width="200%" height="200%">
            <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.2"/>
          </filter>
        </defs>

        <!-- Zoom and pan group -->
        <g ref="zoomGroup" class="zoom-group">
          <!-- Links -->
          <g class="links-group">
            <path
              v-for="link in visibleLinks"
              :key="link.source.data.id + '-' + link.target.data.id"
              :d="createLinkPath(link)"
              class="tree-link"
              :class="{ 
                'highlighted': isLinkHighlighted(link),
                'selected': isLinkSelected(link)
              }"
              @click="selectLink(link)"
            />
          </g>

          <!-- Nodes -->
          <g class="nodes-group">
            <g
              v-for="node in visibleNodes"
              :key="node.data.id"
              :transform="`translate(${node.x}, ${node.y})`"
              class="tree-node"
              :class="{ 
                'selected': selectedNode?.data.id === node.data.id,
                'highlighted': highlightedNodes.has(node.data.id),
                'collapsed': node._children && !node.children
              }"
              @click="selectNode(node)"
              @dblclick="toggleNode(node)"
              @mouseenter="showNodeTooltip(node, $event)"
              @mouseleave="hideTooltip"
            >
              <!-- Company Node -->
              <g v-if="node.data.type === 'company'" class="company-node">
                <rect
                  :width="getNodeWidth(node)"
                  :height="getNodeHeight(node)"
                  :x="-getNodeWidth(node) / 2"
                  :y="-getNodeHeight(node) / 2"
                  rx="8"
                  fill="url(#companyGradient)"
                  :stroke="getNodeStroke(node)"
                  :stroke-width="getNodeStrokeWidth(node)"
                  filter="url(#dropShadow)"
                  class="company-rect"
                />
                
                <!-- Company Icon -->
                <text
                  :x="0"
                  :y="-15"
                  text-anchor="middle"
                  class="company-icon"
                  :font-size="16 * nodeScale"
                >
                  🏢
                </text>
                
                <!-- Company Name -->
                <text
                  :x="0"
                  :y="5"
                  text-anchor="middle"
                  class="company-name"
                  :font-size="12 * nodeScale"
                >
                  {{ truncateText(node.data.name, 20) }}
                </text>
                
                <!-- Company Info -->
                <text
                  v-if="node.data.entity?.industry"
                  :x="0"
                  :y="20"
                  text-anchor="middle"
                  class="company-info"
                  :font-size="10 * nodeScale"
                >
                  {{ node.data.entity.industry }}
                </text>

                <!-- Contact Count Badge -->
                <g v-if="node.data.metadata.contactCount > 0" class="contact-badge">
                  <circle
                    :cx="getNodeWidth(node) / 2 - 8"
                    :cy="-getNodeHeight(node) / 2 + 8"
                    r="8"
                    fill="#ef4444"
                    stroke="#fff"
                    stroke-width="2"
                  />
                  <text
                    :x="getNodeWidth(node) / 2 - 8"
                    :y="-getNodeHeight(node) / 2 + 8"
                    text-anchor="middle"
                    dominant-baseline="middle"
                    class="badge-text"
                    font-size="10"
                    fill="white"
                  >
                    {{ node.data.metadata.contactCount }}
                  </text>
                </g>

                <!-- Expand/Collapse Button -->
                <g 
                  v-if="node.children || node._children"
                  class="expand-button"
                  @click.stop="toggleNode(node)"
                >
                  <circle
                    :cx="0"
                    :cy="getNodeHeight(node) / 2 + 10"
                    r="10"
                    fill="white"
                    stroke="#3b82f6"
                    stroke-width="2"
                    class="expand-circle"
                  />
                  <text
                    :x="0"
                    :y="getNodeHeight(node) / 2 + 15"
                    text-anchor="middle"
                    class="expand-text"
                    font-size="12"
                    fill="#3b82f6"
                  >
                    {{ node.children ? '−' : '+' }}
                  </text>
                </g>
              </g>

              <!-- Contact Nodes (when shown) -->
              <g v-if="node.data.type === 'contact' && showContacts" class="contact-node">
                <circle
                  :r="15 * nodeScale"
                  fill="#f3f4f6"
                  :stroke="getNodeStroke(node)"
                  :stroke-width="getNodeStrokeWidth(node)"
                  class="contact-circle"
                />
                
                <text
                  :x="0"
                  :y="-2"
                  text-anchor="middle"
                  class="contact-icon"
                  :font-size="12 * nodeScale"
                >
                  👤
                </text>
                
                <text
                  :x="0"
                  :y="25"
                  text-anchor="middle"
                  class="contact-name"
                  :font-size="10 * nodeScale"
                >
                  {{ truncateText(node.data.name, 15) }}
                </text>

                <text
                  v-if="node.data.role"
                  :x="0"
                  :y="37"
                  text-anchor="middle"
                  class="contact-role"
                  :font-size="8 * nodeScale"
                >
                  {{ truncateText(node.data.role, 15) }}
                </text>
              </g>
            </g>
          </g>
        </g>
      </svg>

      <!-- Loading overlay -->
      <div v-if="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
        <p>Building hierarchy tree...</p>
      </div>

      <!-- Tooltip -->
      <div
        v-if="tooltip.visible"
        ref="tooltipElement"
        class="tooltip"
        :style="{ left: tooltip.x + 'px', top: tooltip.y + 'px' }"
      >
        <div v-if="tooltip.type === 'company'" class="tooltip-content">
          <h4>{{ tooltip.data.name }}</h4>
          <p v-if="tooltip.data.entity?.industry">
            <strong>Industry:</strong> {{ tooltip.data.entity.industry }}
          </p>
          <p v-if="tooltip.data.entity?.size">
            <strong>Size:</strong> {{ tooltip.data.entity.size }}
          </p>
          <p v-if="tooltip.data.entity?.employeeCount">
            <strong>Employees:</strong> {{ tooltip.data.entity.employeeCount }}
          </p>
          <p><strong>Active Contacts:</strong> {{ tooltip.data.metadata.contactCount || 0 }}</p>
          <p><strong>Subsidiaries:</strong> {{ tooltip.data.children?.length || 0 }}</p>
        </div>
        
        <div v-if="tooltip.type === 'contact'" class="tooltip-content">
          <h4>{{ tooltip.data.name }}</h4>
          <p v-if="tooltip.data.role">
            <strong>Role:</strong> {{ tooltip.data.role }}
          </p>
          <p v-if="tooltip.data.department">
            <strong>Department:</strong> {{ tooltip.data.department }}
          </p>
          <p v-if="tooltip.data.hierarchyLevel">
            <strong>Level:</strong> {{ tooltip.data.hierarchyLevel }}
          </p>
        </div>
      </div>
    </div>

    <!-- Tree Navigation -->
    <div class="tree-navigation">
      <div class="breadcrumb">
        <span
          v-for="(item, index) in breadcrumb"
          :key="item.id"
          class="breadcrumb-item"
          :class="{ 'active': index === breadcrumb.length - 1 }"
          @click="navigateToNode(item)"
        >
          {{ item.name }}
          <span v-if="index < breadcrumb.length - 1" class="breadcrumb-separator">›</span>
        </span>
      </div>
    </div>

    <!-- Selected Node Panel -->
    <div v-if="selectedNode" class="selection-panel">
      <div class="selected-item">
        <h3>{{ selectedNode.data.name }}</h3>
        <div class="item-details">
          <div v-if="selectedNode.data.type === 'company'">
            <p v-if="selectedNode.data.entity?.industry">
              <strong>Industry:</strong> {{ selectedNode.data.entity.industry }}
            </p>
            <p v-if="selectedNode.data.entity?.size">
              <strong>Size:</strong> {{ selectedNode.data.entity.size }}
            </p>
            <p v-if="selectedNode.data.entity?.employeeCount">
              <strong>Employees:</strong> {{ selectedNode.data.entity.employeeCount }}
            </p>
            <p><strong>Active Contacts:</strong> {{ selectedNode.data.metadata.contactCount || 0 }}</p>
            <p><strong>Subsidiaries:</strong> {{ selectedNode.data.children?.length || 0 }}</p>
          </div>

          <div v-if="selectedNode.data.type === 'contact'">
            <p v-if="selectedNode.data.role">
              <strong>Role:</strong> {{ selectedNode.data.role }}
            </p>
            <p v-if="selectedNode.data.department">
              <strong>Department:</strong> {{ selectedNode.data.department }}
            </p>
            <p><strong>Level:</strong> {{ selectedNode.data.level }}</p>
          </div>
          
          <div class="item-actions">
            <button 
              v-if="selectedNode.data.type === 'company'"
              @click="viewEntityDetails(selectedNode)" 
              class="action-button"
            >
              View Details
            </button>
            <button 
              v-if="selectedNode.data.type === 'contact'"
              @click="viewContactDetails(selectedNode)" 
              class="action-button"
            >
              View Contact
            </button>
            <button @click="centerOnNode(selectedNode)" class="action-button">
              Center View
            </button>
            <button 
              v-if="selectedNode.children || selectedNode._children"
              @click="toggleNode(selectedNode)" 
              class="action-button"
            >
              {{ selectedNode.children ? 'Collapse' : 'Expand' }}
            </button>
          </div>
        </div>
      </div>

      <button @click="clearSelection" class="close-button">×</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as d3 from 'd3'
import type { HierarchyNode } from '~/composables/useBusinessEntities'

interface TreeNode extends d3.HierarchyNode<HierarchyNode> {
  x?: number
  y?: number
  _children?: TreeNode[]
}

interface TreeLink extends d3.HierarchyLink<HierarchyNode> {
  source: TreeNode
  target: TreeNode
}

interface Props {
  hierarchyData: HierarchyNode[]
  loading?: boolean
  selectedEntityId?: string | null
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  selectedEntityId: null
})

const emit = defineEmits<{
  entitySelected: [entityId: string]
  contactSelected: [contactId: string]
  entityDetailsRequested: [entityId: string]
  contactDetailsRequested: [contactId: string]
}>()

// Template refs
const treeContainer = ref<HTMLDivElement>()
const svgElement = ref<SVGSVGElement>()
const zoomGroup = ref<SVGGElement>()
const tooltipElement = ref<HTMLDivElement>()

// State
const dimensions = ref({ width: 800, height: 600 })
const viewMode = ref<'tree' | 'org-chart' | 'compact'>('tree')
const showContacts = ref(false)
const nodeScale = ref(1)
const selectedNode = ref<TreeNode | null>(null)
const highlightedNodes = ref(new Set<string>())
const breadcrumb = ref<Array<{ id: string; name: string }>>([])

// Tree data
const rootNode = ref<TreeNode | null>(null)
const visibleNodes = ref<TreeNode[]>([])
const visibleLinks = ref<TreeLink[]>([])

// D3 tree layout and zoom
let treeLayout: d3.TreeLayout<HierarchyNode> | null = null
let zoomBehavior: d3.ZoomBehavior<SVGSVGElement, unknown> | null = null

// Tooltip state
const tooltip = ref({
  visible: false,
  x: 0,
  y: 0,
  type: 'company' as 'company' | 'contact',
  data: null as any
})

// Computed properties
const treeData = computed(() => {
  if (!props.hierarchyData.length) return null
  
  // Create D3 hierarchy from data
  const hierarchy = d3.hierarchy<HierarchyNode>({
    id: 'root',
    name: 'Companies',
    type: 'company',
    level: 0,
    children: props.hierarchyData,
    contacts: [],
    metadata: {}
  })

  return hierarchy
})

// Methods
const updateDimensions = (): void => {
  if (treeContainer.value) {
    const rect = treeContainer.value.getBoundingClientRect()
    dimensions.value = {
      width: rect.width,
      height: Math.max(400, rect.height - 100) // Account for controls
    }
  }
}

const initializeVisualization = (): void => {
  if (!svgElement.value || !zoomGroup.value) return
  
  const svg = d3.select(svgElement.value)
  const zoomG = d3.select(zoomGroup.value)
  
  // Initialize zoom behavior
  zoomBehavior = d3.zoom<SVGSVGElement, unknown>()
    .scaleExtent([0.1, 3])
    .on('zoom', (event) => {
      zoomG.attr('transform', event.transform)
    })
  
  svg.call(zoomBehavior)
  
  // Create tree layout
  createTreeLayout()
}

const createTreeLayout = (): void => {
  if (!treeData.value) return
  
  const { width, height } = dimensions.value
  
  // Configure tree layout based on view mode
  switch (viewMode.value) {
    case 'tree':
      treeLayout = d3.tree<HierarchyNode>()
        .size([width - 100, height - 100])
        .separation((a, b) => a.parent === b.parent ? 1 : 2)
      break
      
    case 'org-chart':
      treeLayout = d3.tree<HierarchyNode>()
        .size([height - 100, width - 100])
        .separation((a, b) => a.parent === b.parent ? 1 : 1.5)
      break
      
    case 'compact':
      treeLayout = d3.tree<HierarchyNode>()
        .size([width - 50, height - 50])
        .separation((a, b) => a.parent === b.parent ? 0.8 : 1.2)
      break
  }
  
  // Apply layout
  rootNode.value = treeLayout(treeData.value) as TreeNode
  
  // Transform coordinates for org-chart mode
  if (viewMode.value === 'org-chart') {
    rootNode.value.descendants().forEach(d => {
      const x = d.x
      d.x = d.y
      d.y = x
    })
  }
  
  updateVisibility()
}

const updateVisibility = (): void => {
  if (!rootNode.value) return
  
  // Get visible nodes and links
  const nodes: TreeNode[] = []
  const links: TreeLink[] = []
  
  rootNode.value.descendants().forEach(node => {
    if (isNodeVisible(node)) {
      nodes.push(node)
      
      // Add contacts if enabled
      if (showContacts.value && node.data.contacts) {
        node.data.contacts.forEach((contact, index) => {
          const contactNode: TreeNode = {
            data: {
              id: contact.contactId,
              name: contact.role, // We'd need contact name from contacts data
              type: 'contact',
              level: node.data.level + 1,
              children: [],
              contacts: [],
              role: contact.role,
              department: contact.department,
              metadata: {}
            },
            parent: node,
            children: null,
            depth: node.depth + 1,
            height: 0,
            value: undefined,
            x: node.x! + (index - node.data.contacts.length / 2) * 100,
            y: node.y! + 80
          }
          nodes.push(contactNode)
          
          links.push({
            source: node,
            target: contactNode
          })
        })
      }
    }
  })
  
  // Add hierarchy links
  rootNode.value.links().forEach(link => {
    if (isNodeVisible(link.source) && isNodeVisible(link.target)) {
      links.push(link as TreeLink)
    }
  })
  
  visibleNodes.value = nodes
  visibleLinks.value = links
}

const isNodeVisible = (node: TreeNode): boolean => {
  // Check if node should be visible based on expansion state
  let current = node.parent
  while (current) {
    if (current._children && !current.children) {
      return false // Parent is collapsed
    }
    current = current.parent
  }
  return true
}

const toggleNode = (node: TreeNode): void => {
  if (node.children) {
    // Collapse
    node._children = node.children
    node.children = null
  } else if (node._children) {
    // Expand
    node.children = node._children
    node._children = null
  }
  
  updateVisibility()
}

const expandAll = (): void => {
  if (!rootNode.value) return
  
  rootNode.value.descendants().forEach(node => {
    if (node._children) {
      node.children = node._children
      node._children = null
    }
  })
  
  updateVisibility()
}

const collapseAll = (): void => {
  if (!rootNode.value) return
  
  rootNode.value.descendants().forEach(node => {
    if (node.children && node.depth > 0) {
      node._children = node.children
      node.children = null
    }
  })
  
  updateVisibility()
}

const selectNode = (node: TreeNode): void => {
  selectedNode.value = node
  highlightedNodes.value.clear()
  
  // Update breadcrumb
  const path: Array<{ id: string; name: string }> = []
  let current = node
  while (current.parent) {
    path.unshift({ id: current.data.id, name: current.data.name })
    current = current.parent
  }
  breadcrumb.value = path
  
  // Emit selection event
  if (node.data.type === 'company') {
    emit('entitySelected', node.data.id)
  } else if (node.data.type === 'contact') {
    emit('contactSelected', node.data.id)
  }
}

const selectLink = (link: TreeLink): void => {
  // Highlight the connection
  highlightedNodes.value.clear()
  highlightedNodes.value.add(link.source.data.id)
  highlightedNodes.value.add(link.target.data.id)
}

const clearSelection = (): void => {
  selectedNode.value = null
  highlightedNodes.value.clear()
  breadcrumb.value = []
}

const navigateToNode = (item: { id: string; name: string }): void => {
  const node = visibleNodes.value.find(n => n.data.id === item.id)
  if (node) {
    selectNode(node)
    centerOnNode(node)
  }
}

const centerOnNode = (node: TreeNode): void => {
  if (!zoomBehavior || !svgElement.value || !node.x || !node.y) return
  
  const { width, height } = dimensions.value
  const svg = d3.select(svgElement.value)
  
  svg.transition()
    .duration(750)
    .call(
      zoomBehavior.transform,
      d3.zoomIdentity
        .translate(width / 2, height / 2)
        .scale(1.2)
        .translate(-node.x, -node.y)
    )
}

const resetView = (): void => {
  if (!zoomBehavior || !svgElement.value) return
  
  const svg = d3.select(svgElement.value)
  svg.transition()
    .duration(750)
    .call(zoomBehavior.transform, d3.zoomIdentity)
}

const updateVisualization = (): void => {
  nextTick(() => {
    createTreeLayout()
  })
}

const getNodeWidth = (node: TreeNode): number => {
  const baseWidth = node.data.type === 'company' ? 120 : 80
  return baseWidth * nodeScale.value
}

const getNodeHeight = (node: TreeNode): number => {
  const baseHeight = node.data.type === 'company' ? 60 : 40
  return baseHeight * nodeScale.value
}

const getNodeStroke = (node: TreeNode): string => {
  if (selectedNode.value?.data.id === node.data.id) return '#000'
  if (highlightedNodes.value.has(node.data.id)) return '#f59e0b'
  return '#fff'
}

const getNodeStrokeWidth = (node: TreeNode): number => {
  if (selectedNode.value?.data.id === node.data.id) return 3
  if (highlightedNodes.value.has(node.data.id)) return 2
  return 1
}

const createLinkPath = (link: TreeLink): string => {
  const source = link.source
  const target = link.target
  
  if (!source.x || !source.y || !target.x || !target.y) return ''
  
  if (viewMode.value === 'org-chart') {
    // Horizontal org chart layout
    return `M${source.x},${source.y}
            C${source.x + 50},${source.y}
             ${target.x - 50},${target.y}
             ${target.x},${target.y}`
  } else {
    // Vertical tree layout
    return `M${source.x},${source.y}
            C${source.x},${(source.y + target.y) / 2}
             ${target.x},${(source.y + target.y) / 2}
             ${target.x},${target.y}`
  }
}

const isLinkHighlighted = (link: TreeLink): boolean => {
  return highlightedNodes.value.has(link.source.data.id) && 
         highlightedNodes.value.has(link.target.data.id)
}

const isLinkSelected = (link: TreeLink): boolean => {
  return selectedNode.value?.data.id === link.source.data.id || 
         selectedNode.value?.data.id === link.target.data.id
}

const showNodeTooltip = (node: TreeNode, event: MouseEvent): void => {
  tooltip.value = {
    visible: true,
    x: event.pageX + 10,
    y: event.pageY - 10,
    type: node.data.type as 'company' | 'contact',
    data: node.data
  }
}

const hideTooltip = (): void => {
  tooltip.value.visible = false
}

const viewEntityDetails = (node: TreeNode): void => {
  if (node.data.type === 'company') {
    emit('entityDetailsRequested', node.data.id)
  }
}

const viewContactDetails = (node: TreeNode): void => {
  if (node.data.type === 'contact') {
    emit('contactDetailsRequested', node.data.id)
  }
}

const truncateText = (text: string, maxLength: number): string => {
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}

// Lifecycle
onMounted(() => {
  updateDimensions()
  window.addEventListener('resize', updateDimensions)
  
  nextTick(() => {
    initializeVisualization()
  })
})

onUnmounted(() => {
  window.removeEventListener('resize', updateDimensions)
})

// Watchers
watch(() => props.hierarchyData, () => {
  nextTick(() => {
    createTreeLayout()
  })
}, { deep: true })

watch(() => props.selectedEntityId, (newId) => {
  if (newId) {
    const node = visibleNodes.value.find(n => n.data.id === newId)
    if (node) {
      selectNode(node)
      centerOnNode(node)
    }
  }
})
</script>

<style scoped>
.hierarchy-tree-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fafafa;
  border-radius: 8px;
  overflow: hidden;
}

.tree-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  align-items: center;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.control-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
}

.control-select {
  padding: 0.375rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.control-slider {
  width: 80px;
}

.control-value {
  font-size: 0.75rem;
  color: #6b7280;
  min-width: 30px;
}

.control-checkbox {
  width: 16px;
  height: 16px;
}

.control-button {
  padding: 0.375rem 0.75rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.control-button:hover {
  background: #2563eb;
}

.tree-container {
  flex: 1;
  position: relative;
  min-height: 400px;
  background: white;
}

.tree-container.loading {
  pointer-events: none;
}

.hierarchy-svg {
  width: 100%;
  height: 100%;
  cursor: grab;
}

.hierarchy-svg:active {
  cursor: grabbing;
}

.tree-link {
  fill: none;
  stroke: #94a3b8;
  stroke-width: 2;
  cursor: pointer;
  transition: all 0.2s;
}

.tree-link:hover {
  stroke: #3b82f6;
  stroke-width: 3;
}

.tree-link.highlighted {
  stroke: #f59e0b;
  stroke-width: 3;
}

.tree-link.selected {
  stroke: #ef4444;
  stroke-width: 3;
}

.tree-node {
  cursor: pointer;
  transition: all 0.2s;
}

.tree-node:hover {
  transform: scale(1.05);
}

.tree-node.selected {
  transform: scale(1.1);
}

.tree-node.highlighted {
  filter: brightness(1.2);
}

.tree-node.collapsed {
  opacity: 0.8;
}

.company-rect {
  transition: all 0.2s;
}

.company-icon {
  pointer-events: none;
  user-select: none;
}

.company-name {
  fill: white;
  font-weight: 600;
  pointer-events: none;
  user-select: none;
}

.company-info {
  fill: rgba(255, 255, 255, 0.8);
  pointer-events: none;
  user-select: none;
}

.contact-circle {
  transition: all 0.2s;
}

.contact-icon {
  pointer-events: none;
  user-select: none;
}

.contact-name {
  fill: #374151;
  font-weight: 500;
  pointer-events: none;
  user-select: none;
}

.contact-role {
  fill: #6b7280;
  pointer-events: none;
  user-select: none;
}

.contact-badge {
  pointer-events: none;
}

.badge-text {
  font-weight: 600;
}

.expand-button {
  cursor: pointer;
}

.expand-circle {
  transition: all 0.2s;
}

.expand-button:hover .expand-circle {
  fill: #f3f4f6;
}

.expand-text {
  font-weight: bold;
  pointer-events: none;
  user-select: none;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  max-width: 250px;
  z-index: 1000;
  pointer-events: none;
}

.tooltip-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
}

.tooltip-content p {
  margin: 0.25rem 0;
}

.tree-navigation {
  padding: 0.75rem 1rem;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.breadcrumb-item {
  font-size: 0.875rem;
  color: #6b7280;
  cursor: pointer;
  transition: color 0.2s;
}

.breadcrumb-item:hover {
  color: #3b82f6;
}

.breadcrumb-item.active {
  color: #111827;
  font-weight: 500;
}

.breadcrumb-separator {
  color: #d1d5db;
  user-select: none;
}

.selection-panel {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 300px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.selected-item {
  padding: 1rem;
}

.selected-item h3 {
  margin: 0 0 0.75rem 0;
  font-size: 1.125rem;
  color: #111827;
}

.item-details p {
  margin: 0.5rem 0;
  font-size: 0.875rem;
  color: #6b7280;
}

.item-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 1rem;
}

.action-button {
  padding: 0.5rem 0.75rem;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-button:hover {
  background: #e5e7eb;
}

.close-button {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: #6b7280;
}

.close-button:hover {
  color: #374151;
}

@media (max-width: 768px) {
  .tree-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .control-group {
    justify-content: space-between;
  }
  
  .selection-panel {
    position: static;
    width: 100%;
    margin: 1rem;
    width: calc(100% - 2rem);
  }
}
</style>