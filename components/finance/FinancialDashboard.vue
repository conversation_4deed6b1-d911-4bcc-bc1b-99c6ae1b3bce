<template>
  <div class="financial-dashboard">
    <!-- Dashboard Header -->
    <div class="dashboard-header bg-white border-b border-gray-200 px-6 py-4">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Financial Dashboard</h1>
          <p class="text-gray-600 mt-1">Payment processing and reconciliation overview</p>
        </div>
        
        <div class="header-actions flex items-center gap-3">
          <div class="date-range-selector">
            <select 
              v-model="selectedPeriod"
              @change="loadDashboardData"
              class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
              <option value="90d">Last 90 Days</option>
              <option value="1y">Last Year</option>
            </select>
          </div>
          
          <button
            @click="runReconciliation"
            :disabled="isReconciling"
            class="reconcile-btn bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
          >
            <Icon name="refresh-cw" class="w-4 h-4" :class="{ 'animate-spin': isReconciling }" />
            {{ isReconciling ? 'Reconciling...' : 'Run Reconciliation' }}
          </button>
          
          <button
            @click="exportFinancialData"
            class="export-btn bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
          >
            <Icon name="download" class="w-4 h-4" />
            Export
          </button>
        </div>
      </div>
    </div>

    <!-- Key Metrics Cards -->
    <div class="metrics-section p-6 bg-gray-50">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Revenue -->
        <div class="metric-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <Icon name="dollar-sign" class="w-4 h-4 text-green-600" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total Revenue</p>
              <p class="text-2xl font-bold text-gray-900">{{ formatCurrency(dashboardData?.total_amount || 0) }}</p>
              <p class="text-sm text-green-600 mt-1">
                <Icon name="trending-up" class="w-3 h-3 inline mr-1" />
                +12.5% from last period
              </p>
            </div>
          </div>
        </div>

        <!-- Successful Payments -->
        <div class="metric-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <Icon name="check-circle" class="w-4 h-4 text-blue-600" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Successful Payments</p>
              <p class="text-2xl font-bold text-gray-900">{{ dashboardData?.successful_payments || 0 }}</p>
              <p class="text-sm text-blue-600 mt-1">
                {{ ((dashboardData?.successful_payments || 0) / Math.max(dashboardData?.total_payments || 1, 1) * 100).toFixed(1) }}% success rate
              </p>
            </div>
          </div>
        </div>

        <!-- Failed Payments -->
        <div class="metric-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                <Icon name="x-circle" class="w-4 h-4 text-red-600" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Failed Payments</p>
              <p class="text-2xl font-bold text-gray-900">{{ dashboardData?.failed_payments || 0 }}</p>
              <p class="text-sm text-red-600 mt-1">
                {{ ((dashboardData?.failed_payments || 0) / Math.max(dashboardData?.total_payments || 1, 1) * 100).toFixed(1) }}% failure rate
              </p>
            </div>
          </div>
        </div>

        <!-- Pending Discrepancies -->
        <div class="metric-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                <Icon name="alert-triangle" class="w-4 h-4 text-yellow-600" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Pending Discrepancies</p>
              <p class="text-2xl font-bold text-gray-900">{{ pendingDiscrepancies }}</p>
              <p class="text-sm text-yellow-600 mt-1">
                {{ criticalDiscrepancies }} critical
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="charts-section p-6">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Revenue Trend Chart -->
        <div class="chart-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Revenue Trend</h3>
            <div class="chart-controls">
              <select 
                v-model="revenueChartPeriod"
                @change="updateRevenueChart"
                class="text-sm border border-gray-300 rounded px-2 py-1"
              >
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
              </select>
            </div>
          </div>
          <div class="chart-container h-64">
            <canvas ref="revenueChart" class="w-full h-full"></canvas>
          </div>
        </div>

        <!-- Payment Methods Breakdown -->
        <div class="chart-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Payment Methods</h3>
          </div>
          <div class="chart-container h-64">
            <canvas ref="paymentMethodsChart" class="w-full h-full"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Reconciliations -->
    <div class="reconciliations-section p-6">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900">Recent Reconciliations</h3>
            <button
              @click="viewAllReconciliations"
              class="text-sm text-blue-600 hover:text-blue-800"
            >
              View All
            </button>
          </div>
        </div>
        
        <div class="reconciliations-list">
          <!-- Loading State -->
          <div v-if="isLoading" class="p-6 text-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p class="text-gray-600 mt-2">Loading reconciliations...</p>
          </div>

          <!-- Empty State -->
          <div v-else-if="reconciliationResults.length === 0" class="p-6 text-center">
            <Icon name="file-text" class="w-12 h-12 text-gray-300 mx-auto mb-3" />
            <h4 class="text-sm font-medium text-gray-900 mb-1">No Reconciliations</h4>
            <p class="text-sm text-gray-600">Run your first reconciliation to get started</p>
          </div>

          <!-- Reconciliations List -->
          <div v-else class="divide-y divide-gray-200">
            <div 
              v-for="reconciliation in reconciliationResults.slice(0, 5)"
              :key="reconciliation.id"
              class="p-6 hover:bg-gray-50 cursor-pointer"
              @click="viewReconciliation(reconciliation)"
            >
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <div class="flex items-center gap-3">
                    <div 
                      :class="[
                        'w-3 h-3 rounded-full',
                        getStatusColor(reconciliation.status)
                      ]"
                    ></div>
                    <div>
                      <p class="text-sm font-medium text-gray-900">
                        {{ formatDateRange(reconciliation.period_start, reconciliation.period_end) }}
                      </p>
                      <p class="text-sm text-gray-600">
                        {{ reconciliation.total_payments }} payments • {{ formatCurrency(reconciliation.total_amount) }}
                      </p>
                    </div>
                  </div>
                </div>
                
                <div class="flex items-center gap-4">
                  <div class="text-right">
                    <p class="text-sm font-medium text-gray-900">
                      {{ reconciliation.reconciled_count }}/{{ reconciliation.total_payments }} reconciled
                    </p>
                    <p v-if="reconciliation.discrepancy_count > 0" class="text-sm text-red-600">
                      {{ reconciliation.discrepancy_count }} discrepancies
                    </p>
                  </div>
                  
                  <div 
                    :class="[
                      'px-2 py-1 rounded-full text-xs font-medium',
                      getStatusBadgeClass(reconciliation.status)
                    ]"
                  >
                    {{ getStatusLabel(reconciliation.status) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Upcoming Payments -->
    <div class="upcoming-payments-section p-6">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">Upcoming Payments</h3>
        </div>
        
        <div class="upcoming-payments-list p-6">
          <div v-if="upcomingPayments.length === 0" class="text-center py-8">
            <Icon name="calendar" class="w-12 h-12 text-gray-300 mx-auto mb-3" />
            <p class="text-sm text-gray-600">No upcoming payments scheduled</p>
          </div>
          
          <div v-else class="space-y-4">
            <div 
              v-for="payment in upcomingPayments.slice(0, 5)"
              :key="payment.subscription_id"
              class="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
            >
              <div class="flex-1">
                <p class="text-sm font-medium text-gray-900">{{ payment.subscription_name }}</p>
                <p class="text-sm text-gray-600">Due {{ formatDate(payment.due_date) }}</p>
              </div>
              
              <div class="text-right">
                <p class="text-sm font-medium text-gray-900">{{ formatCurrency(payment.amount, payment.currency) }}</p>
                <p 
                  :class="[
                    'text-xs',
                    payment.status === 'scheduled' ? 'text-green-600' : 
                    payment.status === 'retry' ? 'text-yellow-600' : 'text-red-600'
                  ]"
                >
                  {{ payment.status }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Reconciliation Detail Modal -->
    <!-- TODO: Implement ReconciliationDetailModal component -->
    <!-- <ReconciliationDetailModal
      v-if="selectedReconciliation"
      :reconciliation="selectedReconciliation"
      @close="selectedReconciliation = null"
      @resolve-discrepancy="resolveDiscrepancy"
    /> -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { Chart, registerables } from 'chart.js'
import { Icon } from '#components'
import type { PaymentReconciliation, FinancialDashboardData, UpcomingPayment } from '~/types/paymentReconciliation'

// Register Chart.js components
Chart.register(...registerables)

// Import child components
// TODO: Create ReconciliationDetailModal component
// import ReconciliationDetailModal from './ReconciliationDetailModal.vue'

// Use composables
const {
  isLoading,
  error,
  reconciliationResults,
  pendingDiscrepancies,
  criticalDiscrepancies,
  performReconciliation,
  getReconciliationsByDateRange,
  resolveDiscrepancy: resolveDiscrepancyAction,
  formatCurrency
} = usePaymentReconciliation()

// Local state
const selectedPeriod = ref('30d')
const revenueChartPeriod = ref('daily')
const isReconciling = ref(false)
const selectedReconciliation = ref<PaymentReconciliation | null>(null)
const dashboardData = ref<FinancialDashboardData | null>(null)
const upcomingPayments = ref<UpcomingPayment[]>([])

// Chart refs
const revenueChart = ref<HTMLCanvasElement>()
const paymentMethodsChart = ref<HTMLCanvasElement>()

// Chart instances
let revenueChartInstance: Chart | null = null
let paymentMethodsChartInstance: Chart | null = null

// Methods
const loadDashboardData = async () => {
  try {
    const endDate = new Date()
    const startDate = new Date()
    
    switch (selectedPeriod.value) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7)
        break
      case '30d':
        startDate.setDate(endDate.getDate() - 30)
        break
      case '90d':
        startDate.setDate(endDate.getDate() - 90)
        break
      case '1y':
        startDate.setFullYear(endDate.getFullYear() - 1)
        break
    }

    // Load financial dashboard data
    const response = await $fetch('/api/finance/dashboard', {
      query: {
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString()
      }
    })

    if (response.success) {
      dashboardData.value = response.data
      upcomingPayments.value = response.upcoming_payments || []
    }

    // Load recent reconciliations
    await getReconciliationsByDateRange(startDate, endDate)

    // Update charts
    await nextTick()
    updateCharts()

  } catch (err: any) {
    console.error('Error loading dashboard data:', err)
  }
}

const runReconciliation = async () => {
  isReconciling.value = true
  
  try {
    const endDate = new Date()
    const startDate = new Date()
    startDate.setDate(endDate.getDate() - 1) // Yesterday

    const result = await performReconciliation(startDate, endDate)
    
    if (result) {
      // Refresh dashboard data
      await loadDashboardData()
    }
  } catch (err: any) {
    console.error('Error running reconciliation:', err)
  } finally {
    isReconciling.value = false
  }
}

const updateCharts = () => {
  updateRevenueChart()
  updatePaymentMethodsChart()
}

const updateRevenueChart = () => {
  if (!revenueChart.value || !dashboardData.value) return

  const ctx = revenueChart.value.getContext('2d')
  if (!ctx) return

  // Destroy existing chart
  if (revenueChartInstance) {
    revenueChartInstance.destroy()
  }

  const trendData = dashboardData.value.revenue_trend || []
  
  revenueChartInstance = new Chart(ctx, {
    type: 'line',
    data: {
      labels: trendData.map(d => formatChartDate(new Date(d.date))),
      datasets: [{
        label: 'Revenue',
        data: trendData.map(d => d.value),
        borderColor: '#3B82F6',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        borderWidth: 2,
        fill: true,
        tension: 0.4
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            callback: function(value: any) {
              return formatCurrency(value)
            }
          }
        }
      }
    }
  })
}

const updatePaymentMethodsChart = () => {
  if (!paymentMethodsChart.value || !dashboardData.value) return

  const ctx = paymentMethodsChart.value.getContext('2d')
  if (!ctx) return

  // Destroy existing chart
  if (paymentMethodsChartInstance) {
    paymentMethodsChartInstance.destroy()
  }

  const methodData = dashboardData.value.payment_method_breakdown || []
  
  paymentMethodsChartInstance = new Chart(ctx, {
    type: 'doughnut',
    data: {
      labels: methodData.map(d => d.payment_method),
      datasets: [{
        data: methodData.map(d => d.transaction_count),
        backgroundColor: [
          '#3B82F6',
          '#10B981',
          '#F59E0B',
          '#EF4444',
          '#8B5CF6'
        ]
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom'
        }
      }
    }
  })
}

const exportFinancialData = async () => {
  try {
    const response = await $fetch('/api/finance/export', {
      method: 'POST',
      body: {
        format: 'excel',
        period: selectedPeriod.value
      }
    })

    if (response.success && response.download_url) {
      window.open(response.download_url, '_blank')
    }
  } catch (err: any) {
    console.error('Error exporting financial data:', err)
  }
}

const viewReconciliation = (reconciliation: PaymentReconciliation) => {
  selectedReconciliation.value = reconciliation
}

const viewAllReconciliations = () => {
  navigateTo('/c/finance/reconciliations')
}

const resolveDiscrepancy = async (reconciliationId: string, discrepancyId: string, resolution: string, notes?: string) => {
  const success = await resolveDiscrepancyAction(reconciliationId, discrepancyId, resolution, notes)
  
  if (success) {
    // Refresh data
    await loadDashboardData()
  }
}

// Utility functions
const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  }).format(date)
}

const formatChartDate = (date: Date): string => {
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric'
  }).format(date)
}

const formatDateRange = (start: Date, end: Date): string => {
  return `${formatDate(start)} - ${formatDate(end)}`
}

const getStatusColor = (status: string): string => {
  const colors = {
    completed: 'bg-green-500',
    requires_review: 'bg-yellow-500',
    failed: 'bg-red-500',
    pending: 'bg-gray-500'
  }
  return colors[status as keyof typeof colors] || 'bg-gray-500'
}

const getStatusBadgeClass = (status: string): string => {
  const classes = {
    completed: 'bg-green-100 text-green-800',
    requires_review: 'bg-yellow-100 text-yellow-800',
    failed: 'bg-red-100 text-red-800',
    pending: 'bg-gray-100 text-gray-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const getStatusLabel = (status: string): string => {
  const labels = {
    completed: 'Completed',
    requires_review: 'Needs Review',
    failed: 'Failed',
    pending: 'Pending'
  }
  return labels[status as keyof typeof labels] || status
}

// Lifecycle
onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.financial-dashboard {
  @apply flex flex-col h-full bg-gray-50;
}

.dashboard-header {
  @apply bg-white border-b border-gray-200 px-6 py-4;
}

.metrics-section {
  @apply p-6 bg-gray-50;
}

.metric-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.charts-section {
  @apply p-6;
}

.chart-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.chart-container {
  @apply h-64;
}

.reconciliations-section {
  @apply p-6;
}

.upcoming-payments-section {
  @apply p-6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .header-actions {
    @apply flex-col gap-2;
  }
  
  .charts-section .grid {
    @apply grid-cols-1;
  }
}
</style>
