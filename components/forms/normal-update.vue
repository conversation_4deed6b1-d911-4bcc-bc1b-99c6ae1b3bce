<script setup lang="ts">
const createCustomer: any = defineProps({
  open: {
    type: Boolean,
    default: () => {
      return false;
    },
  },
  formsItem: {
    type: Object,
    default: () => {
      return {};
    },
  },
  title: {
    type: String,
    default: ''
  },
  item: {
    type: Object,
    default: () => {
      return {};
    },
  }
});

const { currentSpace } = space()
const createEmit: any = getCurrentInstance()

const openModal = ref(false);
const loading = ref(false);
const formDataAdd = ref({});

watch(()=> createCustomer.open, (val) => {
  openModal.value = val
})
onMounted(() => {
  openModal.value = createCustomer.open;
  formDataAdd.value = createCustomer.item;
});

const schemaAdd = computed(() => {
    let new_data = JSON.parse(JSON.stringify(createCustomer.formsItem.data));
    for (let i = 0; i < new_data.length; i++) {
            new_data[i].value = createCustomer.item[new_data[i].val];
    }
    const forms: any = {

    }
  let formItems = schemaSkin(new_data);
  for(let i = 0; i < formItems.length; i++) {
   let area = formItems[i].area
    if(!forms[area]) {
      forms[area] = []
    }
    forms[area].push(formItems[i])
  }
  return forms
});

function inputAdd(data: { path?: any }) {
  if (data && data.path === undefined && data.bubbles === undefined){
    console.log('DATA',data);
    formDataAdd.value = {...formDataAdd.value,...data};
    console.log('FORMDATA',formDataAdd.value);

  }
}
const notificationOpen = useState<any>('notificationOpen', () => {
  return {
    open: false,
    text: '',
    color: 'o_gradient',
  }
})
const spaceAddress = ref(true)
const center: any = ref([])
const currentLocation: any = ref({})
const submit = async () => {
  let payload = formDataAdd.value

  if(spaceAddress.value){
    payload = {
      ...payload,
      hash: currentSpace.value.hash ? currentSpace.value.hash : '',
	lat: currentSpace.value.lat ? currentSpace.value.lat : '',
	lng: currentSpace.value.lng ? currentSpace.value.lng : '',
	location: currentSpace.value.location ? currentSpace.value.location : '',
	formatted_address: currentSpace.value.formatted_address ? currentSpace.value.formatted_address : '',
	// website: currentSpace.value.website ? currentSpace.value.website : '',
	// email: currentSpace.value.email ? currentSpace.value.email : '',
	// phone: currentSpace.value.phone ? currentClient.value.phone : '',
    }
  }else{
    payload = {
      ...payload,
      hash: currentLocation.value.hash ? currentLocation.value.hash : '',
	lat: currentLocation.value.lat ? currentLocation.value.lat : '',
	lng: currentLocation.value.lng ? currentLocation.value.lng : '',
	location: currentLocation.value.location ? currentLocation.value.location : '',
	formatted_address: currentLocation.value.formatted_address ? currentLocation.value.formatted_address : '',
    }
  }
  loading.value = true;
  let collection = createCustomer.formsItem.filters.collection;
  await setById(collection, createCustomer.item.id, {...payload, ...extraInfoUpdate()})
  createEmit.emit('updated', {id: createCustomer.item.id, ...payload,  ...extraInfoUpdate()})
  loading.value = false;

  notificationOpen.value.open = true;
  notificationOpen.value.text = 'Updated ' + createCustomer.title;
  notificationOpen.value.color = 'bg-success';
  openModal.value = false;

};

function input_address(data: { path: undefined; bubbles: undefined; utc_offset: any; lat: any; lng: any; vicinity: any; adr_address: any; formatted_address: string; place_id: any; url: any }) {
  if (data && data.path === undefined && data.bubbles === undefined) {
    currentLocation.value.utc_offset = data.utc_offset
    currentLocation.value.lat = data.lat
    currentLocation.value.lng = data.lng
    currentLocation.value.vicinity = data.vicinity
    currentLocation.value.adr_address = data.adr_address
    currentLocation.value.formatted_address = data.formatted_address
    currentLocation.value.place_id = data.place_id
    currentLocation.value.adr_url = data.url
    const split_address = data.formatted_address.split(',')
    currentLocation.value.address = split_address[0]
    currentLocation.value.city = split_address[1]
    currentLocation.value.state = split_address[2]
    currentLocation.value.zip = split_address[3]
    currentLocation.value.country = split_address[4]
    center.value = [data.lat, data.lng];
    currentLocation.value.hash = createHash(center.value)

  }
}
</script>

<template>
  <div>
    <div v-if="loading">
      <loading-colorrockets />
    </div>
    <div v-else>
      <div class="mb-6">
        <h2 class="text-xl font-semibold text-white mb-2">{{ title }}</h2>
        <p class="text-sm text-gray-300">Update your information</p>
      </div>

      <div v-if="formsItem.filters.areas">
        <div v-for="(area, index) in formsItem.filters.areas" :key="index" class="mb-6">
          <h3 class="text-base font-medium mb-4 text-gray-300 flex items-center">
            <Icon name="mdi:form-select" class="mr-2 h-5 w-5 text-blue-400" />
            {{ area }}
          </h3>
          <div class="bg-gradient-to-br from-gray-700/30 to-gray-600/30 rounded-lg p-6 shadow-sm border border-white/10 backdrop-blur-sm">
            <forms-generator :schema="schemaAdd[index + 1]" @input="inputAdd" />

            <div class="mt-6 p-4 bg-gradient-to-r from-blue-900/20 to-blue-800/20 rounded-lg border border-blue-400/20 backdrop-blur-sm">
              <div class="flex items-center mb-3">
                <input
                  v-model="spaceAddress"
                  type="checkbox"
                  id="useAccountAddress"
                  class="w-4 h-4 text-blue-600 bg-gray-700/50 border-white/20 rounded focus:ring-blue-500 focus:ring-2 focus:ring-opacity-50"
                />
                <label for="useAccountAddress" class="ml-3 text-sm font-medium text-gray-300">
                  Use Account's address
                </label>
              </div>

              <div v-if="!spaceAddress" class="mt-3">
                <google-autocomplete
                  @input="input_address"
                  label="Update location"
                  classes="w-full px-4 py-3 border border-white/20 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-700/50 text-gray-200 placeholder-gray-400 backdrop-blur-sm transition-all duration-200"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Account Information Alerts -->
      <div class="mt-6 space-y-3">
        <div v-if="currentSpace.hash" class="p-4 bg-gradient-to-r from-blue-900/20 to-blue-800/20 rounded-lg text-sm text-gray-300 border border-blue-400/20 backdrop-blur-sm">
          <div class="flex items-center">
            <Icon name="mdi:map-marker" class="mr-2 h-4 w-4 text-blue-400" />
            <span class="font-medium text-blue-300">Location:</span>
            <span class="ml-2">{{ currentSpace.formatted_address }}</span>
          </div>
        </div>
        <div v-else class="p-4 bg-gradient-to-r from-red-900/20 to-red-800/20 rounded-lg text-sm text-red-300 border border-red-400/20 backdrop-blur-sm">
          <p class="flex items-center">
            <Icon name="mdi:alert-circle" class="mr-2 h-4 w-4 text-red-400" />
            Please make sure your Account Location is set before uploading
          </p>
          <a href="/c/dashboard" class="text-blue-400 hover:text-blue-300 underline text-xs mt-2 inline-block transition-colors duration-200">
            Update in Dashboard
          </a>
        </div>

        <div v-if="currentSpace.website" class="p-4 bg-gradient-to-r from-blue-900/20 to-blue-800/20 rounded-lg text-sm text-gray-300 border border-blue-400/20 backdrop-blur-sm">
          <div class="flex items-center">
            <Icon name="mdi:web" class="mr-2 h-4 w-4 text-blue-400" />
            <span class="font-medium text-blue-300">Website:</span>
            <span class="ml-2">{{ currentSpace.website }}</span>
          </div>
        </div>
        <div v-else class="p-4 bg-gradient-to-r from-red-900/20 to-red-800/20 rounded-lg text-sm text-red-300 border border-red-400/20 backdrop-blur-sm">
          <p class="flex items-center">
            <Icon name="mdi:alert-circle" class="mr-2 h-4 w-4 text-red-400" />
            Please make sure your website is set before uploading
          </p>
          <a href="/c/dashboard" class="text-blue-400 hover:text-blue-300 underline text-xs mt-2 inline-block transition-colors duration-200">
            Update in Dashboard
          </a>
        </div>

        <div v-if="currentSpace.phone" class="p-4 bg-gradient-to-r from-blue-900/20 to-blue-800/20 rounded-lg text-sm text-gray-300 border border-blue-400/20 backdrop-blur-sm">
          <div class="flex items-center">
            <Icon name="mdi:phone" class="mr-2 h-4 w-4 text-blue-400" />
            <span class="font-medium text-blue-300">Phone:</span>
            <span class="ml-2">{{ currentSpace.phone }}</span>
          </div>
        </div>
        <div v-else class="p-4 bg-gradient-to-r from-red-900/20 to-red-800/20 rounded-lg text-sm text-red-300 border border-red-400/20 backdrop-blur-sm">
          <p class="flex items-center">
            <Icon name="mdi:alert-circle" class="mr-2 h-4 w-4 text-red-400" />
            Please make sure your phone is set before uploading
          </p>
          <a href="/c/dashboard" class="text-blue-400 hover:text-blue-300 underline text-xs mt-2 inline-block transition-colors duration-200">
            Update in Dashboard
          </a>
        </div>
      </div>

      <!-- Submit Button -->
      <div class="mt-8 flex justify-end">
        <button-submit
          :loading="loading"
          :disabled="loading"
          @click="submit"
          class="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-lg transition-all duration-200 font-medium shadow-lg transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
        >
          <Icon name="mdi:content-save" class="mr-2 h-4 w-4" />
          Update Business Card
        </button-submit>
      </div>
    </div>
  </div>
</template>
