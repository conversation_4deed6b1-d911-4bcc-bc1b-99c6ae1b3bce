<script lang="ts" setup>
const typeProp = defineProps({
  item: {
    type: Object,
    default: () => {},
  },
  cols: {
    type: Object,
    default: () => {},
  },
  cantEdit: {
    type: Boolean,
    default: false,
  },
  classed: {
    type: String,
    default: '',
  },
  collection: {
    type: String,
    default: '',
  },
})
const location = useBrowserLocation()

const new_val = ref('')

onMounted(() => {
  new_val.value = typeProp.item[typeProp.cols.val]
})

const pageURL = computed(() => {
    return `${location.value.origin}/o/site/${typeProp.item[typeProp.cols.val]}`
})
</script>

<template>
  <div class="relative">
    <div>
      <a :href="pageURL" target="_blank">{{ pageURL }}</a>
    </div>
  </div>
</template>
