<script lang="ts" setup>
const typeProp = defineProps({
  item: {
    type: Object,
    default: () => {},
  },
  cols: {
    type: Object,
    default: () => {},
  },
  cantEdit: {
    type: Boolean,
    default: false,
  },
  classed: {
    type: String,
    default: '',
  },
  collection: {
    type: String,
    default: '',
  },
})
const edit = ref(false)
const new_val = ref('')

onMounted(() => {
  new_val.value = typeProp.item[typeProp.cols.val]
})

const saveItem = async () => {
  typeProp.item[typeProp.cols.val] = new_val.value
console.log(typeProp.item[typeProp.cols.val], typeProp.collection)
  update(typeProp.item.id, typeProp.collection, {
    [typeProp.cols.val]: typeProp.item[typeProp.cols.val],
  })
}
</script>

<template>
  <div class="relative">
    <div class="flex items-center w-32 text-sm" @click="edit = !edit">
      <p
        v-if="item[cols.val]"
        class="overflow-hidden hover:overflow-auto"
      >
        <input v-model="item[cols.val]" class="o_input" :type="cols.typeText"> ---
      </p>
      <p v-else class="overflow-auto">
        -----
      </p>
    </div>
    <onClickOutside @trigger="edit = false">
      <div v-if="edit" class="absolute flex w-64 h-12 z-150 theme-base_100">
        <div class="flex o_input">
          <input v-model="new_val" class="o_input" :type="cols.typeText"> <button class="z-50 o_btn_icon_square" @click="saveItem">
            <Icon name="mdi:floppy" />
          </button>
        </div>
      </div>
    </onClickOutside>
  </div>
</template>
