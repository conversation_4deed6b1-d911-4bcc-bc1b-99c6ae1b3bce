<script lang="ts" setup>
const typeProp = defineProps({
  item: {
    type: Object,
    default: () => {},
  },
  cols: {
    type: Object,
    default: () => {},
  },
  cantEdit: {
    type: Boolean,
    default: false,
  },
  classed: {
    type: String,
    default: "",
  },
  collection: {
    type: String,
    default: "",
  },
});
const edit = ref(false);
const new_val = ref("");
onMounted(() => {
  new_val.value = typeProp.item[typeProp.cols.val];
});
const notificationOpen = useState<any>("notificationOpen", () => {
  return {
    open: false,
    title: "",
    text: "",
    color: "o_gradient",
    type: "normal",
    icon: "info",
    button_text: "OK",
  };
});
const saveItem = async () => {
  typeProp.item[typeProp.cols.val] = new_val.value;
  await update(typeProp.item.id, typeProp.collection, {
    [typeProp.cols.val]: typeProp.item[typeProp.cols.val],
  });
  notificationOpen.value = {
    open: true,
    title: "Updated",
    text: `${typeProp.collection} updated`,
    color: "o_gradient",
    type: "normal",
    icon: "info",
    button_text: "OK",
  };
};

function imageLoaded(e: any) {
  console.log(typeProp.item[typeProp.cols.val]);
  new_val.value = {
    src: e[0].src,
    name: e[0].name,
    type: e[0].type,
  };
  console.log(typeProp.item[typeProp.cols.val]);

  saveItem();
  edit.value = false;
}
</script>

<template>
  <div class="relative">
    <div class="flex items-center w-32 text-sm" @click="edit = !edit">
      <div v-if="item[cols.val]" class="overflow-hidden hover:overflow-auto">
        <document-type :doc="item[cols.val]" classes="h-16" />
      </div>
      <p v-else class="overflow-auto">-----</p>
    </div>
    <MazBottomSheet v-model="edit">
      <div class="theme_200 h-82">
        <file-manager @input="imageLoaded" />
        <button class="z-50 o_btn_icon_square" @click="edit = false">
          <Icon name="mdi:close" />
        </button>
      </div>
    </MazBottomSheet>
  </div>
</template>
