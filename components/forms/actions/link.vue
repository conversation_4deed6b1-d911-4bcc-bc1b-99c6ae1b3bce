<script lang="ts" setup>
const typeProp = defineProps({
  item: {
    type: Object,
    default: () => {},
  },
  cols: {
    type: Object,
    default: () => {},
  },
  cantEdit: {
    type: Boolean,
    default: false,
  },
  classed: {
    type: String,
    default: '',
  },
  collection: {
    type: String,
    default: '',
  },
})
const { updateToId } = database()
const edit = ref(false)
const new_val = ref('')

onMounted(() => {
  new_val.value = typeProp.item[typeProp.cols.val]
})

const saveItem = async () => {
  typeProp.item[typeProp.cols.val] = new_val.value

  updateToId(typeProp.item.id, typeProp.collection, {
    [typeProp.cols.val]: typeProp.item[typeProp.cols.val],
  })
}
</script>

<template>
  <div class="relative">
    <div class="flex items-center w-32 text-sm">
      <a
        v-if="item[cols.val]" :href="item[cols.val]"
        target="_blank"
        class="hover:overflow-auto overflow-hidden flex justify-center w-full"
      >
        <Icon name="mdi:printer" />
      </a>
      <p v-else class="overflow-auto">
        -----
      </p>
    </div>
    <onClickOutside @trigger="edit = false">
      <div v-if="edit" class="absolute w-64 h-12 flex z-150 theme-base_100">
        <div class="o_input flex">
          <input v-model="new_val" class="o_input" :type="cols.typeText"> <button class="o_btn_icon_square z-50" @click="saveItem">
            <Icon name="mdi:floppy" />
          </button>
        </div>
      </div>
    </onClickOutside>
  </div>
</template>
