<script lang="ts" setup>
const typeProp = defineProps({
  item: {
    type: Object,
    default: () => {},
  },
  cols: {
    type: Object,
    default: () => {},
  },
  cantEdit: {
    type: Boolean,
    default: false,
  },
  classed: {
    type: String,
    default: '',
  },
  collection: {
    type: String,
    default: '',
  },
})
const edit = ref(false)
const new_val = ref('')

const options: any = ref([])
const selected: any = ref([])
const notificationOpen = useState<any>("notificationOpen", () => {
  return {
    open: false,
    title: "",
    text: "",
    color: "o_gradient",
    type: "normal",
    icon: "info",
    button_text: "OK",
  };
});
onMounted(() => {
  new_val.value = typeProp.item[typeProp.cols.val]
  if (typeProp.cols.optionsUrl && typeProp.item[typeProp.cols.val]) {
    // switch (typeProp.cols.optionsUrl) {
    //   case 'categories':
    //     console.log('typeProp.item[typeProp.cols.val]', typeProp.item[typeProp.cols.val])
    //     options.value = categories.value
    //     if(typeProp.item[typeProp.cols.val] && typeProp.item[typeProp.cols.val].length > 0)
    //     selected.value = typeProp.item[typeProp.cols.val].map((item: any) => item.id)
    //     break
    //   case 'attributes':
    //     options.value = attributes.value
    //     if(typeProp.item[typeProp.cols.val] && typeProp.item[typeProp.cols.val].length > 0)

    //     selected.value = typeProp.item[typeProp.cols.val].map((item: any) => item.id)

    //     break
    // }
  }
})

const input = (data: any) => {
  console.log('data', data)
  if (data === undefined || data.isTrusted) return
  new_val.value = data
}
const saveItem = async () => {
  if (typeProp.cols.multi)
    typeProp.item[typeProp.cols.val] = selected.value

  else
    typeProp.item[typeProp.cols.val] = new_val.value


    console.log('type', typeProp.item.id, typeProp.collection, typeProp.cols.val, typeProp.item[typeProp.cols.val])
  if (typeProp.cols.optionsUrl) {
    switch (typeProp.cols.optionsUrl) {
      case 'categories':
        update(typeProp.item.id, typeProp.collection, {
          search_categories: typeProp.item[typeProp.cols.val],
          categories: typeProp.item[typeProp.cols.val].map((item: any) => { id: item }),
        })
        break
      case 'attributes':
        update(typeProp.item.id, typeProp.collection, {
          search_attributes: typeProp.item[typeProp.cols.val],
          attributes: typeProp.item[typeProp.cols.val].map((item: any) => { id: item }),
        })

        break
      default:
        console.log('UDALEJRJL', typeProp.item)
        console.log('UDALEJRJL', typeProp.collection)
        console.log('typeProp.cols.val', typeProp.cols.val)
        console.log('typeProp.ypeProp.item[typeProp.cols.val].val', typeProp.item[typeProp.cols.val])

        update(typeProp.item.id, typeProp.collection, {
          [typeProp.cols.val]: typeProp.item[typeProp.cols.val],
        })

        break
    }
  }
  notificationOpen.value = {
    open: true,
    title: "Updated",
    text: `${typeProp.collection} updated`,
    color: "o_gradient",
    type: "normal",
    icon: "info",
    button_text: "OK",
  };
}

const getName = () => {
  const items_select: any = []
  if (options.value.length > 0 && selected.value.length > 0) {
    for (let i = 0; i < selected.value.length; i++) {
      const here = options.value.filter((flt: { [x: string]: any }) => flt[typeProp.cols.value] === selected.value[i])
      if (here.length > 0)
        items_select.push(here[0][typeProp.cols.display])
    }

    return items_select.join(', ')
  }
  else {
    return '-----'
  }
}
</script>

<template>
  <div class="relative">
    <div class="flex items-center w-32 h-24 overflow-hidden text-sm hover:overflow-auto" @click="edit = !edit">
      <p
        v-if="item[cols.val]"
        class=""
      >
        {{ item[cols.val] }}
        <!-- {{ getName() }} -->
      </p>
      <p v-else class="overflow-auto">
        -----
      </p>
    </div>
    <MazBottomSheet v-model="edit">
      <div class="flex o_input theme_200  mb-12">
        <!-- <input v-model="new_val" class="h-full o_input" :type="cols.typeText" /> -->
        <div class="w-full">
          <div v-if="cols.multi" class="flex justify-between o_input">
          <forms-inputs-select-multi :array="options" :selected="selected" :value="cols.value" :display="cols.display" />
        </div>
        <div v-else class="flex justify-between o_input">
          <forms-inputs-select :schema="cols" @input="input"/>
        </div>
        </div>
        <button class="z-50 o_btn_icon_square" @click="saveItem">
          <Icon name="mdi:floppy" />
        </button>
      </div>
    </MazBottomSheet>
 
  </div>
</template>
