<script lang="ts" setup>
const typeProp = defineProps({
  item: {
    type: Object,
    default: () => {},
  },
  cols: {
    type: Object,
    default: () => {},
  },
  cantEdit: {
    type: Boolean,
    default: false,
  },
  classed: {
    type: String,
    default: '',
  },
  collection: {
    type: String,
    default: '',
  },
})
const { updateToId } = database()
const edit = ref(false)
const new_val = ref('')

onMounted(() => {
  new_val.value = typeProp.item[typeProp.cols.val]
})

const saveItem = async () => {
  typeProp.item[typeProp.cols.val] = new_val.value

  updateToId(typeProp.item.id, typeProp.collection, {
    [typeProp.cols.val]: typeProp.item[typeProp.cols.val],
  })
}

watch(() => new_val.value, () => {
  console.log(new_val.value)
  typeProp.item[typeProp.cols.val] = new_val.value
  saveItem()
})
</script>

<template>
  <div class="relative">
    <!-- {{item[cols.val]}} -->

    <div v-show="!edit" v-if="item[cols.val]" class="flex items-center w-48 overflow-hidden hover:overflow-auto h-28" @click="edit = true">
      <div v-if="Array.isArray(item[cols.val])" class="flex w-48">
        <div v-if="item[cols.val].length > 0">
          <div v-for="(it, index) in item[cols.val]" :key="index">
            <document-type :doc="it" classes="h-24" />
          </div>
        </div>
        <div v-else>
          <p v-show="!edit" class="overflow-auto" @click="edit = true">
            -----
          </p>
        </div>
      </div>
      <div v-else>
        <document-type :doc="item[cols.val]" classes="h-24" />
      </div>
    </div>
    <onClickOutside @trigger="edit = false">
      <div v-if="edit" class="w-1/2 h-1/2 o_modal">
        <file-manager @input="new_val = $event" />
        <!-- <div class="flex o_input">
          <input v-model="new_val" class="o_input" :type="cols.typeText"> <button class="z-50 o_btn_icon_square" @click="saveItem">
            <Icon name="mdi:floppy" />
          </button>
        </div> -->
      </div>
    </onClickOutside>
  </div>
</template>
