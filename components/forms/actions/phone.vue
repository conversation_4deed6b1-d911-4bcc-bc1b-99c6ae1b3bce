<script lang="ts" setup>
const typeProp = defineProps({
  item: {
    type: Object,
    default: () => {},
  },
  cols: {
    type: Object,
    default: () => {},
  },
  cantEdit: {
    type: Boolean,
    default: false,
  },
  classed: {
    type: String,
    default: "",
  },
  collection: {
    type: String,
    default: "",
  },
});
const edit = ref(false);
const new_val = ref("");
const { updateToId } = database();
onMounted(() => {
  new_val.value = typeProp.item[typeProp.cols.val];
});
const notificationOpen = useState<any>("notificationOpen", () => {
  return {
    open: false,
    title: "",
    text: "",
    color: "o_gradient",
    type: "normal",
    icon: "info",
    button_text: "OK",
  };
});
const saveItem = async () => {
  typeProp.item[typeProp.cols.val] = new_val.value;
  await updateToId(typeProp.item.id, typeProp.collection, {
    [typeProp.cols.val]: typeProp.item[typeProp.cols.val],
  });
  notificationOpen.value = {
    open: true,
    title: "Updated",
    text: `${typeProp.collection} updated`,
    color: "o_gradient",
    type: "normal",
    icon: "info",
    button_text: "OK",
  };
};
const input = (val: any) => {
  if(val === undefined || val.isTrusted) return
  new_val.value = val;
};
</script>

<template>
  <div class="relative">
    <div class="flex items-center w-32 text-sm" @click="edit = !edit">
      <p v-if="item[cols.val]" class="overflow-hidden hover:overflow-auto">
        {{ item[cols.val] }}
      </p>
      <p v-else class="overflow-auto">-----</p>
    </div>
    <MazBottomSheet v-model="edit">
      <div class="flex o_input theme_200  mb-12">
        <forms-inputs-phone :schema="{value: cols.typeText}" @input="input"  class="w-full pb-16"/>
        <button class="z-50 o_btn_icon_square" @click="saveItem">
          <Icon name="mdi:floppy" />
        </button>
      </div>
    </MazBottomSheet> 
  </div>
</template>
