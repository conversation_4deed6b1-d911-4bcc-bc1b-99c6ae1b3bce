<script setup lang='ts'>
const propsMod = defineProps({
  item: {
    type: Object,
    default: () => {},
  },
  cols: {
    type: Object,
    default: () => {},
  },
  cantEdit: {
    type: Boolean,
    default: false,
  },
  collection: {
    type: String,
    default: '',
  },
})
const showButton = ref({
  edit: true,
  delete: true,
  select: true,
})
onMounted(() => {
  if (propsMod.cols.see) {
    showButton.value = propsMod.cols.see
    propsMod.item.isSelected = false
  }
})
function updateEditShow(val: any) {
  if (propsMod.cols.cantEdit)
    editShowMod.value = val
}
</script>

<template>
  <div class="w-10">
    <div class="flex justify-around">
      <input
        v-model="item.isSelected"
        type="checkbox"
        class="o_checkbox mr-2"
      >
      <Icon
        v-if="showButton.select"
        name="mdi:location-enter"
        @click="$emit('selectItem', item)"
      />
    </div>
  </div>
</template>
