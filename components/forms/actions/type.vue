<script lang="ts" setup>
const typeProp = defineProps({
  item: {
    type: Object,
    default: () => {},
  },
  cols: {
    type: Object,
    default: () => {},
  },
  cantEdit: {
    type: Boolean,
    default: false,
  },
  classed: {
    type: String,
    default: "",
  },
  collection: {
    type: String,
    default: "",
  },
});
const edit = ref(false);
const new_val = ref("");
onMounted(() => {
  new_val.value = typeProp.item[typeProp.cols.val];
});
const notificationOpen = useState<any>("notificationOpen", () => {
  return {
    open: false,
    title: "",
    text: "",
    color: "o_gradient",
    type: "normal",
    icon: "info",
    button_text: "OK",
  };
});
const saveItem = async () => {
  typeProp.item[typeProp.cols.val] = new_val.value;
  await update(typeProp.item.id, typeProp.collection, {
    [typeProp.cols.val]: typeProp.item[typeProp.cols.val],
  });
  notificationOpen.value = {
    open: true,
    title: "Updated",
    text: `${typeProp.collection} updated`,
    color: "o_gradient",
    type: "normal",
    icon: "info",
    button_text: "OK",
  };
};
</script>

<template>
  <div class="relative">
    <div class="flex items-center w-32 text-sm" @click="edit = !edit">
      <p v-if="item[cols.val]" class="overflow-hidden hover:overflow-auto">
        {{ item[cols.val] }}
      </p>
      <p v-else class="overflow-auto">-----</p>
    </div>
    <MazBottomSheet v-model="edit">
      <div class="flex o_input theme_200">
        <input v-model="new_val" class="h-full o_input" :type="cols.typeText" />
        <button class="z-50 o_btn_icon_square" @click="saveItem">
          <Icon name="mdi:floppy" />
        </button>
      </div>
    </MazBottomSheet>
  </div>
</template>
