<script setup lang="ts">
const { currentSpace } = space()

const quickCreateProp: any = defineProps({
  open: {
    type: Boolean,
    default: () => {
      return false;
    },
  },
  formsItem: {
    type: Object,
    default: () => {
      return {};
    },
  },
  title: {
    type: String,
    default: ''
  }
});

const createEmit: any = getCurrentInstance()

const openModal = ref(false);
const loading = ref(false);

watch(()=> quickCreateProp.open, (val) => {
  openModal.value = val
})
onMounted(() => {
  openModal.value = quickCreateProp.open;
});

const schemaAdd = computed(() => {
    const forms: any = {}
  let formItems = schemaSkin(quickCreateProp.formsItem.data);
  for(let i = 0; i < formItems.length; i++) {
   let area = formItems[i].area
    if(!forms[area]) {
      forms[area] = []
    }
    forms[area].push(formItems[i])
  }
  return forms
});
const formDataAdd = ref({});

function inputAdd(data: any) {
  if (data && data.path === undefined && data.bubbles === undefined){
    formDataAdd.value = {...formDataAdd.value,...data};
  }
}
const notificationOpen = useState<any>('notificationOpen', () => {
  return {
    open: false,
    text: '',
    color: 'o_gradient',
  }
})
const spaceAddress = ref(true)
const center: any = ref([])
const currentLocation: any = ref({})
const submit = async () => {
  let payload = formDataAdd.value

  if(spaceAddress.value){
    payload = {
      ...payload,
      hash: currentSpace.value.hash ? currentSpace.value.hash : '',
	lat: currentSpace.value.lat ? currentSpace.value.lat : '',
	lng: currentSpace.value.lng ? currentSpace.value.lng : '',
	location: currentSpace.value.location ? currentSpace.value.location : '',
	formatted_address: currentSpace.value.formatted_address ? currentSpace.value.formatted_address : '',
	// website: currentSpace.value.website ? currentSpace.value.website : '',
	// email: currentSpace.value.email ? currentSpace.value.email : '',
	// phone: currentSpace.value.phone ? currentClient.value.phone : '',
    }
  }else{
    payload = {
      ...payload,
      hash: currentLocation.value.hash ? currentLocation.value.hash : '',
	lat: currentLocation.value.lat ? currentLocation.value.lat : '',
	lng: currentLocation.value.lng ? currentLocation.value.lng : '',
	location: currentLocation.value.location ? currentLocation.value.location : '',
	formatted_address: currentLocation.value.formatted_address ? currentLocation.value.formatted_address : '',
    }
  }
  loading.value = true;
  let collection = quickCreateProp.formsItem.filters.collection;
  let res: any = await add(collection, {...payload, ...extraInfo()});
  createEmit.emit('created', {...res, ...payload, ...extraInfo()})
  loading.value = false;
  notificationOpen.value.open = true;
  notificationOpen.value.text = 'Added ' + quickCreateProp.title;
  notificationOpen.value.color = 'bg-success';
  openModal.value = false;
};

function input_address(data: { path: undefined; bubbles: undefined; utc_offset: any; lat: any; lng: any; vicinity: any; adr_address: any; formatted_address: string; place_id: any; url: any }) {
  if (data && data.path === undefined && data.bubbles === undefined) {
    currentLocation.value.utc_offset = data.utc_offset
    currentLocation.value.lat = data.lat
    currentLocation.value.lng = data.lng
    currentLocation.value.vicinity = data.vicinity
    currentLocation.value.adr_address = data.adr_address
    currentLocation.value.formatted_address = data.formatted_address
    currentLocation.value.place_id = data.place_id
    currentLocation.value.adr_url = data.url
    const split_address = data.formatted_address.split(',')
    currentLocation.value.address = split_address[0]
    currentLocation.value.city = split_address[1]
    currentLocation.value.state = split_address[2]
    currentLocation.value.zip = split_address[3]
    currentLocation.value.country = split_address[4]
    center.value = [data.lat, data.lng];
    currentLocation.value.hash = createHash(center.value)

  }
}
</script>

<template>
  <modal-small
    :open="openModal"
    :title="title"
    buttontext="Submit"
    :showButtons="true"
    @submit="submit"
  >
    <template #body>
      <div v-if="loading">
        <loading-colorrockets />
      </div>
      <div v-else>
        <div v-if="formsItem.filters.areas">
          <div v-for="(area, index) in formsItem.filters.areas" :key="index" class="mb-4">
            <label class="o_label">{{ area }}</label>
          <forms-generator :schema="schemaAdd[index + 1]" @input="inputAdd" />
          <input v-model="spaceAddress" type="checkbox"/>
          <label class="ml-1 text-sm">Use Accounts address</label>
          <div v-if="!spaceAddress">
            <google-autocomplete @input="input_address" label="Update location" classes="o_input_small"/>
          </div>
          </div>
        </div>
      </div>
    </template>
  </modal-small>
</template>
