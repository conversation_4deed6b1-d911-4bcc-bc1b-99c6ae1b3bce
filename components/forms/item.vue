<script setup lang="ts">
const { omniCreate: omniCreateFormss, omniCreateFormTypeTexts, omniCreateFormComponents, omniCreateFormItem, omniCreateForms, omniCreateForm, omniCreateFormInputs } = useOmni()
watch(() => omniCreateFormItem.value.label, (newVal: any) => {
  if (newVal)
    omniCreateFormItem.value.val = newVal.toLowerCase().replace(/ /g, '_')
})

watch(() => omniCreateForm.value.title, (newVal: string) => {
  if (newVal)
    omniCreateForm.value.filters.collection = `${omniCreateFormss.value.title.toLowerCase().replace(/ /g, '_')}`
})

// watch(() => omniCreateFormItem.value.input, (newVal: any) => {
//   if (newVal) {
//     if (newVal === 'FormsInputsType') {
//       omniCreateFormItem.value.val_type = 'string'
//     }
//     else if (newVal === 'FormsInputsCheckbox') {
//       omniCreateFormItem.value.val_type = 'bool'
//     }
//     else if (newVal === 'FormsInputsColour') {
//       omniCreateFormItem.value.val_type = 'string'
//     }
//     else if (newVal === 'FormsInputsColourSimple') {
//       omniCreateFormItem.value.val_type = 'string'
//     }
//     else if (newVal === 'FormsInputsQuill') {
//       omniCreateFormItem.value.val_type = 'string'
//     }
//     else if (newVal === 'FormsInputsRadio') {
//       omniCreateFormItem.value.val_type = 'bool'
//     }
//     else if (newVal === 'FormsInputsSelect') {
//       if (omniCreateFormItem.value.multi)
//         omniCreateFormItem.value.val_type = 'string[]'

//       else
//         omniCreateFormItem.value.val_type = 'string'
//     }
//     else if (newVal === 'FormsInputsSignature') {
//       omniCreateFormItem.value.val_type = 'string'
//     }
//     else if (newVal === 'FormsInputsSlider') {
//       omniCreateFormItem.value.val_type = 'int32'
//     }
//     else if (newVal === 'FormsInputsSwitch') {
//       omniCreateFormItem.value.val_type = 'bool'
//     }
//     else if (newVal === 'FormsInputsTextarea') {
//       omniCreateFormItem.value.val_type = 'string'
//     }
//     else if (newVal === 'FormsInputsUpload') {
//       omniCreateFormItem.value.val_type = 'string'
//     }
//     else if (newVal === 'FormsInputsUploadTypes') {
//       omniCreateFormItem.value.val_type = 'string'
//     }
//   }
// })

// watch(() => omniCreateFormItem.value.multi, (newVal: any) => {
//   if (newVal) {
//     if (newVal)
//       omniCreateFormItem.value.val_type = 'string[]'

//     else
//       omniCreateFormItem.value.val_type = 'string'
//   }
// })

// watch(() => omniCreateFormItem.value.typeText, (newVal: any) => {
//   if (newVal) {
//     if (newVal === 'text')
//       omniCreateFormItem.value.val_type = 'string'

//     else if (newVal === 'number')
//       omniCreateFormItem.value.val_type = 'int32'

//     else if (newVal === 'date')
//       omniCreateFormItem.value.val_type = 'int32'

//     else if (newVal === 'time')
//       omniCreateFormItem.value.val_type = 'int32'

//     else if (newVal === 'datetime')
//       omniCreateFormItem.value.val_type = 'int32'

//     else if (newVal === 'checkbox')
//       omniCreateFormItem.value.val_type = 'bool'

//     else if (newVal === 'radio')
//       omniCreateFormItem.value.val_type = 'bool'

//     else if (newVal === 'select')
//       omniCreateFormItem.value.val_type = 'string[]'

//     else if (newVal === 'textarea')
//       omniCreateFormItem.value.val_type = 'string'

//     else if (newVal === 'file')
//       omniCreateFormItem.value.val_type = 'string'

//     else if (newVal === 'password')
//       omniCreateFormItem.value.val_type = 'string'

//     else if (newVal === 'email')
//       omniCreateFormItem.value.val_type = 'string'

//     else if (newVal === 'url')
//       omniCreateFormItem.value.val_type = 'string'

//     else if (newVal === 'tel')
//       omniCreateFormItem.value.val_type = 'string'

//     else if (newVal === 'search')
//       omniCreateFormItem.value.val_type = 'string'

//     else if (newVal === 'color')
//       omniCreateFormItem.value.val_type = 'string'

//     else if (newVal === 'range')
//       omniCreateFormItem.value.val_type = 'string'

//     else if (newVal === 'hidden')
//       omniCreateFormItem.value.val_type = 'string'

//     else if (newVal === 'button')
//       omniCreateFormItem.value.val_type = 'string'

//     else if (newVal === 'reset')
//       omniCreateFormItem.value.val_type = 'string'

//     else if (newVal === 'submit')
//       omniCreateFormItem.value.val_type = 'string'
//   }
// })
const curr: any = getCurrentInstance()
function addOmniCreateFormItems() {
  const payload = { ...omniCreateFormItem.value }
  curr.emit('addOmniCreateFormItems', payload)
  omniCreateFormItem.value = {
      label: "",
      val: "",
      value: "",
      class: "w-2/5",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: '',
        options: [],
      component: "FormsInputsType",
      action: "forms-actions-type"
    }
}
</script>

<template>
  <div>
    <div class="w-full form-control">
      <label class="o_label"> Component </label>
      <select class="o_input"   v-model="omniCreateFormItem.component">
        <option :value="component.input" v-for="component in omniCreateFormInputs" :key="component">
          {{ component.input }}
          </option>
      </select>
    </div>
    <div
      v-if="omniCreateFormItem.component === 'FormsInputsType'"
      class="w-full form-control"
    >
      <label class="o_label"> Text Type </label>
      <select class="o_input"   v-model="omniCreateFormItem.type">
        <option :value="component" v-for="component in omniCreateFormTypeTexts" :key="component">
          {{ component }}
          </option>
      </select>
    </div>
    <div class="grid grid-cols-2 gap-2">
      <div class="relative">
        <label class="o_label">Label</label>
        <input
          id="omni-create-Label"
          v-model="omniCreateFormItem.label"
          type="text"
          class="o_input"
          placeholder="Label"
        >
      </div>
      <div class="relative">
        <label class="o_label">Value</label>
        <input
          id="omni-create-Value"
          v-model="omniCreateFormItem.val"
          type="text"
          class="o_input"
          placeholder="Value"
        >
      </div>
      </div>
      <div class="grid grid-cols-3 gap-2">
      <div class="relative">
        <label class="o_label">Class</label>
        <input
          id="omni-create-Label"
          v-model="omniCreateFormItem.class"
          type="text"
          class="o_input"
          placeholder="value"
        >
      </div>
      <div class="relative">
        <label class="o_label">Class Input</label>
        <input
          id="omni-create-Value"
          v-model="omniCreateFormItem.class_input"
          type="text"
          class="o_input"
          placeholder="Value"
        >
      </div>
      <div class="relative">
        <label class="o_label">Class Label</label>
        <input
          id="omni-create-Value"
          v-model="omniCreateFormItem.class_label"
          type="text"
          class="o_input"
          placeholder="Value"
        >
      </div>
    </div>
    <div class="relative">
        <label class="o_label">Options Url</label>
        <input
          id="omni-create-Value"
          v-model="omniCreateFormItem.optionsUrl"
          type="text"
          class="o_input"
          placeholder="Value"
        >
      </div>
      <div class="relative">
        <label class="o_label">Options</label>
      <button @click="omniCreateFormItem.options.push({label: '', value: ''})">Add</button>
            <div class="">
                <div v-for="(opt, ind) in omniCreateFormItem.options" :key="ind">
                  <div class="grid grid-cols-2 gap-2">
                    <label class="o_input_small">Label</label>
                    <input type="text" v-model="opt.label" class="o_input_small">
                    <label class="o_input_small">Value</label>
                    <input type="text" v-model="opt.value" class="o_input_small">
                  </div>
                
                </div>
            </div>
          </div>


    <div class="relative">
      <label class="o_label">Area</label>
      <input
        id="omni-create-Type"
        v-model="omniCreateFormItem.area"
        type="number"
        class="o_input"
        placeholder="Type"
      >
    </div>
    <div
      v-if="omniCreateFormItem.input === 'FormsInputsSlider'"
      class="relative"
    >
      <label class="o_label">Max</label>
      <input
        id="omni-create-Max"
        v-model="omniCreateFormItem.max"
        type="number"
        class="o_input"
        placeholder="Max"
      >
    </div>
    <div
      v-if="omniCreateFormItem.input === 'FormsInputsSlider'"
      class="relative"
    >
      <label class="o_label">Min</label>
      <input
        id="omni-create-Min"
        v-model="omniCreateFormItem.min"
        type="number"
        class="o_input"
        placeholder="Min"
      >
    </div>
    <div class="w-full form-control">
      <label class="o_label">Actions </label>
      <select class="o_input"   v-model="omniCreateFormItem.action">
        <option v-for="component in omniCreateFormComponents" :key="component">
          {{ component }}
          </option>
      </select>
      <!-- <v-select
        v-model="omniCreateFormItem.component"
        class="w-full rounded style-chooser select-bordered"
        label="input"
        :options="omniCreateFormComponents"
        :reduce="(item) => item"
      /> -->
    </div>
  
    <div class="grid grid-cols-3 my-2">
        <div
          class="flex items-center space-x-1"
        >
          <input
            id="table"
            v-model="omniCreateFormItem.table"
            type="checkbox"
            name="toggle"
            class="o_checkbox"
          >
        
        <span class="font-medium text-gray-400"> Not in Table </span>
      </div>
      <div
          class="flex items-center space-x-1"
        >
          <input
            id="table"
            v-model="omniCreateFormItem.form"
            type="checkbox"
            name="toggle"
            class="o_checkbox"
          >
        
        <span class="font-medium text-gray-400"> Not in Form </span>
      </div>
      <div
          class="flex items-center space-x-1"
        >
          <input
            id="table"
            v-model="omniCreateFormItem.small_table"
            type="checkbox"
            name="toggle"
            class="o_checkbox"
          >
        
        <span class="font-medium text-gray-400"> Not in Small Table </span>
      </div>
      <div
          class="flex items-center space-x-1"
        >
          <input
            id="table"
            v-model="omniCreateFormItem.small_form"
            type="checkbox"
            name="toggle"
            class="o_checkbox"
          >
        
        <span class="font-medium text-gray-400"> Not in Small Form </span>
      </div>
      <div
          class="flex items-center space-x-1"
        >
          <input
            id="table"
            v-model="omniCreateFormItem.editable"
            type="checkbox"
            name="toggle"
            class="o_checkbox"
          >
        
        <span class="font-medium text-gray-400"> Editable </span>
      </div>
      <div
          class="flex items-center space-x-1"
        >
          <input
            id="table"
            v-model="omniCreateFormItem.required"
            type="checkbox"
            name="toggle"
            class="o_checkbox"
          >
        
        <span class="font-medium text-gray-400"> Required </span>
      </div>
      <div
          class="flex items-center space-x-1"
        >
          <input
            id="table"
            v-model="omniCreateFormItem.readonly"
            type="checkbox"
            name="toggle"
            class="o_checkbox"
          >
        
        <span class="font-medium text-gray-400"> Read Only </span>
      </div>
      <div
          class="flex items-center space-x-1"
        >
          <input
            id="table"
            v-model="omniCreateFormItem.unique"
            type="checkbox"
            name="toggle"
            class="o_checkbox"
          >
        
        <span class="font-medium text-gray-400"> Unique </span>
      </div>
      <div
          class="flex items-center space-x-1"
        >
          <input
            id="table"
            v-model="omniCreateFormItem.hidden"
            type="checkbox"
            name="toggle"
            class="o_checkbox"
          >
        
        <span class="font-medium text-gray-400"> Hidden </span>
      </div>
      <div
          class="flex items-center space-x-1"
        >
          <input
            id="table"
            v-model="omniCreateFormItem.disabled"
            type="checkbox"
            name="toggle"
            class="o_checkbox"
          >
        
        <span class="font-medium text-gray-400"> disabled </span>
      </div>
      <div
          class="flex items-center space-x-1"
        >
          <input
            id="table"
            v-model="omniCreateFormItem.quick"
            type="checkbox"
            name="toggle"
            class="o_checkbox"
          >
        
        <span class="font-medium text-gray-400"> Quick </span>
      </div>
      <div
          class="flex items-center space-x-1"
        >
          <input
            id="notInTable"
            v-model="omniCreateFormItem.multi"
            type="checkbox"
            name="toggle"
            class="o_checkbox"
          >
        
        <span class="font-medium text-gray-400"> Multi </span>
      </div>
    </div>
    <button class="w-full mt-2 btn-secondary" @click="addOmniCreateFormItems">
      Add
    </button>
  </div>
</template>
