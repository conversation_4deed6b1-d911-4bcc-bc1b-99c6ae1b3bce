<script setup lang="ts">
const inputsTypeProps = defineProps({
    schema: {
    type: Object,
    default: () => {return {}},
  },
})
const inputsCheckboxInstance: any = getCurrentInstance()
const valCheckbox: any = ref(false)

watch(() => valCheckbox.value, (newVal: boolean) => {
  if (newVal)
    inputsCheckboxInstance.emit('input', newVal)
})
onMounted(() => {
  if (inputsTypeProps.schema.value)
    valCheckbox.value = inputsTypeProps.schema.value
})
</script>

<template>
  <div class="flex items-center">
    <input v-model="valCheckbox" type="checkbox" class="o_checkbox">
    <p class="ml-2">
      {{ schema.label }}
    </p>
  </div>
</template>
