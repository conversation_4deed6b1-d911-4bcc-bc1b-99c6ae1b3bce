<script setup lang="ts">
const inputsTypeProps = defineProps({
  placeholder: {
    type: String,
    default: '',
  },
  label: {
    type: String,
    default: '',
  },
  name: {
    type: String,
    default: '',
  },
  typeText: {
    type: String,
    default: '',
  },
  classes: {
    type: String,
    default: 'o_input',
  },
  value: {
    type: String,
    default: '',
  },
})

const valType: any = ref('')
const inputFormsType: any = ref({
  title: 'InputsTextInput',
  placeholder: inputsTypeProps.placeholder,
  label: inputsTypeProps.label,
  name: inputsTypeProps.name,
  val: inputsTypeProps.value,
})
const inputsTypeInstance: any = getCurrentInstance()
const location = useBrowserLocation()
watch(() => valType.value, (newVal: any) => {
  if (newVal)
    inputsTypeInstance.emit('input', newVal)
})
onMounted(() => {
  if (inputsTypeProps.value)
    valType.value = inputsTypeProps.value
})
const pageURL = computed(() => {
  if (valType.value)
    return `${location.value.origin}/o/site/${valType.value}`
})
</script>

<template>
  <div>
    <div>
      <a :href="pageURL" target="_blank">{{ pageURL }}</a>
    </div>
  </div>
</template>

<style>
.input {
  width: 100% !important;
}
</style>
