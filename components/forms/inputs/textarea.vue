<script setup lang="ts">
import { getCurrentInstance, onMounted, ref, watch } from 'vue';

const inputsTypeProps = defineProps({
  schema: {
    type: Object,
    default: () => {
      return {};
    },
  },
  error: {
    type: String,
    default: '',
  },
});

const valType: any = ref('');
const inputFormsType: any = ref({
  title: 'InputsTextInput',
  placeholder: inputsTypeProps.schema.placeholder,
  label: inputsTypeProps.schema.label,
  name: inputsTypeProps.schema.name,
  val: inputsTypeProps.schema.value,
});
const inputsTypeInstance: any = getCurrentInstance();
const typeEmit: any = getCurrentInstance();
const errors = ref<string[]>([]);
const isDirty = ref(false);
const isTouched = ref(false);

watch(
  () => valType.value,
  (newVal: any) => {
    if (newVal) {
      isDirty.value = true;
      inputsTypeInstance.emit('input', newVal);
      validateInput();
    }
  }
);

onMounted(() => {
  if (inputsTypeProps.schema.value) valType.value = inputsTypeProps.schema.value;
});

const onBlur = () => {
  isTouched.value = true;
  validateInput();
};

const validateInput = () => {
  errors.value = [];

  // Check if required
  if (inputsTypeProps.schema.required && !valType.value) {
    errors.value.push(`${inputsTypeProps.schema.label} is required`);
    typeEmit.emit('validation', { valid: false, error: errors.value[0] });
    return false;
  }

  // Check min length
  if (
    inputsTypeProps.schema.minLength &&
    valType.value &&
    valType.value.length < inputsTypeProps.schema.minLength
  ) {
    errors.value.push(
      `${inputsTypeProps.schema.label} must be at least ${inputsTypeProps.schema.minLength} characters`
    );
    typeEmit.emit('validation', { valid: false, error: errors.value[0] });
    return false;
  }

  // Check max length
  if (
    inputsTypeProps.schema.maxLength &&
    valType.value &&
    valType.value.length > inputsTypeProps.schema.maxLength
  ) {
    errors.value.push(
      `${inputsTypeProps.schema.label} must be at most ${inputsTypeProps.schema.maxLength} characters`
    );
    typeEmit.emit('validation', { valid: false, error: errors.value[0] });
    return false;
  }

  // If no errors, emit validation success
  typeEmit.emit('validation', { valid: true, error: '' });
  return true;
};
</script>

<template>
  <div>
    <label :class="schema.class_label">
      {{ schema.label }}
      <span v-if="schema.required" class="text-red-500">*</span>
    </label>
    <div class="relative">
      <textarea
        v-model="valType"
        :placeholder="schema.placeholder"
        class="w-full"
        :class="[
          schema.class_input,
          errors.length > 0 || error
            ? 'border-red-500 focus:border-red-500 focus:ring-red-500'
            : '',
          isDirty && !errors.length && !error
            ? 'border-green-500 focus:border-green-500 focus:ring-green-500'
            : '',
        ]"
        :name="schema.val"
        rows="4"
        @input="typeEmit.emit('input', valType, inputFormsType)"
        @blur="onBlur"
      ></textarea>

      <!-- Validation state icons -->
      <div v-if="isTouched || isDirty" class="absolute top-0 right-0 mt-2 mr-2 pointer-events-none">
        <Icon
          v-if="errors.length > 0 || error"
          name="mdi:alert-circle"
          class="h-5 w-5 text-red-500"
        />
        <Icon v-else-if="valType" name="mdi:check-circle" class="h-5 w-5 text-green-500" />
      </div>
    </div>

    <!-- Error message -->
    <div v-if="errors.length > 0 || error" class="mt-1 text-sm text-red-600">
      {{ error || errors[0] }}
    </div>

    <!-- Character count -->
    <div v-if="schema.maxLength && valType" class="mt-1 text-xs text-gray-500 text-right">
      {{ valType.length }} / {{ schema.maxLength }}
    </div>
  </div>
</template>
