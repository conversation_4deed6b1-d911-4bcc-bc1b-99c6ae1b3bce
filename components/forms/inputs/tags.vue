<script setup lang="ts">
let selectTagProps: any = defineProps({
  type: {
    type: String,
    default: "selector",
  },
  tags: {
    type: Array,
    default: () => [],
  },
  title: {
    type: String,
    default: "Tag Select",
  },
  showAdd: {
    type: Boolean,
    default: false,
  },
  schema: {
    type: Object,
    default: () => {return {}},
  },
});

let tgs = ref('')
onMounted(() => {
  if (process.client) {
    if (selectTagProps.tags.length > 0) {
      tgs.value = selectTagProps.tags.toString()
    }
  if (selectTagProps.schema.value) {
    tgs.value = selectTagProps.schema.value.toString()

  }
  }
});

watch(()=> selectTagProps.tags, ()=> {
  if (process.client) {
    if (selectTagProps.tags.length > 0) {
      tgs.value = selectTagProps.tags.toString()
    } 
  }
})

const tagEmit: any = getCurrentInstance()

watch(()=> tgs.value, ()=> {
  let new_arr = tgs.value.split(',')
  tagEmit.emit('input', new_arr)
})
const addTag = ref(false);
</script>

<template>
  <div>
    <label :class="schema.class_label">
        {{ schema.label }} 
      </label>
  <input v-model="tgs" class="o_input" />
  </div>
  
</template>