<script setup lang="ts">
import ImageUploader from 'quill-image-uploader';
import MagicUrl from 'quill-magic-url';
import QuillBetterTable from 'quill-better-table';
import htmlEditButton from "quill-html-edit-button";
import "quill-mention";

// Import custom CSS for Quill
import '~/assets/css/quill-custom.css';

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  theme: {
    type: String,
    default: 'snow', // 'snow' or 'bubble'
    validator: (value: string) => ['snow', 'bubble'].includes(value)
  },
  required: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: ''
  },
  hint: {
    type: String,
    default: ''
  },
  minHeight: {
    type: String,
    default: '200px'
  },
  maxHeight: {
    type: String,
    default: '400px'
  },
  id: {
    type: String,
    default: () => `quill-${Math.random().toString(36).substring(2, 9)}`
  },
  // For backward compatibility
  schema: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

const emit = defineEmits(['update:modelValue', 'input', 'focus', 'blur', 'ready']);

const content = ref(props.modelValue || (props.schema?.placeholder || ''));
const editorRef = ref(null);
const isFocused = ref(false);
const editorOption = ref({
  theme: props.theme || props.schema?.editorOptions?.theme || 'snow',
});

// Define toolbar options
const toolbarQuill = ref([
  ['bold', 'italic', 'underline', 'strike'], // toggled buttons
  ['blockquote', 'code-block'],
  [{ header: 1 }, { header: 2 }], // custom button values
  [{ list: 'ordered' }, { list: 'bullet' }],
  [{ script: 'sub' }, { script: 'super' }], // superscript/subscript
  [{ indent: '-1' }, { indent: '+1' }], // outdent/indent
  [{ direction: 'rtl' }], // text direction
  ['image'],
  ['link', 'video'],
  [{ size: ['small', false, 'large', 'huge'] }], // custom dropdown
  [{ header: [1, 2, 3, 4, 5, 6, false] }],
  [{ color: [] }, { background: [] }], // dropdown with defaults from theme
  [{ font: [] }],
  [{ align: [] }],
  ['clean'],
]);

// Process content to add Tailwind classes to lists
const processedContent = computed(() => {
  let processed = content.value;
  if (processed) {
    processed = processed.replace(/<ul>/g, '<ul class="list-disc pl-5 my-2">');
    processed = processed.replace(/<ol>/g, '<ol class="list-decimal pl-5 my-2">');
  }
  return processed;
});

// Watch for changes in modelValue prop
watch(() => props.modelValue, (newValue) => {
  if (newValue !== processedContent.value) {
    content.value = newValue;
  }
});

// Watch for changes in schema.placeholder (backward compatibility)
watch(() => props.schema?.placeholder, (newValue) => {
  if (newValue && !content.value) {
    content.value = newValue;
  }
});

// Watch for changes in content
watch(() => content.value, () => {
  updateModelValue();
});

// Watch for changes in processedContent
watch(() => processedContent.value, () => {
  updateModelValue();
});

// Initialize editor on mount
onMounted(() => {
  // Set initial content from props or schema
  if (props.modelValue) {
    content.value = props.modelValue;
  } else if (props.schema?.value) {
    content.value = props.schema.value;
  } else if (props.schema?.placeholder) {
    content.value = props.schema.placeholder;
  }

  // Set editor options from props or schema
  if (props.schema?.editorOptions) {
    editorOption.value = props.schema.editorOptions;
  }
});

// Update model value and emit input event
function updateModelValue() {
  emit('update:modelValue', processedContent.value);
  emit('input', processedContent.value, props.schema?.selectedIndex);
}

// Handle focus event
function onFocus() {
  isFocused.value = true;
  emit('focus');
}

// Handle blur event
function onBlur() {
  isFocused.value = false;
  emit('blur');
}

// Handle editor ready event
function onReady(editor) {
  editorRef.value = editor;
  emit('ready', editor);
}

// Define Quill modules
const modules = [
  {
    name: 'quill-magic-url',
    module: MagicUrl,
  },
  {
    name: 'htmlEditButton',
    module: htmlEditButton,
  },
  {
    name: 'imageUploader',
    module: ImageUploader,
    options: {
      upload: file => {
        return new Promise((resolve, reject) => {
          const formData = new FormData();
          formData.append("image", file);
          // Implement image upload functionality here
          // For now, we'll just reject with a message
          reject("Image upload not implemented");
        });
      }
    }
  }
];

// Computed properties for labels and IDs
const inputId = computed(() => props.id);
const errorId = computed(() => `${inputId.value}-error`);
const hintId = computed(() => `${inputId.value}-hint`);
const labelText = computed(() => props.label || props.schema?.label || '');
const isRequired = computed(() => props.required || props.schema?.required || false);
const errorMessage = computed(() => props.error || '');
const hintMessage = computed(() => props.hint || '');
</script>

<template>
  <div class="w-full">
    <!-- Label -->
    <label
      v-if="labelText"
      :for="inputId"
      :class="[
        schema?.class_label || 'block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1'
      ]"
    >
      {{ labelText }}
      <span v-if="isRequired" class="text-red-500">*</span>
    </label>

    <!-- Editor wrapper -->
    <div
      class="quill-editor-wrapper"
      :class="{
        'focused': isFocused,
        'has-error': errorMessage
      }"
    >
      <client-only>
        <quill-editor
          v-model:content="content"
          :theme="editorOption.theme"
          content-type="html"
          :toolbar="toolbarQuill"
          :modules="modules"
          :readonly="readonly || disabled"
          :disabled="disabled"
          :aria-required="isRequired"
          :aria-invalid="!!errorMessage"
          :aria-describedby="errorMessage ? errorId : hintMessage ? hintId : undefined"
          :style="{
            minHeight: minHeight,
            maxHeight: maxHeight
          }"
          class="quill-editor"
          @focus="onFocus"
          @blur="onBlur"
          @ready="onReady"
        />
      </client-only>
    </div>

    <!-- Error message -->
    <p v-if="errorMessage" :id="errorId" class="mt-1 text-sm text-red-600 dark:text-red-400">
      {{ errorMessage }}
    </p>

    <!-- Hint message -->
    <p v-else-if="hintMessage" :id="hintId" class="mt-1 text-sm text-gray-500 dark:text-gray-400">
      {{ hintMessage }}
    </p>
  </div>
</template>

<style scoped>
/* Scoped styles for the component */
.quill-editor-wrapper {
  position: relative;
  transition: all 0.2s ease;
}

.quill-editor-wrapper.has-error :deep(.ql-toolbar.ql-snow) {
  border-color: #ef4444 !important;
}

.quill-editor-wrapper.has-error :deep(.ql-container.ql-snow),
.quill-editor-wrapper.has-error :deep(.ql-container.ql-bubble) {
  border-color: #ef4444 !important;
}

/* Disabled state */
.quill-editor-wrapper :deep(.ql-disabled) {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: #f3f4f6;
}

.dark .quill-editor-wrapper :deep(.ql-disabled) {
  background-color: #374151;
}
</style>
