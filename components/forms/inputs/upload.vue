<script setup lang="ts">
const inputsUploadsProps = defineProps({
  schema: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

const imgUpload: any = ref([]);
const imgUploadSingle: any = ref({});

onMounted(() => {
  if (inputsUploadsProps.schema.value) {
    if (inputsUploadsProps.schema.multi) {
      imgUpload.value = inputsUploadsProps.schema.value;
    } else {
      imgUploadSingle.value = inputsUploadsProps.schema.value;
    }
  }
});
const currentInstanceUploadProps: any = getCurrentInstance();

function emitChanges(data: any[]) {
  console.log("data", data);
  if (inputsUploadsProps.schema.multi) {
    imgUpload.value.push(data);
    currentInstanceUploadProps.emit("input", imgUpload.value);
  } else {
    imgUploadSingle.value = data;
    currentInstanceUploadProps.emit("input", data);
  }
}
const showAddItems = ref(false);
</script>

<template>
  <div class="w-full">
    <label :class="schema.class_label" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
      {{ schema.label }}
      <span v-if="schema.required" class="text-red-500">*</span>
    </label>

    <div class="border border-gray-300 dark:border-gray-700 rounded-md p-4 bg-gray-50 dark:bg-gray-800">
      <div class="mb-3">
        <file-button
          @uploadAdd="emitChanges"
          @close="showAddItems = false"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 flex items-center"
        >
          <Icon name="mdi:upload" class="mr-2" />
          Upload {{ schema.multi ? 'Files' : 'File' }}
        </file-button>
      </div>

      <div>
        <div v-if="schema.multi" class="grid grid-cols-2 md:grid-cols-3 gap-3">
          <div v-if="imgUpload.length > 0">
            <div v-for="(img, ind) in imgUpload" :key="ind" class="relative">
              <div class="bg-white dark:bg-gray-700 rounded-lg overflow-hidden shadow-sm border border-gray-200 dark:border-gray-600">
                <div class="flex items-center justify-center h-32 p-2">
                  <document-type :doc="img" classes="max-w-full max-h-full object-contain" />
                </div>
                <div class="p-2 bg-gray-100 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
                  <p class="text-xs truncate">{{ img.name || 'Uploaded file' }}</p>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="text-sm text-gray-500 dark:text-gray-400">
            No files uploaded yet
          </div>
        </div>
        <div v-else>
          <div v-if="imgUploadSingle && imgUploadSingle.src" class="relative">
            <div class="bg-white dark:bg-gray-700 rounded-lg overflow-hidden shadow-sm border border-gray-200 dark:border-gray-600">
              <div class="flex items-center justify-center h-40 p-2">
                <document-type :doc="imgUploadSingle" classes="max-w-full max-h-full object-contain" />
              </div>
              <div class="p-2 bg-gray-100 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
                <p class="text-xs truncate">{{ imgUploadSingle.name || 'Uploaded file' }}</p>
              </div>
            </div>
          </div>
          <div v-else class="text-sm text-gray-500 dark:text-gray-400 p-2">
            No file uploaded yet
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

