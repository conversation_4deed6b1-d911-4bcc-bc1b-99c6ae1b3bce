<script setup lang="ts">
const inputsTypeProps = defineProps({
    schema: {
    type: Object,
    default: () => {return {}},
  },
})

const valType: any = ref([])
const inputFormsType: any = ref({
    title: 'InputsTextInput',
    placeholder: inputsTypeProps.schema.placeholder,
    label: inputsTypeProps.schema.label,
    name: inputsTypeProps.schema.name,
    val: [],
})
const inputsTypeInstance: any = getCurrentInstance()

watch(() => valType.value, (newVal: any) => {
    if (newVal)
        inputsTypeInstance.emit('input', newVal)
})
onMounted(() => {
    if (inputsTypeProps.schema.value)
        valType.value = inputsTypeProps.schema.value
})

const userinvite = (us: { id: any; }) => {
    console.log(us)
    //see if in array, if in array then delete, if not in array then add
    if(us.id === undefined || us.id === null) return
    if (valType.value.includes(us.id)) {
        valType.value = valType.value.filter((item: any) => item !== us.id)
    } else {
        valType.value.push(us.id)
    }
    console.log(valType.value)
    inputsTypeInstance.emit('input', valType.value)
}
const schemas: any = ref({
    options: {
        input_class: { value: 'o_input' },
        search_button_class: { value: 'o_btn_icon_square' },
        icon: { value: 'mdi:magnify' },
        index: 'omni',
        vector: 'client'
    }
})
</script>

<template>
    <div>
    
            <label :class="schema.class_label">
        {{ schema.label }}
      </label>
          
            <user-search :schema="schemas" @input="userinvite" />
            <user-groups :users="valType" />
    
    </div>
</template>

<style>
.input {
    width: 100% !important;
}
</style>
