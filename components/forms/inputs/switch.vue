<script>
export default {
  name: 'InputsSwitch',
  props: {
    label: {
      type: String,
      default: '',
    },
    value: {
      type: String,
      default: '',
    },
    classes: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      val: true,
    }
  },
  watch: {
    val(val) {
      this.$emit('input', val)
    },
  },
  created() {
    if (this.value)
      this.val = this.value
  },
}
</script>

<template>
  <div>
    <div>
      <div class="form-control">
        <label class="cursor-pointer flex items-center">
          <input v-model="val" type="checkbox" checked="checked" class="toggle" :class="classes">
          <span class="label-text ml-2">{{ label }}</span>

        </label>
      </div>
    </div>
  </div>
</template>
