<script setup lang="ts">
import { parsePhoneNumberFromString } from 'libphonenumber-js';

const inputsTypeProps = defineProps({
    schema: {
      type: Object,
      default: () => {return {}},
    },
    error: {
      type: String,
      default: '',
    }
})

const valType: any = ref('')
const inputFormsType: any = ref({
  title: 'InputsTextInput',
  placeholder: inputsTypeProps.schema.placeholder,
  label: inputsTypeProps.schema.label,
  name: inputsTypeProps.schema.name,
  val: inputsTypeProps.schema.value,
})
const inputsTypeInstance: any = getCurrentInstance()
const typeEmit: any = getCurrentInstance()
const phoneNumber = ref()
const results = ref()
const errors = ref<string[]>([])
const isDirty = ref(false)
const isTouched = ref(false)

const emit = defineEmits(['input', 'validation'])

watch(() => valType.value, (newVal: any) => {
  if (newVal) {
    isDirty.value = true
    inputsTypeInstance.emit('input', newVal)
    validatePhone()
  }
})

const onBlur = () => {
  isTouched.value = true
  validatePhone()
}

const validatePhone = () => {
  errors.value = []

  // Check if required
  if (inputsTypeProps.schema.required && !valType.value) {
    errors.value.push(`${inputsTypeProps.schema.label} is required`)
    emit('validation', { valid: false, error: errors.value[0] })
    return false
  }

  // Check phone format
  if (valType.value) {
    try {
      const phoneNumber = parsePhoneNumberFromString(valType.value)
      if (!phoneNumber || !phoneNumber.isValid()) {
        errors.value.push('Please enter a valid phone number')
        emit('validation', { valid: false, error: errors.value[0] })
        return false
      }
    } catch (e) {
      errors.value.push('Please enter a valid phone number')
      emit('validation', { valid: false, error: errors.value[0] })
      return false
    }
  }

  // If no errors, emit validation success
  emit('validation', { valid: true, error: '' })
  return true
}
</script>

<template>
  <div class="w-full">
      <label :class="schema.class_label || 'o_label_small'">
        {{ schema.label }}
        <span v-if="schema.required" class="text-red-500">*</span>
      </label>
      <div class="relative">
        <MazPhoneNumberInput
          v-model="valType"
          show-code-on-list
          :color="errors.length > 0 || error ? 'danger' : 'primary'"
          :class="[
            'phone-input w-full',
            errors.length > 0 || error ? 'is-invalid' : '',
            isDirty && !errors.length && !error && valType ? 'is-valid' : ''
          ]"
          :preferred-countries="['ZA','FR', 'BE', 'DE', 'US', 'GB']"
          :ignored-countries="[]"
          @update="typeEmit.emit('input', valType, inputFormsType)"
          @blur="onBlur"
          :success="results?.isValid"
        />

        <!-- Validation state icons -->
        <div
          v-if="isTouched || isDirty"
          class="absolute top-0 right-0 mt-3 mr-3 pointer-events-none"
        >
          <Icon
            v-if="errors.length > 0 || error"
            name="mdi:alert-circle"
            class="h-5 w-5 text-red-500"
          />
          <Icon
            v-else-if="valType"
            name="mdi:check-circle"
            class="h-5 w-5 text-green-500"
          />
        </div>
      </div>

      <!-- Error message -->
      <div v-if="errors.length > 0 || error" class="mt-1 text-sm text-red-600 validation-error">
        {{ error || errors[0] }}
      </div>
  </div>
</template>

<style scoped>
/* Custom styling for phone input to match other form elements */
:deep(.phone-input) {
  width: 100%;
}

:deep(.maz-input) {
  @apply bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:text-gray-200;
}

:deep(.maz-input__input) {
  @apply bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-200 px-4 py-2;
}

:deep(.maz-input__label) {
  @apply text-gray-700 dark:text-gray-300;
}

:deep(.maz-input__icon) {
  @apply text-gray-500 dark:text-gray-400;
}

:deep(.maz-input__toggle-btn) {
  @apply text-gray-500 dark:text-gray-400;
}

:deep(.maz-select__options-list) {
  @apply bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 shadow-lg;
}

:deep(.maz-select__option) {
  @apply text-gray-900 dark:text-gray-200 hover:bg-blue-100 dark:hover:bg-blue-900;
}

:deep(.maz-select__option--selected) {
  @apply bg-blue-500 text-white;
}

:deep(.is-invalid .maz-input) {
  @apply border-red-500 focus:border-red-500 focus:ring-red-500;
}

:deep(.is-valid .maz-input) {
  @apply border-green-500 focus:border-green-500 focus:ring-green-500;
}

:deep(.maz-phone-number-input .country-selector) {
  @apply bg-white dark:bg-gray-800 border-r border-gray-300 dark:border-gray-700;
}

:deep(.maz-phone-number-input .country-selector__input) {
  @apply bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-200;
}

:deep(.maz-phone-number-input .country-selector__list) {
  @apply bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 shadow-lg;
}

:deep(.maz-phone-number-input .country-selector__list-item) {
  @apply text-gray-900 dark:text-gray-200 hover:bg-blue-100 dark:hover:bg-blue-900;
}

:deep(.maz-phone-number-input .country-selector__list-item--focused) {
  @apply bg-blue-100 dark:bg-blue-900;
}

:deep(.maz-phone-number-input .country-selector__input.has-error) {
  @apply border-red-500;
}

:deep(.maz-phone-number-input .country-selector__input.has-success) {
  @apply border-green-500;
}

:deep(.maz-phone-number-input .input-tel) {
  @apply bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-200;
}

:deep(.maz-phone-number-input .input-tel.has-error) {
  @apply border-red-500;
}

:deep(.maz-phone-number-input .input-tel.has-success) {
  @apply border-green-500;
}
</style>
