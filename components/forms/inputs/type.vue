<script setup lang="ts">
import { getCurrentInstance, onMounted, ref, watch } from 'vue';

import { isValidUrl, normalizeUrl } from '~/utils/urlHelpers.js';

const inputsTypeProps = defineProps({
  schema: {
    type: Object,
    default: () => {
      return {};
    },
  },
  error: {
    type: String,
    default: '',
  },
});

const valType: any = ref('');
const inputFormsType: any = ref({
  title: 'InputsTextInput',
  placeholder: inputsTypeProps.schema.placeholder,
  label: inputsTypeProps.schema.label,
  name: inputsTypeProps.schema.name,
  val: inputsTypeProps.schema.value,
});
const inputsTypeInstance: any = getCurrentInstance();
const typeEmit: any = getCurrentInstance();
const errors = ref<string[]>([]);
const isDirty = ref(false);
const isTouched = ref(false);

watch(
  () => valType.value,
  (newVal: any) => {
    if (newVal) {
      isDirty.value = true;
      inputsTypeInstance.emit('input', newVal);
      validateInput();
    }
  }
);

onMounted(() => {
  if (inputsTypeProps.schema.value) valType.value = inputsTypeProps.schema.value;
});

const onBlur = () => {
  isTouched.value = true;
  validateInput();
};

const validateInput = () => {
  errors.value = [];

  // Check if required
  if (inputsTypeProps.schema.required && !valType.value) {
    errors.value.push(`${inputsTypeProps.schema.label} is required`);
    typeEmit.emit('validation', { valid: false, error: errors.value[0] });
    return false;
  }

  // Check email format
  if (inputsTypeProps.schema.type === 'email' && valType.value) {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(valType.value)) {
      errors.value.push('Please enter a valid email address');
      typeEmit.emit('validation', { valid: false, error: errors.value[0] });
      return false;
    }
  }

  // Check URL format
  if (
    (inputsTypeProps.schema.val === 'website' || inputsTypeProps.schema.val === 'url') &&
    valType.value
  ) {
    if (!isValidUrl(valType.value)) {
      errors.value.push('Please enter a valid URL (e.g., www.example.com or https://example.com)');
      typeEmit.emit('validation', { valid: false, error: errors.value[0] });
      return false;
    } else {
      // Auto-normalize the URL for better user experience
      const normalized = normalizeUrl(valType.value);
      if (normalized && normalized !== valType.value) {
        valType.value = normalized;
        inputsTypeInstance.emit('input', normalized);
      }
    }
  }

  // If no errors, emit validation success
  typeEmit.emit('validation', { valid: true, error: '' });
  return true;
};
</script>

<template>
  <div>
    <label :class="schema.class_label">
      {{ schema.label }}
      <span v-if="schema.required" class="text-red-500">*</span>
    </label>
    <div class="relative">
      <input
        v-model="valType"
        :type="schema.type"
        :placeholder="schema.placeholder"
        :class="[
          schema.class_input,
          errors.length > 0 || error
            ? 'border-red-500 focus:border-red-500 focus:ring-red-500'
            : '',
          isDirty && !errors.length && !error
            ? 'border-green-500 focus:border-green-500 focus:ring-green-500'
            : '',
        ]"
        :name="schema.val"
        @input="typeEmit.emit('input', valType, inputFormsType)"
        @blur="onBlur"
      />

      <!-- Validation state icons -->
      <div
        v-if="isTouched || isDirty"
        class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none"
      >
        <Icon
          v-if="errors.length > 0 || error"
          name="mdi:alert-circle"
          class="h-5 w-5 text-red-500"
        />
        <Icon v-else-if="valType" name="mdi:check-circle" class="h-5 w-5 text-green-500" />
      </div>
    </div>

    <!-- Error message -->
    <div v-if="errors.length > 0 || error" class="mt-1 text-sm text-red-600">
      {{ error || errors[0] }}
    </div>
  </div>
</template>
