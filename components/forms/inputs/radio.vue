<script setup lang="ts">
const inputsRadioProps = defineProps({
  options: {
    type: Array, default: () => { return [] },
  },
  label: {
    type: String, default: '',
  },
  value: {
    type: String,
    default: '',
  },
})
const inputsRadioInstance: any = getCurrentInstance()
const valRadio: any = ref(false)

watch(() => valRadio.value, (newVal: boolean) => {
  if (newVal)
    inputsRadioInstance.$emit('input', newVal)
})
onMounted(() => {
  if (inputsRadioProps.value)
    valRadio.value = inputsRadioProps.value
})
</script>

<template>
  <div>
    <div>
      <span class="label-text ">{{ label }}</span>

      <label v-for="(option, index) in options" :key="index" class="cursor-pointer flex mt-2">
        <input v-model="valRadio" type="radio" name="opt" class="radio" :value="option">
        <span class="label-text ml-2">{{ option }}</span>
      </label>
    </div>
  </div>
</template>
