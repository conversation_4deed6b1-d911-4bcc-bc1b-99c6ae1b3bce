<script setup lang="ts">
const inputsIconsProps = defineProps({
  placeholder: {
    type: String,
    default: '',
  },
  label: {
    type: String,
    default: '',
  },
  name: {
    type: String,
    default: '',
  },
  typeText: {
    type: String,
    default: '',
  },
  classes: {
    type: String,
    default: 'input input-bordered w-full ',
  },
  value: {
    type: String,
    default: '',
  },
})
const icons = materialDesignIcon()

const valTypeIcon: any = ref('')
const inputFormsTypeIcon: any = ref({
  title: 'InputsTextInput',
  placeholder: inputsIconsProps.placeholder,
  label: inputsIconsProps.label,
  name: inputsIconsProps.name,
  val: inputsIconsProps.value,
})
const inputsIconInstance = getCurrentInstance()

watch(() => valTypeIcon.value, (newVal: any) => {
  if (newVal)
    inputsIconInstance.emit('input', newVal)
})
onMounted(() => {
  if (inputsIconsProps.value)
    valTypeIcon.value = inputsIconsProps.value
})
</script>

<template>
  <div>
    <div class="form-control">
      <label class="label">
        <span class="label-text ">{{ label }}</span>
      </label>
    </div>
    <div class="flex">
      <div :class="valTypeIcon" />
    </div>
    <div class="flex flex-wrap">
      <div v-for="(ic, index) in icons" :key="index" :class="{ 'bg-secondary text-secondary_focus': valTypeIcon === `i-mdi-${ic}` }" @click="valTypeIcon = `i-mdi-${ic}`">
        <div :class="`i-mdi-${ic}`" />
        <!-- 'i-mdi-{{ic}}',  -->
      </div>
    </div>
  </div>
</template>
