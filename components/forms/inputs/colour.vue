<script setup lang="ts">
const inputsColourProps = defineProps({
  label: {
    type: String,
    default: '',
  },
  value: {
    type: Boolean,
    default: false,
  },
  classes: {
    type: String,
    default: ' w-full text-gray-600',
  },
})
const inputsColourInstance: any = getCurrentInstance()
const valColours: any = ref(false)
const themeColours: any = ref(['#7367F0', '#28C76F', '#EA5455', '#FF9F43', '#1E1E1E'])
const customPrimaryColour: any = ref('#3DC9B3')
const customNavbarColour: any = ref('#3DC9B3')
const selectedColour: any = ref('#fff')

watch(() => selectedColour.value, (newVal: boolean) => {
  if (newVal)
    inputsColourInstance.emit('input', newVal)
})
const ColourLocal = ref(() => {
  return themeColours.value
})
const navbarColourOptionClasses = (colour: any) => {
  const classes = {}
  return classes
}
onMounted(() => {
  if (inputsColourProps.value)
    selectedColour.value = inputsColourProps.value
})
</script>

<template>
  <div class="w-full">
    <button
      class="px-4 py-2 mr-2 text-sm font-semibold border-none rounded-full shadow-md hover:text-white hover:bg-blue-200 focus:outline-none active:shadow-none"
      :class="{
        'text-white hover:bg-blue-200 focus:outline-none active:shadow-none':
          selectedColour !== '#fff',
      }"
      :style="`background-color: ${selectedColour}`"
    >
      {{ label }} <div v-if="label">:</div> {{ selectedColour }}
    </button>
    <div>
      <br>
      <ul class="flex list-none">
        <!-- WHITE COLOR -->
        <li
          class="w-10 h-10 m-2 border border-solid rounded-lg cursor-pointer bg-base_100 border-grey-light"
          :class="navbarColourOptionClasses('#fff')"
          @click="selectedColour = '#fff'"
        />

        <!-- THEME COLORS -->
        <li
          v-for="colord in themeColours"
          :key="colord"
          class="w-10 h-10 m-2 rounded-lg cursor-pointer"
          :class="navbarColourOptionClasses(colord)"
          :style="{ backgroundColor: colord }"
          :value="valColours"
          @click="selectedColour = colord"
        />

        <!-- CUSTOM COLOR -->
        <li
          class="w-10 h-10 m-2 rounded-lg cursor-pointer"
          :class="navbarColourOptionClasses(navbarColourOptionClasses)"
          :style="{ backgroundColor: customNavbarColour }"
          @click="selectedColour = customNavbarColour"
        />

        <li class="">
          <input
            v-model="customNavbarColour"
            label="Colour"
            class="w-10 h-10 m-2 rounded-lg cursor-pointer"
            type="color"
          >
        </li>
      </ul>
    </div>
  </div>
</template>
