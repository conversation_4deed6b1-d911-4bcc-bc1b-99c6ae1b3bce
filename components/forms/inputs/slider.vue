<script>
export default {
  name: 'InputsSlider',
  props: {
    label: {
      type: String, default: '',
    },
    value: {
      type: String,
      default: '',
    },
    min: {
      type: Number, default: 0,
    },
    max: {
      type: Number, default: 100,
    },
  },
  data() {
    return {
      val: 5,
      val2: [this.min + 1, this.max - 1],
    }
  },
  created() {
    if (this.value)
      this.val = this.value
  },
}
</script>

<template>
  <div>
    <small>{{ label }} - {{ val }}</small>
    <div class="slidecontainer">
      <input
        id="myRange"
        v-model="val"
        type="range"
        :min="min"
        :max="max"
        class="range"
        @input="$emit('input', val)"
      >
    </div>
  </div>
</template>
