<script setup lang="ts">
const inputsUploadsProps = defineProps({
    schema: {
    type: Object,
    default: () => {return {}},
  },
})


const imgUpload: any = ref([])
onMounted(() => {
  if (inputsUploadsProps.schema.value) {
    // img.value = inputsUploadProps.value;
  }
})
const currentInstanceUploadProps: any = getCurrentInstance()

function emitChanges(data: any[]) {
  console.log('wwwwwwwww',data)
  imgUpload.value = data
  currentInstanceUploadProps.emit('input', data)
}
const showAddItems = ref(false)
</script>

<template>
  <div class="w-full">
    <label :class="schema.class_label">
        {{ schema.label }}
      </label>
    <file-button @input="emitChanges" @close="showAddItems = false" />
    <div class="flex flex-wrap">
      <div  class="relative mt-2">
        <div class="flex items-center content-center justify-center h-32 mt-2 mr-2 overflow-hidden rounded ">
          <document-type :doc="imgUpload" classes="w-48 min-h-32" />
        </div>
      </div>
    </div>
  </div>
</template>

