<script setup lang="ts">
const selectprops = defineProps({
  schema: {
    type: Object,
    default: () => {
      return {};
    },
  },
  error: {
    type: String,
    default: '',
  }
});

const { data } = await useFetch<any>("/api/covalonic/forms", {
  pick: ["workspace_types", "industries", "currencies"],
});
// Use const for refs, not let
const val = ref("");
const optionsToSelected = ref([]);
const optionsSelected = ref([]);
const errors = ref<string[]>([]);
const isDirty = ref(false);
const isTouched = ref(false);

const emit = defineEmits(['input', 'validation']);

// Watch for changes to val.value and emit input event
watch(
  () => val.value,
  (newVal) => {
    if (isDirty.value) {
      emit("input", newVal);
      validateSelect();
    }
  }
);

const onBlur = () => {
  isTouched.value = true;
  validateSelect();
}

const validateSelect = () => {
  errors.value = [];

  // Check if required
  if (selectprops.schema.required) {
    if (selectprops.schema.multi) {
      if (!optionsSelected.value.length) {
        errors.value.push(`${selectprops.schema.label} is required`);
        emit('validation', { valid: false, error: errors.value[0] });
        return false;
      }
    } else {
      if (!val.value) {
        errors.value.push(`${selectprops.schema.label} is required`);
        emit('validation', { valid: false, error: errors.value[0] });
        return false;
      }
    }
  }

  // If no errors, emit validation success
  emit('validation', { valid: true, error: '' });
  return true;
}

const removeSelected = (index: any) => {
  if (index > -1) {
    optionsSelected.value.splice(index, 1);
  }
  emit("input", optionsSelected.value);
  validateSelect();
};
onMounted(() => {
  // Clear any existing options
  optionsToSelected.value = [];

  try {
    // Set initial value if provided
    if (selectprops.schema.value) {
      val.value = selectprops.schema.value;
    }

    // Populate options based on optionsUrl
    if (selectprops.schema.optionsUrl) {
      switch (selectprops.schema.optionsUrl) {
        case "workspace_types":
          if (data.value?.workspace_types?.default) {
            data.value.workspace_types.default.forEach((element: any) => {
              optionsToSelected.value.push({
                label: element.content,
                value: element.content,
              });
            });
          }
          break;

        case "industry":
          if (data.value?.industries?.default) {
            data.value.industries.default.forEach((element: any) => {
              optionsToSelected.value.push({
                label: element.content,
                value: element.content,
              });
            });
          }
          break;

        case "currencies":
          if (data.value?.currencies?.default) {
            data.value.currencies.default.forEach((element: any) => {
              optionsToSelected.value.push({
                label: element.content,
                value: element.content,
              });
            });
          }
          break;

        case "plain-label":
          if (selectprops.schema.options) {
            selectprops.schema.options.forEach((element: any) => {
              optionsToSelected.value.push({
                label: element.label,
                value: element.value
              });
            });
          }
          break;

        case "plain":
          if (selectprops.schema.options) {
            selectprops.schema.options.forEach((element: any) => {
              optionsToSelected.value.push({
                label: element,
                value: element
              });
            });
          }
          break;

        default:
          if (selectprops.schema.options) {
            // Make sure each option has label and value properties
            optionsToSelected.value = selectprops.schema.options.map((option: any) => {
              if (typeof option === 'object' && option.label && option.value) {
                return option;
              } else if (typeof option === 'string') {
                return { label: option, value: option };
              } else {
                return { label: String(option), value: option };
              }
            });
          }
          break;
      }
    }

    // If we have options but no value, set a default empty value
    if (optionsToSelected.value.length > 0 && !val.value) {
      val.value = '';
    }

    console.log('Select options loaded:', optionsToSelected.value);
  } catch (error) {
    console.error('Error initializing select component:', error);
  }
});

const valSelected = (data: any) => {
  isDirty.value = true;
  emit("input", data);
  validateSelect();
}
</script>

<template>
  <div class="w-full">
    <label :class="schema.class_label || 'o_label_small'">
      {{ schema.label }}
      <span v-if="schema.required" class="text-red-500">*</span>
    </label>
    <div>
      <client-only>
        <div v-if="schema.multi">
          <forms-inputs-select-multi
            :array="optionsToSelected"
            :selected="val"
            display="label"
            value="value"
            @input="valSelected"
            @blur="onBlur"
            :required="schema.required"
            :error="error"
          />
        </div>
        <div v-else class="relative">
          <select
            v-model="val"
            :class="[
              'o_input w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:text-gray-200 px-4 py-2',
              errors.length > 0 || error ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : '',
              isDirty && !errors.length && !error && val ? 'border-green-500 focus:border-green-500 focus:ring-green-500' : ''
            ]"
            @change="isDirty = true"
            @blur="onBlur"
          >
            <option :disabled="true" value="">
              {{ schema.placeholder }}
            </option>
            <option
              v-for="(item, index) in optionsToSelected"
              :key="index"
              :label="item.label"
              :value="item.value"
              class="py-2"
            >
              {{ item.label }}
            </option>
          </select>

          <!-- Validation state icons -->
          <div
            v-if="isTouched || isDirty"
            class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none"
          >
            <Icon
              v-if="errors.length > 0 || error"
              name="mdi:alert-circle"
              class="h-5 w-5 text-red-500"
            />
            <Icon
              v-else-if="val"
              name="mdi:check-circle"
              class="h-5 w-5 text-green-500"
            />
          </div>
        </div>
      </client-only>
    </div>

    <!-- Error message -->
    <div v-if="errors.length > 0 || error" class="mt-1 text-sm text-red-600 validation-error">
      {{ error || errors[0] }}
    </div>
  </div>
</template>
