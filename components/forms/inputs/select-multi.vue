<script setup lang="ts">
let items_ = defineProps({
  array: {
    type: Array,
    default: () => [],
  },
  selected: {
    type: Array,
    default: () => [],
  },
  display: {
    type: String,
    default: "name",
  },
  value: {
    type: String,
    default: "id",
  },
  required: {
    type: Boolean,
    default: false,
  },
  error: {
    type: String,
    default: '',
  }
});

// Use simple refs for state management
const itemsSelected = ref([])
const errors = ref<string[]>([])
const isDirty = ref(false)
const isTouched = ref(false)

const emit = defineEmits(['input', 'validation', 'blur'])

// Watch for changes in the selected items and emit input event
watch(
  () => itemsSelected.value,
  (newVal) => {
    if (isDirty.value) {
      emit('input', newVal)
      validateSelect()
    }
  },
  { deep: true }
)

// Validate the selection
const validateSelect = () => {
  errors.value = []

  // Check if required
  if (items_.required && (!itemsSelected.value || itemsSelected.value.length === 0)) {
    errors.value.push('Please select at least one option')
    emit('validation', { valid: false, error: errors.value[0] })
    return false
  }

  // If no errors, emit validation success
  emit('validation', { valid: true, error: '' })
  return true
}

// Handle blur event
const onBlur = () => {
  isTouched.value = true
  emit('blur')
  validateSelect()
}

// Initialize component
onMounted(() => {
  // Set initial selected items
  if (items_.selected) {
    if (typeof items_.selected === 'string') {
      itemsSelected.value = [items_.selected]
    } else {
      itemsSelected.value = items_.selected
    }

    // Mark as dirty if we have initial values
    if (itemsSelected.value && itemsSelected.value.length > 0) {
      isDirty.value = true
    }
  }
})

// Watch for prop changes
watch(
  () => items_.selected,
  (newVal) => {
    if (newVal) {
      if (typeof newVal === 'string') {
        itemsSelected.value = [newVal]
      } else {
        itemsSelected.value = newVal
      }
    } else {
      itemsSelected.value = []
    }
  }
)

// Add an item to the selected items
const addItem = (data: any) => {
  // Initialize array if it doesn't exist
  if (!itemsSelected.value) {
    itemsSelected.value = []
  }

  // Add the item if it's not already in the array
  if (!itemsSelected.value.includes(data)) {
    itemsSelected.value.push(data)
  }

  // Mark as dirty to indicate user interaction
  isDirty.value = true

  // Emit the updated value
  emit('input', itemsSelected.value)

  // Validate the selection
  validateSelect()
}

const showDrop = ref(false);
</script>

<template>
  <div class="relative w-full group">
    <div
      class="flex justify-between w-full overflow-x-auto p-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:text-gray-200"
      :class="[
        errors.length > 0 || error ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : '',
        isDirty && !errors.length && !error && itemsSelected.value.length > 0 ? 'border-green-500 focus:border-green-500 focus:ring-green-500' : ''
      ]"
      @blur="onBlur"
    >
      <div class="flex flex-wrap w-full gap-1">
        <div
          v-for="(cat, index) in itemsSelected"
          :key="index"
          class="flex items-center bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-md mr-1 mb-1"
        >
          <div v-if="display === value" class="mr-1">
            {{ cat }}
          </div>
          <div v-else class="mr-1">
            <div v-if="array.filter((flt: any) => flt[value] === cat).length === 0">
              Select Options
            </div>
            <div v-else>
              {{ array.filter((flt: any) => flt[value] === cat)[0][display] }}
            </div>
          </div>
          <button
            type="button"
            class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
            @click="itemsSelected.splice(index, 1)"
          >
            <Icon name="mdi:close" class="h-4 w-4" />
          </button>
        </div>
        <div v-if="itemsSelected.length === 0" class="text-gray-400 py-1">
          Select Options
        </div>
      </div>

      <div class="z-50 flex items-center">
        <button
          type="button"
          class="bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-400 p-1 rounded-md hover:bg-blue-100 dark:hover:bg-blue-800"
          @click.stop="showDrop = true; isDirty = true"
        >
          <Icon name="mdi:plus" class="h-5 w-5" />
        </button>

        <!-- Validation state icons -->
        <div v-if="isTouched || isDirty" class="ml-2">
          <Icon
            v-if="errors.length > 0 || error"
            name="mdi:alert-circle"
            class="h-5 w-5 text-red-500"
          />
          <Icon
            v-else-if="itemsSelected.length > 0"
            name="mdi:check-circle"
            class="h-5 w-5 text-green-500"
          />
        </div>
      </div>
    </div>

    <!-- Error message -->
    <div v-if="errors.length > 0 || error" class="mt-1 text-sm text-red-600 validation-error">
      {{ error || errors[0] }}
    </div>
    <modal-small :open="showDrop" title="Select Options">
      <template #body>
        <div
          v-if="showDrop"
          class="w-full h-48 p-2 mt-2 overflow-auto bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm"
        >
          <div
            v-for="(item, index) in array"
            :key="index"
            class="p-2 cursor-pointer hover:bg-blue-50 dark:hover:bg-blue-900 rounded-md mb-1"
            :class="{ 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200': itemsSelected.includes(item[value]) }"
            :value="item[value]"
            @click="
              itemsSelected.includes(item[value])
                ? itemsSelected.splice(
                  itemsSelected.findIndex((itema) => itema === item[value]),
                    1
                  )
                : addItem(item[value])
            "
          >
            {{ item[display] }}
          </div>
        </div>
      </template>
    </modal-small>
  </div>
</template>
