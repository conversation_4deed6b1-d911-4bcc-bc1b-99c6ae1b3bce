<script setup lang="ts">
const inputsTypeProps = defineProps({
    schema: {
    type: Object,
    default: () => {return {}},
  },
})

const valType: any = ref('')
const inputFormsType: any = ref({
  title: 'InputsTextInput',
  placeholder: inputsTypeProps.schema.placeholder,
  label: inputsTypeProps.schema.label,
  name: inputsTypeProps.schema.name,
  val: inputsTypeProps.schema.value,
})
const inputsTypeInstance: any = getCurrentInstance()

watch(() => valType.value, (newVal: any) => {
  if (newVal)
    inputsTypeInstance.emit('input', newVal)
})
onMounted(() => {
  if (inputsTypeProps.schema.value)
    valType.value = inputsTypeProps.schema.value
})
const typeEmit: any = getCurrentInstance()
</script>

<template>
  <div>
    <div class="flex justify-between mb-1">
        <label :class="schema.class_label">
        {{ schema.label }} 
      </label>
      <Icon name="mdi:plus" @click="schema.value.push('')"/>
    </div>
     
      <div v-for="(item, index) in schema.value" :key="index" class="mb-1">
        <input
        v-model="schema.value[index]"
        :type="schema.type"
        :placeholder="schema.placeholder"
        :class="schema.class_input"
        :name="schema.val"
      >
      </div>
      
  </div>
</template>

