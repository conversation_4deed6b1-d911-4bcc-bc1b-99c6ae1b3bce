<script>
export default {
  props: {
    modelValue: {
      type: null,
      required: true,
    },
    placeholder: {
      type: String,
      default: '',
    },
    label: {
      type: String,
      default: '',
    },
    name: {
      type: String,
      default: '',
    },
    typeText: {
      type: String,
      default: '',
    },
    value: {
      type: String,
      default: '',
    },
    classes: {
      type: String,
      default: 'input input-bordered w-full ',
    },
  },
  emits: ['update:modelValue'],
  data() {
    return {
      ctx: null,
      sign: false,
      prevX: 0,
      prevY: 0,
    }
  },
  watch: {
    modelValue(model) {
      if (!model)
        this.ctx.clearRect(0, 0, this.$el.width, this.$el.height)
    },
  },
  mounted() {
    this.ctx = this.$el.getContext('2d')
    this.ctx.strokeStyle = 'black'
    this.ctx.lineWidth = 2
  },
  created() {
    if (this.value)
      this.val = this.value
  },
  methods: {
    mousedown($event) {
      this.sign = true
      this.prevX = $event.offsetX
      this.prevY = $event.offsetY
    },
    mousemove($event) {
      if (this.sign) {
        const currX = $event.offsetX
        const currY = $event.offsetY
        this.draw(this.prevX, this.prevY, currX, currY)
        this.prevX = currX
        this.prevY = currY
      }
    },
    draw(depX, depY, destX, destY) {
      this.ctx.beginPath()
      this.ctx.moveTo(depX, depY)
      this.ctx.lineTo(destX, destY)
      this.ctx.closePath()
      this.ctx.stroke()

      const img = this.$el.toDataURL('image/png').replace('image/png', 'image/octet-stream')
      this.$emit('update:modelValue', img)
      this.$emit('input', img)
    },
  },
}
</script>

<template>
  <canvas-signature @mousedown="mousedown" @mousemove="mousemove" @mouseup="sign = false" @mouseout="sign = false" />
</template>

<style scoped>
canvas {
    border: 1px solid black;
    background-color: white;
    cursor: crosshair;
}
</style>
