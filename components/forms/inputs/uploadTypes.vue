<script setup lang="ts">
const inputsUploadProp = defineProps({
  label: {
    type: String,
    default: '',
  },
  value: {
    type: String,
    default: '',
  },
  types: {
    type: Array,
    default: () => { return [] },
  },
})

const { modalOpenUpload: modalOpenUploadInputsUpload, modalOpen: modalOpenInputsUpload } = space()

function close() {
  modalOpenUploadInputsUpload.value.open = false
  modalOpenInputsUpload.value.open = true
}
const imgsInputsUpload: any = ref([])

onMounted(() => {
  if (inputsUploadProp.value) {
    // img.value = inputsUploadProps.value;
  }
})
const inputsUploadInstance: any = getCurrentInstance()

function emitInputsChanges(data: any[]) {
  imgsInputsUpload.value = data
  inputsUploadInstance.emit('input', data)
}
</script>

<template>
  <div class="w-full">
    <div class="flex">
      <div for="my-modal-2" class="i-mdi-file-upload" @click="modalOpenUploadInputsUpload.open = true" />{{ label }}
    </div>
    <pop-upload @input="emitInputsChanges" />
    <div class="flex flex-wrap">
      <div v-for="(img, ind) in imgsInputsUpload" :key="ind" class="relative mt-2">
        <div class="flex items-center content-center justify-center h-32 mt-2 mr-2 overflow-hidden rounded ">
          <document-type :doc="img" classes="w-48 min-h-32" />
          <select v-model="img.usetype" class="w-full select select-bordered absolute bottom-0">
            <option v-for="(item, index) in types" :key="index" :value="item">
              {{ item }}
            </option>
          </select>
        </div>
      </div>
    </div>
  </div>
</template>

