<script setup lang="ts">
const inputsTypeProps = defineProps({
  placeholder: {
    type: String,
    default: '',
  },
  label: {
    type: String,
    default: '',
  },
  name: {
    type: String,
    default: '',
  },
  typeText: {
    type: String,
    default: '',
  },
  classes: {
    type: String,
    default: 'o_input',
  },
  value: {
    type: String,
    default: '',
  },
})

const valType: any = ref('')
const inputFormsType: any = ref({
  title: 'InputsTextInput',
  placeholder: inputsTypeProps.placeholder,
  label: inputsTypeProps.label,
  name: inputsTypeProps.name,
  val: inputsTypeProps.value,
})
const inputsTypeInstance: any = getCurrentInstance()

watch(() => valType.value, (newVal: any) => {
  if (newVal)
    inputsTypeInstance.emit('input', newVal)
})
onMounted(() => {
  if (inputsTypeProps.value)
    valType.value = inputsTypeProps.value
})
const typeEmit: any = getCurrentInstance()
</script>

<template>
  <div>
    <div class="form-control">
      <label class="o_label">
        <span class="label-text ">{{ label }}</span>
      </label>
      <input
        v-model="valType"
        :type="typeText"
        :placeholder="placeholder"
        :class="classes"
        @input="typeEmit.emit('input', valType, inputFormsType)"
      >
    </div>
  </div>
</template>

<style>
.input {
  width: 100% !important;
}
</style>
