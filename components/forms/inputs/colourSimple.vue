<script setup lang="ts">
const inputsColourSimpleProps = defineProps({
  label: {
    type: String,
    default: '',
  },
  value: {
    type: String,
    default: '',
  },
  classes: {
    type: String,
    default: ' w-full text-gray-600',
  },
})
const inputsColourSimpleInstance: any = getCurrentInstance()
const valColoursSimple: any = ref(false)
const themeColoursSimple: any = ref(['#7367F0', '#28C76F', '#EA5455', '#FF9F43', '#1E1E1E'])
const customPrimaryColourSimple: any = ref('#3DC9B3')
const customNavbarColourSimple: any = ref('#3DC9B3')
const selectedColourSimple: any = ref('#fff')

watch(() => customNavbarColourSimple.value, (newVal: boolean) => {
  if (newVal)
    inputsColourSimpleInstance.emit('input', newVal)
})
const ColourSimpleLocal = computed(() => {
  return themeColoursSimple.value
})
const navbarColourSimpleOptionClasses = (colour: any) => {
  const classes = {}
  return classes
}
onMounted(() => {
  if (inputsColourSimpleProps.value)
    customNavbarColourSimple.value = inputsColourSimpleProps.value
})
</script>

<template>
  <div class="w-full">
    <input
      v-model="customNavbarColourSimple"
      label="Colour"
      class="w-10 h-10 m-2 rounded-lg cursor-pointer"
      type="color"
    >
  </div>
</template>
