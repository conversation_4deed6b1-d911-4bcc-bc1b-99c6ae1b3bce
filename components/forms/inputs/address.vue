<script setup lang="ts">
const inputsTypeProps = defineProps({
    schema: {
    type: Object,
    default: () => {return {}},
  },
})

const valType: any = ref('')
const inputFormsType: any = ref({
  title: 'InputsTextInput',
  placeholder: inputsTypeProps.schema.placeholder,
  label: inputsTypeProps.schema.label,
  name: inputsTypeProps.schema.name,
  val: inputsTypeProps.schema.value,
})
const inputsTypeInstance: any = getCurrentInstance()

watch(() => valType.value, (newVal: any) => {
  if (newVal)
    inputsTypeInstance.emit('input', newVal)
})
onMounted(() => {
  if (inputsTypeProps.schema.value)
    valType.value = inputsTypeProps.schema.value
})
const typeEmit: any = getCurrentInstance()
</script>

<template>
  <div>
      <label :class="schema.class_label">
        {{ schema.label }}
      </label>
      <google-autocomplete  @input="typeEmit.emit('input', valType, inputFormsType)" />
  </div>
</template>

