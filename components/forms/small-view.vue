<script setup lang="ts">
const createCustomer: any = defineProps({

  formsItem: {
    type: Object,
    default: () => {
      return {};
    },
  },
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
  index: {
    type: Number,
    default: ''
  }
});

const createEmit: any = getCurrentInstance()

const openModal = ref(false);
const loading = ref(false);

watch(()=> createCustomer.open, (val) => {
  openModal.value = val
})
onMounted(() => {
  openModal.value = createCustomer.open;
});

const schemaAdd = computed(() => {
    const forms: any = {}
  let formItems = schemaSkin(createCustomer.formsItem.data);
  for(let i = 0; i < formItems.length; i++) {
   let area = formItems[i].area
    if(!forms[area]) {
      forms[area] = []
    }
    if(createCustomer.data[formItems[i].val]) {
      formItems[i].value = createCustomer.data[formItems[i].val]
      // createCustomer.formsItem.data[formItems[i].val]['value'] = createCustomer.data[formItems[i].val]

    }
    forms[area].push(formItems[i])
  }
  return forms
});
const formDataAdd = ref({});

function inputAdd(data: any) {
  if (data && data.path === undefined && data.bubbles === undefined){
    formDataAdd.value = {...formDataAdd.value,...data};
    createEmit.emit('input', formDataAdd.value)
  }
}


</script>

<template>
  <div class="w-full">
    <div v-if="loading">
        <loading-colorrockets />
      </div>
      <div v-else class="w-full">
          <forms-generator-view :schema="schemaAdd[index + 1]" @input="inputAdd" />
        
      </div>

  </div>
     
 
</template>
