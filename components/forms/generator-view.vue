<script setup lang="ts">
const formsGeneratorProps: any = defineProps({
  schema: {
    type: Array,
    default: () => [],
  },
})

const formDataGenerator: any = ref({})

const formsGeneratorInstance: any = getCurrentInstance()

function conditionCheck(field: { precondition: { condition: any[] }[] }) {
  let conMet = true
  if (field.precondition) {
    conMet = false
    const first: any = formsGeneratorProps.schema.find(
(      x: { val: any; }) => x.val === field.precondition[0].condition[0],
    )
    if (first) {
      if (first.cond_val || first.cond_val === false) {
        const operator = field.precondition[0].condition[1]
        const second = field.precondition[0].condition[2]
        switch (operator) {
          case '===':
            if (first.cond_val === second)
              conMet = true

            break
        }
      }
    }
  }
  return conMet
}

function updateGeneratorForm(fieldName: string | number, value: { path: undefined, type: undefined } | undefined) {
  const ind: number = formsGeneratorProps.schema.findIndex(x => x.val === fieldName)
  if (value !== undefined && fieldName && value.path === undefined && value.type === undefined) {
    formsGeneratorProps.schema[ind].cond_val = value
    formDataGenerator.value[fieldName] = value
    formsGeneratorInstance.emit('input', formDataGenerator.value)
  }
}
</script>

<template>
  <div class="grid items-center w-full grid-cols-2 gap-1">
    <div v-for="(field, index) in schema" :key="index" :class="field.class">
        <label :class="field.class_label">
        {{ field.label }} 
      </label>
      <div class="h-10">
        {{ field.value}}
      </div>
      <!-- <component
        :is="field.component"
        v-if="conditionCheck(field)"
        :schema="field"
        @input="updateGeneratorForm(field.val, $event)"
      /> -->
    </div>
  </div>
</template>
