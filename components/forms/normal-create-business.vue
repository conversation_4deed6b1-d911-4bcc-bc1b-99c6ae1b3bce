<script setup lang="ts">
import moment from "moment";
const { currentSpace } = space();
const extractedText = ref("sd");

const { copy, copied } = useClipboard({ source: extractedText });

const createCustomer: any = defineProps({
  open: {
    type: Boolean,
    default: () => {
      return false;
    },
  },
  formsItem: {
    type: Object,
    default: () => {
      return {};
    },
  },
  title: {
    type: String,
    default: "",
  },
  homepage: {
    type: Boolean,
    default: false,
  },
});

const createEmit: any = getCurrentInstance();
const { createHash } = database();
const openModal = ref(false);
const loading = ref(false);

watch(
  () => createCustomer.open,
  (val) => {
    openModal.value = val;
  }
);
onMounted(() => {
  openModal.value = createCustomer.open;
});

const schemaAdd = computed(() => {
  const forms: any = {};
  let formItems = schemaSkin(createCustomer.formsItem.data);
  for (let i = 0; i < formItems.length; i++) {
    let area = formItems[i].area;
    if (!forms[area]) {
      forms[area] = [];
    }
    forms[area].push(formItems[i]);
  }
  return forms;
});
const formDataAdd = ref({
  email: "",
});
const arrayText: any = ref([]);

function inputAdd(data: any) {
  if (data && data.path === undefined && data.bubbles === undefined) {
    console.log("Received data:", data);
    console.log("Form schema:", schemaAdd.value);

    if (data.image && data.image.text) {
      // Split the extracted text into lines
      arrayText.value = data.image.text.split(/\r?\n/).filter(line => line.trim() !== '');
      console.log("Extracted text lines:", arrayText.value);

      // Initialize potential field values
      let potentialName = '';
      let potentialFirstName = '';
      let potentialLastName = '';
      let potentialEmail = '';
      let potentialWebsite = '';
      let potentialPhone = '';
      let potentialDescription = '';

      // Process each line of text
      for (let i = 0; i < arrayText.value.length; i++) {
        const line = arrayText.value[i].trim();
        console.log(`Processing line ${i}:`, line);

        // Website detection (improved to catch various formats)
        if (line.includes('www.') || line.includes('http') || line.match(/\.[a-z]{2,}(\/|$)/i)) {
          potentialWebsite = line;
          console.log("Potential website found:", potentialWebsite);
        }

        // Email detection
        if (line.includes('@') && line.includes('.')) {
          // Extract email using regex
          const emailMatch = line.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/);
          if (emailMatch) {
            potentialEmail = emailMatch[0];
            console.log("Potential email found:", potentialEmail);
          }
        }

        // Phone detection
        const phoneRegex = /(?:\+\d{1,3}[- ]?)?\(?\d{3}\)?[- ]?\d{3}[- ]?\d{4}/;
        const phoneMatch = line.match(phoneRegex);
        if (phoneMatch) {
          potentialPhone = phoneMatch[0];
          console.log("Potential phone found:", potentialPhone);
        }

        // Name detection (usually at the top of the card)
        if (i === 0 || i === 1) {
          // If it's not an email, website, or phone, it might be a name
          if (!line.includes('@') && !line.includes('www.') && !phoneRegex.test(line)) {
            potentialName = line;

            // Try to split into first and last name
            const nameParts = potentialName.split(' ');
            if (nameParts.length >= 2) {
              potentialFirstName = nameParts[0];
              potentialLastName = nameParts.slice(1).join(' ');
            }
            console.log("Potential name found:", potentialName);
            console.log("First name:", potentialFirstName, "Last name:", potentialLastName);
          }
        }

        // Description/title (often on the second or third line)
        if ((i === 1 || i === 2) && !line.includes('@') && !line.includes('www.') && !phoneRegex.test(line)) {
          if (line !== potentialName) {
            potentialDescription = line;
            console.log("Potential description found:", potentialDescription);
          }
        }
      }

      // Update form fields with the extracted information
      for (let y = 0; y < schemaAdd.value.length; y++) {
        const field = schemaAdd.value[y];

        // Website field
        if (field.val === "website" && potentialWebsite) {
          field.value = potentialWebsite;
        }

        // Email field
        if (field.val === "email" && potentialEmail) {
          field.value = potentialEmail;
        }

        // Phone field
        if (field.val === "phone" && potentialPhone) {
          field.value = potentialPhone;
        }

        // Name field
        if (field.val === "name" && potentialName) {
          field.value = potentialName;
        }

        // First name field
        if (field.val === "first_name" && potentialFirstName) {
          field.value = potentialFirstName;
        }

        // Last name field
        if (field.val === "last_name" && potentialLastName) {
          field.value = potentialLastName;
        }

        // Description field
        if (field.val === "description" && potentialDescription) {
          field.value = potentialDescription;
        }
      }
    }

    // Update the form data
    formDataAdd.value = { ...formDataAdd.value, ...data };
  }
}
const notificationOpendfsf = useState<any>("notificationOpen", () => {
  return {
    open: false,
    text: "",
    color: "o_gradient",
  };
});

const spaceAddress = ref(true);
const center: any = ref([]);
const currentLocation: any = ref({});
const currentUser: any = useState("currentUser", () => {
  return {};
});

const currentClient: any = useState("currentClient", () => {
  return {};
});

const email = computed(() => {
  return formDataAdd.value.email;
});
const submit = async () => {
  console.log("email", email.value);
  if (email.value == "") {
    alert("Please enter an email");
    return;
  }
  let user = await queryByWhereLimit("user", "email", "==", email.value, 1);
  console.log("suer", user);

  if (user.length > 0) {
    currentUser.value = user[0];
    currentClient.value = user[0];
    submitSpace();
  } else {
    let payload = {
      created_at: new Date(),
      created_date: moment(new Date()).format("YYYY-MM-DD"),
      last_action: new Date(),
      last_action_date: moment(new Date()).format("YYYY-MM-DD,HH:mm:ss"),
      email: email.value,
      password: email.value,
      confirm_password: email.value,
      first_name: "",
      last_name: "",
    };
    console.log("create", payload);
    let createUser: any = await registerUser(email.value, email.value, payload);
    console.log("create", createUser);
    if (createUser.id) {
      setTimeout(() => {
        submit();
      }, 1000);
    }
  }
};

const submitSpace = async () => {
  console.log("SPACE", currentUser.value);
  let spce = await queryByWhereLimit(
    "spaces",
    "access_uid",
    "array-contains",
    currentUser.value.id,
    1
  );
  console.log("spce", spce);

  if (spce.length > 0) {
    currentSpace.value = spce[0];
    submitB();
  } else {
    let payload: any = {
      email: email.value,
      password: email.value,
      confirm_password: email.value,
      name: email.value,
      ...extraInfo(),
    };
    console.log("create", payload);
    let createSpace: any = await add("spaces", payload);
    console.log("create", createSpace);
    if (createSpace) {
      setTimeout(() => {
        submitSpace();
      }, 1000);
    }
  }
};

const submitB = async () => {
  loading.value = true;
  let collection = createCustomer.formsItem.filters.collection;
  console.log("collection", collection);
  let payload = formDataAdd.value;

  if (spaceAddress.value) {
    payload = {
      ...payload,
      hash: currentSpace.value.hash ? currentSpace.value.hash : "",
      lat: currentSpace.value.lat ? currentSpace.value.lat : "",
      lng: currentSpace.value.lng ? currentSpace.value.lng : "",
      location: currentSpace.value.location ? currentSpace.value.location : "",
      formatted_address: currentSpace.value.formatted_address
        ? currentSpace.value.formatted_address
        : "",
      // website: currentSpace.value.website ? currentSpace.value.website : '',
      // email: currentSpace.value.email ? currentSpace.value.email : '',
      // phone: currentSpace.value.phone ? currentClient.value.phone : '',
    };
  } else {
    payload = {
      ...payload,
      hash: currentLocation.value.hash ? currentLocation.value.hash : "",
      lat: currentLocation.value.lat ? currentLocation.value.lat : "",
      lng: currentLocation.value.lng ? currentLocation.value.lng : "",
      location: currentLocation.value.location
        ? currentLocation.value.location
        : "",
      formatted_address: currentLocation.value.formatted_address
        ? currentLocation.value.formatted_address
        : "",
    };
  }
  payload = removeUndefined(payload);
  console.log("payload", payload);
  let res: any = await add(collection, { ...payload, ...extraInfo() });
  console.log("res", res);
  createEmit.emit("created", { ...res, ...payload, ...extraInfo() });
  loading.value = false;
  openModal.value = false;
};

function input_address(data: {
  path: undefined;
  bubbles: undefined;
  utc_offset: any;
  lat: any;
  lng: any;
  vicinity: any;
  adr_address: any;
  formatted_address: string;
  place_id: any;
  url: any;
}) {
  if (data && data.path === undefined && data.bubbles === undefined) {
    currentLocation.value.utc_offset = data.utc_offset;
    currentLocation.value.lat = data.lat;
    currentLocation.value.lng = data.lng;
    currentLocation.value.vicinity = data.vicinity;
    currentLocation.value.adr_address = data.adr_address;
    currentLocation.value.formatted_address = data.formatted_address;
    currentLocation.value.place_id = data.place_id;
    currentLocation.value.adr_url = data.url;
    const split_address = data.formatted_address.split(",");
    currentLocation.value.address = split_address[0];
    currentLocation.value.city = split_address[1];
    currentLocation.value.state = split_address[2];
    currentLocation.value.zip = split_address[3];
    currentLocation.value.country = split_address[4];
    center.value = [data.lat, data.lng];
    currentLocation.value.hash = createHash(data.lat, data.lng);
  }
}
</script>

<template>
  <div>
    <div v-if="loading">
      <loading-colorrockets />
    </div>

    <div v-else>
      <div v-if="formsItem.filters.areas">
        <div
          v-for="(area, index) in formsItem.filters.areas"
          :key="index"
          class="mb-4"
        >
          <label class="o_label">{{ area }}</label>
          <forms-generator :schema="schemaAdd[index + 1]" @input="inputAdd" />
          <input v-model="spaceAddress" type="checkbox" />
          <label class="ml-1 text-sm">Use Accounts address</label>

          <div v-if="!spaceAddress">
            <google-autocomplete
              @input="input_address"
              label="Update location"
              classes="o_input_small"
            />
          </div>
        </div>
      </div>
      <div class="mt-4 mb-6">
        <h3 class="text-gray-700 font-medium mb-2" v-if="arrayText.length > 0">Extracted Information:</h3>
        <div class="grid grid-cols-1 gap-2">
          <div v-for="txt in arrayText" :key="txt">
            <div
              class="p-3 rounded-md bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 text-gray-700 flex items-center cursor-pointer hover:shadow-sm transition-all duration-200"
              type="button"
              @click="copy(txt)"
            >
              <Icon name="material-symbols:content-copy-outline-rounded" class="text-indigo-500 mr-2" />
              <span>{{ txt }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="mt-4 mb-6 p-4 bg-gray-50 rounded-lg border border-gray-100">
        <h3 class="text-gray-700 font-medium mb-2">Account Information:</h3>

        <div class="flex items-center mb-2" v-if="currentSpace.hash">
          <Icon name="mdi:map-marker" class="text-green-500 mr-2" />
          <span class="text-sm text-gray-600">{{ currentSpace.formatted_address }}</span>
        </div>
        <div v-else class="flex items-center mb-2">
          <Icon name="mdi:alert-circle" class="text-amber-500 mr-2" />
          <a class="text-sm text-amber-600 hover:text-amber-700" href="/c/dashboard">
            Please set your Account Location before uploading
          </a>
        </div>

        <div class="flex items-center mb-2" v-if="currentSpace.website">
          <Icon name="mdi:web" class="text-green-500 mr-2" />
          <span class="text-sm text-gray-600">{{ currentSpace.website }}</span>
        </div>
        <div v-else class="flex items-center mb-2">
          <Icon name="mdi:alert-circle" class="text-amber-500 mr-2" />
          <a class="text-sm text-amber-600 hover:text-amber-700" href="/c/dashboard">
            Please set your website before uploading
          </a>
        </div>

        <div class="flex items-center mb-2" v-if="currentSpace.phone">
          <Icon name="mdi:phone" class="text-green-500 mr-2" />
          <span class="text-sm text-gray-600">{{ currentSpace.phone }}</span>
        </div>
        <div v-else class="flex items-center mb-2">
          <Icon name="mdi:alert-circle" class="text-amber-500 mr-2" />
          <a class="text-sm text-amber-600 hover:text-amber-700" href="/c/dashboard">
            Please set your phone number before uploading
          </a>
        </div>
      </div>
      <button-submit
        :loading="loading"
        :disabled="loading"
        @click="submit"
        class="w-full py-3 mt-2 bg-gradient-to-r from-indigo-500 to-blue-600 hover:from-indigo-600 hover:to-blue-700 text-white font-medium rounded-lg shadow-md transition-all duration-200"
      >
        Submit Business Card
      </button-submit>
    </div>
  </div>
</template>
