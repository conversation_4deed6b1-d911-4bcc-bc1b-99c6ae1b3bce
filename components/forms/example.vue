<script setup lang="ts">
const createCustomer: any = defineProps({
  open: {
    type: Boolean,
    default: () => {
      return false;
    },
  },
});
const { data: formsItem } = await useFetch<any>("/api/example/forms", {
  pick: ["inputs"],
});

</script>

<template>
  <forms-quick-create :open="open" :formsItem="formsItem.inputs" title="Space Create"/>
  <!-- <modal-small
    :open="openModal"
    title="Create Space"
    buttontext="Submit"
    :showButtons="true"
    @submit="submit"
  >
    <template #body>
      <div v-if="loading">
        <loading-colorrockets />
      </div>
      <div v-else>
        <forms-generator :schema="schemaAdd" @input="inputAdd" />
      </div>
    </template>
  </modal-small> -->
</template>
