<script setup lang="ts">
const { currentSpace } = space();
const extractedText = ref("sd");

const { copy, copied } = useClipboard({ source: extractedText });

const createCustomer: any = defineProps({
  open: {
    type: Boolean,
    default: () => {
      return false;
    },
  },
  formsItem: {
    type: Object,
    default: () => {
      return {};
    },
  },
  title: {
    type: String,
    default: "",
  },
  homepage: {
    type: Boolean,
    default: false,
  },
});

const createEmit: any = getCurrentInstance();
const { createHash } = database();
const openModal = ref(false);
const loading = ref(false);

watch(
  () => createCustomer.open,
  (val) => {
    openModal.value = val;
  }
);
onMounted(() => {
  openModal.value = createCustomer.open;
});

const schemaAdd = computed(() => {
  const forms: any = {};
  let formItems = schemaSkin(createCustomer.formsItem.data);
  for (let i = 0; i < formItems.length; i++) {
    let area = formItems[i].area;
    if (!forms[area]) {
      forms[area] = [];
    }
    forms[area].push(formItems[i]);
  }
  return forms;
});
const formDataAdd = ref({});
const arrayText = ref([]);

function inputAdd(data: any) {
  if (data && data.path === undefined && data.bubbles === undefined) {
    console.log("data", data);
    console.log("data", schemaAdd.value);
    if (data.image) {
      arrayText.value = data.image.text.split(/\r?\n/);
      for (let i = 0; i < arrayText.value.length; i++) {
        console.log("arrayText.value[i]", arrayText.value[i]);
        console.log("includ", arrayText.value[i].includes("www"));
        if (arrayText.value[i].includes("www")) {
          for (let y = 0; y < schemaAdd.value.length; y++) {
            console.log("schemaAdd.value[y]", schemaAdd.value[y]);
            if (schemaAdd.value[y].val == "website") {
              schemaAdd.value[y].value = arrayText.value[i];
            }
          }
        }

        if (arrayText.value[i].includes("@")) {
          for (let y = 0; y < schemaAdd.value.length; y++) {
            if (schemaAdd.value[y].val == "email") {
              schemaAdd.value[y].value = arrayText.value[i];
            }
          }
        }
      }
      console.log("arrayText", arrayText.value);
    }
    formDataAdd.value = { ...formDataAdd.value, ...data };
  }
}
const notificationOpendfsf = useState<any>("notificationOpen", () => {
  return {
    open: false,
    text: "",
    color: "o_gradient",
  };
});

const spaceAddress = ref(true);
const center: any = ref([]);
const currentLocation: any = ref({});
const submit = async () => {
  loading.value = true;
  let collection = createCustomer.formsItem.filters.collection;
  console.log("collection", collection);
  let payload = formDataAdd.value;

  if (spaceAddress.value) {
    payload = {
      ...payload,
      hash: currentSpace.value.hash ? currentSpace.value.hash : "",
      lat: currentSpace.value.lat ? currentSpace.value.lat : "",
      lng: currentSpace.value.lng ? currentSpace.value.lng : "",
      location: currentSpace.value.location ? currentSpace.value.location : "",
      formatted_address: currentSpace.value.formatted_address
        ? currentSpace.value.formatted_address
        : "",
      // website: currentSpace.value.website ? currentSpace.value.website : '',
      // email: currentSpace.value.email ? currentSpace.value.email : '',
      // phone: currentSpace.value.phone ? currentClient.value.phone : '',
    };
  } else {
    payload = {
      ...payload,
      hash: currentLocation.value.hash ? currentLocation.value.hash : "",
      lat: currentLocation.value.lat ? currentLocation.value.lat : "",
      lng: currentLocation.value.lng ? currentLocation.value.lng : "",
      location: currentLocation.value.location
        ? currentLocation.value.location
        : "",
      formatted_address: currentLocation.value.formatted_address
        ? currentLocation.value.formatted_address
        : "",
    };
  }
  let res: any = await add(collection, { ...payload, ...extraInfo() });
  console.log("res", res);
  createEmit.emit("created", { ...res, ...payload, ...extraInfo() });
  loading.value = false;
  openModal.value = false;
};

function input_address(data: {
  path: undefined;
  bubbles: undefined;
  utc_offset: any;
  lat: any;
  lng: any;
  vicinity: any;
  adr_address: any;
  formatted_address: string;
  place_id: any;
  url: any;
}) {
  if (data && data.path === undefined && data.bubbles === undefined) {
    currentLocation.value.utc_offset = data.utc_offset;
    currentLocation.value.lat = data.lat;
    currentLocation.value.lng = data.lng;
    currentLocation.value.vicinity = data.vicinity;
    currentLocation.value.adr_address = data.adr_address;
    currentLocation.value.formatted_address = data.formatted_address;
    currentLocation.value.place_id = data.place_id;
    currentLocation.value.adr_url = data.url;
    const split_address = data.formatted_address.split(",");
    currentLocation.value.address = split_address[0];
    currentLocation.value.city = split_address[1];
    currentLocation.value.state = split_address[2];
    currentLocation.value.zip = split_address[3];
    currentLocation.value.country = split_address[4];
    center.value = [data.lat, data.lng];
    currentLocation.value.hash = createHash(data.lat, data.lng);
  }
}
</script>

<template>
  <div>
    <div v-if="loading">
      <loading-colorrockets />
    </div>
    <div v-else>
      <div v-if="homepage && currentSpace.id == 1">
        <auth-email />
      </div>
      <div v-else>
        <div v-if="formsItem.filters.areas">
          <div
            v-for="(area, index) in formsItem.filters.areas"
            :key="index"
            class="mb-8"
          >
            <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">{{ area }} Information</h3>

            <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
              <forms-generator :schema="schemaAdd[index + 1]" @input="inputAdd" />

              <div class="mt-4 flex items-center">
                <input
                  v-model="spaceAddress"
                  type="checkbox"
                  id="useAccountAddress"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label for="useAccountAddress" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                  Use Account's address
                </label>
              </div>

              <div v-if="!spaceAddress" class="mt-4">
                <google-autocomplete
                  @input="input_address"
                  label="Update location"
                  classes="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:text-gray-200 px-4 py-2 w-full"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Extracted Text Section -->
        <div v-if="arrayText.length > 0" class="mb-6">
          <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Extracted Text</h3>
          <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div v-for="txt in arrayText" :key="txt" class="mb-2">
              <div
                class="p-2 bg-[#0072ff] text-white rounded-md flex items-center cursor-pointer hover:bg-[#0054bb] transition-colors"
                type="button"
                @click="copy(txt)"
              >
                <Icon name="material-symbols:content-copy-outline-rounded" class="mr-2" />
                <span>{{ txt }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Status Information -->
        <div class="mb-6 bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <h3 class="text-md font-medium text-gray-800 dark:text-gray-200 mb-2">Account Status</h3>

          <div v-if="currentSpace.hash" class="flex items-center text-sm text-gray-600 dark:text-gray-400 mb-2">
            <Icon name="mdi:map-marker" class="mr-2 text-green-500" />
            <span>{{ currentSpace.formatted_address }}</span>
          </div>
          <div v-else class="flex items-center text-sm text-red-600 mb-2">
            <Icon name="mdi:alert-circle" class="mr-2" />
            <a href="/c/dashboard" class="hover:underline">Please set your Account Location before uploading</a>
          </div>

          <div v-if="currentSpace.website" class="flex items-center text-sm text-gray-600 dark:text-gray-400 mb-2">
            <Icon name="mdi:web" class="mr-2 text-green-500" />
            <span>{{ currentSpace.website }}</span>
          </div>
          <div v-else class="flex items-center text-sm text-red-600 mb-2">
            <Icon name="mdi:alert-circle" class="mr-2" />
            <a href="/c/dashboard" class="hover:underline">Please set your website before uploading</a>
          </div>

          <div v-if="currentSpace.phone" class="flex items-center text-sm text-gray-600 dark:text-gray-400">
            <Icon name="mdi:phone" class="mr-2 text-green-500" />
            <span>{{ currentSpace.phone }}</span>
          </div>
          <div v-else class="flex items-center text-sm text-red-600">
            <Icon name="mdi:alert-circle" class="mr-2" />
            <a href="/c/dashboard" class="hover:underline">Please set your phone number before uploading</a>
          </div>
        </div>

        <!-- Submit Button -->
        <div class="mt-6">
          <button-submit
            :loading="loading"
            :disabled="loading"
            @click="submit"
            class="w-full bg-[#0072ff] hover:bg-[#0054bb] text-white font-medium py-3 px-4 rounded-md transition-colors duration-200 flex items-center justify-center"
          >
            <Icon name="mdi:check-circle" class="mr-2" v-if="!loading" />
            <span>Create Business Space</span>
          </button-submit>
        </div>
      </div>
    </div>
  </div>
</template>
