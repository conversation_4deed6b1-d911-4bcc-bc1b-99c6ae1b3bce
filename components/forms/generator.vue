<script setup lang="ts">
const formsGeneratorProps: any = defineProps({
  schema: {
    type: Array,
    default: () => [],
  },
})

const formDataGenerator: any = ref({})
const formErrors: any = ref({})

const formsGeneratorInstance: any = getCurrentInstance()

// Emit validation events
const emit = defineEmits(['input', 'validation'])

function conditionCheck(field: { precondition: { condition: any[] }[] }) {
  let conMet = true
  if (field.precondition) {
    conMet = false
    const first: any = formsGeneratorProps.schema.find(
(      x: { val: any; }) => x.val === field.precondition[0].condition[0],
    )

    if (first) {
      if (first.cond_val || first.cond_val === false) {
        const operator = field.precondition[0].condition[1]
        const second = field.precondition[0].condition[2]
        //TODO switch operator
        conMet = first.cond_val === second
        switch (operator) {
          case '===':
            console.log('case')
            if (first.cond_val === second)
              conMet = true

            break
        }
      }
    }
  }
  return conMet
}

function updateGeneratorForm(fieldName: string | number, value: { path: undefined, type: undefined } | undefined) {
  const ind: number = formsGeneratorProps.schema.findIndex(x => x.val === fieldName)
  if (value !== undefined && fieldName && value.path === undefined && value.type === undefined) {
    formsGeneratorProps.schema[ind].cond_val = value
    formDataGenerator.value[fieldName] = value
    formsGeneratorInstance.emit('input', formDataGenerator.value)
  } else if (value !== undefined && fieldName && value.path === undefined && value.id ) {
    formsGeneratorProps.schema[ind].cond_val = value
    formDataGenerator.value[fieldName] = value
    formsGeneratorInstance.emit('input', formDataGenerator.value)
  }
}

// Handle validation events from form components
function handleValidation(fieldName: string, event: { valid: boolean, error: string }) {
  if (!event.valid) {
    formErrors.value[fieldName] = event.error
  } else {
    delete formErrors.value[fieldName]
  }

  // Emit validation event to parent component
  const isFormValid = Object.keys(formErrors.value).length === 0
  emit('validation', {
    valid: isFormValid,
    errors: formErrors.value,
    field: fieldName,
    fieldError: event.error
  })
}
</script>

<template>
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <client-only>
      <div
        v-for="(field, index) in schema"
        :key="index"
        :class="{
          'col-span-1 md:col-span-2': field.component === 'FormsInputsQuill' || field.component === 'FormsInputsUpload',
          'mb-4': true,
          [field.class || '']: true
        }"
      >
        <component
          :is="field.component"
          v-if="conditionCheck(field)"
          :schema="field"
          :error="formErrors[field.val]"
          @input="updateGeneratorForm(field.val, $event)"
          @validation="(event) => handleValidation(field.val, event)"
          class="form-field"
        />
      </div>
    </client-only>
  </div>
</template>
