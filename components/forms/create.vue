<script setup lang="ts">
const payload = {
  filters: {
    index: "crm",
    collection: "leads"
  },
  sorts: {
    sort1: {
      type: "desc"
    }
  },
  data: [
    {
      label: "id",
      val: "id",
      value: "",
      class: "w-2/5",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType"
    },
    {
      label: "Created At",
      val: "created_at",
      value: "",
      class: "w-2/5",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType"
    },
    {
      label: "Updated at",
      val: "updated_at",
      value: "",
      class: "w-2/5",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType"
    },
    {
      label: "User ownership",
      val: "user_own",
      value: "",
      class: "w-2/5",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType"
    },
    {
      label: "Users with access",
      val: "user_access",
      value: "",
      class: "w-2/5",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType"
    },
    {
      label: "Space ownership",
      val: "space_own",
      value: "",
      class: "w-2/5",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType"
    },
    {
      label: "Space with access",
      val: "space_access",
      value: "",
      class: "w-2/5",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType"
    }
  ]
}
const postFrom = async ()=> {
    let res = await $fetch("/api/ordermation/access", {
      method: "POST",
      body: {
        index: payload.value.filters.index,
        vector: payload.value.filters.collection,
        data: payload.value
    },
    })
}

const obj =    {
      label: "Space with access",
      val: "space_access",
      value: "",
      class: "w-2/5",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: '',
        options: [],
      component: "FormsInputsType",
      action: "FormsActionsType"
    }


const comps = ref([
   'FormsInputsBusiness', 'FormsInputsCheckbox', 'FormsInputsColour', 'FormsInputsColourSimple', 'FormsInputsDivider', 'FormsInputsIcon', 'FormsInputsLink', 'FormsInputsPhone', 'FormsInputsPrefix', 'FormsInputsQuill', 'FormsInputsRadio', 'FormsInputsSelectMulti', 'FormsInputsSelect', 'FormsInputsSignature', 'FormsInputsSiteurl', 'FormsInputsSlider', 'FormsInputsSwitch', 'FormsInputsTags', 'FormsInputsTextarea', 'FormsInputsType', 'FormsInputsUpload', 'FormsInputsUploadTypes', 'FormsInputsUser'
])

const actions = ref([
   'FormsActionsBusiness', 'FormsActionsCheckbox', 'FormsActionsColour', 'FormsActionsColourSimple', 'FormsActionsDivider', 'FormsActionsIcon', 'FormsActionsLink', 'FormsActionsPhone', 'FormsActionsPrefix', 'FormsActionsQuill', 'FormsActionsRadio', 'FormsActionsSelectMulti', 'FormsActionsSelect', 'FormsActionsSignature', 'FormsActionsSiteurl', 'FormsActionsSlider', 'FormsActionsSwitch', 'FormsActionsTags', 'FormsActionsTextarea', 'FormsActionsType', 'FormsActionsUpload', 'FormsActionsUploadTypes', 'FormsActionsUser'
])
</script>
<template>
    <div>
    <form>
       <label class="o_input_small">Index</label>
       <input type="text" v-model="payload.filters.index" class="o_input_small">
         <label class="o_input_small">Collection</label>
         <input type="text" v-model="payload.filters.collection" class="o_input_small">
            <label class="o_input_small">Sort</label>
            <input type="text" v-model="payload.sorts.sort1.type" class="o_input_small">
           <div v-for="(item, index) in payload.data" :key="index">
            <label class="o_input_small">Label</label>
            <input type="text" v-model="item.label" class="o_input_small">
            <label class="o_input_small">Val</label>
            <input type="text" v-model="item.val" class="o_input_small">
            <label class="o_input_small">Value</label>
            <input type="text" v-model="item.value" class="o_input_small">
            <label class="o_input_small">Class</label>
            <input type="text" v-model="item.class" class="o_input_small">
            <label class="o_input_small">Class Input</label>
            <input type="text" v-model="item.class_input" class="o_input_small">
            <label class="o_input_small">Class Label</label>
            <input type="text" v-model="item.class_label" class="o_input_small">
            <label class="o_input_small">Type</label>
            <input type="text" v-model="item.type" class="o_input_small">
            <label class="o_input_small">Form</label>
            <input type="checkbox" v-model="item.form" class="o_input_small">
            <label class="o_input_small">Table</label>
            <input type="checkbox" v-model="item.table" class="o_input_small">
            <label class="o_input_small">Small Table</label>
            <input type="checkbox" v-model="item.small_table" class="o_input_small">
            <label class="o_input_small">Small Form</label>
            <input type="checkbox" v-model="item.small_form" class="o_input_small">
            <label class="o_input_small">Editable</label>
            <input type="checkbox" v-model="item.editable" class="o_input_small">
            <label class="o_input_small">Required</label>
            <input type="checkbox" v-model="item.required" class="o_input_small">
            <label class="o_input_small">Readonly</label>
            <input type="checkbox" v-model="item.readonly" class="o_input_small">
            <label class="o_input_small">Unique</label>
            <input type="checkbox" v-model="item.unique" class="o_input_small">
            <label class="o_input_small">Hidden</label>
            <input type="checkbox" v-model="item.hidden" class="o_input_small">
            <label class="o_input_small">Disabled</label>
            <input type="checkbox" v-model="item.disabled" class="o_input_small">
            <label class="o_input_small">Multiple</label>
            <input type="checkbox" v-model="item.multi" class="o_input_small">
            <label class="o_input_small">Options Url</label>
            <input type="text" v-model="item.optionsUrl" class="o_input_small">
            <label class="o_input_small">Options</label>
            <button @click="item.push({label: '', value: ''})">Add</button>
            <div class="">
                <div v-for="(opt, ind) in item.options" :key="ind">
                    <label class="o_input_small">Label</label>
                    <input type="text" v-model="opt.label" class="o_input_small">
                    <label class="o_input_small">Value</label>
                    <input type="text" v-model="opt.value" class="o_input_small">
                </div>
            </div>

            <label class="o_input_small">Area</label>
            <input type="number" v-model="item.area" class="o_input_small">
            <label class="o_input_small">Quick</label>
            <input type="checkbox" v-model="item.quick" class="o_input_small">
            <label class="o_input_small">Component</label>
            <select v-model="item.component" class="o_input_small">
                <option v-for="comp in comps" :value="comp">{{comp}}</option>
            </select>

            <label class="o_input_small">Action</label>
           <select v-model="item.action" class="o_input_small">
               <option v-for="comp in actions" :value="comp">{{comp}}</option>
              </select>
           </div>
        <button @click="postFrom">Submit</button>
    </form>
    </div>
</template>

  o