<script setup lang="ts">
const uiCardPerc = defineProps({
    title: {
        type: String,
        default: () => '',
    },
    actual: {
        type: Number,
        default: () => {return 0},
    },
    target: {
        type: Number,
        default: () => {return 1},
    },
})
const { currentSpace } = space();
const open = ref(false);
const percentage = computed(() => {
    return Math.round((uiCardPerc.actual / uiCardPerc.target) * 100);
});
</script>

<template>
    <div class="h-full p-6 rounded-lg shadow-lg theme_100">
            <div class="flex flex-row justify-between pb-3">
              <div class="flex flex-col">
                <h3 class="text-base font-bold">{{title}}</h3>
              </div>
              <div class="relative">
                <button
                  @click="open = !open"
                  class="text-gray-500 transition-colors duration-200 hover:text-gray-600 dark:hover:text-gray-400 focus:outline-none hover:outline-none"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="currentColor"
                    class="w-6 h-6 bi bi-three-dots"
                    viewBox="0 0 16 16"
                  >
                    <path
                      d="M3 9.5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3z"
                    />
                  </svg>
                </button>
                <div
                  v-show="open"
                  
                  class="absolute z-10 origin-top-right bg-white border border-gray-200 rounded ltr:right-0 rtl:left-0 rounded-t-non dark:bg-gray-800 dark:border-gray-700"
                  style="min-width: 12rem"
                >
                  <a
                    class="block px-3 py-2 hover:bg-gray-100 focus:bg-gray-100 dark:hover:bg-gray-900 dark:hover:bg-opacity-20 dark:focus:bg-gray-900"
                    href="#"
                    >Daily</a
                  >
                  <a
                    class="block px-3 py-2 hover:bg-gray-100 focus:bg-gray-100 dark:hover:bg-gray-900 dark:hover:bg-opacity-20 dark:focus:bg-gray-900"
                    href="#"
                    >Weekly</a
                  >
                  <a
                    class="block px-3 py-2 hover:bg-gray-100 focus:bg-gray-100 dark:hover:bg-gray-900 dark:hover:bg-opacity-20 dark:focus:bg-gray-900"
                    href="#"
                    >Yearly</a
                  >
                </div>
              </div>
            </div>
            <div class="relative">
              <h4 class="mb-3 text-2xl font-bold text-green-500">{{currentSpace.currency ? currentSpace.currency.symbol : ''}}{{actual}}</h4>
              <div class="w-full h-4 mt-2 bg-green-100 rounded-full">
                <div
                  class="h-full text-xs text-center text-white bg-green-500 rounded-full"
                  :style="`width: ${percentage}%`"
                >
                  <span class="text-xs text-center text-white">{{percentage}}%</span>
                </div>
              </div>
              <p class="mt-3 text-sm text-gray-500">From target {{currentSpace.currency ? currentSpace.currency.symbol : ''}}{{ target }}</p>
            </div>
          </div>
</template>