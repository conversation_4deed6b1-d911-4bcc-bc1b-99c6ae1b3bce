<script setup lang="ts">
import { Chart, registerables, CategoryScale } from "chart.js";
const uiCardDoughnut = defineProps({
  title: {
    type: String,
    default: "New Contact",
  },
  id: {
    type: String,
    default: "LineArea",
  },
  label: {
    type: String,
    default: "Traffic Source",
  },
  labels: {
    type: Array,
    default: ["Read", "Spam", "Unread"],
  },
  data: {
    type: Array,
    default: [925, 30, 252],
  },
});
const open = ref(false);
Chart.register(...registerables, CategoryScale);
onMounted(() => {
  console.log("mounted");
  if (process.client) {
    const text_primary_500 = "#6366F1";
    const text_secondary_500 = "#EC4899";
    const text_yellow_500 = "#F59E0B";
    const text_gray_500 = "#84848f";

    Chart.defaults.color = text_gray_500;

    // CRM EMAIL DOUGHNUT CHART
    const chart_email = document.getElementById(uiCardDoughnut.id);
    if (chart_email != null) {
      const ctd = chart_email.getContext("2d");
      const EmailChart = new Chart(ctd, {
        type: "doughnut",
        data: {
          labels: uiCardDoughnut.labels,
          datasets: [
            {
              label: uiCardDoughnut.label,
              data: uiCardDoughnut.data,
              backgroundColor: [
                text_primary_500,
                text_secondary_500,
                text_yellow_500,
              ],
              hoverOffset: 4,
            },
          ],
        },
        options: {
          animation: {
            delay: 2000,
          },
          plugins: {
            legend: {
              display: true,
              position: "bottom",
            },
          },
        },
      });
    }
  }
});
</script>

<template>
  <div class="h-full p-6 rounded-lg shadow-lg theme_100">
    <div class="flex flex-row justify-between pb-3">
      <div class="flex flex-col">
        <h3 class="text-base font-bold">{{ title }}</h3>
      </div>
      <div class="relative">
        <button
          @click="open = !open"
          class="text-gray-500 transition-colors duration-200 hover:text-gray-600 dark:hover:text-gray-400 focus:outline-none hover:outline-none"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            class="w-6 h-6 bi bi-three-dots"
            viewBox="0 0 16 16"
          >
            <path
              d="M3 9.5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3z"
            />
          </svg>
        </button>
        <div
          v-show="open"
          
          class="absolute z-10 origin-top-right bg-white border border-gray-200 rounded ltr:right-0 rtl:left-0 rounded-t-non dark:bg-gray-800 dark:border-gray-700"
          style="min-width: 12rem"
        >
          <a
            class="block px-3 py-2 hover:bg-gray-100 focus:bg-gray-100 dark:hover:bg-gray-900 dark:hover:bg-opacity-20 dark:focus:bg-gray-900"
            href="#"
            >Daily</a
          >
          <a
            class="block px-3 py-2 hover:bg-gray-100 focus:bg-gray-100 dark:hover:bg-gray-900 dark:hover:bg-opacity-20 dark:focus:bg-gray-900"
            href="#"
            >Weekly</a
          >
          <a
            class="block px-3 py-2 hover:bg-gray-100 focus:bg-gray-100 dark:hover:bg-gray-900 dark:hover:bg-opacity-20 dark:focus:bg-gray-900"
            href="#"
            >Yearly</a
          >
        </div>
      </div>
    </div>
    <div class="relative w-2/3 mx-auto text-center sm:w-1/2 lg:w-full">
      <canvas class="max-w-100" :id="id"></canvas>
    </div>
  </div>
</template>
