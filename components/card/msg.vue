<script setup lang="ts">
defineProps({
    msg: {
        type: Object,
        default: ()=> {return {
            id: 0,
            name: "<PERSON>",
            avatar: "",
            msg: "What do you think about this project?",
            time: "12m ago",
            online: true,
        }}
    }
})
</script>

<template>
<div class="flex flex-row flex-wrap items-center py-2 border-b border-gray-200 dark:border-gray-700 dark:bg-gray-900 dark:bg-opacity-40 dark:hover:bg-opacity-20 hover:bg-gray-100 bg-gray-50">
                    <div class="flex-shrink w-1/4 max-w-full px-2 text-center">
                      <div class="relative">
                        <img :src="msg.avatar" class="w-10 h-10 mx-auto rounded-full" :alt="msg.name">
                        <span v-if="msg.online" title="online" class="flex justify-center absolute -bottom-0.5 ltr:right-2 rtl:left-2 text-center bg-green-500 border border-white w-3 h-3 rounded-full"></span>
                      </div>
                    </div>
                    <div class="flex-shrink w-3/4 max-w-full px-2">
                      <div class="text-sm font-bold">{{msg.name}}</div>
                      <div class="mt-1 text-sm text-gray-500">{{ msg.msg }}</div>
                      <div class="mt-1 text-sm text-gray-500">{{msg.time}}</div>
                    </div>
                  </div>
</template>