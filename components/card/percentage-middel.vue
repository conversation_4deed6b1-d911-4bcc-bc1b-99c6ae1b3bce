<script setup lang="ts">
const uiCardPerc = defineProps({
  title: {
    type: String,
    default: () => "",
  },
  color: {
    type: String,
    default: () => "primarys",
  },
  actual: {
    type: Number,
    default: () => {
      return 0;
    },
  },
  target: {
    type: Number,
    default: () => {
      return 1;
    },
  },
  number: {
    type: Number,
    default: () => {
      return 1;
    },
  },
});
const { currentSpace } = space();
 
const percentage = computed(() => {
  return Math.round((uiCardPerc.actual / uiCardPerc.target) * 100);
});
</script>

<template>
  <div
    :class="`relative p-6 mb-6 overflow-hidden text-indigo-100 rounded-lg shadow-lg bg-${color}`"
  >
    <div class="absolute ltr:-right-10 rtl:-left-10 -top-10">
      <div class="bg-white rounded-full opacity-10 w-36 h-36"></div>
    </div>
    <div class="absolute ltr:-right-8 rtl:-left-8 -top-8">
      <div class="w-24 h-24 bg-white rounded-full opacity-20"></div>
    </div>

    <div class="flex flex-row justify-between pb-3">
      <div class="flex flex-col">
        <h3 class="text-base font-bold">{{ title }}</h3>
      </div>
    </div>
    <div class="relative text-center">
      <h4 class="mb-3 text-2xl font-bold text-white">{{currentSpace.currency ? currentSpace.currency.symbol : ''}}{{ number }}</h4>
      <p class="mb-3 text-sm">
        This week <span class="font-semibold">({{ actual }} Deal)</span>
      </p>
      <div class="w-full h-4 mt-2 bg-green-100 rounded-full">
        <div
          class="h-full text-xs text-center text-white bg-green-500 rounded-full"
          :style="`width:${percentage}%`"
        >
          <span class="text-xs text-center text-white">{{ percentage }}%</span>
        </div>
      </div>
      <p class="mt-3 text-sm">
        From target <span class="font-semibold">({{target}} Deal)</span>
      </p>
    </div>
  </div>
</template>
