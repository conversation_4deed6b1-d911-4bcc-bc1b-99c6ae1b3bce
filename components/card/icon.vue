<script setup lang="ts">
defineProps({
    title: {
        type: String,
        required: true,
    },
    subtitle: {
        type: String,
        required: true,
    },
})
</script>

<template>
    <div class="p-6 rounded-lg shadow-lg cursor-pointer theme_100" @click="$emit('cardEmit')">
        <div class="flex flex-row">
            <slot name="icon" />
            <div class="ltr:ml-3 rtl:mr-3">
                <h2 class="self-center mb-1">{{ title }}</h2>
                <p class="text-xl font-semibold">{{ subtitle }}</p>
            </div>
        </div>
    </div>
</template>