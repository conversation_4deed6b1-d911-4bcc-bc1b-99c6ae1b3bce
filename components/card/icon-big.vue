<script setup lang="ts">
defineProps({
    title: {
        type: String,
        required: true,
    },
    subtitle: {
        type: String,
        required: true,
    },
    sidetitle: {
        type: String,
        required: true,
    },
    bottomtext: {
        type: String,
        require: true
    },
    route: {
        type: String,
        require: true
    }
})
</script>

<template>
    <div class="h-full rounded-lg shadow-lg theme_100 cursor-pointer" @click="$emit('cardEmit', route)">
        <div  class="relative px-6 pt-6 text-sm font-semibold">
            {{ title }}
            <div 
                class="text-green-500 ltr:float-right rtl:float-left">
                {{ subtitle }}
                <div class="absolute top-auto mb-3 bottom-full" x-show.transition.origin.top="tooltips"
                    style="display: none;">
                    <div
                        class="z-40 w-32 p-2 -mb-1 text-sm leading-tight text-center text-white bg-black rounded-lg shadow-lg">
                        Since last month
                    </div>
                    <div class="absolute bottom-0 w-1 p-1 -mb-2 transform -rotate-45 bg-black ltr:ml-6 rtl:mr-6">
                    </div>
                </div>
            </div>
        </div>
        <div class="flex flex-row justify-between px-6 py-4">
            <slot name="icon" />
            <h2 class="self-center text-3xl"><span>$</span>{{ sidetitle }}</h2>
        </div>
        <div class="px-6 pb-6">
            <a class="text-sm hover:text-indigo-500" >{{bottomtext}}</a>
        </div>
    </div>
</template>