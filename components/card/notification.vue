<script setup lang="ts">
import moment from 'moment'

defineProps({
    noti: {
        type: Object,
        required: true,
    },
})

</script>
<template>
    <div
              class="flex flex-row flex-wrap items-center py-2 border-b border-gray-200 dark:border-gray-700 dark:bg-gray-900 dark:bg-opacity-40 dark:hover:bg-opacity-20 hover:bg-gray-100 bg-gray-50">
              <div class="flex-shrink w-1/4 max-w-full px-2 text-center">
                <div class="flex items-center justify-center w-8 h-8 mx-auto text-white rounded-full bg-primarys_focus">
                  <Icon :name="noti.icon ? noti.icon : 'mdi:rocket'" size="20"/>
                </div>
              </div>
              <div class="flex-shrink w-3/4 max-w-full px-2">
                <div class="text-sm font-bold">{{ noti.title }}</div>
                <div class="mt-1 text-sm text-gray-500" v-html="noti.message"></div>
                <div class="w-full mt-1 text-xs font-bold text-gray-400">{{moment(noti.last_action_date).fromNow()}}</div>
              </div>
            </div>
</template>