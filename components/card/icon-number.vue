<script setup lang="ts">
defineProps({
  title: {
    type: String,
    default: "New Contact",
  },
  icon: {
    type: String,
    default: "mdi:account-plus",
  },
  color: {
    type: String,
    default: "primarys",
  },
  number: {
	type: String,
	default: "0",
  },
});
</script>

<template>
  <div
    :class="`relative h-full p-6 overflow-hidden rounded-lg shadow-lg text-${color}_content bg-${color}`" @click="$emit('cardEmit')">
    <div class="absolute text-white -bottom-8">
   
      <Icon :name="icon" class="h-auto opacity-30 w-36" />
    </div>
    <div class="flex flex-row justify-between pb-3">
      <div class="flex flex-col">
        <h3 class="text-base font-bold">{{ title }}</h3>
      </div>
    </div>
    <div class="flex flex-row justify-between py-3">
      <div
        :class="`relative self-center w-20 h-20 text-center rounded-full text-${color} bg-white hover:opacity-25`"
      >
    
		<Icon :name="icon"  class="absolute w-8 h-8 transform -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2" />

      </div>
      <h2 class="self-center text-2xl font-bold">{{number}}</h2>
    </div>
  </div>
</template>
