<script setup lang="ts">
defineProps({
  title: {
    type: String,
    default: "Upgrade your services",
  },
  description: {
    type: String,
    default: "Upgrade your services",
  },
  buttonText: {
    type: String,
    default: "Upgrade Now",
  },
});

let contentEmit: any = getCurrentInstance()

const submit = () => {
  contentEmit.emit('submit')
}
</script>

<template>
  <div
    class="relative px-8 py-6 overflow-hidden rounded-lg shadow-lg theme_100"
  >
    <div class="absolute text-indigo-500 ltr:-right-8 rtl:-left-8 -bottom-8">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        fill="currentColor"
        class="h-auto opacity-10 w-36 bi bi-hdd-stack"
        viewBox="0 0 16 16"
      >
        <path
          d="M14 10a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h12zM2 9a2 2 0 0 0-2 2v1a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-1a2 2 0 0 0-2-2H2z"
        />
        <path
          d="M5 11.5a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0zm-2 0a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0zM14 3a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h12zM2 2a2 2 0 0 0-2 2v1a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H2z"
        />
        <path
          d="M5 4.5a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0zm-2 0a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0z"
        />
      </svg>
    </div>
    <div class="relative">
      <h4
        class="mb-3 text-2xl font-bold text-gray-800 dark:text-gray-200"
        v-html="title"
      ></h4>
      <p class="mb-6 text-gray-500" v-html="description"></p>
      <button @click="submit" class="o_btn_primarys">
        {{ buttonText }}
      </button>
    </div>
  </div>
</template>
