<script setup lang='ts'>
import vCardFactory from "vcards-js";

let currentClient = defineProps({
  info: {
    type: Object,
    default: () => {
      return {};
    },
  },
});
const open = ref(false);
const download = (vCardString: any, fileName: any) => {
  let fileURL = window.URL.createObjectURL(new Blob([vCardString]));
  let fileLink = document.createElement("a");
  fileLink.href = fileURL;
  fileLink.setAttribute("download", fileName);
  document.body.appendChild(fileLink);
  fileLink.click();
};
const generate_vCard = () => {
  const vCard = vCardFactory();
  //set basic properties shown before
  vCard.firstName = currentClient.info.first_name;
  vCard.middleName = currentClient.info.middle_name;
  vCard.lastName = currentClient.info.last_name;
  vCard.uid = currentClient.info.id;
  vCard.organization = currentClient.info.name;

  //link to image
  // vCard.photo.attachFromUrl('https://avatars2.githubusercontent.com/u/5659221?v=3&s=460', 'JPEG');

  //or embed image
  // vCard.photo.attachFromUrl('/path/to/file.jpeg');

  vCard.workPhone = currentClient.info.phone;
  // vCard.birthday = new Date(1985, 0, 1);
  // vCard.title = 'Software Developer';
  // vCard.url = 'https://github.com/enesser';
  // vCard.workUrl = 'https://acme-corporation/enesser';
  vCard.note = currentClient.info.note;

  //set other vitals
  // vCard.nickname = 'Scarface';
  // vCard.namePrefix = 'Mr.';
  // vCard.nameSuffix = 'JR';
  // vCard.gender = 'M';
  // vCard.anniversary = new Date(2004, 0, 1);
  // vCard.role = 'Software Development';

  //set other phone numbers
  // vCard.homePhone = '************';
  vCard.cellPhone = currentClient.info.cell;
  // vCard.pagerPhone = '************';

  //set fax/facsimile numbers
  // vCard.homeFax = '************';
  vCard.workFax = currentClient.info.fax;

  //set email addresses
  vCard.email = currentClient.info.email;
  // vCard.workEmail = '<EMAIL>';

  //set logo of organization or personal logo (also supports embedding, see above)
  // vCard.logo.attachFromUrl('https://avatars2.githubusercontent.com/u/5659221?v=3&s=460', 'JPEG');

  //set URL where the vCard can be found
  // vCard.source = location.value.href;

  //set address information
  // vCard.homeAddress.label = 'Home Address';
  // vCard.homeAddress.street = '123 Main Street';
  // vCard.homeAddress.city = 'Chicago';
  // vCard.homeAddress.stateProvince = 'IL';
  // vCard.homeAddress.postalCode = '12345';
  // vCard.homeAddress.countryRegion = 'United States of America';

  // vCard.workAddress.label = 'Work Address';
  // vCard.workAddress.street = '123 Corporate Loop\nSuite 500';
  // vCard.workAddress.city = 'Los Angeles';
  // vCard.workAddress.stateProvince = 'CA';
  // vCard.workAddress.postalCode = '54321';
  // vCard.workAddress.countryRegion = 'United States of America';

  //set social media URLs
  vCard.socialUrls["facebook"] = "";
  vCard.socialUrls["linkedIn"] = "";
  vCard.socialUrls["twitter"] = "";
  vCard.socialUrls["flickr"] = "";
  vCard.socialUrls["custom"] = "";
  console.log(vCard.getFormattedString());
  download(vCard.getFormattedString(), "vcardFile.vcf");
};

const openLinkNewTab = (site: string) => {
  if (site == undefined || site == "") {
    alert("No website link");
    return;
  }

  if (!site.includes("http:")) site = "http://" + site;

  if (process.client)
    window.open(
      site,
      "_blank",
      "toolbar=yes,scrollbars=yes,resizable=yes,top=500,left=500,width=400,height=400"
    );
};
</script>

<template>
  <!-- Card -->
  <div class="w-full theme_100 shadow-lg relative">
    <div class="flex flex-col h-full rounded-sm border border-gray-600">
      <div class="flex-grow p-5">
        <div class="flex justify-between items-start">
          <header>
            <div class="flex mb-2">
              <a
                class="relative inline-flex items-start mr-5"
                @click="$router.push(`/businesscards/${info.id}`)"
              >
                <IconsAvatar
                  :name="info.first_name"
                  classes="h-24 w-24 cursor-pointer"
                />
              </a>
              <div class="-mt-1 pr-1">
                <a
                  class="inline-flex text-gray-800 dark:text-gray-400 hover:text-gray-900"
                >
                  <h2 class="text-xl leading-snug justify-center font-semibold">
                    {{ info.name }}
                  </h2>
                  <br />
                </a>
                <!-- <h4 class="leading-snug justify-center">{{ info.first_name }} {{ info.last_name }}</h4> -->

                <div class="flex items-center">
                  <span
                    class="text-sm font-medium text-gray-400 -mt-0.5"
                  ></span>
                  <span class="text-xs"
                    >{{ info.first_name }} {{ info.last_name }}</span
                  >
                </div>
                <div class="flex items-center">
                  <span
                    class="text-sm font-medium text-gray-400 -mt-0.5"
                  ></span>
                  <span class="text-xs">{{ info.category }}</span>
                </div>
              </div>
            </div>
          </header>
          <!-- Menu button -->
          <div class="relative inline-flex flex-shrink-0">
            <button
              class="text-gray-400 hover:text-primarys rounded-full focus:outline-none focus-within:ring-2"
              :class="{ 'bg-gray-100 text-gray-500': open }"
              aria-haspopup="true"
              @click.prevent="generate_vCard"
              :aria-expanded="open"
            >
              <Icon
                name="carbon:download-study"
                class="animate-pulse text-secondary"
              />
            </button>
            <div
              class="origin-top-right z-10 absolute top-full right-0 min-w-[9rem] bg-white border border-gray-600 py-1.5 rounded shadow-lg overflow-hidden mt-1"
              @click.outside="open = false"
              @keydown.escape.window="open = false"
              v-show="open"
              x-transition:enter="transition ease-out duration-200 transform"
              x-transition:enter-start="opacity-0 -translate-y-2"
              x-transition:enter-end="opacity-100 translate-y-0"
              x-transition:leave="transition ease-out duration-200"
              x-transition:leave-start="opacity-100"
              x-transition:leave-end="opacity-0"
              x-cloak
            >
              <ul>
                <li>
                  <a
                    class="font-medium text-sm text-gray-600 hover:text-gray-800 flex py-1 px-3"
                    href="#0"
                    @click="open = false"
                    @focus="open = true"
                    @focusout="open = false"
                    >Option 1</a
                  >
                </li>
                <li>
                  <a
                    class="font-medium text-sm text-gray-600 hover:text-gray-800 flex py-1 px-3"
                    href="#0"
                    @click="open = false"
                    @focus="open = true"
                    @focusout="open = false"
                    >Option 2</a
                  >
                </li>
                <li>
                  <a
                    class="font-medium text-sm text-red-500 hover:text-red-600 flex py-1 px-3"
                    href="#0"
                    @click="open = false"
                    @focus="open = true"
                    @focusout="open = false"
                    >Remove</a
                  >
                </li>
              </ul>
            </div>
          </div>
        </div>
        <!-- Bio -->
        <div class="mt-2 h-10">
          <div class="text-sm">{{ info.formatted_address }}</div>
        </div>
      </div>
      <!-- Card footer -->
      <div class="border-t border-gray-600">
        <div class="flex divide-x divide-gray-600r">
          <a
            class="block flex-1 text-center text-sm text-gray-600 hover:text-primarys font-medium px-3 py-4 group"
            :href="`tel: ${info.phone}`"
          >
            <div class="flex items-center justify-center">
              <!-- <svg class="w-4 h-4 fill-current text-gray-400 group-hover:text-gray-500 flex-shrink-0 mr-2" viewBox="0 0 16 16">
                                    <path d="M11.7.3c-.4-.4-1-.4-1.4 0l-10 10c-.2.2-.3.4-.3.7v4c0 .6.4 1 1 1h4c.3 0 .5-.1.7-.3l10-10c.4-.4.4-1 0-1.4l-4-4zM4.6 14H2v-2.6l6-6L10.6 8l-6 6zM12 6.6L9.4 4 11 2.4 13.6 5 12 6.6z" />
                                </svg> -->
              <Icon
                name="mdi:phone"
                class="w-4 h-4 fill-current flex-shrink-0 mr-2"
              />
              <span>Call</span>
            </div>
          </a>
          <a
            class="block flex-1 text-center text-sm text-indigo-500 hover:text-primarys font-medium px-3 py-4"
            :href="`mailto: ${info.email}`"
          >
            <div class="flex items-center justify-center">
              <svg
                class="w-4 h-4 fill-current flex-shrink-0 mr-2"
                viewBox="0 0 16 16"
              >
                <path
                  d="M8 0C3.6 0 0 3.1 0 7s3.6 7 8 7h.6l5.4 2v-4.4c1.2-1.2 2-2.8 2-4.6 0-3.9-3.6-7-8-7zm4 10.8v2.3L8.9 12H8c-3.3 0-6-2.2-6-5s2.7-5 6-5 6 2.2 6 5c0 2.2-2 3.8-2 3.8z"
                />
              </svg>
              <span>Send Email</span>
            </div>
          </a>
          <a
            class="block flex-1 text-center text-sm text-gray-600 hover:text-primarys font-medium px-3 py-4 group"
            @click="openLinkNewTab(info.website)"
          >
            <div class="flex items-center justify-center">
              <!-- <svg class="w-4 h-4 fill-current text-gray-400 group-hover:text-gray-500 flex-shrink-0 mr-2" viewBox="0 0 16 16">
                                    <path d="M11.7.3c-.4-.4-1-.4-1.4 0l-10 10c-.2.2-.3.4-.3.7v4c0 .6.4 1 1 1h4c.3 0 .5-.1.7-.3l10-10c.4-.4.4-1 0-1.4l-4-4zM4.6 14H2v-2.6l6-6L10.6 8l-6 6zM12 6.6L9.4 4 11 2.4 13.6 5 12 6.6z" />
                                </svg> -->
              <Icon
                name="gg:website"
                class="w-4 h-4 fill-current flex-shrink-0 mr-2"
              />
              <span>Website</span>
            </div>
          </a>
        </div>
      </div>
    </div>
    <!-- <div class="absolute bottom-0 w-full -mt-60" v-if="info.image">
      <img class="w-full" :src="info.image.src" />
    </div> -->
  </div>
  
  <!-- More components -->
  <!-- <div x-show="open" class="fixed bottom-0 right-0 w-full md:bottom-8 md:right-12 md:w-auto z-60" x-data="{ open: true }">
    <div class="bg-gray-800 text-gray-50 text-sm p-3 md:rounded shadow-lg flex justify-between">
        <div>👉 <a class="hover:underline ml-1" href="https://cruip.com/mosaic/?ref=codepen-cruip-team-card" target="_blank">More components on Cruip.com</a></div>
        <button class="text-gray-500 hover:text-gray-400 ml-5" @click="open = false">
            <span class="sr-only">Close</span>
            <svg class="w-4 h-4 flex-shrink-0 fill-current" viewBox="0 0 16 16">
                <path d="M12.72 3.293a1 1 0 00-1.415 0L8.012 6.586 4.72 3.293a1 1 0 00-1.414 1.414L6.598 8l-3.293 3.293a1 1 0 101.414 1.414l3.293-3.293 3.293 3.293a1 1 0 001.414-1.414L9.426 8l3.293-3.293a1 1 0 000-1.414z" />
            </svg>
        </button>
    </div>
</div> -->
</template>