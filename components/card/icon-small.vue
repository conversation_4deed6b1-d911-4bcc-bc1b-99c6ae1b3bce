<script setup lang="ts">
defineProps({
    title: {
        type: String,
        required: true,
    },
    subtitle: {
        type: String,
        required: true,
    },
    sidetitle: {
        type: String,
        required: true,
    },
    bottomtext: {
        type: String,
        require: true
    },
    route: {
        type: String,
        require: true
    }
})
</script>

<template>
    <div class="h-full bg-white rounded-lg shadow-lg cursor-pointer dark:bg-gray-800" @click="$emit('cardEmit', route)">
        <div  class="relative w-full px-6 py-6 text-sm font-semibold">
           <div class="flex items-center justify-between w-full">
            <div class="flex items-center space-x-2">
                <slot name="icon" />
            <div>
                {{ title }}
               
            </div>
            </div>
            <div 
                class="float-right text-green-500">
                {{ subtitle }}
            </div>
           </div> 
        </div>
    </div>
</template>