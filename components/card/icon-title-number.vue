<script setup lang="ts">
defineProps({
    title: {
        type: String,
        default: "Upgrade your services",
    },
    percentage: {
        type: String,
        default: "12",
    },
    icon: {
        type: String,
        default: "bi bi-cart3",
    },
    color: {
    type: String,
    default: "primarys",
  },
    number: {
        type: String,
        default: "12",
    },
    subtitle: {
        type: String,
        default: "Since last month",
    },
})
</script>

<template>
    <div class="h-full rounded-lg shadow-lg theme_100">
                    <div
                      x-data="{ tooltips: false }"
                      class="relative px-6 pt-6 text-sm font-semibold"
                    >
                      {{ title }}
                      <div
                        x-on:mouseover="tooltips = true"
                        x-on:mouseleave="tooltips = false"
                        class="text-green-500 ltr:float-right rtl:float-left"
                      >
                        +{{percentage}}%
                        <div
                          class="absolute top-auto mb-3 bottom-full"
                          x-show.transition.origin.top="tooltips"
                          style="display: none"
                        >
                          <div
                            class="z-40 w-32 p-2 -mb-1 text-sm leading-tight text-center text-white bg-black rounded-lg shadow-lg"
                          >
                            Since last month
                          </div>
                          <div
                            class="absolute bottom-0 w-1 p-1 -mb-2 transform -rotate-45 bg-black ltr:ml-6 rtl:mr-6"
                          ></div>
                        </div>
                      </div>
                    </div>
                    <div class="flex flex-row justify-between px-6 py-4">
                      <div
                        :class="`relative flex items-center self-center text-center text-${color}_focus bg-${color} rounded-full w-14 h-14  dark:bg-opacity-40`"
                      >
                        <Icon :name="icon" class="w-8 h-8 m-auto" />
                      </div>
                      <h2 class="self-center text-3xl">{{number}}</h2>
                    </div>
                    <div class="px-6 pb-6">
                      <a class="cursor-pointer text-sm hover:text-indigo-500" @click="$emit('submit')"
                        >{{subtitle}}</a
                      >
                    </div>
                  </div>
</template>