<script setup lang="ts">
defineProps({
    item: {
        type: Object,
        default: () => {}
    },
    icon: {
        type: String,
        default: ''
    },
    src: {
        type: String,
        default: ''
    }
})
</script>

<template>
    <div
              class="w-full max-w-full px-3 mb-6"
            >
              <div
                class="relative flex flex-col min-w-0 break-words border-0 shadow-lg dark:shadow-gray-900 bg-base_100 dark:bg-gray-800 hover:shadow-2xl rounded-2xl bg-clip-border"
              >
                <div class="flex-auto p-4">
                  <div class="flex">
                    <div
                      class="inline-flex items-center justify-center p-1 transition-all duration-200 border rounded-lg w-19 h-19 text-size-base ease-soft-in-out"
                      :class="{'border-primary text-primary': item}"
                    >
                      <!-- :class="{ 'o_gradient': item.integratedType === 'Omni', 'bg-primary': item.integratedType === 'Outside' }" -->

                      <icons-avatar
                        :avatar="src" :name="item.name" :icon="icon" classes="w-full rounded h-16 w-16"
                      />
                    </div>
                    <div class="my-auto ml-4">
                      <h6 class="text-xl o_label">
                        {{ item.name ? item.name : item.title}}
                      </h6>
                    </div>
                    <div class="ml-auto group">
                        <Icon name="ph:dots-six-vertical-duotone" class="group-hover:text-primarys"/>
                      <div class="relative">
                        <button
                         :class="{'bg-success text-success': item}" class="inline-block py-3 pl-0 pr-2 font-bold text-center uppercase align-middle transition-all bg-transparent border-0 rounded-lg shadow-none cursor-pointer hover:text-primary leading-pro text-size-xs ease-soft-in tracking-tight-soft bg-150 bg-x-25 "
                          @click="showPlug = item.name"
                        >
                          <Icon :name="item.icon" />
                        </button>
                        <div class="absolute z-50 hidden -mt-12 rounded-lg shadow-lg dark:shadow-gray-900 bg-base_100 dark:bg-gray-900 group-hover:block">
                            <slot name="buttons" />

                        </div>
                      </div>
                    </div>
                  </div>
                  <hr
                    class="h-px mx-0 my-4 bg-transparent border-0 opacity-25 o_gradient"
                  >

                  <div class="h-24 mt-4 overflow-auto leading-normal text-size-sms scrollbar-hide" v-html="item.description" />
                  <hr
                    class="h-px mx-0 my-4 bg-transparent border-0 opacity-25 o_gradient"
                  >
                  <div class="flex flex-wrap -mx-3">
                    <div class="w-6/12 max-w-full px-3 flex-0">
                      <slot name="left" />
                    </div>
                    <div class="w-6/12 max-w-full px-3 text-right flex-0">
                        <slot name="right" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
</template>
    
