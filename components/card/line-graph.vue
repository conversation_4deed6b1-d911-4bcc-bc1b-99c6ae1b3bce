<script setup lang="ts">
import { Chart, registerables, CategoryScale } from "chart.js";

const uiCardLineGraph = defineProps({
    title: {
        type: String,
        default: "New Contact",
    },
    id: {
        type: String,
        default: "LineArea",
    },
    labels: {
        type: Array,
        default: [
            "Aug 1",
            "Aug 2",
            "Aug 3",
            "Aug 4",
            "Aug 5",
            "Aug 6",
            "Aug 7",
            "Aug 8",
            "Aug 9",
            "Aug 10",
            "Aug 11",
            "Aug 12",
        ],
    },
    data: {
        type: Array,
        default: [
            120,
            462,
            323,
            184,
            187,
            362,
            324,
            429,
            289,
            559,
            461,
            394,
            541,
        ],
    },
})

const open = ref(false);
Chart.register(...registerables, CategoryScale);
onMounted(() => {
  console.log("mounted");
  if (process.client) {
    const text_primary_500 = "#6366F1";
    const text_secondary_500 = "#EC4899";
    const text_yellow_500 = "#F59E0B";
    const text_gray_500 = "#84848f";

    Chart.defaults.color = text_gray_500;

    // CRM CHART LINE AREA
    const chart_linearea = document.getElementById(uiCardLineGraph.id);
    if (chart_linearea != null) {
      const ctla = chart_linearea.getContext("2d");

      const gradientIndigo = ctla.createLinearGradient(0, 230, 0, 50);
      gradientIndigo.addColorStop(1, hexToRGBA(text_primary_500, 0.3));
      gradientIndigo.addColorStop(0.2, hexToRGBA(text_primary_500, 0.02));
      gradientIndigo.addColorStop(0, hexToRGBA(text_primary_500, 0.01));

      const LineArea = new Chart(ctla, {
        type: "line",
        data: {
          labels: uiCardLineGraph.labels,
          datasets: [
            {
              fill: {
                target: "origin",
              },
              borderColor: text_primary_500,
              backgroundColor: gradientIndigo,
              label: "Deals",
              tension: 0.3,
              pointBackgroundColor: text_primary_500,
              pointBorderWidth: 0,
              pointHitRadius: 30,
              pointHoverBackgroundColor: text_primary_500,
              pointHoverRadius: 5,
              pointRadius: 0,
              data: uiCardLineGraph.data,
            },
          ],
        },
        options: {
          scales: {
            x: {
              display: true,
              grid: {
                display: false,
              },
            },
            y: {
              display: true,
              grid: {
                borderDash: [4, 4],
              },
            },
          },
          animation: {
            y: {
              duration: 4000,
              from: 500,
            },
          },
          plugins: {
            legend: {
              display: false,
            },
          },
        },
      });
    }
  }
});
</script>

<template>
  <div class="h-full p-6 rounded-lg shadow-lg theme_100">
    <div class="flex flex-row justify-between pb-3">
      <div class="flex flex-col">
        <h3 class="text-base font-bold">{{ title }}</h3>
      </div>
      <div class="relative">
        <button
          @click="open = !open"
          class="text-gray-500 transition-colors duration-200 hover:text-gray-600 dark:hover:text-gray-400 focus:outline-none hover:outline-none"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            class="w-6 h-6 bi bi-three-dots"
            viewBox="0 0 16 16"
          >
            <path
              d="M3 9.5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3z"
            />
          </svg>
        </button>
        <div
          v-show="open"
          
          class="absolute z-10 origin-top-right bg-white border border-gray-200 rounded ltr:right-0 rtl:left-0 rounded-t-non dark:bg-gray-800 dark:border-gray-700"
          style="min-width: 12rem"
        >
          <a
            class="block px-3 py-2 hover:bg-gray-100 focus:bg-gray-100 dark:hover:bg-gray-900 dark:hover:bg-opacity-20 dark:focus:bg-gray-900"
            href="#"
            >Latest 30 Days</a
          >
          <a
            class="block px-3 py-2 hover:bg-gray-100 focus:bg-gray-100 dark:hover:bg-gray-900 dark:hover:bg-opacity-20 dark:focus:bg-gray-900"
            href="#"
            >Latest 90 Days</a
          >
          <a
            class="block px-3 py-2 hover:bg-gray-100 focus:bg-gray-100 dark:hover:bg-gray-900 dark:hover:bg-opacity-20 dark:focus:bg-gray-900"
            href="#"
            >Latest 150 Days</a
          >
        </div>
      </div>
    </div>
    <div class="relative">
      <canvas class="max-w-100" :id="id"></canvas>
    </div>
  </div>
</template>
