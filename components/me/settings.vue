<script setup lang="ts">
const { updateToId } = database()
const { notificationOpen } = states()

const currentClient: any = useState('currentClient', () => {
        return {}
    })

function input_address(data: { path: undefined; bubbles: undefined; utc_offset: any; lat: any; lng: any; vicinity: any; adr_address: any; formatted_address: string; place_id: any; url: any }) {
  if (data && data.path === undefined && data.bubbles === undefined) {
    currentClient.value.utc_offset = data.utc_offset
    currentClient.value.lat = data.lat
    currentClient.value.lng = data.lng
    currentClient.value.vicinity = data.vicinity
    currentClient.value.adr_address = data.adr_address
    currentClient.value.formatted_address = data.formatted_address
    currentClient.value.place_id = data.place_id
    currentClient.value.adr_url = data.url
    const split_address = data.formatted_address.split(',')
    currentClient.value.address = split_address[0]
    currentClient.value.city = split_address[1]
    currentClient.value.state = split_address[2]
    currentClient.value.zip = split_address[3]
    currentClient.value.country = split_address[4]
  }
}

async function save() {
  await updateToId(currentClient.value.id, 'contacts', currentClient.value)
  notificationOpen.value = {
    open: true,
    text: 'Saved',
    color: 'bg-success',
    icon: 'mdi-check',
    timeout: 2000,
  }
}

function addAvatar(data: any) {
  currentClient.value.avatar_url = data[0]
}
const showImageUpload = ref(false)
</script>

<template>
  <div class="o_card">
    <div v-if="showImageUpload" class="o_modal">
      <file-manager @input="addAvatar" @close="showImageUpload = false" />
    </div>
    <div class="flex justify-end">
      <Icon name="mdi:image" class="text-4xl" @click="showImageUpload = !showImageUpload" />
    </div>
    <div>
      <label class="o_label" for="First_name">First name</label>
      <input v-model="currentClient.first_name" type="text" name="First_name" class="o_input">
    </div>
    <div>
      <label class="o_label" for="First_name">Last name</label>
      <input v-model="currentClient.last_name" type="text" name="First_name" class="o_input">
    </div>

    <div>
      <label class="o_label" for="Cell">Contact No</label>
      <input v-model="currentClient.cell" type="text" name="Cell" class="o_input">
    </div>

    <div>
      <label class="o_label" for="Email">Email</label>
      <input v-model="currentClient.email" type="text" name="Email" class="o_input">
    </div>

    <div>
      <label class="o_label" for="Email">Address</label>
      <small>{{ currentClient.address }}</small>
      <google-autocomplete @input="input_address" />
    </div>
    <button class="bg-primary rounded w-full mt-2 text-primary_content" @click="save">
      SAVE
    </button>
  </div>
</template>
