<script setup lang="ts">
const { currentClient: mainUserNavbar, meUser } = useMe()
const avatar = computed(() => {
  if (mainUserNavbar.value) {
    if (mainUserNavbar.value.avatar)
      return mainUserNavbar.value.avatar.src

    return ''
  }
  return ''
})
const editEmit: any = getCurrentInstance()
function editProfile() {
  editEmit.emit('edit')
}
</script>

<template>
  <div class="o_card group">
    <div class="grid grid-cols-3 gap-4 ">
      <div class="flex space-x-2 items-center">
        <div>
          <icons-avatar
            :avatar="avatar" :name="mainUserNavbar.first_name"
            @click="$router.push('/o/me/dashboard')"
          />
        </div>
        <div class="flex space-x-2 o_label_small">
          <h1>{{ mainUserNavbar.first_name }}</h1>
          <h1>{{ mainUserNavbar.last_name }}</h1>
        </div>
      </div>
      <div class="flex justify-center items-center o_label_small">
        <h1>{{ mainUserNavbar.email }}</h1>
        <h1>{{ mainUserNavbar.phone }}</h1>
      </div>
      <div class="flex justify-end space-x-2">
        <!-- <h1>{{ mainUserNavbar.first_name }}</h1>
      <h1>{{ mainUserNavbar.last_name }}</h1> -->
        <button class="o_btn_icon_square" @click="editProfile">
          <Icon name="mdi:pencil" />
        </button>
      </div>
    </div>
    <div class="hidden group-hover:block">
      <div class="flex o_label_small space-x-6 justify-center">
        <div class="flex space-x-2">
          <div class="font-bold mr-1">
            User:
          </div> {{ meUser.id }}
        </div>
        <div class="flex">
          <div class="font-bold mr-1">
            Customer:
          </div> {{ mainUserNavbar.id }}
        </div>
      </div>
    </div>
  </div>
</template>
