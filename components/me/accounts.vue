<script setup lang="ts">
import { collection, onSnapshot, query, where } from 'firebase/firestore'

// Use the new Firebase initialization
const { firestore } = useFirebase();
const { meUser: currentUser, meUserAccounts: accounts } = useMe()

if (currentUser.value.id) {
  const q = query(collection(firestore, 'accounts'), where('access_uid', 'array-contains', currentUser.value.id))
  const unsubscribe = onSnapshot(q, (querySnapshot) => {
    const result: any = []
    querySnapshot.forEach((doc) => {
      result.push({ id: doc.id, ...doc.data() })
    })
    accounts.value = result
  })
}

const showUploads = ref(false)
</script>

<template>
  <div class="o_card w-full">
    <div class="flex justify-between">
      <div class="o_label">
        Accounts
      </div>
      <Icon name="mdi:plus" class="text-2xl" @click="showUploads = true" />
    </div>

    <onClickOutside @trigger="showUploads = false">
      <div v-if="showUploads" class="o_modal">
        <file-manager @close="showUploads = false" />
      </div>
    </onClickOutside>

    <div class="w-full">
      <div
        v-for="(acc, index) in accounts" :key="index"
        class="border-b-4 border-base-200  flex w-full justify-between my-1 p-2 hover:bg-base-200 hover:shadow-xl cursor-pointer"
      >
        <p>{{ acc.name }}</p>
        <p>{{ acc.description }}</p>
        <p> {{ acc.coins }}</p>
      </div>
    </div>
  </div>
</template>
