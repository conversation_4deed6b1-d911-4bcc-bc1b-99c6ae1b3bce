<script setup lang="ts">
import { collection, limit, onSnapshot, query, where } from 'firebase/firestore'
const { meUser: currentUser } = useMe()
const { firestore } = useFirebase()

const media: any = ref([])
if (currentUser.value.id && firestore) {
  const q = query(collection(firestore, 'uploads'), where('access_uid', 'array-contains', currentUser.value.id), limit(9))
  const unsubscribe = onSnapshot(q, (querySnapshot) => {
    const result: any = []
    querySnapshot.forEach((doc) => {
      result.push({ id: doc.id, ...doc.data() })
    })
    media.value = result
  })

  // onBeforeUnmount(() => unsubscribe)
}

const showUploads = ref(false)
</script>

<template>
  <div class="o_card w-full">
    <div class="flex justify-between mb-2">
      <div class="o_label">
        Latest Media
      </div>
      <div class="o_btn_icon_square" @click="showUploads = true">
        <Icon name="mdi:plus" class="text-2xl" @click="showUploads = true" />
      </div>
    </div>

    <onClickOutside @trigger="showUploads = false">
      <div v-if="showUploads" class="o_modal">
        <file-manager @close="showUploads = false" />
      </div>
    </onClickOutside>

    <div class="grid grid-cols-3 gap-2">
      <div v-for="(img, index) in media" :key="index" class="shadow p-1 rounded-xl flex items-center justify-center">
        <document-type :doc="img" classes="w-24 min-h-24" />
      </div>
    </div>
  </div>
</template>
