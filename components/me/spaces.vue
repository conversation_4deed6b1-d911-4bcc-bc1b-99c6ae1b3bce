<script setup lang="ts">

const { meUser: currentUser, meUserSpaces, meUserAccounts: accounts } = useMe()
const { currentSpace } = space()

const showUploads = ref(false)
</script>

<template>
  <div class="o_card w-full">
    <div class="flex justify-between">
      <div class="o_label">
        Spaces
      </div>
      <div class="o_btn_icon_square" @click="showUploads = true">
        <Icon name="mdi:plus" />
      </div>
    </div>

    <onClickOutside @trigger="showUploads = false">
      <div v-if="showUploads" class="o_modal w-1/2 h-2/3 scrollbar-hide">
        <space-register @close="showUploads = false" />
      </div>
    </onClickOutside>

    <div class="w-full h-80 overflow-auto scrollbar-hide">
      <div
        v-for="(space, index) in meUserSpaces" :key="index" :class="{ 'theme-secondary': currentSpace.id === space.id }"
        class="border-b-4 border-base-200  flex w-full justify-between my-1 p-2 hover:bg-base-200 hover:shadow-xl cursor-pointer"
        @click="currentSpace = space"
      >
        <p>{{ space.name }}</p>
        <p>{{ space.industry }}</p>
      </div>
    </div>
  </div>
</template>
