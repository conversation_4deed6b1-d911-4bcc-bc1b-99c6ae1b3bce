<script setup lang="ts">
let meProfileProps = defineProps({
  schema: {
    type: Object, default: () => { return {} },
  },
})
const {
  updateToId,
  getCollectionWhere,
} = database();
const currentClient: any = useState("currentClient", () => {
  return {};
});
const open = ref(false)

let options = computed(()=> {
  if(meProfileProps.schema.options) {
    return meProfileProps.schema.options
  }else{
    return {}
  }
})

const getInviteId = async () => {
  let new_id = makeid(15)
  currentClient.value.invite_id = new_id
  await updateToId(currentClient.value.id, "contacts", {invite_id: new_id});

}
</script>
<template>
<layouts-main title="Profile">
    <template #buttons
      ><tooltip-icon
        text="Dashboard"
        side="left"
        icon="line-md:grid-3-filled"
        @submit="$router.push('/o/performance')"
    /></template>
    <template #content>
          <!-- row -->
          <div class="flex flex-row flex-wrap">

            <div class="flex-shrink w-full max-w-full mb-6">
             <me-profile-banner @open="open = true" :schema="schema"/>
              <div class="flex flex-row flex-wrap -mx-4">
                <div class="flex-shrink w-full max-w-full px-4 mb-8 lg:w-1/3">
                  <div class="h-full p-6 rounded-lg shadow-md bg-white dark:bg-gray-800">
                    <div class="flex flex-row justify-between pb-4 border-b border-gray-200 dark:border-gray-700 mb-4">
                      <div class="flex flex-col">
                        <h3 class="text-lg font-bold text-gray-800 dark:text-gray-200">Information</h3>
                        <small class="text-gray-500 dark:text-gray-400">ID: {{ currentClient.id }}</small>
                      </div>
                    </div>
                    <me-profile-card :schema="schema"/>
                  </div>
                </div>
                <div class="flex-shrink w-full max-w-full px-4 lg:w-2/3">
                  <div class="h-full rounded-lg shadow-md bg-white dark:bg-gray-800">
                    <calendar />
                  </div>
                </div>
                <!-- <div class="flex-shrink w-full max-w-full px-4 mb-8 lg:w-1/3">
                  <div class="p-6 rounded-lg shadow-lg theme_100">
                    <div class="flex flex-row justify-between pb-3">
                      <div class="flex flex-col">
                        <h3 class="text-base font-bold">Messages</h3>
                      </div>
                    </div>
                    <ul>
                      <li>
                        <a href="#" class="flex my-6">
                          <div class="relative block">
                            <img alt="profil" src="" class="object-cover w-10 h-10 mx-auto rounded-full ">
                          </div>
                          <div class="flex flex-col">
                            <span class="mb-1 text-sm font-semibold text-gray-900 dark:text-white ltr:ml-2 rtl:mr-2">Carlos Garcia</span>
                            <span class="text-sm text-gray-400 dark:text-gray-300 ltr:ml-2 rtl:mr-2">What do you think about this project?</span>
                          </div>
                        </a>
                      </li>
                      <li>
                        <a href="#" class="flex my-6">
                          <div class="relative block">
                            <img alt="profil" src="" class="object-cover w-10 h-10 mx-auto rounded-full ">
                          </div>
                          <div class="flex flex-col">
                            <span class="mb-1 text-sm font-semibold text-gray-900 dark:text-white ltr:ml-2 rtl:mr-2">Roman davis</span>
                            <span class="text-sm text-gray-400 dark:text-gray-300 ltr:ml-2 rtl:mr-2">What do you think about this project?</span>
                          </div>
                        </a>
                      </li>
                      <li>
                        <a href="#" class="flex my-6">
                          <div class="relative block">
                            <img alt="profil" src="" class="object-cover w-10 h-10 mx-auto rounded-full ">
                          </div>
                          <div class="flex flex-col">
                            <span class="mb-1 text-sm font-semibold text-gray-900 dark:text-white ltr:ml-2 rtl:mr-2">Jessica jane</span>
                            <span class="text-sm text-gray-400 dark:text-gray-300 ltr:ml-2 rtl:mr-2">What do you think about this project?</span>
                          </div>
                        </a>
                      </li>
                    </ul>
                  </div>
                </div> -->
                <!-- <div class="flex-shrink w-full max-w-full px-4 mb-8 lg:w-2/3">
                  <div class="p-6 mb-6 rounded-lg shadow-lg theme_100">
                    <div class="flex flex-row justify-between pb-3">
                      <div class="flex flex-col">
                        <h3 class="text-base font-bold">Tailnet Inc</h3>
                      </div>
                    </div>
                    <div class="flex items-center justify-between my-3">
                      <span class="flex items-center px-2 py-1 text-xs font-semibold text-yellow-700 bg-yellow-200 rounded">On Progress</span>
                      <span class="flex items-center px-2 py-1 text-xs font-semibold text-red-400 bg-white border border-red-400 rounded">Due date: 15-11-2022</span>
                    </div>
                    <div class="relative mb-4">
                      <a href="#">
                        <img class="inline-block w-12 h-12 max-w-full transform bg-gray-300 border-2 border-gray-200 rounded-full shadow-xl ltr:-mr-4 rtl:-ml-4 hover:-translate-y-1" src="" alt="Image Description">
                      </a>
                      <a href="#">
                        <img class="inline-block w-12 h-12 max-w-full transform bg-gray-300 border-2 border-gray-200 rounded-full shadow-xl ltr:-mr-4 rtl:-ml-4 hover:-translate-y-1" src="" alt="Image Description">
                      </a>
                      <a href="#">
                        <img class="inline-block w-12 h-12 max-w-full transform bg-gray-300 border-2 border-gray-200 rounded-full shadow-xl ltr:-mr-4 rtl:-ml-4 hover:-translate-y-1" src="" alt="Image Description">
                      </a>
                    </div>
                    <div class="w-full h-2 mt-2 bg-gray-200 rounded-full">
                      <div class="h-full text-xs text-center text-white bg-indigo-500 rounded-full" style="width:86%">
                      </div>
                    </div>
                    <div class="flex justify-between mt-3">
                      <div>
                        <span class="inline-block text-sm text-gray-500 dark:text-gray-100">Task done : <span class="font-bold text-gray-700 dark:text-white">43</span>/50</span>
                      </div>
                      <div>
                        <span class="px-2 py-1 text-xs font-semibold text-green-500 rounded bg-green-50">Front End</span>
                        <span class="px-2 py-1 text-xs font-semibold text-blue-500 bg-blue-100 rounded">UI/UX</span>
                      </div>
                    </div>
                  </div>
                  <div class="p-6 mb-6 rounded-lg shadow-lg theme_100">
                    <div class="flex flex-row justify-between pb-3">
                      <div class="flex flex-col">
                        <h3 class="text-base font-bold">Google Inc</h3>
                      </div>
                    </div>
                    <div class="flex items-center justify-between my-3">
                      <span class="flex items-center px-2 py-1 text-xs font-semibold text-gray-500 bg-gray-200 rounded">Complete</span>
                      <span class="flex items-center px-2 py-1 text-xs font-semibold text-gray-500 bg-gray-200 rounded">Due date: 1-08-2022</span>
                    </div>
                    <div class="relative mb-4">
                      <a href="#">
                        <img class="inline-block w-12 h-12 max-w-full transform bg-gray-300 border-2 border-gray-200 rounded-full shadow-xl ltr:-mr-4 rtl:-ml-4 hover:-translate-y-1" src="" alt="Image Description">
                      </a>
                      <a href="#">
                        <img class="inline-block w-12 h-12 max-w-full transform bg-gray-300 border-2 border-gray-200 rounded-full shadow-xl ltr:-mr-4 rtl:-ml-4 hover:-translate-y-1" src="" alt="Image Description">
                      </a>
                      <a href="#">
                        <img class="inline-block w-12 h-12 max-w-full transform bg-gray-300 border-2 border-gray-200 rounded-full shadow-xl ltr:-mr-4 rtl:-ml-4 hover:-translate-y-1" src="" alt="Image Description">
                      </a>
                    </div>
                    <div class="w-full h-2 mt-2 bg-gray-200 rounded-full">
                      <div class="h-full text-xs text-center text-white bg-green-500 rounded-full" style="width:100%">
                      </div>
                    </div>
                    <div class="flex justify-between mt-3">
                      <div>
                        <span class="inline-block text-sm text-gray-500 dark:text-gray-100">Task done : <span class="font-bold text-gray-700 dark:text-white">50</span>/50</span>
                      </div>
                      <div>
                        <span class="px-2 py-1 text-xs font-semibold text-green-500 rounded bg-green-50">Front End</span>
                        <span class="px-2 py-1 text-xs font-semibold text-blue-500 bg-blue-100 rounded">UI/UX</span>
                      </div>
                    </div>
                  </div>
                </div> -->
              </div>

              <!-- Modal -->
              <div v-show="open" tabindex="0" class="fixed inset-0 z-50 w-full h-full py-6 overflow-auto">
                <div
                  class="relative z-50 max-w-full p-3 mx-auto my-0 transition-all duration-300 transform"
                  style="max-width:700px;"
                  v-show="open"
                  :class="open ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-4'"
                >
                  <me-profile-edit @close="open = false"></me-profile-edit>
                </div>
                <div class="fixed top-0 bottom-0 left-0 right-0 z-40 w-full h-full overflow-auto bg-black bg-opacity-50 backdrop-blur-sm transition-opacity duration-300" :class="open ? 'opacity-100' : 'opacity-0'"></div>
              </div>
            </div>
          </div>
       </template>
       </layouts-main>

</template>
