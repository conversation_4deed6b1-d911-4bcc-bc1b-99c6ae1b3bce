<script setup lang="ts">
const currentClient: any = useState("currentClient", () => {
  return {};
});

const location = useBrowserLocation();

const url = `${location.value.origin}/c/user/${currentClient.value.slug}`;
</script>

<template>
  <div class="py-4">
    <div class="mb-4 text-gray-600 dark:text-gray-400" v-html="currentClient.about"></div>
    <div class="my-4 border-t border-gray-200 dark:border-gray-700"></div>
    <div class="ltr:text-left rtl:text-right space-y-3">
      <div class="flex flex-col">
        <p class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Full Name</p>
        <p class="text-gray-800 dark:text-gray-200">
          {{ currentClient.first_name }} {{ currentClient.last_name }}
        </p>
      </div>
      <div class="flex flex-col">
        <p class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Mobile</p>
        <p class="text-gray-800 dark:text-gray-200">
          {{ currentClient.phone || 'Not provided' }}
        </p>
      </div>
      <div class="flex flex-col">
        <p class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Email</p>
        <p class="text-gray-800 dark:text-gray-200">
          {{ currentClient.email }}
        </p>
      </div>
      <div class="flex flex-col">
        <p class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Location</p>
        <p class="text-gray-800 dark:text-gray-200">
          {{ currentClient.formatted_address || 'Not provided' }}
        </p>
      </div>
      <div class="flex flex-col">
        <p class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Languages</p>
        <p class="text-gray-800 dark:text-gray-200">
          {{ currentClient.language || 'Not provided' }}
        </p>
      </div>
      <div class="flex flex-col">
        <p class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">VCard</p>
        <a
          :href="url"
          target="_blank"
          class="text-[#0072ff] hover:text-[#0054bb] transition-colors duration-200 truncate"
        >
          {{ currentClient.slug || url }}
        </a>
      </div>
    </div>
  </div>
</template>
