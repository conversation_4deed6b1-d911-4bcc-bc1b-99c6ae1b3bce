<script setup lang="ts">
const currentClient: any = useState('currentClient', () => {
        return {}
    })
const { setToId } = database()
const profileEditEmit: any = getCurrentInstance()
const notificationOpen = useState<any>('notificationOpen', () => {
  return {
    open: false,
    text: '',
    color: 'o_gradient',
  }
})
const saveProfile = async () => {
  console.log('slug',currentClient.value.slug)
  if(currentClient.value.slug === undefined){
    const name = currentClient.value.first_name + ' ' + currentClient.value.last_name
    const slug = name.toLowerCase().replace(/\s/g, '_')
console.log('slug', slug)
currentClient.value.slug = slug
  }


  setToId(currentClient.value.id, 'contacts', currentClient.value)
  notificationOpen.value.open = true
  notificationOpen.value.text = 'Profile saved'
  notificationOpen.value.color = 'bg-blue-500'
  profileEditEmit.emit('close')
}

const uploadAdd = (data: any) => {
  console.log(data)
  currentClient.value.avatar = data[0]
  currentClient.value.avatar_url = data[0].src

}

const uploadAddBackground = (data: any) => {
  console.log(data)
  currentClient.value.background = data[0]
  currentClient.value.background_url = data[0].src

}

function inputsAbout(data) {
  if (data && data.path === undefined && data.bubbles === undefined)
    currentClient.value.about = data
}

function inputsDescription(data) {
  if (data && data.path === undefined && data.bubbles === undefined)
    currentClient.value.description = data
}
// function addressinput(data) {
//   if (data && data.path === undefined && data.bubbles === undefined){
//   console.log('DATA', data)
//   currentClient.value.address = data


//   }
// }

function addressinput(data: { path: undefined; utc_offset: any; lat: any; lng: any; vicinity: any; adr_address: any; formatted_address: string; place_id: any; url: any }) {
  if (data && data.path === undefined && data.bubbles === undefined) {
    currentClient.value.utc_offset = data.utc_offset
    currentClient.value.lat = data.lat
    currentClient.value.lng = data.lng
    currentClient.value.vicinity = data.vicinity
    currentClient.value.adr_address = data.adr_address
    currentClient.value.formatted_address = data.formatted_address
    currentClient.value.place_id = data.place_id
    currentClient.value.adr_url = data.url
    const split_address = data.formatted_address.split(',')
    currentClient.value.address = split_address[0]
    currentClient.value.city = split_address[1]
    currentClient.value.state = split_address[2]
    currentClient.value.zip = split_address[3]
    currentClient.value.country = split_address[4]
  }
}

const inputcell = (val: any) => {
  if(val === undefined || val.isTrusted) return
  currentClient.value.cell = val;
};

const inputphone = (val: any) => {
  if(val === undefined || val.isTrusted) return
  currentClient.value.phone = val;
};
</script>

<template>
  <div>
    <div
      class="flex flex-col overflow-hidden rounded-lg shadow-md bg-white dark:bg-gray-800 valid-form">
      <button @click="$emit('close')"
        class="absolute top-0 right-0 w-8 h-8 m-4 flex items-center justify-center text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#0072ff] focus:ring-opacity-50 rounded-full rtl:left-0">
        <Icon name="mdi:close" size="24px" />
      </button>
      <!-- modal title -->
      <div class="px-6 py-4 text-xl font-bold text-gray-800 dark:text-gray-200 border-b border-gray-200 dark:border-gray-700">Edit Profile</div>
      <!-- modal content -->
      <div class="flex-grow p-6">
        <div class="flex flex-row flex-wrap -mx-4">
          <div class="flex-shrink w-full max-w-full px-4">
            <div class="pb-8 overflow-hidden bg-white rounded-lg dark:bg-gray-800">
              <div class="relative h-40 overflow-hidden">
                <img v-if="currentClient.avatar" :src="currentClient.background ? currentClient.background.src : ''"
                  class="w-full object-cover" alt="Profile background">
              </div>
              <div class="relative flex justify-center -mt-10">
                <img v-if="currentClient.avatar" :src="currentClient.avatar ? currentClient.avatar.src : ''"
                  class="w-24 h-24 -mt-3 bg-gray-100 dark:bg-gray-700 border-4 border-white dark:border-gray-800 rounded-full shadow-md object-cover" alt="Profile avatar">
              </div>
            </div>
          </div>
          <div class="flex-shrink w-full max-w-full px-4 mb-6 form-group md:w-1/2">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Avatar (80x80)</label>
            <FileButton @upload-add="uploadAdd" />
          </div>
          <div class="flex-shrink w-full max-w-full px-4 mb-6 form-group md:w-1/2">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Cover (1287x160)</label>
            <FileButton @upload-add="uploadAddBackground" />
          </div>
          <div class="flex-shrink w-full max-w-full px-4 mb-6 form-group md:w-1/2">
            <label for="inputname" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">First name</label>
            <input v-model="currentClient.first_name" type="text"
              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-700 dark:text-white"
              id="inputname" required>
          </div>
          <div class="flex-shrink w-full max-w-full px-4 mb-6 form-group md:w-1/2">
            <label for="inputlast" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Last name</label>
            <input v-model="currentClient.last_name" type="text"
              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-700 dark:text-white"
              id="inputlast">
          </div>
          <div class="flex-shrink w-full max-w-full px-4 mb-6 form-group md:w-1/2">
            <label for="inputEmail4" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email</label>
            <input v-model="currentClient.email" type="email"
              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-700 dark:text-white"
              id="inputEmail4" required>
          </div>
          <div class="flex-shrink w-full max-w-full px-4 mb-6 form-group md:w-1/2">
            <label for="inputPassword4" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Password</label>
            <input type="password"
              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-700 dark:text-white"
              id="inputPassword4" required>
          </div>
          <div class="flex-shrink w-full max-w-full px-4 mb-6 form-group">
            <label for="inputnumber" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Phone number</label>
            <forms-inputs-phone v-model="currentClient.phone" @input="inputphone" />
          </div>
          <div class="flex-shrink w-full max-w-full px-4 mb-6 form-group">
            <label for="inputnumber" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Cell number</label>
            <forms-inputs-phone v-model="currentClient.cell" @input="inputcell" />
          </div>
          <div class="flex-shrink w-full max-w-full px-4 mb-6 form-group md:w-1/2">
            <label for="inputnumber" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Fax number</label>
            <input v-model="currentClient.fax" type="text"
              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-700 dark:text-white">
          </div>
          <div class="flex-shrink w-full max-w-full px-4 mb-6 form-group md:w-1/2">
            <label for="inputlocation" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Location</label>
            <google-autocomplete @input="addressinput" />
          </div>
          <div class="flex-shrink w-full max-w-full px-4 mb-6 form-group">
            <label for="inputlocation" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Note</label>
            <input type="text" v-model="currentClient.note"
              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-700 dark:text-white"
              id="inputlocation">
          </div>
          <div class="flex-shrink w-full max-w-full px-4 mb-6 form-group">
            <label for="inputshort" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Short description</label>
            <forms-inputs-quill @input="inputsDescription" :placeholder="currentClient.description" :edit="true"
              :editorOptions="{theme: 'bubble'}" />
          </div>
          <div class="flex-shrink w-full max-w-full px-4 mb-6 form-group">
            <label for="inputlong" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">About description</label>
            <forms-inputs-quill @input="inputsAbout" :placeholder="currentClient.about" :edit="true"
              :editorOptions="{theme: 'bubble'}" />
          </div>
        </div>
      </div>
      <div class="flex justify-end px-6 py-4 border-t border-gray-200 dark:border-gray-700 space-x-3">
        <button @click="$emit('close')" type="button"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-[#0072ff] focus:ring-opacity-50 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600">
          Cancel
        </button>
        <button @click="saveProfile"
          class="px-4 py-2 text-sm font-medium text-white bg-[#0072ff] border border-transparent rounded-md shadow-sm hover:bg-[#0054bb] focus:outline-none focus:ring-2 focus:ring-[#0072ff] focus:ring-opacity-50">
          Save Changes
        </button>
      </div>
    </div>
  </div>
</template>