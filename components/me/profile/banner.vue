<script setup lang="ts">
const currentClient: any = useState("currentClient", () => {
  return {};
});

let meProfileProps = defineProps({
  schema: {
    type: Object,
    default: () => {
      return {};
    },
  },
});
let options = computed(() => {
  if (meProfileProps.schema.options) {
    return meProfileProps.schema.options;
  } else {
    return {};
  }
});
</script>

<template>
  <div class="mb-8 overflow-hidden rounded-lg shadow-md bg-white dark:bg-gray-800">
    <div class="relative h-40 overflow-hidden group">
      <img
        v-if="currentClient.avatar"
        :src="currentClient.background ? currentClient.background.src : ''"
        class="w-full object-cover"
        alt="Profile background"
      />
      <div
        class="absolute top-4 ltr:right-4 rtl:left-4"
        v-if="options.can_edit.value"
      >
        <button
          @click="$emit('open')"
          type="button"
          class="group-hover:opacity-100 opacity-0 py-2 px-4 inline-block text-center rounded-md text-white bg-[#0072ff] hover:bg-[#0054bb] shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-[#0072ff] focus:ring-opacity-50"
        >
          <span class="flex items-center">
            <span>Edit cover</span>
            <Icon
              name="mdi:camera"
              class="ml-2"
              size="16px"
            />
          </span>
        </button>
      </div>
    </div>
    <div class="relative flex justify-center -mt-10">
      <a @click="$emit('open')" class="z-30 group" href="javascript:;">
        <img
          v-if="currentClient.avatar"
          :src="currentClient.avatar ? currentClient.avatar.src : ''"
          class="w-24 h-24 -mt-3 bg-gray-100 dark:bg-gray-700 border-4 border-white dark:border-gray-800 rounded-full shadow-md object-cover"
          alt="Profile avatar"
        />
        <div
          title="Change avatar"
          class="absolute text-white bg-[#0072ff] bg-opacity-80 rounded-full p-2 transform -translate-x-1/2 -translate-y-1/2 opacity-0 left-1/2 top-1/2 group-hover:opacity-100 transition-opacity duration-200"
        >
          <Icon
            name="mdi:camera"
            size="20px"
          />
        </div>
      </a>
    </div>
    <div class="px-6 pt-4 pb-6 text-center">
      <h3 class="text-xl font-bold text-gray-800 dark:text-gray-200 mb-2">
        {{ currentClient.first_name }} {{ currentClient.last_name }}
      </h3>
      <div class="mt-2 text-gray-600 dark:text-gray-400" v-html="currentClient.description"></div>
    </div>
    <!-- <div class="flex justify-center pb-6">
                  <div class="text-center px-2.5">
                    <p class="text-gray-500">{{meCustomerData.projects}}</p>
                    <span>Projects</span>
                  </div>
                  <div class="text-center px-2.5">
                    <p class="text-gray-500">{{meCustomerData.teams}}</p>
                    <span>Team</span>
                  </div>
                  <div class="text-center px-2.5">
                    <p class="text-gray-500">{{meCustomerData.tasks}}</p>
                    <span>Tasks</span>
                  </div>
                </div> -->
  </div>
</template>
