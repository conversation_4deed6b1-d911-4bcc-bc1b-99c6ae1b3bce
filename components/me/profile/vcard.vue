<script setup lang="ts">
import vCardFactory from 'vcards-js';
import { useQRCode } from '@vueuse/integrations/useQRCode'

const location = useBrowserLocation()
const routeId = useRoute()
const { getCollectionWhere, getDoc } = database()
const currentClient: any = useState('currentClient', () => {
        return {}
    })
const { currentSpace } = space()
const getCalendarBookings = async () => {
    const calendarBookings = await queryByWhere2('contacts', 'slug', routeId.params.id, '==')
    console.log(calendarBookings)
    if (calendarBookings.result.length > 0) {
        currentClient.value = calendarBookings.result[0]

        getSpace()
    }
}

const getSpace = async () => {
    const space = await getDoc('spaces', currentClient.value.space_own)
    console.log(space)
        currentSpace.value = space.result
}

getCalendarBookings()

const user = ref({})
const company = ref({})
const download = (vCardString: any, fileName: any) => {
    let fileURL = window.URL.createObjectURL(new Blob([vCardString]));
    let fileLink = document.createElement('a');
    fileLink.href = fileURL;
    fileLink.setAttribute('download', fileName);
    document.body.appendChild(fileLink);
    fileLink.click();
};

const url = ref(`${location.value.origin}/o/user/${currentClient.value.slug}`)

const generate_vCard = () => {
    const vCard = vCardFactory();
    //set basic properties shown before
    vCard.firstName = currentClient.value.first_name;
    vCard.middleName = currentClient.value.middle_name;
    vCard.lastName = currentClient.value.last_name;
    vCard.uid = currentClient.value.id;
    vCard.organization = currentSpace.value.name;

    //link to image
    // vCard.photo.attachFromUrl('https://avatars2.githubusercontent.com/u/5659221?v=3&s=460', 'JPEG');

    //or embed image
    // vCard.photo.attachFromUrl('/path/to/file.jpeg');

    vCard.workPhone = currentClient.value.phone;
    // vCard.birthday = new Date(1985, 0, 1);
    // vCard.title = 'Software Developer';
    // vCard.url = 'https://github.com/enesser';
    // vCard.workUrl = 'https://acme-corporation/enesser';
    vCard.note = currentClient.value.note;

    //set other vitals
    // vCard.nickname = 'Scarface';
    // vCard.namePrefix = 'Mr.';
    // vCard.nameSuffix = 'JR';
    // vCard.gender = 'M';
    // vCard.anniversary = new Date(2004, 0, 1);
    // vCard.role = 'Software Development';

    //set other phone numbers
    // vCard.homePhone = '************';
    vCard.cellPhone = currentClient.value.cell;
    // vCard.pagerPhone = '************';

    //set fax/facsimile numbers
    // vCard.homeFax = '************';
    vCard.workFax = currentClient.value.fax;

    //set email addresses
    vCard.email = currentClient.value.email;
    // vCard.workEmail = '<EMAIL>';

    //set logo of organization or personal logo (also supports embedding, see above)
    // vCard.logo.attachFromUrl('https://avatars2.githubusercontent.com/u/5659221?v=3&s=460', 'JPEG');

    //set URL where the vCard can be found
    vCard.source = location.value.href;

    //set address information
    // vCard.homeAddress.label = 'Home Address';
    // vCard.homeAddress.street = '123 Main Street';
    // vCard.homeAddress.city = 'Chicago';
    // vCard.homeAddress.stateProvince = 'IL';
    // vCard.homeAddress.postalCode = '12345';
    // vCard.homeAddress.countryRegion = 'United States of America';

    // vCard.workAddress.label = 'Work Address';
    // vCard.workAddress.street = '123 Corporate Loop\nSuite 500';
    // vCard.workAddress.city = 'Los Angeles';
    // vCard.workAddress.stateProvince = 'CA';
    // vCard.workAddress.postalCode = '54321';
    // vCard.workAddress.countryRegion = 'United States of America';

    //set social media URLs
    vCard.socialUrls['facebook'] = '';
    vCard.socialUrls['linkedIn'] = '';
    vCard.socialUrls['twitter'] = '';
    vCard.socialUrls['flickr'] = '';
    vCard.socialUrls['custom'] = '';
    console.log(vCard.getFormattedString())
    download(vCard.getFormattedString(), 'vcardFile.vcf')
};
// `qrcode` will be a ref of data URL
const qrcode = useQRCode(location.value.href)
</script>

<template>
    <div class="mb-8 p-4 overflow-hidden rounded-lg shadow-lg theme_100 group">
        <div class="relative flex items-center overflow-hidden group">
            <img v-if="currentClient.avatar" :src="currentClient.background ? currentClient.background.src : ''" class="w-full">
            <div class="absolute top-4 ltr:right-4 rtl:left-4">

            </div>
        </div>
        <!-- <div class="relative flex justify-center -mt-10">
            <a class="z-30 group" href="javascript:;">
                <img v-if="currentClient.avatar" :src="currentClient.avatar ? currentClient.avatar.src : ''"
                    class="w-24 h-24 -mt-3 bg-gray-200 border-2 border-white border-solid rounded-full">

            </a>
        </div> -->
        <div class="px-3 pb-2 text-center">
            <h3 class="text-lg font-bold text-gray-800 dark:text-gray-100">{{ currentClient.first_name }} {{
                currentClient.last_name
            }}</h3>
            <!-- <div class="mt-2 font-light" v-html="currentClient.description"></div> -->
        </div>

        <div class="flex justify-center px-2">
            <div class="py-3 group-hover:block" >
                <div v-if="currentClient.about">
                    <div class="mb-2 text-center text-gray-500" v-html="currentClient.about">

</div>
                </div>
                
                <div class="my-3 border-t border-gray-200 dark:border-gray-700"></div>
                <div class="ltr:text-left rtl:text-right">
                    <p class="mb-2"><strong>Full Name :</strong><span class="ml-2">{{ currentClient.first_name }} {{
                        currentClient.last_name
                    }}</span></p>
                    <p class="mb-2"><strong>Mobile :</strong><span class="ml-2">{{ formatPhoneNumber(currentClient.cell) }}</span></p>
                    <p class="mb-2"><strong>Phone :</strong><span class="ml-2">{{ formatPhoneNumber(currentClient.phone) }}</span></p>
                    <p class="mb-2"><strong>Fax :</strong><span class="ml-2">{{ formatPhoneNumber(currentClient.fax) }}</span></p>

                    <p class="mb-2"><strong>Email :</strong><span class="ml-2">{{ currentClient.email }}</span></p>
                    <p class="mb-2"><strong>Location :</strong><span class="ml-2">{{ currentClient.formatted_address }}</span></p>
                    <p class="mb-2"><strong>Note :</strong><span class="ml-2"> {{ currentClient.note }}</span></p>
                    <a :href="url" target="_blank" class="mb-2"><strong>VCard :</strong><span class="ml-2"> {{
                        currentClient.slug
                    }}</span></a>
<button class="w-full mt-4 mb-2 rounded bg-cyan-500 text-primary_content" @click="generate_vCard">Download VCard</button>
                </div>
                <div class="flex justify-center">
                    <img  v-if="url" :src="qrcode" alt="QR Code" />

                </div>
            </div>
        </div>

    </div>
</template>