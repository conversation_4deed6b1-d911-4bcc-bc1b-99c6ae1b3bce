<template>
  <div class="budget-manager">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
      <div>
        <h2 class="text-2xl font-bold text-gray-900">Budget Management</h2>
        <p class="text-sm text-gray-600 mt-1">
          Set spending limits and get alerts to stay within your advertising budget
        </p>
      </div>
      <button
        @click="showCreateBudget = true"
        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        <PlusIcon class="w-4 h-4 mr-2" />
        Create Budget
      </button>
    </div>

    <!-- Budget Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Total Spent Today -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <CalendarDaysIcon class="h-6 w-6 text-gray-400" />
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Today's Spending</dt>
                <dd class="text-lg font-medium text-gray-900">
                  {{ formatCurrency(totalSpentToday, 'USD') }}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Total Spent This Month -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <ChartBarIcon class="h-6 w-6 text-gray-400" />
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Monthly Spending</dt>
                <dd class="text-lg font-medium text-gray-900">
                  {{ formatCurrency(totalSpentThisMonth, 'USD') }}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Active Budgets -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <CheckCircleIcon class="h-6 w-6 text-green-400" />
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Active Budgets</dt>
                <dd class="text-lg font-medium text-gray-900">
                  {{ activeBudgets.length }}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Budget Alerts -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <ExclamationTriangleIcon 
                :class="[
                  'h-6 w-6',
                  pendingAlerts.length > 0 ? 'text-amber-400' : 'text-gray-400'
                ]" 
              />
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Pending Alerts</dt>
                <dd class="text-lg font-medium text-gray-900">
                  {{ pendingAlerts.length }}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Budget Alerts -->
    <div v-if="pendingAlerts.length > 0" class="mb-8">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Budget Alerts</h3>
      <div class="space-y-3">
        <div
          v-for="alert in pendingAlerts"
          :key="alert.id"
          class="bg-amber-50 border border-amber-200 rounded-md p-4"
        >
          <div class="flex">
            <div class="flex-shrink-0">
              <ExclamationTriangleIcon class="h-5 w-5 text-amber-400" />
            </div>
            <div class="ml-3 flex-1">
              <h4 class="text-sm font-medium text-amber-800">
                Budget Alert - {{ alert.threshold_percentage }}% Reached
              </h4>
              <p class="text-sm text-amber-700 mt-1">
                {{ alert.message }}
              </p>
              <div class="mt-2 flex items-center space-x-3">
                <button
                  @click="acknowledgeBudgetAlert(alert.id)"
                  class="text-sm font-medium text-amber-800 hover:text-amber-900"
                >
                  Acknowledge
                </button>
                <button
                  @click="viewBudgetDetails(alert.budget_id)"
                  class="text-sm font-medium text-amber-800 hover:text-amber-900"
                >
                  View Budget
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Budget List -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Your Budgets</h3>
      </div>
      
      <div v-if="budgetLimits.length === 0" class="p-6 text-center">
        <CurrencyDollarIcon class="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">No budgets created</h3>
        <p class="text-sm text-gray-600 mb-4">
          Create your first budget to start managing your advertising spend
        </p>
        <button
          @click="showCreateBudget = true"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          <PlusIcon class="w-4 h-4 mr-2" />
          Create Budget
        </button>
      </div>

      <div v-else class="divide-y divide-gray-200">
        <div
          v-for="budget in budgetLimits"
          :key="budget.id"
          class="p-6 hover:bg-gray-50"
        >
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <!-- Budget Info -->
              <div class="flex items-center space-x-3 mb-3">
                <h4 class="text-lg font-medium text-gray-900">
                  {{ getBudgetDisplayName(budget) }}
                </h4>
                <span
                  :class="[
                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                    budget.status === 'active' ? 'bg-green-100 text-green-800' :
                    budget.status === 'exceeded' ? 'bg-red-100 text-red-800' :
                    'bg-gray-100 text-gray-800'
                  ]"
                >
                  {{ budget.status.charAt(0).toUpperCase() + budget.status.slice(1) }}
                </span>
              </div>

              <!-- Budget Progress -->
              <div class="mb-3">
                <div class="flex items-center justify-between text-sm mb-1">
                  <span class="text-gray-600">
                    {{ formatCurrency(budget.spent_amount, budget.currency) }} spent of {{ formatCurrency(budget.amount, budget.currency) }}
                  </span>
                  <span class="font-medium text-gray-900">
                    {{ Math.round((budget.spent_amount / budget.amount) * 100) }}%
                  </span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div
                    :class="[
                      'h-2 rounded-full transition-all duration-500',
                      budget.status === 'exceeded' ? 'bg-red-500' :
                      (budget.spent_amount / budget.amount) >= 0.8 ? 'bg-amber-500' :
                      'bg-green-500'
                    ]"
                    :style="{ width: `${Math.min((budget.spent_amount / budget.amount) * 100, 100)}%` }"
                  ></div>
                </div>
              </div>

              <!-- Budget Details -->
              <div class="flex items-center space-x-6 text-sm text-gray-600">
                <div class="flex items-center">
                  <ClockIcon class="w-4 h-4 mr-1" />
                  {{ budget.limit_type.charAt(0).toUpperCase() + budget.limit_type.slice(1) }}
                </div>
                <div class="flex items-center">
                  <CalendarIcon class="w-4 h-4 mr-1" />
                  {{ formatDate(budget.period_start) }} - {{ formatDate(budget.period_end) }}
                </div>
                <div v-if="budget.auto_pause_campaigns" class="flex items-center">
                  <PauseIcon class="w-4 h-4 mr-1" />
                  Auto-pause enabled
                </div>
              </div>

              <!-- Alert Thresholds -->
              <div class="mt-2 flex items-center space-x-2">
                <span class="text-xs text-gray-500">Alerts at:</span>
                <div class="flex space-x-1">
                  <span
                    v-for="threshold in budget.alert_thresholds"
                    :key="threshold"
                    :class="[
                      'inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium',
                      budget.alerts_sent.includes(threshold) 
                        ? 'bg-gray-100 text-gray-600' 
                        : 'bg-blue-100 text-blue-800'
                    ]"
                  >
                    {{ threshold }}%
                    <CheckIcon v-if="budget.alerts_sent.includes(threshold)" class="w-3 h-3 ml-1" />
                  </span>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center space-x-2 ml-6">
              <button
                @click="editBudget(budget)"
                class="p-2 text-gray-400 hover:text-gray-600"
              >
                <PencilIcon class="w-4 h-4" />
              </button>
              <button
                v-if="budget.status === 'active'"
                @click="pauseBudget(budget.id)"
                class="p-2 text-gray-400 hover:text-amber-600"
              >
                <PauseIcon class="w-4 h-4" />
              </button>
              <button
                v-if="budget.status === 'paused'"
                @click="resumeBudget(budget.id)"
                class="p-2 text-gray-400 hover:text-green-600"
              >
                <PlayIcon class="w-4 h-4" />
              </button>
              <button
                @click="resetBudget(budget.id)"
                class="p-2 text-gray-400 hover:text-blue-600"
              >
                <ArrowPathIcon class="w-4 h-4" />
              </button>
              <button
                @click="deleteBudget(budget.id)"
                class="p-2 text-gray-400 hover:text-red-600"
              >
                <TrashIcon class="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Create/Edit Budget Modal -->
    <TransitionRoot as="template" :show="showCreateBudget || !!editingBudget">
      <Dialog as="div" class="relative z-50" @close="closeBudgetModal">
        <TransitionChild
          as="template"
          enter="ease-out duration-300"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="ease-in duration-200"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </TransitionChild>

        <div class="fixed inset-0 z-10 overflow-y-auto">
          <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <TransitionChild
              as="template"
              enter="ease-out duration-300"
              enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enter-to="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leave-from="opacity-100 translate-y-0 sm:scale-100"
              leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <DialogPanel class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                <div>
                  <div class="text-center">
                    <CurrencyDollarIcon class="mx-auto h-12 w-12 text-blue-600" />
                    <DialogTitle as="h3" class="text-lg font-semibold leading-6 text-gray-900 mt-2">
                      {{ editingBudget ? 'Edit Budget' : 'Create Budget' }}
                    </DialogTitle>
                  </div>

                  <form @submit.prevent="saveBudget" class="mt-6 space-y-6">
                    <!-- Budget Type -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">Budget Type</label>
                      <select
                        v-model="budgetForm.limit_type"
                        class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      >
                        <option value="daily">Daily Budget</option>
                        <option value="weekly">Weekly Budget</option>
                        <option value="monthly">Monthly Budget</option>
                        <option value="campaign_total">Campaign Total</option>
                      </select>
                    </div>

                    <!-- Budget Amount -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">Budget Amount</label>
                      <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <span class="text-gray-500 sm:text-sm">$</span>
                        </div>
                        <input
                          v-model="budgetForm.amount"
                          type="number"
                          min="1"
                          step="0.01"
                          required
                          class="block w-full pl-7 pr-12 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                          placeholder="0.00"
                        />
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                          <span class="text-gray-500 sm:text-sm">USD</span>
                        </div>
                      </div>
                    </div>

                    <!-- Campaign Selection -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">Apply to Campaign (Optional)</label>
                      <select
                        v-model="budgetForm.campaign_id"
                        class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      >
                        <option value="">All campaigns</option>
                        <!-- Campaign options would be populated here -->
                      </select>
                    </div>

                    <!-- Alert Thresholds -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">Alert Thresholds</label>
                      <div class="flex space-x-2">
                        <input
                          v-for="(threshold, index) in budgetForm.alert_thresholds"
                          :key="index"
                          v-model="budgetForm.alert_thresholds[index]"
                          type="number"
                          min="1"
                          max="100"
                          class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        />
                      </div>
                      <p class="text-xs text-gray-500 mt-1">Get alerts when spending reaches these percentages</p>
                    </div>

                    <!-- Options -->
                    <div class="space-y-3">
                      <label class="flex items-center">
                        <input
                          v-model="budgetForm.auto_pause_campaigns"
                          type="checkbox"
                          class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        />
                        <span class="ml-2 text-sm text-gray-700">
                          Automatically pause campaigns when budget is exceeded
                        </span>
                      </label>
                      
                      <label class="flex items-center">
                        <input
                          v-model="budgetForm.rollover_unused_budget"
                          type="checkbox"
                          class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        />
                        <span class="ml-2 text-sm text-gray-700">
                          Roll over unused budget to next period
                        </span>
                      </label>
                    </div>

                    <!-- Actions -->
                    <div class="mt-6 flex items-center justify-end space-x-3">
                      <button
                        type="button"
                        @click="closeBudgetModal"
                        class="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        :disabled="isLoading"
                        class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
                      >
                        {{ editingBudget ? 'Update Budget' : 'Create Budget' }}
                      </button>
                    </div>
                  </form>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'
import {
  PlusIcon,
  CalendarDaysIcon,
  ChartBarIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  CurrencyDollarIcon,
  ClockIcon,
  CalendarIcon,
  PauseIcon,
  PlayIcon,
  PencilIcon,
  TrashIcon,
  ArrowPathIcon,
  CheckIcon
} from '@heroicons/vue/24/outline'

// Composables
const {
  budgetLimits,
  activeBudgets,
  pendingAlerts,
  totalSpentToday,
  totalSpentThisMonth,
  isLoading,
  error,
  createBudgetLimit,
  updateBudgetLimit,
  loadBudgetLimits,
  acknowledgeBudgetAlert,
  getBudgetDisplayName,
  formatCurrency
} = useBudgetManagement()

// State
const showCreateBudget = ref(false)
const editingBudget = ref<any>(null)

// Budget form
const budgetForm = ref({
  limit_type: 'monthly',
  amount: 0,
  currency: 'USD',
  campaign_id: '',
  ad_spot_id: '',
  alert_thresholds: [50, 80, 95],
  auto_pause_campaigns: true,
  rollover_unused_budget: false
})

// Methods
const saveBudget = async () => {
  try {
    if (editingBudget.value) {
      await updateBudgetLimit(editingBudget.value.id, budgetForm.value)
    } else {
      await createBudgetLimit(budgetForm.value)
    }
    closeBudgetModal()
  } catch (err) {
    console.error('Save budget error:', err)
  }
}

const editBudget = (budget: any) => {
  editingBudget.value = budget
  budgetForm.value = {
    limit_type: budget.limit_type,
    amount: budget.amount,
    currency: budget.currency,
    campaign_id: budget.campaign_id || '',
    ad_spot_id: budget.ad_spot_id || '',
    alert_thresholds: [...budget.alert_thresholds],
    auto_pause_campaigns: budget.auto_pause_campaigns,
    rollover_unused_budget: budget.rollover_unused_budget
  }
}

const closeBudgetModal = () => {
  showCreateBudget.value = false
  editingBudget.value = null
  budgetForm.value = {
    limit_type: 'monthly',
    amount: 0,
    currency: 'USD',
    campaign_id: '',
    ad_spot_id: '',
    alert_thresholds: [50, 80, 95],
    auto_pause_campaigns: true,
    rollover_unused_budget: false
  }
}

const pauseBudget = async (budgetId: string) => {
  // Implementation would pause budget
  console.log('Pausing budget:', budgetId)
}

const resumeBudget = async (budgetId: string) => {
  // Implementation would resume budget
  console.log('Resuming budget:', budgetId)
}

const resetBudget = async (budgetId: string) => {
  if (confirm('Are you sure you want to reset this budget period?')) {
    // Implementation would reset budget
    console.log('Resetting budget:', budgetId)
  }
}

const deleteBudget = async (budgetId: string) => {
  if (confirm('Are you sure you want to delete this budget?')) {
    // Implementation would delete budget
    console.log('Deleting budget:', budgetId)
  }
}

const viewBudgetDetails = (budgetId: string) => {
  const budget = budgetLimits.value.find(b => b.id === budgetId)
  if (budget) {
    editBudget(budget)
  }
}

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  }).format(date)
}

// Initialize
onMounted(async () => {
  await loadBudgetLimits()
})
</script>

<style scoped>
.budget-manager {
  @apply max-w-7xl mx-auto;
}
</style>