<template>
  <div class="p-6 bg-gray-100 rounded-lg">
    <h3 class="text-lg font-bold mb-4">User Management Debugger</h3>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <!-- User Management Status -->
      <div class="bg-white p-4 rounded-lg shadow">
        <h4 class="font-semibold mb-2">Fetch Status</h4>
        <div class="space-y-2 text-sm">
          <div>Loading: <span :class="isLoading ? 'text-orange-600' : 'text-green-600'">{{ isLoading }}</span></div>
          <div>Error: <span :class="error ? 'text-red-600' : 'text-green-600'">{{ error || 'None' }}</span></div>
          <div>Total Users: <span class="font-medium">{{ users.length }}</span></div>
          <div>Total (from stats): <span class="font-medium">{{ totalUsers }}</span></div>
        </div>
        
        <button 
          @click="refetchUsers" 
          :disabled="isLoading"
          class="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {{ isLoading ? 'Loading...' : 'Refetch Users' }}
        </button>
      </div>

      <!-- Role Distribution -->
      <div class="bg-white p-4 rounded-lg shadow">
        <h4 class="font-semibold mb-2">Role Distribution</h4>
        <div class="space-y-1 text-sm">
          <div v-for="(count, role) in userStats.roleDistribution" :key="role">
            {{ role }}: <span class="font-medium">{{ count }}</span>
          </div>
          <div v-if="Object.keys(userStats.roleDistribution).length === 0" class="text-gray-500">
            No role data available
          </div>
        </div>
      </div>

      <!-- Users Sample -->
      <div class="bg-white p-4 rounded-lg shadow">
        <h4 class="font-semibold mb-2">Users Sample (first 5)</h4>
        <div class="space-y-2 text-sm">
          <div v-for="user in users.slice(0, 5)" :key="user.id || user.email" class="border-b pb-1">
            <div><strong>Name:</strong> {{ user.name || `${user.first_name || ''} ${user.last_name || ''}`.trim() || user.email }}</div>
            <div><strong>Email:</strong> {{ user.email }}</div>
            <div><strong>Roles:</strong> {{ JSON.stringify(user.roles) }}</div>
            <div><strong>Role:</strong> {{ user.role }}</div>
            <div><strong>Type:</strong> {{ user.type }}</div>
            <div><strong>UserType:</strong> {{ user.userType }}</div>
          </div>
          <div v-if="users.length === 0" class="text-gray-500">
            No users loaded
          </div>
        </div>
      </div>

      <!-- Advertisers -->
      <div class="bg-white p-4 rounded-lg shadow">
        <h4 class="font-semibold mb-2">Identified Advertisers</h4>
        <div class="space-y-2 text-sm">
          <div v-for="advertiser in advertisers" :key="advertiser.id || advertiser.email" class="border-b pb-1">
            <div><strong>Name:</strong> {{ advertiser.name }}</div>
            <div><strong>Email:</strong> {{ advertiser.email }}</div>
            <div><strong>Roles:</strong> {{ JSON.stringify(advertiser.roles) }}</div>
            <div><strong>Role:</strong> {{ advertiser.role }}</div>
          </div>
          <div v-if="advertisers.length === 0" class="text-gray-500">
            No advertisers found
          </div>
        </div>
      </div>
    </div>

    <!-- Raw API Test -->
    <div class="mt-6 bg-white p-4 rounded-lg shadow">
      <h4 class="font-semibold mb-2">Raw API Test</h4>
      <button 
        @click="testRawAPI" 
        :disabled="testingAPI"
        class="mb-4 px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
      >
        {{ testingAPI ? 'Testing...' : 'Test Raw API' }}
      </button>
      
      <div v-if="rawAPIResult" class="text-sm">
        <div class="mb-2"><strong>API Response:</strong></div>
        <pre class="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-40">{{ JSON.stringify(rawAPIResult, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useUserManagement } from '~/composables/user-management'

const { 
  users, 
  isLoading, 
  error, 
  totalUsers, 
  userStats,
  fetchUsers 
} = useUserManagement()

const testingAPI = ref(false)
const rawAPIResult = ref(null)

// Compute advertisers with same logic as form
const advertisers = computed(() => {
  return users.value.filter(user => {
    return (
      (user.roles && Array.isArray(user.roles) && user.roles.includes('Advertiser')) ||
      (user.roles && Array.isArray(user.roles) && user.roles.includes('advertiser')) ||
      (user.role === 'advertiser') ||
      (user.role === 'Advertiser') ||
      (user.type === 'advertiser') ||
      (user.type === 'Advertiser') ||
      (user.userType === 'advertiser') ||
      (user.userType === 'Advertiser')
    )
  })
})

const refetchUsers = async () => {
  await fetchUsers(true)
}

const testRawAPI = async () => {
  testingAPI.value = true
  try {
    const response = await $fetch('/api/firestore/query?col=user')
    rawAPIResult.value = response
  } catch (err) {
    rawAPIResult.value = { error: err.message }
  } finally {
    testingAPI.value = false
  }
}

onMounted(() => {
  refetchUsers()
})
</script>