<script setup lang="ts">
const tabs = ref(1)
const open = ref(false)
import { Chart, registerables, CategoryScale } from "chart.js";
Chart.register(...registerables, CategoryScale);

onMounted(()=> {
	if(process.client){
    const text_primary_500   =  '#6366F1';
    const text_green_500 =   '#22C55E';
    const chart_storage = document.getElementById("StorageCharts");

    if ( chart_storage != null) {
      const ctds = chart_storage.getContext('2d');
      const StorageChart = new Chart(ctds, {
        type: 'doughnut',
        data: {
          labels: ['Used','Free'],
          datasets: [{
            label: 'Storage',
            data: [90000, 52000],
            backgroundColor: [
              text_primary_500,
              text_green_500
            ],
            hoverOffset: 4
          }]
        },
        options: {
          animation: {
            delay: 2000
          },
          plugins: {
            legend: {
              display: false
            }
          }
        }
      })
    }
  }
})
const companyData = useState('space_data', () => {
  return {
    tickets: 0,
    invoices: 0,
    domains: 0,
    yearly_spend: 0,
    credit: 0,
    storage_space: 1000,
    storage_used: 250,
  }
})
</script>
<template>
    <div class="h-full p-6 rounded-lg shadow-lg theme_100">
      <div class="flex flex-col pb-3">
        <h3 class="text-base font-bold">Storage</h3>
      </div>
      <div class="relative w-2/3 mx-auto mb-4 lg:w-full">
        <canvas class="max-w-100" id="StorageCharts"></canvas>
        <p class="absolute text-2xl font-bold transform -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2">{{
        (companyData.storage_used / companyData.storage_space) * 100}}%</p>
      </div>
      <div class="text-sm font-semibold text-center text-gray-500">
        <p class="mb-2"><span class="inline-block w-4 h-4 mr-2 bg-primary_focus"></span>Used:
          {{ companyData.storage_used }} <span
            class="inline-block w-4 h-4 mr-2 bg-green-500 ltr:ml-4 rtl:mr-4"></span>Free: {{ companyData.storage_space }}
        </p>
      </div>
    </div>
</template>
