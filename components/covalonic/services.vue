<script setup lang="ts">
const { currentSpace, spaceApps, serviceOmniAll, credits_total } = space();

</script>
<template>
  <div class="flex-shrink w-full max-w-full px-4 mb-6">
    <div class="h-full p-6 rounded-lg shadow-lg theme_100">
      <div class="relative">
        <div class="overflow-auto">
          <table class="w-full table-sorter table-bordered-bottom">
            <thead>
              <tr class="bg-gray-200 dark:bg-gray-900 dark:bg-opacity-40 text-left">
                <th>Services</th>
                <th class="hidden lg:table-cell">Monthly <small>(credits)</small></th>
                <th class="hidden md:table-cell">Expired</th>
                <th>Status</th>
                <th data-sortable="false">Action</th>
              </tr>
            </thead>
            <tbody class="text-sm leading-5">
              <tr
                v-for="(app, index) in serviceOmniAll"
                :key="index"
                class="hover:table_base_100"
              >
                <td>
                  <div>
                    <div class="font-semibold">
                      {{ app.name }}
                    </div>
                    <div class="italic">
                      {{ app.subtitle }}
                    </div>
                  </div>
                </td>
                <td class="hidden lg:table-cell">
                  {{ app.credits }}
                </td>
                <td class="hidden md:table-cell">
                  {{ app.expires }}
                </td>
                <td>
                  <div
                    class="inline-block px-3 py-1 text-sm font-semibold leading-tight text-center rounded-full dark:bg-opacity-80"
                    :class="{
                      'text-yellow-700 bg-yellow-100': app.status === 'Pending',
                      'text-green-700 bg-green-100': app.status === 'Active',
                      'text-gray-700 bg-gray-100': app.status === 'Expired',
                    }"
                  >
                    {{ app.status }}
                  </div>
                </td>
                <td>
                  <a
                    href="#"
                    class="px-3 py-2 text-sm leading-5 text-center text-gray-100 border rounded bg-primary_focus border-primary_focus hover:text-white hover:bg-primary hover:ring-0 hover:border-primary focus:bg-primary focus:border-primary focus:outline-none focus:ring-0"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="currentColor"
                      class="inline w-4 h-4 bi bi-pencil-square"
                      viewBox="0 0 16 16"
                    >
                      <path
                        d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z"
                      ></path>
                      <path
                        fill-rule="evenodd"
                        d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5v11z"
                      ></path>
                    </svg>
                  </a>
                </td>
              </tr>
              <tr
                class="hover:table_base_100"
              >
                <td>

                </td>
                <td class="hidden lg:table-cell font-bold text-lg underline">
                  {{ credits_total }}
                </td>
                <td class="hidden md:table-cell">
                </td>
                <td>
                </td>
                <td>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>
