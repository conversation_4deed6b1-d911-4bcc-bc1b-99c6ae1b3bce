<script setup lang="ts">
import { collection, query, where, getDocs, limit } from "firebase/firestore";
// Use the new Firebase initialization
const { firestore } = useFirebase();
const { currentSpace } = space()
const { meUser } = useMe()

const historyProp = defineProps({
  title: {
    type: String,
    default: 'History',
  },
  type: {
    type: String,
    required: true,
  },
})

const history: any = ref([])


const getHistory = async () => {
  // Check if firestore is initialized
  if (!firestore) {
    console.error('Firestore is not initialized');
    return;
  }

  try {
    if (historyProp.type == 'Personal') {
      const q = query(collection(firestore, "history"), where('linked_ids', 'array-contains', currentSpace.value.id,), where('linked_ids', 'array-contains', meUser.value.id));

      const querySnapshot = await getDocs(q);
      querySnapshot.forEach((doc) => {
        // doc.data() is never undefined for query doc snapshots
        console.log(doc.id, " => ", doc.data());
        history.value.push({ id: doc.id, ...doc.data() })
      });
    } else if (historyProp.type == 'Company') {
      const q = query(collection(firestore, "history"), where('linked_ids', 'array-contains', currentSpace.value.id), limit(10));

      const querySnapshot = await getDocs(q);
      querySnapshot.forEach((doc) => {
        // doc.data() is never undefined for query doc snapshots
        history.value.push({ id: doc.id, ...doc.data() })
      });
    }
  } catch (error) {
    console.error('Error fetching history:', error);
  }
}

onMounted(async () => {
 setTimeout(() => {
    getHistory()
  }, 1000);
})
</script>

<template>
  <div class="h-full p-6 rounded-lg shadow-lg theme_100">
    <div class="flex flex-col pb-6">
      <h3 class="text-base font-bold">{{ title }}</h3>
    </div>
    <div class="relative">
      <ol class="mb-6 overflow-y-auto h-72 scrollbars scrollbar-hide show">
        <li class="flex mb-2" v-for="(item, index) in history" :key="index">
          <history-card :item="item" />
        </li>
      </ol>
    </div>
  </div>
</template>