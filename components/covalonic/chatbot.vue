<script setup lang="ts">
const openChatbot: any = useState('openChatbot', ()=> false)
const showChat = ref(false)
</script>

<template>
  <div class="relative dark:text-gray-500">
   
     <!-- Right Offcanvas -->
     <div class="fixed inset-0 z-50 w-full h-full" id="mobile-canvas" x-description="Mobile menu" v-show="openChatbot"
      style="display: none;">
      <!-- bg openChatbot -->
      <span class="fixed inset-x-0 top-0 w-full h-full bg-gray-900 bg-opacity-70"></span>

      <nav id="mobile-nav"
        class="fixed top-0 right-0 flex flex-col h-full overflow-auto bg-white z-200 ltr:left-0 rtl:right-0 w-72 dark:bg-gray-800 scrollbars show"
        v-show="openChatbot" x-description="Mobile menu" role="menu">
        <div class="p-6 border-b border-gray-200 text-primary_content bg-primary dark:border-gray-700">
          <div class="flex flex-row justify-between">
            <div @click="showChat = !showChat" class="flex items-center space-x-2 cursor-pointer">
                <Icon name='ion:md-switch' />
            <h3 class="font-bold text-md">Chat Bot</h3>
            </div>
             
            <button @click="openChatbot = false" type="button" class="inline-block w-4 h-4">
              <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" class="inline-block text-gray-100 bi bi-x-lg"
                viewBox="0 0 16 16" id="x-lg">
                <path
                  d="M1.293 1.293a1 1 0 011.414 0L8 6.586l5.293-5.293a1 1 0 111.414 1.414L9.414 8l5.293 5.293a1 1 0 01-1.414 1.414L8 9.414l-5.293 5.293a1 1 0 01-1.414-1.414L6.586 8 1.293 2.707a1 1 0 010-1.414z">
                </path>
              </svg>
              <!-- <i class="fas fa-times"></i> -->
            </button>
          </div>
        </div>
       <open-ai-chat-gpt-interface :showChat="showChat"/>
      </nav>
    </div>
   
  </div>
</template>