<script setup lang="ts">
const itemIdea = ref(true)
const openChatbot: any = useState('openChatbot', ()=> false)

</script>

<template>
	<div>
		<tooltip-icon
                      text="Bot"
                      @click="openChatbot = !openChatbot"
                      icon="vscode-icons:file-type-robots"
                      side="top"
					  classes="border-y-2 border-primarys"
                    />
	</div>
    <!-- <div class="relative cursor-pointer" @click="openChatbot = !openChatbot">
        <div class="flame" :class="{'bg-gradient-to-r from-cyan-500 to-blue-500': itemIdea, 'bg-gradient-to-r from-orange-500 to-red-500': !itemIdea}">
        </div>

    </div> -->
</template>

<style>

 .flame {
	 width: 25px;
	 height: 25px;
	 /* background: linear-gradient(-45deg, red, orange); */
	 border-radius: 25px 25px 0px 25px;
	 transform: rotate(-135deg);
	 animation: 0.1s flame infinite;
	 filter: blur(10px);
	 position: relative;
	 box-shadow: 17px 20px 90px #700;
}
 .flame:after, .flame:before {
	 content: '';
	 width: 25px;
	 height: 25px;
	 display: block;
	 position: absolute;
	 /* background: linear-gradient(-45deg, red, orange); */
	 animation: 0.2s flame infinite;
	 transform: scale(0.8) rotate(20deg);
	 border-radius: 25px 25px 0px 25px;
	 top: 20px;
}
 .flame:before {
	 top: 0;
	 animation-duration: 0.09s;
	 transform: scale(0.9) rotate(-15deg) translate(10px, 0px);
}
 @keyframes flame {
	 0% {
		 height: 50px;
		 width: 50px;
	}
	 50% {
		 height: 45px;
		 width: 45px;
	}
	 100% {
		 height: 50px;
		 width: 50px;
	}
}
 
</style>