<script setup lang="ts">
const open: boolean = ref(false);
</script>
<template>
  <div>
    <modal title="Top Up" :open="open" @close="open = false">
      <template #body>
        <covalonic-wallet-pay v-if="open" />
      </template>
    </modal>
    <div
      @click="open = !open"
      class="block px-4 py-2 mb-6 text-sm leading-5 text-center text-gray-100 border rounded cursor-pointer bg-primarys border-primarys hover:text-white hover:bg-secondary hover:ring-0 hover:border-primary focus:bg-primary focus:border-primary focus:outline-none focus:ring-0"
    >
      Top up
    </div>
  </div>
</template>
