<script setup lang="ts">
const { cartItems } = useShop();
const { credits_total, credits } = space()
const config = useRuntimeConfig();
const amount = ref(10);

const addToCart = () => {
  const product = {
    id: 1,
    name: "Top Up",
    price: amount.value,
    quantity: 1,
    image: "",
    attributes: [],
  };
  cartItems.value = [product];

  console.log(cartItems.value);
};

onMounted(() => {
    amount.value = country.value == 'South Africa' ? credits.value.total_sa : credits.value.total_us
  addToCart();
});

watch(
  () => amount.value,
  () => addToCart()
);

const country: any = useState("country", () => {
  return "";
});
const isTest:any  = useState("isTest");
const payfast = computed(()=> {
  return {
    ...config.public.PAYFAST, title: "Top Up",

  }
})
</script>

<template>
  <div>
    <!-- {{ credits_total }} -{{ credits }} -->
    <div class="flex-shrink w-full max-w-full mb-4">
      <label for="inputtitle" class="o_label"
        >Purchase Amount ({{ country == 'South Africa' ? 'R' : '$' }} {{ amount }})</label
      >
      <input
        v-model="amount"
        type="number"
        class="o_input"
        id="inputtitle"
      />
    </div>
    <div>
      <div v-if="country == 'South Africa'">
        <payfast-button :amount="amount" :payfastSettings="payfast" :isSandbox="isTest"/>
      </div>
      <div v-else>
        <paypal :client_id="config.public.PAYPAL.client_id" currency="USD"/>

      </div>
    </div>
  </div>
</template>
