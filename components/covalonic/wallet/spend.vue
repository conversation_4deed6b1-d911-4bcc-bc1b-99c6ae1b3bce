<script setup lang="ts">
const { currentSpace } = space()
const companyData = useState('space_data', () => {
  return {
    tickets: 0,
    invoices: 0,
    domains: 0,
    yearly_spend: 0,
    credit: 0,
    storage_space: 1000,
    storage_used: 250,
  }
})
</script>
<template>
  <div class="relative p-6 mb-6 overflow-hidden text-pink-100 bg-pink-500 rounded-lg shadow-lg">
    <div class="absolute ltr:-right-10 rtl:-left-10 -top-10">
      <div class="bg-white rounded-full opacity-10 w-36 h-36"></div>
    </div>
    <div class="absolute ltr:-right-8 rtl:-left-8 -top-8">
      <div class="w-24 h-24 bg-white rounded-full opacity-20"></div>
    </div>

    <div class="flex flex-row justify-between pb-3">
      <div class="flex flex-col">
        <h3 class="text-base font-bold">My Spend</h3>
      </div>
    </div>
    <div class="relative text-center">
      <h4 class="mb-3 text-2xl font-bold text-white">{{currentSpace.currency ? currentSpace.currency.symbol : ''}} {{ companyData.yearly_spend }}/Year</h4>
      <p class="mb-3 text-sm">All subscription fee</p>
    </div>
  </div>
</template>