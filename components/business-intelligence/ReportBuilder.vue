<template>
  <div class="report-builder">
    <!-- Builder Header -->
    <div class="builder-header bg-white border-b border-gray-200 px-6 py-4">
      <div class="flex items-center justify-between">
        <div class="header-info">
          <h1 class="text-2xl font-bold text-gray-900">Report Builder</h1>
          <p class="text-gray-600 mt-1">Create custom reports with drag-and-drop components</p>
        </div>
        
        <div class="header-actions flex items-center gap-3">
          <button
            v-if="state.currentReport"
            @click="togglePreview"
            :class="[
              'preview-btn px-4 py-2 rounded-lg flex items-center gap-2 transition-colors',
              state.previewMode 
                ? 'bg-gray-200 text-gray-800 hover:bg-gray-300' 
                : 'bg-blue-600 text-white hover:bg-blue-700'
            ]"
          >
            <Icon :name="state.previewMode ? 'edit' : 'eye'" class="w-4 h-4" />
            {{ state.previewMode ? 'Edit' : 'Preview' }}
          </button>
          
          <button
            @click="saveReport"
            :disabled="!canSaveReport || state.isGenerating"
            class="save-btn bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Icon name="save" class="w-4 h-4 mr-2" />
            {{ state.currentReport?.id ? 'Update' : 'Save' }}
          </button>
          
          <button
            v-if="state.currentReport"
            @click="exportReport(state.currentReport.id, 'pdf')"
            :disabled="state.isGenerating"
            class="export-btn bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg disabled:opacity-50 flex items-center gap-2 transition-colors"
          >
            <Icon name="download" class="w-4 h-4" />
            Export
          </button>
        </div>
      </div>
    </div>

    <div class="builder-content flex h-[calc(100vh-80px)]">
      <!-- Left Sidebar - Components & Templates -->
      <div v-if="!state.previewMode" class="sidebar bg-white border-r border-gray-200 w-80 flex flex-col">
        <div class="sidebar-header border-b border-gray-200 p-4">
          <div class="tabs flex">
            <button
              @click="activeTab = 'components'"
              :class="[
                'tab-btn px-4 py-2 text-sm font-medium rounded-lg transition-colors',
                activeTab === 'components' 
                  ? 'bg-blue-100 text-blue-700' 
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
              ]"
            >
              Components
            </button>
            <button
              @click="activeTab = 'templates'"
              :class="[
                'tab-btn px-4 py-2 text-sm font-medium rounded-lg transition-colors ml-2',
                activeTab === 'templates' 
                  ? 'bg-blue-100 text-blue-700' 
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
              ]"
            >
              Templates
            </button>
          </div>
        </div>

        <div class="sidebar-content flex-1 overflow-y-auto p-4">
          <!-- Components Tab -->
          <div v-if="activeTab === 'components'" class="components-tab">
            <div class="component-categories space-y-6">
              <!-- Chart Components -->
              <div class="category">
                <h3 class="text-sm font-semibold text-gray-900 mb-3 flex items-center gap-2">
                  <Icon name="bar-chart-2" class="w-4 h-4" />
                  Charts
                </h3>
                <div class="components-grid grid grid-cols-2 gap-3">
                  <div 
                    v-for="chartType in availableChartTypes"
                    :key="chartType.value"
                    @click="addComponent('chart', { chart_type: chartType.value })"
                    class="component-item bg-gray-50 hover:bg-blue-50 border border-gray-200 hover:border-blue-300 rounded-lg p-3 cursor-pointer transition-colors group"
                  >
                    <div class="component-icon mb-2">
                      <Icon :name="chartType.icon" class="w-6 h-6 text-gray-400 group-hover:text-blue-600 transition-colors" />
                    </div>
                    <div class="component-label text-xs font-medium text-gray-700 group-hover:text-blue-700">
                      {{ chartType.label }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- Data Components -->
              <div class="category">
                <h3 class="text-sm font-semibold text-gray-900 mb-3 flex items-center gap-2">
                  <Icon name="database" class="w-4 h-4" />
                  Data
                </h3>
                <div class="components-grid grid grid-cols-2 gap-3">
                  <div 
                    @click="addComponent('table')"
                    class="component-item bg-gray-50 hover:bg-blue-50 border border-gray-200 hover:border-blue-300 rounded-lg p-3 cursor-pointer transition-colors group"
                  >
                    <div class="component-icon mb-2">
                      <Icon name="table" class="w-6 h-6 text-gray-400 group-hover:text-blue-600 transition-colors" />
                    </div>
                    <div class="component-label text-xs font-medium text-gray-700 group-hover:text-blue-700">
                      Data Table
                    </div>
                  </div>

                  <div 
                    @click="addComponent('kpi')"
                    class="component-item bg-gray-50 hover:bg-blue-50 border border-gray-200 hover:border-blue-300 rounded-lg p-3 cursor-pointer transition-colors group"
                  >
                    <div class="component-icon mb-2">
                      <Icon name="target" class="w-6 h-6 text-gray-400 group-hover:text-blue-600 transition-colors" />
                    </div>
                    <div class="component-label text-xs font-medium text-gray-700 group-hover:text-blue-700">
                      KPI Card
                    </div>
                  </div>

                  <div 
                    @click="addComponent('metric')"
                    class="component-item bg-gray-50 hover:bg-blue-50 border border-gray-200 hover:border-blue-300 rounded-lg p-3 cursor-pointer transition-colors group"
                  >
                    <div class="component-icon mb-2">
                      <Icon name="hash" class="w-6 h-6 text-gray-400 group-hover:text-blue-600 transition-colors" />
                    </div>
                    <div class="component-label text-xs font-medium text-gray-700 group-hover:text-blue-700">
                      Metric
                    </div>
                  </div>
                </div>
              </div>

              <!-- Content Components -->
              <div class="category">
                <h3 class="text-sm font-semibold text-gray-900 mb-3 flex items-center gap-2">
                  <Icon name="type" class="w-4 h-4" />
                  Content
                </h3>
                <div class="components-grid grid grid-cols-2 gap-3">
                  <div 
                    @click="addComponent('text')"
                    class="component-item bg-gray-50 hover:bg-blue-50 border border-gray-200 hover:border-blue-300 rounded-lg p-3 cursor-pointer transition-colors group"
                  >
                    <div class="component-icon mb-2">
                      <Icon name="type" class="w-6 h-6 text-gray-400 group-hover:text-blue-600 transition-colors" />
                    </div>
                    <div class="component-label text-xs font-medium text-gray-700 group-hover:text-blue-700">
                      Text Block
                    </div>
                  </div>

                  <div 
                    @click="addComponent('image')"
                    class="component-item bg-gray-50 hover:bg-blue-50 border border-gray-200 hover:border-blue-300 rounded-lg p-3 cursor-pointer transition-colors group"
                  >
                    <div class="component-icon mb-2">
                      <Icon name="image" class="w-6 h-6 text-gray-400 group-hover:text-blue-600 transition-colors" />
                    </div>
                    <div class="component-label text-xs font-medium text-gray-700 group-hover:text-blue-700">
                      Image
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Templates Tab -->
          <div v-if="activeTab === 'templates'" class="templates-tab">
            <div class="template-categories space-y-6">
              <div 
                v-for="(templates, category) in templatesByCategory"
                :key="category"
                class="category"
              >
                <h3 class="text-sm font-semibold text-gray-900 mb-3 capitalize">
                  {{ category }} Templates
                </h3>
                <div class="templates-list space-y-3">
                  <div 
                    v-for="template in templates"
                    :key="template.id"
                    @click="createReportFromTemplate(template.id)"
                    class="template-item bg-gray-50 hover:bg-blue-50 border border-gray-200 hover:border-blue-300 rounded-lg p-4 cursor-pointer transition-colors group"
                  >
                    <div class="template-header flex items-center justify-between mb-2">
                      <h4 class="text-sm font-medium text-gray-900 group-hover:text-blue-700">
                        {{ template.name }}
                      </h4>
                      <div class="template-usage text-xs text-gray-500">
                        Used {{ template.usage_count }} times
                      </div>
                    </div>
                    <p class="text-xs text-gray-600 mb-3">{{ template.description }}</p>
                    <div class="template-tags flex flex-wrap gap-1">
                      <span 
                        v-for="tag in template.tags"
                        :key="tag"
                        class="tag text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded"
                      >
                        {{ tag }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Canvas Area -->
      <div class="canvas-area flex-1 flex flex-col">
        <!-- Canvas Header -->
        <div v-if="state.currentReport && !state.previewMode" class="canvas-header bg-gray-50 border-b border-gray-200 px-6 py-4">
          <div class="flex items-center justify-between">
            <div class="report-info">
              <input
                v-model="state.currentReport.name"
                class="text-xl font-bold text-gray-900 bg-transparent border-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white rounded px-2 py-1"
                placeholder="Report Name"
              />
              <textarea
                v-model="state.currentReport.description"
                class="text-sm text-gray-600 bg-transparent border-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white rounded px-2 py-1 mt-1 resize-none"
                placeholder="Report description..."
                rows="1"
              ></textarea>
            </div>
            
            <div class="canvas-actions flex items-center gap-3">
              <div class="component-count text-sm text-gray-500">
                {{ state.currentReport.components.length }} components
              </div>
              <button
                @click="refreshReportData"
                :disabled="state.isGenerating"
                class="refresh-btn p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <Icon name="refresh-cw" class="w-5 h-5" :class="{ 'animate-spin': state.isGenerating }" />
              </button>
            </div>
          </div>
        </div>

        <!-- Canvas Content -->
        <div class="canvas-content flex-1 p-6 bg-gray-100 overflow-auto">
          <div v-if="!state.currentReport" class="empty-state text-center py-16">
            <Icon name="file-plus" class="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 class="text-xl font-medium text-gray-900 mb-2">Create Your First Report</h3>
            <p class="text-gray-600 mb-6">Start with a template or build from scratch</p>
            <div class="actions flex justify-center gap-4">
              <button
                @click="createBlankReport"
                class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
              >
                Start from Scratch
              </button>
              <button
                @click="activeTab = 'templates'"
                class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-6 py-3 rounded-lg transition-colors"
              >
                Browse Templates
              </button>
            </div>
          </div>

          <div v-else class="report-canvas">
            <!-- Report Grid -->
            <div 
              class="report-grid bg-white rounded-lg shadow-sm min-h-[800px] relative"
              :class="{ 'pointer-events-none': state.previewMode }"
            >
              <!-- Grid Background -->
              <div class="grid-background absolute inset-0 opacity-10 pointer-events-none">
                <div class="grid-lines" style="background-image: repeating-linear-gradient(0deg, #d1d5db, #d1d5db 1px, transparent 1px, transparent 20px), repeating-linear-gradient(90deg, #d1d5db, #d1d5db 1px, transparent 1px, transparent 20px);"></div>
              </div>

              <!-- Report Components -->
              <div
                v-for="component in state.currentReport.components"
                :key="component.id"
                :style="getComponentStyle(component)"
                @click="selectComponent(component.id)"
                :class="[
                  'report-component group absolute border-2 transition-all duration-200 cursor-pointer',
                  state.selectedComponents.includes(component.id) 
                    ? 'border-blue-500 bg-blue-50/50' 
                    : 'border-transparent hover:border-gray-300 bg-white'
                ]"
              >
                <div class="component-wrapper h-full p-4 relative">
                  <!-- Component Header -->
                  <div class="component-header flex items-center justify-between mb-3">
                    <h3 class="component-title text-sm font-medium text-gray-900">
                      {{ component.title }}
                    </h3>
                    <div v-if="!state.previewMode" class="component-actions flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <button
                        @click.stop="editComponent(component.id)"
                        class="action-btn p-1 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                      >
                        <Icon name="settings" class="w-4 h-4" />
                      </button>
                      <button
                        @click.stop="duplicateComponent(component.id)"
                        class="action-btn p-1 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded transition-colors"
                      >
                        <Icon name="copy" class="w-4 h-4" />
                      </button>
                      <button
                        @click.stop="removeComponent(component.id)"
                        class="action-btn p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
                      >
                        <Icon name="trash-2" class="w-4 h-4" />
                      </button>
                    </div>
                  </div>

                  <!-- Component Content -->
                  <div class="component-content flex-1">
                    <!-- Chart Component -->
                    <div v-if="component.type === 'chart'" class="chart-component">
                      <div v-if="getComponentData(component.id)" class="chart-container">
                        <!-- Chart placeholder - would integrate with actual chart library -->
                        <div class="chart-placeholder bg-gray-50 rounded-lg p-4 h-48 flex items-center justify-center">
                          <div class="text-center">
                            <Icon :name="getChartIcon(component.visualization_config.chart_type)" class="w-12 h-12 text-gray-300 mx-auto mb-2" />
                            <div class="text-sm text-gray-600">{{ component.visualization_config.chart_type || 'line' }} Chart</div>
                            <div class="text-xs text-gray-500 mt-1">
                              {{ getComponentData(component.id)?.data.length || 0 }} data points
                            </div>
                          </div>
                        </div>
                      </div>
                      <div v-else class="loading-state p-4 text-center text-gray-500">
                        <Icon name="loader" class="w-6 h-6 animate-spin mx-auto mb-2" />
                        Loading chart data...
                      </div>
                    </div>

                    <!-- Table Component -->
                    <div v-else-if="component.type === 'table'" class="table-component">
                      <div v-if="getComponentData(component.id)" class="table-container">
                        <div class="overflow-x-auto">
                          <table class="min-w-full text-sm">
                            <thead class="bg-gray-50">
                              <tr>
                                <th 
                                  v-for="column in getTableColumns(getComponentData(component.id)?.data || [])"
                                  :key="column"
                                  class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase"
                                >
                                  {{ column }}
                                </th>
                              </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                              <tr 
                                v-for="(row, index) in (getComponentData(component.id)?.data || []).slice(0, 5)"
                                :key="index"
                              >
                                <td 
                                  v-for="column in getTableColumns(getComponentData(component.id)?.data || [])"
                                  :key="column"
                                  class="px-3 py-2 text-gray-900"
                                >
                                  {{ formatValue(row[column], component.visualization_config.format_values) }}
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </div>
                      </div>
                      <div v-else class="loading-state p-4 text-center text-gray-500">
                        <Icon name="loader" class="w-6 h-6 animate-spin mx-auto mb-2" />
                        Loading table data...
                      </div>
                    </div>

                    <!-- KPI Component -->
                    <div v-else-if="component.type === 'kpi'" class="kpi-component">
                      <div v-if="getComponentData(component.id)" class="kpi-content text-center">
                        <div class="kpi-value text-4xl font-bold text-blue-600 mb-2">
                          {{ formatValue(getKPIValue(component), component.visualization_config.format_values) }}
                        </div>
                        <div class="kpi-label text-sm text-gray-600">
                          {{ component.query_config.aggregations ? Object.keys(component.query_config.aggregations)[0] : 'Total' }}
                        </div>
                        <div class="kpi-meta text-xs text-gray-500 mt-2">
                          {{ getComponentData(component.id)?.metadata.total_records || 0 }} records
                        </div>
                      </div>
                      <div v-else class="loading-state p-4 text-center text-gray-500">
                        <Icon name="loader" class="w-6 h-6 animate-spin mx-auto mb-2" />
                        Loading KPI data...
                      </div>
                    </div>

                    <!-- Metric Component -->
                    <div v-else-if="component.type === 'metric'" class="metric-component">
                      <div v-if="getComponentData(component.id)" class="metric-content">
                        <div class="metric-value text-2xl font-bold text-gray-900 mb-1">
                          {{ formatValue(getMetricValue(component), component.visualization_config.format_values) }}
                        </div>
                        <div class="metric-change text-sm text-green-600 flex items-center gap-1">
                          <Icon name="trending-up" class="w-4 h-4" />
                          +12.5% vs last period
                        </div>
                      </div>
                      <div v-else class="loading-state p-4 text-center text-gray-500">
                        <Icon name="loader" class="w-6 h-6 animate-spin mx-auto mb-2" />
                        Loading metric data...
                      </div>
                    </div>

                    <!-- Text Component -->
                    <div v-else-if="component.type === 'text'" class="text-component">
                      <div 
                        v-if="!state.previewMode && state.selectedComponents.includes(component.id)"
                        class="text-editor"
                      >
                        <textarea
                          v-model="component.description"
                          class="w-full h-32 p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="Enter your text content here..."
                        ></textarea>
                      </div>
                      <div v-else class="text-content">
                        <div class="text-display text-gray-700 leading-relaxed">
                          {{ component.description || 'Click to edit text content...' }}
                        </div>
                      </div>
                    </div>

                    <!-- Image Component -->
                    <div v-else-if="component.type === 'image'" class="image-component">
                      <div class="image-placeholder bg-gray-100 rounded-lg p-8 text-center h-32 flex items-center justify-center">
                        <div>
                          <Icon name="image" class="w-12 h-12 text-gray-300 mx-auto mb-2" />
                          <div class="text-sm text-gray-500">Image placeholder</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Resize Handle -->
                  <div v-if="!state.previewMode && state.selectedComponents.includes(component.id)" class="resize-handle absolute bottom-0 right-0 w-4 h-4 bg-blue-500 cursor-se-resize"></div>
                </div>
              </div>

              <!-- Add Component Button -->
              <div v-if="!state.previewMode && state.currentReport.components.length === 0" class="add-component-prompt absolute inset-0 flex items-center justify-center">
                <div class="text-center">
                  <Icon name="plus-circle" class="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 class="text-lg font-medium text-gray-900 mb-2">Add Your First Component</h3>
                  <p class="text-gray-600 mb-4">Drag components from the sidebar or click to add</p>
                  <button
                    @click="addComponent('chart')"
                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                  >
                    Add Chart
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Sidebar - Component Properties -->
      <div v-if="!state.previewMode && state.selectedComponents.length > 0" class="properties-sidebar bg-white border-l border-gray-200 w-80 flex flex-col">
        <div class="sidebar-header border-b border-gray-200 p-4">
          <h3 class="text-lg font-semibold text-gray-900">Component Properties</h3>
        </div>
        
        <div class="sidebar-content flex-1 overflow-y-auto p-4 space-y-6">
          <div v-for="componentId in state.selectedComponents" :key="componentId">
            <div v-if="getSelectedComponent(componentId)" class="component-properties">
              <ComponentProperties
                :component="getSelectedComponent(componentId)"
                :available-data-sources="availableDataSources"
                :available-chart-types="availableChartTypes"
                @update="updateComponent(componentId, $event)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Icon } from '#components'
import { useCustomReporting } from '~/composables/useCustomReporting'
import type { ReportComponent } from '~/composables/useCustomReporting'

// Use composable
const {
  state,
  error,
  availableChartTypes,
  availableDataSources,
  templatesByCategory,
  canSaveReport,
  initializeReporting,
  createReportFromTemplate,
  createBlankReport,
  addComponent: addReportComponent,
  updateComponent,
  removeComponent,
  duplicateComponent,
  refreshReportData,
  saveReport,
  exportReport,
  loadComponentData,
  formatValue,
  getComponentIcon
} = useCustomReporting()

// Local state
const activeTab = ref<'components' | 'templates'>('components')
const editingComponent = ref<string | null>(null)

// Computed properties
const selectedComponent = computed(() => {
  if (state.selectedComponents.length !== 1) return null
  return state.currentReport?.components.find(c => c.id === state.selectedComponents[0])
})

// Methods
const togglePreview = () => {
  state.previewMode = !state.previewMode
  if (state.previewMode) {
    state.selectedComponents = []
  }
}

const addComponent = (type: ReportComponent['type'], config: any = {}) => {
  const component = addReportComponent(type)
  
  // Apply any configuration
  if (config.chart_type) {
    component.visualization_config.chart_type = config.chart_type
  }
  
  // Load initial data
  loadComponentData(component)
}

const selectComponent = (componentId: string) => {
  if (state.previewMode) return
  
  if (event?.ctrlKey || event?.metaKey) {
    // Multi-select
    if (state.selectedComponents.includes(componentId)) {
      state.selectedComponents = state.selectedComponents.filter(id => id !== componentId)
    } else {
      state.selectedComponents.push(componentId)
    }
  } else {
    // Single select
    state.selectedComponents = [componentId]
  }
}

const editComponent = (componentId: string) => {
  editingComponent.value = componentId
  state.selectedComponents = [componentId]
}

const getSelectedComponent = (componentId: string) => {
  return state.currentReport?.components.find(c => c.id === componentId)
}

const getComponentStyle = (component: ReportComponent) => {
  const { x, y, width, height } = component.layout
  return {
    left: `${x * 60}px`,
    top: `${y * 40}px`,
    width: `${width * 60}px`,
    height: `${height * 40}px`,
    minWidth: component.layout.min_width ? `${component.layout.min_width}px` : undefined,
    minHeight: component.layout.min_height ? `${component.layout.min_height}px` : undefined
  }
}

const getComponentData = (componentId: string) => {
  return state.reportData.get(componentId)
}

const getTableColumns = (data: any[]) => {
  if (!data || data.length === 0) return []
  return Object.keys(data[0]).slice(0, 6) // Limit columns for display
}

const getChartIcon = (chartType?: string) => {
  const icons = {
    line: 'trending-up',
    bar: 'bar-chart-2',
    pie: 'pie-chart',
    doughnut: 'circle',
    area: 'activity',
    scatter: 'scatter-chart',
    heatmap: 'grid'
  }
  return icons[chartType as keyof typeof icons] || 'bar-chart-2'
}

const getKPIValue = (component: ReportComponent) => {
  const data = getComponentData(component.id)
  if (!data || !data.aggregations) return 0
  
  const aggregationKey = Object.keys(data.aggregations)[0]
  return data.aggregations[aggregationKey] || 0
}

const getMetricValue = (component: ReportComponent) => {
  const data = getComponentData(component.id)
  if (!data) return 0
  
  if (data.aggregations) {
    const aggregationKey = Object.keys(data.aggregations)[0]
    return data.aggregations[aggregationKey] || 0
  }
  
  return data.data.length
}

// Component to handle properties editing
const ComponentProperties = {
  props: {
    component: Object,
    availableDataSources: Array,
    availableChartTypes: Array
  },
  emits: ['update'],
  template: `
    <div class="component-properties space-y-4">
      <!-- Basic Properties -->
      <div class="property-group">
        <label class="block text-sm font-medium text-gray-700 mb-2">Title</label>
        <input
          :value="component.title"
          @input="$emit('update', { title: $event.target.value })"
          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      <div class="property-group">
        <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
        <textarea
          :value="component.description"
          @input="$emit('update', { description: $event.target.value })"
          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
          rows="3"
        ></textarea>
      </div>

      <!-- Data Source -->
      <div class="property-group">
        <label class="block text-sm font-medium text-gray-700 mb-2">Data Source</label>
        <select
          :value="component.data_source"
          @change="$emit('update', { data_source: $event.target.value })"
          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option v-for="source in availableDataSources" :key="source.value" :value="source.value">
            {{ source.label }}
          </option>
        </select>
      </div>

      <!-- Chart Type (for chart components) -->
      <div v-if="component.type === 'chart'" class="property-group">
        <label class="block text-sm font-medium text-gray-700 mb-2">Chart Type</label>
        <select
          :value="component.visualization_config.chart_type"
          @change="$emit('update', { visualization_config: { ...component.visualization_config, chart_type: $event.target.value } })"
          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option v-for="type in availableChartTypes" :key="type.value" :value="type.value">
            {{ type.label }}
          </option>
        </select>
      </div>

      <!-- Value Format -->
      <div class="property-group">
        <label class="block text-sm font-medium text-gray-700 mb-2">Value Format</label>
        <select
          :value="component.visualization_config.format_values"
          @change="$emit('update', { visualization_config: { ...component.visualization_config, format_values: $event.target.value } })"
          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="number">Number</option>
          <option value="currency">Currency</option>
          <option value="percentage">Percentage</option>
          <option value="count">Count</option>
        </select>
      </div>

      <!-- Layout Properties -->
      <div class="property-group">
        <label class="block text-sm font-medium text-gray-700 mb-2">Layout</label>
        <div class="grid grid-cols-2 gap-3">
          <div>
            <label class="block text-xs text-gray-500 mb-1">Width</label>
            <input
              type="number"
              :value="component.layout.width"
              @input="$emit('update', { layout: { ...component.layout, width: parseInt($event.target.value) } })"
              class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
            />
          </div>
          <div>
            <label class="block text-xs text-gray-500 mb-1">Height</label>
            <input
              type="number"
              :value="component.layout.height"
              @input="$emit('update', { layout: { ...component.layout, height: parseInt($event.target.value) } })"
              class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>
    </div>
  `
}

// Lifecycle
onMounted(async () => {
  await initializeReporting()
})
</script>

<style scoped>
.report-builder {
  @apply h-screen bg-gray-50;
}

.sidebar {
  @apply flex-shrink-0;
}

.report-component {
  /* @apply group; */ /* group utility should be applied directly in template */
}

.report-component:hover .component-actions {
  @apply opacity-100;
}

.component-actions {
  @apply transition-opacity duration-200;
}

.grid-background {
  background-size: 20px 20px;
}

.resize-handle::after {
  content: '';
  @apply absolute bottom-0 right-0 w-2 h-2 bg-blue-500 transform rotate-45 translate-x-1 translate-y-1;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .sidebar {
    @apply w-64;
  }
  
  .properties-sidebar {
    @apply w-64;
  }
}

@media (max-width: 768px) {
  .builder-content {
    @apply flex-col;
  }
  
  .sidebar,
  .properties-sidebar {
    @apply w-full h-64 border-r-0 border-b;
  }
  
  .canvas-area {
    @apply min-h-96;
  }
}

/* Animation classes */
.component-item {
  @apply transition-all duration-200;
}

.component-item:hover {
  @apply transform scale-105;
}

.template-item {
  @apply transition-all duration-200;
}

.template-item:hover {
  @apply transform -translate-y-0.5 shadow-md;
}
</style>