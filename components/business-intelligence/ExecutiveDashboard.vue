<template>
  <div class="executive-dashboard">
    <!-- Dashboard Header with Real-time Status -->
    <div class="dashboard-header bg-gradient-to-r from-blue-600 to-purple-600 text-white">
      <div class="header-content px-6 py-6">
        <div class="flex items-center justify-between">
          <div class="header-info">
            <h1 class="text-3xl font-bold mb-2">Executive Dashboard</h1>
            <p class="text-blue-100 flex items-center gap-2">
              <Icon name="activity" class="w-4 h-4" />
              Real-time business intelligence and insights
            </p>
          </div>
          
          <div class="header-actions flex items-center gap-4">
            <!-- Auto-refresh indicator -->
            <div class="refresh-status flex items-center gap-2 text-sm text-blue-100">
              <div 
                :class="[
                  'w-2 h-2 rounded-full',
                  state.lastRefresh ? 'bg-green-400' : 'bg-yellow-400'
                ]"
              ></div>
              <span v-if="state.lastRefresh">
                Last updated: {{ formatTime(state.lastRefresh) }}
              </span>
              <span v-else>Connecting...</span>
            </div>

            <!-- Drill-down breadcrumb -->
            <div v-if="state.drillDownStack.length > 0" class="drill-down-breadcrumb">
              <nav class="flex items-center space-x-2 text-sm">
                <button
                  @click="navigateBack(0)"
                  class="text-blue-200 hover:text-white transition-colors"
                >
                  Overview
                </button>
                <Icon 
                  v-for="(level, index) in state.drillDownStack" 
                  :key="index"
                  name="chevron-right" 
                  class="w-4 h-4 text-blue-300" 
                />
                <span class="text-white font-medium">
                  {{ state.drillDownStack[state.drillDownStack.length - 1]?.category }}
                </span>
              </nav>
            </div>

            <!-- Action buttons -->
            <div class="action-buttons flex items-center gap-3">
              <button
                @click="refreshDashboard"
                :disabled="state.isLoading"
                class="btn-refresh p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-colors"
                :class="{ 'animate-spin': state.isLoading }"
              >
                <Icon name="refresh-cw" class="w-5 h-5" />
              </button>
              
              <button
                @click="exportDashboard('pdf')"
                class="btn-export bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <Icon name="download" class="w-4 h-4" />
                Export
              </button>
            </div>
          </div>
        </div>

        <!-- Executive KPI Status Bar -->
        <div class="kpi-status-bar mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
          <div 
            v-for="status in kpiStatusSummary" 
            :key="status.type"
            class="status-item bg-white/10 backdrop-blur-sm rounded-lg p-4"
          >
            <div class="flex items-center gap-3">
              <div 
                :class="[
                  'w-10 h-10 rounded-lg flex items-center justify-center',
                  status.color
                ]"
              >
                <Icon :name="status.icon" class="w-5 h-5" />
              </div>
              <div>
                <div class="text-2xl font-bold">{{ status.count }}</div>
                <div class="text-sm text-blue-100">{{ status.label }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Dashboard Content -->
    <div class="dashboard-content">
      <!-- Critical Alerts Banner -->
      <div v-if="criticalKPIs.length > 0" class="alerts-banner bg-red-50 border-l-4 border-red-400 p-4 mb-6">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <Icon name="alert-triangle" class="w-6 h-6 text-red-600" />
            <div>
              <h3 class="text-lg font-semibold text-red-800">Critical KPIs Require Attention</h3>
              <p class="text-red-700">{{ criticalKPIs.length }} metrics are below target thresholds</p>
            </div>
          </div>
          <button
            @click="showCriticalDetails = !showCriticalDetails"
            class="text-red-600 hover:text-red-800 px-3 py-1 border border-red-300 rounded hover:bg-red-100 transition-colors"
          >
            {{ showCriticalDetails ? 'Hide Details' : 'View Details' }}
          </button>
        </div>
        
        <div v-if="showCriticalDetails" class="mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div 
            v-for="kpi in criticalKPIs" 
            :key="kpi.id"
            class="critical-kpi bg-white rounded-lg border border-red-200 p-4"
          >
            <div class="flex items-center justify-between mb-2">
              <h4 class="font-medium text-gray-900">{{ kpi.name }}</h4>
              <span class="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">Critical</span>
            </div>
            <div class="flex items-center justify-between">
              <div class="text-2xl font-bold text-red-600">
                {{ formatKPIValue(kpi.current_value, kpi.unit) }}
              </div>
              <div class="text-sm text-gray-500">
                Target: {{ formatKPIValue(kpi.target_value, kpi.unit) }}
              </div>
            </div>
            <div class="mt-2 text-xs text-gray-600">
              {{ (kpi.current_value / kpi.target_value * 100).toFixed(1) }}% of target
            </div>
          </div>
        </div>
      </div>

      <!-- Main Dashboard Grid -->
      <div class="dashboard-grid">
        <!-- Overview Section -->
        <div v-if="state.drillDownStack.length === 0" class="overview-section">
          <!-- Key Metrics Cards -->
          <div class="metrics-row grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Revenue Growth -->
            <div class="metric-card bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
              <div class="flex items-center justify-between mb-4">
                <div class="metric-icon bg-green-100 p-3 rounded-lg">
                  <Icon name="trending-up" class="w-6 h-6 text-green-600" />
                </div>
                <button
                  @click="drillDown('revenue', 'total_revenue')"
                  class="drill-down-btn text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <Icon name="external-link" class="w-4 h-4" />
                </button>
              </div>
              <div class="metric-content">
                <div class="metric-value text-3xl font-bold text-gray-900 mb-1">
                  {{ formatCurrency(state.metrics.revenue.total_revenue) }}
                </div>
                <div class="metric-label text-sm text-gray-600 mb-2">Total Revenue</div>
                <div class="metric-trend flex items-center gap-1">
                  <Icon 
                    :name="getChangeIcon(revenueGrowthTrend.trend)" 
                    :class="['w-4 h-4', getChangeColor(revenueGrowthTrend.trend)]" 
                  />
                  <span :class="['text-sm font-medium', getChangeColor(revenueGrowthTrend.trend)]">
                    {{ formatPercentage(revenueGrowthTrend.growth) }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Customer Health Score -->
            <div class="metric-card bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
              <div class="flex items-center justify-between mb-4">
                <div class="metric-icon bg-blue-100 p-3 rounded-lg">
                  <Icon name="users" class="w-6 h-6 text-blue-600" />
                </div>
                <button
                  @click="drillDown('customer', 'active_customers')"
                  class="drill-down-btn text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <Icon name="external-link" class="w-4 h-4" />
                </button>
              </div>
              <div class="metric-content">
                <div class="metric-value text-3xl font-bold text-gray-900 mb-1">
                  {{ customerHealthScore.score }}
                </div>
                <div class="metric-label text-sm text-gray-600 mb-2">Customer Health Score</div>
                <div class="health-indicator">
                  <div 
                    :class="[
                      'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                      customerHealthScore.status === 'healthy' ? 'bg-green-100 text-green-800' :
                      customerHealthScore.status === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    ]"
                  >
                    {{ customerHealthScore.status }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Operational Efficiency -->
            <div class="metric-card bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
              <div class="flex items-center justify-between mb-4">
                <div class="metric-icon bg-purple-100 p-3 rounded-lg">
                  <Icon name="zap" class="w-6 h-6 text-purple-600" />
                </div>
                <button
                  @click="drillDown('operational', 'conversion_rate')"
                  class="drill-down-btn text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <Icon name="external-link" class="w-4 h-4" />
                </button>
              </div>
              <div class="metric-content">
                <div class="metric-value text-3xl font-bold text-gray-900 mb-1">
                  {{ operationalEfficiency.score }}%
                </div>
                <div class="metric-label text-sm text-gray-600 mb-2">Operational Efficiency</div>
                <div class="efficiency-breakdown">
                  <div class="text-xs text-gray-500">
                    System: {{ operationalEfficiency.breakdown.uptime.toFixed(1) }}% |
                    Response: {{ operationalEfficiency.breakdown.response.toFixed(0) }}ms avg
                  </div>
                </div>
              </div>
            </div>

            <!-- Active KPIs -->
            <div class="metric-card bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
              <div class="flex items-center justify-between mb-4">
                <div class="metric-icon bg-indigo-100 p-3 rounded-lg">
                  <Icon name="target" class="w-6 h-6 text-indigo-600" />
                </div>
                <button
                  @click="showKPIDetails = !showKPIDetails"
                  class="drill-down-btn text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <Icon name="list" class="w-4 h-4" />
                </button>
              </div>
              <div class="metric-content">
                <div class="metric-value text-3xl font-bold text-gray-900 mb-1">
                  {{ state.kpis.length }}
                </div>
                <div class="metric-label text-sm text-gray-600 mb-2">Active KPIs</div>
                <div class="kpi-status-mini flex gap-2">
                  <div class="flex items-center gap-1 text-xs">
                    <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                    {{ healthyKPIs.length }}
                  </div>
                  <div class="flex items-center gap-1 text-xs">
                    <div class="w-2 h-2 bg-yellow-400 rounded-full"></div>
                    {{ warningKPIs.length }}
                  </div>
                  <div class="flex items-center gap-1 text-xs">
                    <div class="w-2 h-2 bg-red-400 rounded-full"></div>
                    {{ criticalKPIs.length }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Business Insights Section -->
          <div class="insights-section mb-8">
            <div class="section-header flex items-center justify-between mb-6">
              <h2 class="text-2xl font-bold text-gray-900">AI Business Insights</h2>
              <button
                @click="generateInsights"
                :disabled="isGeneratingInsights"
                class="btn-generate-insights bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
              >
                <Icon name="lightbulb" class="w-5 h-5" :class="{ 'animate-pulse': isGeneratingInsights }" />
                {{ isGeneratingInsights ? 'Generating...' : 'Generate Insights' }}
              </button>
            </div>

            <div v-if="relevantInsights.length > 0" class="insights-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div 
                v-for="insight in relevantInsights.slice(0, 6)" 
                :key="insight.id"
                class="insight-card bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
              >
                <div class="insight-header flex items-start gap-4 mb-4">
                  <div 
                    :class="[
                      'insight-icon w-12 h-12 rounded-lg flex items-center justify-center flex-shrink-0',
                      getInsightColor(insight.type) === 'green' ? 'bg-green-100 text-green-600' :
                      getInsightColor(insight.type) === 'red' ? 'bg-red-100 text-red-600' :
                      getInsightColor(insight.type) === 'blue' ? 'bg-blue-100 text-blue-600' :
                      getInsightColor(insight.type) === 'yellow' ? 'bg-yellow-100 text-yellow-600' :
                      getInsightColor(insight.type) === 'purple' ? 'bg-purple-100 text-purple-600' :
                      'bg-gray-100 text-gray-600'
                    ]"
                  >
                    <Icon :name="getInsightIcon(insight.type)" class="w-6 h-6" />
                  </div>
                  <div class="flex-1">
                    <h3 class="text-lg font-semibold text-gray-900 mb-1">{{ insight.title }}</h3>
                    <div class="insight-meta flex items-center gap-4 text-sm text-gray-500">
                      <span>{{ formatConfidence(insight.confidence_score) }} confidence</span>
                      <span>{{ insight.relevance_score }}/100 relevance</span>
                    </div>
                  </div>
                </div>

                <div class="insight-content mb-4">
                  <p class="text-gray-700 mb-3">{{ insight.natural_language_explanation }}</p>
                  
                  <div class="impact-assessment">
                    <div class="flex items-center justify-between text-sm mb-2">
                      <span class="text-gray-600">Financial Impact</span>
                      <span 
                        :class="[
                          'font-medium',
                          insight.impact_assessment.financial_impact >= 0 ? 'text-green-600' : 'text-red-600'
                        ]"
                      >
                        {{ formatImpact(insight.impact_assessment.financial_impact) }}
                      </span>
                    </div>
                    <div class="flex items-center justify-between text-sm">
                      <span class="text-gray-600">Strategic Priority</span>
                      <span 
                        :class="[
                          'text-xs px-2 py-1 rounded-full font-medium',
                          insight.impact_assessment.strategic_importance === 'critical' ? 'bg-red-100 text-red-800' :
                          insight.impact_assessment.strategic_importance === 'high' ? 'bg-orange-100 text-orange-800' :
                          insight.impact_assessment.strategic_importance === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        ]"
                      >
                        {{ insight.impact_assessment.strategic_importance }}
                      </span>
                    </div>
                  </div>
                </div>

                <div class="insight-actions flex items-center justify-between">
                  <button
                    @click="provideFeedback(insight.id, { helpful: true, relevance_rating: 5, implemented: false })"
                    class="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1"
                  >
                    <Icon name="thumbs-up" class="w-4 h-4" />
                    Helpful
                  </button>
                  <button
                    @click="showInsightDetails(insight)"
                    class="text-sm text-gray-600 hover:text-gray-800 flex items-center gap-1"
                  >
                    <Icon name="eye" class="w-4 h-4" />
                    Details
                  </button>
                </div>
              </div>
            </div>

            <div v-else class="insights-empty text-center py-12">
              <Icon name="lightbulb" class="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 class="text-lg font-medium text-gray-900 mb-2">No Insights Available</h3>
              <p class="text-gray-600 mb-4">Generate AI-powered insights to discover opportunities and risks in your business data.</p>
              <button
                @click="generateInsights"
                :disabled="isGeneratingInsights"
                class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg"
              >
                Generate First Insights
              </button>
            </div>
          </div>

          <!-- Key Performance Indicators Detail -->
          <div v-if="showKPIDetails" class="kpis-section mb-8">
            <div class="section-header mb-6">
              <h2 class="text-2xl font-bold text-gray-900">Key Performance Indicators</h2>
            </div>

            <div class="kpis-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div 
                v-for="kpi in state.kpis" 
                :key="kpi.id"
                class="kpi-card bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
              >
                <div class="kpi-header flex items-center justify-between mb-4">
                  <h3 class="text-lg font-semibold text-gray-900">{{ kpi.name }}</h3>
                  <span 
                    :class="[
                      'status-badge text-xs px-2 py-1 rounded-full font-medium',
                      kpi.status === 'healthy' ? 'bg-green-100 text-green-800' :
                      kpi.status === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    ]"
                  >
                    {{ kpi.status }}
                  </span>
                </div>

                <div class="kpi-content">
                  <div class="kpi-values flex items-end justify-between mb-4">
                    <div>
                      <div class="current-value text-3xl font-bold text-gray-900">
                        {{ formatKPIValue(kpi.current_value, kpi.unit) }}
                      </div>
                      <div class="target-value text-sm text-gray-600">
                        Target: {{ formatKPIValue(kpi.target_value, kpi.unit) }}
                      </div>
                    </div>
                    <div class="trend-indicator">
                      <Icon 
                        :name="getChangeIcon(kpi.trend)" 
                        :class="['w-5 h-5', getChangeColor(kpi.trend)]" 
                      />
                    </div>
                  </div>

                  <div class="progress-bar mb-4">
                    <div class="progress-track bg-gray-200 rounded-full h-2">
                      <div 
                        class="progress-fill rounded-full h-2 transition-all duration-300"
                        :class="[
                          kpi.status === 'healthy' ? 'bg-green-500' :
                          kpi.status === 'warning' ? 'bg-yellow-500' :
                          'bg-red-500'
                        ]"
                        :style="{ 
                          width: `${Math.min(100, (kpi.current_value / kpi.target_value) * 100)}%` 
                        }"
                      ></div>
                    </div>
                    <div class="progress-label text-xs text-gray-500 mt-1">
                      {{ ((kpi.current_value / kpi.target_value) * 100).toFixed(1) }}% of target
                    </div>
                  </div>

                  <div class="kpi-meta text-xs text-gray-500">
                    <div>Category: {{ kpi.category }}</div>
                    <div>Updated: {{ formatTime(kpi.last_updated) }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Drill-down Content -->
        <div v-else class="drill-down-section">
          <div class="drill-down-header mb-6">
            <div class="flex items-center justify-between">
              <div>
                <h2 class="text-2xl font-bold text-gray-900 mb-2">
                  {{ currentDrillDown.category | capitalize }} Analysis
                </h2>
                <p class="text-gray-600">
                  Detailed view of {{ currentDrillDown.metric }} with {{ currentDrillDown.data.length }} records
                </p>
              </div>
              <button
                @click="navigateBack(state.drillDownStack.length - 1)"
                class="back-btn bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <Icon name="arrow-left" class="w-4 h-4" />
                Back
              </button>
            </div>
          </div>

          <!-- Drill-down Visualization -->
          <div class="drill-down-content bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div v-if="currentDrillDown.visualization_type === 'table'" class="data-table">
              <div class="table-container">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th 
                        v-for="column in getTableColumns(currentDrillDown.data)"
                        :key="column"
                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        {{ column }}
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-for="(row, index) in currentDrillDown.data.slice(0, 50)" :key="index">
                      <td 
                        v-for="column in getTableColumns(currentDrillDown.data)"
                        :key="column"
                        class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
                      >
                        {{ formatTableValue(row[column], column) }}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div v-else class="chart-container">
              <!-- Chart placeholder - would integrate with Chart.js or similar -->
              <div class="chart-placeholder bg-gray-50 rounded-lg p-8 text-center">
                <Icon name="bar-chart-2" class="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 class="text-lg font-medium text-gray-900 mb-2">{{ currentDrillDown.visualization_type }} Chart</h3>
                <p class="text-gray-600">Chart visualization would render here with the drill-down data</p>
              </div>
            </div>
          </div>

          <!-- Drill-down Summary -->
          <div class="drill-down-summary mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
            <div 
              v-for="(value, key) in currentDrillDown.aggregations"
              :key="key"
              class="summary-card bg-white rounded-lg border border-gray-200 p-4"
            >
              <div class="text-2xl font-bold text-gray-900">{{ formatNumber(value) }}</div>
              <div class="text-sm text-gray-600">{{ key.replace('_', ' ') | capitalize }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Insight Detail Modal -->
    <div v-if="selectedInsight" class="insight-modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="modal-content bg-white rounded-xl shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div class="modal-header border-b border-gray-200 p-6">
          <div class="flex items-center justify-between">
            <h2 class="text-2xl font-bold text-gray-900">{{ selectedInsight.title }}</h2>
            <button
              @click="selectedInsight = null"
              class="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <Icon name="x" class="w-6 h-6" />
            </button>
          </div>
        </div>
        
        <div class="modal-body p-6">
          <div class="insight-details grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="insight-main lg:col-span-2">
              <div class="insight-explanation mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Explanation</h3>
                <p class="text-gray-700">{{ selectedInsight.natural_language_explanation }}</p>
              </div>

              <div class="supporting-data mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Supporting Data</h3>
                <div class="data-grid grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div 
                    v-for="data in selectedInsight.supporting_data"
                    :key="data.source"
                    class="data-item bg-gray-50 rounded-lg p-4"
                  >
                    <div class="text-sm font-medium text-gray-900">{{ data.source }}</div>
                    <div class="text-2xl font-bold text-blue-600 my-1">{{ formatNumber(data.value) }}</div>
                    <div class="text-xs text-gray-500">{{ data.metric }} - {{ data.timeframe }}</div>
                  </div>
                </div>
              </div>

              <div class="recommendations">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Recommendations</h3>
                <div class="recommendations-list space-y-4">
                  <div 
                    v-for="rec in selectedInsight.recommendations"
                    :key="rec.action"
                    class="recommendation-item border border-gray-200 rounded-lg p-4"
                  >
                    <h4 class="font-medium text-gray-900 mb-2">{{ rec.action }}</h4>
                    <p class="text-gray-700 text-sm mb-3">{{ rec.expected_outcome }}</p>
                    <div class="rec-meta flex items-center gap-4 text-xs text-gray-500">
                      <span>Effort: {{ rec.effort_required }}</span>
                      <span>Timeline: {{ rec.timeline }}</span>
                      <span>Owner: {{ rec.responsible_party }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="insight-sidebar">
              <div class="insight-stats bg-gray-50 rounded-lg p-4 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Insight Metrics</h3>
                <div class="stats-grid space-y-3">
                  <div class="stat-item flex justify-between">
                    <span class="text-gray-600">Confidence</span>
                    <span class="font-medium">{{ formatConfidence(selectedInsight.confidence_score) }}</span>
                  </div>
                  <div class="stat-item flex justify-between">
                    <span class="text-gray-600">Relevance</span>
                    <span class="font-medium">{{ selectedInsight.relevance_score }}/100</span>
                  </div>
                  <div class="stat-item flex justify-between">
                    <span class="text-gray-600">Impact</span>
                    <span class="font-medium">{{ formatImpact(selectedInsight.impact_assessment.financial_impact) }}</span>
                  </div>
                  <div class="stat-item flex justify-between">
                    <span class="text-gray-600">Priority</span>
                    <span class="font-medium">{{ selectedInsight.impact_assessment.strategic_importance }}</span>
                  </div>
                </div>
              </div>

              <div class="insight-actions space-y-3">
                <button
                  @click="provideFeedback(selectedInsight.id, { helpful: true, relevance_rating: 5, implemented: false })"
                  class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  Mark as Helpful
                </button>
                <button
                  @click="provideFeedback(selectedInsight.id, { helpful: true, relevance_rating: 5, implemented: true })"
                  class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  Mark as Implemented
                </button>
                <button
                  @click="provideFeedback(selectedInsight.id, { helpful: false, relevance_rating: 2, implemented: false })"
                  class="w-full bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-lg transition-colors"
                >
                  Not Relevant
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Icon } from '#components'
import { useExecutiveDashboard } from '~/composables/useExecutiveDashboard'
import { useAIInsights } from '~/composables/useAIInsights'
import type { AIInsight } from '~/composables/useAIInsights'

// Use composables
const {
  state,
  error,
  criticalKPIs,
  warningKPIs,
  healthyKPIs,
  revenueGrowthTrend,
  customerHealthScore,
  operationalEfficiency,
  initializeDashboard,
  refreshDashboard,
  drillDown,
  navigateBack,
  exportDashboard,
  formatCurrency,
  formatPercentage,
  formatNumber,
  getChangeIcon,
  getChangeColor
} = useExecutiveDashboard()

const {
  state: insightsState,
  relevantInsights,
  generateInsights,
  provideFeedback,
  getInsightIcon,
  getInsightColor,
  formatConfidence,
  formatImpact
} = useAIInsights()

// Local state
const showCriticalDetails = ref(false)
const showKPIDetails = ref(false)
const selectedInsight = ref<AIInsight | null>(null)
const isGeneratingInsights = ref(false)

// Computed properties
const kpiStatusSummary = computed(() => [
  {
    type: 'healthy',
    count: healthyKPIs.value.length,
    label: 'Healthy KPIs',
    icon: 'check-circle',
    color: 'bg-green-500 text-white'
  },
  {
    type: 'warning',
    count: warningKPIs.value.length,
    label: 'Warning KPIs',
    icon: 'alert-circle',
    color: 'bg-yellow-500 text-white'
  },
  {
    type: 'critical',
    count: criticalKPIs.value.length,
    label: 'Critical KPIs',
    icon: 'alert-triangle',
    color: 'bg-red-500 text-white'
  },
  {
    type: 'total',
    count: state.kpis.length,
    label: 'Total KPIs',
    icon: 'target',
    color: 'bg-blue-500 text-white'
  }
])

const currentDrillDown = computed(() => {
  return state.drillDownStack[state.drillDownStack.length - 1]
})

// Methods
const formatTime = (date: Date | string) => {
  const d = typeof date === 'string' ? new Date(date) : date
  return new Intl.DateTimeFormat('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    second: '2-digit'
  }).format(d)
}

const formatKPIValue = (value: number, unit: string) => {
  switch (unit) {
    case 'currency':
      return formatCurrency(value)
    case 'percentage':
      return formatPercentage(value / 100) // Assuming value is already in decimal form
    case 'count':
      return formatNumber(value)
    default:
      return value.toString()
  }
}

const showInsightDetails = (insight: AIInsight) => {
  selectedInsight.value = insight
}

const handleGenerateInsights = async () => {
  isGeneratingInsights.value = true
  try {
    await generateInsights()
  } finally {
    isGeneratingInsights.value = false
  }
}

const getTableColumns = (data: any[]) => {
  if (!data.length) return []
  return Object.keys(data[0]).slice(0, 8) // Limit columns for readability
}

const formatTableValue = (value: any, column: string) => {
  if (value === null || value === undefined) return '-'
  if (typeof value === 'number') {
    if (column.includes('amount') || column.includes('revenue')) {
      return formatCurrency(value)
    }
    if (column.includes('rate') || column.includes('percentage')) {
      return formatPercentage(value)
    }
    return formatNumber(value)
  }
  if (value instanceof Date) {
    return value.toLocaleDateString()
  }
  return value.toString()
}

// Vue 3 filters replacement
const capitalize = (str: string) => {
  return str.charAt(0).toUpperCase() + str.slice(1).replace(/_/g, ' ')
}

// Lifecycle
onMounted(async () => {
  await initializeDashboard()
})

onUnmounted(() => {
  // Cleanup any subscriptions
})
</script>

<style scoped>
.executive-dashboard {
  @apply min-h-screen bg-gray-50;
}

.dashboard-header {
  @apply sticky top-0 z-10;
}

.dashboard-content {
  @apply p-6;
}

.metric-card {
  @apply relative overflow-hidden;
}

.metric-card:hover .drill-down-btn {
  @apply opacity-100;
}

.drill-down-btn {
  @apply opacity-0 transition-opacity;
}

.insight-card {
  @apply transform transition-transform hover:-translate-y-1;
}

.insight-modal {
  @apply backdrop-blur-sm;
}

.table-container {
  @apply max-h-96 overflow-y-auto;
}

.chart-placeholder {
  @apply min-h-64;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dashboard-header .header-content {
    @apply px-4 py-4;
  }
  
  .dashboard-header .header-actions {
    @apply flex-col gap-2;
  }
  
  .metrics-row {
    @apply grid-cols-1;
  }
  
  .insights-grid {
    @apply grid-cols-1;
  }
  
  .kpis-grid {
    @apply grid-cols-1;
  }
  
  .insight-modal .modal-content {
    @apply mx-2;
  }
  
  .insight-details {
    @apply grid-cols-1;
  }
}

/* Animation classes */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Loading states */
.loading-shimmer {
  @apply bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] animate-pulse;
}
</style>