<template>
  <div class="bi-dashboard">
    <!-- Dashboard Header -->
    <div class="dashboard-header bg-white border-b border-gray-200 px-6 py-4">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Business Intelligence Dashboard</h1>
          <p class="text-gray-600 mt-1">Advanced analytics and predictive insights</p>
        </div>
        
        <div class="header-actions flex items-center gap-3">
          <div class="dashboard-selector">
            <select 
              v-model="selectedDashboard"
              @change="loadDashboard"
              class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="overview">Overview</option>
              <option value="cash_flow">Cash Flow Analytics</option>
              <option value="customer_insights">Customer Insights</option>
              <option value="workflow_performance">Workflow Performance</option>
              <option value="predictive_analytics">Predictive Analytics</option>
            </select>
          </div>
          
          <button
            @click="refreshDashboard"
            :disabled="isLoading"
            class="refresh-btn p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <Icon name="refresh-cw" class="w-5 h-5" :class="{ 'animate-spin': isLoading }" />
          </button>
          
          <button
            @click="generateInsights"
            :disabled="isGeneratingInsights"
            class="insights-btn bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
          >
            <Icon name="lightbulb" class="w-4 h-4" :class="{ 'animate-pulse': isGeneratingInsights }" />
            {{ isGeneratingInsights ? 'Generating...' : 'Generate Insights' }}
          </button>
          
          <button
            @click="exportDashboard"
            class="export-btn bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
          >
            <Icon name="download" class="w-4 h-4" />
            Export
          </button>
        </div>
      </div>
    </div>

    <!-- Key Insights Banner -->
    <div v-if="keyInsights.length > 0" class="insights-banner bg-gradient-to-r from-purple-50 to-blue-50 border-b border-gray-200 p-6">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
          <Icon name="lightbulb" class="w-5 h-5 text-purple-600" />
          Key Insights
        </h2>
        <button
          @click="showAllInsights = !showAllInsights"
          class="text-sm text-purple-600 hover:text-purple-800"
        >
          {{ showAllInsights ? 'Show Less' : 'View All' }}
        </button>
      </div>
      
      <div class="insights-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div 
          v-for="insight in displayedInsights"
          :key="insight.id"
          class="insight-card bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow"
        >
          <div class="flex items-start gap-3">
            <div 
              :class="[
                'w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0',
                getInsightIconClass(insight.type)
              ]"
            >
              <Icon :name="getInsightIcon(insight.type)" class="w-4 h-4" />
            </div>
            <div class="flex-1">
              <h3 class="text-sm font-medium text-gray-900 mb-1">{{ insight.title }}</h3>
              <p class="text-xs text-gray-600 mb-2">{{ insight.description }}</p>
              <div class="flex items-center justify-between">
                <span 
                  :class="[
                    'text-xs px-2 py-1 rounded-full',
                    getImpactClass(insight.impact_score)
                  ]"
                >
                  {{ getImpactLabel(insight.impact_score) }} Impact
                </span>
                <span class="text-xs text-gray-500">
                  {{ (insight.confidence_level * 100).toFixed(0) }}% confidence
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Dashboard Content -->
    <div class="dashboard-content flex-1 p-6">
      <!-- Overview Dashboard -->
      <div v-if="selectedDashboard === 'overview'" class="overview-dashboard">
        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          <!-- Cash Flow Summary -->
          <div class="widget-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="widget-header flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900">Cash Flow Forecast</h3>
              <Icon name="trending-up" class="w-5 h-5 text-green-600" />
            </div>
            <div class="cash-flow-summary">
              <div class="metric-row flex items-center justify-between mb-3">
                <span class="text-sm text-gray-600">Next 30 Days</span>
                <span class="text-lg font-bold text-green-600">
                  {{ formatCurrency(cashFlowForecast?.period_30d?.net_cash_flow || 0) }}
                </span>
              </div>
              <div class="metric-row flex items-center justify-between mb-3">
                <span class="text-sm text-gray-600">Next 60 Days</span>
                <span class="text-lg font-bold text-blue-600">
                  {{ formatCurrency(cashFlowForecast?.period_60d?.net_cash_flow || 0) }}
                </span>
              </div>
              <div class="metric-row flex items-center justify-between">
                <span class="text-sm text-gray-600">Next 90 Days</span>
                <span class="text-lg font-bold text-purple-600">
                  {{ formatCurrency(cashFlowForecast?.period_90d?.net_cash_flow || 0) }}
                </span>
              </div>
            </div>
            <div class="confidence-indicator mt-4 p-3 bg-gray-50 rounded-lg">
              <div class="flex items-center justify-between">
                <span class="text-xs text-gray-600">Prediction Confidence</span>
                <span class="text-xs font-medium text-gray-900">
                  {{ (cashFlowForecast?.confidence_score || 0) * 100 }}%
                </span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div 
                  class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  :style="{ width: `${(cashFlowForecast?.confidence_score || 0) * 100}%` }"
                ></div>
              </div>
            </div>
          </div>

          <!-- Customer Insights -->
          <div class="widget-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="widget-header flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900">Customer Insights</h3>
              <Icon name="users" class="w-5 h-5 text-blue-600" />
            </div>
            <div class="customer-metrics space-y-4">
              <div class="metric-item">
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm text-gray-600">Payment Reliability</span>
                  <span class="text-sm font-medium text-gray-900">{{ averageReliabilityScore }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    class="bg-green-600 h-2 rounded-full"
                    :style="{ width: `${averageReliabilityScore}%` }"
                  ></div>
                </div>
              </div>
              
              <div class="metric-item">
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm text-gray-600">Churn Risk</span>
                  <span class="text-sm font-medium text-gray-900">{{ averageChurnRisk }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    class="bg-red-600 h-2 rounded-full"
                    :style="{ width: `${averageChurnRisk}%` }"
                  ></div>
                </div>
              </div>
              
              <div class="customer-segments mt-4">
                <h4 class="text-sm font-medium text-gray-900 mb-2">Customer Segments</h4>
                <div class="segments-list space-y-2">
                  <div 
                    v-for="segment in customerSegments"
                    :key="segment.name"
                    class="segment-item flex items-center justify-between text-xs"
                  >
                    <span class="text-gray-600">{{ segment.name }}</span>
                    <span class="font-medium text-gray-900">{{ segment.count }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Workflow Performance -->
          <div class="widget-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="widget-header flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900">Workflow Performance</h3>
              <Icon name="zap" class="w-5 h-5 text-yellow-600" />
            </div>
            <div class="workflow-metrics space-y-4">
              <div class="metric-item">
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600">Active Workflows</span>
                  <span class="text-lg font-bold text-gray-900">{{ activeWorkflowCount }}</span>
                </div>
              </div>
              
              <div class="metric-item">
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600">Success Rate</span>
                  <span class="text-lg font-bold text-green-600">{{ averageSuccessRate }}%</span>
                </div>
              </div>
              
              <div class="metric-item">
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600">Executions Today</span>
                  <span class="text-lg font-bold text-blue-600">{{ todayExecutions }}</span>
                </div>
              </div>
              
              <div class="recent-optimizations mt-4">
                <h4 class="text-sm font-medium text-gray-900 mb-2">Recent Optimizations</h4>
                <div class="optimizations-list space-y-2">
                  <div 
                    v-for="optimization in recentOptimizations"
                    :key="optimization.id"
                    class="optimization-item p-2 bg-gray-50 rounded text-xs"
                  >
                    <div class="font-medium text-gray-900">{{ optimization.title }}</div>
                    <div class="text-gray-600">{{ optimization.impact }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Recommendations Section -->
        <div class="recommendations-section mt-8">
          <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
              <h3 class="text-lg font-semibold text-gray-900">AI-Powered Recommendations</h3>
            </div>
            
            <div class="recommendations-grid p-6">
              <div v-if="recommendations.length === 0" class="text-center py-8">
                <Icon name="lightbulb" class="w-12 h-12 text-gray-300 mx-auto mb-3" />
                <p class="text-gray-600">No recommendations available. Generate insights to see AI-powered suggestions.</p>
              </div>
              
              <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div 
                  v-for="recommendation in recommendations.slice(0, 6)"
                  :key="recommendation.id"
                  class="recommendation-card border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div class="flex items-start gap-3">
                    <div 
                      :class="[
                        'w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0',
                        getRecommendationIconClass(recommendation.category)
                      ]"
                    >
                      <Icon :name="getRecommendationIcon(recommendation.category)" class="w-4 h-4" />
                    </div>
                    <div class="flex-1">
                      <h4 class="text-sm font-medium text-gray-900 mb-1">{{ recommendation.title }}</h4>
                      <p class="text-xs text-gray-600 mb-3">{{ recommendation.description }}</p>
                      
                      <div class="impact-metrics mb-3">
                        <div class="flex items-center justify-between text-xs">
                          <span class="text-gray-500">Revenue Impact</span>
                          <span class="font-medium text-green-600">
                            +{{ formatCurrency(recommendation.expected_impact.revenue_impact) }}
                          </span>
                        </div>
                        <div class="flex items-center justify-between text-xs mt-1">
                          <span class="text-gray-500">Implementation</span>
                          <span 
                            :class="[
                              'font-medium',
                              recommendation.estimated_effort === 'low' ? 'text-green-600' :
                              recommendation.estimated_effort === 'medium' ? 'text-yellow-600' : 'text-red-600'
                            ]"
                          >
                            {{ recommendation.estimated_effort }} effort
                          </span>
                        </div>
                      </div>
                      
                      <div class="recommendation-actions flex items-center gap-2">
                        <button
                          @click="implementRecommendation(recommendation)"
                          class="text-xs bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 transition-colors"
                        >
                          Implement
                        </button>
                        <button
                          @click="dismissRecommendation(recommendation)"
                          class="text-xs text-gray-600 hover:text-gray-800"
                        >
                          Dismiss
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Other Dashboard Views - TODO: Implement these components -->
      <div v-else-if="selectedDashboard === 'cash_flow'" class="cash-flow-dashboard">
        <div class="text-center text-gray-500 py-8">Cash Flow Analytics coming soon...</div>
        <!-- <CashFlowAnalyticsDashboard /> -->
      </div>
      
      <div v-else-if="selectedDashboard === 'customer_insights'" class="customer-insights-dashboard">
        <div class="text-center text-gray-500 py-8">Customer Insights coming soon...</div>
        <!-- <CustomerInsightsDashboard /> -->
      </div>
      
      <div v-else-if="selectedDashboard === 'workflow_performance'" class="workflow-dashboard">
        <div class="text-center text-gray-500 py-8">Workflow Performance coming soon...</div>
        <!-- <WorkflowPerformanceDashboard /> -->
      </div>
      
      <div v-else-if="selectedDashboard === 'predictive_analytics'" class="predictive-dashboard">
        <div class="text-center text-gray-500 py-8">Predictive Analytics coming soon...</div>
        <!-- <PredictiveAnalyticsDashboard /> -->
      </div>
    </div>

    <!-- Insights Detail Modal - TODO: Implement this component -->
    <!-- 
    <InsightDetailModal
      v-if="selectedInsight"
      :insight="selectedInsight"
      @close="selectedInsight = null"
      @implement="implementInsight"
    />
    -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Icon } from '#components'
import type { 
  AnalyticsInsight, 
  AnalyticsRecommendation, 
  CashFlowPrediction 
} from '~/types/businessIntelligence'

// Import child components - TODO: Create these components
// import CashFlowAnalyticsDashboard from './CashFlowAnalyticsDashboard.vue'
// import CustomerInsightsDashboard from './CustomerInsightsDashboard.vue'
// import WorkflowPerformanceDashboard from './WorkflowPerformanceDashboard.vue'
// import PredictiveAnalyticsDashboard from './PredictiveAnalyticsDashboard.vue'
// import InsightDetailModal from './InsightDetailModal.vue'

// Use composables
const { 
  isLoading: isCashFlowLoading,
  cashFlowPredictions,
  generateCashFlowPrediction
} = useCashFlowAnalytics()

const {
  isLoading: isWorkflowLoading,
  activeWorkflowCount,
  averageSuccessRate
} = useInvoiceWorkflows()

// Local state
const selectedDashboard = ref('overview')
const isLoading = ref(false)
const isGeneratingInsights = ref(false)
const showAllInsights = ref(false)
const selectedInsight = ref<AnalyticsInsight | null>(null)

const keyInsights = ref<AnalyticsInsight[]>([])
const recommendations = ref<AnalyticsRecommendation[]>([])
const cashFlowForecast = ref<CashFlowPrediction | null>(null)

// Mock data for demonstration
const averageReliabilityScore = ref(78)
const averageChurnRisk = ref(15)
const todayExecutions = ref(42)

const customerSegments = ref([
  { name: 'Premium', count: 12 },
  { name: 'High Value', count: 28 },
  { name: 'Medium Value', count: 45 },
  { name: 'Low Value', count: 23 }
])

const recentOptimizations = ref([
  { id: '1', title: 'Payment Terms Optimization', impact: '+12% faster payments' },
  { id: '2', title: 'Email Template A/B Test', impact: '+8% open rate' },
  { id: '3', title: 'Workflow Automation', impact: '-25% manual work' }
])

// Computed properties
const displayedInsights = computed(() => {
  return showAllInsights.value ? keyInsights.value : keyInsights.value.slice(0, 3)
})

// Methods
const loadDashboard = async () => {
  isLoading.value = true
  
  try {
    switch (selectedDashboard.value) {
      case 'overview':
        await loadOverviewData()
        break
      case 'cash_flow':
        await loadCashFlowData()
        break
      // Add other dashboard loading logic
    }
  } catch (err: any) {
    console.error('Error loading dashboard:', err)
  } finally {
    isLoading.value = false
  }
}

const loadOverviewData = async () => {
  // Load cash flow forecast
  if (cashFlowPredictions.value.length === 0) {
    const prediction = await generateCashFlowPrediction(currentUser.value?.uid || '')
    if (prediction) {
      cashFlowForecast.value = prediction
    }
  } else {
    cashFlowForecast.value = cashFlowPredictions.value[0]
  }

  // Load recent insights and recommendations
  await loadRecentInsights()
  await loadRecommendations()
}

const loadCashFlowData = async () => {
  // Implementation for cash flow specific data
}

const refreshDashboard = async () => {
  await loadDashboard()
}

const generateInsights = async () => {
  isGeneratingInsights.value = true
  
  try {
    // Simulate AI insight generation
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Generate mock insights
    const newInsights: AnalyticsInsight[] = [
      {
        id: 'insight-1',
        type: 'opportunity',
        title: 'Payment Terms Optimization Opportunity',
        description: 'Adjusting payment terms for high-value customers could improve cash flow by 15%',
        impact_score: 85,
        confidence_level: 0.92,
        supporting_data: [],
        related_metrics: ['cash_flow', 'payment_timing'],
        actionable: true,
        auto_generated: true,
        created_at: new Date()
      },
      {
        id: 'insight-2',
        type: 'risk',
        title: 'Customer Concentration Risk',
        description: 'Top 3 customers represent 65% of revenue, creating concentration risk',
        impact_score: 70,
        confidence_level: 0.88,
        supporting_data: [],
        related_metrics: ['customer_concentration', 'revenue_distribution'],
        actionable: true,
        auto_generated: true,
        created_at: new Date()
      }
    ]
    
    keyInsights.value = newInsights
  } catch (err: any) {
    console.error('Error generating insights:', err)
  } finally {
    isGeneratingInsights.value = false
  }
}

const loadRecentInsights = async () => {
  // Implementation to load recent insights from database
}

const loadRecommendations = async () => {
  // Mock recommendations
  recommendations.value = [
    {
      id: 'rec-1',
      category: 'optimization',
      title: 'Implement Dynamic Payment Terms',
      description: 'Adjust payment terms based on customer reliability scores',
      rationale: 'Analysis shows reliable customers can handle extended terms',
      expected_impact: {
        revenue_impact: 2500,
        cost_impact: -200,
        risk_reduction: 0.15,
        efficiency_gain: 0.2
      },
      implementation_steps: [],
      estimated_effort: 'medium',
      estimated_timeline: 30,
      required_resources: ['Development', 'Finance'],
      priority: 'high',
      status: 'pending',
      created_at: new Date(),
      updated_at: new Date()
    }
  ]
}

const exportDashboard = async () => {
  try {
    const response = await $fetch('/api/business-intelligence/export', {
      method: 'POST',
      body: {
        dashboard: selectedDashboard.value,
        format: 'pdf'
      }
    })

    if (response.success && response.download_url) {
      window.open(response.download_url, '_blank')
    }
  } catch (err: any) {
    console.error('Error exporting dashboard:', err)
  }
}

const implementRecommendation = async (recommendation: AnalyticsRecommendation) => {
  try {
    const response = await $fetch('/api/business-intelligence/implement-recommendation', {
      method: 'POST',
      body: {
        recommendation_id: recommendation.id
      }
    })

    if (response.success) {
      // Update recommendation status
      const index = recommendations.value.findIndex(r => r.id === recommendation.id)
      if (index !== -1) {
        recommendations.value[index].status = 'approved'
      }
    }
  } catch (err: any) {
    console.error('Error implementing recommendation:', err)
  }
}

const dismissRecommendation = async (recommendation: AnalyticsRecommendation) => {
  const index = recommendations.value.findIndex(r => r.id === recommendation.id)
  if (index !== -1) {
    recommendations.value.splice(index, 1)
  }
}

const implementInsight = async (insight: AnalyticsInsight) => {
  // Implementation for insight actions
}

// Utility functions
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount)
}

const getInsightIcon = (type: string): string => {
  const icons = {
    opportunity: 'trending-up',
    risk: 'alert-triangle',
    trend: 'activity',
    anomaly: 'zap',
    correlation: 'link'
  }
  return icons[type as keyof typeof icons] || 'info'
}

const getInsightIconClass = (type: string): string => {
  const classes = {
    opportunity: 'bg-green-100 text-green-600',
    risk: 'bg-red-100 text-red-600',
    trend: 'bg-blue-100 text-blue-600',
    anomaly: 'bg-yellow-100 text-yellow-600',
    correlation: 'bg-purple-100 text-purple-600'
  }
  return classes[type as keyof typeof classes] || 'bg-gray-100 text-gray-600'
}

const getImpactClass = (score: number): string => {
  if (score >= 80) return 'bg-red-100 text-red-800'
  if (score >= 60) return 'bg-yellow-100 text-yellow-800'
  if (score >= 40) return 'bg-blue-100 text-blue-800'
  return 'bg-gray-100 text-gray-800'
}

const getImpactLabel = (score: number): string => {
  if (score >= 80) return 'High'
  if (score >= 60) return 'Medium'
  if (score >= 40) return 'Low'
  return 'Minimal'
}

const getRecommendationIcon = (category: string): string => {
  const icons = {
    optimization: 'trending-up',
    risk_mitigation: 'shield',
    growth: 'arrow-up',
    efficiency: 'zap',
    customer_experience: 'heart'
  }
  return icons[category as keyof typeof icons] || 'lightbulb'
}

const getRecommendationIconClass = (category: string): string => {
  const classes = {
    optimization: 'bg-blue-100 text-blue-600',
    risk_mitigation: 'bg-red-100 text-red-600',
    growth: 'bg-green-100 text-green-600',
    efficiency: 'bg-yellow-100 text-yellow-600',
    customer_experience: 'bg-purple-100 text-purple-600'
  }
  return classes[category as keyof typeof classes] || 'bg-gray-100 text-gray-600'
}

// Lifecycle
onMounted(() => {
  loadDashboard()
})
</script>

<style scoped>
.bi-dashboard {
  @apply flex flex-col h-full bg-gray-50;
}

.dashboard-header {
  @apply bg-white border-b border-gray-200 px-6 py-4;
}

.insights-banner {
  @apply bg-gradient-to-r from-purple-50 to-blue-50 border-b border-gray-200 p-6;
}

.insight-card {
  @apply bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow;
}

.widget-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.recommendation-card {
  @apply border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow;
}

.dashboard-content {
  @apply flex-1 p-6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .header-actions {
    @apply flex-col gap-2;
  }
  
  .insights-grid {
    @apply grid-cols-1;
  }
  
  .recommendations-grid .grid {
    @apply grid-cols-1;
  }
}
</style>
