<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
    <!-- Component Header -->
    <div class="px-4 py-3 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
      <h3 class="text-sm font-medium text-gray-700">Email Templates</h3>
      <button 
        @click="showCreateForm = true" 
        class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        <Icon name="mdi:plus" class="mr-1 h-4 w-4" />
        New Template
      </button>
    </div>
    
    <!-- Template List -->
    <div v-if="!showCreateForm && !editingTemplate" class="p-4">
      <div v-if="loading" class="py-4 flex justify-center">
        <Icon name="mdi:loading" class="h-5 w-5 text-blue-600 animate-spin" />
      </div>
      
      <div v-else-if="templates.length === 0" class="py-4 text-center text-gray-500">
        No email templates found. Create one to get started.
      </div>
      
      <div v-else class="space-y-4">
        <div v-for="template in templates" :key="template.id" class="border border-gray-200 rounded-md overflow-hidden">
          <div class="px-4 py-2 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
            <div class="font-medium text-sm">{{ template.name }}</div>
            <div class="flex space-x-2">
              <button 
                @click="previewTemplate(template)" 
                class="text-xs px-2 py-1 bg-gray-100 text-gray-800 rounded hover:bg-gray-200"
              >
                Preview
              </button>
              <button 
                @click="startEditing(template)" 
                class="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded hover:bg-blue-200"
              >
                Edit
              </button>
            </div>
          </div>
          <div class="p-4">
            <div class="text-xs text-gray-500 mb-1">Subject: {{ template.subject }}</div>
            <div class="text-sm text-gray-500 mb-2">{{ template.description || 'No description' }}</div>
            <div class="text-sm text-gray-700 line-clamp-3" v-html="template.content"></div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Create/Edit Form -->
    <div v-if="showCreateForm || editingTemplate" class="p-4">
      <form @submit.prevent="saveTemplate" class="space-y-4">
        <!-- Template Name -->
        <div>
          <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
            Template Name
          </label>
          <input 
            id="name" 
            v-model="templateForm.name" 
            type="text" 
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            placeholder="Enter template name"
            required
          />
        </div>
        
        <!-- Template Subject -->
        <div>
          <label for="subject" class="block text-sm font-medium text-gray-700 mb-1">
            Email Subject
          </label>
          <input 
            id="subject" 
            v-model="templateForm.subject" 
            type="text" 
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            placeholder="Enter email subject"
            required
          />
        </div>
        
        <!-- Template Description -->
        <div>
          <label for="description" class="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <input 
            id="description" 
            v-model="templateForm.description" 
            type="text" 
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            placeholder="Enter template description (optional)"
          />
        </div>
        
        <!-- Template Content -->
        <div>
          <label for="content" class="block text-sm font-medium text-gray-700 mb-1">
            Template Content
          </label>
          <div class="mb-2 text-xs text-gray-500">
            Use {{variable}} syntax for dynamic content. Available variables: {{firstName}}, {{lastName}}, {{email}}
          </div>
          <textarea 
            id="content" 
            v-model="templateForm.content" 
            rows="8" 
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            placeholder="Enter template content"
            required
          ></textarea>
        </div>
        
        <!-- Error Message -->
        <div v-if="error" class="text-red-600 text-sm">
          {{ error }}
        </div>
        
        <!-- Form Actions -->
        <div class="flex justify-end space-x-3">
          <button 
            type="button" 
            @click="cancelEdit" 
            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button 
            type="submit" 
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            :disabled="saving"
          >
            <Icon v-if="saving" name="mdi:loading" class="animate-spin -ml-1 mr-2 h-4 w-4" />
            {{ saving ? 'Saving...' : (editingTemplate ? 'Update Template' : 'Create Template') }}
          </button>
        </div>
      </form>
    </div>
    
    <!-- Preview Modal -->
    <div v-if="showPreview" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
        <div class="px-4 py-3 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
          <h3 class="text-lg font-medium text-gray-700">Template Preview: {{ previewData.name }}</h3>
          <button @click="showPreview = false" class="text-gray-400 hover:text-gray-500">
            <Icon name="mdi:close" class="h-5 w-5" />
          </button>
        </div>
        <div class="p-6 overflow-y-auto max-h-[calc(80vh-4rem)]">
          <div class="mb-4">
            <div class="text-sm font-medium text-gray-700 mb-1">Subject:</div>
            <div class="text-sm text-gray-900 border border-gray-200 rounded p-2">
              {{ previewData.subject }}
            </div>
          </div>
          <div>
            <div class="text-sm font-medium text-gray-700 mb-1">Content:</div>
            <div class="text-sm text-gray-900 border border-gray-200 rounded p-4 prose prose-sm max-w-none" v-html="previewData.content"></div>
          </div>
        </div>
        <div class="px-4 py-3 bg-gray-50 border-t border-gray-200 flex justify-end">
          <button 
            @click="showPreview = false" 
            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useEmail } from '~/composables/useEmail'

// Props
const props = defineProps({
  // Whether to show the create form by default
  showCreateFormByDefault: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['template-created', 'template-updated'])

// State
const templates = ref<any[]>([])
const showCreateForm = ref(props.showCreateFormByDefault)
const editingTemplate = ref<any>(null)
const templateForm = ref({
  name: '',
  subject: '',
  content: '',
  description: ''
})
const error = ref('')
const loading = ref(false)
const saving = ref(false)
const showPreview = ref(false)
const previewData = ref<any>({})

// Get email composable
const { getEmailTemplates, createEmailTemplate, updateEmailTemplate } = useEmail()

// Load templates
const loadTemplates = async () => {
  loading.value = true
  
  try {
    templates.value = await getEmailTemplates()
  } catch (err) {
    console.error('Error loading templates:', err)
  } finally {
    loading.value = false
  }
}

// Start editing a template
const startEditing = (template: any) => {
  editingTemplate.value = template
  templateForm.value = {
    name: template.name,
    subject: template.subject,
    content: template.content,
    description: template.description || ''
  }
}

// Cancel editing
const cancelEdit = () => {
  showCreateForm.value = false
  editingTemplate.value = null
  templateForm.value = {
    name: '',
    subject: '',
    content: '',
    description: ''
  }
  error.value = ''
}

// Save template
const saveTemplate = async () => {
  if (!templateForm.value.name || !templateForm.value.subject || !templateForm.value.content) {
    error.value = 'Please fill in all required fields'
    return
  }
  
  saving.value = true
  error.value = ''
  
  try {
    if (editingTemplate.value) {
      // Update existing template
      const result = await updateEmailTemplate(editingTemplate.value.id, {
        name: templateForm.value.name,
        subject: templateForm.value.subject,
        content: templateForm.value.content,
        description: templateForm.value.description
      })
      
      if (result.success) {
        // Reload templates
        await loadTemplates()
        
        // Reset form
        cancelEdit()
        
        // Emit event
        emit('template-updated', editingTemplate.value.id)
      } else {
        error.value = result.error || 'Failed to update template'
      }
    } else {
      // Create new template
      const result = await createEmailTemplate({
        name: templateForm.value.name,
        subject: templateForm.value.subject,
        content: templateForm.value.content,
        description: templateForm.value.description
      })
      
      if (result.success) {
        // Reload templates
        await loadTemplates()
        
        // Reset form
        cancelEdit()
        
        // Emit event
        emit('template-created', result.id)
      } else {
        error.value = result.error || 'Failed to create template'
      }
    }
  } catch (err: any) {
    error.value = err.message || 'Error saving template'
  } finally {
    saving.value = false
  }
}

// Preview template
const previewTemplate = (template: any) => {
  previewData.value = template
  showPreview.value = true
}

// Load templates on component mount
onMounted(() => {
  loadTemplates()
})
</script>
