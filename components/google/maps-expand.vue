<script setup lang="ts">
defineProps({
  markers: {
    type: Array,
    default: () => [{
      position: {
        lat: 51.093048, lng: 6.842120,
      },
    }],
  },
  openedMarkerID: {
    type: Number,
    default: null,
  },
  classes: {
    type: String,
    default: 'w-full h-full min-h-120',
  },
  zoom: {
    type: Number,
    default: 7,
  },
  center: {
    type: Object,
    default: () => ({ lat: 51.093048, lng: 6.84212 }),
  },
  mapTypeId: {
    type: String,
    default: 'terrain',
  },
  options: {
    type: Object,
    default: () => ({
      zoomControl: true,
      mapTypeControl: true,
      scaleControl: true,
      streetViewControl: true,
      rotateControl: true,
      fullscreenControl: true,
      autobindAllEvents: false,
    }
    ),
  },
})
const openedMarkerID = ref(null)
function openMarker(id: null) {
  openedMarkerID.value = id
}
</script>

<template>
  <div :class="classes">
    <client-only>
      <GMapMap
        :center="center" :zoom="zoom" :map-type-id="mapTypeId" :options="options"
        style="width: auto; height: 300px"
      >
        <GMapCluster>
          <GMapMarker
            v-for="(m, index) in markers" :key="index" :position="m.position" :clickable="true"
            :draggable="true" @click="openMarker(m.id)"
          >
            <GMapInfoWindow :closeclick="true" :opened="openedMarkerID === m.id" @closeclick="openMarker(null)">
              <!-- <div>I am in info window {{ m }} </div> -->
              <div v-html="m.description"> </div>

            </GMapInfoWindow>
          </GMapMarker>
        </GMapCluster>
      </GMapMap>
    </client-only>
  </div>
</template>
