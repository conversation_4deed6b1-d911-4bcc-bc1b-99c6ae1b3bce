<script setup lang="ts">
const darkMode = useState('darkMode', () => true)
const { coords, locatedAt, error, resume, pause } = useGeolocation();
const { currentSpace } = space();

// Location state
const center = useState("center", () => [-25.8800624, 28.1530337]);
const radius = ref(30)
const radiusInM = useState('radiusInM', ()=> {
  return 100 * 1000
});
const center_set = ref(false)
const currentLocation = ref({
  formatted_address: '',
  lat: null,
  lng: null
})
const isLocating = ref(false)

// Predefined radius options
const radiusOptions = [
  { value: 5, label: '5 km' },
  { value: 10, label: '10 km' },
  { value: 25, label: '25 km' },
  { value: 50, label: '50 km' },
  { value: 100, label: '100 km' }
]

// Get user's current location
const gettingLatLng = () => {
  isLocating.value = true;
  const lat = coords.value.latitude;
  const lng = coords.value.longitude;

  if (lat !== Infinity) {
    center.value = [lat, lng];
    center_set.value = true;
    isLocating.value = false;

    // Try to get address from coordinates using reverse geocoding
    fetchAddressFromCoords(lat, lng);
  } else {
    setTimeout(() => {
      if(!center_set.value) {
        gettingLatLng();
      }
    }, 1000);
  }
};

// Fetch address from coordinates (reverse geocoding)
const fetchAddressFromCoords = async (lat, lng) => {
  try {
    // This would typically use a geocoding service
    // For now, we'll just set the coordinates
    currentLocation.value = {
      formatted_address: 'Current Location',
      lat,
      lng
    };
  } catch (error) {
    console.error('Error fetching address:', error);
  }
};

// Watch for radius changes
watch(() => radius.value, () => {
  radiusInM.value = radius.value * 1000;
})

// Initialize on component mount
onMounted(() => {
  resume();
  setTimeout(() => {
    gettingLatLng();
  }, 1000);
});

// Handle address input from Google Autocomplete
function input_address(data: { path: undefined; bubbles: undefined; utc_offset: any; lat: any; lng: any; vicinity: any; adr_address: any; formatted_address: string; place_id: any; url: any }) {
  if (data && data.path === undefined && data.bubbles === undefined) {
    currentLocation.value = {
      utc_offset: data.utc_offset,
      lat: data.lat,
      lng: data.lng,
      vicinity: data.vicinity,
      adr_address: data.adr_address,
      formatted_address: data.formatted_address,
      place_id: data.place_id,
      adr_url: data.url
    };

    const split_address = data.formatted_address.split(',');
    currentLocation.value.address = split_address[0];
    currentLocation.value.city = split_address[1];
    currentLocation.value.state = split_address[2];
    currentLocation.value.zip = split_address[3];
    currentLocation.value.country = split_address[4];

    center.value = [data.lat, data.lng];
  }
}
</script>

<template>
  <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
    <div class="flex items-center mb-4">
      <div class="flex-shrink-0 w-10 h-10 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center">
        <Icon name="mdi:map-marker" class="text-blue-600 dark:text-blue-300" size="24" />
      </div>
      <h3 class="ml-3 text-lg font-medium text-gray-800 dark:text-gray-200">Location Settings</h3>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <!-- Location Input -->
      <div class="relative">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Search Location
        </label>
        <div class="relative">
          <google-autocomplete
            @input="input_address"
            :label="null"
            classes="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-gray-200"
          />
          <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <Icon name="mdi:magnify" class="text-gray-400" />
          </div>
        </div>

        <!-- Current Location Button -->
        <button
          @click="gettingLatLng"
          class="mt-2 flex items-center text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
        >
          <Icon
            :name="isLocating ? 'mdi:loading' : 'mdi:crosshairs-gps'"
            class="mr-1"
            :class="{ 'animate-spin': isLocating }"
          />
          {{ isLocating ? 'Getting location...' : 'Use my current location' }}
        </button>

        <!-- Display current location if available -->
        <div v-if="currentLocation.formatted_address" class="mt-2 text-sm text-gray-600 dark:text-gray-400 flex items-start">
          <Icon name="mdi:map-marker-check" class="mr-1 mt-0.5 flex-shrink-0 text-green-500" />
          <span>{{ currentLocation.formatted_address }}</span>
        </div>
      </div>

      <!-- Radius Selection -->
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Search Radius
        </label>

        <!-- Radius Slider -->
        <div class="mb-4">
          <input
            v-model="radius"
            type="range"
            min="5"
            max="100"
            step="5"
            class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer accent-blue-600"
          />
          <div class="flex justify-between text-xs text-gray-600 dark:text-gray-400 mt-1">
            <span>5 km</span>
            <span>{{ radius }} km</span>
            <span>100 km</span>
          </div>
        </div>

        <!-- Radius Quick Select Buttons -->
        <div class="flex flex-wrap gap-2">
          <button
            v-for="option in radiusOptions"
            :key="option.value"
            @click="radius = option.value"
            class="px-3 py-1 text-xs rounded-full border transition-colors duration-200"
            :class="radius === option.value
              ? 'bg-blue-600 text-white border-blue-600'
              : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-700 hover:border-blue-500 dark:hover:border-blue-500'"
          >
            {{ option.label }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>