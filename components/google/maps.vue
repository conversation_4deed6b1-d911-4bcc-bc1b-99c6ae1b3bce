<script setup lang="ts">
defineProps({
  markers: {
    type: Array,
    default: () => [],
  },
  openedMarkerID: {
    type: Number,
    default: null,
  },
  classes: {
    type: String,
    default: 'w-full h-full min-h-120',
  },
  zoom: {
    type: Number,
    default: 7,
  },
  center: {
    type: Object,
    default: () => ({ lat: 51.093048, lng: 6.84212 }),
  },
  mapTypeId: {
    type: String,
    default: 'terrain',
  },
})
</script>

<template>
  <div :class="classes">
    <client-only>
      {{ center }}
      <!-- <GMapInfoWindow
        :closeclick="true"
        @closeclick="closeMarker"
        :opened="openedMarkerID === m.id"
      >
        <div>I am in info window {{ m.id }}</div>
      </GMapInfoWindow> -->
      <GMapMap
        :center="center"
        :zoom="zoom"
        :map-type-id="mapTypeId"
        :class="classes"
      />
    </client-only>
  </div>
</template>
