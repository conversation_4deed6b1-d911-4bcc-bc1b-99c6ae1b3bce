<script setup lang="ts">
import * as geofire from "geofire-common";
import { useGeolocation } from '@vueuse/core'

// Use the new Firebase initialization
const { firestore } = useFirebase();

import {
  collection,
  query,
  startAt,
  getDocs,
  orderBy,
  endAt,
  where,
} from "firebase/firestore";
const { coords, locatedAt, error, resume, pause } = useGeolocation();
const { currentSpace } = space();
// Find cities within 50km of London
const center = useState("center", () => [-25.8800624, 28.1530337]);
const radiusInM = 100 * 1000;
const promises = useState("nearbyRestaurants", () => {
  return [];
});

const searchBy = useState("searchBy", () => "Restaurant");

// Each item in 'bounds' represents a startAt/endAt pair. We have to issue
// a separate query for each pair. There can be up to 9 pairs of bounds
// depending on overlap, but in most cases there are 4.
const gettingPromises = async () => {
  promises.value = [];

  const bounds = geofire.geohashQueryBounds(center.value, radiusInM);
  for (const b of bounds) {
    console.log('b', b)
    const q = query(
      collection(firestore, "spaces"),
      orderBy("hash"),
      startAt(b[0]),
      endAt(b[1])
    );
    // const q = query(collection(firestore, "spaces"), where("active", "==", true));

    const querySnapshot = await getDocs(q);
    querySnapshot.forEach((doc) => {
      let space = { id: doc.id, ...doc.data() };
      const lat = space.lat;
      const lng = space.lng;

      const distanceInKm = geofire.distanceBetween([lat, lng], center.value);
      space.distanceInKm = Number(distanceInKm);
      const distanceInM = distanceInKm * 1000;
      space.distanceInM = Number(distanceInM);
      if (distanceInM <= radiusInM) {
        promises.value.push(space);
      }
    });
  }
};
// const printCurrentPosition = async () => {
//   const coordinates = await Geolocation.getCurrentPosition();

//   console.log('Current position:', coordinates);
// };
const gettingLatLng = () => {
  const lat = coords.value.latitude;
  const lng = coords.value.longitude;

  if (lat !== Infinity) {
    center.value = [lat, lng];
  } else {
    setTimeout(() => {
      gettingLatLng();
      // printCurrentPosition()
    }, 1000);
  }
};


watch(
  () => center.value,
  (val) => {
    promises.value = [];
    gettingPromises();
  }
);
onMounted(() => {
  promises.value = [];
  resume();
  setTimeout(() => {
    gettingLatLng();
    // console.log('mounted2 coords', [coords.value.coords.latitude, coords.value.coords.longitude])

    // center.value = [coords.value.coords.latitude, coords.value.coords.longitude]
    gettingPromises();
  }, 1000);
  // gettingPromises()
});
const router = useRouter();
const setSpace = (space: SpaceItem) => {
  console.log("space", space);
  currentSpace.value = space;
  router.push("/restaurant?name=" + space.name);
};
</script>

<template>
  <div >
    {{ promises }}
    <div
      v-for="(rest, index) in promises"
      :key="index"
      class="relative h-48 rounded cursor-pointer"
      @click="setSpace(rest)"
    >
      <div
        class="h-48 bg-center bg-no-repeat bg-cover rounded-t-lg"
        v-if="rest.background"
        :style="`background-image: url(${rest.background.src})`"
      ></div>
      <div class="absolute bottom-0 w-full o_card">
        <div class="flex justify-between w-full mt-2">
          <div>
            {{ rest.name }}
          </div>
          <div class="text-sm text-gray-300" v-if="rest.distanceInKm">
            {{ rest.distanceInKm.toFixed(2) }} km
          </div>
        </div>
        <div class="flex justify-between w-full mt-2">
          <div v-for="(tag, indx) in rest.tags" :key="indx">
            {{ tag }}
          </div>
        </div>
      </div>
    </div>
  </div>

</template>
