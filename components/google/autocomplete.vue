<script setup lang="ts">
let gProps = defineProps({
  label: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '',
  },
  classes: {
    type: String,
    default: 'o_input w-full',
  },
  schema: {
    type: Object,
    default: () => {
    return {}
    }
  },
  value: {
    type: Object,
    default: () => {
      return {
        lat: null,
        lng: null,
      }
    },
  },
})


// created() {
//   if (this.value)
//     this.$emit('input', this.value)
// },
const autoEmit: any = getCurrentInstance()

function setPlace(data: any) {
  const payload = {
    place_id: data.place_id,
    lat: data.geometry.location.lat(),
    lng: data.geometry.location.lng(),
    adr_address: data.adr_address,
    formatted_address: data.formatted_address,
    url: data.url,
    vicinity: data.vicinity,
    utc_offset: data.utc_offset_minutes,
  }
  autoEmit.emit('input', payload)
}
</script>

<template>
  <div class="w-full">
    <div class="o_label mr-4">
      {{ schema.label ? schema.label : label}}
    </div>
    <client-only>
      <GMapAutocomplete :placeholder="placeholder" :class="classes" @place_changed="setPlace" />
      <div v-if="value" v-html="value.adr_address" />
    </client-only>
  </div>
</template>
