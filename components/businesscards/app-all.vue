<script setup lang="ts">
import { useGeolocation } from '@vueuse/core';
import * as geofire from 'geofire-common';

import { collection, getDocs, query, where } from 'firebase/firestore';
const { coords, locatedAt, error, resume, pause } = useGeolocation();
const { currentSpace } = space();
// Find cities within 50km of London
const center: any = useState('center', () => [-25.8800624, 28.1530337]);
const radiusInM = useState('radiusInM', () => {
  return 500 * 1000;
});
// const promises = useState("nearbyBusinesscards", () => {
//   return [];
// });
const promises: any = ref([]);

const promisesRemoveDuplicates = computed(() => {
  //remove duplicates
  let array = promises.value.filter(
    (v: any, i: any, a: any) => a.findIndex((t: any) => t.id === v.id) === i
  );
  return array;
});

// Each item in 'bounds' represents a startAt/endAt pair. We have to issue
// a separate query for each pair. There can be up to 9 pairs of bounds
// depending on overlap, but in most cases there are 4.
const gettingPromises = async () => {
  promises.value = [];

  const bounds = geofire.geohashQueryBounds(center.value, radiusInM.value);
  for (const b of bounds) {
    console.log('b', b);
    // const q = query(
    //   collection(firestoreDb, "businesscards"),
    //   where("status", "==", "Approved"),
    //   orderBy("hash"),
    //   startAt(b[0]),
    //   endAt(b[1])
    // );
    const { firestore } = useFirebase();
    const q = query(collection(firestore, 'businesscards'), where('status', '==', 'Approved'));

    const querySnapshot = await getDocs(q);
    querySnapshot.forEach(doc => {
      let space: any = { id: doc.id, ...doc.data() };
      const lat = space.lat;
      const lng = space.lng;
      if (lat && lng) {
        const distanceInKm = geofire.distanceBetween([lat, lng], center.value);
        space.distanceInKm = Number(distanceInKm);
        const distanceInM = distanceInKm * 1000;
        space.distanceInM = Number(distanceInM);
        if (distanceInM <= radiusInM.value) {
          promises.value.push(space);
        }
      } else {
        promises.value.push(space);
      }
    });
    shuffle(promises.value);
  }
};

watch(
  () => center.value,
  val => {
    promises.value = [];
    gettingPromises();
  }
);

watch(
  () => radiusInM.value,
  val => {
    promises.value = [];
    gettingPromises();
  }
);
onMounted(() => {
  promises.value = [];
  setTimeout(() => {
    // console.log('mounted2 coords', [coords.value.coords.latitude, coords.value.coords.longitude])

    // center.value = [coords.value.coords.latitude, coords.value.coords.longitude]
    gettingPromises();
  }, 1000);
  // gettingPromises()
});

function shuffle(array: any) {
  let currentIndex = array.length,
    randomIndex;

  // While there remain elements to shuffle.
  while (currentIndex != 0) {
    // Pick a remaining element.
    randomIndex = Math.floor(Math.random() * currentIndex);
    currentIndex--;

    // And swap it with the current element.
    [array[currentIndex], array[randomIndex]] = [array[randomIndex], array[currentIndex]];
  }

  return array;
}
</script>

<template>
  <!-- <div>
        <Carousel :items-to-show="4" :wrap-around="true">
      <Slide v-for="(comp, ind) in promises" :key="ind">
        <card-slide-up :schema="comp" />
      </Slide>

      <template #addons>
        <Navigation />
        <Pagination />
      </template>
    </Carousel>
    </div> -->
  <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
    <div v-for="(rest, index) in promisesRemoveDuplicates" :key="index" class="relative">
      <div class="w-96 h-52">
        <div className="cursor-pointer group perspective">
          <div
            className="relative preserve-3d group-hover:my-rotate-y-180 w-full h-full duration-1000"
          >
            <div className=" backface-hidden w-96 h-52">
              <img
                v-if="rest.imageUrl || rest.thumbnailUrl || rest.image"
                class="w-96 h-52"
                :src="rest.imageUrl || rest.thumbnailUrl || (rest.image ? rest.image.src : '')"
              />
              <card-user :info="rest" v-else />
            </div>
            <div
              className="absolute top-0 my-rotate-y-180 backface-hidden w-96 h-52 overflow-hidden"
            >
              <card-user :info="rest" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
