<script setup lang="ts">
import { useAnalyticsTracking } from '~/composables/analytics-tracking';
import { useCRMInteractions } from '~/composables/useCRMInteractions';

let viewProps = defineProps({
    user: {
        type: Object,
        default: () => {
            return {}
        }
    }
})
const { businesscardsSelect } = useCRM();
const { data: formsItem } = await useFetch<any>("/api/crm/forms", {
  pick: ["businesscards"],
});
const { setToId } = database();
const dataselect = useState("selectedBusinesscards", () => {
  return {};
});

// CRM Integration
const {
  interactions,
  followUps,
  getInteractionsForCard,
  getFollowUpsForCard,
  calculateContactStatus,
  createInteraction,
  createFollowUp,
  completeFollowUp,
  deleteInteraction,
  deleteFollowUp,
  formatDate,
  formatRelativeTime,
  interactionTypes,
  followUpTypes,
  priorityOptions,
  newInteraction,
  newFollowUp,
  resetInteractionForm,
  resetFollowUpForm
} = useCRMInteractions();

// CRM State
const cardInteractions = ref([]);
const cardFollowUps = ref([]);
const contactStatus = ref(null);
const activeTab = ref('interactions');
const showAddNoteDialog = ref(false);
const showScheduleFollowUpDialog = ref(false);
const showLogCallDialog = ref(false);
const callDuration = ref(null);

// Initialize analytics tracking
const { trackCardView } = useAnalyticsTracking();

const notificationOpen = useState<any>("notificationOpen", () => {
  return {
    open: false,
    title: "",
    text: "",
    color: "o_gradient",
    type: "normal",
    icon: "info",
    button_text: "OK",
  };
});

async function save() {
  let payload: any = createData(businesscardsSelect.value);

  let prodset = await setToId(
    businesscardsSelect.value.id ? businesscardsSelect.value.id : makeid(16),
    formsItem.value.businesscards.filters.collection,
    payload
  );
  notificationOpen.value = {
    open: true,
    title: businesscardsSelect.value.id ? "Updated" : "Created",
    text: `${formsItem.value.businesscards.title} has been submitted`,
    color: "o_gradient",
    type: "normal",
    icon: "info",
    button_text: "OK",
  };
  businesscardsSelect.value = {};
  dataselect.value = "";
  useRouter().back();
}

onMounted(async () => {
  if (viewProps.user) {
    businesscardsSelect.value = viewProps.user;
    dataselect.value = viewProps.user;

    // Track the business card view
    if (viewProps.user.id) {
      try {
        await trackCardView(viewProps.user.id, 'direct');
        console.log('View tracked for business card:', viewProps.user.id);
      } catch (error) {
        console.error('Error tracking view:', error);
      }

      // Load CRM data
      loadCRMData();
    }
  }
});

// CRM Functions
const loadCRMData = () => {
  if (viewProps.user.id) {
    cardInteractions.value = getInteractionsForCard(viewProps.user.id);
    cardFollowUps.value = getFollowUpsForCard(viewProps.user.id);
    contactStatus.value = calculateContactStatus(viewProps.user.id);
  }
};

const handleAddNote = async () => {
  if (newInteraction.title && newInteraction.content) {
    await createInteraction(viewProps.user.id, {
      type: 'note',
      title: newInteraction.title,
      content: newInteraction.content
    });
    resetInteractionForm();
    showAddNoteDialog.value = false;
    loadCRMData();
  }
};

const handleScheduleFollowUp = async () => {
  if (newFollowUp.title && newFollowUp.dueDate) {
    await createFollowUp(viewProps.user.id, {
      type: newFollowUp.type,
      title: newFollowUp.title,
      description: newFollowUp.description,
      dueDate: new Date(newFollowUp.dueDate),
      priority: newFollowUp.priority,
      recurring: newFollowUp.recurring
    });
    resetFollowUpForm();
    showScheduleFollowUpDialog.value = false;
    loadCRMData();
  }
};

const handleLogCall = async () => {
  if (newInteraction.title && newInteraction.content) {
    await createInteraction(viewProps.user.id, {
      type: 'call',
      title: newInteraction.title,
      content: newInteraction.content,
      metadata: {
        duration: callDuration.value
      }
    });
    resetInteractionForm();
    callDuration.value = null;
    showLogCallDialog.value = false;
    loadCRMData();
  }
};

const handleCompleteFollowUp = async (followUpId: string) => {
  await completeFollowUp(followUpId);
  loadCRMData();
};

const handleDeleteInteraction = async (interactionId: string) => {
  if (confirm('Are you sure you want to delete this interaction?')) {
    await deleteInteraction(interactionId);
    loadCRMData();
  }
};

const handleDeleteFollowUp = async (followUpId: string) => {
  if (confirm('Are you sure you want to delete this follow-up?')) {
    await deleteFollowUp(followUpId);
    loadCRMData();
  }
};

// Engagement level styling
const getEngagementColor = (level: string) => {
  switch (level) {
    case 'hot': return 'text-red-500';
    case 'warm': return 'text-yellow-500';
    case 'cold': return 'text-blue-500';
    default: return 'text-gray-500';
  }
};

const getEngagementBgColor = (level: string) => {
  switch (level) {
    case 'hot': return 'bg-red-500/10';
    case 'warm': return 'bg-yellow-500/10';
    case 'cold': return 'bg-blue-500/10';
    default: return 'bg-gray-500/10';
  }
};

const addImage = ref(false);

function imageLoaded(data: any) {
  if (businesscardsSelect.value.images) businesscardsSelect.value.images.push(...data);
  else businesscardsSelect.value.images = [...data];

  //remove any duplicates
  businesscardsSelect.value.images = businesscardsSelect.value.images.filter(
    (item: any, index: any, self: any) =>
      index === self.findIndex((t: any) => t.src === item.src)
  );
}
function deleteImage(index: number) {
  businesscardsSelect.value.images.splice(index, 1);
}

const editImage = ref(false);

function changeEditImage() {
  editImage.value = !editImage.value;
}

const input = (data) => {
  if (data && data.path === undefined && data.bubbles === undefined) {
    businesscardsSelect.value = { ...businesscardsSelect.value, ...data };
  }
};
</script>

<template>

      <div class="grid lg:grid-cols-12">
        <div
          class="w-full max-w-full px-3 mt-6 shrink-0 lg:flex-0 lg:col-span-8 lg:mt-0"
        >
          <div
            class="relative flex flex-col min-w-0 break-words border-0 o_card"
          >
            <div class="flex-auto p-6">
              <div class="flex justify-between items-center mb-4">
                <!-- <h5 class="text-2xl o_label">Contact Information</h5> -->
                <div></div> <!-- Empty div for flex spacing -->
                <NuxtLink
                  v-if="businesscardsSelect.id"
                  :to="`/businesscards/analytics/${businesscardsSelect.id}`"
                  class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
                  </svg>
                  View Analytics
                </NuxtLink>
              </div>
              <div class="flex flex-wrap -mx-3">
                <forms-small-view
                  :index="1"
                  :formsItem="formsItem.businesscards"
                  :data="dataselect"
                  @input="input"
                />
              </div>
            </div>
          </div>

          <!-- CRM Section -->
          <div class="relative flex flex-col min-w-0 break-words border-0 o_card mt-6">
            <div class="flex-auto p-6">
              <div class="flex justify-between items-center mb-6">
                <h5 class="text-2xl o_label">CRM Information</h5>
                
                <!-- Quick Action Buttons -->
                <div class="flex space-x-2">
                  <button
                    @click="showAddNoteDialog = true"
                    class="bg-gray-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center"
                  >
                    <Icon name="mdi:note-text" class="mr-1" />
                    Add Note
                  </button>
                  <button
                    @click="showScheduleFollowUpDialog = true"
                    class="bg-blue-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center"
                  >
                    <Icon name="mdi:calendar-check" class="mr-1" />
                    Schedule Follow-up
                  </button>
                  <button
                    @click="showLogCallDialog = true"
                    class="bg-green-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 flex items-center"
                  >
                    <Icon name="mdi:phone" class="mr-1" />
                    Log Call
                  </button>
                </div>
              </div>

              <!-- Contact Status -->
              <div v-if="contactStatus" class="mb-6 p-4 bg-gray-800 rounded-lg">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <p class="text-sm text-gray-400">Engagement Level</p>
                    <p class="text-lg font-semibold capitalize flex items-center">
                      <span :class="[getEngagementBgColor(contactStatus.engagementLevel), getEngagementColor(contactStatus.engagementLevel)]" class="px-2 py-1 rounded-md">
                        {{ contactStatus.engagementLevel }}
                      </span>
                    </p>
                  </div>
                  <div>
                    <p class="text-sm text-gray-400">Last Contact</p>
                    <p class="text-lg font-semibold">
                      {{ contactStatus.lastContact ? formatRelativeTime(contactStatus.lastContact) : 'Never' }}
                    </p>
                  </div>
                  <div>
                    <p class="text-sm text-gray-400">Total Interactions</p>
                    <p class="text-lg font-semibold">{{ contactStatus.totalInteractions }}</p>
                  </div>
                  <div>
                    <p class="text-sm text-gray-400">Pending Follow-ups</p>
                    <p class="text-lg font-semibold">{{ contactStatus.followUpsPending }}</p>
                  </div>
                </div>
              </div>

              <!-- Tabs for Interactions and Follow-ups -->
              <ui-tabs v-model="activeTab" :tabs="[
                { id: 'interactions', label: 'Interaction History', icon: 'mdi:history' },
                { id: 'followups', label: 'Follow-ups', icon: 'mdi:calendar-clock' }
              ]" variant="underline">
                
                <!-- Interactions Tab -->
                <ui-tab-panel id="interactions">
                  <div class="mt-4">
                    <div v-if="cardInteractions.length === 0" class="text-center py-8 text-gray-500">
                      No interactions recorded yet
                    </div>
                    <div v-else class="space-y-4">
                      <div v-for="interaction in cardInteractions" :key="interaction.id" 
                           class="p-4 bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors">
                        <div class="flex justify-between items-start">
                          <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-2">
                              <Icon :name="interactionTypes.find(t => t.value === interaction.type)?.icon || 'mdi:note'" 
                                    class="text-lg" />
                              <h4 class="font-semibold">{{ interaction.title }}</h4>
                              <span class="text-sm text-gray-400">
                                {{ formatRelativeTime(interaction.timestamp) }}
                              </span>
                            </div>
                            <p class="text-gray-300">{{ interaction.content }}</p>
                            <div v-if="interaction.metadata?.duration" class="mt-1 text-sm text-gray-400">
                              Duration: {{ interaction.metadata.duration }} minutes
                            </div>
                          </div>
                          <button @click="handleDeleteInteraction(interaction.id)" 
                                  class="ml-4 text-red-400 hover:text-red-300">
                            <Icon name="mdi:delete" class="text-lg" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </ui-tab-panel>

                <!-- Follow-ups Tab -->
                <ui-tab-panel id="followups">
                  <div class="mt-4">
                    <div v-if="cardFollowUps.length === 0" class="text-center py-8 text-gray-500">
                      No follow-ups scheduled
                    </div>
                    <div v-else class="space-y-4">
                      <div v-for="followUp in cardFollowUps" :key="followUp.id" 
                           class="p-4 bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors"
                           :class="{ 'opacity-50': followUp.completed }">
                        <div class="flex justify-between items-start">
                          <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-2">
                              <Icon :name="followUpTypes.find(t => t.value === followUp.type)?.icon || 'mdi:calendar'" 
                                    class="text-lg" />
                              <h4 class="font-semibold" :class="{ 'line-through': followUp.completed }">
                                {{ followUp.title }}
                              </h4>
                              <span class="text-sm px-2 py-0.5 rounded"
                                    :class="{
                                      'bg-red-500/20 text-red-400': followUp.priority === 'high',
                                      'bg-yellow-500/20 text-yellow-400': followUp.priority === 'medium',
                                      'bg-gray-500/20 text-gray-400': followUp.priority === 'low'
                                    }">
                                {{ followUp.priority }}
                              </span>
                            </div>
                            <p class="text-gray-300 mb-2">{{ followUp.description }}</p>
                            <div class="flex items-center space-x-4 text-sm text-gray-400">
                              <span>Due: {{ formatDate(followUp.dueDate) }}</span>
                              <span v-if="followUp.recurring" class="flex items-center">
                                <Icon name="mdi:repeat" class="mr-1" />
                                {{ followUp.recurring }}
                              </span>
                              <span v-if="followUp.completed" class="text-green-400">
                                Completed {{ formatRelativeTime(followUp.completedAt) }}
                              </span>
                            </div>
                          </div>
                          <div class="flex items-center space-x-2 ml-4">
                            <button v-if="!followUp.completed"
                                    @click="handleCompleteFollowUp(followUp.id)" 
                                    class="text-green-400 hover:text-green-300">
                              <Icon name="mdi:check-circle" class="text-lg" />
                            </button>
                            <button @click="handleDeleteFollowUp(followUp.id)" 
                                    class="text-red-400 hover:text-red-300">
                              <Icon name="mdi:delete" class="text-lg" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </ui-tab-panel>
              </ui-tabs>
            </div>
          </div>
        </div>
        <div class="w-full max-w-full px-3 shrink-0 lg:col-span-4">
          <div class="h-full o_card">
            <div class="flex-auto">
              <!-- <h5 class="text-2xl o_label">Image</h5> -->
              <div class="flex flex-wrap -mx-3">
                <div
                  class="w-full max-w-full px-3 flex-0"
                  v-if="businesscardsSelect.images"
                >
                  <img
                    v-if="businesscardsSelect.images.length > 0"
                    class="w-full mt-4 shadow-xl rounded-xl"
                    :src="
                      businesscardsSelect.images[0] ? businesscardsSelect.images[0].src : ''
                    "
                    alt="product_image"
                  />
                </div>
                <!-- <div class="w-full max-w-full px-3 mt-6 flex-0">
                  <div class="flex">
                    <modal-full :open="addImage" @close="addImage = false">
                      <template #body>
                        <file-manager @input="imageLoaded" />
                      </template>
                    </modal-full>
                    <button
                      type="button"
                      class="mr-2 o_btn_accent_border"
                      @click="addImage = !addImage"
                    >
                      Add
                    </button>
                    <button type="button" class="o_btn_warning_border">
                      Remove
                    </button>
                  </div>
                </div> -->
              </div>
              <div v-if="businesscardsSelect.images">
                <div
                  v-if="businesscardsSelect.images.length > 0"
                  class="flex w-full pt-2 mt-6 overflow-x-auto"
                >
                  <figure
                    v-for="(img, index) in businesscardsSelect.images"
                    :key="index"
                    class="relative w-24 h-24 px-1 mb-4 text-white"
                  >
                    <Icon
                      name="mdi:delete"
                      class="absolute top-0 right-0 z-50 w-6 h-6 bg-red-600 rounded-full cursor-pointer text-base_100 shadow-soft-md hover:shadow-soft-lg"
                      @click="deleteImage(index)"
                    />
                    <img
                      class="h-24 shadow-xl rounded-xl"
                      :src="img.src"
                      alt="Image description"
                    />
                  </figure>
                </div>
              </div>
            </div>
            <div class="mt-4">
              <forms-small-view
                :index="3"
                :formsItem="formsItem.businesscards"
                :data="dataselect"
                @input="input"
              />
              <hr class="my-4 text-base_100" />
              <forms-small-view
                :index="2"
                :formsItem="formsItem.businesscards"
                :data="dataselect"
                @input="input"
              />
            </div>
          </div>
        </div>

      </div>

      <!-- Dialog Components -->
      <!-- Add Note Dialog -->
      <div v-if="showAddNoteDialog" class="fixed inset-0 z-50 overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center">
          <div class="fixed inset-0 transition-opacity" @click="showAddNoteDialog = false">
            <div class="absolute inset-0 bg-gray-900 opacity-75"></div>
          </div>
          <div class="relative inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-gray-800 shadow-xl rounded-lg">
            <h3 class="text-lg font-medium leading-6 text-white mb-4">Add Note</h3>
            <div class="mt-2">
              <input
                v-model="newInteraction.title"
                type="text"
                placeholder="Note title"
                class="w-full px-3 py-2 bg-gray-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mb-3"
              />
              <textarea
                v-model="newInteraction.content"
                placeholder="Note content"
                rows="4"
                class="w-full px-3 py-2 bg-gray-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              ></textarea>
            </div>
            <div class="mt-4 flex justify-end space-x-2">
              <button
                @click="showAddNoteDialog = false; resetInteractionForm()"
                class="px-4 py-2 text-sm font-medium text-gray-300 bg-gray-700 rounded-md hover:bg-gray-600"
              >
                Cancel
              </button>
              <button
                @click="handleAddNote"
                class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
              >
                Save Note
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Schedule Follow-up Dialog -->
      <div v-if="showScheduleFollowUpDialog" class="fixed inset-0 z-50 overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center">
          <div class="fixed inset-0 transition-opacity" @click="showScheduleFollowUpDialog = false">
            <div class="absolute inset-0 bg-gray-900 opacity-75"></div>
          </div>
          <div class="relative inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-gray-800 shadow-xl rounded-lg">
            <h3 class="text-lg font-medium leading-6 text-white mb-4">Schedule Follow-up</h3>
            <div class="mt-2 space-y-3">
              <select
                v-model="newFollowUp.type"
                class="w-full px-3 py-2 bg-gray-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option v-for="type in followUpTypes" :key="type.value" :value="type.value">
                  {{ type.label }}
                </option>
              </select>
              <input
                v-model="newFollowUp.title"
                type="text"
                placeholder="Follow-up title"
                class="w-full px-3 py-2 bg-gray-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <textarea
                v-model="newFollowUp.description"
                placeholder="Description"
                rows="3"
                class="w-full px-3 py-2 bg-gray-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              ></textarea>
              <input
                v-model="newFollowUp.dueDate"
                type="datetime-local"
                class="w-full px-3 py-2 bg-gray-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <select
                v-model="newFollowUp.priority"
                class="w-full px-3 py-2 bg-gray-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option v-for="priority in priorityOptions" :key="priority.value" :value="priority.value">
                  {{ priority.label }}
                </option>
              </select>
              <select
                v-model="newFollowUp.recurring"
                class="w-full px-3 py-2 bg-gray-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option v-for="option in recurringOptions" :key="option.value" :value="option.value">
                  {{ option.label }}
                </option>
              </select>
            </div>
            <div class="mt-4 flex justify-end space-x-2">
              <button
                @click="showScheduleFollowUpDialog = false; resetFollowUpForm()"
                class="px-4 py-2 text-sm font-medium text-gray-300 bg-gray-700 rounded-md hover:bg-gray-600"
              >
                Cancel
              </button>
              <button
                @click="handleScheduleFollowUp"
                class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
              >
                Schedule
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Log Call Dialog -->
      <div v-if="showLogCallDialog" class="fixed inset-0 z-50 overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center">
          <div class="fixed inset-0 transition-opacity" @click="showLogCallDialog = false">
            <div class="absolute inset-0 bg-gray-900 opacity-75"></div>
          </div>
          <div class="relative inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-gray-800 shadow-xl rounded-lg">
            <h3 class="text-lg font-medium leading-6 text-white mb-4">Log Phone Call</h3>
            <div class="mt-2 space-y-3">
              <input
                v-model="newInteraction.title"
                type="text"
                placeholder="Call subject"
                class="w-full px-3 py-2 bg-gray-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <textarea
                v-model="newInteraction.content"
                placeholder="Call notes"
                rows="4"
                class="w-full px-3 py-2 bg-gray-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              ></textarea>
              <input
                v-model.number="callDuration"
                type="number"
                placeholder="Duration (minutes)"
                class="w-full px-3 py-2 bg-gray-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div class="mt-4 flex justify-end space-x-2">
              <button
                @click="showLogCallDialog = false; resetInteractionForm(); callDuration = null"
                class="px-4 py-2 text-sm font-medium text-gray-300 bg-gray-700 rounded-md hover:bg-gray-600"
              >
                Cancel
              </button>
              <button
                @click="handleLogCall"
                class="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700"
              >
                Log Call
              </button>
            </div>
          </div>
        </div>
      </div>

</template>
