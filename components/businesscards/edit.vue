<script setup lang="ts">
import UploadEnhancedFormUploader from '~/components/upload/EnhancedFormUploader.vue';
import UiCard from '~/components/ui/Card.vue';
import { useActivityTracking } from '~/composables/activity-tracking';
import { useRouter } from '#app';

const props = defineProps({
  open: {
    type: Boolean,
    default: () => {
      return true;
    },
  },
});

const router = useRouter();

const formsItem = ref({
  title: "Business Cards",
  filters: {
    index: "omni",
    collection: "businesscards",
    queryBy: "",
    route_add: "/c/businesscards-create",

    description: "Businesscards",
    areas: ["Business Card Information"],
    filterBy:
      "Touched Records, Untouched Records, Record Action, Related Records Action",
  },
  data: [
    {
      label: "id",
      val: "id",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Created At",
      val: "created_at",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Updated at",
      val: "updated_at",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "User ownership",
      val: "user_own",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Users with access",
      val: "user_access",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Space ownership",
      val: "space_own",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Space with access",
      val: "space_access",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Business Card",
      val: "image",
      value: "",
      class: "col-span-2",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "Image",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      component: "FormsInputsUpload",
      action: "forms-actions-docs",
    },
    {
      label: "Name",
      val: "name",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
    {
      label: "Tags (comma seperated)",
      val: "tags",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsTags",
      action: "forms-actions-tags",
    },
    {
      label: "Description",
      val: "description",
      value: "",
      class: "col-span-2",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsTextarea",
      action: "forms-actions-quill",
    },
    {
      label: "Status",
      val: "status",
      value: "Pending",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: true,
      small_table: false,
      small_form: false,
      editable: false,
      card: false,
      card_type: "Image",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      component: "FormsInputsSelect",
      action: "forms-actions-select",
      options: ["Approved", "Pending", "Declined"],
    },
    {
      label: "First Name",
      val: "first_name",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
    {
      label: "Last Name",
      val: "last_name",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
    {
      label: "Email",
      val: "email",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
    {
      label: "Website",
      val: "website",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: false,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
    {
      label: "Phone",
      val: "phone",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
  ],
});

const userSpaces = useState<any>("userSpaces", () => {
  return [];
});

const selectedBusinesscards: any = useState("selectedBusinesscards", () => {
  return null;
});

// Check if we have business card data, if not redirect to list
onMounted(() => {
  console.log('selectedBusinesscards on mount:', selectedBusinesscards.value);
  console.log('selectedBusinesscards type:', typeof selectedBusinesscards.value);
  console.log('selectedBusinesscards is object:', typeof selectedBusinesscards.value === 'object');
  if (!selectedBusinesscards.value || !selectedBusinesscards.value.id) {
    console.log('No business card selected, redirecting to list');
    router.push('/c/businesscards/list');
  } else {
    console.log('Business card data available:', JSON.stringify(selectedBusinesscards.value, null, 2));
  }
});

const created = async (data: any) => {
  // Update the selectedBusinesscards with the new data
  selectedBusinesscards.value = data;
  userSpaces.value.push(data);

  // Track the business card update activity
  try {
    const { trackBusinessCardUpdated } = useActivityTracking();
    const cardName = data?.name ||
      `${data?.first_name || ''} ${data?.last_name || ''}`.trim() ||
      'Unnamed Card';

    await trackBusinessCardUpdated(data?.id || selectedBusinesscards.value?.id, cardName);
    console.log('Business card update activity tracked');
  } catch (error) {
    console.error('Error tracking business card update:', error);
    // Continue even if tracking fails
  }

  // Navigate back to the list after successful update
  router.push('/c/businesscards/list');
};
</script>

<template>
  <div>
    <!-- Business Card Edit Form -->
    <UploadEnhancedFormUploader
      title="Edit Business Card"
      description="Update your business card information"
      :formItem="formsItem"
      :homepage="false"
      :enableCropping="true"
      :cropAspectRatio="1.78"
      imageField="image"
      :initialData="selectedBusinesscards"
      @created="created"
    />
  </div>
</template>
