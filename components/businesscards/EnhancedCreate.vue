<!--
  Enhanced Business Card Creation Component
  Part of Story 1.4 - Integrates content moderation workflow
-->

<template>
  <div class="space-y-6">
    <!-- Upload Section -->
    <div v-if="!hideUpload" class="bg-white rounded-xl shadow-lg p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Upload Business Card</h3>
      
      <upload-enhanced-form-uploader
        @files-selected="handleFilesSelected"
        @file-cropped="handleFileCropped"
        :show-crop="true"
        :accept="'image/*'"
        :max-files="1"
      />
      
      <!-- Content Filtering Progress -->
      <div v-if="isFilteringContent" class="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <div class="flex items-center space-x-3">
          <div class="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-500"></div>
          <div class="flex-1">
            <h4 class="text-sm font-medium text-blue-900">Analyzing Content</h4>
            <p class="text-sm text-blue-700">Checking content for safety and quality...</p>
          </div>
        </div>
      </div>
      
      <!-- Content Filtering Results -->
      <div v-if="filteringResult && !isFilteringContent" class="mt-4">
        <!-- High Risk Warning -->
        <div v-if="filteringResult.riskLevel === 'critical' || filteringResult.riskLevel === 'high'" 
             class="p-4 bg-red-50 rounded-lg border border-red-200">
          <div class="flex items-start space-x-3">
            <Icon name="mdi:alert-circle" class="h-5 w-5 text-red-600 mt-0.5" />
            <div class="flex-1">
              <h4 class="text-sm font-medium text-red-900">Content Issues Detected</h4>
              <p class="text-sm text-red-700 mt-1">
                {{ filteringResult.riskLevel === 'critical' ? 'Critical issues found that require immediate attention.' : 'Some issues were detected that may need review.' }}
              </p>
              
              <!-- Issues List -->
              <div v-if="filteringResult.detectedIssues.length > 0" class="mt-3">
                <h5 class="text-xs font-medium text-red-900 mb-2">Detected Issues:</h5>
                <ul class="space-y-1">
                  <li v-for="issue in filteringResult.detectedIssues" :key="issue.description" 
                      class="text-xs text-red-700 flex items-start space-x-2">
                    <Icon name="mdi:circle-small" class="h-3 w-3 mt-0.5" />
                    <span>{{ issue.description }}</span>
                  </li>
                </ul>
              </div>
              
              <!-- Recommendations -->
              <div v-if="filteringResult.recommendations.length > 0" class="mt-3">
                <h5 class="text-xs font-medium text-red-900 mb-2">Recommendations:</h5>
                <ul class="space-y-1">
                  <li v-for="recommendation in filteringResult.recommendations" :key="recommendation" 
                      class="text-xs text-red-700 flex items-start space-x-2">
                    <Icon name="mdi:lightbulb-outline" class="h-3 w-3 mt-0.5" />
                    <span>{{ recommendation }}</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Medium Risk Warning -->
        <div v-else-if="filteringResult.riskLevel === 'medium'" 
             class="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
          <div class="flex items-start space-x-3">
            <Icon name="mdi:alert" class="h-5 w-5 text-yellow-600 mt-0.5" />
            <div class="flex-1">
              <h4 class="text-sm font-medium text-yellow-900">Content Review Required</h4>
              <p class="text-sm text-yellow-700 mt-1">
                Your content will be reviewed by our moderation team before publishing.
              </p>
            </div>
          </div>
        </div>
        
        <!-- Safe Content -->
        <div v-else class="p-4 bg-green-50 rounded-lg border border-green-200">
          <div class="flex items-start space-x-3">
            <Icon name="mdi:check-circle" class="h-5 w-5 text-green-600 mt-0.5" />
            <div class="flex-1">
              <h4 class="text-sm font-medium text-green-900">Content Looks Good</h4>
              <p class="text-sm text-green-700 mt-1">
                No issues detected. Your content meets our community guidelines.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- OCR Processing -->
    <div v-if="selectedImage">
      <businesscards-ocr-processor
        :image-file="selectedImage"
        @extraction-complete="handleExtractionComplete"
        @processing-status="handleProcessingStatus"
        @validation-update="handleOcrValidation"
        :use-enhanced-ocr="true"
        :auto-start="true"
      />
    </div>

    <!-- Business Card Form -->
    <div v-if="showForm" class="bg-white rounded-xl shadow-lg p-6">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-gray-900">Business Card Information</h3>
        <div v-if="selectedImage" class="text-sm text-gray-500">
          <button @click="resetUpload" class="text-blue-600 hover:text-blue-800 font-medium">
            Upload Different Image
          </button>
        </div>
      </div>

      <!-- Moderation Notice -->
      <div v-if="needsModeration" class="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <div class="flex items-start space-x-3">
          <Icon name="mdi:information" class="h-5 w-5 text-blue-600 mt-0.5" />
          <div class="flex-1">
            <h4 class="text-sm font-medium text-blue-900">Moderation Review</h4>
            <p class="text-sm text-blue-700 mt-1">
              Your business card will be reviewed by our team before becoming visible to other users. 
              This typically takes less than 24 hours.
            </p>
          </div>
        </div>
      </div>

      <!-- Form Fields -->
      <form @submit.prevent="submitBusinessCard" class="space-y-6">
        <!-- Basic Information -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700">Full Name *</label>
            <input
              id="name"
              v-model="formData.name"
              type="text"
              required
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              placeholder="Enter full name"
            />
          </div>
          
          <div>
            <label for="company" class="block text-sm font-medium text-gray-700">Company</label>
            <input
              id="company"
              v-model="formData.company"
              type="text"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              placeholder="Company name"
            />
          </div>
          
          <div>
            <label for="position" class="block text-sm font-medium text-gray-700">Job Title</label>
            <input
              id="position"
              v-model="formData.position"
              type="text"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              placeholder="Job title"
            />
          </div>
          
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
            <input
              id="email"
              v-model="formData.email"
              type="email"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              placeholder="<EMAIL>"
            />
          </div>
          
          <div>
            <label for="phone" class="block text-sm font-medium text-gray-700">Phone</label>
            <input
              id="phone"
              v-model="formData.phone"
              type="tel"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              placeholder="(*************"
            />
          </div>
          
          <div>
            <label for="website" class="block text-sm font-medium text-gray-700">Website</label>
            <input
              id="website"
              v-model="formData.website"
              type="url"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              placeholder="https://example.com"
            />
          </div>
        </div>

        <!-- Address Information -->
        <div class="border-t border-gray-200 pt-6">
          <h4 class="text-md font-medium text-gray-900 mb-4">Address Information</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="md:col-span-2">
              <label for="street" class="block text-sm font-medium text-gray-700">Street Address</label>
              <input
                id="street"
                v-model="formData.address.street"
                type="text"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                placeholder="123 Main St"
              />
            </div>
            
            <div>
              <label for="city" class="block text-sm font-medium text-gray-700">City</label>
              <input
                id="city"
                v-model="formData.address.city"
                type="text"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                placeholder="City"
              />
            </div>
            
            <div>
              <label for="state" class="block text-sm font-medium text-gray-700">State/Province</label>
              <input
                id="state"
                v-model="formData.address.state"
                type="text"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                placeholder="State"
              />
            </div>
            
            <div>
              <label for="postalCode" class="block text-sm font-medium text-gray-700">Postal Code</label>
              <input
                id="postalCode"
                v-model="formData.address.postalCode"
                type="text"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                placeholder="12345"
              />
            </div>
            
            <div>
              <label for="country" class="block text-sm font-medium text-gray-700">Country</label>
              <input
                id="country"
                v-model="formData.address.country"
                type="text"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                placeholder="Country"
              />
            </div>
          </div>
        </div>

        <!-- Additional Information -->
        <div class="border-t border-gray-200 pt-6">
          <h4 class="text-md font-medium text-gray-900 mb-4">Additional Information</h4>
          <div>
            <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
            <textarea
              id="notes"
              v-model="formData.notes"
              rows="3"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              placeholder="Additional notes or information..."
            />
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
          <button
            type="button"
            @click="$router.back()"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Cancel
          </button>
          
          <button
            type="submit"
            :disabled="isSubmitting || (filteringResult && !filteringResult.isAllowed)"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Icon v-if="isSubmitting" name="mdi:loading" class="mr-2 h-4 w-4 animate-spin" />
            {{ isSubmitting ? 'Creating...' : 'Create Business Card' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useBusinessCards } from '~/composables/useBusinessCards'
import { useContentFiltering } from '~/composables/useContentFiltering'
import { useEnhancedOCR } from '~/composables/useEnhancedOCR'

interface Props {
  homepage?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  homepage: false
})

const router = useRouter()

// Composables
const { createBusinessCard } = useBusinessCards()
const { filterContent } = useContentFiltering()
const { processImage } = useEnhancedOCR()

// State
const selectedImage = ref<File | null>(null)
const hideUpload = ref(false)
const showForm = ref(true)
const isSubmitting = ref(false)
const isFilteringContent = ref(false)
const filteringResult = ref(null)
const ocrData = ref(null)

// Form data
const formData = ref({
  name: '',
  company: '',
  position: '',
  email: '',
  phone: '',
  website: '',
  address: {
    street: '',
    city: '',
    state: '',
    country: '',
    postalCode: ''
  },
  notes: ''
})

// Computed
const needsModeration = computed(() => {
  if (!filteringResult.value) return false
  return filteringResult.value.riskLevel === 'medium' || filteringResult.value.riskLevel === 'high'
})

// Methods
const handleFilesSelected = async (files: File[]) => {
  if (files && files.length > 0) {
    selectedImage.value = files[0]
    await performContentFiltering(files[0])
  }
}

const handleFileCropped = async (file: File) => {
  selectedImage.value = file
  await performContentFiltering(file)
}

const performContentFiltering = async (file: File) => {
  isFilteringContent.value = true
  
  try {
    // First, perform OCR to extract text for filtering
    const ocrResult = await processImage(file, { 
      engine: 'auto',
      preprocessing: true 
    })
    
    // Create content object for filtering
    const contentToFilter = {
      text: ocrResult.rawText,
      imageUrl: URL.createObjectURL(file),
      metadata: {
        name: ocrResult.name,
        company: ocrResult.company,
        email: ocrResult.email,
        phone: ocrResult.phone
      },
      source: 'ocr' as const,
      contentType: 'business_card' as const
    }
    
    // Perform content filtering
    const filterResult = await filterContent(contentToFilter)
    filteringResult.value = filterResult
    
    // If content is allowed, populate form with OCR data
    if (filterResult.isAllowed) {
      populateFormFromOCR(ocrResult)
    }
    
  } catch (error) {
    console.error('Content filtering error:', error)
    // Allow form population even if filtering fails
    if (ocrData.value) {
      populateFormFromOCR(ocrData.value)
    }
  } finally {
    isFilteringContent.value = false
  }
}

const populateFormFromOCR = (data: any) => {
  formData.value = {
    name: data.name || '',
    company: data.company || '',
    position: data.title || '',
    email: data.email || '',
    phone: data.phone || '',
    website: data.website || '',
    address: {
      street: data.address?.street || '',
      city: data.address?.city || '',
      state: data.address?.state || '',
      country: data.address?.country || '',
      postalCode: data.address?.postalCode || ''
    },
    notes: ''
  }
}

const handleExtractionComplete = (data: any) => {
  ocrData.value = data
  if (!filteringResult.value || filteringResult.value.isAllowed) {
    populateFormFromOCR(data)
  }
}

const handleProcessingStatus = (status: any) => {
  console.log('OCR status:', status)
}

const handleOcrValidation = (validation: any) => {
  console.log('OCR validation:', validation)
}

const resetUpload = () => {
  selectedImage.value = null
  hideUpload.value = false
  filteringResult.value = null
  ocrData.value = null
  formData.value = {
    name: '',
    company: '',
    position: '',
    email: '',
    phone: '',
    website: '',
    address: {
      street: '',
      city: '',
      state: '',
      country: '',
      postalCode: ''
    },
    notes: ''
  }
}

const submitBusinessCard = async () => {
  if (isSubmitting.value) return
  
  isSubmitting.value = true
  
  try {
    // Determine moderation status based on content filtering
    let moderationStatus = 'approved'
    if (filteringResult.value) {
      if (filteringResult.value.riskLevel === 'critical' || !filteringResult.value.isAllowed) {
        moderationStatus = 'rejected'
      } else if (filteringResult.value.riskLevel === 'high' || filteringResult.value.riskLevel === 'medium') {
        moderationStatus = 'pending'
      }
    }
    
    const businessCardData = {
      ...formData.value,
      source: selectedImage.value ? 'scan' : 'manual',
      moderationStatus,
      imageUrl: selectedImage.value ? URL.createObjectURL(selectedImage.value) : undefined,
      contentFilteringResult: filteringResult.value
    }
    
    const result = await createBusinessCard(businessCardData)
    
    if (result) {
      // Show success message and redirect
      if (moderationStatus === 'pending') {
        alert('Business card created! It will be reviewed by our team before becoming visible.')
      } else {
        alert('Business card created successfully!')
      }
      
      router.push('/c/businesscards/list')
    } else {
      alert('Failed to create business card. Please try again.')
    }
    
  } catch (error) {
    console.error('Error creating business card:', error)
    alert('An error occurred while creating the business card.')
  } finally {
    isSubmitting.value = false
  }
}
</script>