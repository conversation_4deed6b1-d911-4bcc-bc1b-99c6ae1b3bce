<template>
  <div class="smart-uploader">
    <!-- Loading Overlay -->
    <div v-if="isProcessing" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-8 max-w-md mx-4 text-center">
        <Icon name="mdi:loading" class="w-12 h-12 animate-spin text-blue-600 mx-auto mb-4" />
        <h3 class="text-lg font-semibold mb-2">{{ progressMessage }}</h3>
        <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
          <div 
            class="bg-blue-600 h-2 rounded-full transition-all duration-300"
            :style="{ width: `${progress}%` }"
          ></div>
        </div>
        <p class="text-sm text-gray-600">{{ progress }}%</p>
      </div>
    </div>

    <!-- Step 1: Camera Scanning -->
    <div v-if="currentStep === 'camera'" class="step-container">
      <div class="mb-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-2">Scan Business Card</h2>
        <p class="text-gray-600">
          Use your camera to scan a business card or upload an image file.
        </p>
      </div>
      
      <CameraScanner 
        @capture="handleImageCapture"
        @error="handleCameraError"
      />
    </div>

    <!-- Step 2: OCR Review -->
    <div v-if="currentStep === 'review'" class="step-container">
      <div class="mb-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-2">Review Information</h2>
        <p class="text-gray-600">
          Review and edit the extracted information before saving.
        </p>
      </div>

      <OCRReview
        v-if="ocrResult"
        :ocr-data="ocrResult"
        :image-file="capturedImage"
        @submit="handleSubmit"
        @back="goBack"
      />
    </div>

    <!-- Step 3: Success -->
    <div v-if="currentStep === 'success'" class="step-container text-center">
      <div class="mb-6">
        <Icon name="mdi:check-circle" class="w-20 h-20 text-green-500 mx-auto mb-4" />
        <h2 class="text-2xl font-bold text-gray-900 mb-2">Business Card Saved!</h2>
        <p class="text-gray-600">
          Your business card has been successfully added to your CRM.
        </p>
      </div>

      <div class="flex space-x-4 justify-center">
        <button
          @click="resetUploader"
          class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
        >
          Add Another Card
        </button>
        <button
          @click="viewBusinessCards"
          class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          View All Cards
        </button>
      </div>
    </div>

    <!-- Error State -->
    <div v-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
      <div class="flex items-center">
        <Icon name="mdi:alert-circle" class="w-5 h-5 text-red-600 mr-2" />
        <span class="text-red-800 font-medium">Error</span>
      </div>
      <p class="text-red-700 mt-1">{{ error }}</p>
      <button
        @click="error = null"
        class="text-red-600 hover:text-red-800 underline text-sm mt-2"
      >
        Dismiss
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted, computed } from 'vue'
import { useOCR, type OCRResult } from '~/composables/useOCR'
import { useEnhancedOCR, type EnhancedOCRResult } from '~/composables/useEnhancedOCR'
import { useRouter } from '#app'
import { normalizeUrl } from '~/utils/urlHelpers.js'
import CameraScanner from './CameraScanner.vue'
import OCRReview from './OCRReview.vue'

type UploadStep = 'camera' | 'review' | 'success'

const emit = defineEmits(['uploaded', 'error'])

const router = useRouter()

// OCR composables - use enhanced OCR with fallback
const useEnhanced = ref(true) // Can be toggled by user or config
const { 
  processImage: processEnhanced, 
  isProcessing: isProcessingEnhanced, 
  progress: progressEnhanced, 
  error: ocrErrorEnhanced, 
  progressMessage: progressMessageEnhanced
} = useEnhancedOCR()

const { 
  processImage: processBasic, 
  isProcessing: isProcessingBasic, 
  progress: progressBasic, 
  error: ocrErrorBasic, 
  progressMessage: progressMessageBasic,
  cleanup 
} = useOCR()

// Computed properties to switch between enhanced and basic
const isProcessing = computed(() => useEnhanced.value ? isProcessingEnhanced.value : isProcessingBasic.value)
const progress = computed(() => useEnhanced.value ? progressEnhanced.value : progressBasic.value)
const progressMessage = computed(() => useEnhanced.value ? progressMessageEnhanced.value : progressMessageBasic.value)
const ocrError = computed(() => useEnhanced.value ? ocrErrorEnhanced.value : ocrErrorBasic.value)

// Component state
const currentStep = ref<UploadStep>('camera')
const capturedImage = ref<File | null>(null)
const ocrResult = ref<OCRResult | null>(null)
const error = ref<string | null>(null)

// Handle image capture from camera
const handleImageCapture = async (imageFile: File) => {
  console.log('Image captured:', imageFile.name, imageFile.size)
  
  capturedImage.value = imageFile
  error.value = null

  try {
    let result: OCRResult | EnhancedOCRResult
    
    if (useEnhanced.value) {
      // Use enhanced OCR with preprocessing and multi-engine support
      result = await processEnhanced(imageFile, {
        engine: 'auto', // Will try Google Vision first, fallback to Tesseract
        preprocessing: true,
        confidence: 0.7
      })
      console.log('Enhanced OCR completed:', result)
      console.log(`Used engine: ${(result as EnhancedOCRResult).engine}`)
    } else {
      // Use basic Tesseract OCR
      result = await processBasic(imageFile)
      console.log('Basic OCR completed:', result)
    }
    
    ocrResult.value = result
    
    // Move to review step
    currentStep.value = 'review'
    
  } catch (err) {
    console.error('OCR processing failed:', err)
    error.value = ocrError.value || 'Failed to process the image. Please try again.'
  }
}

// Handle camera errors
const handleCameraError = (errorMessage: string) => {
  console.error('Camera error:', errorMessage)
  error.value = errorMessage
}

// Handle form submission
const handleSubmit = async (formData: any) => {
  try {
    console.log('Submitting business card data:', formData)
    
    // Normalize website URL if provided
    let normalizedWebsite = ''
    if (formData.website && formData.website.trim()) {
      const normalized = normalizeUrl(formData.website.trim())
      if (normalized) {
        normalizedWebsite = normalized
        console.log('Normalized website URL:', formData.website, '→', normalizedWebsite)
      } else {
        console.warn('Invalid website URL provided:', formData.website)
        normalizedWebsite = formData.website // Keep original if normalization fails
      }
    }
    
    // Prepare the business card data
    const businessCardData = {
      // Basic info from form
      first_name: formData.firstName || formData.first_name || '',
      last_name: formData.lastName || formData.last_name || '',
      name: `${formData.firstName || formData.first_name || ''} ${formData.lastName || formData.last_name || ''}`.trim(),
      email: formData.email || '',
      phone: formData.phone || '',
      company: formData.company || '',
      position: formData.position || formData.job_title || formData.title || '',
      job_title: formData.job_title || formData.position || formData.title || '',
      website: normalizedWebsite,
      
      // Address information
      address: formData.address || '',
      city: formData.city || '',
      state: formData.state || '',
      postal_code: formData.postal_code || formData.zip || '',
      zip: formData.postal_code || formData.zip || '',
      country: formData.country || '',
      
      // Social media
      linkedin: formData.linkedin || '',
      twitter: formData.twitter || '',
      
      // Additional fields
      notes: formData.notes || '',
      tags: formData.tags || [],
      
      // OCR metadata
      ocr_confidence: formData.ocrConfidence || null,
      original_text: formData.originalText || null,
      
      // Image reference if available
      image_url: formData.imageUrl || null,
      image: capturedImage.value || null
    }
    
    // Emit the data to the parent component
    currentStep.value = 'success'
    emit('uploaded', businessCardData)
    
  } catch (err) {
    console.error('Failed to process business card:', err)
    error.value = 'Failed to process business card. Please try again.'
    emit('error', error.value)
  }
}

// Go back to camera step
const goBack = () => {
  currentStep.value = 'camera'
  capturedImage.value = null
  ocrResult.value = null
  error.value = null
}

// Reset uploader to start over
const resetUploader = () => {
  currentStep.value = 'camera'
  capturedImage.value = null
  ocrResult.value = null
  error.value = null
}

// Navigate to business cards list
const viewBusinessCards = () => {
  router.push('/c/businesscards/list')
}

// Cleanup on unmount
onUnmounted(() => {
  cleanup()
})
</script>

<style scoped>
.smart-uploader {
  max-width: 100%;
  margin: 0 auto;
}

.step-container {
  min-height: 60vh;
}

@media (max-width: 768px) {
  .step-container {
    min-height: 70vh;
  }
}
</style>