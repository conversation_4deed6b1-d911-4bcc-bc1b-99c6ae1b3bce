<template>
  <div class="claim-card-modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 rounded-t-lg">
        <div class="flex items-center justify-between">
          <h2 class="text-xl font-semibold text-gray-900">Claim Business Card</h2>
          <button
            @click="$emit('close')"
            class="text-gray-400 hover:text-gray-600"
          >
            <Icon name="mdi:close" class="w-6 h-6" />
          </button>
        </div>
      </div>

      <div class="p-6">
        <!-- Business Card Preview -->
        <div class="mb-6 p-4 bg-gray-50 rounded-lg border">
          <div class="flex items-start space-x-4">
            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-xl">
              {{ businessCard.name.charAt(0) }}
            </div>
            <div class="flex-1">
              <h3 class="text-lg font-semibold text-gray-900">{{ businessCard.name }}</h3>
              <p class="text-gray-600">{{ businessCard.title }}</p>
              <p class="text-gray-600">{{ businessCard.company }}</p>
              <div class="flex items-center mt-2 text-sm text-gray-500">
                <Icon name="mdi:email" class="w-4 h-4 mr-1" />
                <span>{{ businessCard.email }}</span>
                <Icon name="mdi:phone" class="w-4 h-4 ml-4 mr-1" />
                <span>{{ businessCard.phone }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Claim Status Check -->
        <div v-if="!canClaim" class="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div class="flex items-center">
            <Icon name="mdi:alert-circle" class="w-5 h-5 text-yellow-600 mr-2" />
            <span class="text-yellow-800 font-medium">Cannot Claim This Card</span>
          </div>
          <p class="text-yellow-700 mt-1 text-sm">
            {{ claimStatusMessage }}
          </p>
        </div>

        <!-- Claim Form -->
        <form v-if="canClaim" @submit.prevent="submitClaim" class="space-y-6">
          <!-- Verification Method Selection -->
          <div>
            <h4 class="text-lg font-medium text-gray-900 mb-4">Choose Verification Method</h4>
            <div class="grid grid-cols-1 gap-3">
              <div
                v-for="method in verificationMethods"
                :key="method.type"
                class="relative"
              >
                <input
                  :id="method.type"
                  v-model="claimData.verificationMethod"
                  :value="method.type"
                  type="radio"
                  class="sr-only"
                />
                <label
                  :for="method.type"
                  class="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                  :class="claimData.verificationMethod === method.type 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'border-gray-300'"
                >
                  <div class="flex items-center flex-1">
                    <Icon :name="method.icon" class="w-6 h-6 text-blue-600 mr-3" />
                    <div>
                      <p class="font-medium text-gray-900">{{ method.name }}</p>
                      <p class="text-sm text-gray-600">{{ method.description }}</p>
                    </div>
                  </div>
                  <div
                    v-if="claimData.verificationMethod === method.type"
                    class="w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center"
                  >
                    <Icon name="mdi:check" class="w-3 h-3 text-white" />
                  </div>
                </label>
              </div>
            </div>
          </div>

          <!-- Verification Input -->
          <div v-if="selectedMethod?.requiresInput" class="space-y-4">
            <h4 class="text-lg font-medium text-gray-900">Verification Details</h4>
            
            <!-- Email/Phone Input -->
            <div v-if="selectedMethod.inputType === 'email' || selectedMethod.inputType === 'tel'">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ selectedMethod.inputPlaceholder }}
              </label>
              <input
                v-model="claimData.verificationData.contactInfo"
                :type="selectedMethod.inputType"
                :placeholder="selectedMethod.inputPlaceholder"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
              <p class="text-sm text-gray-600 mt-1">
                Make sure this matches the {{ selectedMethod.inputType === 'email' ? 'email' : 'phone number' }} on the business card exactly.
              </p>
            </div>

            <!-- File Upload -->
            <div v-if="selectedMethod.inputType === 'file'">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Upload Verification Document
              </label>
              <div
                @drop="handleFileDrop"
                @dragover.prevent
                @dragenter.prevent
                class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors"
              >
                <input
                  ref="fileInput"
                  type="file"
                  accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                  @change="handleFileSelect"
                  class="hidden"
                />
                <Icon name="mdi:cloud-upload" class="w-12 h-12 text-gray-400 mx-auto mb-2" />
                <p class="text-gray-600 mb-2">Drop your document here or click to browse</p>
                <button
                  type="button"
                  @click="$refs.fileInput.click()"
                  class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Choose File
                </button>
                <p class="text-xs text-gray-500 mt-2">
                  Accepted formats: PDF, JPG, PNG, DOC, DOCX (max 10MB)
                </p>
              </div>
              
              <div v-if="uploadedFile" class="mt-3 p-3 bg-green-50 border border-green-200 rounded-md">
                <div class="flex items-center">
                  <Icon name="mdi:file-check" class="w-5 h-5 text-green-600 mr-2" />
                  <span class="text-green-800 font-medium">{{ uploadedFile.name }}</span>
                  <button
                    type="button"
                    @click="removeFile"
                    class="ml-auto text-green-600 hover:text-green-800"
                  >
                    <Icon name="mdi:close" class="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Additional Evidence (Optional) -->
          <div class="space-y-4">
            <h4 class="text-lg font-medium text-gray-900">Additional Evidence (Optional)</h4>
            <p class="text-sm text-gray-600">
              Provide additional proof to strengthen your claim. This could include:
            </p>
            <ul class="text-sm text-gray-600 list-disc list-inside space-y-1">
              <li>Website or social media profiles</li>
              <li>Other business documents</li>
              <li>Professional references</li>
            </ul>

            <div class="space-y-3">
              <div
                v-for="(evidence, index) in claimData.evidence"
                :key="index"
                class="flex items-center space-x-2"
              >
                <select
                  v-model="evidence.type"
                  class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="website">Website</option>
                  <option value="social_media">Social Media</option>
                  <option value="document">Document</option>
                  <option value="photo">Photo</option>
                </select>
                <input
                  v-model="evidence.url"
                  type="url"
                  placeholder="Enter URL or upload file"
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <button
                  type="button"
                  @click="removeEvidence(index)"
                  class="text-red-600 hover:text-red-800"
                >
                  <Icon name="mdi:trash-can" class="w-4 h-4" />
                </button>
              </div>
              
              <button
                type="button"
                @click="addEvidence"
                class="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                <Icon name="mdi:plus" class="w-4 h-4 inline mr-1" />
                Add Evidence
              </button>
            </div>
          </div>

          <!-- Disclaimer -->
          <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div class="flex items-start">
              <Icon name="mdi:information" class="w-5 h-5 text-blue-600 mr-2 mt-0.5" />
              <div class="text-sm text-blue-800">
                <p class="font-medium mb-1">Important:</p>
                <p>
                  By claiming this business card, you confirm that you are the legitimate owner of this contact information. 
                  False claims may result in account suspension.
                </p>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex space-x-4 pt-6 border-t">
            <button
              type="button"
              @click="$emit('close')"
              class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              :disabled="isSubmitting || !isFormValid"
              class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Icon 
                v-if="isSubmitting" 
                name="mdi:loading" 
                class="w-4 h-4 inline mr-2 animate-spin" 
              />
              {{ isSubmitting ? 'Submitting Claim...' : 'Submit Claim' }}
            </button>
          </div>
        </form>

        <!-- Success Message -->
        <div v-if="claimSubmitted" class="text-center py-8">
          <Icon name="mdi:check-circle" class="w-16 h-16 text-green-500 mx-auto mb-4" />
          <h3 class="text-lg font-semibold text-gray-900 mb-2">Claim Submitted!</h3>
          <p class="text-gray-600">
            Your claim has been submitted and is being reviewed. You'll receive a notification once it's processed.
          </p>
          <button
            @click="$emit('close')"
            class="mt-4 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { useClaimingSystem } from '~/composables/useClaimingSystem'

interface Props {
  businessCard: {
    id: string
    name: string
    title: string
    company: string
    email: string
    phone: string
  }
  currentUserId: string
}

const props = defineProps<Props>()
const emit = defineEmits(['close', 'claimed'])

// Use claiming system
const {
  verificationMethods,
  canClaimCard,
  submitClaimRequest,
  isProcessing
} = useClaimingSystem()

// Component state
const isSubmitting = ref(false)
const claimSubmitted = ref(false)
const uploadedFile = ref<File | null>(null)

// Form data
const claimData = reactive({
  verificationMethod: 'email',
  verificationData: {
    contactInfo: ''
  },
  evidence: []
})

// Computed properties
const canClaim = computed(() => canClaimCard(props.businessCard.id, props.currentUserId))

const claimStatusMessage = computed(() => {
  if (!canClaim.value) {
    return 'This business card is already claimed or has pending claims.'
  }
  return ''
})

const selectedMethod = computed(() => 
  verificationMethods.value.find(m => m.type === claimData.verificationMethod)
)

const isFormValid = computed(() => {
  if (!selectedMethod.value?.requiresInput) return true
  
  if (selectedMethod.value.inputType === 'email' || selectedMethod.value.inputType === 'tel') {
    return claimData.verificationData.contactInfo.trim() !== ''
  }
  
  if (selectedMethod.value.inputType === 'file') {
    return uploadedFile.value !== null
  }
  
  return true
})

// Methods
const submitClaim = async () => {
  if (!isFormValid.value) return

  try {
    isSubmitting.value = true

    // Prepare verification data
    const verificationData = { ...claimData.verificationData }
    if (uploadedFile.value) {
      // In real app, upload file and store URL
      verificationData.documentUrl = `uploaded/${uploadedFile.value.name}`
    }

    await submitClaimRequest(
      props.businessCard.id,
      props.currentUserId,
      'uploader-user-id', // Would be actual uploader ID
      {
        verificationMethod: claimData.verificationMethod,
        verificationData,
        evidence: claimData.evidence
      }
    )

    claimSubmitted.value = true
    emit('claimed')

  } catch (error) {
    console.error('Failed to submit claim:', error)
    alert('Failed to submit claim. Please try again.')
  } finally {
    isSubmitting.value = false
  }
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files[0]) {
    handleFile(target.files[0])
  }
}

const handleFileDrop = (event: DragEvent) => {
  event.preventDefault()
  if (event.dataTransfer?.files && event.dataTransfer.files[0]) {
    handleFile(event.dataTransfer.files[0])
  }
}

const handleFile = (file: File) => {
  // Validate file size (10MB max)
  if (file.size > 10 * 1024 * 1024) {
    alert('File size must be less than 10MB')
    return
  }

  // Validate file type
  const allowedTypes = [
    'application/pdf',
    'image/jpeg',
    'image/jpg',
    'image/png',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ]

  if (!allowedTypes.includes(file.type)) {
    alert('Please upload a valid file type (PDF, JPG, PNG, DOC, DOCX)')
    return
  }

  uploadedFile.value = file
}

const removeFile = () => {
  uploadedFile.value = null
}

const addEvidence = () => {
  claimData.evidence.push({
    type: 'website',
    url: '',
    description: ''
  })
}

const removeEvidence = (index: number) => {
  claimData.evidence.splice(index, 1)
}
</script>

<style scoped>
/* Custom scrollbar */
.claim-card-modal ::-webkit-scrollbar {
  width: 6px;
}

.claim-card-modal ::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.claim-card-modal ::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.claim-card-modal ::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>