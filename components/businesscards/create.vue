<script setup lang="ts">
import { watchEffect } from 'vue';
import UploadEnhancedFormUploader from '~/components/upload/EnhancedFormUploader.vue';
import BusinesscardsOcrProcessor from '~/components/businesscards/OcrProcessor.vue';
import UiCard from '~/components/ui/Card.vue';

const props = defineProps({
  open: {
    type: Boolean,
    default: () => {
      return true;
    },
  },
  homepage: {
    type: Boolean,
    default: false,
  },
  customSubmitHandler: {
    type: Function,
    default: null,
  },
  hideNavigation: {
    type: Boolean,
    default: false,
  },
  forceHideUploadAfterOcr: {
    type: Boolean,
    default: true,
  },
  // New props for attach mode
  attachMode: {
    type: Boolean,
    default: false,
  },
  existingContactData: {
    type: Object,
    default: null,
  },
  showBrowseOption: {
    type: Boolean,
    default: false,
  },
});

// For OCR processing
const selectedImage = ref(null);
const ocrComplete = ref(false);
const ocrData = ref(null);
const ocrStatus = ref({ status: 'idle', progress: 0 });
const ocrValidationState = ref({ valid: false, errors: {} });
const showOcrDetails = ref(false);
const hideUploadAfterOcr = ref(true);

// Helper function to create object URL safely
const createObjectURL = (file: any) => {
  if (file && typeof URL !== 'undefined') {
    return URL.createObjectURL(file);
  }
  return '';
};

// Handle file selection
const handleFilesSelected = (files: any) => {
  if (files && files.length > 0) {
    selectedImage.value = files[0];
    ocrComplete.value = false;
  }
};

// This watchEffect will be moved after formsItem definition

// Handle file cropped
const handleFileCropped = (file) => {
  console.log('File cropped:', file);
  selectedImage.value = file;
  ocrComplete.value = false;

  // Skip OCR if in attach mode
  if (props.attachMode) {
    // In attach mode, we skip OCR and directly mark as complete
    setTimeout(() => {
      ocrComplete.value = true;
      ocrData.value = null; // No OCR data in attach mode
      hideUploadAfterOcr.value = true;
    }, 100);
    return;
  }

  // Add a small delay to ensure the file is properly set before OCR processing
  setTimeout(() => {
    console.log('Starting OCR after crop with file:', selectedImage.value);
    // Force OCR to start if it hasn't already
    if (selectedImage.value && ocrStatus.value.status !== 'processing') {
      ocrStatus.value = { status: 'started', progress: 0 };
    }
  }, 100);
};

// Handle OCR completion
const handleExtractionComplete = (data) => {
  ocrComplete.value = true;
  ocrData.value = data;
  // Add a small delay to ensure form is populated before hiding upload
  setTimeout(() => {
    hideUploadAfterOcr.value = true;
  }, 500);
};

// Handle OCR status updates
const handleProcessingStatus = (status) => {
  console.log('OCR status update:', status);
  ocrStatus.value = status;

  // If there's an error, show it in the console for debugging
  if (status.status === 'error') {
    console.error('OCR processing error details:', status.error);
  }
};

// Handle OCR validation
const handleOcrValidation = (validation) => {
  ocrValidationState.value = validation;
};

// Reset for trying again
const resetUpload = () => {
  selectedImage.value = null;
  ocrComplete.value = false;
  ocrData.value = null;
  ocrStatus.value = { status: 'idle', progress: 0 };
  ocrValidationState.value = { valid: false, errors: {} };
  showOcrDetails.value = false;
  hideUploadAfterOcr.value = false;
};

const formsItem = ref({
  title: "Business Cards",
  filters: {
    index: "omni",
    collection: "businesscards",
    queryBy: "",
    route_add: "/c/businesscards-create",

    description: "Businesscards",
    areas: ["Business Card Information"],
    filterBy:
      "Touched Records, Untouched Records, Record Action, Related Records Action",
  },
  data: [
    {
      label: "id",
      val: "id",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Created At",
      val: "created_at",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Updated at",
      val: "updated_at",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "User ownership",
      val: "user_own",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Users with access",
      val: "user_access",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Space ownership",
      val: "space_own",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Space with access",
      val: "space_access",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Business Card",
      val: "imageUrl",
      value: "",
      class: "col-span-2",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "Image",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      component: "FormsInputsUpload",
      action: "forms-actions-docs",
    },
    {
      label: "Name",
      val: "name",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
    {
      label: "Tags (comma seperated)",
      val: "tags",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsTags",
      action: "forms-actions-tags",
    },
    {
      label: "Description",
      val: "description",
      value: "",
      class: "col-span-2",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsTextarea",
      action: "forms-actions-quill",
    },

    {
      label: "Status",
      val: "status",
      value: "Pending",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: true,
      small_table: false,
      small_form: false,
      editable: false,
      card: false,
      card_type: "Image",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      component: "FormsInputsSelect",
      action: "forms-actions-select",
      options: ["Approved", "Pending", "Declined"],
    },
    {
      label: "First Name",
      val: "first_name",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
    {
      label: "Last Name",
      val: "last_name",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
    {
      label: "Email",
      val: "email",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },

    {
      label: "Website",
      val: "website",
      value: "",
      placeholder: "www.example.com or https://example.com",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: false,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
    {
      label: "Phone",
      val: "phone",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
    {
      label: "Company",
      val: "company",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: false,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
    {
      label: "Job Title",
      val: "job_title",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: false,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
    {
      label: "Address",
      val: "address",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: false,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
    {
      label: "City",
      val: "city",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: false,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
    {
      label: "State",
      val: "state",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: false,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
    {
      label: "Postal Code",
      val: "postal_code",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: false,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
    {
      label: "Country",
      val: "country",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: false,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
  ],
});

// Pre-populate form data when in attach mode
watchEffect(() => {
  if (props.attachMode && props.existingContactData && formsItem.value.data) {
    // Map existing contact data to form fields
    const contactData = props.existingContactData;
    
    formsItem.value.data.forEach(field => {
      switch (field.val) {
        case 'first_name':
          field.value = contactData.first_name || contactData.firstName || '';
          break;
        case 'last_name':
          field.value = contactData.last_name || contactData.lastName || '';
          break;
        case 'email':
          field.value = contactData.email || '';
          break;
        case 'phone':
          field.value = contactData.phone || '';
          break;
        case 'company':
          field.value = contactData.company || '';
          break;
        case 'job_title':
          field.value = contactData.jobTitle || '';
          break;
        case 'website':
          field.value = contactData.website || '';
          break;
        case 'address':
          field.value = contactData.address?.street || '';
          break;
        case 'city':
          field.value = contactData.address?.city || '';
          break;
        case 'state':
          field.value = contactData.address?.state || '';
          break;
        case 'postal_code':
          field.value = contactData.address?.postalCode || '';
          break;
        case 'country':
          field.value = contactData.address?.country || '';
          break;
        case 'tags':
          field.value = Array.isArray(contactData.tags) ? contactData.tags.join(', ') : '';
          break;
        case 'description':
          field.value = contactData.leadNotes || contactData.description || '';
          break;
      }
    });
  }
});

// Dynamic form title and description based on mode
const getFormTitle = (): string => {
  if (props.attachMode) {
    return hideUploadAfterOcr.value && ocrComplete.value ? 'Business Card Attachment' : 'Attach Business Card';
  }
  return hideUploadAfterOcr.value && ocrComplete.value ? 'Business Card Details' : 'Create Business Card';
};

const getFormDescription = (): string => {
  if (props.attachMode) {
    return hideUploadAfterOcr.value && ocrComplete.value 
      ? 'Review the contact information and attach the business card image' 
      : 'Upload a business card image to attach to this contact';
  }
  return hideUploadAfterOcr.value && ocrComplete.value 
    ? 'Review and edit the extracted information' 
    : 'Upload a business card or enter contact information';
};

const userSpaces = useState<any>("userSpaces", () => {
  return [];
});

const created = async (data: any) => {
  // Transform imageUrl from object to string if needed
  if (data.imageUrl && typeof data.imageUrl === 'object') {
    // Use the download URL if available, otherwise use the src
    data.imageUrl = data.imageUrl.url || data.imageUrl.src || '';
  }
  
  // If a custom submit handler is provided, use it instead
  if (props.customSubmitHandler) {
    await props.customSubmitHandler(data);
  } else {
    // Default behavior
    userSpaces.value.push(data);
  }
};
</script>

<template>
  <div>
    <!-- Try Again Button (shown when image is selected and OCR is complete) -->
    <div v-if="selectedImage && ocrComplete && hideUploadAfterOcr" class="mb-6 flex justify-center">
      <button
        @click="resetUpload"
        class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
      >
        <Icon name="mdi:camera-retake" class="mr-2 h-4 w-4" />
        Try Another Card
      </button>
    </div>

    <!-- OCR Processing Section (only show status, not OCR details) - Skip in attach mode -->
    <UiCard v-if="selectedImage && !ocrComplete && !attachMode" title="Processing Card" variant="bordered" elevated class="mb-6">
      <!-- OCR Status Indicator -->
      <div class="mb-4">
        <div v-if="ocrStatus.status === 'started'" class="flex items-center justify-center p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span class="text-blue-700 dark:text-blue-300">Starting OCR processing...</span>
        </div>

        <div v-else-if="ocrStatus.status === 'processing'" class="flex items-center justify-center p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span class="text-blue-700 dark:text-blue-300">Processing OCR... {{ Math.round(ocrStatus.progress * 100) }}%</span>
        </div>

        <div v-else-if="ocrStatus.status === 'completed'" class="flex items-center justify-center p-4 bg-green-50 dark:bg-green-900 rounded-lg">
          <svg class="h-5 w-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          <span class="text-green-700 dark:text-green-300">OCR processing complete! Form fields have been populated.</span>
        </div>

        <div v-else-if="ocrStatus.status === 'error'" class="flex items-center justify-center p-4 bg-red-50 dark:bg-red-900 rounded-lg">
          <svg class="h-5 w-5 mr-2 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <span class="text-red-700 dark:text-red-300">Error during OCR processing. Please try again.</span>
        </div>
      </div>
    </UiCard>

    <!-- Hidden OCR Processor (runs in background) - Only shown when not in attach mode -->
    <div v-show="false">
      <BusinesscardsOcrProcessor
        v-if="selectedImage && !attachMode"
        :image="selectedImage"
        @extraction-complete="handleExtractionComplete"
        @processing-status="handleProcessingStatus"
        @validation="handleOcrValidation"
      />
    </div>

    <!-- Browse Existing Cards Button (only in attach mode with browse option) -->
    <div v-if="attachMode && showBrowseOption" class="mb-6">
      <UiCard title="Business Card Options" variant="bordered" elevated>
        <div class="flex flex-col sm:flex-row gap-4">
          <button
            type="button"
            class="flex-1 px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors flex items-center justify-center"
          >
            <Icon name="mdi:folder-image" class="mr-2 h-5 w-5" />
            Browse Existing Cards
          </button>
          <div class="flex items-center px-4 text-gray-400">
            <span class="text-sm">or</span>
          </div>
          <div class="flex-1 text-center text-gray-300">
            <span class="text-sm">Upload a new business card below</span>
          </div>
        </div>
      </UiCard>
    </div>

    <!-- Business Card Form -->
    <UploadEnhancedFormUploader
      :title="getFormTitle()"
      :description="getFormDescription()"
      :formItem="formsItem"
      :homepage="homepage"
      :enableCropping="true"
      :cropAspectRatio="1.78"
      imageField="imageUrl"
      :ocrData="attachMode ? null : ocrData"
      :hideUploadSection="hideUploadAfterOcr && selectedImage && ocrComplete"
      @files-selected="handleFilesSelected"
      @file-cropped="handleFileCropped"
      @created="created"
    />
  </div>
</template>

