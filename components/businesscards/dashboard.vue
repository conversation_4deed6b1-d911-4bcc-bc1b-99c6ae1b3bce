<script setup lang="ts">
const selectedBusinesscards = useState('selectedBusinesscards', ()=> { return {}})
onMounted(()=> {
  selectedBusinesscards.value = {}
})
  </script>
  
  <template>
        <layouts-main title="Business cards">
          <template #buttons><tooltip-icon text="Dashboard" side="left" icon="line-md:grid-3-filled" @submit="$router.push('/c/dashboard')"/></template>
        <template #content> 
        <div class="w-full overflow-hidden">
          <businesscards-lists />
        </div>
        </template>
      </layouts-main>
  </template>