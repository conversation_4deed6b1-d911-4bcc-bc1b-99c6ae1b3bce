<template>
  <div class="camera-scanner">
    <!-- Camera Permission Request Modal -->
    <div v-if="showPermissionModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 max-w-md mx-4">
        <div class="text-center">
          <div class="mb-4">
            <Icon name="mdi:camera" class="w-16 h-16 text-blue-600 mx-auto" />
          </div>
          <h3 class="text-lg font-semibold mb-2">Camera Access Required</h3>
          <p class="text-gray-600 mb-4">
            We need access to your camera to scan business cards. This helps you quickly add contacts to your CRM.
          </p>
          <div class="flex space-x-3">
            <button 
              @click="requestCameraPermission"
              class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Allow Camera
            </button>
            <button 
              @click="useFileUpload"
              class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
            >
              Upload Image
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Camera Interface -->
    <div v-if="cameraActive" class="relative">
      <video 
        ref="videoElement"
        class="w-full h-auto rounded-lg"
        autoplay 
        playsinline
        @loadedmetadata="onVideoLoaded"
      ></video>
      
      <!-- Camera Controls -->
      <div class="absolute bottom-4 left-0 right-0 flex justify-center space-x-4">
        <button 
          @click="capturePhoto"
          :disabled="!canCapture"
          class="bg-white rounded-full p-4 shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50"
        >
          <Icon name="mdi:camera" class="w-8 h-8 text-blue-600" />
        </button>
        <button 
          @click="switchCamera"
          v-if="cameras.length > 1"
          class="bg-white rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-200"
        >
          <Icon name="mdi:camera-flip" class="w-6 h-6 text-gray-700" />
        </button>
        <button 
          @click="stopCamera"
          class="bg-white rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-200"
        >
          <Icon name="mdi:close" class="w-6 h-6 text-red-600" />
        </button>
      </div>

      <!-- Auto-capture indicator -->
      <div v-if="autoCapture && detectedCard" class="absolute top-4 left-4 right-4">
        <div class="bg-green-500 text-white px-4 py-2 rounded-lg text-center">
          <Icon name="mdi:check-circle" class="w-5 h-5 inline mr-2" />
          Business card detected! Capturing in {{ autoCapturCountdown }}s
        </div>
      </div>
    </div>

    <!-- File Upload Fallback -->
    <div v-if="showFileUpload" class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
      <input 
        ref="fileInput"
        type="file" 
        accept="image/*" 
        @change="handleFileUpload"
        class="hidden"
      >
      <div class="mb-4">
        <Icon name="mdi:file-image" class="w-16 h-16 text-gray-400 mx-auto" />
      </div>
      <h3 class="text-lg font-semibold mb-2">Upload Business Card Image</h3>
      <p class="text-gray-600 mb-4">
        Choose an image file from your device to extract contact information.
      </p>
      <button 
        @click="triggerFileUpload"
        class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
      >
        Select Image
      </button>
    </div>

    <!-- Processing Indicator -->
    <div v-if="processing" class="flex items-center justify-center py-8">
      <div class="text-center">
        <Icon name="mdi:loading" class="w-8 h-8 animate-spin text-blue-600 mx-auto mb-2" />
        <p class="text-gray-600">{{ processingMessage }}</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4">
      <div class="flex items-center">
        <Icon name="mdi:alert-circle" class="w-5 h-5 text-red-600 mr-2" />
        <span class="text-red-800">{{ error }}</span>
      </div>
      <div class="mt-2">
        <button 
          @click="retryCamera"
          class="text-red-600 hover:text-red-800 underline text-sm"
        >
          Try Again
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'

const emit = defineEmits(['capture', 'error'])

// Camera state
const videoElement = ref<HTMLVideoElement>()
const fileInput = ref<HTMLInputElement>()
const stream = ref<MediaStream>()
const cameraActive = ref(false)
const canCapture = ref(false)
const cameras = ref<MediaDeviceInfo[]>([])
const currentCameraIndex = ref(0)

// UI state
const showPermissionModal = ref(true)
const showFileUpload = ref(false)
const processing = ref(false)
const processingMessage = ref('')
const error = ref('')

// Auto-capture
const autoCapture = ref(false)
const detectedCard = ref(false)
const autoCapturCountdown = ref(3)

// Check if camera is supported
const isCameraSupported = () => {
  return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)
}

// Get available cameras
const getCameras = async () => {
  try {
    const devices = await navigator.mediaDevices.enumerateDevices()
    cameras.value = devices.filter(device => device.kind === 'videoinput')
  } catch (err) {
    console.error('Error getting cameras:', err)
  }
}

// Request camera permission and start camera
const requestCameraPermission = async () => {
  if (!isCameraSupported()) {
    error.value = 'Camera not supported on this device'
    showFileUpload.value = true
    showPermissionModal.value = false
    return
  }

  try {
    processing.value = true
    processingMessage.value = 'Requesting camera access...'
    
    await getCameras()
    await startCamera()
    
    showPermissionModal.value = false
    processing.value = false
  } catch (err) {
    console.error('Camera permission denied:', err)
    error.value = 'Camera access denied. Please use file upload instead.'
    showFileUpload.value = true
    showPermissionModal.value = false
    processing.value = false
  }
}

// Start camera with current camera index
const startCamera = async () => {
  try {
    const constraints = {
      video: {
        deviceId: cameras.value[currentCameraIndex.value]?.deviceId,
        width: { ideal: 1920 },
        height: { ideal: 1080 },
        facingMode: currentCameraIndex.value === 0 ? 'environment' : 'user'
      }
    }

    stream.value = await navigator.mediaDevices.getUserMedia(constraints)
    
    await nextTick()
    if (videoElement.value) {
      videoElement.value.srcObject = stream.value
      cameraActive.value = true
    }
  } catch (err) {
    console.error('Error starting camera:', err)
    throw err
  }
}

// Video loaded handler
const onVideoLoaded = () => {
  canCapture.value = true
  
  // Start card detection (simplified simulation)
  if (autoCapture.value) {
    setTimeout(() => {
      detectedCard.value = true
      startAutoCapture()
    }, 2000)
  }
}

// Auto-capture countdown
const startAutoCapture = () => {
  const countdown = setInterval(() => {
    autoCapturCountdown.value--
    if (autoCapturCountdown.value <= 0) {
      clearInterval(countdown)
      capturePhoto()
    }
  }, 1000)
}

// Capture photo from video stream
const capturePhoto = async () => {
  if (!videoElement.value || !canCapture.value) return

  try {
    processing.value = true
    processingMessage.value = 'Capturing image...'

    const canvas = document.createElement('canvas')
    const context = canvas.getContext('2d')
    
    canvas.width = videoElement.value.videoWidth
    canvas.height = videoElement.value.videoHeight
    
    context?.drawImage(videoElement.value, 0, 0)
    
    // Convert to blob
    canvas.toBlob((blob) => {
      if (blob) {
        const file = new File([blob], 'business-card.jpg', { type: 'image/jpeg' })
        emit('capture', file)
        stopCamera()
      }
      processing.value = false
    }, 'image/jpeg', 0.9)
    
  } catch (err) {
    console.error('Error capturing photo:', err)
    error.value = 'Failed to capture photo'
    processing.value = false
  }
}

// Switch between cameras
const switchCamera = async () => {
  if (cameras.value.length <= 1) return
  
  currentCameraIndex.value = (currentCameraIndex.value + 1) % cameras.value.length
  
  stopCamera()
  await startCamera()
}

// Stop camera
const stopCamera = () => {
  if (stream.value) {
    stream.value.getTracks().forEach(track => track.stop())
    stream.value = undefined
  }
  cameraActive.value = false
  canCapture.value = false
}

// Use file upload instead
const useFileUpload = () => {
  showPermissionModal.value = false
  showFileUpload.value = true
}

// Trigger file input
const triggerFileUpload = () => {
  fileInput.value?.click()
}

// Handle file upload
const handleFileUpload = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (file) {
    if (!file.type.startsWith('image/')) {
      error.value = 'Please select a valid image file'
      return
    }
    
    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      error.value = 'Image file is too large. Please choose a file under 10MB.'
      return
    }
    
    emit('capture', file)
  }
}

// Retry camera
const retryCamera = () => {
  error.value = ''
  showPermissionModal.value = true
  showFileUpload.value = false
}

// Cleanup on unmount
onUnmounted(() => {
  stopCamera()
})

// Initialize
onMounted(() => {
  if (!isCameraSupported()) {
    showPermissionModal.value = false
    showFileUpload.value = true
  }
})
</script>

<style scoped>
.camera-scanner {
  max-width: 100%;
}

video {
  transform: scaleX(-1); /* Mirror for better UX */
}

@media (max-width: 768px) {
  video {
    height: 60vh;
    object-fit: cover;
  }
}
</style>