<script setup lang="ts">
import { computed, ref, watch } from 'vue';

import { OcrRecognize, supportedLanguages } from '@/assets/scripts/ocr';
import { useFormValidation } from '~/composables/useFormValidation';
import { useEnhancedOCR } from '~/composables/useEnhancedOCR';

const props = defineProps({
  image: {
    type: Object,
    default: null,
  },
  validateOnChange: {
    type: Boolean,
    default: true,
  },
  validateOnBlur: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(['extraction-complete', 'processing-status', 'validation']);

// OCR state
const isProcessing = ref(false);
const progress = ref(0);
const extractedText = ref('');
const extractedData = ref({
  first_name: '',
  last_name: '',
  company: '',
  job_title: '',
  email: '',
  phone: '',
  website: '',
  address: '',
  city: '',
  state: '',
  postal_code: '',
  country: '',
});
const confidence = ref(0);
const selectedLanguages = ref([supportedLanguages.find(lang => lang.label === 'English')]);
const showRawText = ref(false);
const usedEngine = ref(''); // Track which OCR engine was used

// Validation state
const validationErrors = ref({});
const isDirty = ref({});
const isTouched = ref({});

// Validation schema
const validationSchema = {
  first_name: [{ rule: 'required', message: 'First name is required' }],
  last_name: [{ rule: 'required', message: 'Last name is required' }],
  email: [{ rule: 'email', message: 'Please enter a valid email address' }],
  phone: [{ rule: 'phone', message: 'Please enter a valid phone number' }],
  website: [{ rule: 'url', message: 'Please enter a valid URL' }],
};

// Initialize form validation
const { formState, isValid, validateForm, validateField, handleChange, handleBlur } =
  useFormValidation(extractedData.value, validationSchema);

// Enhanced OCR composable
const { processImage: enhancedProcessImage } = useEnhancedOCR();

// Start OCR processing
const startOcrProcessing = async () => {
  if (!props.image) return;

  isProcessing.value = true;
  progress.value = 0;
  extractedText.value = '';
  emit('processing-status', { status: 'started' });

  try {
    console.log('Starting Enhanced OCR processing with image:', props.image);

    // Convert image to File if it's not already
    let imageFile = props.image;
    if (!(props.image instanceof File)) {
      // If it's a data URL or blob, convert to File
      if (typeof props.image === 'string' && props.image.startsWith('data:')) {
        const response = await fetch(props.image);
        const blob = await response.blob();
        imageFile = new File([blob], 'business-card.jpg', { type: blob.type });
      } else if (props.image instanceof Blob) {
        imageFile = new File([props.image], 'business-card.jpg', { type: props.image.type });
      }
    }

      // Use enhanced OCR with Gemini as primary engine for best accuracy
      const result = await enhancedProcessImage(imageFile, {
        engine: 'auto', // Will try Gemini first (best), then Google Vision, then Tesseract
        preprocessing: false, // Gemini handles various image qualities natively
        confidence: 0.6, // Lower threshold for better results
        languages: selectedLanguages.value.map(lang => lang.value)
      });

      console.log('Enhanced OCR result:', result);

      if (result && result.rawText) {
        extractedText.value = result.rawText;
        confidence.value = result.confidence || 0;
        usedEngine.value = result.engine || 'tesseract'; // Track which engine was used

      // Use the structured data from enhanced OCR if available
      if (result.name || result.email || result.phone) {
        // Map enhanced OCR results to our data structure
        extractedData.value.first_name = result.name?.split(' ')[0] || '';
        extractedData.value.last_name = result.name?.split(' ').slice(1).join(' ') || '';
        extractedData.value.company = result.company || '';
        extractedData.value.job_title = result.title || '';
        extractedData.value.email = result.email || '';
        extractedData.value.phone = result.phone || '';
        extractedData.value.website = result.website || '';
        extractedData.value.address = result.address?.street || '';
        extractedData.value.city = result.address?.city || '';
        extractedData.value.state = result.address?.state || '';
        extractedData.value.postal_code = result.address?.postalCode || '';
        extractedData.value.country = result.address?.country || '';
      } else {
        // Fallback to text parsing if structured data not available
        parseBusinessCardText(result.rawText);
      }

      // Mark all fields as dirty since they were populated by OCR
      Object.keys(extractedData.value).forEach(key => {
        if (extractedData.value[key]) {
          isDirty.value[key] = true;
          validateField(key, extractedData.value[key]);
        }
      });

      emit('processing-status', { 
        status: 'completed', 
        engine: result.engine,
        processingTime: result.processingTime 
      });
      emit('extraction-complete', {
        text: result.rawText,
        data: extractedData.value,
        confidence: result.confidence || 0,
        engine: result.engine,
        preprocessed: result.preprocessed
      });
    } else {
      throw new Error('Enhanced OCR processing failed to return text');
    }
  } catch (error) {
    console.error('Enhanced OCR processing error:', error);
    
    // Fallback to basic Tesseract if enhanced OCR fails
    try {
      console.log('Falling back to basic Tesseract OCR...');
      const logger = message => {
        if (message.status === 'recognizing text') {
          progress.value = message.progress * 0.5; // Show partial progress
        }
      };

      const workerConfig = { logger };
      const result = await OcrRecognize(workerConfig, selectedLanguages.value, props.image);
      
      if (result && result.text) {
        extractedText.value = result.text;
        confidence.value = result.confidence || 0;
        usedEngine.value = 'tesseract-fallback'; // Track fallback engine
        parseBusinessCardText(result.text);
        
        emit('processing-status', { status: 'completed', engine: 'tesseract-fallback' });
        emit('extraction-complete', {
          text: result.text,
          data: extractedData.value,
          confidence: result.confidence || 0,
          engine: 'tesseract'
        });
      } else {
        throw new Error('All OCR processing methods failed');
      }
    } catch (fallbackError) {
      console.error('Fallback OCR also failed:', fallbackError);
      emit('processing-status', { status: 'error', error: fallbackError });
    }
  } finally {
    isProcessing.value = false;
  }
};

// Enhanced name parsing function to handle various business card name formats
const parseNameFromText = (nameText: string) => {
  console.log('OCR: Starting name parsing for:', nameText);

  // Clean the name text
  let cleanName = nameText.trim();

  // Remove common titles and suffixes
  const titles = ['dr', 'dr.', 'mr', 'mr.', 'mrs', 'mrs.', 'ms', 'ms.', 'prof', 'prof.'];
  const suffixes = ['jr', 'jr.', 'sr', 'sr.', 'iii', 'ii', 'iv', 'phd', 'md', 'ceo', 'cto', 'cfo'];

  // Convert to lowercase for comparison
  const cleanNameLower = cleanName.toLowerCase();

  // Remove titles from the beginning
  for (const title of titles) {
    if (cleanNameLower.startsWith(`${title} `)) {
      cleanName = cleanName.substring(title.length).trim();
      console.log('OCR: Removed title, now:', cleanName);
      break;
    }
  }

  // Remove suffixes from the end (but keep them if they might be job titles)
  const words = cleanName.split(' ');
  const lastWord = words[words.length - 1]?.toLowerCase();

  // Only remove if it's clearly a personal suffix (not a job title like CEO)
  const personalSuffixes = ['jr', 'jr.', 'sr', 'sr.', 'iii', 'ii', 'iv'];
  if (personalSuffixes.includes(lastWord)) {
    words.pop();
    cleanName = words.join(' ');
    console.log('OCR: Removed suffix, now:', cleanName);
  }

  // Handle different name formats
  let firstName = '';
  let lastName = '';

  // Format 1: "LastName, FirstName" or "LastName,FirstName"
  if (cleanName.includes(',')) {
    const parts = cleanName.split(',').map(p => p.trim());
    if (parts.length >= 2) {
      lastName = parts[0];
      firstName = parts[1];
      console.log('OCR: Detected comma format - Last:', lastName, 'First:', firstName);
      return { firstName, lastName };
    }
  }

  // Format 2: "FirstName LastName" or "FirstName MiddleName LastName"
  const nameParts = cleanName.split(' ').filter(part => part.length > 0);

  if (nameParts.length === 0) {
    console.log('OCR: No name parts found');
    return { firstName: '', lastName: '' };
  } else if (nameParts.length === 1) {
    // Single name - put in first name
    firstName = nameParts[0];
    console.log('OCR: Single name detected:', firstName);
  } else if (nameParts.length === 2) {
    // Two parts - first and last name
    firstName = nameParts[0];
    lastName = nameParts[1];
    console.log('OCR: Two-part name - First:', firstName, 'Last:', lastName);
  } else {
    // Three or more parts - first name + middle name(s) + last name
    firstName = nameParts[0];
    lastName = nameParts.slice(-1)[0]; // Last part
    // Include middle names with first name for now
    if (nameParts.length > 2) {
      const middleNames = nameParts.slice(1, -1);
      firstName = `${firstName} ${middleNames.join(' ')}`;
    }
    console.log('OCR: Multi-part name - First (with middle):', firstName, 'Last:', lastName);
  }

  return { firstName, lastName };
};

// Parse business card text to extract structured data
const parseBusinessCardText = (text: string) => {
  console.log('OCR: Starting parseBusinessCardText with full text:', text);
  console.log('OCR: Text lines:', text.split('\n'));

  // Reset extracted data
  Object.keys(extractedData.value).forEach(key => {
    extractedData.value[key] = '';
    isDirty.value[key] = false;
    isTouched.value[key] = false;
  });

  const lines = text.split('\n').filter(line => line.trim() !== '');
  console.log('OCR: Filtered lines:', lines);

  // Enhanced parsing logic with better name recognition
  if (lines.length > 0) {
    // First line is often the name
    const nameLine = lines[0].trim();
    console.log('OCR: Raw name line from OCR:', nameLine);

    // Parse the name with enhanced logic
    const parsedName = parseNameFromText(nameLine);
    console.log('OCR: Parsed name result:', parsedName);

    extractedData.value.first_name = parsedName.firstName;
    extractedData.value.last_name = parsedName.lastName;
  }

  // Second line is often job title or company
  if (lines.length > 1) {
    extractedData.value.job_title = lines[1].trim();
  }

  // Third line might be company if second was job title
  if (lines.length > 2) {
    extractedData.value.company = lines[2].trim();
  }

  // Look for email
  const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/;
  const emailMatch = text.match(emailRegex);
  if (emailMatch) {
    extractedData.value.email = emailMatch[0];
  }

  // Look for phone number
  const phoneRegex = /(?:\+\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/;
  const phoneMatch = text.match(phoneRegex);
  if (phoneMatch) {
    extractedData.value.phone = phoneMatch[0];
  }

  // Look for website
  const websiteRegex =
    /\b(?:https?:\/\/)?(?:www\.)?[A-Za-z0-9-]+\.[A-Za-z]{2,}(?:\.[A-Za-z]{2,})?(?:\/[^\s]*)?/i;
  const websiteMatch = text.match(websiteRegex);
  if (websiteMatch) {
    extractedData.value.website = websiteMatch[0];
  }

  // Try to find address (this is more complex and might need improvement)
  // Look for lines with postal codes or that contain words like "street", "avenue", etc.
  const addressIndicators = [
    'street',
    'avenue',
    'ave',
    'st',
    'road',
    'rd',
    'boulevard',
    'blvd',
    'lane',
    'ln',
    'drive',
    'dr',
  ];
  const postalCodeRegex = /\b\d{5}(?:-\d{4})?\b/; // US format
  const stateRegex = /\b[A-Z]{2}\b/; // Two uppercase letters for state

  for (let i = 3; i < lines.length; i++) {
    const line = lines[i];
    const lineLower = line.toLowerCase();
    if (
      postalCodeRegex.test(line) ||
      addressIndicators.some(indicator => lineLower.includes(indicator))
    ) {
      // Take this line and possibly the next one as the address
      const fullAddress = lines.slice(i, i + 2).join(', ');
      extractedData.value.address = fullAddress;

      // Try to parse the address into components
      // Look for pattern: "Street, City, State Zip"
      const addressPattern = /^(.+?),\s*([^,]+),\s*([A-Z]{2})\s*(\d{5}(?:-\d{4})?)/;
      const match = fullAddress.match(addressPattern);

      if (match) {
        extractedData.value.address = match[1].trim(); // Street address
        extractedData.value.city = match[2].trim();
        extractedData.value.state = match[3].trim();
        extractedData.value.postal_code = match[4].trim();
      } else {
        // Try alternative patterns
        // Pattern for "Street City State Zip" without commas
        const altPattern = /^(.+?)\s+([A-Za-z\s]+)\s+([A-Z]{2})\s+(\d{5}(?:-\d{4})?)/;
        const altMatch = fullAddress.match(altPattern);

        if (altMatch) {
          // Need to be more careful here to separate street from city
          const parts = fullAddress.split(/\s+/);
          const zipIndex = parts.findIndex(p => postalCodeRegex.test(p));
          const stateIndex = parts.findIndex((p, idx) => stateRegex.test(p) && idx < zipIndex);

          if (zipIndex > -1 && stateIndex > -1) {
            extractedData.value.postal_code = parts[zipIndex];
            extractedData.value.state = parts[stateIndex];

            // Everything before state is street and city
            const beforeState = parts.slice(0, stateIndex).join(' ');
            // Try to identify where street ends and city begins
            // Look for common street indicators
            let streetEndIndex = -1;
            const streetWords = beforeState.split(' ');
            for (let j = 0; j < streetWords.length; j++) {
              if (addressIndicators.some(ind => streetWords[j].toLowerCase().includes(ind))) {
                streetEndIndex = j;
                break;
              }
            }

            if (streetEndIndex > -1) {
              extractedData.value.address = streetWords.slice(0, streetEndIndex + 1).join(' ');
              extractedData.value.city = streetWords.slice(streetEndIndex + 1).join(' ');
            } else {
              // If we can't find a clear division, put it all in address
              extractedData.value.address = beforeState;
            }
          }
        }
      }

      // Clean up any extra text (like "website" etc) from the parsed fields
      if (extractedData.value.postal_code) {
        // Remove anything after the zip code
        const zipMatch = extractedData.value.postal_code.match(/^\d{5}(?:-\d{4})?/);
        if (zipMatch) {
          extractedData.value.postal_code = zipMatch[0];
        }
      }

      break;
    }
  }

  // Mark all fields as dirty since they were populated by OCR
  Object.keys(extractedData.value).forEach(key => {
    if (extractedData.value[key]) {
      isDirty.value[key] = true;
      validateField(key, extractedData.value[key]);
    }
  });

  // Emit validation event
  emitFormValidation();
};

// Format confidence as percentage
const formattedConfidence = computed(() => {
  return `${Math.round(confidence.value * 100)}%`;
});

// Retry OCR processing
const retryProcessing = () => {
  if (props.image) {
    startOcrProcessing();
  }
};

// Change OCR language
const changeLanguage = language => {
  selectedLanguages.value = [language];
  if (props.image) {
    startOcrProcessing();
  }
};

// Handle input change
const handleInputChange = (field: string, value: any) => {
  extractedData.value[field] = value;
  isDirty.value[field] = true;

  if (props.validateOnChange) {
    validateField(field, value);
    emitFormValidation();
  }
};

// Handle input blur
const handleInputBlur = (field: string) => {
  isTouched.value[field] = true;

  if (props.validateOnBlur) {
    validateField(field, extractedData.value[field]);
    emitFormValidation();
  }
};

// Emit form validation event
const emitFormValidation = () => {
  const isFormValid = validateForm();
  emit('validation', {
    valid: isFormValid,
    errors: formState.errors,
    data: extractedData.value,
  });
  return isFormValid;
};

// Watch for image changes after all functions are defined
watch(
  () => props.image,
  newImage => {
    if (newImage) {
      startOcrProcessing();
    }
  },
  { immediate: true }
);

// Check if a field has an error
const hasError = (field: string) => {
  return (
    formState.fields[field] &&
    !formState.fields[field].valid &&
    (isDirty.value[field] || isTouched.value[field])
  );
};

// Get error message for a field
const getErrorMessage = (field: string) => {
  return formState.fields[field] ? formState.fields[field].error : '';
};

// Check if a field is valid
const isFieldValid = (field: string) => {
  return (
    formState.fields[field] &&
    formState.fields[field].valid &&
    (isDirty.value[field] || isTouched.value[field])
  );
};
</script>

<template>
  <div class="w-full">
    <!-- Processing indicator -->
    <div v-if="isProcessing" class="mb-6">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm font-medium">Processing business card...</span>
        <span class="text-sm">{{ Math.round(progress * 100) }}%</span>
      </div>
      <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
        <div
          class="bg-primarys h-2.5 rounded-full transition-all duration-300"
          :style="{ width: `${progress * 100}%` }"
        ></div>
      </div>
    </div>

    <!-- Results section -->
    <div v-if="extractedText && !isProcessing" class="mt-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-base font-medium">Extracted Information</h3>
        <div class="flex items-center">
          <!-- OCR Engine Status Badge -->
          <div class="flex items-center mr-3">
            <span 
              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
              :class="{
                'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': usedEngine === 'google-vision',
                'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200': usedEngine === 'tesseract' || usedEngine === 'tesseract-fallback',
                'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200': !usedEngine
              }"
            >
              <Icon 
                :name="usedEngine === 'google-vision' ? 'mdi:google' : 'mdi:eye'"
                class="w-3 h-3 mr-1"
              />
              {{ usedEngine === 'google-vision' ? 'Google Vision' : usedEngine === 'tesseract-fallback' ? 'Tesseract (Fallback)' : 'Tesseract' }}
            </span>
          </div>
          <span class="text-sm mr-2">Confidence: {{ formattedConfidence }}</span>
          <button
            class="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700"
            title="Retry OCR"
            @click="retryProcessing"
          >
            <Icon name="mdi:refresh" class="text-lg" />
          </button>
          <button
            class="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 ml-2"
            :title="showRawText ? 'Show structured data' : 'Show raw text'"
            @click="showRawText = !showRawText"
          >
            <Icon :name="showRawText ? 'mdi:format-list-bulleted' : 'mdi:text'" class="text-lg" />
          </button>
        </div>
      </div>

      <!-- Raw text view -->
      <div v-if="showRawText" class="theme_200 p-4 rounded-lg mb-4">
        <pre class="whitespace-pre-wrap text-sm">{{ extractedText }}</pre>
      </div>

      <!-- Structured data view -->
      <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div v-for="(value, key) in extractedData" :key="key" class="theme_200 p-4 rounded-lg">
          <label class="block text-sm font-medium mb-1 capitalize">
            {{ key.replace('_', ' ') }}
            <span v-if="key === 'first_name' || key === 'last_name'" class="text-red-500">*</span>
          </label>
          <div class="relative">
            <input
              v-model="extractedData[key]"
              type="text"
              class="o_input w-full"
              :class="[
                hasError(key) ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : '',
                isFieldValid(key)
                  ? 'border-green-500 focus:border-green-500 focus:ring-green-500'
                  : '',
              ]"
              :placeholder="`Enter ${key.replace('_', ' ')}`"
              @input="handleInputChange(key, $event.target.value)"
              @blur="handleInputBlur(key)"
            />

            <!-- Validation state icons -->
            <div
              v-if="isDirty[key] || isTouched[key]"
              class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none"
            >
              <Icon v-if="hasError(key)" name="mdi:alert-circle" class="h-5 w-5 text-red-500" />
              <Icon
                v-else-if="extractedData[key]"
                name="mdi:check-circle"
                class="h-5 w-5 text-green-500"
              />
            </div>
          </div>

          <!-- Error message -->
          <div v-if="hasError(key)" class="mt-1 text-sm text-red-600">
            {{ getErrorMessage(key) }}
          </div>
        </div>
      </div>

      <!-- Language selection -->
      <div class="mt-4">
        <label class="block text-sm font-medium mb-2">OCR Language</label>
        <select
          v-model="selectedLanguages[0]"
          class="o_input w-full md:w-auto"
          @change="changeLanguage(selectedLanguages[0])"
        >
          <option v-for="language in supportedLanguages" :key="language.value" :value="language">
            {{ language.label }}
          </option>
        </select>
      </div>
    </div>
  </div>
</template>
