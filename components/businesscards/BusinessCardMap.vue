<template>
  <div class="business-card-map">
    <!-- Map Container -->
    <ClientOnly>
      <div 
        ref="mapContainer" 
        class="map-container"
        :style="{ height: mapHeight }"
      ></div>

    <!-- Map Controls -->
    <div class="absolute top-4 right-4 z-10 space-y-2">
      <!-- Toggle View Button -->
      <button
        @click="toggleMapStyle"
        class="bg-white p-2 rounded-lg shadow-lg hover:shadow-xl transition-shadow"
        title="Toggle map style"
      >
        <Icon :name="mapStyle === 'satellite' ? 'mdi:map' : 'mdi:satellite'" class="w-5 h-5 text-gray-700" />
      </button>

      <!-- Center on User Button -->
      <button
        v-if="userLocation"
        @click="centerOnUser"
        class="bg-white p-2 rounded-lg shadow-lg hover:shadow-xl transition-shadow"
        title="Center on my location"
      >
        <Icon name="mdi:crosshairs-gps" class="w-5 h-5 text-blue-600" />
      </button>

      <!-- Cluster Toggle -->
      <button
        @click="toggleClustering"
        class="bg-white p-2 rounded-lg shadow-lg hover:shadow-xl transition-shadow"
        :title="clusteringEnabled ? 'Disable clustering' : 'Enable clustering'"
      >
        <Icon 
          :name="clusteringEnabled ? 'mdi:ungroup' : 'mdi:group'" 
          class="w-5 h-5"
          :class="clusteringEnabled ? 'text-orange-600' : 'text-gray-700'"
        />
      </button>
    </div>

    <!-- Map Legend -->
    <div class="absolute bottom-4 left-4 z-10 bg-white p-3 rounded-lg shadow-lg">
      <h4 class="text-sm font-semibold text-gray-900 mb-2">Legend</h4>
      <div class="space-y-1 text-xs">
        <div class="flex items-center">
          <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
          <span>Business Cards</span>
        </div>
        <div class="flex items-center">
          <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
          <span>Your Location</span>
        </div>
        <div class="flex items-center">
          <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
          <span>Search Center</span>
        </div>
      </div>
    </div>

    <!-- Loading Overlay -->
    <div v-if="isLoading" class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-20">
      <div class="text-center">
        <Icon name="mdi:loading" class="w-8 h-8 animate-spin text-blue-600 mx-auto mb-2" />
        <p class="text-sm text-gray-600">Loading map...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-if="error" class="absolute inset-0 bg-red-50 flex items-center justify-center z-20">
      <div class="text-center p-6">
        <Icon name="mdi:alert-circle" class="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h3 class="text-lg font-semibold text-red-800 mb-2">Map Loading Error</h3>
        <p class="text-red-600 mb-4">{{ error }}</p>
        <button
          @click="initializeMap"
          class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
        >
          Retry
        </button>
      </div>
    </div>

    <!-- Business Card Info Panel -->
    <div
      v-if="selectedCard"
      class="absolute bottom-20 left-4 right-4 bg-white rounded-lg shadow-lg p-4 z-20 max-w-sm"
    >
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <h3 class="font-semibold text-gray-900">{{ selectedCard.name }}</h3>
          <p class="text-sm text-gray-600">{{ selectedCard.company }}</p>
          <div class="flex items-center mt-2 text-xs text-gray-500">
            <Icon name="mdi:map-marker" class="w-3 h-3 mr-1" />
            <span v-if="selectedCard.distance">{{ formatDistance(selectedCard.distance) }} away</span>
            <span v-else>{{ selectedCard.address?.city }}, {{ selectedCard.address?.state }}</span>
          </div>
        </div>
        <button
          @click="selectedCard = null"
          class="text-gray-400 hover:text-gray-600"
        >
          <Icon name="mdi:close" class="w-5 h-5" />
        </button>
      </div>
      <div class="flex space-x-2 mt-3">
        <button
          @click="viewCard(selectedCard)"
          class="flex-1 px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
        >
          View Details
        </button>
        <button
          @click="getDirections(selectedCard)"
          class="flex-1 px-3 py-1 border border-gray-300 text-gray-700 text-sm rounded-md hover:bg-gray-50"
        >
          Directions
        </button>
      </div>
    </div>
    </ClientOnly>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import type { BusinessCardLocation, LocationData } from '~/composables/useGeographicFiltering'

// Leaflet type imports (safe for SSR)
import type * as L from 'leaflet'

// Dynamic imports for Leaflet (client-side only)
let Leaflet: typeof L | null = null

interface Props {
  businessCards: BusinessCardLocation[]
  userLocation?: LocationData | null
  searchCenter?: { lat: number; lng: number } | null
  searchRadius?: number
  height?: string
}

const props = withDefaults(defineProps<Props>(), {
  businessCards: () => [],
  userLocation: null,
  searchCenter: null,
  searchRadius: 0,
  height: '400px'
})

const emit = defineEmits(['card-selected', 'bounds-changed'])

// Refs
const mapContainer = ref<HTMLDivElement>()
const map = ref<L.Map | null>(null)
const markers = ref<L.Marker[]>([])
const markerClusterGroup = ref<any>(null)
const userMarker = ref<L.Marker | null>(null)
const searchMarker = ref<L.Marker | null>(null)
const searchCircle = ref<L.Circle | null>(null)

// State
const isLoading = ref(true)
const error = ref<string | null>(null)
const selectedCard = ref<BusinessCardLocation | null>(null)
const mapStyle = ref<'standard' | 'satellite'>('standard')
const clusteringEnabled = ref(true)

// Computed
const mapHeight = computed(() => props.height)

// Icons (will be created after Leaflet loads)
let businessCardIcon: L.DivIcon | null = null
let userLocationIcon: L.DivIcon | null = null
let searchCenterIcon: L.DivIcon | null = null

// Load Leaflet dynamically (client-side only)
const loadLeaflet = async () => {
  if (!process.client || Leaflet) return
  
  try {
    // Dynamic imports
    Leaflet = await import('leaflet')
    await import('leaflet/dist/leaflet.css')
    await import('leaflet.markercluster')
    await import('leaflet.markercluster/dist/MarkerCluster.css')
    await import('leaflet.markercluster/dist/MarkerCluster.Default.css')
    
    // Create icons after Leaflet loads
    createIcons()
  } catch (err) {
    console.error('Failed to load Leaflet:', err)
    throw err
  }
}

// Create custom icons
const createIcons = () => {
  if (!Leaflet) return
  
  const createIcon = (color: string, icon: string) => {
    return Leaflet!.divIcon({
      html: `
        <div style="
          background-color: ${color};
          width: 30px;
          height: 30px;
          border-radius: 50%;
          border: 3px solid white;
          box-shadow: 0 2px 4px rgba(0,0,0,0.3);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 14px;
        ">
          <i class="mdi ${icon}"></i>
        </div>
      `,
      className: 'custom-marker',
      iconSize: [30, 30],
      iconAnchor: [15, 15]
    })
  }
  
  businessCardIcon = createIcon('#3b82f6', 'mdi-card-account-details')
  userLocationIcon = createIcon('#10b981', 'mdi-crosshairs-gps')
  searchCenterIcon = createIcon('#ef4444', 'mdi-map-marker')
}

// Initialize map
const initializeMap = async () => {
  try {
    isLoading.value = true
    error.value = null

    // Load Leaflet first
    await loadLeaflet()
    
    if (!Leaflet || !mapContainer.value) {
      throw new Error('Map container or Leaflet not available')
    }

    // Create map
    map.value = Leaflet.map(mapContainer.value, {
      zoomControl: false
    })

    // Add zoom control to bottom right
    Leaflet.control.zoom({ position: 'bottomright' }).addTo(map.value)

    // Set initial view
    const initialCenter = props.userLocation 
      ? [props.userLocation.lat, props.userLocation.lng]
      : props.searchCenter
      ? [props.searchCenter.lat, props.searchCenter.lng]
      : [37.7749, -122.4194] // Default to San Francisco

    map.value.setView(initialCenter as [number, number], 10)

    // Add tile layer
    updateMapStyle()

    // Initialize marker cluster group
    markerClusterGroup.value = (Leaflet as any).markerClusterGroup({
      maxClusterRadius: 50,
      spiderfyOnMaxZoom: true,
      showCoverageOnHover: false
    })

    if (clusteringEnabled.value) {
      map.value.addLayer(markerClusterGroup.value)
    }

    // Add markers
    updateMarkers()
    updateUserLocationMarker()
    updateSearchMarkers()

    // Listen for map events
    map.value.on('moveend', onMapMoveEnd)

    isLoading.value = false
  } catch (err) {
    console.error('Failed to initialize map:', err)
    error.value = err instanceof Error ? err.message : 'Failed to load map'
    isLoading.value = false
  }
}

// Update map style
const updateMapStyle = () => {
  if (!map.value || !Leaflet) return

  // Remove existing tile layers
  map.value.eachLayer(layer => {
    if (layer instanceof Leaflet.TileLayer) {
      map.value?.removeLayer(layer)
    }
  })

  // Add new tile layer based on style
  if (mapStyle.value === 'satellite') {
    Leaflet.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
      attribution: '&copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community'
    }).addTo(map.value)
  } else {
    Leaflet.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map.value)
  }
}

// Update business card markers
const updateMarkers = () => {
  if (!map.value || !Leaflet || !businessCardIcon) return

  // Clear existing markers
  if (clusteringEnabled.value) {
    markerClusterGroup.value?.clearLayers()
  } else {
    markers.value.forEach(marker => map.value?.removeLayer(marker))
  }
  markers.value = []

  // Add new markers
  props.businessCards.forEach(card => {
    const marker = Leaflet.marker([card.lat, card.lng], {
      icon: businessCardIcon
    })

    marker.bindPopup(`
      <div class="p-2">
        <h3 class="font-semibold">${card.name}</h3>
        <p class="text-sm text-gray-600">${card.company}</p>
        ${card.distance ? `<p class="text-xs text-gray-500">${formatDistance(card.distance)} away</p>` : ''}
      </div>
    `)

    marker.on('click', () => {
      selectedCard.value = card
      emit('card-selected', card)
    })

    markers.value.push(marker)

    if (clusteringEnabled.value) {
      markerClusterGroup.value?.addLayer(marker)
    } else {
      marker.addTo(map.value!)
    }
  })
}

// Update user location marker
const updateUserLocationMarker = () => {
  if (!map.value || !Leaflet || !userLocationIcon) return

  if (userMarker.value) {
    map.value.removeLayer(userMarker.value)
  }

  if (props.userLocation) {
    userMarker.value = Leaflet.marker([props.userLocation.lat, props.userLocation.lng], {
      icon: userLocationIcon
    }).addTo(map.value)

    userMarker.value.bindPopup('Your Location')
  }
}

// Update search center and radius markers
const updateSearchMarkers = () => {
  if (!map.value || !Leaflet || !searchCenterIcon) return

  // Remove existing search markers
  if (searchMarker.value) {
    map.value.removeLayer(searchMarker.value)
  }
  if (searchCircle.value) {
    map.value.removeLayer(searchCircle.value)
  }

  if (props.searchCenter) {
    // Add search center marker
    searchMarker.value = Leaflet.marker([props.searchCenter.lat, props.searchCenter.lng], {
      icon: searchCenterIcon
    }).addTo(map.value)

    searchMarker.value.bindPopup('Search Center')

    // Add search radius circle
    if (props.searchRadius && props.searchRadius > 0) {
      searchCircle.value = Leaflet.circle([props.searchCenter.lat, props.searchCenter.lng], {
        radius: props.searchRadius * 1000, // Convert km to meters
        fillColor: '#ef4444',
        fillOpacity: 0.1,
        color: '#ef4444',
        weight: 2
      }).addTo(map.value)
    }
  }
}

// Event handlers
const onMapMoveEnd = () => {
  if (!map.value) return

  const bounds = map.value.getBounds()
  emit('bounds-changed', {
    north: bounds.getNorth(),
    south: bounds.getSouth(),
    east: bounds.getEast(),
    west: bounds.getWest()
  })
}

const toggleMapStyle = () => {
  mapStyle.value = mapStyle.value === 'standard' ? 'satellite' : 'standard'
  updateMapStyle()
}

const centerOnUser = () => {
  if (props.userLocation && map.value) {
    map.value.setView([props.userLocation.lat, props.userLocation.lng], 14)
  }
}

const toggleClustering = () => {
  clusteringEnabled.value = !clusteringEnabled.value
  
  if (clusteringEnabled.value) {
    map.value?.addLayer(markerClusterGroup.value)
    markers.value.forEach(marker => {
      map.value?.removeLayer(marker)
      markerClusterGroup.value?.addLayer(marker)
    })
  } else {
    map.value?.removeLayer(markerClusterGroup.value)
    markers.value.forEach(marker => {
      markerClusterGroup.value?.removeLayer(marker)
      marker.addTo(map.value!)
    })
  }
}

const viewCard = (card: BusinessCardLocation) => {
  // Navigate to card details page
  navigateTo(`/c/businesscards/${card.id}`)
}

const getDirections = (card: BusinessCardLocation) => {
  // Open directions in external maps app
  const url = `https://maps.google.com/maps?daddr=${card.lat},${card.lng}`
  window.open(url, '_blank')
}

const formatDistance = (distanceKm: number): string => {
  if (distanceKm < 1) {
    return `${Math.round(distanceKm * 1000)}m`
  } else if (distanceKm < 10) {
    return `${distanceKm.toFixed(1)}km`
  } else {
    return `${Math.round(distanceKm)}km`
  }
}

// Watchers
watch(() => props.businessCards, updateMarkers, { deep: true })
watch(() => props.userLocation, updateUserLocationMarker, { deep: true })
watch(() => [props.searchCenter, props.searchRadius], updateSearchMarkers, { deep: true })

// Lifecycle
onMounted(async () => {
  await nextTick()
  initializeMap()
})

onUnmounted(() => {
  if (map.value) {
    map.value.remove()
  }
})
</script>

<style scoped>
.business-card-map {
  position: relative;
  width: 100%;
}

.map-container {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

/* Override Leaflet's default styles */
:deep(.leaflet-popup-content-wrapper) {
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

:deep(.leaflet-popup-tip) {
  background: white;
}

:deep(.custom-marker) {
  background: transparent;
  border: none;
}

:deep(.marker-cluster) {
  background: rgba(59, 130, 246, 0.8);
  border: 3px solid white;
  border-radius: 50%;
  color: white;
  font-weight: bold;
  text-align: center;
  font-size: 12px;
}

:deep(.marker-cluster-small) {
  width: 40px;
  height: 40px;
  line-height: 34px;
}

:deep(.marker-cluster-medium) {
  width: 50px;
  height: 50px;
  line-height: 44px;
}

:deep(.marker-cluster-large) {
  width: 60px;
  height: 60px;
  line-height: 54px;
}
</style>