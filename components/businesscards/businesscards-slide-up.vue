<script setup lang="ts">
let slideUpProps = defineProps({
  schema: {
    type: Object,
    default: () => {},
  },
});

const image = computed(() => {
  if(slideUpProps.schema) {
    if(slideUpProps.schema.images) {
      if(slideUpProps.schema.images.length > 0) {
        return slideUpProps.schema.images[0].src;
      } else {
        return '';
      }
    } else if(slideUpProps.schema.image) {
      if(slideUpProps.schema.image.src) {
        return slideUpProps.schema.image.src;
      } else {
        return slideUpProps.schema.image;
      }
    } else {
      return '';
    }
  }
});

const formatDate = (date) => {
  if (!date) return 'Unknown'
  
  const dateObj = date.toDate ? date.toDate() : new Date(date)
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(dateObj)
}
</script>

<template>
  <div class="business-card-container">
    <div class="business-card">
      <!-- Front of Card -->
      <div class="business-card-front">
        <!-- Gradient background -->
        <div class="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 rounded-xl"></div>
        
        <!-- Content -->
        <div class="relative z-10 p-6 h-full flex flex-col">
          <!-- Header -->
          <div class="flex items-start justify-between mb-4">
            <div class="flex-1">
              <h3 class="text-lg font-bold text-white mb-1 leading-tight">
                {{ schema.name || `${schema.first_name || ''} ${schema.last_name || ''}`.trim() || 'Unknown' }}
              </h3>
              <p v-if="schema.position" class="text-sm text-blue-300 font-medium">
                {{ schema.position }}
              </p>
              <p v-if="schema.company" class="text-xs text-gray-300 mt-1">
                {{ schema.company }}
              </p>
            </div>
            
            <!-- Status indicator -->
            <div class="flex items-center space-x-1 text-xs px-2 py-1 rounded-full border"
                 :class="{
                   'bg-green-500/20 border-green-400/30 text-green-300': schema.status === 'Approved',
                   'bg-yellow-500/20 border-yellow-400/30 text-yellow-300': schema.status === 'Pending',
                   'bg-red-500/20 border-red-400/30 text-red-300': schema.status === 'Declined',
                   'bg-blue-500/20 border-blue-400/30 text-blue-300': !schema.status
                 }">
              <Icon name="mdi:wifi" class="w-3 h-3" />
              <span>{{ schema.status || 'Active' }}</span>
            </div>
          </div>

          <!-- Contact Info -->
          <div class="space-y-2 flex-grow">
            <div v-if="schema.email" class="flex items-center space-x-2 text-gray-300">
              <Icon name="mdi:email" class="w-4 h-4 text-blue-400 flex-shrink-0" />
              <span class="text-sm truncate">{{ schema.email }}</span>
            </div>
            
            <div v-if="schema.phone" class="flex items-center space-x-2 text-gray-300">
              <Icon name="mdi:phone" class="w-4 h-4 text-green-400 flex-shrink-0" />
              <span class="text-sm">{{ schema.phone }}</span>
            </div>
            
            <div v-if="schema.website" class="flex items-center space-x-2 text-gray-300">
              <Icon name="mdi:web" class="w-4 h-4 text-purple-400 flex-shrink-0" />
              <span class="text-sm truncate">{{ schema.website }}</span>
            </div>
          </div>

          <!-- Footer -->
          <div class="mt-4 pt-4 border-t border-gray-700">
            <div class="flex items-center justify-between text-xs mb-3">
              <span class="text-gray-400">{{ formatDate(schema.created_at) }}</span>
              <div v-if="schema.tags && Array.isArray(schema.tags) && schema.tags.length > 0" class="flex flex-wrap gap-1">
                <span 
                  v-for="tag in schema.tags.slice(0, 2)" 
                  :key="tag"
                  class="px-2 py-1 bg-gray-700/50 text-gray-300 rounded-full text-xs"
                >
                  {{ tag }}
                </span>
                <span v-if="schema.tags.length > 2" class="text-gray-400">...</span>
              </div>
            </div>
            
            <!-- Social Share Button -->
            <div class="flex justify-center">
              <SharedSocialShareButton
                :card-id="schema.id || schema._id || ''"
                :card-name="schema.name || `${schema.first_name || ''} ${schema.last_name || ''}`.trim() || 'Unknown'"
                :card-company="schema.company || ''"
                :card-email="schema.email || ''"
                share-button-text="Share"
              />
            </div>
          </div>

          <!-- Decorative elements -->
          <div class="absolute top-4 right-4 w-12 h-12 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-sm"></div>
          <div class="absolute bottom-4 left-4 w-8 h-8 bg-gradient-to-br from-green-400/20 to-blue-400/20 rounded-full blur-sm"></div>
        </div>
      </div>

      <!-- Back of Card (Business Card Image) -->
      <div class="business-card-back">
        <div v-if="image" class="w-full h-full relative">
          <img
            :src="image"
            :alt="`Business card for ${schema.name}`"
            class="w-full h-full object-cover rounded-xl"
            @load="console.log('Image loaded successfully:', image)"
            @error="console.log('Image failed to load:', $event.target.src)"
          />
          <!-- Overlay for better text readability -->
          <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent rounded-xl flex items-end p-4">
            <div class="text-white">
              <p class="text-xs opacity-90 font-medium">Original Business Card</p>
            </div>
          </div>
        </div>
        
        <!-- Fallback when no image -->
        <div v-else class="w-full h-full bg-gradient-to-br from-gray-700 via-gray-800 to-gray-900 rounded-xl flex items-center justify-center">
          <div class="text-center text-gray-400">
            <Icon name="mdi:card-account-details" class="w-20 h-20 mb-3 mx-auto opacity-50" />
            <p class="text-sm font-medium">No Business Card Image</p>
            <p class="text-xs opacity-70 mt-1">Upload an image to see it here</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Business Card Container */
.business-card-container {
  perspective: 1000px;
  height: 280px;
  cursor: pointer;
}

/* Business Card */
.business-card {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-style: preserve-3d;
}

/* Hover Effect - Flip Animation */
.business-card-container:hover .business-card {
  transform: rotateY(180deg);
}

/* Front and Back Face Base Styles */
.business-card-front,
.business-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 
    0 10px 25px rgba(0, 0, 0, 0.3),
    0 20px 48px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Front Face */
.business-card-front {
  transform: rotateY(0deg);
  background: linear-gradient(135deg, #1f2937 0%, #111827 50%, #0f172a 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Back Face */
.business-card-back {
  transform: rotateY(180deg);
  background: #000;
}

/* Enhanced Hover Effects */
.business-card-container:hover .business-card-front {
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 30px 60px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.business-card-container:hover .business-card-back {
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 30px 60px rgba(0, 0, 0, 0.3);
}

/* Subtle Animations */
.business-card-front::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.6s ease;
}

.business-card-container:hover .business-card-front::before {
  left: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
  .business-card-container {
    height: 260px;
  }
}

@media (max-width: 640px) {
  .business-card-container {
    height: 240px;
  }
  
  .business-card-front .relative.z-10 {
    padding: 1.25rem;
  }
}

/* Additional Polish */
.business-card img {
  transition: transform 0.3s ease;
}

.business-card-container:hover img {
  transform: scale(1.02);
}

/* Subtle Glow Effect */
.business-card-container {
  filter: drop-shadow(0 0 0 rgba(59, 130, 246, 0));
  transition: filter 0.3s ease;
}

.business-card-container:hover {
  filter: drop-shadow(0 0 20px rgba(59, 130, 246, 0.3));
}
</style>
