<script setup lang="ts">
import moment from "moment";
const formsItem = ref({
  title: "Business Cards",
  filters: {
    index: "omni",
    collection: "businesscards",
    queryBy: "",
    route_add: "/c/businesscards-create",

    description: "Businesscards",
    areas: ["Business Card Information"],
    filterBy:
      "Touched Records, Untouched Records, Record Action, Related Records Action",
  },
  data: [
    {
      label: "id",
      val: "id",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Created At",
      val: "created_at",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Updated at",
      val: "updated_at",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "User ownership",
      val: "user_own",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Users with access",
      val: "user_access",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Space ownership",
      val: "space_own",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Space with access",
      val: "space_access",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Name",
      val: "name",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
    {
      label: "Tags (comma seperated)",
      val: "tags",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsTags",
      action: "forms-actions-tags",
    },
    {
      label: "Description",
      val: "description",
      value: "",
      class: "col-span-2",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsQuill",
      action: "forms-actions-quill",
    },
    {
      label: "Business Card",
      val: "image",
      value: "",
      class: "col-span-2",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "Image",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      component: "FormsInputsUpload",
      action: "forms-actions-docs",
    },
    {
      label: "Status",
      val: "status",
      value: "Pending",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: true,
      small_table: false,
      small_form: false,
      editable: false,
      card: false,
      card_type: "Image",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      component: "FormsInputsSelect",
      action: "forms-actions-select",
      options: ["Approved", "Pending", "Declined"],
    },
    {
      label: "First Name",
      val: "first_name",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
    {
      label: "Last Name",
      val: "last_name",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
    {
      label: "Email",
      val: "email",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },

    {
      label: "Website",
      val: "website",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: false,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
    {
      label: "Phone",
      val: "phone",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
  ],
});

const info = ref({
  title: "Businesscards",
});

const selectedBusinesscards = useState("selectedBusinesscards", () => {
  return {};
});

const selected = (item: any) => {
  selectedBusinesscards.value = item;
  useRouter().push("/c/businesscards-individual");
};
const edited = (item: any) => {
  selectedBusinesscards.value = item;
  useRouter().push("/c/businesscards/edit");
};
const extraInfo: any = ref({
  view: false,
  edit: false,
  delete: false,
  add: false,
  export: false,
  import: false,
  ai: false,
});

onMounted(() => {
  let res = getAccess("CRM", "Leads");
  if (res) {
    extraInfo.value = res;
  }
});
const filter = ref({});
const cards = ref([
  {
    id: 15,
    country: "South Africa",
    city: "Johannesburg",
    category: "Computer, Electronics & Software",
    subcategory: "Programming",
    name: "Business",
    email: "<EMAIL>",
    website: "www.business.company",
    phone: "0832848385",
    first_name: "Myth",
    last_name: "Citizen",
    status: "Approved",
    shares: 0,
    paymenttype: "free",
    expiry: "",
    editLink:
      "https://covalonic.com/upload/South Africa/Johannesburg/Computer, Electronics & Software/Programming/edit/card/15",
  },
  {
    id: 26,
    country: "South Africa",
    city: "Roodepoort",
    category: "Computer, Electronics & Software",
    subcategory: "E-Commerce & Internet Business",
    name: "JHB Digital",
    email: "<EMAIL>",
    website: "www.jhbdigital.co.za",
    phone: "0763752131",
    first_name: "Justin",
    last_name: "JHB",
    status: "Approved",
    shares: 0,
    paymenttype: "free",
    expiry: "",
    editLink:
      "https://covalonic.com/upload/South Africa/Roodepoort/Computer, Electronics & Software/E-Commerce & Internet Business/edit/card/26",
  },
  {
    id: 38,
    country: "South Africa",
    city: "Pretoria",
    category: "Security & Safety",
    subcategory: "Alarm Systems & CCTV",
    name: "Proeye Technologies",
    email: "<EMAIL>",
    website: "",
    phone: "0676687986",
    first_name: "William ",
    last_name: "van Loggerenberg",
    status: "Approved",
    shares: 0,
    paymenttype: "free",
    expiry: "",
    editLink:
      "https://covalonic.com/upload/South Africa/Pretoria/Security & Safety/Alarm Systems & CCTV/edit/card/38",
  },
  {
    id: 50,
    country: "South Africa",
    city: "Cape Town",
    category: "Repair & Service Tradesman",
    subcategory: "Electrical",
    name: "SolarSun Eco",
    email: "<EMAIL>",
    website: "https://solarsuneco.co.za",
    phone: "0843639103",
    first_name: "Solar ",
    last_name: "Sun",
    status: "Approved",
    shares: 0,
    paymenttype: "free",
    expiry: "",
    editLink:
      "https://covalonic.com/upload/South Africa/Cape Town/Repair & Service Tradesman/Electrical/edit/card/50",
  },
  {
    id: 57,
    country: "South Africa",
    city: "Johannesburg",
    category: "Repair & Service Tradesman",
    subcategory: "Air Conditioning",
    name: "Ac Electrical",
    email: "<EMAIL>",
    website: "",
    phone: "0824461534",
    first_name: "Ac",
    last_name: "Elec",
    status: "Approved",
    shares: 0,
    paymenttype: "free",
    expiry: "",
    editLink:
      "https://covalonic.com/upload/South Africa/Johannesburg/Repair & Service Tradesman/Air Conditioning/edit/card/57",
  },
  {
    id: 58,
    country: "South Africa",
    city: "Boksburg",
    category: "Security & Safety",
    subcategory: "Armed Response",
    name: "Shamco Security",
    email: "<EMAIL>",
    website: "https://shamco.co.za/",
    phone: "0749951170",
    first_name: "Shamco",
    last_name: "Security",
    status: "Approved",
    shares: 0,
    paymenttype: "free",
    expiry: "",
    editLink:
      "https://covalonic.com/upload/South Africa/Boksburg/Security & Safety/Armed Response/edit/card/58",
  },
  {
    id: 62,
    country: "South Africa",
    city: "Pretoria",
    category: "Business & Professional Services",
    subcategory: "Other",
    name: "Rare NotesCoins",
    email: "<EMAIL>",
    website: "",
    phone: "0645820289",
    first_name: "Morne",
    last_name: "Van Dyk",
    status: "Approved",
    shares: 0,
    paymenttype: "free",
    expiry: "",
    editLink:
      "https://covalonic.com/upload/South Africa/Pretoria/Business & Professional Services/Other/edit/card/62",
  },
  {
    id: 63,
    country: "South Africa",
    city: "Pretoria",
    category: "Cars, Bikes & Related",
    subcategory: "Bike Dealerships & Sales",
    name: "JB Motorcycle",
    email: "<EMAIL>",
    website: "www.jbmotorcycles.co.za",
    phone: "0790791158",
    first_name: "Johan",
    last_name: "Botha",
    status: "Approved",
    shares: 0,
    paymenttype: "free",
    expiry: "",
    editLink:
      "https://covalonic.com/upload/South Africa/Pretoria/Cars, Bikes & Related/Bike Dealerships & Sales/edit/card/63",
  },
  {
    id: 64,
    country: "South Africa",
    city: "Pretoria",
    category: "Cars, Bikes & Related",
    subcategory: "Other",
    name: "Cable engineering",
    email: "<EMAIL>",
    website: "",
    phone: "0827740954",
    first_name: "Dolf",
    last_name: "Cable engineering",
    status: "Approved",
    shares: 0,
    paymenttype: "free",
    expiry: "",
    editLink:
      "https://covalonic.com/upload/South Africa/Pretoria/Cars, Bikes & Related/Other/edit/card/64",
  },
  {
    id: 65,
    country: "South Africa",
    city: "Pretoria",
    category: "Cars, Bikes & Related",
    subcategory: "Bike Dealerships & Sales",
    name: "SYM Motorcycles and Scooters",
    email: "<EMAIL>",
    website: "www.optraauto.co.za",
    phone: "**********",
    first_name: "Lizette",
    last_name: "Lombaard",
    status: "Approved",
    shares: 0,
    paymenttype: "free",
    expiry: "",
    editLink:
      "https://covalonic.com/upload/South Africa/Pretoria/Cars, Bikes & Related/Bike Dealerships & Sales/edit/card/65",
  },
  {
    id: 66,
    country: "South Africa",
    city: "Pretoria",
    category: "Healthcare",
    subcategory: "Personal Health Care Products",
    name: "Bederfies",
    email: "<EMAIL>",
    website: "",
    phone: "**********",
    first_name: "Louise",
    last_name: "van Rooyen",
    status: "Approved",
    shares: 0,
    paymenttype: "free",
    expiry: "",
    editLink:
      "https://covalonic.com/upload/South Africa/Pretoria/Healthcare/Personal Health Care Products/edit/card/66",
  },
  {
    id: 67,
    country: "South Africa",
    city: "Pretoria",
    category: "Food Industry",
    subcategory: "Food Manufacturing",
    name: "Flavour City",
    email: "<EMAIL>",
    website: "",
    phone: "**********",
    first_name: "Antoinette",
    last_name: "Venter",
    status: "Approved",
    shares: 0,
    paymenttype: "free",
    expiry: "",
    editLink:
      "https://covalonic.com/upload/South Africa/Pretoria/Food Industry/Food Manufacturing/edit/card/67",
  },
  {
    id: 68,
    country: "South Africa",
    city: "Pretoria",
    category: "Food Industry",
    subcategory: "Food Manufacturing",
    name: "Sanctuary Farm Produce",
    email: "<EMAIL>",
    website: "http://www.sanctuaryfarmproduce.com",
    phone: "**********",
    first_name: "Hannelie",
    last_name: "van Staden",
    status: "Approved",
    shares: 0,
    paymenttype: "free",
    expiry: "",
    editLink:
      "https://covalonic.com/upload/South Africa/Pretoria/Food Industry/Food Manufacturing/edit/card/68",
  },
  {
    id: 69,
    country: "South Africa",
    city: "Pretoria",
    category: "Repair & Service Tradesman",
    subcategory: "Handyman",
    name: "Hout is Goud",
    email: "<EMAIL>",
    website: "",
    phone: "0722579758",
    first_name: "Leoni",
    last_name: "van der Berg",
    status: "Approved",
    shares: 0,
    paymenttype: "free",
    expiry: "",
    editLink:
      "https://covalonic.com/upload/South Africa/Pretoria/Repair & Service Tradesman/Handyman/edit/card/69",
  },
  {
    id: 70,
    country: "South Africa",
    city: "Pretoria",
    category: "Security & Safety",
    subcategory: "Products & Personal Protection Equipment",
    name: "Venter Workwear",
    email: "<EMAIL>",
    website: "",
    phone: "0828242347",
    first_name: "Gert",
    last_name: "Venter",
    status: "Approved",
    shares: 0,
    paymenttype: "free",
    expiry: "",
    editLink:
      "https://covalonic.com/upload/South Africa/Pretoria/Security & Safety/Products & Personal Protection Equipment/edit/card/70",
  },
  {
    id: 71,
    country: "South Africa",
    city: "Pretoria",
    category: "Repair & Service Tradesman",
    subcategory: "Plumbing",
    name: "Doctor Do All",
    email: "<EMAIL>",
    website: "",
    phone: "0799398962",
    first_name: "Tertius",
    last_name: "Venter",
    status: "Approved",
    shares: 0,
    paymenttype: "free",
    expiry: "",
    editLink:
      "https://covalonic.com/upload/South Africa/Pretoria/Repair & Service Tradesman/Plumbing/edit/card/71",
  },
  {
    id: 72,
    country: "South Africa",
    city: "Pretoria",
    category: "Retail & Shopping",
    subcategory: "Camping & Outdoors",
    name: "JAG Sersant",
    email: "<EMAIL>",
    website: "",
    phone: "**********",
    first_name: "Charls",
    last_name: "Jag Sersant",
    status: "Approved",
    shares: 0,
    paymenttype: "free",
    expiry: "",
    editLink:
      "https://covalonic.com/upload/South Africa/Pretoria/Retail & Shopping/Camping & Outdoors/edit/card/72",
  },
  {
    id: 73,
    country: "South Africa",
    city: "Pretoria",
    category: "Healthcare",
    subcategory: "Personal Health Care Products",
    name: "Maridele Soapery",
    email: "<EMAIL>",
    website: "",
    phone: "**********",
    first_name: "Marisa",
    last_name: "Soapery",
    status: "Approved",
    shares: 0,
    paymenttype: "free",
    expiry: "",
    editLink:
      "https://covalonic.com/upload/South Africa/Pretoria/Healthcare/Personal Health Care Products/edit/card/73",
  },
  {
    id: 74,
    country: "South Africa",
    city: "Pretoria",
    category: "Retail & Shopping",
    subcategory: "Sports, Toys & Hobby",
    name: "Bert Knives",
    email: "<EMAIL>",
    website: "",
    phone: "**********",
    first_name: "Bert",
    last_name: "Knives",
    status: "Approved",
    shares: 0,
    paymenttype: "free",
    expiry: "",
    editLink:
      "https://covalonic.com/upload/South Africa/Pretoria/Retail & Shopping/Sports, Toys & Hobby/edit/card/74",
  },
  {
    id: 75,
    country: "South Africa",
    city: "Pretoria",
    category: "Retail & Shopping",
    subcategory: "Other",
    name: "Little Critter Creations",
    email: "<EMAIL>",
    website: "",
    phone: "**********",
    first_name: "Irma",
    last_name: "Beneke",
    status: "Approved",
    shares: 0,
    paymenttype: "free",
    expiry: "",
    editLink:
      "https://covalonic.com/upload/South Africa/Pretoria/Retail & Shopping/Other/edit/card/75",
  },
  {
    id: 77,
    country: "South Africa",
    city: "Pretoria",
    category: "Repair & Service Tradesman",
    subcategory: "Appliance",
    name: "Flowbins",
    email: "<EMAIL>",
    website: "www.flowbins.co.za",
    phone: "0613214467",
    first_name: "Dylen",
    last_name: "Dickinson",
    status: "Approved",
    shares: 0,
    paymenttype: "free",
    expiry: "",
    editLink:
      "https://covalonic.com/upload/South Africa/Pretoria/Repair & Service Tradesman/Appliance/edit/card/77",
  },
  {
    id: 78,
    country: "South Africa",
    city: "Pretoria",
    category: "Agriculture & Mining",
    subcategory: "Farming",
    name: "SPLITEQ",
    email: "<EMAIL>",
    website: "www.spliteq.co.za",
    phone: "0823201645",
    first_name: "Waldi",
    last_name: "Smit",
    status: "Approved",
    shares: 0,
    paymenttype: "free",
    expiry: "",
    editLink:
      "https://covalonic.com/upload/South Africa/Pretoria/Agriculture & Mining/Farming/edit/card/78",
  },
  {
    id: 84,
    country: "South Africa",
    city: "Durban",
    category: "Media & Entertainment",
    subcategory: "Hobbies",
    name: "Diamond Dot Painting",
    email: "<EMAIL>",
    website: "www.diamonddotpainting.co.za",
    phone: "0737197639",
    first_name: "Nicole",
    last_name: "Simons",
    status: "Approved",
    shares: 0,
    paymenttype: "free",
    expiry: "",
    editLink:
      "https://covalonic.com/upload/South Africa/Durban/Media & Entertainment/Hobbies/edit/card/84",
  },
  {
    id: 85,
    country: "South Africa",
    city: "Pretoria",
    category: "Repair & Service Tradesman",
    subcategory: "Plumbing",
    name: "ZinG Co",
    email: "<EMAIL>",
    website: "",
    phone: "0846944144",
    first_name: "Gilbert",
    last_name: "Stewart",
    status: "Approved",
    shares: 0,
    paymenttype: "free",
    expiry: "",
    editLink:
      "https://covalonic.com/upload/South Africa/Pretoria/Repair & Service Tradesman/Plumbing/edit/card/85",
  },
  {
    id: 86,
    country: "South Africa",
    city: "Pretoria",
    category: "Cars, Bikes & Related",
    subcategory: "Car Service & Repairs",
    name: "Syd s Suspension",
    email: "<EMAIL>",
    website: "www.sydssuspensionparts.co.za",
    phone: "0123275700",
    first_name: "Syd",
    last_name: "Suspension",
    status: "Approved",
    shares: 0,
    paymenttype: "free",
    expiry: "",
    editLink:
      "https://covalonic.com/upload/South Africa/Pretoria/Cars, Bikes & Related/Car Service & Repairs/edit/card/86",
  },
  {
    id: 88,
    country: "South Africa",
    city: "Centurion",
    category: "Agriculture & Mining",
    subcategory: "Equipment & Products",
    name: "Jaco Testing",
    email: "<EMAIL>",
    website: "www.covalonic.com",
    phone: "0723058372",
    first_name: "Jaco",
    last_name: "van Niekerk",
    status: "Approved",
    shares: 0,
    paymenttype: "free",
    expiry: "",
    editLink:
      "https://covalonic.com/upload/South Africa/Centurion/Agriculture & Mining/Equipment & Products/edit/card/88",
  },
]);

const setcards = async () => {
  for (let i = 0; i < cards.value.length; i++) {
    let payload: any = cards.value[i];
    delete payload.editLink;
    delete payload.id;
    payload.formatted_address = payload.city + ", " + payload.country;
    console.log("i", i);
    let itemadded = await submit(payload);
    console.log("i", i, itemadded);
  }
};
const extraInfos = () => {
  const currentSpace: any = useState("currentSpace", () => {
    return {};
  });
  const currentUser: any = useState("currentUser", () => {
    return {};
  });
  const currentClient: any = useState("currentClient", () => {
    return {};
  });

  const id = currentSpace.value.id ? currentSpace.value.id : 0;
  const acc: any = currentSpace.value.account ? currentSpace.value.account : 0;
  return {
    created_at: new Date(),
    created_date: moment(new Date()).format("YYYY-MM-DD"),
    last_action: new Date(),
    last_action_date: moment(new Date()).format("YYYY-MM-DD,HH:mm:ss"),
    created_by: currentUser.value.id ? currentUser.value.id : 0,
    space_own: id,
    access_uid: [currentUser.value.id],
    spaces: [id],
    account: acc,
    status: "Pending",
    hash: currentSpace.value.hash ? currentSpace.value.hash : "",
    lat: currentSpace.value.lat ? currentSpace.value.lat : "",
    lng: currentSpace.value.lng ? currentSpace.value.lng : "",
    location: currentSpace.value.location ? currentSpace.value.location : "",
    formatted_address: currentSpace.value.formatted_address
      ? currentSpace.value.formatted_address
      : "",
    website: currentSpace.value.website ? currentSpace.value.website : "",
    email: currentSpace.value.email ? currentSpace.value.email : "",
    phone: currentSpace.value.phone ? currentClient.value.phone : "",
    space_name: currentSpace.value.name ? currentSpace.value.name : "",
    customer_name: currentUser.value.first_name
      ? currentUser.value.first_name
      : "" + " " + currentUser.value.last_name
      ? currentUser.value.last_name
      : "",
    account_own: acc.length ? acc[0] : 0,
  };
};
const submit = async (item: any) => {
  let collection = "businesscards";
  let res: any = await add(collection, { ...extraInfos(), ...item });
  console.log("res", res);
  return res;
};
</script>

<template>
  <div>
    <!-- <button @click="setcards">set {{ cards.length }}</button> -->
    <client-only>
      <table-layout
        @selected="selected"
        @edited="edited"
        :info="info"
        :filter="filter"
        :extraInfo="extraInfo"
        :formsItem="formsItem"
        :filters="formsItem.filters"
        :forms="formsItem.data"
      />
    </client-only>
  </div>
</template>