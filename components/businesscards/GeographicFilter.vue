<template>
  <div class="geographic-filter bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-xl border border-white/10 p-6 transition-all duration-300 hover:border-white/20">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h3 class="text-lg font-semibold text-white flex items-center">
          <Icon name="mdi:map-search" class="w-5 h-5 mr-2 text-blue-400" />
          Location Filter
        </h3>
        <p class="text-sm text-gray-400">Find business cards in specific areas</p>
      </div>
      <button
        v-if="hasActiveFilters"
        @click="clearAllFilters"
        class="text-sm text-red-400 hover:text-red-300 font-medium transition-colors duration-200"
      >
        Clear All
      </button>
    </div>

    <!-- Current Location Section -->
    <div class="mb-6">
      <div class="flex items-center justify-between mb-3">
        <h4 class="font-medium text-gray-300">Current Location</h4>
        <button
          @click="useCurrentLocation"
          :disabled="isLocationLoading"
          class="flex items-center text-sm text-blue-400 hover:text-blue-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
        >
          <Icon 
            :name="isLocationLoading ? 'mdi:loading' : 'mdi:crosshairs-gps'" 
            :class="['w-4 h-4 mr-1', { 'animate-spin': isLocationLoading }]" 
          />
          {{ isLocationLoading ? 'Getting location...' : 'Use my location' }}
        </button>
      </div>

      <div v-if="currentLocation" class="text-sm text-green-300 bg-gradient-to-br from-green-900/20 to-green-800/20 p-3 rounded-lg border border-green-400/20">
        <Icon name="mdi:map-marker" class="w-4 h-4 inline mr-1 text-green-400" />
        {{ formatCurrentLocation() }}
      </div>

      <div v-if="locationError" class="text-sm text-red-300 bg-gradient-to-br from-red-900/20 to-red-800/20 p-3 rounded-lg border border-red-400/20">
        <Icon name="mdi:alert-circle" class="w-4 h-4 inline mr-1 text-red-400" />
        {{ locationError }}
      </div>
    </div>

    <!-- Radius Filter (when using coordinates) -->
    <div v-if="activeFilters.center.lat !== 0" class="mb-6">
      <label class="block text-sm font-medium text-gray-300 mb-2">
        Search Radius: <span class="text-blue-400 font-semibold">{{ activeFilters.radius }}km</span>
      </label>
      <input
        v-model.number="activeFilters.radius"
        type="range"
        min="1"
        max="200"
        step="5"
        class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-dark"
        @input="$emit('filter-changed', activeFilters)"
      />
      <div class="flex justify-between text-xs text-gray-500 mt-1">
        <span>1km</span>
        <span>200km</span>
      </div>
    </div>

    <!-- Address-based Filters -->
    <div class="space-y-4">
      <h4 class="font-medium text-gray-300">Filter by Address</h4>

      <!-- Country Filter -->
      <div>
        <label for="country" class="block text-sm font-medium text-gray-400 mb-1">
          Country
        </label>
        <select
          id="country"
          v-model="activeFilters.country"
          class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
          @change="onAddressFilterChange"
        >
          <option value="">All Countries</option>
          <option value="United States">United States</option>
          <option value="Canada">Canada</option>
          <option value="United Kingdom">United Kingdom</option>
          <option value="Australia">Australia</option>
          <option value="Germany">Germany</option>
          <option value="France">France</option>
          <option value="Japan">Japan</option>
        </select>
      </div>

      <!-- State/Province Filter -->
      <div>
        <label for="state" class="block text-sm font-medium text-gray-400 mb-1">
          State/Province
        </label>
        <input
          id="state"
          v-model="activeFilters.state"
          type="text"
          placeholder="Enter state or province"
          class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
          @input="debounceAddressFilter"
        />
      </div>

      <!-- City Filter -->
      <div>
        <label for="city" class="block text-sm font-medium text-gray-400 mb-1">
          City
        </label>
        <input
          id="city"
          v-model="activeFilters.city"
          type="text"
          placeholder="Enter city name"
          class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
          @input="debounceAddressFilter"
        />
      </div>

      <!-- Postal Code Filter -->
      <div>
        <label for="postalCode" class="block text-sm font-medium text-gray-400 mb-1">
          Postal Code
        </label>
        <input
          id="postalCode"
          v-model="activeFilters.postalCode"
          type="text"
          placeholder="Enter postal code"
          class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
          @input="debounceAddressFilter"
        />
      </div>
    </div>

    <!-- Popular Locations -->
    <div class="mt-6 pt-6 border-t border-gray-700/50">
      <h4 class="font-medium text-gray-300 mb-3 flex items-center">
        <Icon name="mdi:fire" class="w-4 h-4 mr-2 text-orange-400" />
        Popular Locations
      </h4>
      <div class="grid grid-cols-1 gap-2">
        <button
          v-for="location in popularLocations"
          :key="`${location.city}-${location.state}`"
          @click="selectPopularLocation(location)"
          class="group flex items-center justify-between p-3 text-sm text-left rounded-lg bg-gray-800/30 hover:bg-gray-800/50 border border-white/5 hover:border-white/10 transition-all duration-200"
        >
          <span class="font-medium text-gray-300 group-hover:text-white transition-colors">
            <Icon name="mdi:map-marker" class="w-3 h-3 inline mr-1 text-blue-400" />
            {{ location.city }}, {{ location.state }}
          </span>
          <span class="text-gray-500 text-xs">{{ location.count }} cards</span>
        </button>
      </div>
    </div>

    <!-- Active Filter Summary -->
    <div v-if="hasActiveFilters" class="mt-6 pt-6 border-t border-gray-700/50">
      <h4 class="font-medium text-gray-300 mb-2">Active Filters</h4>
      <div class="flex flex-wrap gap-2">
        <span
          v-if="filterSummary !== 'No location filters'"
          class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-blue-600/20 to-blue-700/20 text-blue-300 border border-blue-400/20"
        >
          <Icon name="mdi:map-marker" class="w-3 h-3 mr-1" />
          {{ filterSummary }}
          <button
            @click="clearAllFilters"
            class="ml-2 text-blue-400 hover:text-blue-300 transition-colors"
          >
            <Icon name="mdi:close" class="w-3 h-3" />
          </button>
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useGeographicFiltering } from '~/composables/useGeographicFiltering'

const emit = defineEmits(['filter-changed'])

// Use geographic filtering composable
const {
  currentLocation,
  isLocationLoading,
  locationError,
  activeFilters,
  hasActiveFilters,
  filterSummary,
  getCurrentLocation,
  setFilterToCurrentLocation,
  clearFilters,
  getPopularLocations
} = useGeographicFiltering()

// Local state
const popularLocations = ref([
  { city: 'San Francisco', state: 'CA', count: 150 },
  { city: 'New York', state: 'NY', count: 200 },
  { city: 'Los Angeles', state: 'CA', count: 120 },
  { city: 'Chicago', state: 'IL', count: 95 },
  { city: 'Austin', state: 'TX', count: 80 }
])

let addressFilterTimeout: NodeJS.Timeout

// Methods
const useCurrentLocation = async () => {
  try {
    await setFilterToCurrentLocation()
    emit('filter-changed', activeFilters)
  } catch (error) {
    console.error('Failed to use current location:', error)
  }
}

const clearAllFilters = () => {
  clearFilters()
  emit('filter-changed', activeFilters)
}

const onAddressFilterChange = () => {
  // Clear coordinate-based filters when using address filters
  activeFilters.center = { lat: 0, lng: 0 }
  activeFilters.radius = 50
  emit('filter-changed', activeFilters)
}

const debounceAddressFilter = () => {
  if (addressFilterTimeout) {
    clearTimeout(addressFilterTimeout)
  }
  
  addressFilterTimeout = setTimeout(() => {
    onAddressFilterChange()
  }, 500)
}

const selectPopularLocation = (location: any) => {
  activeFilters.city = location.city
  activeFilters.state = location.state
  activeFilters.country = 'United States' // Assuming US for popular locations
  activeFilters.postalCode = ''
  
  // Clear coordinate-based filters
  activeFilters.center = { lat: 0, lng: 0 }
  activeFilters.radius = 50
  
  emit('filter-changed', activeFilters)
}

const formatCurrentLocation = () => {
  if (!currentLocation.value?.address) {
    return `${currentLocation.value?.lat.toFixed(4)}, ${currentLocation.value?.lng.toFixed(4)}`
  }
  
  const addr = currentLocation.value.address
  const parts = []
  
  if (addr.city) parts.push(addr.city)
  if (addr.state) parts.push(addr.state)
  if (addr.country) parts.push(addr.country)
  
  return parts.join(', ') || 'Location detected'
}

// Load popular locations on mount
onMounted(async () => {
  try {
    const locations = await getPopularLocations()
    popularLocations.value = locations
  } catch (error) {
    console.error('Failed to load popular locations:', error)
  }
})

// Watch for filter changes and emit
watch(activeFilters, (newFilters) => {
  emit('filter-changed', newFilters)
}, { deep: true })
</script>

<style scoped>
/* Custom dark theme range slider styling */
input[type="range"].slider-dark {
  -webkit-appearance: none;
  background: transparent;
}

input[type="range"].slider-dark::-webkit-slider-track {
  background: #374151;
  height: 8px;
  border-radius: 4px;
}

input[type="range"].slider-dark::-webkit-slider-thumb {
  -webkit-appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  cursor: pointer;
  transition: all 0.2s ease;
}

input[type="range"].slider-dark::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 12px rgba(59, 130, 246, 0.5);
}

input[type="range"].slider-dark::-moz-range-track {
  background: #374151;
  height: 8px;
  border-radius: 4px;
  border: none;
}

input[type="range"].slider-dark::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
}

input[type="range"].slider-dark::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 12px rgba(59, 130, 246, 0.5);
}

/* Dark theme select styling */
select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}
</style>