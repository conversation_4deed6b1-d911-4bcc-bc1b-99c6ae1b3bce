<script setup lang="ts">
import vCardFactory from "vcards-js";
import { useQRCode } from "@vueuse/integrations/useQRCode";
import { useAnalyticsTracking } from '~/composables/analytics-tracking';
import { normalizeUrl, truncateUrl } from '~/utils/urlHelpers';

const location = useBrowserLocation();
const routeId: any = useRoute();
const currentClient: any = useState("currentClient", () => {
  return {};
});
const { currentSpace } = space();

// Initialize analytics tracking
const { trackCardView, trackCardDownload, trackContactAction } = useAnalyticsTracking();
const getCalendarBookings = async () => {
  console.log('GET CARD id', routeId.params.id)
  const cards = await queryById("businesscards", routeId.params.id);
  console.log("cards", cards);
  console.log("cards result", cards);

  if (cards) {
    currentClient.value = cards;
    getSpace();

    // Track the business card view
    try {
      await trackCardView(routeId.params.id, 'vcard');
      console.log('View tracked for business card:', routeId.params.id);
    } catch (error) {
      console.error('Error tracking view:', error);
    }
  }
};

const getSpace = async () => {
  const space = await queryById("spaces", currentClient.value.space_own);
  console.log(space);
  currentSpace.value = space;
};

onMounted(()=> {
console.log('MOUNTED')
getCalendarBookings();

})

const user = ref({});
const company = ref({});
const download = (vCardString: any, fileName: any) => {
  let fileURL = window.URL.createObjectURL(new Blob([vCardString]));
  let fileLink = document.createElement("a");
  fileLink.href = fileURL;
  fileLink.setAttribute("download", fileName);
  document.body.appendChild(fileLink);
  fileLink.click();
};

// Computed URL for QR code that updates when currentClient changes
const url = computed(() => {
  if (currentClient.value.slug) {
    return `${location.value.origin}/o/user/${currentClient.value.slug}`;
  }
  return '';
});

const generate_vCard = () => {
  const vCard = vCardFactory();
  //set basic properties shown before
  vCard.firstName = currentClient.value.first_name;
  vCard.middleName = currentClient.value.middle_name;
  vCard.lastName = currentClient.value.last_name;
  vCard.uid = currentClient.value.id;
  vCard.organization = currentSpace.value.name;

  //link to image
  // vCard.photo.attachFromUrl('https://avatars2.githubusercontent.com/u/5659221?v=3&s=460', 'JPEG');

  //or embed image
  // vCard.photo.attachFromUrl('/path/to/file.jpeg');

  vCard.workPhone = currentClient.value.phone;
  // vCard.birthday = new Date(1985, 0, 1);
  // vCard.title = 'Software Developer';
  // vCard.url = 'https://github.com/enesser';
  // vCard.workUrl = 'https://acme-corporation/enesser';
  vCard.note = currentClient.value.note;

  //set other vitals
  // vCard.nickname = 'Scarface';
  // vCard.namePrefix = 'Mr.';
  // vCard.nameSuffix = 'JR';
  // vCard.gender = 'M';
  // vCard.anniversary = new Date(2004, 0, 1);
  // vCard.role = 'Software Development';

  //set other phone numbers
  // vCard.homePhone = '************';
  vCard.cellPhone = currentClient.value.cell;
  // vCard.pagerPhone = '************';

  //set fax/facsimile numbers
  // vCard.homeFax = '************';
  vCard.workFax = currentClient.value.fax;

  //set email addresses
  vCard.email = currentClient.value.email;
  // vCard.workEmail = '<EMAIL>';

  //set logo of organization or personal logo (also supports embedding, see above)
  // vCard.logo.attachFromUrl('https://avatars2.githubusercontent.com/u/5659221?v=3&s=460', 'JPEG');

  //set URL where the vCard can be found
  vCard.source = location.value.href;

  //set address information
  // vCard.homeAddress.label = 'Home Address';
  // vCard.homeAddress.street = '123 Main Street';
  // vCard.homeAddress.city = 'Chicago';
  // vCard.homeAddress.stateProvince = 'IL';
  // vCard.homeAddress.postalCode = '12345';
  // vCard.homeAddress.countryRegion = 'United States of America';

  // vCard.workAddress.label = 'Work Address';
  // vCard.workAddress.street = '123 Corporate Loop\nSuite 500';
  // vCard.workAddress.city = 'Los Angeles';
  // vCard.workAddress.stateProvince = 'CA';
  // vCard.workAddress.postalCode = '54321';
  // vCard.workAddress.countryRegion = 'United States of America';

  //set social media URLs
  vCard.socialUrls["facebook"] = "";
  vCard.socialUrls["linkedIn"] = "";
  vCard.socialUrls["twitter"] = "";
  vCard.socialUrls["flickr"] = "";
  vCard.socialUrls["custom"] = "";
  console.log(vCard.getFormattedString());
  download(vCard.getFormattedString(), "vcardFile.vcf");

  // Track the download event
  try {
    trackCardDownload(currentClient.value.id, 'vcard');
    console.log('Download tracked for business card:', currentClient.value.id);
  } catch (error) {
    console.error('Error tracking download:', error);
  }
};
// `qrcode` will be a ref of data URL for the business card QR page
const qrcode = useQRCode(url);
</script>

<template>
  <div class="mb-8 p-6 overflow-hidden rounded-xl shadow-lg bg-gradient-to-br from-gray-800/50 via-gray-700/50 to-gray-800/50 backdrop-blur-sm border border-white/10 group">
    <div class="relative flex items-center overflow-hidden group">
      <img
        v-if="currentClient.avatar"
        :src="currentClient.background ? currentClient.background.src : ''"
        class="w-full rounded-lg"
      />
      <div class="absolute top-4 ltr:right-4 rtl:left-4"></div>
    </div>
    
    <div class="px-3 pb-2 text-center">
      <h3 class="text-xl font-bold text-white">
        {{ currentClient.name }}
      </h3>
    </div>

    <div class="flex justify-center px-2">
      <div class="py-3 group-hover:block w-full">
        <div v-if="currentClient.about">
          <div
            class="mb-4 text-center text-gray-300"
            v-html="currentClient.about"
          ></div>
        </div>

        <div class="my-4 border-t border-white/10"></div>
        
        <!-- Social Share Button -->
        <div class="flex justify-center mb-4">
          <SharedSocialShareButton
            :card-id="currentClient.id || ''"
            :card-name="currentClient.name || `${currentClient.first_name || ''} ${currentClient.last_name || ''}`.trim() || 'Unknown'"
            :card-company="currentSpace?.name || ''"
            :card-email="currentClient.email || ''"
            share-button-text="Share Business Card"
          />
        </div>
        
        <div class="ltr:text-left rtl:text-right space-y-3">
          <div class="flex items-center py-2 border-b border-white/10">
            <Icon name="mdi:account" class="h-4 w-4 text-blue-400 mr-3" />
            <span class="font-medium text-gray-200">Full Name:</span>
            <span class="ml-2 text-gray-300">{{ currentClient.first_name }} {{ currentClient.last_name }}</span>
          </div>

          <div v-if="currentClient.cell" class="flex items-center py-2 border-b border-white/10">
            <Icon name="mdi:cellphone" class="h-4 w-4 text-green-400 mr-3" />
            <span class="font-medium text-gray-200">Mobile:</span>
            <a
              :href="'tel:' + currentClient.cell"
              class="ml-2 text-blue-400 hover:text-blue-300 transition-colors duration-200"
              @click="trackContactAction(currentClient.id, 'mobile')"
            >
              {{ formatPhoneNumber(currentClient.cell) }}
            </a>
          </div>

          <div v-if="currentClient.phone" class="flex items-center py-2 border-b border-white/10">
            <Icon name="mdi:phone" class="h-4 w-4 text-blue-400 mr-3" />
            <span class="font-medium text-gray-200">Phone:</span>
            <a
              :href="'tel:' + currentClient.phone"
              class="ml-2 text-blue-400 hover:text-blue-300 transition-colors duration-200"
              @click="trackContactAction(currentClient.id, 'phone')"
            >
              {{ formatPhoneNumber(currentClient.phone) }}
            </a>
          </div>

          <div v-if="currentClient.fax" class="flex items-center py-2 border-b border-white/10">
            <Icon name="mdi:fax" class="h-4 w-4 text-purple-400 mr-3" />
            <span class="font-medium text-gray-200">Fax:</span>
            <a
              :href="'fax:' + currentClient.fax"
              class="ml-2 text-blue-400 hover:text-blue-300 transition-colors duration-200"
              @click="trackContactAction(currentClient.id, 'fax')"
            >
              {{ formatPhoneNumber(currentClient.fax) }}
            </a>
          </div>

          <div v-if="currentClient.email" class="flex items-center py-2 border-b border-white/10">
            <Icon name="mdi:email" class="h-4 w-4 text-red-400 mr-3" />
            <span class="font-medium text-gray-200">Email:</span>
            <a
              :href="'mailto:' + currentClient.email"
              class="ml-2 text-blue-400 hover:text-blue-300 transition-colors duration-200"
              @click="trackContactAction(currentClient.id, 'email')"
            >
              {{ currentClient.email }}
            </a>
          </div>

          <div v-if="currentClient.website" class="flex items-center py-2 border-b border-white/10">
            <Icon name="mdi:web" class="h-4 w-4 text-purple-400 mr-3" />
            <span class="font-medium text-gray-200">Website:</span>
            <a
              v-if="normalizeUrl(currentClient.website)"
              :href="normalizeUrl(currentClient.website)"
              target="_blank"
              rel="noopener noreferrer"
              class="ml-2 text-blue-400 hover:text-blue-300 transition-colors duration-200 underline"
              @click="trackContactAction(currentClient.id, 'website')"
            >
              {{ truncateUrl(currentClient.website, 40) }}
            </a>
            <span v-else class="ml-2 text-gray-400 italic">
              {{ currentClient.website }} (Invalid URL)
            </span>
          </div>

          <div v-if="currentClient.formatted_address" class="flex items-center py-2">
            <Icon name="mdi:map-marker" class="h-4 w-4 text-yellow-400 mr-3" />
            <span class="font-medium text-gray-200">Location:</span>
            <span class="ml-2 text-gray-300">{{ currentClient.formatted_address }}</span>
          </div>

          <div class="mt-6 pt-4 border-t border-white/10">
            <button
              class="w-full px-4 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-lg transition-all duration-200 flex items-center justify-center font-medium shadow-lg transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50"
              @click="generate_vCard"
            >
              <Icon name="mdi:download" class="h-5 w-5 mr-2" />
              Download vCard
            </button>
          </div>
        </div>
        
        <div v-if="currentClient.slug" class="mt-6 pt-4 border-t border-white/10">
          <h4 class="text-sm font-medium text-gray-200 mb-3 text-center">QR Code</h4>
          <div class="flex justify-center">
            <div class="p-3 bg-white rounded-lg shadow-lg">
              <img :src="qrcode" alt="QR Code" class="h-32 w-32" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>