<template>
  <div class="ocr-settings bg-white rounded-lg shadow-sm p-6">
    <h3 class="text-lg font-semibold mb-4">OCR Settings</h3>
    
    <!-- OCR Engine Selection -->
    <div class="mb-6">
      <label class="block text-sm font-medium text-gray-700 mb-2">
        OCR Engine
      </label>
      <div class="space-y-2">
        <label class="flex items-center">
          <input
            type="radio"
            v-model="settings.engine"
            value="auto"
            class="mr-2"
          />
          <span class="text-sm">
            <strong>Auto (Recommended)</strong> - Uses Google Vision when online, Tesseract offline
          </span>
        </label>
        <label class="flex items-center">
          <input
            type="radio"
            v-model="settings.engine"
            value="google-vision"
            class="mr-2"
          />
          <span class="text-sm">
            <strong>Google Vision</strong> - Highest accuracy, requires internet
          </span>
        </label>
        <label class="flex items-center">
          <input
            type="radio"
            v-model="settings.engine"
            value="tesseract"
            class="mr-2"
          />
          <span class="text-sm">
            <strong>Tesseract.js</strong> - Works offline, good for privacy
          </span>
        </label>
      </div>
    </div>
    
    <!-- Preprocessing Options -->
    <div class="mb-6">
      <h4 class="text-sm font-medium text-gray-700 mb-2">Image Preprocessing</h4>
      <div class="space-y-2">
        <label class="flex items-center">
          <input
            type="checkbox"
            v-model="settings.preprocessing"
            class="mr-2"
          />
          <span class="text-sm">Enable automatic image enhancement</span>
        </label>
        <div v-if="settings.preprocessing" class="ml-6 space-y-1 text-xs text-gray-600">
          <p>✓ Contrast enhancement</p>
          <p>✓ Noise reduction</p>
          <p>✓ Automatic rotation correction</p>
          <p>✓ Adaptive thresholding</p>
        </div>
      </div>
    </div>
    
    <!-- Confidence Threshold -->
    <div class="mb-6">
      <label class="block text-sm font-medium text-gray-700 mb-2">
        Minimum Confidence Level
      </label>
      <div class="flex items-center space-x-2">
        <input
          type="range"
          v-model.number="settings.confidence"
          min="0"
          max="1"
          step="0.1"
          class="flex-1"
        />
        <span class="text-sm font-medium w-12">{{ (settings.confidence * 100).toFixed(0) }}%</span>
      </div>
      <p class="text-xs text-gray-600 mt-1">
        Results below this confidence will trigger a warning
      </p>
    </div>
    
    <!-- Test Mode -->
    <div class="mb-6">
      <label class="flex items-center">
        <input
          type="checkbox"
          v-model="settings.testMode"
          class="mr-2"
        />
        <span class="text-sm">
          <strong>Test Mode</strong> - Compare results from different engines
        </span>
      </label>
    </div>
    
    <!-- API Key Status -->
    <div class="bg-gray-50 rounded p-4">
      <h4 class="text-sm font-medium text-gray-700 mb-2">API Status</h4>
      <div class="space-y-1 text-xs">
        <div class="flex items-center justify-between">
          <span>Google Vision API:</span>
          <span :class="apiStatus.googleVision ? 'text-green-600' : 'text-red-600'">
            {{ apiStatus.googleVision ? '✓ Configured' : '✗ Not configured' }}
          </span>
        </div>
        <div class="flex items-center justify-between">
          <span>Tesseract.js:</span>
          <span class="text-green-600">✓ Ready</span>
        </div>
      </div>
    </div>
    
    <!-- Save Button -->
    <div class="mt-6">
      <button
        @click="saveSettings"
        class="w-full bg-blue-600 text-white rounded-md py-2 px-4 hover:bg-blue-700 transition-colors"
      >
        Save Settings
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'

interface OCRSettings {
  engine: 'auto' | 'google-vision' | 'tesseract'
  preprocessing: boolean
  confidence: number
  testMode: boolean
}

const emit = defineEmits<{
  'update:settings': [settings: OCRSettings]
}>()

const settings = reactive<OCRSettings>({
  engine: 'auto',
  preprocessing: true,
  confidence: 0.7,
  testMode: false
})

const apiStatus = reactive({
  googleVision: false
})

// Check API status
const checkApiStatus = async () => {
  try {
    const response = await $fetch('/api/ocr/status')
    apiStatus.googleVision = response.googleVision || false
  } catch (err) {
    console.error('Failed to check API status:', err)
  }
}

// Load saved settings
const loadSettings = () => {
  const saved = localStorage.getItem('ocrSettings')
  if (saved) {
    try {
      const parsed = JSON.parse(saved)
      Object.assign(settings, parsed)
    } catch (err) {
      console.error('Failed to load settings:', err)
    }
  }
}

// Save settings
const saveSettings = () => {
  localStorage.setItem('ocrSettings', JSON.stringify(settings))
  emit('update:settings', { ...settings })
  
  // Show success message
  const toast = document.createElement('div')
  toast.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded shadow-lg'
  toast.textContent = 'Settings saved!'
  document.body.appendChild(toast)
  
  setTimeout(() => {
    toast.remove()
  }, 2000)
}

onMounted(() => {
  loadSettings()
  checkApiStatus()
})
</script>

<style scoped>
.ocr-settings {
  max-width: 400px;
}

input[type="range"] {
  @apply h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer;
}

input[type="range"]::-webkit-slider-thumb {
  @apply appearance-none w-4 h-4 bg-blue-600 rounded-full cursor-pointer;
}

input[type="range"]::-moz-range-thumb {
  @apply w-4 h-4 bg-blue-600 rounded-full cursor-pointer border-0;
}
</style>