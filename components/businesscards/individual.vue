<script setup lang="ts">
const selectedBusinesscards: any = useState('selectedBusinesscards', () => {
  return '';
});

const schema = computed(() => {
  return {
    name: 'test',
  };
});
</script>

<template>
  <div>
    <layouts-main :title="selectedBusinesscards.name">
      <template #buttons>
        <tooltip-icon
          text="List"
          side="left"
          icon="material-symbols:list-alt-sharp"
          @submit="$router.push('/c/businesscards')"
        />
        <tooltip-icon
          text="Edit"
          side="bottom"
          icon="mdi:pencil"
          @submit="$router.push('/c/businesscards/edit')"
        />
      </template>
      <template #content>
        <div class="w-full">
          <div class="w-96 h-52">
            <div className="cursor-pointer group perspective">
              <div
                className="relative preserve-3d group-hover:my-rotate-y-180 w-full h-full duration-1000"
              >
                <div className=" backface-hidden w-96 h-52">
                  <img
                    v-if="
                      selectedBusinesscards.imageUrl ||
                      selectedBusinesscards.thumbnailUrl ||
                      selectedBusinesscards.image
                    "
                    class="w-96 h-52"
                    :src="
                      selectedBusinesscards.imageUrl ||
                      selectedBusinesscards.thumbnailUrl ||
                      (selectedBusinesscards.image ? selectedBusinesscards.image.src : '')
                    "
                  />
                  <card-user :info="selectedBusinesscards" v-else />
                </div>
                <div
                  className="absolute top-0 my-rotate-y-180 backface-hidden w-96 h-52 overflow-hidden"
                >
                  <card-user :info="selectedBusinesscards" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </layouts-main>
  </div>
</template>
