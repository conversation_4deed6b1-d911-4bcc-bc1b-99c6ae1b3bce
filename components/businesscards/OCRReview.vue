<template>
  <div class="ocr-review-container bg-white rounded-lg shadow-lg p-6">
    <!-- Header -->
    <div class="mb-6">
      <h3 class="text-xl font-semibold text-gray-900 mb-2">Review Extracted Information</h3>
      <p class="text-gray-600">
        We've extracted the following information from your business card. Please review and edit as needed.
      </p>
    </div>

    <!-- OCR Confidence Indicator -->
    <div v-if="ocrData && ocrData.confidence" class="mb-6">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm font-medium text-gray-700">Extraction Confidence</span>
        <span class="text-sm text-gray-600">{{ Math.round(ocrData.confidence) }}%</span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div 
          class="h-2 rounded-full transition-all duration-300"
          :class="getConfidenceColor(ocrData.confidence)"
          :style="{ width: `${ocrData.confidence}%` }"
        ></div>
      </div>
      <p v-if="ocrData.confidence < 70" class="text-sm text-amber-600 mt-1">
        <Icon name="mdi:alert-circle-outline" class="w-4 h-4 inline mr-1" />
        Low confidence detected. Please review all fields carefully.
      </p>
    </div>

    <!-- Form Fields -->
    <form @submit.prevent="handleSubmit" class="space-y-6">
      <!-- Personal Information -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
            Full Name *
          </label>
          <input
            id="name"
            v-model="formData.name"
            type="text"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter full name"
          />
        </div>

        <div>
          <label for="title" class="block text-sm font-medium text-gray-700 mb-1">
            Job Title
          </label>
          <input
            id="title"
            v-model="formData.title"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter job title"
          />
        </div>
      </div>

      <!-- Company Information -->
      <div>
        <label for="company" class="block text-sm font-medium text-gray-700 mb-1">
          Company
        </label>
        <input
          id="company"
          v-model="formData.company"
          type="text"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Enter company name"
        />
      </div>

      <!-- Contact Information -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
            Email Address
          </label>
          <input
            id="email"
            v-model="formData.email"
            type="email"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter email address"
          />
        </div>

        <div>
          <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">
            Phone Number
          </label>
          <input
            id="phone"
            v-model="formData.phone"
            type="tel"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter phone number"
          />
        </div>
      </div>

      <!-- Website -->
      <div>
        <label for="website" class="block text-sm font-medium text-gray-700 mb-1">
          Website
        </label>
        <input
          id="website"
          v-model="formData.website"
          type="url"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Enter website URL"
        />
      </div>

      <!-- Address Information -->
      <div class="space-y-4">
        <h4 class="text-lg font-medium text-gray-900">Address</h4>
        
        <div>
          <label for="street" class="block text-sm font-medium text-gray-700 mb-1">
            Street Address
          </label>
          <input
            id="street"
            v-model="formData.address.street"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter street address"
          />
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label for="city" class="block text-sm font-medium text-gray-700 mb-1">
              City
            </label>
            <input
              id="city"
              v-model="formData.address.city"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter city"
            />
          </div>

          <div>
            <label for="state" class="block text-sm font-medium text-gray-700 mb-1">
              State/Province
            </label>
            <input
              id="state"
              v-model="formData.address.state"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter state"
            />
          </div>

          <div>
            <label for="postalCode" class="block text-sm font-medium text-gray-700 mb-1">
              Postal Code
            </label>
            <input
              id="postalCode"
              v-model="formData.address.postalCode"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter postal code"
            />
          </div>
        </div>

        <div>
          <label for="country" class="block text-sm font-medium text-gray-700 mb-1">
            Country
          </label>
          <input
            id="country"
            v-model="formData.address.country"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter country"
          />
        </div>
      </div>

      <!-- Raw Text Viewer (Collapsible) -->
      <div class="border-t pt-6">
        <button
          type="button"
          @click="showRawText = !showRawText"
          class="flex items-center text-sm text-gray-600 hover:text-gray-800"
        >
          <Icon 
            :name="showRawText ? 'mdi:chevron-down' : 'mdi:chevron-right'" 
            class="w-4 h-4 mr-1" 
          />
          View Raw Extracted Text
        </button>
        
        <div v-if="showRawText" class="mt-3">
          <textarea
            v-model="ocrData.rawText"
            readonly
            rows="6"
            class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm font-mono resize-none"
            placeholder="Raw extracted text will appear here..."
          />
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex space-x-4 pt-6 border-t">
        <button
          type="button"
          @click="$emit('back')"
          class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
        >
          <Icon name="mdi:arrow-left" class="w-4 h-4 inline mr-2" />
          Back to Camera
        </button>
        
        <button
          type="submit"
          :disabled="isSubmitting || !formData.name"
          class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <Icon 
            v-if="isSubmitting" 
            name="mdi:loading" 
            class="w-4 h-4 inline mr-2 animate-spin" 
          />
          <Icon 
            v-else 
            name="mdi:check" 
            class="w-4 h-4 inline mr-2" 
          />
          {{ isSubmitting ? 'Saving...' : 'Save Business Card' }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import type { OCRResult } from '~/composables/useOCR'

interface Props {
  ocrData: OCRResult
  imageFile?: File
}

const props = defineProps<Props>()
const emit = defineEmits(['submit', 'back'])

// Form state
const isSubmitting = ref(false)
const showRawText = ref(false)

// Form data
const formData = reactive({
  name: '',
  email: '',
  phone: '',
  company: '',
  title: '',
  website: '',
  address: {
    street: '',
    city: '',
    state: '',
    country: '',
    postalCode: ''
  }
})

// Initialize form data with OCR results
watch(() => props.ocrData, (newData) => {
  if (newData) {
    Object.assign(formData, {
      name: newData.name || '',
      email: newData.email || '',
      phone: newData.phone || '',
      company: newData.company || '',
      title: newData.title || '',
      website: newData.website || '',
      address: {
        street: newData.address?.street || '',
        city: newData.address?.city || '',
        state: newData.address?.state || '',
        country: newData.address?.country || '',
        postalCode: newData.address?.postalCode || ''
      }
    })
  }
}, { immediate: true })

// Get confidence color based on percentage
const getConfidenceColor = (confidence: number) => {
  if (confidence >= 80) return 'bg-green-500'
  if (confidence >= 60) return 'bg-yellow-500'
  return 'bg-red-500'
}

// Handle form submission
const handleSubmit = async () => {
  if (!formData.name.trim()) {
    return
  }

  isSubmitting.value = true
  
  try {
    // Prepare submission data
    const submissionData = {
      ...formData,
      confidence: props.ocrData.confidence,
      rawText: props.ocrData.rawText,
      imageFile: props.imageFile
    }
    
    emit('submit', submissionData)
  } catch (error) {
    console.error('Failed to submit business card:', error)
  } finally {
    isSubmitting.value = false
  }
}
</script>

<style scoped>
.ocr-review-container {
  max-width: 100%;
  max-height: 80vh;
  overflow-y: auto;
}

/* Improve mobile responsiveness */
@media (max-width: 768px) {
  .ocr-review-container {
    max-height: 90vh;
    margin: 0;
    border-radius: 0;
  }
}
</style>