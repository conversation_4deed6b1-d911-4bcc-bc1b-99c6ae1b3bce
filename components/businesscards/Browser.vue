<template>
  <div class="business-card-browser">
    <!-- Header -->
    <div class="mb-6">
      <h2 class="text-xl font-semibold text-white mb-2">Browse Business Cards</h2>
      <p class="text-gray-400">Select an existing business card to attach to this contact</p>
    </div>

    <!-- Search and Filters -->
    <div class="mb-6 space-y-4">
      <div class="flex flex-col sm:flex-row gap-4">
        <div class="flex-1">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search by name, company, email..."
            class="w-full px-4 py-2 bg-gray-700/50 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
        </div>
        <div class="flex gap-2">
          <select
            v-model="statusFilter"
            class="px-3 py-2 bg-gray-700/50 border border-white/10 rounded-lg text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Cards</option>
            <option value="approved">User Uploaded</option>
            <option value="admin-approved">Admin Approved</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="text-center py-12">
      <Icon name="mdi:loading" class="w-8 h-8 text-gray-400 mx-auto mb-4 animate-spin" />
      <p class="text-gray-300">Loading business cards...</p>
    </div>

    <!-- Empty State -->
    <div v-else-if="filteredCards.length === 0" class="text-center py-12">
      <Icon name="mdi:card-account-details-outline" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-semibold text-white mb-2">No Business Cards Found</h3>
      <p class="text-gray-300 mb-4">
        {{ searchQuery ? 'No cards match your search criteria.' : 'You haven\'t uploaded any business cards yet.' }}
      </p>
    </div>

    <!-- Business Cards Grid -->
    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
      <div
        v-for="card in filteredCards"
        :key="card.id"
        :class="[
          'business-card-item',
          'bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70',
          'backdrop-blur-sm rounded-xl border transition-all duration-200 cursor-pointer',
          selectedCard?.id === card.id 
            ? 'border-blue-500 ring-2 ring-blue-500/50' 
            : 'border-white/10 hover:border-white/20'
        ]"
        @click="selectCard(card)"
      >
        <!-- Card Image -->
        <div class="aspect-[1.78] bg-gray-800 rounded-t-xl overflow-hidden">
          <img
            v-if="card.image || card.imageUrl"
            :src="card.image || card.imageUrl"
            :alt="`Business card for ${getCardDisplayName(card)}`"
            class="w-full h-full object-cover"
            @error="handleImageError"
          >
          <div v-else class="w-full h-full flex items-center justify-center">
            <Icon name="mdi:card-account-details" class="w-12 h-12 text-gray-400" />
          </div>
        </div>

        <!-- Card Info -->
        <div class="p-4">
          <div class="flex items-start justify-between mb-2">
            <div class="flex-1 min-w-0">
              <h3 class="text-white font-medium truncate">
                {{ getCardDisplayName(card) }}
              </h3>
              <p v-if="card.company" class="text-gray-400 text-sm truncate">
                {{ card.company }}
              </p>
            </div>
            <div class="flex-shrink-0 ml-2">
              <span
                :class="[
                  'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                  card.status === 'approved' ? 'bg-green-100 text-green-800' :
                  card.status === 'admin-approved' ? 'bg-blue-100 text-blue-800' :
                  'bg-yellow-100 text-yellow-800'
                ]"
              >
                {{ getStatusLabel(card.status) }}
              </span>
            </div>
          </div>

          <div class="space-y-1 text-sm text-gray-300">
            <p v-if="card.email" class="truncate">
              <Icon name="mdi:email" class="inline w-4 h-4 mr-1" />
              {{ card.email }}
            </p>
            <p v-if="card.phone" class="truncate">
              <Icon name="mdi:phone" class="inline w-4 h-4 mr-1" />
              {{ card.phone }}
            </p>
            <p class="text-xs text-gray-400">
              <Icon name="mdi:calendar" class="inline w-3 h-3 mr-1" />
              {{ formatDate(card.createdAt) }}
            </p>
          </div>

          <!-- Selection Indicator -->
          <div
            v-if="selectedCard?.id === card.id"
            class="absolute top-2 right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center"
          >
            <Icon name="mdi:check" class="w-4 h-4 text-white" />
          </div>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div v-if="totalPages > 1" class="flex justify-center items-center space-x-2 mb-6">
      <button
        @click="previousPage"
        :disabled="currentPage === 1"
        class="px-3 py-2 bg-gray-700/50 border border-white/10 rounded-lg text-white disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-700"
      >
        <Icon name="mdi:chevron-left" class="w-4 h-4" />
      </button>
      
      <span class="text-gray-300 text-sm">
        Page {{ currentPage }} of {{ totalPages }}
      </span>
      
      <button
        @click="nextPage"
        :disabled="currentPage === totalPages"
        class="px-3 py-2 bg-gray-700/50 border border-white/10 rounded-lg text-white disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-700"
      >
        <Icon name="mdi:chevron-right" class="w-4 h-4" />
      </button>
    </div>

    <!-- Actions -->
    <div class="flex justify-end space-x-3">
      <button
        @click="$emit('cancel')"
        type="button"
        class="px-6 py-2 bg-white/10 hover:bg-white/20 text-white font-medium rounded-lg transition-colors border border-white/20"
      >
        Cancel
      </button>
      <button
        @click="confirmSelection"
        :disabled="!selectedCard"
        type="button"
        class="px-6 py-2 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Select Card
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useBusinessCards, type BusinessCard } from '~/composables/useBusinessCards'

// Props
const props = defineProps<{
  excludeContactId?: string // Don't show cards already attached to this contact
}>()

// Emits
const emit = defineEmits<{
  cardSelected: [card: BusinessCard]
  cancel: []
}>()

// State
const { businessCards, loading, subscribeToBusinessCards } = useBusinessCards()
const selectedCard = ref<BusinessCard | null>(null)
const searchQuery = ref('')
const statusFilter = ref<'all' | 'approved' | 'admin-approved'>('all')
const currentPage = ref(1)
const itemsPerPage = 12

// Computed
const filteredCards = computed(() => {
  let cards = businessCards.value.filter(card => {
    // Only show approved cards or admin-approved cards
    if (statusFilter.value === 'approved') {
      return card.status === 'approved' && card.userId // User's own cards
    } else if (statusFilter.value === 'admin-approved') {
      return card.status === 'admin-approved' // Admin approved cards
    } else {
      return card.status === 'approved' || card.status === 'admin-approved'
    }
  })

  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    cards = cards.filter(card => {
      const name = getCardDisplayName(card).toLowerCase()
      const company = card.company?.toLowerCase() || ''
      const email = card.email?.toLowerCase() || ''
      return name.includes(query) || company.includes(query) || email.includes(query)
    })
  }

  // Exclude cards already attached to the current contact
  if (props.excludeContactId) {
    cards = cards.filter(card => card.contactId !== props.excludeContactId)
  }

  // Pagination
  const startIndex = (currentPage.value - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  return cards.slice(startIndex, endIndex)
})

const totalCards = computed(() => {
  let cards = businessCards.value.filter(card => {
    if (statusFilter.value === 'approved') {
      return card.status === 'approved' && card.userId
    } else if (statusFilter.value === 'admin-approved') {
      return card.status === 'admin-approved'
    } else {
      return card.status === 'approved' || card.status === 'admin-approved'
    }
  })

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    cards = cards.filter(card => {
      const name = getCardDisplayName(card).toLowerCase()
      const company = card.company?.toLowerCase() || ''
      const email = card.email?.toLowerCase() || ''
      return name.includes(query) || company.includes(query) || email.includes(query)
    })
  }

  if (props.excludeContactId) {
    cards = cards.filter(card => card.contactId !== props.excludeContactId)
  }

  return cards.length
})

const totalPages = computed(() => Math.ceil(totalCards.value / itemsPerPage))

// Methods
const getCardDisplayName = (card: BusinessCard): string => {
  if (card.name) return card.name
  
  const firstName = card.first_name || card.firstName || ''
  const lastName = card.last_name || card.lastName || ''
  const fullName = `${firstName} ${lastName}`.trim()
  
  return fullName || 'Unknown Contact'
}

const getStatusLabel = (status: string): string => {
  switch (status) {
    case 'approved':
      return 'User Card'
    case 'admin-approved':
      return 'Admin Approved'
    default:
      return 'Pending'
  }
}

const formatDate = (date: Date | string): string => {
  try {
    const d = typeof date === 'string' ? new Date(date) : date
    return d.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  } catch {
    return 'Unknown date'
  }
}

const selectCard = (card: BusinessCard) => {
  selectedCard.value = selectedCard.value?.id === card.id ? null : card
}

const confirmSelection = () => {
  if (selectedCard.value) {
    emit('cardSelected', selectedCard.value)
  }
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

// Reset page when search or filter changes
watch([searchQuery, statusFilter], () => {
  currentPage.value = 1
})

// Initialize
onMounted(() => {
  subscribeToBusinessCards()
})
</script>

<style scoped>
.business-card-item {
  position: relative;
}

.business-card-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.business-card-item.selected {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}
</style>