<template>
  <div class="business-card-form">
    <!-- Header -->
    <div class="form-header">
      <h2 class="form-title">Contact Information</h2>
      <p class="form-subtitle">Upload a business card to auto-fill this form</p>
    </div>

    <!-- Business Card Upload Section -->
    <div class="upload-section">
      <div 
        class="upload-area"
        :class="{ 
          'drag-over': isDragOver,
          'processing': isProcessing,
          'has-result': hasResult
        }"
        @dragover.prevent="handleDragOver"
        @dragleave.prevent="handleDragLeave"
        @drop.prevent="handleDrop"
        @click="triggerFileInput"
      >
        <input
          ref="fileInput"
          type="file"
          accept="image/*"
          class="file-input"
          @change="handleFileSelect"
        />

        <div v-if="!isProcessing && !hasResult" class="upload-content">
          <Icon name="material-symbols:upload" class="upload-icon" />
          <h3>Drop business card here</h3>
          <p>or click to select image</p>
          <div class="supported-formats">
            <span class="format-tag">JPEG</span>
            <span class="format-tag">PNG</span>
            <span class="format-tag">WebP</span>
          </div>
        </div>

        <div v-if="isProcessing" class="processing-content">
          <div class="spinner" />
          <h3>Processing Business Card...</h3>
          <div class="progress-bar">
            <div 
              class="progress-fill" 
              :style="{ width: `${progress}%` }"
            />
          </div>
          <p class="progress-text">{{ progress }}%</p>
        </div>

        <div v-if="hasResult && !isProcessing" class="result-preview">
          <Icon name="material-symbols:check-circle" class="success-icon" />
          <h3>Processing Complete!</h3>
          <p>Form has been auto-filled with extracted data</p>
          <button @click="reset" class="reset-btn">
            <Icon name="material-symbols:refresh" />
            Process Another Card
          </button>
        </div>
      </div>

      <!-- Error Display -->
      <div v-if="error" class="error-message">
        <Icon name="material-symbols:error" />
        <span>{{ error }}</span>
        <button @click="clearError" class="error-close">×</button>
      </div>
    </div>

    <!-- Contact Form -->
    <form @submit.prevent="handleSubmit" class="contact-form">
      <div class="form-grid">
        <!-- Name Fields -->
        <div class="form-group">
          <label for="firstName">First Name</label>
          <input
            id="firstName"
            v-model="formData.firstName"
            type="text"
            class="form-input"
            :class="{ 'auto-filled': autoFilledFields.firstName }"
            placeholder="Enter first name"
          />
        </div>

        <div class="form-group">
          <label for="lastName">Last Name</label>
          <input
            id="lastName"
            v-model="formData.lastName"
            type="text"
            class="form-input"
            :class="{ 'auto-filled': autoFilledFields.lastName }"
            placeholder="Enter last name"
          />
        </div>

        <!-- Company Information -->
        <div class="form-group full-width">
          <label for="company">Company</label>
          <input
            id="company"
            v-model="formData.company"
            type="text"
            class="form-input"
            :class="{ 'auto-filled': autoFilledFields.company }"
            placeholder="Enter company name"
          />
        </div>

        <div class="form-group full-width">
          <label for="jobTitle">Job Title</label>
          <input
            id="jobTitle"
            v-model="formData.jobTitle"
            type="text"
            class="form-input"
            :class="{ 'auto-filled': autoFilledFields.jobTitle }"
            placeholder="Enter job title"
          />
        </div>

        <!-- Contact Information -->
        <div class="form-group">
          <label for="email">Email</label>
          <input
            id="email"
            v-model="formData.email"
            type="email"
            class="form-input"
            :class="{ 'auto-filled': autoFilledFields.email }"
            placeholder="Enter email address"
          />
        </div>

        <div class="form-group">
          <label for="phone">Phone</label>
          <input
            id="phone"
            v-model="formData.phone"
            type="tel"
            class="form-input"
            :class="{ 'auto-filled': autoFilledFields.phone }"
            placeholder="Enter phone number"
          />
        </div>

        <!-- Website -->
        <div class="form-group full-width">
          <label for="website">Website</label>
          <input
            id="website"
            v-model="formData.website"
            type="url"
            class="form-input"
            :class="{ 'auto-filled': autoFilledFields.website }"
            placeholder="Enter website URL"
          />
        </div>

        <!-- Address -->
        <div class="form-group full-width">
          <label for="address">Address</label>
          <textarea
            id="address"
            v-model="formData.address"
            class="form-textarea"
            :class="{ 'auto-filled': autoFilledFields.address }"
            placeholder="Enter full address"
            rows="3"
          />
        </div>

        <!-- Social Media -->
        <div class="form-group">
          <label for="linkedin">LinkedIn</label>
          <input
            id="linkedin"
            v-model="formData.linkedin"
            type="url"
            class="form-input"
            :class="{ 'auto-filled': autoFilledFields.linkedin }"
            placeholder="LinkedIn profile URL"
          />
        </div>

        <div class="form-group">
          <label for="twitter">Twitter</label>
          <input
            id="twitter"
            v-model="formData.twitter"
            type="text"
            class="form-input"
            :class="{ 'auto-filled': autoFilledFields.twitter }"
            placeholder="Twitter handle"
          />
        </div>
      </div>

      <!-- Form Actions -->
      <div class="form-actions">
        <button type="button" @click="clearForm" class="btn-secondary">
          Clear Form
        </button>
        <button type="submit" class="btn-primary" :disabled="!isFormValid">
          Save Contact
        </button>
      </div>
    </form>

    <!-- Processing Info -->
    <div v-if="lastResult && hasResult" class="processing-info">
      <h4>Processing Details</h4>
      <div class="info-grid">
        <div class="info-item">
          <span class="label">Confidence:</span>
          <span class="value">{{ Math.round((lastResult.confidence || 0) * 100) }}%</span>
        </div>
        <div class="info-item">
          <span class="label">Processing Time:</span>
          <span class="value">{{ lastResult.processingTime }}ms</span>
        </div>
        <div class="info-item">
          <span class="label">Engine:</span>
          <span class="value">{{ lastResult.engine }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { useBusinessCardProcessor } from '~/composables/useBusinessCardProcessor'

// Props
interface Props {
  initialData?: Record<string, any>
  customFieldMapping?: Record<string, string>
}

const props = withDefaults(defineProps<Props>(), {
  initialData: () => ({}),
  customFieldMapping: () => ({})
})

// Emits
const emit = defineEmits<{
  submit: [data: Record<string, any>]
  autoFill: [data: Record<string, any>]
  error: [error: string]
}>()

// Business card processor
const {
  isProcessing,
  progress,
  error,
  lastResult,
  hasResult,
  extractedData,
  processBusinessCard,
  autoFillForm,
  reset,
  clearError
} = useBusinessCardProcessor()

// Component state
const isDragOver = ref(false)
const fileInput = ref<HTMLInputElement>()

// Form data
const formData = reactive({
  firstName: '',
  lastName: '',
  company: '',
  jobTitle: '',
  email: '',
  phone: '',
  website: '',
  address: '',
  linkedin: '',
  twitter: '',
  ...props.initialData
})

// Track which fields were auto-filled
const autoFilledFields = reactive({
  firstName: false,
  lastName: false,
  company: false,
  jobTitle: false,
  email: false,
  phone: false,
  website: false,
  address: false,
  linkedin: false,
  twitter: false
})

// Computed
const isFormValid = computed(() => {
  return formData.firstName.trim() && formData.lastName.trim() && formData.email.trim()
})

// Watch for successful processing to auto-fill form
watch(extractedData, (data) => {
  if (data) {
    const originalData = { ...formData }
    
    // Apply auto-fill with custom mapping
    const fieldMapping = {
      firstName: 'firstName',
      lastName: 'lastName',
      company: 'company',
      jobTitle: 'jobTitle',
      email: 'email',
      phone: 'phone',
      website: 'website',
      address: 'address.full',
      linkedin: 'linkedin',
      twitter: 'twitter',
      ...props.customFieldMapping
    }
    
    autoFillForm(formData, fieldMapping)
    
    // Track which fields were auto-filled
    Object.keys(autoFilledFields).forEach(field => {
      autoFilledFields[field] = originalData[field] !== formData[field] && !!formData[field]
    })
    
    emit('autoFill', { ...formData })
  }
})

// File handling
const handleDragOver = () => {
  isDragOver.value = true
}

const handleDragLeave = () => {
  isDragOver.value = false
}

const handleDrop = (event: DragEvent) => {
  isDragOver.value = false
  const files = Array.from(event.dataTransfer?.files || [])
  if (files.length > 0) {
    processFile(files[0])
  }
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = Array.from(target.files || [])
  if (files.length > 0) {
    processFile(files[0])
  }
}

const triggerFileInput = () => {
  if (!isProcessing.value) {
    fileInput.value?.click()
  }
}

// Process uploaded file
const processFile = async (file: File) => {
  try {
    await processBusinessCard(file)
  } catch (err: any) {
    emit('error', err.message)
  }
}

// Form actions
const handleSubmit = () => {
  if (isFormValid.value) {
    emit('submit', { ...formData })
  }
}

const clearForm = () => {
  Object.keys(formData).forEach(key => {
    formData[key] = ''
  })
  Object.keys(autoFilledFields).forEach(key => {
    autoFilledFields[key] = false
  })
}
</script>

<style scoped>
.business-card-form {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.form-header {
  text-align: center;
  margin-bottom: 2rem;
}

.form-title {
  font-size: 2rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.form-subtitle {
  color: #6b7280;
  font-size: 1.1rem;
}

.upload-section {
  margin-bottom: 2rem;
}

.upload-area {
  border: 2px dashed #d1d5db;
  border-radius: 0.75rem;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-area:hover {
  border-color: #3b82f6;
  background-color: #f8fafc;
}

.upload-area.drag-over {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.upload-area.processing {
  border-color: #f59e0b;
  background-color: #fffbeb;
  cursor: not-allowed;
}

.upload-area.has-result {
  border-color: #10b981;
  background-color: #f0fdf4;
}

.file-input {
  display: none;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.upload-icon {
  font-size: 2.5rem;
  color: #9ca3af;
}

.supported-formats {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.format-tag {
  background: #e5e7eb;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  color: #374151;
}

.processing-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.progress-bar {
  width: 200px;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #3b82f6;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.result-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.success-icon {
  font-size: 2.5rem;
  color: #10b981;
}

.reset-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #6b7280;
  color: white;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s;
}

.reset-btn:hover {
  background: #4b5563;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.5rem;
  color: #dc2626;
  margin-top: 1rem;
}

.error-close {
  margin-left: auto;
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: #dc2626;
}

.contact-form {
  background: #f9fafb;
  padding: 2rem;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.form-input,
.form-textarea {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.auto-filled,
.form-textarea.auto-filled {
  background-color: #f0fdf4;
  border-color: #10b981;
  animation: highlight 2s ease-in-out;
}

@keyframes highlight {
  0% { background-color: #dcfce7; }
  100% { background-color: #f0fdf4; }
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.btn-primary,
.btn-secondary {
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-primary:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.btn-secondary {
  background: transparent;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #f9fafb;
}

.processing-info {
  margin-top: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

.processing-info h4 {
  margin-bottom: 1rem;
  color: #374151;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: #f9fafb;
  border-radius: 0.25rem;
}

.info-item .label {
  font-size: 0.875rem;
  color: #6b7280;
}

.info-item .value {
  font-weight: 600;
  color: #374151;
}

@media (max-width: 640px) {
  .business-card-form {
    padding: 1rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .upload-area {
    padding: 1.5rem 1rem;
  }
}
</style>
