<script setup lang="ts">
const formsItem = ref({
  title: "Items",
  filters: {
    index: "omni",
    collection: "items",
    queryBy: "",
    route_add: "/c/items-create",

    description: "Items",
    areas: ["Items Information"],
    filterBy:
      "Touched Records, Untouched Records, Record Action, Related Records Action",
  },
  data: [
    {
      label: "id",
      val: "id",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Created At",
      val: "created_at",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Updated at",
      val: "updated_at",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "User ownership",
      val: "user_own",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Users with access",
      val: "user_access",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Space ownership",
      val: "space_own",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Space with access",
      val: "space_access",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Title",
      val: "title",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
    {
      label: "Tags (comma seperated)",
      val: "tags",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsTags",
      action: "forms-actions-tags",
    },
    {
      label: "Asking Price",
      val: "price",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "number",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
    {
      label: "Listed By",
      val: "selling_by",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [
        "Owner",
        "Friend",
        "Family Member",
        "Business",
        "Consignment",
        "Other",
      ],
      component: "FormsInputsSelect",
      action: "forms-actions-select",
    },
    {
      label: "Pickup/Delivery/Shipping Options",
      val: "pickup_delivery",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [
        "Local Pickup Only",
        "Shipping Available",
        "Free Shipping",
        "Delivery Available",
      ],
      component: "FormsInputsSelect",
      action: "forms-actions-select",
    },
    {
      label: "Availability",
      val: "availability",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [
        "Immediately Available",
        "Pre-Order",
        "Limited Availability",
        "Backorder",
        "Seasonal",
      ],
      component: "FormsInputsSelect",
      action: "forms-actions-select",
    },
    {
      label: "Description",
      val: "description",
      value: "",
      class: "col-span-2",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsQuill",
      action: "forms-actions-quill",
    },
    {
      label: "Condition",
      val: "condition",
      value: "",
      class: "col-span-2",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsQuill",
      action: "forms-actions-quill",
    },
    {
      label: "Location",
      val: "location",
      value: "",
      class: "col-span-2",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "GoogleAutocomplete",
      action: "forms-actions-quill",
    },
    {
      label: "Special",
      val: "image",
      value: "",
      class: "col-span-2",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "Image",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: true,
      optionsUrl: "plain",
      component: "FormsInputsUpload",
      action: "forms-actions-docs",
    },
    {
      label: "Status",
      val: "status",
      value: "Pending",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: true,
      small_table: false,
      small_form: false,
      editable: false,
      card: false,
      card_type: "Image",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      component: "FormsInputsSelect",
      action: "forms-actions-select",
      options: ["Approved", "Pending", "Declined"],
    },
  ],
});

const info = ref({
  title: "Items",
});

const selectedItems = useState("selectedItems", () => {
  return {};
});

const selected = (item: any) => {
  selectedItems.value = item;
  useRouter().push("/c/items-individual");
};
const edited = (item: any) => {
  selectedItems.value = item;
  useRouter().push("/c/items-edit");
};
const extraInfo: any = ref({
  view: false,
  edit: false,
  delete: false,
  add: false,
  export: false,
  import: false,
  ai: false,
});

onMounted(() => {
  let res = getAccess("CRM", "Leads");
  if (res) {
    extraInfo.value = res;
  }
});
const filter = ref({});
</script>

<template>
  <div>
    <client-only>
      <table-layout
        @selected="selected"
        @edited="edited"
        :info="info"
        :filter="filter"
        :extraInfo="extraInfo"
        :formsItem="formsItem"
        :filters="formsItem.filters"
        :forms="formsItem.data"
      />
    </client-only>
  </div>
</template>