<script setup lang="ts">
let viewProps = defineProps({
    user: {
        type: Object,
        default: () => {
            return {}
        }
    }
})
const { itemsSelect } = useCRM();
const { data: formsItem } = await useFetch<any>("/api/crm/forms", {
  pick: ["items"],
});
const { setToId } = database();
const dataselect = useState("selectedItems", () => {
  return {};
});

const notificationOpen = useState<any>("notificationOpen", () => {
  return {
    open: false,
    title: "",
    text: "",
    color: "o_gradient",
    type: "normal",
    icon: "info",
    button_text: "OK",
  };
});

async function save() {
  let payload: any = createData(itemsSelect.value);

  let prodset = await setToId(
    itemsSelect.value.id ? itemsSelect.value.id : makeid(16),
    formsItem.value.items.filters.collection,
    payload
  );
  notificationOpen.value = {
    open: true,
    title: itemsSelect.value.id ? "Updated" : "Created",
    text: `${formsItem.value.items.title} has been submitted`,
    color: "o_gradient",
    type: "normal",
    icon: "info",
    button_text: "OK",
  };
  itemsSelect.value = {};
  dataselect.value = "";
  useRouter().back();
}

onMounted(() => {
  if (viewProps.user) {
    itemsSelect.value = viewProps.user;
    dataselect.value = viewProps.user;
  }
});

const addImage = ref(false);

function imageLoaded(data: any) {
  if (itemsSelect.value.images) itemsSelect.value.images.push(...data);
  else itemsSelect.value.images = [...data];

  //remove any duplicates
  itemsSelect.value.images = itemsSelect.value.images.filter(
    (item: any, index: any, self: any) =>
      index === self.findIndex((t: any) => t.src === item.src)
  );
}
function deleteImage(index: number) {
  itemsSelect.value.images.splice(index, 1);
}

const editImage = ref(false);

function changeEditImage() {
  editImage.value = !editImage.value;
}

const input = (data) => {
  if (data && data.path === undefined && data.bubbles === undefined) {
    itemsSelect.value = { ...itemsSelect.value, ...data };
  }
};
</script>

<template>
  
      <div class="grid lg:grid-cols-12">
        <div
          class="w-full max-w-full px-3 mt-6 shrink-0 lg:flex-0 lg:col-span-8 lg:mt-0"
        >
          <div
            class="relative flex flex-col min-w-0 break-words border-0 o_card"
          >
            <div class="flex-auto p-6">
              <!-- <h5 class="text-2xl o_label">Contact Information</h5> -->
              <div class="flex flex-wrap -mx-3">
                <forms-small-view
                  :index="1"
                  :formsItem="formsItem.items"
                  :data="dataselect"
                  @input="input"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="w-full max-w-full px-3 shrink-0 lg:col-span-4">
          <div class="h-full o_card">
            <div class="flex-auto">
              <!-- <h5 class="text-2xl o_label">Image</h5> -->
              <div class="flex flex-wrap -mx-3">
                <div
                  class="w-full max-w-full px-3 flex-0"
                  v-if="itemsSelect.images"
                >
                  <img
                    v-if="itemsSelect.images.length > 0"
                    class="w-full mt-4 shadow-xl rounded-xl"
                    :src="
                      itemsSelect.images[0] ? itemsSelect.images[0].src : ''
                    "
                    alt="product_image"
                  />
                </div>
                <!-- <div class="w-full max-w-full px-3 mt-6 flex-0">
                  <div class="flex">
                    <modal-full :open="addImage" @close="addImage = false">
                      <template #body>
                        <file-manager @input="imageLoaded" />
                      </template>
                    </modal-full>
                    <button
                      type="button"
                      class="mr-2 o_btn_accent_border"
                      @click="addImage = !addImage"
                    >
                      Add
                    </button>
                    <button type="button" class="o_btn_warning_border">
                      Remove
                    </button>
                  </div>
                </div> -->
              </div>
              <div v-if="itemsSelect.images">
                <div
                  v-if="itemsSelect.images.length > 0"
                  class="flex w-full pt-2 mt-6 overflow-x-auto"
                >
                  <figure
                    v-for="(img, index) in itemsSelect.images"
                    :key="index"
                    class="relative w-24 h-24 px-1 mb-4 text-white"
                  >
                    <Icon
                      name="mdi:delete"
                      class="absolute top-0 right-0 z-50 w-6 h-6 bg-red-600 rounded-full cursor-pointer text-base_100 shadow-soft-md hover:shadow-soft-lg"
                      @click="deleteImage(index)"
                    />
                    <img
                      class="h-24 shadow-xl rounded-xl"
                      :src="img.src"
                      alt="Image description"
                    />
                  </figure>
                </div>
              </div>
            </div>
            <div class="mt-4">
              <forms-small-view
                :index="3"
                :formsItem="formsItem.items"
                :data="dataselect"
                @input="input"
              />
              <hr class="my-4 text-base_100" />
              <forms-small-view
                :index="2"
                :formsItem="formsItem.items"
                :data="dataselect"
                @input="input"
              />
            </div>
          </div>
        </div>
       
      </div>
    
</template>
