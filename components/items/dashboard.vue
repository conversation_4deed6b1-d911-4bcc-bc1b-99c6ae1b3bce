<script setup lang="ts">
const selectedItems = useState('selectedItems', ()=> { return {}})
onMounted(()=> {
  selectedItems.value = {}
})
  </script>
  
  <template>
        <layouts-main title="Items">
          <template #buttons><tooltip-icon text="Dashboard" side="left" icon="line-md:grid-3-filled" @submit="$router.push('/c/dashboard')"/></template>
        <template #content> 
        <div class="w-full overflow-hidden">
          <items-lists />
        </div>
        </template>
      </layouts-main>
  </template>