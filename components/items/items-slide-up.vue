<script setup lang="ts">
const props = defineProps({
  schema: {
    type: Object,
    default: () => ({})
  }
});

// Format price with currency symbol
const formatPrice = (price: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(price);
};
</script>

<template>
  <div class="relative h-full overflow-hidden group">
    <!-- Image Section -->
    <div class="h-48 overflow-hidden bg-gray-200">
      <img 
        v-if="schema.image" 
        :src="schema.image" 
        :alt="schema.title" 
        class="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
      >
      <div v-else class="flex items-center justify-center h-full bg-gray-200">
        <svg xmlns="http://www.w3.org/2000/svg" class="w-16 h-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      </div>
    </div>
    
    <!-- Content Section -->
    <div class="p-4">
      <h3 class="mb-2 text-lg font-semibold text-gray-800 dark:text-white">{{ schema.title || 'Untitled Item' }}</h3>
      <p class="mb-4 text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
        {{ schema.description || 'No description available' }}
      </p>
      
      <!-- Price -->
      <div class="mb-2 text-lg font-bold text-blue-600">
        {{ schema.price ? formatPrice(schema.price) : 'Price not available' }}
      </div>
      
      <!-- Status Badge -->
      <div class="absolute top-2 right-2">
        <span 
          class="px-2 py-1 text-xs font-medium rounded-full"
          :class="{
            'bg-green-100 text-green-800': schema.status === 'Approved',
            'bg-yellow-100 text-yellow-800': schema.status === 'Pending',
            'bg-red-100 text-red-800': schema.status === 'Declined',
            'bg-gray-100 text-gray-800': !schema.status
          }"
        >
          {{ schema.status || 'Unknown' }}
        </span>
      </div>
      
      <!-- Date -->
      <div class="absolute bottom-2 right-2 text-xs text-gray-500 dark:text-gray-400">
        {{ schema.created_at ? new Date(schema.created_at).toLocaleDateString() : 'No date' }}
      </div>
    </div>
    
    <!-- Hover Overlay -->
    <div class="absolute inset-0 flex items-center justify-center transition-opacity duration-300 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100">
      <button class="px-4 py-2 font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
        View Item
      </button>
    </div>
  </div>
</template>
