<script setup lang="ts">

const selectedItems: any = useState("selectedItems", () => {
  return "";
});

const schema = computed(()=> {
  return {
    name: 'test'
  }
})
</script>

<template>
  <div>
    <layouts-main
      :title="selectedItems.name"
    >
      <template #buttons>
        <tooltip-icon
          text="List"
          side="left"
          icon="material-symbols:list-alt-sharp"
          @submit="$router.push('/c/items')"
        />
        <tooltip-icon
          text="Edit"
          side="bottom"
          icon="mdi:pencil"
          @submit="$router.push('/c/items-edit')"
        />
       
      </template>
      <template #content>
        <div class="w-full">
          <items-slide-up :schema="selectedItems" />
        </div>
      </template>
    </layouts-main>
  </div>
</template>
