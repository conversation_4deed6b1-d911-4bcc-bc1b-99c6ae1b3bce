<script setup lang="ts">
defineProps({
  open: {
    type: Boolean,
    default: () => {
      return true;
    },
  },
});
const { data: formsItem } = await useFetch<any>("/api/covalonic/forms", {
  pick: ["items"],
});
const userSpaces = useState<any>("userSpaces", () => {
    return [];
  });
const created = (data: any) => {
 userSpaces.value.push(data)
};
const selectedItems: any = useState("selectedItems", () => {
  return "";
});

</script>

<template>
    <layouts-main
      :title="selectedItems.name"
    >
      <template #buttons>
        <tooltip-icon
          text="List"
          side="left"
          icon="material-symbols:list-alt-sharp"
          @submit="$router.push('/c/items')"
        />
        <tooltip-icon
          text="Go to Item"
          side="bottom"
          icon="ic:outline-brightness-1"
          @submit="$router.push('/c/items-individual')"
        />
       
      </template>
      <template #content>
  <div class="o_card">
    <forms-normal-update :item="selectedItems" @created="created" :open="open" :formsItem="formsItem.items" title="Create Flyer"/>
  </div>
  </template>
  </layouts-main>
</template>
