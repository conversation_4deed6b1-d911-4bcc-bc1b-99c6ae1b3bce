<script setup lang="ts">

</script>
<template>
      <main class="w-full">
        <div class="mx-auto">
          <div class="flex flex-row flex-wrap">
            <div class="flex-shrink w-full max-w-full px-4 mb-6 lg:w-1/2 h-full">
              <slot name="content-1" />
            </div>
            <div class="flex-shrink w-full max-w-full px-4 lg:w-1/2">
              <slot name="content-2" />
            </div>
          </div>
          <!-- row -->
          <div class="flex flex-row flex-wrap">
            <div class="flex-shrink w-full max-w-full px-4 mb-6 lg:w-1/3">
              <slot name="content-3" />
            </div>
            <div class="flex-shrink w-full max-w-full px-4 lg:w-1/3">
              <slot name="content-4" />

              
            </div>
            <div class="flex-shrink w-full max-w-full px-4 mb-6 lg:w-1/3">
              <slot name="content-5" />
            </div>
          </div>
          <!-- row -->
          <div class="flex flex-row flex-wrap">
            <slot name="content-6" />
           
          </div>
        </div>
      </main>
</template>
