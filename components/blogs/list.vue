<script setup lang="ts">
import moment from "moment";
const { blogsContent, blogsContentSelected } = useContent();
const { currentSpace } = space()
const currentUser: object = useState("currentUser", () => {
  return {};
});
const { getCollectionWhere } = database();

const router = useRouter();
const getData = async () => {
  const data = await queryByWhere2(
    "blogs-content",
    "spaces",
    currentSpace.value.id,
    "array-contains"
  );
  console.log('data', data)
  blogsContent.value = data.result;
};

getData();
const open = ref(false);
const openCreate = ref(false);
const selected = ref(1);
const item = ref("1");

const getPercentage = (pr: any) => {
  const base = pr.tasks ? pr.tasks : 1;
  const numerator = pr.completed ? pr.completed : 0;
  return (numerator * 100) / base;
};

const updateProject = (pr: any) => {
  blogsContentSelected.value = pr;
  router.push(`/c/blog-detail?id=${pr.id}`);
};
</script>
<template>
  <layouts-main title="Blogs">
    <template #buttons
      ><tooltip-icon
        text="Create a Blog"
        side="left"
        icon="mdi:plus"
        @submit="openCreate = !openCreate"
    /></template>
    <template #content>
      <modal-small
        :open="openCreate"
        @close="openCreate = false"
        title="Add Blog"
      >
        <template #body>
          <blogs-create @close="openCreate = false" />
        </template>
      </modal-small>
      <div class="w-full">
        <div
                  class="grid grid-cols-1 gap-5 lg:grid-cols-4 sm:grid-cols-2"
                >
                  <div
                    v-for="(blog, index) in blogsContent"
                    :key="index"
                    @click="updateProject(blog)"
                    class="relative flex items-end justify-start w-full text-left bg-center bg-cover cursor-pointer h-96 dark:bg-gray-500"
                    :style="`background-image: url(${
                      blog.background_image.src
                        ? blog.background_image.src
                        : 'https://source.unsplash.com/random/240x320'
                    });`"
                  >
                    <div
                      class="absolute top-0 bottom-0 left-0 right-0 bg-gradient-to-b dark:via-transparent dark:from-gray-900 dark:to-gray-900"
                    ></div>
                    <div
                      class="absolute top-0 left-0 right-0 flex items-center justify-between mx-5 mt-3"
                    >
                      <a
                        rel="noopener noreferrer"
                        href="#"
                        class="px-3 py-2 text-xs font-semibold tracking-wider uppercase dark:text-gray-100 bgundefined"
                        >{{ blog.title }}</a
                      >
                      <div
                        class="flex flex-col justify-start text-center dark:text-gray-100"
                      >
                        <span
                          class="text-3xl font-semibold leading-none tracking-wide"
                          >{{ moment(blog.created_date).format("DD") }}</span
                        >
                        <span class="leading-none uppercase">{{
                          moment(blog.created_date).format("MMM")
                        }}</span>
                      </div>
                    </div>
                    <h2 class="z-10 p-5">
                      <div
                        rel="noopener noreferrer"
                        href="#"
                        class="font-medium text-md hover:underline dark:text-gray-100"
                        v-html="blog.about"
                      ></div>
                    </h2>
                  </div>
                </div>
      </div>
    </template>
  </layouts-main>

</template>
