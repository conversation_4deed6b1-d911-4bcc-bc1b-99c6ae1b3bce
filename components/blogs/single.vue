<script setup lang="ts">
const { snippetsSelected, blogsContentSelected } = useContent();

const script = ref("");
const formSpaceRegister: any = useState("blogsave", () => {
  return {
    title: "",
    about: "",
    tags: [],
    blogs: "",
  };
});

watch(
  () => formSpaceRegister.value.blogs,
  () => {
    console.log("here", formSpaceRegister.value.blogs);
    script.value = formSpaceRegister.value.blogs;
    
  }
);

const input = (data: any) => {
  if (data && data.path === undefined && data.bubbles === undefined){
    script.value = data;
    formSpaceRegister.value.blogs = data
  }
    
};

const messages = (data: any) => {
  if (data && data.path === undefined && data.bubbles === undefined)
    blogsContentSelected.value.messages = data;
};
const snippetsshow = useState("snippetsshow", () => "list");
const snippetSingle: any = useState("snippets-single", () => {});

onMounted(() => {
  snippetsshow.value = "snippets";
  snippetsSelected.value = {};
  snippetSingle.value = {};
});

const extratext = ref("");
const edit = ref(false);
const usegpt = ref(false);
</script>

<template>
  <div>
    --
    <content-blogs-save :script="script" />
    
  </div>
</template>
