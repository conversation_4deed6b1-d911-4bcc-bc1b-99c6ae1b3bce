<script setup lang="ts">
const { addToCollection: addToCollectionSpaceRegister } = database()
const currentUser: object = useState('currentUser', () => {
        return {}
    })
const { $swal } = useNuxtApp()
const { blogsContent } = useContent()
const open = ref(false)
const spaceInstance: any = getCurrentInstance()
const formSpaceRegister: any = ref({
  title: '',
  about: '',
  type: '',
  tags: [],
  members: [currentUser.value.id],
  invitees: [],
  other: '',
  approval_needed: false,
  approvals: [],
  status: 'Pending',
  emails: [],
  logo: {},
    background_image: {},

})
async function submitNetcashRegistration() {

  try {
  let payload = formSpaceRegister.value

    const res = await addToCollectionSpaceRegister('blogs-content', payload)
    const payloads = { ...payload, ...res }
    blogsContent.value.push(payloads)

    $swal.fire({
      title: 'Success!',
      text: `Yeah, enjoy`,
      icon: 'success',
      confirmButtonText: 'Cool'
    })
  }
  catch (e) {
    console.error(e)
  }

  formSpaceRegister.value = {
    title: '',
    about: '',
    type: '',
    tags: [],
    members: [currentUser.value.id],
    invitees: [],
    other: '',
    approval_needed: false,
    approvals: [],
    status: 'Pending',
    emails: [],
    market: false,
    logo: {},
    background_image: {},

  }
  spaceInstance.emit('close')
}

const emailTypes = ref([
'Travel Blog',
'Fashion Blog',
'Lifestyle Blog',
'Food Blog',
'Parenting Blog',
'Health & Fitness Blog',
'Music Blog',
'Outdoor/ Adventure Blog',
'DIY Blog',
'Personal Blog',
'Product/ Gadget Review Blog',
'News/ Politics Blog',
'Education/ Learning Blog',
'Business/ Entrepreneurship Blog',
'Pet Blog',
'Sports Blog',
'Art/ Design Blog',
'Comedy Blog',
'Photo Blog',
'Video Blog',
  'Other'
])

const schema: any = ref({
  options: {
    input_class: { value: 'o_input' },
    search_button_class: { value: 'o_btn_icon_square' },
    icon: { value: 'mdi:magnify' },
  }
})

const userinvite = (us: { id: any; }) => {
  console.log(us)
  //see if in array, if in array then delete, if not in array then add
  if (formSpaceRegister.value.invitees.includes(us.id)) {
    formSpaceRegister.value.invitees = formSpaceRegister.value.invitees.filter((item: any) => item !== us.id)
  } else {
    formSpaceRegister.value.invitees.push(us.id)
  }
  // formSpaceRegister.value.invitees.push(us.id)
}

const userapproval = (us: { id: any; }) => {
  console.log(us)
  //see if in array, if in array then delete, if not in array then add
  if (formSpaceRegister.value.approvals.includes(us.id)) {
    formSpaceRegister.value.approvals = formSpaceRegister.value.approvals.filter((item: any) => item !== us.id)
  } else {
    formSpaceRegister.value.approvals.push(us.id)
  }
  // formSpaceRegister.value.invitees.push(us.id)
}

const input = (data) => {
  if (data && data.path === undefined && data.bubbles === undefined)
    formSpaceRegister.value.about = data
}

const uploadAdd = (data: any) => {
  console.log(data)
  formSpaceRegister.value.images = data
}

function setIcon(e: any) {
  formSpaceRegister.value.logo = {
    src: e[0].src,
    name: e[0].name,
    type: e[0].type,
  };
}

function setBackground(e: any) {
  formSpaceRegister.value.background_image = {
    src: e[0].src,
    name: e[0].name,
    type: e[0].type,
  };
}

const TagsSelected = (data: any)=> {
  if (data && data.path === undefined && data.bubbles === undefined)
  formSpaceRegister.value.tags = data
}
</script>
<template>

                  <div class="flex flex-row flex-wrap -mx-4">
                    <div class="flex-shrink w-full max-w-full px-4 mb-4">
                      <label for="inputtitle" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Title</label>
                      <input
                        v-model="formSpaceRegister.title"
                        type="text"
                        class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
                        id="inputtitle"
                        placeholder="Enter blog title"
                      >
                    </div>
                    <div class="flex-shrink w-full max-w-full px-4 mb-4">
                      <label for="inputbudget" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">About</label>
                      <forms-inputs-quill
                        @input="input"
                        :placeholder="formSpaceRegister.about"
                        class="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm"
                      />
                    </div>
                    <div class="flex-shrink w-full max-w-full px-4 mb-4 md:w-1/2">
                      <label for="inputtask" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Type</label>
                      <select
                        v-model="formSpaceRegister.type"
                        class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
                        id="inputtask"
                      >
                        <option value="" disabled selected>Select blog type</option>
                        <option v-for="(type, index) in emailTypes" :key="type" :value="type">{{ type }}</option>
                      </select>
                    </div>
                    <div class="flex-shrink w-full max-w-full px-4 mb-4 md:w-1/2">
                      <client-only>
                        <label for="inputtitle" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tags <small>(comma separated)</small></label>
                        <TagsSelect
                          type="snippets"
                          :show-add="true"
                          @input="TagsSelected"
                        />
                      </client-only>
                    </div>

                    <div class="flex-shrink w-full max-w-full px-4 mb-4" v-if="formSpaceRegister.type == 'Other'">
                      <label for="inputother" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Other</label>
                      <input
                        v-model="formSpaceRegister.other"
                        type="text"
                        class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
                        id="inputother"
                        placeholder="Specify blog type"
                      >
                    </div>

                    <div class="grid md:grid-cols-2 gap-4 w-full px-4 mb-4">
                      <div>
                        <label for="inputlogo" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Logo</label>
                        <file-button @uploadAdd="setIcon" />
                        <file-view :images="[formSpaceRegister.logo]" />
                      </div>
                      <div>
                        <label for="inputbackground" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Background Image</label>
                        <file-button @uploadAdd="setBackground" />
                        <file-view :images="[formSpaceRegister.background_image]" />
                      </div>
                    </div>

                    <div class="flex-shrink w-full max-w-full px-4 mt-2">
                      <button
                        @click="submitNetcashRegistration"
                        class="px-6 py-2 text-sm font-medium text-white bg-[#0072ff] border border-transparent rounded-md shadow-sm hover:bg-[#0054bb] focus:outline-none focus:ring-2 focus:ring-[#0072ff] focus:ring-opacity-50 flex items-center"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                        Add New Blog
                      </button>
                    </div>
                  </div>

</template>