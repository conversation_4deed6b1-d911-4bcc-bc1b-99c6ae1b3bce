<script setup lang="ts">
let chatAppProp = defineProps({
    schema: {
        type: Object,
       default: () => ({})
    }
})
const { blogsContentSelected } = useContent()


const { getDoc } = database()

const id = computed(() => {
    if(chatAppProp.schema.options === undefined) {
        return null
    }else{
        return chatAppProp.schema.options.item_id.value

    }
})


onMounted(() => {
    if(id.value!== null){
        getItemById()
    }
})
watch(()=> id.value, ()=>{
    getItemById()
})
const getItemById = async () => {
    let item = await getDoc('blogs-content', id.value)
    console.log('item', item)
    blogsContentSelected.value = item.result
}

</script>

<template>
    <div class="flex justify-center w-full">
        <blogs-detail-posts v-if="blogsContentSelected.id"/>
    </div>
</template>