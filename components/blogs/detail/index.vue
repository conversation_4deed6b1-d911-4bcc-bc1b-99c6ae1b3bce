<script setup lang="ts">
import moment from "moment";
const { blogsContentSelected } = useContent();
const { getCollectionWhere, deleteDoc } = database();
const blogsSingle: any = useState("blogs-singles", () => []);

onMounted(async () => {
  let res = await queryByWhere2(
    "blogs-singles",
    "blogs_id",
    blogsContentSelected.value.id,
    "=="
  );
  
  blogsSingle.value = res.result;
});

const deleteblogs = async (snip: any) => {
  try {
    await deleteDoc("blogs-singles", snip.id);
    const index = blogsSingle.value.findIndex(
      (item: any) => item.id === snip.id
    );
    blogsSingle.value.splice(index, 1);
  } catch (e) {
    console.error(e);
  }
};
const formSpaceRegister: any = useState("blogsSave", () => {
  return {
    title: "",
    about: "",
    type: "",
    tags: [],
    blogs: [],
  };
});
const useblogs = (snip: any) => {
  formSpaceRegister.value = snip;
};
const router = useRouter();
const createBlog = () => {
    router.push(`/c/blogs-detail-create`)

};

const openCreate = ref(false);
const item = ref(1);
const open = ref(false);

const goToBlog = () => {
 window.open(useBrowserUrl() + '/c/app/blogs-app?id=' + blogsContentSelected.value.id, "_blank");
};
</script>
<template>

<layouts-main :title="blogsContentSelected.title">
        <template #buttons>
          <tooltip-icon text="Create Blog Post" side="left" icon="mdi:plus" @submit="openCreate = !openCreate"/></template>
      <template #content> 
        <modal
            :open="openCreate"
            @close="openCreate = false"
            title="Add Blog"
          >
            <template #body>
              <content-blogs-single @close="openCreate = false" />
            </template>
          </modal>
          <blogs-detail-posts />

        </template>
        </layouts-main>
  <!-- <div>
    <div
      class="font-sans text-base font-normal"
    >
      <div
        x-data="{ open: false }"
        class="overflow-x-hidden"
      >
        <div
          x-bind:aria-expanded="open"
          :class="{
            'ltr:ml-64 ltr:-mr-64 md:ltr:ml-0 md:ltr:mr-0 rtl:mr-64 rtl:-ml-64 md:rtl:mr-0 md:rtl:ml-0':
              open,
            'ltr:ml-0 ltr:mr-0 md:ltr:ml-64 rtl:mr-0 rtl:ml-0 md:rtl:mr-64':
              !open,
          }"
          class="flex flex-col min-h-screen transition-all duration-500 ease-in-out ltr:ml-64 rtl:mr-64"
        >
      

          <main class="pt-6 -mt-2">
            <div class="p-2 mx-auto">
              <div class="flex flex-row flex-wrap">
                <div class="flex-shrink w-full max-w-full px-4">
                  
                  <p class="mt-3 mb-5 text-xl font-bold cursor-pointer" @click="goToBlog">
                    {{ blogsContentSelected.title }} 
                  </p>
                </div>
                <div class="flex-shrink w-full mb-6">
                  <div
                    class="h-full px-6 py-4 rounded-lg shadow-lg theme_100"
                  >
                    <div class="flex flex-row flex-wrap">
                      <div class="flex-shrink w-full max-w-full">
                        <div class="md:flex md:justify-between">
                          <div
                            @click="createBlog"
                            class="block px-4 py-2 leading-5 text-center text-gray-100 bg-indigo-500 border border-indigo-500 rounded lg:inline-block hover:text-white hover:bg-indigo-600 hover:ring-0 hover:border-indigo-600 focus:bg-indigo-600 focus:border-indigo-600 focus:outline-none focus:ring-0"
                          >
                            Add new post
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="12"
                              height="12"
                              fill="currentColor"
                              class="inline-block ltr:ml-1 rtl:mr-1 bi bi-plus-lg"
                              viewBox="0 0 16 16"
                            >
                              <path
                                d="M8 0a1 1 0 0 1 1 1v6h6a1 1 0 1 1 0 2H9v6a1 1 0 1 1-2 0V9H1a1 1 0 0 1 0-2h6V1a1 1 0 0 1 1-1z"
                              ></path>
                            </svg>
                          </div>

                          <div
                            class="relative flex items-center w-full h-full mt-4 md:w-60 group md:mt-0 md:self-center"
                          >
                            <svg
                              class="absolute z-20 block w-4 h-4 text-gray-500 pointer-events-none fill-current ltr:left-0 rtl:right-0 ltr:ml-4 rtl:mr-4 group-hover:text-gray-400"
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 20 20"
                            >
                              <path
                                d="M12.9 14.32a8 8 0 1 1 1.41-1.41l5.35 5.33-1.42 1.42-5.33-5.34zM8 14A6 6 0 1 0 8 2a6 6 0 0 0 0 12z"
                              ></path>
                            </svg>
                            <input
                              type="text"
                              class="block w-full py-1.5 ltr:pl-10 ltr:pr-4 rtl:pr-10 rtl:pl-4 leading-normal rounded-2xl focus:outline-none bg-gray-100 border border-gray-100 focus:border-gray-200 focus:ring-0 dark:text-gray-400 dark:bg-gray-700 dark:border-gray-700 dark:focus:border-gray-700"
                              placeholder="Search server"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <blogs-detail-posts />
            </div>
          </main>
        </div>
      </div>
    </div>
  </div> -->
</template>
