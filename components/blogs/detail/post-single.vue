<script setup lang="ts">
import moment from 'moment'
let singlePostProps = defineProps({
    post: {
        type: Object,
        default: () => {return {}}
    }
})

const words = computed(() => {
    return singlePostProps.post.blogs.split(' ').length
})
const readingTime = computed(() => {
    return Math.ceil(words.value / 200)
})
</script>

<template>
  <div class="w-full">
    <div
      class="container px-8 py-5 lg:py-8 mx-auto xl:px-5 max-w-screen-lg !pt-0"
    >
      <div class="max-w-screen-md mx-auto">
        <div class="flex justify-center">
          <div class="flex gap-3">
            <a v-for="(tg, index) in post.tags" :key="index"
              ><span
                :class="`inline-block mt-5 text-xs font-medium tracking-wider text-${randomColorName()}-600 uppercase`"
                >{{tg}}</span
              ></a
            >
          </div>
        </div>
        <h1
          class="mt-2 mb-3 text-3xl font-semibold tracking-tight text-center lg:leading-snug text-brand-primarys lg:text-4xl dark:text-white"
        >
          {{post.title}}
        </h1>
        <div class="flex justify-center mt-3 space-x-3 text-gray-500">
          <div class="flex items-center gap-3">
            <div class="relative flex-shrink-0 w-10 h-10">
           
            </div>
            <div>
              <p class="text-gray-800 dark:text-gray-400">{{post.name}}</p>
              <div class="flex items-center space-x-2 text-sm">
                <time
                  class="text-gray-500 dark:text-gray-400"
                  datetime="2022-10-20T12:28:00.000Z" v-if="post.publish_date"
                  >{{post.publish_date.split('-')[0]}} {{ numberToShortMonth(Number(post.publish_date.split('-')[1])) }}, {{post.publish_date.split('-')[2]}}</time
                ><span>· {{ readingTime }} min read</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="relative z-0 max-w-screen-lg mx-auto overflow-hidden lg:rounded-lg aspect-video" v-if="post.background_image.src"
    >
      <span
        style="
          box-sizing: border-box;
          display: block;
          overflow: hidden;
          width: initial;
          height: initial;
          background: none;
          opacity: 1;
          border: 0px;
          margin: 0px;
          padding: 0px;
          position: absolute;
          inset: 0px;
        "
        ><img
          alt="Thumbnail"
          sizes="100vw"
        
          :src="post.background_image.src"
          decoding="async"
          data-nimg="fill"
          style="
            position: absolute;
            inset: 0px;
            box-sizing: border-box;
            padding: 0px;
            border: none;
            margin: auto;
            display: block;
            width: 0px;
            height: 0px;
            min-width: 100%;
            max-width: 100%;
            min-height: 100%;
            max-height: 100%;
            object-fit: cover;
          " /></span>
    </div>
    <div class="container max-w-screen-lg px-8 py-5 mx-auto lg:py-8 xl:px-5">
      <article class="max-w-screen-md mx-auto">
        <div
          class="mx-auto my-3 prose prose-base dark:prose-invert prose-a:text-blue-500"
          v-html="post.blogs"
        >
        </div>
        <div class="flex justify-center mt-7 mb-7">
          <a
            class="px-5 py-2 text-sm text-blue-600 rounded-full cursor-pointer dark:text-blue-500 bg-brand-secondary/20"
            @click="$emit('close')"
            >← View all posts</a
          >
        </div>
      </article>
    </div>
  </div>
</template>
