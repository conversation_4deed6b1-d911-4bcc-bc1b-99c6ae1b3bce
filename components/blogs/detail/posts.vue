<script setup lang="ts">
import moment from "moment";
const { blogsContentSelected } = useContent();
const { getCollectionWhere, deleteDoc } = database();
const blogsSingle: any = useState("blogs-singles", () => []);

onMounted(async () => {
  console.log(blogsContentSelected.value);
  let res = await queryByWhere2(
    "blogs-singles",
    "blogs_id",
    blogsContentSelected.value.id,
    "=="
  );

  blogsSingle.value = res.result;
});

const deleteblogs = async (snip: any) => {
  try {
    await deleteDoc("blogs-singles", snip.id);
    const index = blogsSingle.value.findIndex(
      (item: any) => item.id === snip.id
    );
    blogsSingle.value.splice(index, 1);
  } catch (e) {
    console.error(e);
  }
};
const formSpaceRegister: any = useState("blogsSave", () => {
  return {
    title: "",
    about: "",
    type: "",
    tags: [],
    blogs: [],
  };
});
const useblogs = (snip: any) => {
  formSpaceRegister.value = snip;
};

const openCreate = ref(false);
const item = ref("1");
const open = ref(false);
const showSingle = ref(false);
const singlePost = ref({});
const showSinglePost = (post: any) => {
  console.log("post title", post.title);
  singlePost.value = post;
  showSingle.value = true;
};
</script>
<template>
  <div class="w-full font-sans font-normal">
    <div v-if="showSingle" class="flex justify-center w-full">
      <blogs-detail-post-single
        :post="singlePost"
        @close="showSingle = false"
      />
    </div>
    <div v-else>
      <div>
        <div class="">
          <div
            class="grid grid-cols-1 gap-4 cursor-pointer md:grid-cols-2 lg:grid-cols-4 md:gap-0 lg:grid-rows-2"
          >
            <div
              v-if="blogsSingle.length > 0"
              class="relative flex items-end justify-start w-full text-left bg-center bg-cover cursor-pointer md:col-span-2 lg:row-span-2 h-96 group dark:bg-gray-500"
              :style="`background-image: url(${
                blogsSingle[0].background_image.src
                  ? blogsSingle[0].background_image.src
                  : 'https://source.unsplash.com/random/240x320'
              });`"
              @click="showSinglePost(blogsSingle[0])"
            >
              <div
                class="absolute top-0 bottom-0 left-0 right-0 bg-gradient-to-b dark:via-transparent dark:from-gray-900 dark:to-gray-900"
              ></div>
              <div
                class="absolute top-0 left-0 right-0 flex items-center justify-between mx-5 mt-3"
              >
                <a
                  class="px-3 py-2 text-xs font-semibold tracking-wider text-gray-100 uppercase hover:underline dark:bg-violet-400 bg-gray-600/50"
                  >{{ blogsSingle[0].title }}</a
                >
                <div
                  class="flex flex-col justify-start p-2 text-center text-gray-100 bg-gray-600/50"
                >
                  <span
                    class="text-3xl font-semibold leading-none tracking-wide"
                    >{{ blogsSingle[0].publish_date.split("-")[0] }}</span
                  >
                  <span class="leading-none uppercase">{{
                    numberToShortMonth(
                      Number(blogsSingle[0].publish_date.split("-")[1])
                    )
                  }}</span>
                </div>
              </div>
              <h2 class="z-10 p-5 bg-gray-600/50">
                <a
                  rel="noopener noreferrer"
                  href="#"
                  class="font-medium text-gray-100 text-md group-hover:underline lg:text-2xl lg:font-semibold"
                  v-html="blogsSingle[0].about"
                ></a>
              </h2>
            </div>
            <div
              v-for="(post, index) in blogsSingle.slice(1)"
              :key="index"
              @click="showSinglePost(post)"
              class="relative flex items-end justify-start w-full text-left bg-center bg-cover cursor-pointer h-96 group dark:bg-gray-500"
              :style="`background-image: url(${
                post.background_image.src
                  ? post.background_image.src
                  : 'https://source.unsplash.com/random/240x320'
              });`"
            >
              <div
                class="absolute top-0 bottom-0 left-0 right-0 bg-gradient-to-b dark:via-transparent dark:from-gray-900 dark:to-gray-900"
              ></div>
              <div
                class="absolute top-0 left-0 right-0 flex items-center justify-between mx-5 mt-3"
              >
                <div
                  class="px-3 py-2 text-xs font-semibold text-gray-100 uppercase hover:underline dark:text-gray-100 dark:bg-violet-400 bg-gray-600/50"
                >
                  {{ post.title }}
                </div>
                <div
                  class="flex flex-col justify-start text-center text-gray-100 dark:text-gray-100 bg-gray-600/50"
                >
                  <span
                    class="text-3xl font-semibold leading-none tracking-wide"
                    >{{ post.publish_date.split("-")[0] }}
                  </span>
                  <span class="leading-none uppercase text-base_100 p-1">{{
                    numberToShortMonth(Number(post.publish_date.split("-")[1]))
                  }}</span>
                </div>
              </div>
              <h2 class="z-10 p-5 text-gray-100 bg-gray-600/50" v-if="post.about">
                <a
                  class="font-medium text-md group-hover:underline dark:text-gray-100"
                  v-html="post.about"
                ></a>
              </h2>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
