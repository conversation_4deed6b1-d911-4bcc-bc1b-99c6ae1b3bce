<template>
  <div class="simplified-profile-view">
    <!-- Header -->
    <div class="profile-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mr-3 shadow-lg">
            <Icon name="mdi:account-circle" class="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 class="text-xl font-bold text-white">My Profile</h1>
            <p class="text-purple-300 text-sm">Manage your personal settings</p>
          </div>
        </div>
        <NuxtLink 
          to="/c/welcome"
          class="w-10 h-10 bg-white/10 hover:bg-white/20 rounded-xl flex items-center justify-center transition-colors shadow-lg"
        >
          <Icon name="mdi:home" class="w-5 h-5 text-white" />
        </NuxtLink>
      </div>
    </div>

    <!-- Profile Summary Card -->
    <div class="profile-summary bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 mb-6">
      <div class="flex items-center space-x-4 mb-4">
        <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-xl shadow-lg">
          {{ displayName.charAt(0).toUpperCase() }}
        </div>
        <div class="flex-1">
          <h2 class="text-white font-semibold text-lg">{{ displayName }}</h2>
          <p class="text-purple-200 text-sm">{{ userEmail }}</p>
        </div>
      </div>
      
      <!-- Quick Stats -->
      <div class="grid grid-cols-2 gap-3">
        <div class="bg-purple-500/20 rounded-lg p-3 text-center">
          <Icon name="mdi:calendar-check" class="w-6 h-6 text-purple-300 mx-auto mb-1" />
          <p class="text-white text-sm font-medium">Member Since</p>
          <p class="text-purple-200 text-xs">{{ memberSince }}</p>
        </div>
        <div class="bg-blue-500/20 rounded-lg p-3 text-center">
          <Icon name="mdi:shield-account" class="w-6 h-6 text-blue-300 mx-auto mb-1" />
          <p class="text-white text-sm font-medium">Account Type</p>
          <p class="text-blue-200 text-xs">{{ accountType }}</p>
        </div>
      </div>
    </div>

    <!-- Business Card Status -->
    <div class="business-card-status bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 mb-6">
      <div class="flex items-center justify-between mb-3">
        <h3 class="text-white font-semibold flex items-center">
          <Icon name="mdi:card-account-details" class="w-5 h-5 mr-2 text-green-300" />
          My Business Card
        </h3>
        <span v-if="hasBusinessCard" class="px-2 py-1 bg-green-500/20 text-green-300 text-xs rounded-full border border-green-400/30">
          Active
        </span>
        <span v-else class="px-2 py-1 bg-orange-500/20 text-orange-300 text-xs rounded-full border border-orange-400/30">
          Not Set
        </span>
      </div>

      <div v-if="hasBusinessCard" class="space-y-3">
        <p class="text-gray-300 text-sm">Your digital business card is ready to share</p>
        <div class="grid grid-cols-2 gap-2">
          <NuxtLink
            to="/c/businesscards/my-card"
            class="px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors text-center"
          >
            View Card
          </NuxtLink>
          <NuxtLink
            to="/c/businesscards/edit"
            class="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors text-center"
          >
            Edit Card
          </NuxtLink>
        </div>
      </div>

      <div v-else class="space-y-3">
        <p class="text-gray-300 text-sm">Create your digital business card to start networking</p>
        <NuxtLink
          to="/c/businesscards/create"
          class="block w-full px-4 py-2 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-medium rounded-lg transition-all duration-200 text-center"
        >
          <Icon name="mdi:plus" class="w-4 h-4 mr-2" />
          Create Business Card
        </NuxtLink>
      </div>
    </div>

    <!-- Profile Navigation Cards -->
    <div class="profile-navigation grid grid-cols-1 gap-4 mb-6">
      <!-- Calendar Card -->
      <NuxtLink 
        to="/c/calendar"
        class="nav-card group bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 hover:border-white/30 transition-all duration-300 hover:shadow-lg"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-4 shadow-md">
              <Icon name="mdi:calendar" class="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 class="text-white font-semibold">Calendar</h3>
              <p class="text-blue-200 text-sm">Manage your schedule</p>
            </div>
          </div>
          <Icon name="mdi:chevron-right" class="w-5 h-5 text-white/60 group-hover:text-white transition-colors" />
        </div>
      </NuxtLink>

      <!-- Profile Settings Card -->
      <NuxtLink 
        to="/c/profile/edit"
        class="nav-card group bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 hover:border-white/30 transition-all duration-300 hover:shadow-lg"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-4 shadow-md">
              <Icon name="mdi:account-edit" class="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 class="text-white font-semibold">Edit Profile</h3>
              <p class="text-purple-200 text-sm">Update your information</p>
            </div>
          </div>
          <Icon name="mdi:chevron-right" class="w-5 h-5 text-white/60 group-hover:text-white transition-colors" />
        </div>
      </NuxtLink>

      <!-- Account Settings Card -->
      <NuxtLink 
        to="/c/profile/settings"
        class="nav-card group bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 hover:border-white/30 transition-all duration-300 hover:shadow-lg"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-gray-500 to-gray-600 rounded-lg flex items-center justify-center mr-4 shadow-md">
              <Icon name="mdi:cog" class="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 class="text-white font-semibold">Account Settings</h3>
              <p class="text-gray-200 text-sm">Privacy & preferences</p>
            </div>
          </div>
          <Icon name="mdi:chevron-right" class="w-5 h-5 text-white/60 group-hover:text-white transition-colors" />
        </div>
      </NuxtLink>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useCurrentUser } from '~/composables/useCurrentUser'

// Composables
const { currentUser } = useCurrentUser()

// Get user display name
const displayName = computed(() => {
  if (!currentUser.value) return 'User'
  
  return currentUser.value.first_name || 
         currentUser.value.firstName || 
         currentUser.value.displayName || 
         currentUser.value.name ||
         'User'
})

// Get user email
const userEmail = computed(() => {
  return currentUser.value?.email || 'No email set'
})

// Get member since date
const memberSince = computed(() => {
  // This would be based on actual user creation date
  return new Date().getFullYear().toString()
})

// Get account type
const accountType = computed(() => {
  return currentUser.value?.role || 'User'
})

// Check if user has a business card
const hasBusinessCard = computed(() => {
  // This would need to be implemented based on your business card storage logic
  return false // Placeholder - implement based on your data structure
})
</script>

<style scoped>
.simplified-profile-view {
  animation: slideInUp 0.6s ease-out;
}

.nav-card {
  animation: slideInUp 0.6s ease-out both;
}

.nav-card:nth-child(1) { animation-delay: 0.1s; }
.nav-card:nth-child(2) { animation-delay: 0.2s; }
.nav-card:nth-child(3) { animation-delay: 0.3s; }

.profile-summary {
  animation: slideInUp 0.6s ease-out 0.1s both;
}

.business-card-status {
  animation: slideInUp 0.6s ease-out 0.2s both;
}

.profile-navigation {
  animation: slideInUp 0.6s ease-out 0.3s both;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover effects */
.nav-card:hover {
  transform: translateY(-2px);
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .profile-header .text-xl {
    font-size: 1.125rem;
  }
  
  .nav-card {
    padding: 0.875rem;
  }
  
  .profile-summary .grid-cols-2 {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}
</style>