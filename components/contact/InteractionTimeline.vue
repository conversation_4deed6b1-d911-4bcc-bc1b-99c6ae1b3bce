<template>
  <div class="interaction-timeline">
    <!-- Timeline Header -->
    <div class="timeline-header">
      <div class="header-info">
        <h3 class="timeline-title">Interaction History</h3>
        <p class="timeline-subtitle">
          {{ interactions.length }} interactions • Last: {{ lastInteractionText }}
        </p>
      </div>
      
      <div class="header-actions">
        <button
          @click="showFilters = !showFilters"
          class="filter-button"
          :class="{ active: showFilters }"
        >
          <Icon name="filter" class="w-4 h-4" />
          Filter
        </button>
        
        <button
          @click="addNewInteraction"
          class="add-button"
        >
          <Icon name="plus" class="w-4 h-4" />
          Add Interaction
        </button>
      </div>
    </div>

    <!-- Filters Panel -->
    <div v-if="showFilters" class="filters-panel">
      <div class="filter-group">
        <label for="type-filter">Type:</label>
        <select
          id="type-filter"
          v-model="typeFilter"
          class="filter-select"
        >
          <option value="all">All Types</option>
          <option value="email">Email</option>
          <option value="call">Call</option>
          <option value="meeting">Meeting</option>
          <option value="social">Social</option>
          <option value="note">Note</option>
          <option value="task">Task</option>
          <option value="follow_up">Follow-up</option>
        </select>
      </div>
      
      <div class="filter-group">
        <label for="importance-filter">Importance:</label>
        <select
          id="importance-filter"
          v-model="importanceFilter"
          class="filter-select"
        >
          <option value="all">All Levels</option>
          <option value="high">High</option>
          <option value="medium">Medium</option>
          <option value="low">Low</option>
        </select>
      </div>
      
      <div class="filter-group">
        <label for="date-range">Date Range:</label>
        <select
          id="date-range"
          v-model="dateRange"
          class="filter-select"
        >
          <option value="all">All Time</option>
          <option value="today">Today</option>
          <option value="week">This Week</option>
          <option value="month">This Month</option>
          <option value="quarter">This Quarter</option>
        </select>
      </div>
      
      <div class="filter-group">
        <label for="search-interactions">Search:</label>
        <input
          id="search-interactions"
          v-model="searchQuery"
          type="text"
          placeholder="Search interactions..."
          class="filter-input"
        />
      </div>
    </div>

    <!-- Timeline Content -->
    <div class="timeline-content">
      <div v-if="loading" class="timeline-loading">
        <Icon name="loader-2" class="w-5 h-5 animate-spin" />
        <span>Loading interactions...</span>
      </div>

      <div v-else-if="filteredInteractions.length === 0" class="timeline-empty">
        <Icon name="message-circle" class="w-12 h-12 text-gray-400" />
        <h4 class="empty-title">No interactions found</h4>
        <p class="empty-description">
          {{ interactions.length === 0 ? 'Start tracking interactions with this contact.' : 'Try adjusting your filters.' }}
        </p>
        <button @click="addNewInteraction" class="empty-add-button">
          <Icon name="plus" class="w-4 h-4" />
          Add First Interaction
        </button>
      </div>

      <div v-else class="timeline-list">
        <!-- Group interactions by date -->
        <div
          v-for="(group, date) in groupedInteractions"
          :key="date"
          class="timeline-group"
        >
          <div class="group-header">
            <h4 class="group-date">{{ formatGroupDate(date) }}</h4>
            <span class="group-count">{{ group.length }} interactions</span>
          </div>

          <div class="timeline-items">
            <div
              v-for="interaction in group"
              :key="interaction.id"
              class="timeline-item"
              :class="{
                'high-importance': interaction.importance === 'high',
                'follow-up-required': interaction.followUpDate && !isPastDate(interaction.followUpDate)
              }"
            >
              <!-- Timeline Connector -->
              <div class="timeline-connector">
                <div class="connector-dot" :class="getInteractionTypeClass(interaction.type)">
                  <Icon :name="getInteractionIcon(interaction.type)" class="w-3 h-3" />
                </div>
                <div class="connector-line"></div>
              </div>

              <!-- Interaction Content -->
              <div class="interaction-content">
                <!-- Interaction Header -->
                <div class="interaction-header">
                  <div class="interaction-meta">
                    <span class="interaction-type" :class="getInteractionTypeClass(interaction.type)">
                      {{ formatInteractionType(interaction.type) }}
                    </span>
                    <span class="interaction-time">{{ formatTime(interaction.date) }}</span>
                    <span
                      v-if="interaction.duration"
                      class="interaction-duration"
                    >
                      {{ formatDuration(interaction.duration) }}
                    </span>
                  </div>
                  
                  <div class="interaction-actions">
                    <span
                      v-if="interaction.importance !== 'medium'"
                      class="importance-badge"
                      :class="interaction.importance"
                    >
                      {{ interaction.importance }}
                    </span>
                    
                    <button
                      @click="editInteraction(interaction)"
                      class="action-button"
                      title="Edit Interaction"
                    >
                      <Icon name="edit-2" class="w-3 h-3" />
                    </button>
                    
                    <button
                      @click="deleteInteraction(interaction)"
                      class="action-button delete"
                      title="Delete Interaction"
                    >
                      <Icon name="trash-2" class="w-3 h-3" />
                    </button>
                  </div>
                </div>

                <!-- Interaction Subject -->
                <h5 class="interaction-subject">{{ interaction.subject }}</h5>

                <!-- Interaction Content -->
                <div v-if="interaction.content" class="interaction-body">
                  <p class="interaction-text">{{ interaction.content }}</p>
                </div>

                <!-- Interaction Details -->
                <div class="interaction-details">
                  <div v-if="interaction.outcome" class="detail-item">
                    <Icon name="target" class="w-3 h-3" />
                    <span class="detail-label">Outcome:</span>
                    <span class="detail-value">{{ interaction.outcome }}</span>
                  </div>
                  
                  <div v-if="interaction.location" class="detail-item">
                    <Icon name="map-pin" class="w-3 h-3" />
                    <span class="detail-label">Location:</span>
                    <span class="detail-value">{{ interaction.location }}</span>
                  </div>
                  
                  <div v-if="interaction.participants && interaction.participants.length > 0" class="detail-item">
                    <Icon name="users" class="w-3 h-3" />
                    <span class="detail-label">Participants:</span>
                    <span class="detail-value">{{ interaction.participants.join(', ') }}</span>
                  </div>
                  
                  <div v-if="interaction.sentiment" class="detail-item">
                    <Icon :name="getSentimentIcon(interaction.sentiment)" class="w-3 h-3" />
                    <span class="detail-label">Sentiment:</span>
                    <span class="detail-value sentiment" :class="interaction.sentiment">
                      {{ formatSentiment(interaction.sentiment) }}
                    </span>
                  </div>
                </div>

                <!-- Follow-up Information -->
                <div v-if="interaction.followUpDate" class="follow-up-info">
                  <div class="follow-up-content" :class="{ overdue: isPastDate(interaction.followUpDate) }">
                    <Icon name="calendar" class="w-3 h-3" />
                    <span class="follow-up-label">Follow-up:</span>
                    <span class="follow-up-date">{{ formatDate(interaction.followUpDate) }}</span>
                    <span v-if="isPastDate(interaction.followUpDate)" class="overdue-badge">Overdue</span>
                  </div>
                  
                  <button
                    v-if="!isPastDate(interaction.followUpDate)"
                    @click="scheduleFollowUp(interaction)"
                    class="follow-up-button"
                  >
                    <Icon name="bell" class="w-3 h-3" />
                    Set Reminder
                  </button>
                </div>

                <!-- Interaction Tags -->
                <div v-if="interaction.tags && interaction.tags.length > 0" class="interaction-tags">
                  <span
                    v-for="tag in interaction.tags"
                    :key="tag"
                    class="interaction-tag"
                  >
                    {{ tag }}
                  </span>
                </div>

                <!-- Attachments -->
                <div v-if="interaction.attachments && interaction.attachments.length > 0" class="interaction-attachments">
                  <div class="attachments-header">
                    <Icon name="paperclip" class="w-3 h-3" />
                    <span>{{ interaction.attachments.length }} attachment(s)</span>
                  </div>
                  <div class="attachments-list">
                    <a
                      v-for="(attachment, index) in interaction.attachments"
                      :key="index"
                      :href="attachment"
                      target="_blank"
                      class="attachment-link"
                    >
                      <Icon name="file" class="w-3 h-3" />
                      <span>Attachment {{ index + 1 }}</span>
                    </a>
                  </div>
                </div>

                <!-- Created By Info -->
                <div class="interaction-footer">
                  <span class="created-by">
                    Added by {{ getCreatedByName(interaction.createdBy) }}
                  </span>
                  <span class="created-at">
                    {{ formatDetailedDate(interaction.createdAt) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="timeline-quick-actions">
      <button
        v-for="action in quickActions"
        :key="action.type"
        @click="addQuickInteraction(action.type)"
        class="quick-action-button"
        :class="action.type"
      >
        <Icon :name="action.icon" class="w-4 h-4" />
        <span>{{ action.label }}</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { format, formatDistanceToNow, isToday, isThisWeek, isThisMonth, isThisQuarter, startOfDay, endOfDay } from 'date-fns'
import type { ContactInteraction } from '~/composables/useContacts'

// Props
interface Props {
  contactId: string
  interactions: ContactInteraction[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// Emits
const emit = defineEmits<{
  addInteraction: [type?: string]
  editInteraction: [interaction: ContactInteraction]
  deleteInteraction: [interaction: ContactInteraction]
  scheduleFollowUp: [interaction: ContactInteraction]
}>()

// Local state
const showFilters = ref(false)
const typeFilter = ref<string>('all')
const importanceFilter = ref<string>('all')
const dateRange = ref<string>('all')
const searchQuery = ref('')

// Quick actions configuration
const quickActions = [
  { type: 'call', icon: 'phone', label: 'Call' },
  { type: 'email', icon: 'mail', label: 'Email' },
  { type: 'meeting', icon: 'calendar', label: 'Meeting' },
  { type: 'note', icon: 'edit', label: 'Note' }
]

// Computed properties
const filteredInteractions = computed(() => {
  return props.interactions.filter(interaction => {
    // Type filter
    if (typeFilter.value !== 'all' && interaction.type !== typeFilter.value) {
      return false
    }

    // Importance filter
    if (importanceFilter.value !== 'all' && interaction.importance !== importanceFilter.value) {
      return false
    }

    // Date range filter
    if (dateRange.value !== 'all') {
      const interactionDate = interaction.date
      switch (dateRange.value) {
        case 'today':
          if (!isToday(interactionDate)) return false
          break
        case 'week':
          if (!isThisWeek(interactionDate)) return false
          break
        case 'month':
          if (!isThisMonth(interactionDate)) return false
          break
        case 'quarter':
          if (!isThisQuarter(interactionDate)) return false
          break
      }
    }

    // Search filter
    if (searchQuery.value) {
      const searchTerm = searchQuery.value.toLowerCase()
      const subject = interaction.subject.toLowerCase()
      const content = interaction.content?.toLowerCase() || ''
      const outcome = interaction.outcome?.toLowerCase() || ''
      
      if (!subject.includes(searchTerm) && !content.includes(searchTerm) && !outcome.includes(searchTerm)) {
        return false
      }
    }

    return true
  })
})

const groupedInteractions = computed(() => {
  const groups: Record<string, ContactInteraction[]> = {}
  
  filteredInteractions.value
    .sort((a, b) => b.date.getTime() - a.date.getTime())
    .forEach(interaction => {
      const dateKey = format(interaction.date, 'yyyy-MM-dd')
      if (!groups[dateKey]) {
        groups[dateKey] = []
      }
      groups[dateKey].push(interaction)
    })
  
  return groups
})

const lastInteractionText = computed(() => {
  if (props.interactions.length === 0) return 'No interactions'
  
  const latest = props.interactions
    .sort((a, b) => b.date.getTime() - a.date.getTime())[0]
  
  return formatDistanceToNow(latest.date, { addSuffix: true })
})

// Methods
const formatGroupDate = (dateString: string): string => {
  const date = new Date(dateString)
  
  if (isToday(date)) return 'Today'
  
  return format(date, 'EEEE, MMMM d, yyyy')
}

const formatTime = (date: Date): string => {
  return format(date, 'h:mm a')
}

const formatDate = (date: Date): string => {
  return format(date, 'MMM d, yyyy')
}

const formatDetailedDate = (date: Date): string => {
  return format(date, 'MMM d, yyyy \'at\' h:mm a')
}

const formatDuration = (minutes: number): string => {
  if (minutes < 60) {
    return `${minutes}m`
  }
  
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  
  if (remainingMinutes === 0) {
    return `${hours}h`
  }
  
  return `${hours}h ${remainingMinutes}m`
}

const formatInteractionType = (type: string): string => {
  return type.charAt(0).toUpperCase() + type.slice(1).replace('_', ' ')
}

const formatSentiment = (sentiment: string): string => {
  return sentiment.charAt(0).toUpperCase() + sentiment.slice(1)
}

const getInteractionIcon = (type: string): string => {
  const icons: Record<string, string> = {
    email: 'mail',
    call: 'phone',
    meeting: 'calendar',
    social: 'share-2',
    note: 'edit-3',
    task: 'check-square',
    follow_up: 'bell'
  }
  
  return icons[type] || 'message-circle'
}

const getInteractionTypeClass = (type: string): string => {
  return `type-${type.replace('_', '-')}`
}

const getSentimentIcon = (sentiment: string): string => {
  const icons: Record<string, string> = {
    positive: 'smile',
    neutral: 'meh',
    negative: 'frown'
  }
  
  return icons[sentiment] || 'meh'
}

const isPastDate = (date: Date): boolean => {
  return date < new Date()
}

const getCreatedByName = (userId: string): string => {
  // This would typically fetch user name from a user store/service
  return 'You' // Placeholder
}

// Actions
const addNewInteraction = () => {
  emit('addInteraction')
}

const addQuickInteraction = (type: string) => {
  emit('addInteraction', type)
}

const editInteraction = (interaction: ContactInteraction) => {
  emit('editInteraction', interaction)
}

const deleteInteraction = (interaction: ContactInteraction) => {
  emit('deleteInteraction', interaction)
}

const scheduleFollowUp = (interaction: ContactInteraction) => {
  emit('scheduleFollowUp', interaction)
}

// Reset filters
const resetFilters = () => {
  typeFilter.value = 'all'
  importanceFilter.value = 'all'
  dateRange.value = 'all'
  searchQuery.value = ''
}

// Watch for interaction changes
watch(
  () => props.interactions,
  () => {
    // Any reaction to interaction changes if needed
  },
  { deep: true }
)

onMounted(() => {
  // Any initialization if needed
})
</script>

<style scoped>
.interaction-timeline {
  @apply h-full flex flex-col bg-white;
}

/* Header */
.timeline-header {
  @apply flex items-center justify-between p-4 border-b;
}

.header-info h3.timeline-title {
  @apply text-lg font-semibold text-gray-900;
}

.header-info p.timeline-subtitle {
  @apply text-sm text-gray-600 mt-1;
}

.header-actions {
  @apply flex items-center gap-2;
}

.filter-button,
.add-button {
  @apply flex items-center gap-2 px-3 py-2 text-sm font-medium border rounded-lg transition-colors;
}

.filter-button {
  @apply text-gray-700 bg-white border-gray-300 hover:bg-gray-50;
}

.filter-button.active {
  @apply bg-blue-50 text-blue-700 border-blue-300;
}

.add-button {
  @apply text-white bg-blue-600 border-blue-600 hover:bg-blue-700;
}

/* Filters Panel */
.filters-panel {
  @apply flex flex-wrap items-center gap-4 p-4 bg-gray-50 border-b;
}

.filter-group {
  @apply flex items-center gap-2;
}

.filter-group label {
  @apply text-sm font-medium text-gray-700 whitespace-nowrap;
}

.filter-select,
.filter-input {
  @apply px-3 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

/* Timeline Content */
.timeline-content {
  @apply flex-1 overflow-y-auto;
}

.timeline-loading,
.timeline-empty {
  @apply flex flex-col items-center justify-center p-8 text-center;
}

.timeline-loading {
  @apply gap-2 text-gray-600;
}

.timeline-empty {
  @apply gap-4;
}

.empty-title {
  @apply text-lg font-semibold text-gray-900;
}

.empty-description {
  @apply text-gray-600 max-w-md;
}

.empty-add-button {
  @apply flex items-center gap-2 px-4 py-2 text-sm font-medium text-blue-600 border border-blue-300 rounded-lg hover:bg-blue-50 transition-colors;
}

/* Timeline List */
.timeline-list {
  @apply p-4 space-y-6;
}

.timeline-group {
  @apply space-y-3;
}

.group-header {
  @apply flex items-center justify-between;
}

.group-date {
  @apply text-lg font-semibold text-gray-900;
}

.group-count {
  @apply text-sm text-gray-500;
}

.timeline-items {
  @apply space-y-4;
}

/* Timeline Item */
.timeline-item {
  @apply flex gap-4 relative;
}

.timeline-item.high-importance {
  @apply bg-orange-50 -mx-2 px-2 py-2 rounded-lg;
}

.timeline-item.follow-up-required {
  @apply bg-blue-50 -mx-2 px-2 py-2 rounded-lg;
}

/* Timeline Connector */
.timeline-connector {
  @apply flex flex-col items-center flex-shrink-0;
}

.connector-dot {
  @apply w-8 h-8 rounded-full flex items-center justify-center text-white;
}

.connector-dot.type-email {
  @apply bg-blue-500;
}

.connector-dot.type-call {
  @apply bg-green-500;
}

.connector-dot.type-meeting {
  @apply bg-purple-500;
}

.connector-dot.type-social {
  @apply bg-pink-500;
}

.connector-dot.type-note {
  @apply bg-gray-500;
}

.connector-dot.type-task {
  @apply bg-yellow-500;
}

.connector-dot.type-follow-up {
  @apply bg-red-500;
}

.connector-line {
  @apply w-0.5 bg-gray-200 flex-1 mt-2;
}

.timeline-item:last-child .connector-line {
  @apply hidden;
}

/* Interaction Content */
.interaction-content {
  @apply flex-1 space-y-3;
}

.interaction-header {
  @apply flex items-start justify-between;
}

.interaction-meta {
  @apply flex items-center gap-2 text-sm;
}

.interaction-type {
  @apply font-medium px-2 py-1 rounded text-white;
}

.interaction-type.type-email {
  @apply bg-blue-500;
}

.interaction-type.type-call {
  @apply bg-green-500;
}

.interaction-type.type-meeting {
  @apply bg-purple-500;
}

.interaction-type.type-social {
  @apply bg-pink-500;
}

.interaction-type.type-note {
  @apply bg-gray-500;
}

.interaction-type.type-task {
  @apply bg-yellow-500;
}

.interaction-type.type-follow-up {
  @apply bg-red-500;
}

.interaction-time {
  @apply text-gray-600;
}

.interaction-duration {
  @apply text-gray-500 bg-gray-100 px-2 py-0.5 rounded text-xs;
}

.interaction-actions {
  @apply flex items-center gap-1;
}

.importance-badge {
  @apply text-xs font-medium px-2 py-1 rounded;
}

.importance-badge.high {
  @apply bg-red-100 text-red-800;
}

.importance-badge.low {
  @apply bg-green-100 text-green-800;
}

.action-button {
  @apply p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded transition-colors;
}

.action-button.delete:hover {
  @apply text-red-600 bg-red-50;
}

.interaction-subject {
  @apply font-semibold text-gray-900;
}

.interaction-body {
  @apply text-gray-700;
}

.interaction-text {
  @apply leading-relaxed;
}

/* Interaction Details */
.interaction-details {
  @apply space-y-2;
}

.detail-item {
  @apply flex items-center gap-2 text-sm text-gray-600;
}

.detail-label {
  @apply font-medium;
}

.detail-value.sentiment.positive {
  @apply text-green-600;
}

.detail-value.sentiment.negative {
  @apply text-red-600;
}

.detail-value.sentiment.neutral {
  @apply text-gray-600;
}

/* Follow-up Info */
.follow-up-info {
  @apply flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200;
}

.follow-up-info.overdue {
  @apply bg-red-50 border-red-200;
}

.follow-up-content {
  @apply flex items-center gap-2 text-sm;
}

.follow-up-content.overdue {
  @apply text-red-600;
}

.follow-up-label {
  @apply font-medium;
}

.overdue-badge {
  @apply bg-red-100 text-red-800 text-xs font-medium px-2 py-1 rounded;
}

.follow-up-button {
  @apply flex items-center gap-1 px-3 py-1 text-sm font-medium text-blue-600 border border-blue-300 rounded hover:bg-blue-50 transition-colors;
}

/* Tags and Attachments */
.interaction-tags {
  @apply flex flex-wrap gap-1;
}

.interaction-tag {
  @apply px-2 py-1 text-xs font-medium text-gray-600 bg-gray-100 rounded-full;
}

.interaction-attachments {
  @apply space-y-2;
}

.attachments-header {
  @apply flex items-center gap-2 text-sm font-medium text-gray-700;
}

.attachments-list {
  @apply space-y-1;
}

.attachment-link {
  @apply flex items-center gap-2 text-sm text-blue-600 hover:text-blue-800 transition-colors;
}

/* Interaction Footer */
.interaction-footer {
  @apply flex items-center justify-between text-xs text-gray-500 pt-3 border-t;
}

/* Quick Actions */
.timeline-quick-actions {
  @apply flex items-center gap-2 p-4 border-t bg-gray-50;
}

.quick-action-button {
  @apply flex items-center gap-2 px-3 py-2 text-sm font-medium border rounded-lg transition-colors;
}

.quick-action-button.call {
  @apply text-green-700 bg-green-50 border-green-200 hover:bg-green-100;
}

.quick-action-button.email {
  @apply text-blue-700 bg-blue-50 border-blue-200 hover:bg-blue-100;
}

.quick-action-button.meeting {
  @apply text-purple-700 bg-purple-50 border-purple-200 hover:bg-purple-100;
}

.quick-action-button.note {
  @apply text-gray-700 bg-gray-50 border-gray-200 hover:bg-gray-100;
}

/* Responsive */
@media (max-width: 768px) {
  .timeline-header {
    @apply flex-col gap-3 items-stretch;
  }
  
  .header-actions {
    @apply justify-center;
  }
  
  .filters-panel {
    @apply flex-col gap-3 items-stretch;
  }
  
  .interaction-header {
    @apply flex-col gap-2 items-stretch;
  }
  
  .interaction-actions {
    @apply justify-end;
  }
  
  .follow-up-info {
    @apply flex-col gap-2 items-stretch;
  }
  
  .timeline-quick-actions {
    @apply grid grid-cols-2 gap-2;
  }
}
</style>