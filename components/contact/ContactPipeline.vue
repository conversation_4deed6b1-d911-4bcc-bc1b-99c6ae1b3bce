<template>
  <div class="contact-pipeline">
    <!-- Pipeline Header -->
    <div class="pipeline-header">
      <div class="header-info">
        <h2 class="pipeline-title">Contact Pipeline</h2>
        <p class="pipeline-subtitle">
          {{ stats.totalContacts }} contacts across {{ Object.keys(contactsByStage).length }} stages
        </p>
      </div>
      
      <!-- Pipeline Actions -->
      <div class="pipeline-actions">
        <button
          @click="showFilters = !showFilters"
          class="filter-button"
          :class="{ active: showFilters }"
        >
          <Icon name="filter" class="w-4 h-4" />
          Filters
        </button>
        
        <button
          @click="showBulkActions = !showBulkActions"
          class="bulk-button"
          :class="{ active: selectedContacts.size > 0 }"
        >
          <Icon name="check-square" class="w-4 h-4" />
          Bulk Actions ({{ selectedContacts.size }})
        </button>
        
        <button @click="refreshPipeline" class="refresh-button">
          <Icon name="refresh-cw" class="w-4 h-4" />
          Refresh
        </button>
      </div>
    </div>

    <!-- Filters Panel -->
    <div v-if="showFilters" class="filters-panel">
      <div class="filter-group">
        <label for="priority-filter">Priority:</label>
        <select
          id="priority-filter"
          v-model="priorityFilter"
          class="filter-select"
        >
          <option value="all">All Priorities</option>
          <option value="high">High Priority</option>
          <option value="medium">Medium Priority</option>
          <option value="low">Low Priority</option>
        </select>
      </div>
      
      <div class="filter-group">
        <label for="search-filter">Search:</label>
        <input
          id="search-filter"
          v-model="searchQuery"
          type="text"
          placeholder="Search contacts..."
          class="filter-input"
        />
      </div>
      
      <div class="filter-group">
        <label for="overdue-filter">Show Overdue:</label>
        <input
          id="overdue-filter"
          v-model="showOverdue"
          type="checkbox"
          class="filter-checkbox"
        />
      </div>
    </div>

    <!-- Bulk Actions Panel -->
    <div v-if="showBulkActions && selectedContacts.size > 0" class="bulk-actions-panel">
      <div class="bulk-actions-content">
        <span class="bulk-count">{{ selectedContacts.size }} contacts selected</span>
        
        <div class="bulk-actions-buttons">
          <select v-model="bulkStage" class="bulk-select">
            <option value="">Move to stage...</option>
            <option value="prospect">Prospect</option>
            <option value="contact">Contact</option>
            <option value="relationship">Relationship</option>
            <option value="partner">Partner</option>
          </select>
          
          <select v-model="bulkPriority" class="bulk-select">
            <option value="">Set priority...</option>
            <option value="high">High Priority</option>
            <option value="medium">Medium Priority</option>
            <option value="low">Low Priority</option>
          </select>
          
          <button @click="executeBulkUpdate" class="bulk-execute-button">
            Apply Changes
          </button>
          
          <button @click="clearSelection" class="bulk-clear-button">
            Clear Selection
          </button>
        </div>
      </div>
    </div>

    <!-- Pipeline Stats -->
    <div class="pipeline-stats">
      <div class="stat-card">
        <div class="stat-value">{{ stats.prospects }}</div>
        <div class="stat-label">Prospects</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">{{ stats.contacts }}</div>
        <div class="stat-label">Contacts</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">{{ stats.relationships }}</div>
        <div class="stat-label">Relationships</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">{{ stats.partners }}</div>
        <div class="stat-label">Partners</div>
      </div>
      <div class="stat-card urgent">
        <div class="stat-value">{{ stats.overdueFollowUps }}</div>
        <div class="stat-label">Overdue</div>
      </div>
      <div class="stat-card priority">
        <div class="stat-value">{{ stats.highPriority }}</div>
        <div class="stat-label">High Priority</div>
      </div>
    </div>

    <!-- Pipeline Columns -->
    <div class="pipeline-columns">
      <div
        v-for="(stage, stageKey) in contactsByStage"
        :key="stageKey"
        class="pipeline-column"
        @drop="onDrop($event, stageKey)"
        @dragover.prevent
        @dragenter.prevent
      >
        <!-- Column Header -->
        <div class="column-header">
          <div class="stage-info">
            <h3 class="stage-title">{{ formatStageTitle(stageKey) }}</h3>
            <span class="stage-count">{{ stage.length }}</span>
          </div>
          
          <div class="stage-actions">
            <button @click="addNewContact(stageKey)" class="add-contact-button">
              <Icon name="plus" class="w-4 h-4" />
            </button>
          </div>
        </div>

        <!-- Contact Cards -->
        <div class="contact-cards">
          <div
            v-for="contact in stage"
            :key="contact.id"
            class="contact-card"
            :class="{
              'high-priority': contact.lifecycle.priority === 'high',
              'overdue': isOverdue(contact),
              'selected': selectedContacts.has(contact.id)
            }"
            draggable="true"
            @dragstart="onDragStart($event, contact)"
            @click="selectContact(contact)"
          >
            <!-- Contact Header -->
            <div class="contact-header">
              <input
                type="checkbox"
                :checked="selectedContacts.has(contact.id)"
                @change="toggleContactSelection(contact.id)"
                @click.stop
                class="contact-checkbox"
              />
              
              <div class="contact-avatar">
                <img
                  v-if="contact.imageUrl"
                  :src="contact.imageUrl"
                  :alt="getContactName(contact)"
                  class="avatar-image"
                />
                <div v-else class="avatar-placeholder">
                  {{ getContactInitials(contact) }}
                </div>
              </div>
              
              <div class="contact-priority" :class="contact.lifecycle.priority">
                <Icon :name="getPriorityIcon(contact.lifecycle.priority)" class="w-3 h-3" />
              </div>
            </div>

            <!-- Contact Info -->
            <div class="contact-info">
              <h4 class="contact-name">{{ getContactName(contact) }}</h4>
              <p v-if="contact.company" class="contact-company">{{ contact.company }}</p>
              <p v-if="contact.email" class="contact-email">{{ contact.email }}</p>
            </div>

            <!-- Contact Meta -->
            <div class="contact-meta">
              <div v-if="contact.lastInteractionDate" class="meta-item">
                <Icon name="clock" class="w-3 h-3" />
                <span>{{ formatDate(contact.lastInteractionDate) }}</span>
              </div>
              
              <div v-if="contact.nextFollowUpDate" class="meta-item follow-up" :class="{ overdue: isOverdue(contact) }">
                <Icon name="calendar" class="w-3 h-3" />
                <span>{{ formatDate(contact.nextFollowUpDate) }}</span>
              </div>
              
              <div v-if="contact.interactionCount > 0" class="meta-item">
                <Icon name="message-circle" class="w-3 h-3" />
                <span>{{ contact.interactionCount }}</span>
              </div>
            </div>

            <!-- Contact Tags -->
            <div v-if="contact.tags && contact.tags.length > 0" class="contact-tags">
              <span
                v-for="tag in contact.tags.slice(0, 3)"
                :key="tag"
                class="contact-tag"
              >
                {{ tag }}
              </span>
              <span v-if="contact.tags.length > 3" class="tag-more">
                +{{ contact.tags.length - 3 }}
              </span>
            </div>

            <!-- Contact Actions -->
            <div class="contact-actions">
              <button
                @click.stop="quickAction(contact, 'call')"
                class="action-button"
                title="Quick Call"
              >
                <Icon name="phone" class="w-3 h-3" />
              </button>
              
              <button
                @click.stop="quickAction(contact, 'email')"
                class="action-button"
                title="Quick Email"
              >
                <Icon name="mail" class="w-3 h-3" />
              </button>
              
              <button
                @click.stop="quickAction(contact, 'note')"
                class="action-button"
                title="Add Note"
              >
                <Icon name="edit" class="w-3 h-3" />
              </button>
              
              <button
                @click.stop="advanceStage(contact)"
                class="action-button advance"
                title="Advance Stage"
                :disabled="!canAdvance(contact)"
              >
                <Icon name="arrow-right" class="w-3 h-3" />
              </button>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div v-if="stage.length === 0" class="empty-state">
          <Icon name="users" class="w-8 h-8 text-gray-400" />
          <p class="empty-text">No contacts in this stage</p>
          <button @click="addNewContact(stageKey)" class="empty-add-button">
            Add Contact
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner">
        <Icon name="loader-2" class="w-6 h-6 animate-spin" />
        <span>Loading pipeline...</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useContacts, type Contact, type ContactStage, type BulkUpdateData } from '~/composables/useContacts'
import { formatDistanceToNow, format } from 'date-fns'

// Composables
const {
  contactsByStage,
  pipelineStats: stats,
  loading,
  error,
  searchQuery,
  priorityFilter,
  advanceContactStage,
  updateContactStage,
  bulkUpdateContacts,
  addInteraction
} = useContacts()

// Local state
const showFilters = ref(false)
const showBulkActions = ref(false)
const showOverdue = ref(false)
const selectedContacts = ref(new Set<string>())
const bulkStage = ref<ContactStage | ''>('')
const bulkPriority = ref<'low' | 'medium' | 'high' | ''>('')

// Emits
const emit = defineEmits<{
  selectContact: [contact: Contact]
  addContact: [stage: ContactStage]
  quickAction: [contact: Contact, action: string]
}>()

// Computed properties
const filteredContactsByStage = computed(() => {
  const filtered: Record<ContactStage, Contact[]> = {
    prospect: [],
    contact: [],
    relationship: [],
    partner: []
  }

  Object.entries(contactsByStage.value).forEach(([stage, contacts]) => {
    filtered[stage as ContactStage] = contacts.filter(contact => {
      // Apply priority filter
      if (priorityFilter.value !== 'all' && contact.lifecycle.priority !== priorityFilter.value) {
        return false
      }

      // Apply search filter
      if (searchQuery.value) {
        const searchTerm = searchQuery.value.toLowerCase()
        const name = getContactName(contact).toLowerCase()
        const company = contact.company?.toLowerCase() || ''
        const email = contact.email?.toLowerCase() || ''
        
        if (!name.includes(searchTerm) && !company.includes(searchTerm) && !email.includes(searchTerm)) {
          return false
        }
      }

      // Apply overdue filter
      if (showOverdue.value && !isOverdue(contact)) {
        return false
      }

      return true
    })
  })

  return filtered
})

// Methods
const getContactName = (contact: Contact): string => {
  return contact.name || `${contact.first_name || ''} ${contact.last_name || ''}`.trim() || 'Unnamed Contact'
}

const getContactInitials = (contact: Contact): string => {
  const name = getContactName(contact)
  return name.split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

const formatStageTitle = (stage: string): string => {
  return stage.charAt(0).toUpperCase() + stage.slice(1) + 's'
}

const formatDate = (date: Date): string => {
  return formatDistanceToNow(date, { addSuffix: true })
}

const isOverdue = (contact: Contact): boolean => {
  return contact.nextFollowUpDate ? contact.nextFollowUpDate < new Date() : false
}

const getPriorityIcon = (priority: string): string => {
  switch (priority) {
    case 'high': return 'alert-triangle'
    case 'medium': return 'minus'
    case 'low': return 'arrow-down'
    default: return 'minus'
  }
}

const canAdvance = (contact: Contact): boolean => {
  const stageProgression: Record<ContactStage, ContactStage | null> = {
    prospect: 'contact',
    contact: 'relationship',
    relationship: 'partner',
    partner: null
  }
  
  return stageProgression[contact.lifecycle.stage] !== null
}

// Contact selection
const selectContact = (contact: Contact) => {
  emit('selectContact', contact)
}

const toggleContactSelection = (contactId: string) => {
  if (selectedContacts.value.has(contactId)) {
    selectedContacts.value.delete(contactId)
  } else {
    selectedContacts.value.add(contactId)
  }
}

const clearSelection = () => {
  selectedContacts.value.clear()
  showBulkActions.value = false
}

// Drag and drop
const onDragStart = (event: DragEvent, contact: Contact) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('application/json', JSON.stringify(contact))
    event.dataTransfer.effectAllowed = 'move'
  }
}

const onDrop = async (event: DragEvent, targetStage: ContactStage) => {
  event.preventDefault()
  
  if (event.dataTransfer) {
    try {
      const contact = JSON.parse(event.dataTransfer.getData('application/json')) as Contact
      
      if (contact.lifecycle.stage !== targetStage) {
        await updateContactStage(contact.id, targetStage, 'Moved via drag and drop')
      }
    } catch (error) {
      console.error('Failed to process drop:', error)
    }
  }
}

// Actions
const addNewContact = (stage: ContactStage) => {
  emit('addContact', stage)
}

const quickAction = (contact: Contact, action: string) => {
  emit('quickAction', contact, action)
}

const advanceStage = async (contact: Contact) => {
  if (canAdvance(contact)) {
    await advanceContactStage(contact.id, 'Advanced via pipeline')
  }
}

const executeBulkUpdate = async () => {
  if (selectedContacts.value.size === 0) return

  const updateData: BulkUpdateData = {}
  
  if (bulkStage.value) {
    updateData.stage = bulkStage.value
  }
  
  if (bulkPriority.value) {
    updateData.priority = bulkPriority.value
  }

  if (Object.keys(updateData).length === 0) return

  try {
    const contactIds = Array.from(selectedContacts.value)
    await bulkUpdateContacts(contactIds, updateData)
    
    // Reset bulk action state
    clearSelection()
    bulkStage.value = ''
    bulkPriority.value = ''
  } catch (error) {
    console.error('Bulk update failed:', error)
  }
}

const refreshPipeline = () => {
  // Force refresh by clearing filters and reloading
  searchQuery.value = ''
  priorityFilter.value = 'all'
  showOverdue.value = false
  clearSelection()
}

// Watch for selection changes
watch(
  () => selectedContacts.value.size,
  (newSize) => {
    if (newSize > 0) {
      showBulkActions.value = true
    }
  }
)

// Cleanup on unmount
onMounted(() => {
  // Any initialization if needed
})
</script>

<style scoped>
.contact-pipeline {
  @apply h-full flex flex-col bg-gray-50;
}

/* Header */
.pipeline-header {
  @apply flex items-center justify-between p-6 bg-white border-b;
}

.header-info h2.pipeline-title {
  @apply text-2xl font-bold text-gray-900;
}

.header-info p.pipeline-subtitle {
  @apply text-sm text-gray-600 mt-1;
}

.pipeline-actions {
  @apply flex items-center gap-3;
}

.filter-button,
.bulk-button,
.refresh-button {
  @apply flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors;
}

.filter-button.active,
.bulk-button.active {
  @apply bg-blue-50 text-blue-700 border-blue-300;
}

/* Filters Panel */
.filters-panel {
  @apply flex items-center gap-6 p-4 bg-gray-100 border-b;
}

.filter-group {
  @apply flex items-center gap-2;
}

.filter-group label {
  @apply text-sm font-medium text-gray-700;
}

.filter-select,
.filter-input {
  @apply px-3 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

.filter-checkbox {
  @apply rounded text-blue-600 focus:ring-blue-500;
}

/* Bulk Actions Panel */
.bulk-actions-panel {
  @apply p-4 bg-blue-50 border-b;
}

.bulk-actions-content {
  @apply flex items-center justify-between;
}

.bulk-count {
  @apply text-sm font-medium text-blue-900;
}

.bulk-actions-buttons {
  @apply flex items-center gap-3;
}

.bulk-select {
  @apply px-3 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500;
}

.bulk-execute-button {
  @apply px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors;
}

.bulk-clear-button {
  @apply px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors;
}

/* Pipeline Stats */
.pipeline-stats {
  @apply flex items-center gap-4 p-4 bg-white border-b overflow-x-auto;
}

.stat-card {
  @apply flex flex-col items-center p-4 min-w-[100px] bg-gray-50 rounded-lg border;
}

.stat-card.urgent {
  @apply bg-red-50 border-red-200;
}

.stat-card.priority {
  @apply bg-yellow-50 border-yellow-200;
}

.stat-value {
  @apply text-2xl font-bold text-gray-900;
}

.stat-card.urgent .stat-value {
  @apply text-red-600;
}

.stat-card.priority .stat-value {
  @apply text-yellow-600;
}

.stat-label {
  @apply text-xs font-medium text-gray-600 mt-1;
}

/* Pipeline Columns */
.pipeline-columns {
  @apply flex-1 flex gap-6 p-6 overflow-x-auto;
}

.pipeline-column {
  @apply flex-1 min-w-[300px] flex flex-col bg-white rounded-lg border;
}

/* Column Header */
.column-header {
  @apply flex items-center justify-between p-4 border-b bg-gray-50 rounded-t-lg;
}

.stage-info {
  @apply flex items-center gap-3;
}

.stage-title {
  @apply text-lg font-semibold text-gray-900;
}

.stage-count {
  @apply px-2 py-1 text-xs font-medium text-gray-600 bg-gray-200 rounded-full;
}

.add-contact-button {
  @apply p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-200 rounded-md transition-colors;
}

/* Contact Cards */
.contact-cards {
  @apply flex-1 p-4 space-y-3 overflow-y-auto;
}

.contact-card {
  @apply p-4 bg-white border border-gray-200 rounded-lg cursor-pointer hover:shadow-md transition-all duration-200;
}

.contact-card.high-priority {
  @apply border-l-4 border-l-red-500;
}

.contact-card.overdue {
  @apply bg-red-50 border-red-200;
}

.contact-card.selected {
  @apply bg-blue-50 border-blue-300;
}

.contact-card:hover {
  @apply border-gray-300;
}

/* Contact Header */
.contact-header {
  @apply flex items-center justify-between mb-3;
}

.contact-checkbox {
  @apply rounded text-blue-600 focus:ring-blue-500;
}

.contact-avatar {
  @apply flex-1 flex justify-center;
}

.avatar-image {
  @apply w-10 h-10 rounded-full object-cover;
}

.avatar-placeholder {
  @apply w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center text-sm font-medium text-gray-700;
}

.contact-priority {
  @apply p-1 rounded;
}

.contact-priority.high {
  @apply text-red-500;
}

.contact-priority.medium {
  @apply text-yellow-500;
}

.contact-priority.low {
  @apply text-green-500;
}

/* Contact Info */
.contact-info {
  @apply mb-3;
}

.contact-name {
  @apply font-semibold text-gray-900 truncate;
}

.contact-company {
  @apply text-sm text-gray-600 truncate mt-1;
}

.contact-email {
  @apply text-xs text-gray-500 truncate mt-1;
}

/* Contact Meta */
.contact-meta {
  @apply space-y-1 mb-3;
}

.meta-item {
  @apply flex items-center gap-1 text-xs text-gray-500;
}

.meta-item.follow-up.overdue {
  @apply text-red-600;
}

/* Contact Tags */
.contact-tags {
  @apply flex flex-wrap gap-1 mb-3;
}

.contact-tag {
  @apply px-2 py-1 text-xs font-medium text-gray-600 bg-gray-100 rounded-full;
}

.tag-more {
  @apply px-2 py-1 text-xs font-medium text-gray-500;
}

/* Contact Actions */
.contact-actions {
  @apply flex items-center justify-between gap-1;
}

.action-button {
  @apply p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded transition-colors;
}

.action-button.advance {
  @apply text-blue-500 hover:text-blue-700 hover:bg-blue-50;
}

.action-button:disabled {
  @apply text-gray-300 cursor-not-allowed hover:text-gray-300 hover:bg-transparent;
}

/* Empty State */
.empty-state {
  @apply flex flex-col items-center justify-center p-8 text-center;
}

.empty-text {
  @apply text-sm text-gray-500 mt-2 mb-4;
}

.empty-add-button {
  @apply px-4 py-2 text-sm font-medium text-blue-600 border border-blue-300 rounded-md hover:bg-blue-50 transition-colors;
}

/* Loading State */
.loading-overlay {
  @apply absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center;
}

.loading-spinner {
  @apply flex flex-col items-center gap-2 text-gray-600;
}

/* Responsive */
@media (max-width: 768px) {
  .pipeline-columns {
    @apply gap-4 p-4;
  }
  
  .pipeline-column {
    @apply min-w-[250px];
  }
  
  .pipeline-header {
    @apply flex-col gap-4 items-stretch;
  }
  
  .pipeline-actions {
    @apply justify-center;
  }
  
  .filters-panel {
    @apply flex-col gap-3 items-stretch;
  }
  
  .bulk-actions-content {
    @apply flex-col gap-3 items-stretch;
  }
  
  .pipeline-stats {
    @apply grid grid-cols-2 gap-2;
  }
}
</style>