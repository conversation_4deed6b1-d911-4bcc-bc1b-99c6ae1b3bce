<template>
  <div class="custom-fields-manager">
    <!-- Header -->
    <div class="manager-header">
      <div class="header-info">
        <h3 class="manager-title">Custom Fields</h3>
        <p class="manager-subtitle">
          Manage custom fields for your contacts. {{ customFields.length }} field{{ customFields.length !== 1 ? 's' : '' }} configured.
        </p>
      </div>
      
      <button
        @click="showCreateModal = true"
        class="create-button"
      >
        <Icon name="plus" class="w-4 h-4" />
        Add Field
      </button>
    </div>

    <!-- Fields List -->
    <div class="fields-list">
      <div v-if="loading" class="loading-state">
        <Icon name="loader-2" class="w-6 h-6 animate-spin" />
        <span>Loading custom fields...</span>
      </div>

      <div v-else-if="customFields.length === 0" class="empty-state">
        <Icon name="settings" class="w-12 h-12 text-gray-400" />
        <h4 class="empty-title">No Custom Fields</h4>
        <p class="empty-description">
          Create custom fields to capture additional information about your contacts.
        </p>
        <button
          @click="showCreateModal = true"
          class="empty-create-button"
        >
          <Icon name="plus" class="w-4 h-4" />
          Create First Field
        </button>
      </div>

      <div v-else class="fields-grid">
        <div
          v-for="field in sortedFields"
          :key="field.id"
          class="field-card"
          :class="{ invisible: !field.visible }"
        >
          <!-- Field Header -->
          <div class="field-header">
            <div class="field-info">
              <h4 class="field-name">{{ field.name }}</h4>
              <div class="field-meta">
                <span class="field-type" :class="getFieldTypeClass(field.type)">
                  <Icon :name="getFieldTypeIcon(field.type)" class="w-3 h-3" />
                  {{ formatFieldType(field.type) }}
                </span>
                <span v-if="field.required" class="required-badge">Required</span>
                <span v-if="!field.visible" class="hidden-badge">Hidden</span>
              </div>
            </div>
            
            <div class="field-actions">
              <button
                @click="toggleFieldVisibility(field)"
                class="action-button"
                :title="field.visible ? 'Hide Field' : 'Show Field'"
              >
                <Icon :name="field.visible ? 'eye' : 'eye-off'" class="w-4 h-4" />
              </button>
              
              <button
                @click="editField(field)"
                class="action-button"
                title="Edit Field"
              >
                <Icon name="edit-2" class="w-4 h-4" />
              </button>
              
              <button
                @click="deleteField(field)"
                class="action-button delete"
                title="Delete Field"
              >
                <Icon name="trash-2" class="w-4 h-4" />
              </button>
            </div>
          </div>

          <!-- Field Details -->
          <div class="field-details">
            <div v-if="field.category" class="detail-item">
              <Icon name="folder" class="w-3 h-3" />
              <span class="detail-label">Category:</span>
              <span class="detail-value">{{ field.category }}</span>
            </div>
            
            <div class="detail-item">
              <Icon name="hash" class="w-3 h-3" />
              <span class="detail-label">Order:</span>
              <span class="detail-value">{{ field.order }}</span>
            </div>
            
            <div v-if="field.options && field.options.length > 0" class="detail-item">
              <Icon name="list" class="w-3 h-3" />
              <span class="detail-label">Options:</span>
              <span class="detail-value">{{ field.options.slice(0, 3).join(', ') }}{{ field.options.length > 3 ? '...' : '' }}</span>
            </div>
          </div>

          <!-- Field Validation -->
          <div v-if="field.validation" class="field-validation">
            <h5 class="validation-title">Validation Rules</h5>
            <div class="validation-rules">
              <span v-if="field.validation.minLength" class="rule-badge">
                Min: {{ field.validation.minLength }}
              </span>
              <span v-if="field.validation.maxLength" class="rule-badge">
                Max: {{ field.validation.maxLength }}
              </span>
              <span v-if="field.validation.pattern" class="rule-badge">
                Pattern
              </span>
              <span v-if="field.validation.min !== undefined" class="rule-badge">
                Min: {{ field.validation.min }}
              </span>
              <span v-if="field.validation.max !== undefined" class="rule-badge">
                Max: {{ field.validation.max }}
              </span>
            </div>
          </div>

          <!-- Field Preview -->
          <div class="field-preview">
            <h5 class="preview-title">Preview</h5>
            <div class="preview-content">
              <CustomFieldInput
                :field="field"
                :value="getPreviewValue(field)"
                :disabled="true"
                @update:value="() => {}"
              />
            </div>
          </div>

          <!-- Field Stats -->
          <div class="field-stats">
            <div class="stat-item">
              <Icon name="users" class="w-3 h-3" />
              <span class="stat-label">Usage:</span>
              <span class="stat-value">{{ getFieldUsageCount(field.id) }} contacts</span>
            </div>
            
            <div class="stat-item">
              <Icon name="calendar" class="w-3 h-3" />
              <span class="stat-label">Created:</span>
              <span class="stat-value">{{ formatDate(field.createdAt) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Field Creation/Edit Modal -->
    <CustomFieldModal
      v-if="showCreateModal || editingField"
      :field="editingField"
      :existing-fields="customFields"
      @save="saveField"
      @close="closeModal"
    />

    <!-- Delete Confirmation Modal -->
    <ConfirmationModal
      v-if="deletingField"
      title="Delete Custom Field"
      :message="`Are you sure you want to delete the field '${deletingField.name}'? This will remove the field from all contacts and cannot be undone.`"
      confirm-text="Delete Field"
      confirm-variant="danger"
      @confirm="confirmDeleteField"
      @cancel="deletingField = null"
    />

    <!-- Bulk Field Operations -->
    <div v-if="selectedFields.size > 0" class="bulk-operations">
      <div class="bulk-header">
        <span class="bulk-count">{{ selectedFields.size }} field{{ selectedFields.size !== 1 ? 's' : '' }} selected</span>
        <div class="bulk-actions">
          <button
            @click="bulkToggleVisibility"
            class="bulk-button"
          >
            <Icon name="eye" class="w-4 h-4" />
            Toggle Visibility
          </button>
          
          <button
            @click="bulkDelete"
            class="bulk-button delete"
          >
            <Icon name="trash-2" class="w-4 h-4" />
            Delete Selected
          </button>
          
          <button
            @click="selectedFields.clear()"
            class="bulk-button"
          >
            <Icon name="x" class="w-4 h-4" />
            Clear
          </button>
        </div>
      </div>
    </div>

    <!-- Field Categories -->
    <div v-if="fieldCategories.length > 0" class="field-categories">
      <h4 class="categories-title">Field Categories</h4>
      <div class="categories-list">
        <button
          v-for="category in fieldCategories"
          :key="category"
          @click="filterByCategory(category)"
          class="category-button"
          :class="{ active: selectedCategory === category }"
        >
          {{ category }}
          <span class="category-count">{{ getCategoryCount(category) }}</span>
        </button>
        
        <button
          v-if="selectedCategory"
          @click="clearCategoryFilter"
          class="category-button clear"
        >
          <Icon name="x" class="w-4 h-4" />
          Clear Filter
        </button>
      </div>
    </div>

    <!-- Field Templates -->
    <div class="field-templates">
      <h4 class="templates-title">Quick Templates</h4>
      <p class="templates-subtitle">Create fields from common templates</p>
      
      <div class="templates-grid">
        <button
          v-for="template in fieldTemplates"
          :key="template.name"
          @click="createFromTemplate(template)"
          class="template-button"
        >
          <Icon :name="template.icon" class="w-5 h-5" />
          <div class="template-info">
            <h5 class="template-name">{{ template.name }}</h5>
            <p class="template-description">{{ template.description }}</p>
          </div>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { format } from 'date-fns'
import { useContacts, type CustomField, type Contact } from '~/composables/useContacts'

// Composables
const { customFields, contacts, loading, createCustomField, updateCustomField } = useContacts()

// Local state
const showCreateModal = ref(false)
const editingField = ref<CustomField | null>(null)
const deletingField = ref<CustomField | null>(null)
const selectedFields = ref(new Set<string>())
const selectedCategory = ref<string | null>(null)

// Field templates
const fieldTemplates = [
  {
    name: 'Social Media',
    description: 'LinkedIn, Twitter, Facebook URLs',
    icon: 'share-2',
    fields: [
      { name: 'LinkedIn URL', type: 'url', category: 'Social' },
      { name: 'Twitter Handle', type: 'text', category: 'Social' },
      { name: 'Facebook Profile', type: 'url', category: 'Social' }
    ]
  },
  {
    name: 'Business Info',
    description: 'Industry, company size, revenue',
    icon: 'building',
    fields: [
      { name: 'Industry', type: 'select', category: 'Business', options: ['Technology', 'Healthcare', 'Finance', 'Education', 'Other'] },
      { name: 'Company Size', type: 'select', category: 'Business', options: ['1-10', '11-50', '51-200', '201-1000', '1000+'] },
      { name: 'Annual Revenue', type: 'select', category: 'Business', options: ['< $1M', '$1M - $10M', '$10M - $100M', '$100M+'] }
    ]
  },
  {
    name: 'Personal Info',
    description: 'Birthday, interests, preferences',
    icon: 'user',
    fields: [
      { name: 'Birthday', type: 'date', category: 'Personal' },
      { name: 'Interests', type: 'multiselect', category: 'Personal', options: ['Sports', 'Travel', 'Technology', 'Art', 'Music'] },
      { name: 'Preferred Contact Method', type: 'select', category: 'Personal', options: ['Email', 'Phone', 'Text', 'LinkedIn'] }
    ]
  },
  {
    name: 'Sales Tracking',
    description: 'Lead source, budget, timeline',
    icon: 'trending-up',
    fields: [
      { name: 'Lead Source', type: 'select', category: 'Sales', options: ['Website', 'Referral', 'Cold Outreach', 'Event', 'Social Media'] },
      { name: 'Budget Range', type: 'select', category: 'Sales', options: ['< $10K', '$10K - $50K', '$50K - $100K', '$100K+'] },
      { name: 'Decision Timeline', type: 'select', category: 'Sales', options: ['Immediate', '1-3 months', '3-6 months', '6+ months'] }
    ]
  }
]

// Computed properties
const sortedFields = computed(() => {
  let fields = customFields.value.slice()
  
  // Filter by category if selected
  if (selectedCategory.value) {
    fields = fields.filter(field => field.category === selectedCategory.value)
  }
  
  // Sort by order, then by name
  return fields.sort((a, b) => {
    if (a.order !== b.order) {
      return a.order - b.order
    }
    return a.name.localeCompare(b.name)
  })
})

const fieldCategories = computed(() => {
  const categories = new Set<string>()
  customFields.value.forEach(field => {
    if (field.category) {
      categories.add(field.category)
    }
  })
  return Array.from(categories).sort()
})

// Methods
const formatDate = (date: Date): string => {
  return format(date, 'MMM d, yyyy')
}

const formatFieldType = (type: string): string => {
  return type.charAt(0).toUpperCase() + type.slice(1).replace('_', ' ')
}

const getFieldTypeClass = (type: string): string => {
  const typeClasses: Record<string, string> = {
    text: 'text',
    number: 'number',
    date: 'date',
    select: 'select',
    multiselect: 'multiselect',
    boolean: 'boolean',
    email: 'email',
    phone: 'phone',
    url: 'url'
  }
  
  return typeClasses[type] || 'default'
}

const getFieldTypeIcon = (type: string): string => {
  const typeIcons: Record<string, string> = {
    text: 'type',
    number: 'hash',
    date: 'calendar',
    select: 'chevron-down',
    multiselect: 'list',
    boolean: 'toggle-left',
    email: 'mail',
    phone: 'phone',
    url: 'link'
  }
  
  return typeIcons[type] || 'circle'
}

const getPreviewValue = (field: CustomField): any => {
  switch (field.type) {
    case 'text':
      return 'Sample text'
    case 'number':
      return 42
    case 'date':
      return new Date()
    case 'select':
      return field.options?.[0] || 'Option 1'
    case 'multiselect':
      return field.options?.slice(0, 2) || ['Option 1', 'Option 2']
    case 'boolean':
      return true
    case 'email':
      return '<EMAIL>'
    case 'phone':
      return '+****************'
    case 'url':
      return 'https://example.com'
    default:
      return 'Sample value'
  }
}

const getFieldUsageCount = (fieldId: string): number => {
  return contacts.value.filter(contact => 
    contact.customFields && contact.customFields[fieldId] !== undefined
  ).length
}

const getCategoryCount = (category: string): number => {
  return customFields.value.filter(field => field.category === category).length
}

// Field operations
const editField = (field: CustomField) => {
  editingField.value = field
}

const deleteField = (field: CustomField) => {
  deletingField.value = field
}

const toggleFieldVisibility = async (field: CustomField) => {
  await updateCustomField(field.id, { visible: !field.visible })
}

const saveField = async (fieldData: Omit<CustomField, 'id' | 'createdAt' | 'updatedAt'>) => {
  if (editingField.value) {
    // Update existing field
    await updateCustomField(editingField.value.id, fieldData)
  } else {
    // Create new field
    await createCustomField(fieldData)
  }
  
  closeModal()
}

const confirmDeleteField = async () => {
  if (deletingField.value) {
    // In a real implementation, you'd call a delete method
    // await deleteCustomField(deletingField.value.id)
    deletingField.value = null
  }
}

const closeModal = () => {
  showCreateModal.value = false
  editingField.value = null
}

// Category filtering
const filterByCategory = (category: string) => {
  selectedCategory.value = category
}

const clearCategoryFilter = () => {
  selectedCategory.value = null
}

// Template operations
const createFromTemplate = (template: any) => {
  // This would open a modal to create multiple fields from a template
  // For now, we'll just create the first field
  if (template.fields && template.fields.length > 0) {
    const firstField = template.fields[0]
    editingField.value = null
    showCreateModal.value = true
    // Pass template data to modal
  }
}

// Bulk operations
const bulkToggleVisibility = async () => {
  const selectedFieldIds = Array.from(selectedFields.value)
  for (const fieldId of selectedFieldIds) {
    const field = customFields.value.find(f => f.id === fieldId)
    if (field) {
      await updateCustomField(fieldId, { visible: !field.visible })
    }
  }
  selectedFields.value.clear()
}

const bulkDelete = () => {
  // This would show a confirmation modal for bulk deletion
  console.log('Bulk delete:', Array.from(selectedFields.value))
}

onMounted(() => {
  // Any initialization if needed
})
</script>

<style scoped>
.custom-fields-manager {
  @apply space-y-6;
}

/* Header */
.manager-header {
  @apply flex items-center justify-between;
}

.header-info h3.manager-title {
  @apply text-xl font-semibold text-gray-900;
}

.header-info p.manager-subtitle {
  @apply text-sm text-gray-600 mt-1;
}

.create-button {
  @apply flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-600 rounded-lg hover:bg-blue-700 transition-colors;
}

/* Loading and Empty States */
.loading-state,
.empty-state {
  @apply flex flex-col items-center justify-center p-8 text-center;
}

.loading-state {
  @apply gap-2 text-gray-600;
}

.empty-state {
  @apply gap-4;
}

.empty-title {
  @apply text-lg font-semibold text-gray-900;
}

.empty-description {
  @apply text-gray-600 max-w-md;
}

.empty-create-button {
  @apply flex items-center gap-2 px-4 py-2 text-sm font-medium text-blue-600 border border-blue-300 rounded-lg hover:bg-blue-50 transition-colors;
}

/* Fields Grid */
.fields-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

.field-card {
  @apply bg-white border border-gray-200 rounded-lg p-6 space-y-4 hover:shadow-md transition-shadow;
}

.field-card.invisible {
  @apply opacity-60 bg-gray-50;
}

/* Field Header */
.field-header {
  @apply flex items-start justify-between;
}

.field-info {
  @apply flex-1;
}

.field-name {
  @apply font-semibold text-gray-900;
}

.field-meta {
  @apply flex items-center gap-2 mt-1;
}

.field-type {
  @apply flex items-center gap-1 px-2 py-1 text-xs font-medium rounded;
}

.field-type.text {
  @apply bg-blue-100 text-blue-800;
}

.field-type.number {
  @apply bg-green-100 text-green-800;
}

.field-type.date {
  @apply bg-purple-100 text-purple-800;
}

.field-type.select,
.field-type.multiselect {
  @apply bg-yellow-100 text-yellow-800;
}

.field-type.boolean {
  @apply bg-indigo-100 text-indigo-800;
}

.field-type.email {
  @apply bg-red-100 text-red-800;
}

.field-type.phone {
  @apply bg-pink-100 text-pink-800;
}

.field-type.url {
  @apply bg-gray-100 text-gray-800;
}

.required-badge {
  @apply px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded;
}

.hidden-badge {
  @apply px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded;
}

.field-actions {
  @apply flex items-center gap-1;
}

.action-button {
  @apply p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded transition-colors;
}

.action-button.delete:hover {
  @apply text-red-600 bg-red-50;
}

/* Field Details */
.field-details {
  @apply space-y-2;
}

.detail-item {
  @apply flex items-center gap-2 text-sm text-gray-600;
}

.detail-label {
  @apply font-medium;
}

.detail-value {
  @apply text-gray-900;
}

/* Field Validation */
.field-validation {
  @apply space-y-2;
}

.validation-title {
  @apply text-sm font-medium text-gray-700;
}

.validation-rules {
  @apply flex flex-wrap gap-1;
}

.rule-badge {
  @apply px-2 py-1 text-xs font-medium bg-gray-100 text-gray-700 rounded;
}

/* Field Preview */
.field-preview {
  @apply space-y-2;
}

.preview-title {
  @apply text-sm font-medium text-gray-700;
}

.preview-content {
  @apply p-3 bg-gray-50 rounded border;
}

/* Field Stats */
.field-stats {
  @apply space-y-2 pt-4 border-t;
}

.stat-item {
  @apply flex items-center gap-2 text-sm text-gray-600;
}

.stat-label {
  @apply font-medium;
}

.stat-value {
  @apply text-gray-900;
}

/* Bulk Operations */
.bulk-operations {
  @apply fixed bottom-6 left-1/2 transform -translate-x-1/2 bg-white border border-gray-300 rounded-lg shadow-lg p-4 z-50;
}

.bulk-header {
  @apply flex items-center gap-4;
}

.bulk-count {
  @apply text-sm font-medium text-gray-900;
}

.bulk-actions {
  @apply flex items-center gap-2;
}

.bulk-button {
  @apply flex items-center gap-2 px-3 py-2 text-sm font-medium border rounded-lg transition-colors;
}

.bulk-button:not(.delete) {
  @apply text-gray-700 bg-white border-gray-300 hover:bg-gray-50;
}

.bulk-button.delete {
  @apply text-red-700 bg-red-50 border-red-200 hover:bg-red-100;
}

/* Field Categories */
.field-categories {
  @apply space-y-3;
}

.categories-title {
  @apply text-lg font-semibold text-gray-900;
}

.categories-list {
  @apply flex flex-wrap gap-2;
}

.category-button {
  @apply flex items-center gap-2 px-3 py-2 text-sm font-medium border rounded-lg transition-colors;
}

.category-button:not(.active):not(.clear) {
  @apply text-gray-700 bg-white border-gray-300 hover:bg-gray-50;
}

.category-button.active {
  @apply text-blue-700 bg-blue-50 border-blue-300;
}

.category-button.clear {
  @apply text-red-700 bg-red-50 border-red-200 hover:bg-red-100;
}

.category-count {
  @apply px-2 py-1 text-xs bg-gray-200 text-gray-700 rounded-full;
}

/* Field Templates */
.field-templates {
  @apply space-y-4 p-6 bg-gray-50 rounded-lg;
}

.templates-title {
  @apply text-lg font-semibold text-gray-900;
}

.templates-subtitle {
  @apply text-sm text-gray-600;
}

.templates-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.template-button {
  @apply flex items-start gap-3 p-4 bg-white border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors text-left;
}

.template-info {
  @apply flex-1;
}

.template-name {
  @apply font-semibold text-gray-900;
}

.template-description {
  @apply text-sm text-gray-600 mt-1;
}

/* Responsive */
@media (max-width: 768px) {
  .manager-header {
    @apply flex-col gap-3 items-stretch;
  }
  
  .fields-grid {
    @apply grid-cols-1;
  }
  
  .field-header {
    @apply flex-col gap-2 items-stretch;
  }
  
  .field-actions {
    @apply justify-end;
  }
  
  .bulk-operations {
    @apply left-4 right-4 transform-none;
  }
  
  .bulk-header {
    @apply flex-col gap-2 items-stretch;
  }
  
  .categories-list {
    @apply grid grid-cols-2 gap-2;
  }
  
  .templates-grid {
    @apply grid-cols-1;
  }
}
</style>