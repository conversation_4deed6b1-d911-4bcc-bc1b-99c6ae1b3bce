<template>
  <div class="bulk-operations">
    <!-- Bulk Operations Header -->
    <div class="bulk-header">
      <div class="selection-info">
        <Icon name="check-square" class="w-5 h-5" />
        <span class="selection-count">
          {{ selectedContactIds.length }} contact{{ selectedContactIds.length !== 1 ? 's' : '' }} selected
        </span>
      </div>
      
      <button
        @click="clearSelection"
        class="clear-button"
      >
        <Icon name="x" class="w-4 h-4" />
        Clear Selection
      </button>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <div class="action-group">
        <h4 class="group-title">Stage Management</h4>
        <div class="action-buttons">
          <button
            v-for="stage in stageOptions"
            :key="stage.value"
            @click="updateStage(stage.value)"
            class="action-button stage"
            :class="stage.value"
          >
            <Icon :name="stage.icon" class="w-4 h-4" />
            <span>Move to {{ stage.label }}</span>
          </button>
        </div>
      </div>

      <div class="action-group">
        <h4 class="group-title">Priority</h4>
        <div class="action-buttons">
          <button
            v-for="priority in priorityOptions"
            :key="priority.value"
            @click="updatePriority(priority.value)"
            class="action-button priority"
            :class="priority.value"
          >
            <Icon :name="priority.icon" class="w-4 h-4" />
            <span>Set {{ priority.label }}</span>
          </button>
        </div>
      </div>

      <div class="action-group">
        <h4 class="group-title">Tags</h4>
        <div class="action-buttons">
          <button
            @click="showTagsModal = true"
            class="action-button tags"
          >
            <Icon name="tag" class="w-4 h-4" />
            <span>Manage Tags</span>
          </button>
        </div>
      </div>

      <div class="action-group">
        <h4 class="group-title">Data Operations</h4>
        <div class="action-buttons">
          <button
            @click="exportContacts"
            class="action-button export"
          >
            <Icon name="download" class="w-4 h-4" />
            <span>Export Selected</span>
          </button>
          
          <button
            @click="showImportModal = true"
            class="action-button import"
          >
            <Icon name="upload" class="w-4 h-4" />
            <span>Import Contacts</span>
          </button>
          
          <button
            @click="showDeleteConfirm = true"
            class="action-button delete"
          >
            <Icon name="trash-2" class="w-4 h-4" />
            <span>Delete Selected</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Advanced Operations -->
    <div class="advanced-operations">
      <h4 class="section-title">Advanced Operations</h4>
      
      <div class="operation-grid">
        <!-- Custom Fields Update -->
        <div class="operation-card">
          <div class="card-header">
            <Icon name="settings" class="w-5 h-5" />
            <h5 class="card-title">Update Custom Fields</h5>
          </div>
          <p class="card-description">
            Bulk update custom field values for selected contacts.
          </p>
          <button
            @click="showCustomFieldsModal = true"
            class="card-button"
          >
            Configure Fields
          </button>
        </div>

        <!-- Follow-up Scheduling -->
        <div class="operation-card">
          <div class="card-header">
            <Icon name="calendar" class="w-5 h-5" />
            <h5 class="card-title">Schedule Follow-ups</h5>
          </div>
          <p class="card-description">
            Set follow-up dates and reminders for all selected contacts.
          </p>
          <button
            @click="showFollowUpModal = true"
            class="card-button"
          >
            Schedule Follow-ups
          </button>
        </div>

        <!-- Automation Rules -->
        <div class="operation-card">
          <div class="card-header">
            <Icon name="zap" class="w-5 h-5" />
            <h5 class="card-title">Apply Automation</h5>
          </div>
          <p class="card-description">
            Apply automation rules to selected contacts.
          </p>
          <button
            @click="showAutomationModal = true"
            class="card-button"
          >
            Configure Automation
          </button>
        </div>

        <!-- Duplicate Detection -->
        <div class="operation-card">
          <div class="card-header">
            <Icon name="copy" class="w-5 h-5" />
            <h5 class="card-title">Find Duplicates</h5>
          </div>
          <p class="card-description">
            Identify and merge duplicate contacts.
          </p>
          <button
            @click="findDuplicates"
            class="card-button"
          >
            Find Duplicates
          </button>
        </div>
      </div>
    </div>

    <!-- Operation Status -->
    <div v-if="operationStatus" class="operation-status">
      <div class="status-content" :class="operationStatus.type">
        <Icon
          :name="operationStatus.type === 'success' ? 'check-circle' : operationStatus.type === 'error' ? 'x-circle' : 'loader-2'"
          :class="['w-5 h-5', { 'animate-spin': operationStatus.type === 'loading' }]"
        />
        <div class="status-text">
          <h5 class="status-title">{{ operationStatus.title }}</h5>
          <p class="status-message">{{ operationStatus.message }}</p>
          <div v-if="operationStatus.progress" class="status-progress">
            <div class="progress-bar">
              <div
                class="progress-fill"
                :style="{ width: `${operationStatus.progress.percentage}%` }"
              ></div>
            </div>
            <span class="progress-text">
              {{ operationStatus.progress.current }} / {{ operationStatus.progress.total }}
            </span>
          </div>
        </div>
        <button
          v-if="operationStatus.type !== 'loading'"
          @click="operationStatus = null"
          class="status-close"
        >
          <Icon name="x" class="w-4 h-4" />
        </button>
      </div>
    </div>

    <!-- Tags Management Modal -->
    <BulkTagsModal
      v-if="showTagsModal"
      :contact-ids="selectedContactIds"
      :existing-tags="allTags"
      @close="showTagsModal = false"
      @apply="applyTags"
    />

    <!-- Custom Fields Modal -->
    <BulkCustomFieldsModal
      v-if="showCustomFieldsModal"
      :contact-ids="selectedContactIds"
      :custom-fields="customFields"
      @close="showCustomFieldsModal = false"
      @apply="applyCustomFields"
    />

    <!-- Follow-up Scheduling Modal -->
    <BulkFollowUpModal
      v-if="showFollowUpModal"
      :contact-ids="selectedContactIds"
      @close="showFollowUpModal = false"
      @apply="applyFollowUps"
    />

    <!-- Import Modal -->
    <ContactImportModal
      v-if="showImportModal"
      @close="showImportModal = false"
      @imported="handleImportComplete"
    />

    <!-- Delete Confirmation Modal -->
    <ConfirmationModal
      v-if="showDeleteConfirm"
      title="Delete Contacts"
      :message="`Are you sure you want to delete ${selectedContactIds.length} contact${selectedContactIds.length !== 1 ? 's' : ''}? This action cannot be undone.`"
      confirm-text="Delete"
      confirm-variant="danger"
      @confirm="deleteContacts"
      @cancel="showDeleteConfirm = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useContacts, type ContactStage, type BulkUpdateData, type Contact } from '~/composables/useContacts'

// Props
interface Props {
  selectedContactIds: string[]
  allTags: string[]
  customFields: any[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  clearSelection: []
  operationComplete: [result: any]
}>()

// Composables
const { bulkUpdateContacts, contacts } = useContacts()

// Local state
const showTagsModal = ref(false)
const showCustomFieldsModal = ref(false)
const showFollowUpModal = ref(false)
const showImportModal = ref(false)
const showDeleteConfirm = ref(false)
const showAutomationModal = ref(false)

interface OperationStatus {
  type: 'loading' | 'success' | 'error'
  title: string
  message: string
  progress?: {
    current: number
    total: number
    percentage: number
  }
}

const operationStatus = ref<OperationStatus | null>(null)

// Configuration
const stageOptions = [
  { value: 'prospect', label: 'Prospect', icon: 'user-plus' },
  { value: 'contact', label: 'Contact', icon: 'user' },
  { value: 'relationship', label: 'Relationship', icon: 'users' },
  { value: 'partner', label: 'Partner', icon: 'handshake' }
]

const priorityOptions = [
  { value: 'high', label: 'High Priority', icon: 'alert-triangle' },
  { value: 'medium', label: 'Medium Priority', icon: 'minus' },
  { value: 'low', label: 'Low Priority', icon: 'arrow-down' }
]

// Methods
const clearSelection = () => {
  emit('clearSelection')
}

const setOperationStatus = (status: OperationStatus) => {
  operationStatus.value = status
}

const updateOperationProgress = (current: number, total: number) => {
  if (operationStatus.value) {
    operationStatus.value.progress = {
      current,
      total,
      percentage: Math.round((current / total) * 100)
    }
  }
}

// Stage Management
const updateStage = async (stage: ContactStage) => {
  setOperationStatus({
    type: 'loading',
    title: 'Updating Contact Stages',
    message: `Moving ${props.selectedContactIds.length} contacts to ${stage} stage...`,
    progress: { current: 0, total: props.selectedContactIds.length, percentage: 0 }
  })

  try {
    const result = await bulkUpdateContacts(props.selectedContactIds, { stage })
    
    updateOperationProgress(result.success, props.selectedContactIds.length)
    
    if (result.success === props.selectedContactIds.length) {
      setOperationStatus({
        type: 'success',
        title: 'Stage Update Complete',
        message: `Successfully moved ${result.success} contacts to ${stage} stage.`
      })
    } else {
      setOperationStatus({
        type: 'error',
        title: 'Partial Update Failure',
        message: `Updated ${result.success} contacts successfully, ${result.failed} failed.`
      })
    }

    emit('operationComplete', result)
  } catch (error) {
    setOperationStatus({
      type: 'error',
      title: 'Update Failed',
      message: 'Failed to update contact stages. Please try again.'
    })
  }
}

// Priority Management
const updatePriority = async (priority: 'low' | 'medium' | 'high') => {
  setOperationStatus({
    type: 'loading',
    title: 'Updating Priorities',
    message: `Setting ${props.selectedContactIds.length} contacts to ${priority} priority...`,
    progress: { current: 0, total: props.selectedContactIds.length, percentage: 0 }
  })

  try {
    const result = await bulkUpdateContacts(props.selectedContactIds, { priority })
    
    updateOperationProgress(result.success, props.selectedContactIds.length)
    
    if (result.success === props.selectedContactIds.length) {
      setOperationStatus({
        type: 'success',
        title: 'Priority Update Complete',
        message: `Successfully updated ${result.success} contacts to ${priority} priority.`
      })
    } else {
      setOperationStatus({
        type: 'error',
        title: 'Partial Update Failure',
        message: `Updated ${result.success} contacts successfully, ${result.failed} failed.`
      })
    }

    emit('operationComplete', result)
  } catch (error) {
    setOperationStatus({
      type: 'error',
      title: 'Update Failed',
      message: 'Failed to update contact priorities. Please try again.'
    })
  }
}

// Tags Management
const applyTags = async (tagOperation: { action: 'add' | 'remove' | 'replace'; tags: string[] }) => {
  setOperationStatus({
    type: 'loading',
    title: 'Updating Tags',
    message: `${tagOperation.action === 'add' ? 'Adding' : tagOperation.action === 'remove' ? 'Removing' : 'Replacing'} tags for ${props.selectedContactIds.length} contacts...`,
    progress: { current: 0, total: props.selectedContactIds.length, percentage: 0 }
  })

  try {
    let newTags: string[]
    
    // For now, we'll implement replace functionality
    // In a full implementation, you'd need to fetch existing tags first
    if (tagOperation.action === 'replace') {
      newTags = tagOperation.tags
    } else {
      // For add/remove, you'd need to merge with existing tags
      newTags = tagOperation.tags
    }

    const result = await bulkUpdateContacts(props.selectedContactIds, { tags: newTags })
    
    updateOperationProgress(result.success, props.selectedContactIds.length)
    
    if (result.success === props.selectedContactIds.length) {
      setOperationStatus({
        type: 'success',
        title: 'Tags Update Complete',
        message: `Successfully updated tags for ${result.success} contacts.`
      })
    } else {
      setOperationStatus({
        type: 'error',
        title: 'Partial Update Failure',
        message: `Updated ${result.success} contacts successfully, ${result.failed} failed.`
      })
    }

    emit('operationComplete', result)
    showTagsModal.value = false
  } catch (error) {
    setOperationStatus({
      type: 'error',
      title: 'Update Failed',
      message: 'Failed to update contact tags. Please try again.'
    })
  }
}

// Custom Fields Management
const applyCustomFields = async (customFieldUpdates: Record<string, any>) => {
  setOperationStatus({
    type: 'loading',
    title: 'Updating Custom Fields',
    message: `Updating custom fields for ${props.selectedContactIds.length} contacts...`,
    progress: { current: 0, total: props.selectedContactIds.length, percentage: 0 }
  })

  try {
    const result = await bulkUpdateContacts(props.selectedContactIds, { customFields: customFieldUpdates })
    
    updateOperationProgress(result.success, props.selectedContactIds.length)
    
    if (result.success === props.selectedContactIds.length) {
      setOperationStatus({
        type: 'success',
        title: 'Custom Fields Update Complete',
        message: `Successfully updated custom fields for ${result.success} contacts.`
      })
    } else {
      setOperationStatus({
        type: 'error',
        title: 'Partial Update Failure',
        message: `Updated ${result.success} contacts successfully, ${result.failed} failed.`
      })
    }

    emit('operationComplete', result)
    showCustomFieldsModal.value = false
  } catch (error) {
    setOperationStatus({
      type: 'error',
      title: 'Update Failed',
      message: 'Failed to update custom fields. Please try again.'
    })
  }
}

// Follow-up Management
const applyFollowUps = async (followUpData: { date: Date; type: string; description?: string }) => {
  setOperationStatus({
    type: 'loading',
    title: 'Scheduling Follow-ups',
    message: `Scheduling follow-ups for ${props.selectedContactIds.length} contacts...`,
    progress: { current: 0, total: props.selectedContactIds.length, percentage: 0 }
  })

  try {
    // This would typically involve calling a follow-up scheduling service
    // For now, we'll simulate the operation
    let completed = 0
    for (const contactId of props.selectedContactIds) {
      // Schedule follow-up for each contact
      // await scheduleFollowUp(contactId, followUpData)
      completed++
      updateOperationProgress(completed, props.selectedContactIds.length)
      
      // Add small delay to show progress
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    setOperationStatus({
      type: 'success',
      title: 'Follow-ups Scheduled',
      message: `Successfully scheduled follow-ups for ${completed} contacts.`
    })

    emit('operationComplete', { success: completed, failed: 0 })
    showFollowUpModal.value = false
  } catch (error) {
    setOperationStatus({
      type: 'error',
      title: 'Scheduling Failed',
      message: 'Failed to schedule follow-ups. Please try again.'
    })
  }
}

// Data Operations
const exportContacts = async () => {
  setOperationStatus({
    type: 'loading',
    title: 'Exporting Contacts',
    message: `Preparing export for ${props.selectedContactIds.length} contacts...`
  })

  try {
    // Get selected contacts data
    const selectedContacts = contacts.value.filter(contact => 
      props.selectedContactIds.includes(contact.id)
    )

    // Convert to CSV format
    const csvContent = generateCSV(selectedContacts)
    
    // Create download
    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `contacts-export-${new Date().toISOString().split('T')[0]}.csv`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    setOperationStatus({
      type: 'success',
      title: 'Export Complete',
      message: `Successfully exported ${selectedContacts.length} contacts.`
    })

    emit('operationComplete', { exported: selectedContacts.length })
  } catch (error) {
    setOperationStatus({
      type: 'error',
      title: 'Export Failed',
      message: 'Failed to export contacts. Please try again.'
    })
  }
}

const generateCSV = (contacts: Contact[]): string => {
  const headers = [
    'Name', 'Email', 'Phone', 'Company', 'Position', 'Stage', 'Priority', 'Tags', 'Last Interaction'
  ]

  const rows = contacts.map(contact => [
    contact.name || `${contact.first_name || ''} ${contact.last_name || ''}`.trim(),
    contact.email || '',
    contact.phone || '',
    contact.company || '',
    contact.position || '',
    contact.lifecycle?.stage || '',
    contact.lifecycle?.priority || '',
    contact.tags?.join('; ') || '',
    contact.lastInteractionDate ? contact.lastInteractionDate.toISOString().split('T')[0] : ''
  ])

  return [headers, ...rows]
    .map(row => row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(','))
    .join('\n')
}

const deleteContacts = async () => {
  setOperationStatus({
    type: 'loading',
    title: 'Deleting Contacts',
    message: `Deleting ${props.selectedContactIds.length} contacts...`,
    progress: { current: 0, total: props.selectedContactIds.length, percentage: 0 }
  })

  try {
    // This would typically involve calling a bulk delete service
    // For now, we'll simulate the operation
    let deleted = 0
    for (const contactId of props.selectedContactIds) {
      // await deleteContact(contactId)
      deleted++
      updateOperationProgress(deleted, props.selectedContactIds.length)
      
      // Add small delay to show progress
      await new Promise(resolve => setTimeout(resolve, 50))
    }

    setOperationStatus({
      type: 'success',
      title: 'Deletion Complete',
      message: `Successfully deleted ${deleted} contacts.`
    })

    emit('operationComplete', { deleted })
    showDeleteConfirm.value = false
    clearSelection()
  } catch (error) {
    setOperationStatus({
      type: 'error',
      title: 'Deletion Failed',
      message: 'Failed to delete contacts. Please try again.'
    })
  }
}

const findDuplicates = async () => {
  setOperationStatus({
    type: 'loading',
    title: 'Finding Duplicates',
    message: 'Analyzing contacts for potential duplicates...'
  })

  try {
    // Implement duplicate detection logic
    // This would typically involve comparing names, emails, phone numbers, etc.
    await new Promise(resolve => setTimeout(resolve, 2000)) // Simulate processing

    setOperationStatus({
      type: 'success',
      title: 'Duplicate Analysis Complete',
      message: 'No duplicates found in selected contacts.'
    })

    emit('operationComplete', { duplicatesFound: 0 })
  } catch (error) {
    setOperationStatus({
      type: 'error',
      title: 'Analysis Failed',
      message: 'Failed to analyze contacts for duplicates. Please try again.'
    })
  }
}

const handleImportComplete = (result: any) => {
  setOperationStatus({
    type: 'success',
    title: 'Import Complete',
    message: `Successfully imported ${result.imported} contacts.`
  })

  emit('operationComplete', result)
  showImportModal.value = false
}

// Watch for selection changes
watch(
  () => props.selectedContactIds.length,
  (newLength) => {
    if (newLength === 0) {
      operationStatus.value = null
    }
  }
)
</script>

<style scoped>
.bulk-operations {
  @apply p-6 bg-white border-t;
}

/* Header */
.bulk-header {
  @apply flex items-center justify-between mb-6;
}

.selection-info {
  @apply flex items-center gap-2 text-lg font-semibold text-gray-900;
}

.clear-button {
  @apply flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 transition-colors;
}

/* Quick Actions */
.quick-actions {
  @apply space-y-6 mb-8;
}

.action-group {
  @apply space-y-3;
}

.group-title {
  @apply text-sm font-semibold text-gray-700 uppercase tracking-wide;
}

.action-buttons {
  @apply flex flex-wrap gap-2;
}

.action-button {
  @apply flex items-center gap-2 px-4 py-2 text-sm font-medium border rounded-lg transition-colors;
}

.action-button.stage.prospect {
  @apply text-blue-700 bg-blue-50 border-blue-200 hover:bg-blue-100;
}

.action-button.stage.contact {
  @apply text-green-700 bg-green-50 border-green-200 hover:bg-green-100;
}

.action-button.stage.relationship {
  @apply text-purple-700 bg-purple-50 border-purple-200 hover:bg-purple-100;
}

.action-button.stage.partner {
  @apply text-yellow-700 bg-yellow-50 border-yellow-200 hover:bg-yellow-100;
}

.action-button.priority.high {
  @apply text-red-700 bg-red-50 border-red-200 hover:bg-red-100;
}

.action-button.priority.medium {
  @apply text-yellow-700 bg-yellow-50 border-yellow-200 hover:bg-yellow-100;
}

.action-button.priority.low {
  @apply text-green-700 bg-green-50 border-green-200 hover:bg-green-100;
}

.action-button.tags {
  @apply text-indigo-700 bg-indigo-50 border-indigo-200 hover:bg-indigo-100;
}

.action-button.export {
  @apply text-blue-700 bg-blue-50 border-blue-200 hover:bg-blue-100;
}

.action-button.import {
  @apply text-green-700 bg-green-50 border-green-200 hover:bg-green-100;
}

.action-button.delete {
  @apply text-red-700 bg-red-50 border-red-200 hover:bg-red-100;
}

/* Advanced Operations */
.advanced-operations {
  @apply space-y-4;
}

.section-title {
  @apply text-lg font-semibold text-gray-900;
}

.operation-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4;
}

.operation-card {
  @apply p-4 bg-gray-50 border border-gray-200 rounded-lg space-y-3;
}

.card-header {
  @apply flex items-center gap-2;
}

.card-title {
  @apply font-semibold text-gray-900;
}

.card-description {
  @apply text-sm text-gray-600;
}

.card-button {
  @apply w-full px-4 py-2 text-sm font-medium text-blue-600 border border-blue-300 rounded-lg hover:bg-blue-50 transition-colors;
}

/* Operation Status */
.operation-status {
  @apply mt-6 p-4 border rounded-lg;
}

.status-content {
  @apply flex items-start gap-3;
}

.status-content.loading {
  @apply bg-blue-50 border-blue-200;
}

.status-content.success {
  @apply bg-green-50 border-green-200;
}

.status-content.error {
  @apply bg-red-50 border-red-200;
}

.status-content.loading .status-text {
  @apply text-blue-900;
}

.status-content.success .status-text {
  @apply text-green-900;
}

.status-content.error .status-text {
  @apply text-red-900;
}

.status-text {
  @apply flex-1 space-y-2;
}

.status-title {
  @apply font-semibold;
}

.status-message {
  @apply text-sm opacity-80;
}

.status-progress {
  @apply space-y-1;
}

.progress-bar {
  @apply w-full bg-gray-200 rounded-full h-2 overflow-hidden;
}

.progress-fill {
  @apply h-full bg-blue-600 transition-all duration-300;
}

.progress-text {
  @apply text-xs font-medium;
}

.status-close {
  @apply p-1 text-gray-500 hover:text-gray-700 transition-colors;
}

/* Responsive */
@media (max-width: 768px) {
  .bulk-header {
    @apply flex-col gap-3 items-stretch;
  }
  
  .action-buttons {
    @apply grid grid-cols-1 gap-2;
  }
  
  .operation-grid {
    @apply grid-cols-1;
  }
}
</style>