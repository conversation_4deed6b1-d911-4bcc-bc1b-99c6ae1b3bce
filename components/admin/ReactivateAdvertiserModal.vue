<template>
  <div
    v-if="open"
    class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
    @click="handleClose"
  >
    <div
      class="bg-gradient-to-br from-gray-900/95 via-gray-800/95 to-gray-900/95 rounded-xl border border-white/10 p-6 max-w-md w-full shadow-2xl"
      @click.stop
    >
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-xl font-bold text-white">Reactivate Advertiser</h3>
        <button
          class="p-2 rounded-lg hover:bg-white/10 text-gray-400 hover:text-white transition-colors duration-200"
          @click="handleClose"
        >
          <Icon name="mdi:close" class="text-xl" />
        </button>
      </div>

      <form class="space-y-4" @submit.prevent="handleSubmit">
        <!-- Advertiser Info -->
        <div v-if="advertiser">
          <div class="bg-gray-800/30 rounded-lg p-4 mb-4">
            <h4 class="font-semibold text-white mb-2">{{ advertiser.userName }}</h4>
            <div class="text-sm text-gray-400">
              <p>Previous Campaigns: {{ advertiser.totalCampaigns }}</p>
              <p>Total Spent: ${{ advertiser.totalSpent.toFixed(2) }}</p>
            </div>
          </div>
        </div>

        <!-- Subscription Selection -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Select Campaign to Reactivate <span class="text-red-400">*</span>
          </label>
          <select
            v-model="selectedSubscriptionId"
            required
            class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Select a previous campaign</option>
            <option 
              v-for="subscription in advertiser?.subscriptions || []" 
              :key="subscription.id" 
              :value="subscription.id"
            >
              {{ subscription.spot_name }} - {{ formatDate(subscription.start_date) }} to {{ formatDate(subscription.end_date) }}
            </option>
          </select>
        </div>

        <!-- New Date Range -->
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">
              New Start Date <span class="text-red-400">*</span>
            </label>
            <input
              v-model="form.startDate"
              type="date"
              required
              :min="today"
              class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">
              New End Date <span class="text-red-400">*</span>
            </label>
            <input
              v-model="form.endDate"
              type="date"
              required
              :min="form.startDate || today"
              class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        <!-- Campaign Duration -->
        <div v-if="campaignDuration > 0" class="bg-blue-900/20 border border-blue-400/30 rounded-lg p-3">
          <p class="text-blue-300 text-sm">
            Campaign Duration: {{ campaignDuration }} days
          </p>
        </div>

        <!-- Error Display -->
        <div v-if="error" class="bg-red-900/20 border border-red-400/30 rounded-lg p-3">
          <p class="text-red-300 text-sm">{{ error }}</p>
        </div>

        <!-- Actions -->
        <div class="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            class="px-4 py-2 rounded-lg bg-gray-700/50 hover:bg-gray-600/50 text-gray-300 hover:text-white transition-colors duration-200"
            @click="handleClose"
          >
            Cancel
          </button>
          <button
            type="submit"
            :disabled="!selectedSubscriptionId || !form.startDate || !form.endDate || isSubmitting"
            class="px-4 py-2 rounded-lg bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span v-if="isSubmitting">Reactivating...</span>
            <span v-else>Reactivate Campaign</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useAds } from '~/composables/useAds';

interface Props {
  open: boolean;
  advertiser: any | null;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  close: [];
  success: [subscription: any];
}>();

const { updateAd } = useAds();

// Reactivate subscription function
const reactivateSubscription = async (id: string, data: any) => {
  return await updateAd(id, {
    status: 'active',
    start_date: data.startDate,
    end_date: data.endDate
  });
};

// Form state
const selectedSubscriptionId = ref('');
const form = ref({
  startDate: '',
  endDate: ''
});
const isSubmitting = ref(false);
const error = ref<string | null>(null);

// Get today's date in YYYY-MM-DD format
const today = computed(() => {
  return new Date().toISOString().split('T')[0];
});

// Calculate campaign duration
const campaignDuration = computed(() => {
  if (!form.value.startDate || !form.value.endDate) return 0;
  
  const start = new Date(form.value.startDate);
  const end = new Date(form.value.endDate);
  const timeDiff = end.getTime() - start.getTime();
  return Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
});

// Format date for display
const formatDate = (dateString: string) => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleDateString();
};

// Handle form submission
const handleSubmit = async () => {
  if (!selectedSubscriptionId.value || !form.value.startDate || !form.value.endDate) {
    error.value = 'Please fill in all required fields';
    return;
  }

  if (new Date(form.value.endDate) <= new Date(form.value.startDate)) {
    error.value = 'End date must be after start date';
    return;
  }

  try {
    isSubmitting.value = true;
    error.value = null;

    const result = await reactivateSubscription(
      selectedSubscriptionId.value,
      form.value.startDate,
      form.value.endDate
    );

    if (result) {
      emit('success', result);
      resetForm();
    } else {
      error.value = 'Failed to reactivate campaign. Please try again.';
    }
  } catch (err: any) {
    console.error('Error reactivating campaign:', err);
    error.value = err.message || 'Failed to reactivate campaign. Please try again.';
  } finally {
    isSubmitting.value = false;
  }
};

// Reset form
const resetForm = () => {
  selectedSubscriptionId.value = '';
  form.value = {
    startDate: '',
    endDate: ''
  };
  error.value = null;
};

// Handle close
const handleClose = () => {
  emit('close');
  resetForm();
};

// Watch for modal open/close to reset form
watch(() => props.open, (newValue) => {
  if (newValue) {
    resetForm();
  }
});
</script>

<style scoped>
/* Component styles are handled by Tailwind classes */
</style>