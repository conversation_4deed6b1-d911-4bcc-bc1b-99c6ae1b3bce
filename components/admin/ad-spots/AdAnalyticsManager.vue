<template>
  <div class="ad-analytics-manager">
    <!-- Header with date range selector -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Ad Analytics Management</h1>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Monitor and analyze platform-wide advertising performance
        </p>
      </div>
      
      <!-- Date Range Selector -->
      <div class="mt-4 md:mt-0 flex space-x-2">
        <div class="flex space-x-2 items-center">
          <label for="start-date" class="text-sm text-gray-700 dark:text-gray-300">From</label>
          <input
            id="start-date"
            v-model="startDateInput"
            type="date"
            class="px-2 py-1 border border-gray-300 dark:border-gray-700 rounded-md text-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
          />
        </div>
        <div class="flex space-x-2 items-center">
          <label for="end-date" class="text-sm text-gray-700 dark:text-gray-300">To</label>
          <input
            id="end-date"
            v-model="endDateInput"
            type="date"
            class="px-2 py-1 border border-gray-300 dark:border-gray-700 rounded-md text-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
          />
        </div>
        <button
          @click="applyDateRange"
          class="px-3 py-1 bg-[#0072ff] text-white rounded-md text-sm hover:bg-[#0054bb] transition-colors duration-200"
        >
          Apply
        </button>
      </div>
    </div>
    
    <!-- Date Range Presets -->
    <div class="flex flex-wrap gap-2 mb-6">
      <button
        v-for="preset in datePresets"
        :key="preset.label"
        @click="selectDatePreset(preset.days)"
        class="px-3 py-1 text-sm rounded-md transition-colors duration-200"
        :class="activeDatePreset === preset.days ? 'bg-[#0072ff] text-white' : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'"
      >
        {{ preset.label }}
      </button>
    </div>
    
    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#0072ff]"></div>
    </div>
    
    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-md mb-6">
      <p>{{ error }}</p>
      <button 
        @click="fetchData" 
        class="mt-2 text-sm font-medium text-red-700 dark:text-red-300 hover:text-red-900 dark:hover:text-red-200"
      >
        Try Again
      </button>
    </div>
    
    <!-- Empty State -->
    <div v-else-if="!hasData" class="text-center py-12 bg-gray-50 dark:bg-gray-800 rounded-lg">
      <Icon name="mdi:chart-line" class="h-16 w-16 text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">No Analytics Data Available</h3>
      <p class="mt-2 text-gray-500 dark:text-gray-400">
        There is no analytics data available for the selected date range.
      </p>
      <p class="mt-1 text-gray-500 dark:text-gray-400">
        Try selecting a different date range or check back later.
      </p>
    </div>
    
    <!-- Analytics Content -->
    <div v-else>
      <!-- Summary Metrics Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <div class="dark:bg-gray-800 rounded-lg shadow p-4">
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Views</h3>
          <p class="text-2xl font-bold text-gray-800 dark:text-gray-100">{{ formatNumber(summaryMetrics.views) }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ formatNumber(summaryMetrics.views / daysBetween) }} per day
          </p>
        </div>
        
        <div class="dark:bg-gray-800 rounded-lg shadow p-4">
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Clicks</h3>
          <p class="text-2xl font-bold text-gray-800 dark:text-gray-100">{{ formatNumber(summaryMetrics.clicks) }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ formatNumber(summaryMetrics.ctr, { style: 'percent', maximumFractionDigits: 2 }) }} CTR
          </p>
        </div>
        
        <div class="dark:bg-gray-800 rounded-lg shadow p-4">
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Revenue</h3>
          <p class="text-2xl font-bold text-gray-800 dark:text-gray-100">
            {{ formatNumber(summaryMetrics.cost, { style: 'currency', currency: 'USD' }) }}
          </p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ formatNumber(summaryMetrics.cost / daysBetween, { style: 'currency', currency: 'USD' }) }} per day
          </p>
        </div>
        
        <div class="dark:bg-gray-800 rounded-lg shadow p-4">
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Active Subscriptions</h3>
          <p class="text-2xl font-bold text-gray-800 dark:text-gray-100">{{ activeSubscriptions }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ formatNumber(summaryMetrics.cost / activeSubscriptions, { style: 'currency', currency: 'USD' }) }} avg. value
          </p>
        </div>
      </div>
      
      <!-- Performance Chart -->
      <div class="dark:bg-gray-800 rounded-lg shadow p-4 mb-8">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-medium text-gray-800 dark:text-gray-200">Platform Performance</h2>
          <div class="flex space-x-2">
            <button
              v-for="interval in chartIntervals"
              :key="interval.value"
              @click="chartInterval = interval.value"
              class="px-3 py-1 text-sm rounded-md transition-colors duration-200"
              :class="chartInterval === interval.value ? 'bg-[#0072ff] text-white' : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'"
            >
              {{ interval.label }}
            </button>
          </div>
        </div>
        <div class="h-64">
          <canvas ref="performanceChartRef"></canvas>
        </div>
      </div>
      
      <!-- Performance by Ad Spot Type -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <!-- Ad Spot Performance -->
        <div class="dark:bg-gray-800 rounded-lg shadow p-4">
          <h2 class="text-lg font-medium text-gray-800 dark:text-gray-200 mb-4">Performance by Ad Spot Type</h2>
          <div class="h-64">
            <canvas ref="adSpotTypeChartRef"></canvas>
          </div>
        </div>
        
        <!-- Revenue Distribution -->
        <div class="dark:bg-gray-800 rounded-lg shadow p-4">
          <h2 class="text-lg font-medium text-gray-800 dark:text-gray-200 mb-4">Revenue Distribution</h2>
          <div class="h-64">
            <canvas ref="revenueChartRef"></canvas>
          </div>
        </div>
      </div>
      
      <!-- Top Performing Ad Spots -->
      <div class="dark:bg-gray-800 rounded-lg shadow p-4 mb-8">
        <h2 class="text-lg font-medium text-gray-800 dark:text-gray-200 mb-4">Top Performing Ad Spots</h2>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead>
              <tr>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Ad Spot</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Views</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Clicks</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">CTR</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Revenue</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Subscriptions</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
              <tr v-for="spot in topPerformingSpots" :key="spot.ad_spot_id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                <td class="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">{{ spot.ad_spot_name }}</td>
                <td class="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">{{ formatNumber(spot.views) }}</td>
                <td class="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">{{ formatNumber(spot.clicks) }}</td>
                <td class="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">{{ formatNumber(spot.ctr, { style: 'percent', maximumFractionDigits: 2 }) }}</td>
                <td class="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">{{ formatNumber(spot.revenue, { style: 'currency', currency: 'USD' }) }}</td>
                <td class="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">{{ spot.subscription_count }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      
      <!-- Revenue Projections -->
      <div class="dark:bg-gray-800 rounded-lg shadow p-4 mb-8">
        <h2 class="text-lg font-medium text-gray-800 dark:text-gray-200 mb-4">Revenue Projections</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Monthly Projection</h3>
            <p class="text-2xl font-bold text-gray-800 dark:text-gray-100">
              {{ formatNumber(monthlyProjection, { style: 'currency', currency: 'USD' }) }}
            </p>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              Based on current daily average
            </p>
          </div>
          
          <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Quarterly Projection</h3>
            <p class="text-2xl font-bold text-gray-800 dark:text-gray-100">
              {{ formatNumber(quarterlyProjection, { style: 'currency', currency: 'USD' }) }}
            </p>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              Based on current daily average
            </p>
          </div>
          
          <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Annual Projection</h3>
            <p class="text-2xl font-bold text-gray-800 dark:text-gray-100">
              {{ formatNumber(annualProjection, { style: 'currency', currency: 'USD' }) }}
            </p>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              Based on current daily average
            </p>
          </div>
        </div>
      </div>
      
      <!-- Export Controls -->
      <div class="flex justify-end mb-8">
        <button
          @click="exportData"
          class="bg-[#0072ff] text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-[#0054bb] focus:outline-none focus:ring-2 focus:ring-[#0072ff] focus:ring-offset-2"
        >
          Export Data
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useAdAnalytics } from '~/composables/useAdAnalytics'
import Chart from 'chart.js/auto'
import moment from 'moment'

// Analytics composable
const {
  isLoading,
  error,
  startDate,
  endDate,
  dailyStats,
  subscriptionStats,
  summaryMetrics,
  fetchAllAnalytics,
  getTimeSeriesData,
  getPerformanceByAdSpot,
  setDateRange,
  exportAsCSV
} = useAdAnalytics()

// Chart references
const performanceChartRef = ref<HTMLCanvasElement | null>(null)
const adSpotTypeChartRef = ref<HTMLCanvasElement | null>(null)
const revenueChartRef = ref<HTMLCanvasElement | null>(null)

// Chart instances
let performanceChart: Chart | null = null
let adSpotTypeChart: Chart | null = null
let revenueChart: Chart | null = null

// Chart interval
const chartInterval = ref<'daily' | 'weekly' | 'monthly'>('daily')

// Date inputs
const startDateInput = ref(moment(startDate.value).format('YYYY-MM-DD'))
const endDateInput = ref(moment(endDate.value).format('YYYY-MM-DD'))

// Active date preset
const activeDatePreset = ref(30) // Default to 30 days

// Date presets
const datePresets = [
  { label: 'Last 7 days', days: 7 },
  { label: 'Last 30 days', days: 30 },
  { label: 'Last 90 days', days: 90 },
  { label: 'Year to date', days: 'ytd' }
]

// Chart intervals
const chartIntervals = [
  { label: 'Daily', value: 'daily' },
  { label: 'Weekly', value: 'weekly' },
  { label: 'Monthly', value: 'monthly' }
]

// Computed properties
const hasData = computed(() => dailyStats.value.length > 0)

const daysBetween = computed(() => {
  const start = moment(startDate.value)
  const end = moment(endDate.value)
  return end.diff(start, 'days') + 1
})

const activeSubscriptions = computed(() => {
  return subscriptionStats.value.filter(sub => sub.status === 'active').length
})

const dailyRevenue = computed(() => {
  return summaryMetrics.value.cost / daysBetween.value
})

const monthlyProjection = computed(() => {
  return dailyRevenue.value * 30
})

const quarterlyProjection = computed(() => {
  return dailyRevenue.value * 90
})

const annualProjection = computed(() => {
  return dailyRevenue.value * 365
})

const topPerformingSpots = computed(() => {
  // Group by ad spot
  const spotPerformance: Record<string, any> = {}
  
  // Process performance data
  const performanceData = getPerformanceByAdSpot()
  
  performanceData.forEach(item => {
    const spotId = item.ad_spot_id
    
    if (!spotPerformance[spotId]) {
      spotPerformance[spotId] = {
        ad_spot_id: spotId,
        ad_spot_name: item.ad_spot_name,
        views: 0,
        clicks: 0,
        ctr: 0,
        revenue: 0,
        subscription_count: 0
      }
    }
    
    spotPerformance[spotId].views += item.views
    spotPerformance[spotId].clicks += item.clicks
    spotPerformance[spotId].revenue += item.cost
    spotPerformance[spotId].subscription_count++
  })
  
  // Calculate CTR
  Object.values(spotPerformance).forEach((spot: any) => {
    if (spot.views > 0) {
      spot.ctr = (spot.clicks / spot.views) * 100
    }
  })
  
  // Sort by clicks and return top 5
  return Object.values(spotPerformance)
    .sort((a: any, b: any) => b.clicks - a.clicks)
    .slice(0, 5)
})

// Methods
const fetchData = async () => {
  await fetchAllAnalytics()
  updateCharts()
}

const selectDatePreset = (days: number | string) => {
  const end = new Date()
  let start: Date
  
  if (days === 'ytd') {
    // Year to date
    start = new Date(end.getFullYear(), 0, 1) // January 1st of current year
    activeDatePreset.value = 'ytd'
  } else {
    // Last X days
    start = new Date()
    start.setDate(end.getDate() - (days as number))
    activeDatePreset.value = days as number
  }
  
  startDateInput.value = moment(start).format('YYYY-MM-DD')
  endDateInput.value = moment(end).format('YYYY-MM-DD')
  
  setDateRange(start, end)
  fetchData()
}

const applyDateRange = () => {
  const start = new Date(startDateInput.value)
  const end = new Date(endDateInput.value)
  
  // Reset active preset
  activeDatePreset.value = null
  
  setDateRange(start, end)
  fetchData()
}

const initCharts = () => {
  // Initialize performance chart
  if (performanceChartRef.value) {
    const ctx = performanceChartRef.value.getContext('2d')
    if (!ctx) return
    
    performanceChart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: [],
        datasets: []
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    })
  }
  
  // Initialize ad spot type chart
  if (adSpotTypeChartRef.value) {
    const ctx = adSpotTypeChartRef.value.getContext('2d')
    if (!ctx) return
    
    adSpotTypeChart = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: [],
        datasets: []
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    })
  }
  
  // Initialize revenue chart
  if (revenueChartRef.value) {
    const ctx = revenueChartRef.value.getContext('2d')
    if (!ctx) return
    
    revenueChart = new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: [],
        datasets: []
      },
      options: {
        responsive: true,
        maintainAspectRatio: false
      }
    })
  }
}

const updateCharts = () => {
  if (!performanceChart || !adSpotTypeChart || !revenueChart) return
  
  // Update performance chart
  const viewsData = getTimeSeriesData('views', chartInterval.value)
  const clicksData = getTimeSeriesData('clicks', chartInterval.value)
  
  performanceChart.data.labels = viewsData.map(item => item.date)
  performanceChart.data.datasets = [
    {
      label: 'Views',
      data: viewsData.map(item => item.value),
      borderColor: '#3B82F6',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      tension: 0.1
    },
    {
      label: 'Clicks',
      data: clicksData.map(item => item.value),
      borderColor: '#10B981',
      backgroundColor: 'rgba(16, 185, 129, 0.1)',
      tension: 0.1
    }
  ]
  
  performanceChart.update()
  
  // Update ad spot type chart
  const performanceData = getPerformanceByAdSpot()
  
  // Group by ad spot type
  const spotTypeData: Record<string, { views: number, clicks: number }> = {}
  
  performanceData.forEach(item => {
    const spotName = item.ad_spot_name
    const spotType = spotName.includes('Premium') ? 'Premium' : 
                    spotName.includes('Standard') ? 'Standard' : 'Basic'
    
    if (!spotTypeData[spotType]) {
      spotTypeData[spotType] = { views: 0, clicks: 0 }
    }
    
    spotTypeData[spotType].views += item.views
    spotTypeData[spotType].clicks += item.clicks
  })
  
  adSpotTypeChart.data.labels = Object.keys(spotTypeData)
  adSpotTypeChart.data.datasets = [
    {
      label: 'Views',
      data: Object.values(spotTypeData).map(item => item.views),
      backgroundColor: '#3B82F6'
    },
    {
      label: 'Clicks',
      data: Object.values(spotTypeData).map(item => item.clicks),
      backgroundColor: '#10B981'
    }
  ]
  
  adSpotTypeChart.update()
  
  // Update revenue chart
  const revenueData: Record<string, number> = {}
  
  performanceData.forEach(item => {
    const spotName = item.ad_spot_name
    
    if (!revenueData[spotName]) {
      revenueData[spotName] = 0
    }
    
    revenueData[spotName] += item.cost
  })
  
  // Sort by revenue and get top 5
  const topRevenueSpots = Object.entries(revenueData)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 5)
  
  // Calculate "Other" category
  const otherRevenue = Object.entries(revenueData)
    .sort((a, b) => b[1] - a[1])
    .slice(5)
    .reduce((sum, [_, value]) => sum + value, 0)
  
  // Add "Other" if there are more than 5 spots
  if (otherRevenue > 0) {
    topRevenueSpots.push(['Other', otherRevenue])
  }
  
  revenueChart.data.labels = topRevenueSpots.map(([name]) => name)
  revenueChart.data.datasets = [
    {
      data: topRevenueSpots.map(([_, value]) => value),
      backgroundColor: [
        '#3B82F6', // Blue
        '#10B981', // Green
        '#F59E0B', // Yellow
        '#EF4444', // Red
        '#8B5CF6', // Purple
        '#6B7280'  // Gray (for "Other")
      ]
    }
  ]
  
  revenueChart.update()
}

const formatNumber = (value: number, options: Intl.NumberFormatOptions = {}) => {
  if (value === undefined || value === null) return '0'
  
  if (options.style === 'percent') {
    value = value / 100
  }
  
  return new Intl.NumberFormat('en-US', options).format(value)
}

const exportData = () => {
  const csvContent = exportAsCSV()
  if (!csvContent) return
  
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.setAttribute('href', url)
  link.setAttribute('download', `ad_analytics_admin_${moment(startDate.value).format('YYYY-MM-DD')}_to_${moment(endDate.value).format('YYYY-MM-DD')}.csv`)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// Watch for changes in chart interval
watch(chartInterval, () => {
  updateCharts()
})

// Fetch data and initialize charts on mount
onMounted(async () => {
  initCharts()
  await fetchData()
})
</script>

<style scoped>
.ad-analytics-manager {
  @apply p-6;
}
</style>
