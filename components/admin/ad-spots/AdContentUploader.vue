<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
    <div class="p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
          Upload Ad Content for "{{ adSpot?.name }}"
        </h3>
        <button @click="$emit('close')" class="text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400">
          <Icon name="mdi:close" class="h-5 w-5" />
        </button>
      </div>

      <!-- Ad Spot Info -->
      <div class="mb-6 p-4 bg-blue-50 dark:bg-blue-900/30 border border-blue-100 dark:border-blue-800 rounded-md">
        <div class="flex items-center">
          <Icon name="mdi:information" class="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" />
          <div>
            <p class="text-sm font-medium text-blue-800 dark:text-blue-200">
              Position: {{ formatPosition(adSpot?.position || adSpot?.location) }}
            </p>
            <p class="text-xs text-blue-700 dark:text-blue-300">
              Content will appear in the {{ adSpot?.position || adSpot?.location }} area of the homepage
            </p>
          </div>
        </div>
      </div>

      <!-- Upload Form -->
      <form @submit.prevent="uploadContent" class="space-y-6">
        <!-- Content Type -->
        <div>
          <label for="contentType" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Content Type <span class="text-red-500">*</span>
          </label>
          <select
            id="contentType"
            v-model="form.contentType"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
            :class="{ 'border-red-500 dark:border-red-500': errors.contentType }"
            required
          >
            <option value="">Select Content Type</option>
            <option value="banner">Banner Ad</option>
            <option value="image">Image Ad</option>
            <option value="promotional">Promotional Content</option>
          </select>
          <p v-if="errors.contentType" class="mt-1 text-sm text-red-600 dark:text-red-400">{{ errors.contentType }}</p>
        </div>

        <!-- Title -->
        <div>
          <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Content Title <span class="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="title"
            v-model="form.title"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
            :class="{ 'border-red-500 dark:border-red-500': errors.title }"
            placeholder="Summer Sale Banner"
            required
          />
          <p v-if="errors.title" class="mt-1 text-sm text-red-600 dark:text-red-400">{{ errors.title }}</p>
        </div>

        <!-- Description -->
        <div>
          <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Description
          </label>
          <textarea
            id="description"
            v-model="form.description"
            rows="3"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
            placeholder="Brief description of the ad content"
          ></textarea>
        </div>

        <!-- File Upload -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Upload Content <span class="text-red-500">*</span>
          </label>
          <div
            class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-dashed rounded-md transition-colors duration-200"
            :class="{
              'border-[#0072ff] dark:border-[#82aae3] bg-blue-50 dark:bg-blue-900/20': isDragOver,
              'border-gray-300 dark:border-gray-700 hover:border-[#0072ff] dark:hover:border-[#82aae3]': !isDragOver
            }"
            @dragover.prevent="handleDragOver"
            @dragleave.prevent="handleDragLeave"
            @drop.prevent="handleDrop"
          >
            <div class="space-y-1 text-center">
              <Icon
                :name="isDragOver ? 'mdi:cloud-upload-outline' : 'mdi:cloud-upload'"
                class="mx-auto h-12 w-12"
                :class="isDragOver ? 'text-[#0072ff] dark:text-[#82aae3]' : 'text-gray-400 dark:text-gray-500'"
              />
              <div class="flex text-sm text-gray-600 dark:text-gray-400">
                <label for="file-upload" class="relative cursor-pointer bg-white dark:bg-gray-800 rounded-md font-medium text-[#0072ff] dark:text-[#82aae3] hover:text-[#0054bb] dark:hover:text-white focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-[#0072ff] dark:focus-within:ring-offset-gray-800">
                  <span>Upload a file</span>
                  <input
                    id="file-upload"
                    name="file-upload"
                    type="file"
                    class="sr-only"
                    accept="image/*"
                    @change="handleFileSelect"
                  />
                </label>
                <p class="pl-1">or drag and drop</p>
              </div>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                PNG, JPG, GIF, WebP up to 10MB
              </p>
            </div>
          </div>
          <p v-if="errors.file" class="mt-1 text-sm text-red-600 dark:text-red-400">{{ errors.file }}</p>
        </div>

        <!-- File Preview -->
        <div v-if="selectedFile || form.previewUrl" class="space-y-4">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">Preview</h4>
          <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div class="flex items-start space-x-4">
              <div class="flex-shrink-0">
                <img
                  :src="previewImageUrl"
                  :alt="form.title"
                  class="h-24 w-24 object-cover rounded-lg border border-gray-200 dark:border-gray-700"
                />
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ selectedFile?.name || form.fileName || 'Uploaded Content' }}
                </p>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ selectedFile ? formatFileSize(selectedFile.size) : 'File size not available' }}
                </p>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  Type: {{ form.contentType || 'Not specified' }}
                </p>
              </div>
              <button
                type="button"
                @click="removeFile"
                class="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300"
              >
                <Icon name="mdi:delete" class="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>

        <!-- Link URL (Optional) -->
        <div>
          <label for="linkUrl" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Link URL (Optional)
          </label>
          <input
            type="url"
            id="linkUrl"
            v-model="form.linkUrl"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
            placeholder="https://example.com"
          />
          <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
            URL to redirect when the ad is clicked
          </p>
        </div>

        <!-- Status -->
        <div>
          <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Status <span class="text-red-500">*</span>
          </label>
          <select
            id="status"
            v-model="form.status"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
            required
          >
            <option value="draft">Draft</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
        </div>

        <!-- Error message -->
        <div v-if="formError" class="p-4 bg-red-50 dark:bg-red-900/30 border border-red-100 dark:border-red-800 rounded-md">
          <p class="text-sm text-red-600 dark:text-red-400">{{ formError }}</p>
        </div>

        <!-- Form actions -->
        <div class="flex items-center justify-end space-x-3 pt-4">
          <button
            type="button"
            @click="$emit('close')"
            class="px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0072ff] dark:focus:ring-offset-gray-800"
          >
            Cancel
          </button>
          <button
            type="submit"
            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#0072ff] hover:bg-[#0054bb] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0072ff] dark:focus:ring-offset-gray-800"
            :disabled="isUploading || (!selectedFile && !form.previewUrl)"
          >
            <span v-if="isUploading" class="flex items-center">
              <Icon name="mdi:loading" class="animate-spin mr-2 h-4 w-4" />
              Uploading...
            </span>
            <span v-else>
              Upload Content
            </span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useAdContent } from '~/composables/useAdContent'

const props = defineProps({
  adSpot: {
    type: Object,
    required: true
  },
  existingContent: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['close', 'uploaded', 'updated'])

// Initialize composable
const { createAdContent, updateAdContent, isUploading: composableUploading, error: contentError } = useAdContent()

// Form state
const form = reactive({
  contentType: '',
  title: '',
  description: '',
  linkUrl: '',
  status: 'draft',
  previewUrl: '',
  fileName: ''
})

// File handling
const selectedFile = ref<File | null>(null)
const isUploading = computed(() => composableUploading.value)
const formError = ref('')
const isDragOver = ref(false)

// Computed property for preview image URL
const previewImageUrl = computed(() => {
  if (form.previewUrl) {
    return form.previewUrl
  }
  if (selectedFile.value) {
    return URL.createObjectURL(selectedFile.value)
  }
  return ''
})

// Form validation
const errors = reactive({
  contentType: '',
  title: '',
  file: ''
})

// Format position for display
const formatPosition = (position: string) => {
  if (!position) return 'Not specified'
  switch (position.toLowerCase()) {
    case 'top': return 'Top (Header Area)'
    case 'middle': return 'Middle (Content Area)'
    case 'bottom': return 'Bottom (Footer Area)'
    default: return position
  }
}

// Format file size
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Validate and process file
const processFile = (file: File) => {
  // Validate file type
  if (!file.type.startsWith('image/')) {
    errors.file = 'Please select an image file'
    return false
  }

  // Validate file size (10MB limit)
  if (file.size > 10 * 1024 * 1024) {
    errors.file = 'File size must be less than 10MB'
    return false
  }

  selectedFile.value = file
  errors.file = ''

  // Auto-fill title if empty
  if (!form.title) {
    form.title = file.name.replace(/\.[^/.]+$/, '')
  }

  return true
}

// Handle file selection
const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (file) {
    processFile(file)
  }
}

// Drag and drop handlers
const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false

  const files = event.dataTransfer?.files
  if (files && files.length > 0) {
    processFile(files[0])
  }
}

// Remove selected file
const removeFile = () => {
  selectedFile.value = null
  form.previewUrl = ''
  form.fileName = ''
  const fileInput = document.getElementById('file-upload') as HTMLInputElement
  if (fileInput) {
    fileInput.value = ''
  }
}

// Validate form
const validateForm = () => {
  let isValid = true
  
  // Reset errors
  Object.keys(errors).forEach(key => {
    errors[key] = ''
  })
  
  // Validate content type
  if (!form.contentType) {
    errors.contentType = 'Content type is required'
    isValid = false
  }
  
  // Validate title
  if (!form.title.trim()) {
    errors.title = 'Title is required'
    isValid = false
  }
  
  // Validate file
  if (!selectedFile.value && !form.previewUrl) {
    errors.file = 'Please select a file to upload'
    isValid = false
  }
  
  return isValid
}

// Initialize form with existing content if editing
onMounted(() => {
  if (props.existingContent) {
    form.contentType = props.existingContent.contentType || ''
    form.title = props.existingContent.title || ''
    form.description = props.existingContent.description || ''
    form.linkUrl = props.existingContent.linkUrl || ''
    form.status = props.existingContent.status || 'draft'
    form.previewUrl = props.existingContent.url || ''
    form.fileName = props.existingContent.fileName || ''
  }
})

// Upload content
const uploadContent = async () => {
  if (!validateForm()) {
    return
  }

  formError.value = ''

  try {
    if (props.existingContent) {
      // Update existing content
      const updatedContent = await updateAdContent(props.existingContent.id, {
        contentType: form.contentType,
        title: form.title,
        description: form.description,
        linkUrl: form.linkUrl,
        status: form.status
      })

      if (updatedContent) {
        emit('updated', updatedContent)
      } else {
        formError.value = contentError.value || 'Failed to update content'
      }
    } else {
      // Create new content
      if (!selectedFile.value) {
        formError.value = 'Please select a file to upload'
        return
      }

      const newContent = await createAdContent(
        props.adSpot.id,
        selectedFile.value,
        {
          contentType: form.contentType,
          title: form.title,
          description: form.description,
          linkUrl: form.linkUrl,
          status: form.status
        }
      )

      if (newContent) {
        emit('uploaded', newContent)
      } else {
        formError.value = contentError.value || 'Failed to upload content'
      }
    }
  } catch (err: any) {
    console.error('Error uploading content:', err)
    formError.value = err.message || 'An unexpected error occurred'
  }
}
</script>
