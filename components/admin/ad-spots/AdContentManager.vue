<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-6xl w-full mx-4">
    <div class="p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
          Manage Content for "{{ adSpot?.name }}"
        </h3>
        <button @click="$emit('close')" class="text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400">
          <Icon name="mdi:close" class="h-5 w-5" />
        </button>
      </div>

      <!-- Ad Spot Info -->
      <div class="mb-6 p-4 bg-blue-50 dark:bg-blue-900/30 border border-blue-100 dark:border-blue-800 rounded-md">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <Icon name="mdi:information" class="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" />
            <div>
              <p class="text-sm font-medium text-blue-800 dark:text-blue-200">
                Position: {{ formatPosition(adSpot?.position || adSpot?.location) }}
              </p>
              <p class="text-xs text-blue-700 dark:text-blue-300">
                {{ adSpot?.description || 'No description available' }}
              </p>
            </div>
          </div>
          <button
            @click="showUploader = true"
            class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-[#0072ff] hover:bg-[#0054bb] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0072ff] dark:focus:ring-offset-gray-800"
          >
            <Icon name="mdi:plus" class="mr-1 h-4 w-4" />
            Add Content
          </button>
        </div>
      </div>

      <!-- Content List -->
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <h4 class="text-base font-medium text-gray-900 dark:text-white">
            Content Items ({{ contentItems.length }})
          </h4>
          <div class="flex items-center space-x-2">
            <select
              v-model="statusFilter"
              class="text-sm border border-gray-300 dark:border-gray-700 rounded-md px-3 py-1 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-200"
            >
              <option value="">All Status</option>
              <option value="active">Active</option>
              <option value="draft">Draft</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </div>

        <!-- Loading state -->
        <div v-if="isLoading" class="text-center py-8">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#0072ff] dark:border-[#82aae3]"></div>
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Loading content...</p>
        </div>

        <!-- Empty state -->
        <div v-else-if="filteredContent.length === 0" class="text-center py-12">
          <Icon name="mdi:image-off" class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" />
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No content found</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {{ statusFilter ? `No ${statusFilter} content items.` : 'Get started by adding some content.' }}
          </p>
          <div class="mt-6">
            <button
              @click="showUploader = true"
              class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-[#0072ff] hover:bg-[#0054bb] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0072ff]"
            >
              <Icon name="mdi:plus" class="mr-2 -ml-1 h-5 w-5" />
              Add Content
            </button>
          </div>
        </div>

        <!-- Content Grid -->
        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div
            v-for="content in filteredContent"
            :key="content.id"
            class="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200"
          >
            <!-- Content Preview -->
            <div class="aspect-w-16 aspect-h-9 bg-gray-100 dark:bg-gray-600 rounded-t-lg overflow-hidden">
              <img
                :src="content.url || content.previewUrl"
                :alt="content.title"
                class="w-full h-48 object-cover"
                @error="handleImageError"
              />
              <div class="absolute top-2 right-2">
                <span
                  class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                  :class="{
                    'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300': content.status === 'active',
                    'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300': content.status === 'draft',
                    'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300': content.status === 'inactive'
                  }"
                >
                  {{ content.status }}
                </span>
              </div>
            </div>

            <!-- Content Info -->
            <div class="p-4">
              <div class="flex items-start justify-between">
                <div class="flex-1 min-w-0">
                  <h5 class="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {{ content.title }}
                  </h5>
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {{ content.contentType }} • {{ formatDate(content.created_at) }}
                  </p>
                  <p v-if="content.description" class="text-xs text-gray-600 dark:text-gray-300 mt-2 line-clamp-2">
                    {{ content.description }}
                  </p>
                </div>
              </div>

              <!-- Actions -->
              <div class="mt-4 flex items-center justify-between">
                <div class="flex items-center space-x-2">
                  <button
                    @click="previewContent(content)"
                    class="text-[#0072ff] dark:text-[#82aae3] hover:text-[#0054bb] dark:hover:text-white text-xs"
                  >
                    <Icon name="mdi:eye" class="h-4 w-4" />
                  </button>
                  <button
                    @click="editContent(content)"
                    class="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 text-xs"
                  >
                    <Icon name="mdi:pencil" class="h-4 w-4" />
                  </button>
                  <button
                    @click="confirmDelete(content)"
                    class="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 text-xs"
                  >
                    <Icon name="mdi:delete" class="h-4 w-4" />
                  </button>
                </div>
                <div class="flex items-center space-x-1">
                  <button
                    v-if="content.status !== 'active'"
                    @click="toggleStatus(content, 'active')"
                    class="text-xs px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 rounded hover:bg-green-200 dark:hover:bg-green-800"
                  >
                    Activate
                  </button>
                  <button
                    v-if="content.status === 'active'"
                    @click="toggleStatus(content, 'inactive')"
                    class="text-xs px-2 py-1 bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300 rounded hover:bg-red-200 dark:hover:bg-red-800"
                  >
                    Deactivate
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Upload Modal -->
    <div v-if="showUploader" class="fixed inset-0 bg-gray-500 dark:bg-gray-900 bg-opacity-50 dark:bg-opacity-70 flex items-center justify-center z-50">
      <AdminAdSpotsAdContentUploader
        :ad-spot="adSpot"
        :existing-content="editingContent"
        @close="closeUploader"
        @uploaded="handleUploaded"
        @updated="handleUpdated"
      />
    </div>

    <!-- Preview Modal -->
    <div v-if="previewingContent" class="fixed inset-0 bg-gray-500 dark:bg-gray-900 bg-opacity-50 dark:bg-opacity-70 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full mx-4">
        <div class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">
              Preview: {{ previewingContent.title }}
            </h3>
            <button @click="previewingContent = null" class="text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400">
              <Icon name="mdi:close" class="h-5 w-5" />
            </button>
          </div>
          <div class="text-center">
            <img
              :src="previewingContent.url || previewingContent.previewUrl"
              :alt="previewingContent.title"
              class="max-w-full max-h-96 mx-auto rounded-lg shadow-lg"
            />
            <div class="mt-4 text-sm text-gray-600 dark:text-gray-400">
              <p><strong>Type:</strong> {{ previewingContent.contentType }}</p>
              <p v-if="previewingContent.linkUrl"><strong>Link:</strong> {{ previewingContent.linkUrl }}</p>
              <p v-if="previewingContent.description"><strong>Description:</strong> {{ previewingContent.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div v-if="deletingContent" class="fixed inset-0 bg-gray-500 dark:bg-gray-900 bg-opacity-50 dark:bg-opacity-70 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Confirm Deletion</h3>
            <button @click="deletingContent = null" class="text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400">
              <Icon name="mdi:close" class="h-5 w-5" />
            </button>
          </div>
          <div class="mb-6">
            <p class="text-sm text-gray-600 dark:text-gray-300">
              Are you sure you want to delete "{{ deletingContent?.title }}"? This action cannot be undone.
            </p>
          </div>
          <div class="flex items-center justify-end space-x-3">
            <button
              @click="deletingContent = null"
              class="px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              Cancel
            </button>
            <button
              @click="deleteContent"
              class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700"
            >
              Delete
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAdContent } from '~/composables/useAdContent'

const props = defineProps({
  adSpot: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['close', 'contentUpdated'])

// Initialize composable
const {
  adContent,
  isLoading,
  fetchAdSpotContent,
  updateAdContent,
  deleteAdContent
} = useAdContent()

// State
const statusFilter = ref('')
const showUploader = ref(false)
const editingContent = ref(null)
const previewingContent = ref(null)
const deletingContent = ref(null)

// Computed - filter content for this ad spot
const contentItems = computed(() => {
  return adContent.value.filter(item => item.adSpotId === props.adSpot.id)
})

// Computed
const filteredContent = computed(() => {
  if (!statusFilter.value) return contentItems.value
  return contentItems.value.filter(item => item.status === statusFilter.value)
})

// Format position for display
const formatPosition = (position: string) => {
  if (!position) return 'Not specified'
  switch (position.toLowerCase()) {
    case 'top': return 'Top (Header Area)'
    case 'middle': return 'Middle (Content Area)'
    case 'bottom': return 'Bottom (Footer Area)'
    default: return position
  }
}

// Format date
const formatDate = (timestamp: any) => {
  if (!timestamp) return 'N/A'
  const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp)
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date)
}

// Handle image error
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/placeholder-image.png' // You'll need to add a placeholder image
}

// Load content for ad spot
const loadContent = async () => {
  try {
    await fetchAdSpotContent(props.adSpot.id)
  } catch (error) {
    console.error('Error loading content:', error)
  }
}

// Close uploader
const closeUploader = () => {
  showUploader.value = false
  editingContent.value = null
}

// Handle content uploaded
const handleUploaded = (data) => {
  closeUploader()
  loadContent() // Refresh content list
  emit('contentUpdated')
}

// Handle content updated
const handleUpdated = (data) => {
  closeUploader()
  loadContent() // Refresh content list
  emit('contentUpdated')
}

// Preview content
const previewContent = (content) => {
  previewingContent.value = content
}

// Edit content
const editContent = (content) => {
  editingContent.value = content
  showUploader.value = true
}

// Confirm delete
const confirmDelete = (content) => {
  deletingContent.value = content
}

// Delete content
const deleteContent = async () => {
  if (!deletingContent.value) return

  try {
    const success = await deleteAdContent(deletingContent.value.id)
    if (success) {
      deletingContent.value = null
      emit('contentUpdated')
    }
  } catch (error) {
    console.error('Error deleting content:', error)
  }
}

// Toggle content status
const toggleStatus = async (content, newStatus) => {
  try {
    const updatedContent = await updateAdContent(content.id, { status: newStatus })
    if (updatedContent) {
      emit('contentUpdated')
    }
  } catch (error) {
    console.error('Error updating content status:', error)
  }
}

// Load content on mount
onMounted(() => {
  loadContent()
})
</script>
