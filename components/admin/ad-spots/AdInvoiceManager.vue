<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full mx-4">
    <div class="p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
          {{ isEdit ? 'Edit Invoice' : 'Create New Invoice' }}
        </h3>
        <button @click="$emit('close')" class="text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400">
          <Icon name="mdi:close" class="h-5 w-5" />
        </button>
      </div>

      <form @submit.prevent="submitForm" class="space-y-6">
        <!-- Invoice Number -->
        <div>
          <label for="invoice_number" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Invoice Number <span class="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="invoice_number"
            v-model="form.invoice_number"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
            :class="{ 'border-red-500 dark:border-red-500': errors.invoice_number }"
            placeholder="INV-123456"
            required
          />
          <p v-if="errors.invoice_number" class="mt-1 text-sm text-red-600 dark:text-red-400">{{ errors.invoice_number }}</p>
        </div>

        <!-- Subscription Selection -->
        <div>
          <label for="subscription_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Subscription <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            <select
              id="subscription_id"
              v-model="form.subscription_id"
              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
              :class="{ 'border-red-500 dark:border-red-500': errors.subscription_id }"
              required
              @change="updateDetailsFromSubscription"
            >
              <option value="" disabled>Select a subscription</option>
              <option v-for="subscription in subscriptions" :key="subscription.id" :value="subscription.id">
                {{ getSubscriptionLabel(subscription) }}
              </option>
            </select>
          </div>
          <p v-if="errors.subscription_id" class="mt-1 text-sm text-red-600 dark:text-red-400">{{ errors.subscription_id }}</p>
        </div>

        <!-- Payment Selection -->
        <div>
          <label for="payment_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Payment
          </label>
          <div class="relative">
            <select
              id="payment_id"
              v-model="form.payment_id"
              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
              :class="{ 'border-red-500 dark:border-red-500': errors.payment_id }"
            >
              <option value="">No payment</option>
              <option v-for="payment in filteredPayments" :key="payment.id" :value="payment.id">
                {{ getPaymentLabel(payment) }}
              </option>
            </select>
          </div>
          <p v-if="errors.payment_id" class="mt-1 text-sm text-red-600 dark:text-red-400">{{ errors.payment_id }}</p>
        </div>

        <!-- Amount -->
        <div>
          <label for="amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Amount ($) <span class="text-red-500">*</span>
          </label>
          <input
            type="number"
            id="amount"
            v-model="form.amount"
            min="0"
            step="0.01"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
            :class="{ 'border-red-500 dark:border-red-500': errors.amount }"
            placeholder="99.99"
            required
          />
          <p v-if="errors.amount" class="mt-1 text-sm text-red-600 dark:text-red-400">{{ errors.amount }}</p>
        </div>

        <!-- Issue Date -->
        <div>
          <label for="issue_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Issue Date <span class="text-red-500">*</span>
          </label>
          <input
            type="date"
            id="issue_date"
            v-model="form.issue_date"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
            :class="{ 'border-red-500 dark:border-red-500': errors.issue_date }"
            required
            @change="updateDueDate"
          />
          <p v-if="errors.issue_date" class="mt-1 text-sm text-red-600 dark:text-red-400">{{ errors.issue_date }}</p>
        </div>

        <!-- Due Date -->
        <div>
          <label for="due_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Due Date <span class="text-red-500">*</span>
          </label>
          <input
            type="date"
            id="due_date"
            v-model="form.due_date"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
            :class="{ 'border-red-500 dark:border-red-500': errors.due_date }"
            :min="form.issue_date"
            required
          />
          <p v-if="errors.due_date" class="mt-1 text-sm text-red-600 dark:text-red-400">{{ errors.due_date }}</p>
        </div>

        <!-- Status -->
        <div>
          <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Status <span class="text-red-500">*</span>
          </label>
          <select
            id="status"
            v-model="form.status"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
            :class="{ 'border-red-500 dark:border-red-500': errors.status }"
            required
          >
            <option value="pending">Pending</option>
            <option value="paid">Paid</option>
            <option value="cancelled">Cancelled</option>
          </select>
          <p v-if="errors.status" class="mt-1 text-sm text-red-600 dark:text-red-400">{{ errors.status }}</p>
        </div>

        <!-- Notes -->
        <div>
          <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Notes
          </label>
          <textarea
            id="notes"
            v-model="form.notes"
            rows="3"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
            :class="{ 'border-red-500 dark:border-red-500': errors.notes }"
            placeholder="Additional notes about this invoice"
          ></textarea>
          <p v-if="errors.notes" class="mt-1 text-sm text-red-600 dark:text-red-400">{{ errors.notes }}</p>
        </div>

        <!-- Error message -->
        <div v-if="formError" class="p-4 bg-red-50 dark:bg-red-900/30 border border-red-100 dark:border-red-800 rounded-md">
          <p class="text-sm text-red-600 dark:text-red-400">{{ formError }}</p>
        </div>

        <!-- Form actions -->
        <div class="flex items-center justify-end space-x-3 pt-4">
          <button
            type="button"
            @click="$emit('close')"
            class="px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0072ff] dark:focus:ring-offset-gray-800"
          >
            Cancel
          </button>
          <button
            type="submit"
            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#0072ff] hover:bg-[#0054bb] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0072ff] dark:focus:ring-offset-gray-800"
            :disabled="isSubmitting"
          >
            <span v-if="isSubmitting" class="flex items-center">
              <Icon name="mdi:loading" class="animate-spin mr-2 h-4 w-4" />
              {{ isEdit ? 'Updating...' : 'Creating...' }}
            </span>
            <span v-else>
              {{ isEdit ? 'Update Invoice' : 'Create Invoice' }}
            </span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { useAdInvoices } from '~/composables/useAdInvoices'
import { useAdPayments } from '~/composables/useAdPayments'
import { useAds } from '~/composables/useAds'

const props = defineProps({
  invoice: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close', 'created', 'updated'])

// Initialize composables
const { createInvoice, updateInvoice, error: invoiceError } = useAdInvoices()
const { ads, fetchAds } = useAds()
const { payments, fetchAllPayments } = useAdPayments()

// Map unified ads to subscriptions for compatibility
const subscriptions = computed(() => ads.value)

// Get unique ad spots from unified ads
const adSpots = computed(() => {
  const spotMap = new Map()
  ads.value.forEach(ad => {
    const spotId = ad.legacy_spot_id || ad.id
    if (!spotMap.has(spotId)) {
      spotMap.set(spotId, {
        id: spotId,
        name: ad.name,
        description: ad.description,
        position: ad.position,
        price: ad.price,
        dimensions: ad.dimensions,
        status: ad.status
      })
    }
  })
  return Array.from(spotMap.values())
})

// State
const isSubmitting = ref(false)
const formError = ref('')

// Form state
const form = reactive({
  invoice_number: '',
  subscription_id: '',
  payment_id: '',
  amount: 0,
  issue_date: '',
  due_date: '',
  status: 'pending',
  notes: ''
})

// Form validation
const errors = reactive({
  invoice_number: '',
  subscription_id: '',
  payment_id: '',
  amount: '',
  issue_date: '',
  due_date: '',
  status: '',
  notes: ''
})

// Computed properties
const filteredPayments = computed(() => {
  if (!form.subscription_id) return []

  return payments.value.filter(payment =>
    payment.subscription_id === form.subscription_id
  )
})

// Initialize form with invoice data if editing
onMounted(async () => {
  try {
    // Generate invoice number if not editing
    if (!props.isEdit) {
      form.invoice_number = `INV-${Date.now()}`

      // Set issue date to today
      const today = new Date()
      form.issue_date = today.toISOString().split('T')[0]

      // Set due date to 30 days from today
      const dueDate = new Date(today)
      dueDate.setDate(dueDate.getDate() + 30)
      form.due_date = dueDate.toISOString().split('T')[0]
    }

    // Fetch subscriptions, payments, and ad spots
    await fetchAds()
    await fetchAllPayments()

    if (props.isEdit && props.invoice) {
      form.invoice_number = props.invoice.invoice_number || ''
      form.subscription_id = props.invoice.subscription_id || ''
      form.payment_id = props.invoice.payment_id || ''
      form.amount = props.invoice.amount || 0

      // Convert Firestore timestamps to date strings
      if (props.invoice.issue_date) {
        const issueDate = props.invoice.issue_date.toDate ?
          props.invoice.issue_date.toDate() :
          new Date(props.invoice.issue_date)
        form.issue_date = issueDate.toISOString().split('T')[0]
      }

      if (props.invoice.due_date) {
        const dueDate = props.invoice.due_date.toDate ?
          props.invoice.due_date.toDate() :
          new Date(props.invoice.due_date)
        form.due_date = dueDate.toISOString().split('T')[0]
      }

      form.status = props.invoice.status || 'pending'
      form.notes = props.invoice.notes || ''
    }
  } catch (err) {
    console.error('Error initializing form:', err)
    formError.value = 'Failed to load form data'
  }
})

// Get subscription label
const getSubscriptionLabel = (subscription) => {
  const spot = adSpots.value.find(s => s.id === subscription.spot_id)
  const spotName = spot ? spot.name : 'Unknown Spot'
  return `${spotName} - $${spot?.price || '0.00'} (${subscription.status})`
}

// Get payment label
const getPaymentLabel = (payment) => {
  return `$${payment.amount} - ${payment.payment_method} (${payment.status})`
}

// Update details from selected subscription
const updateDetailsFromSubscription = () => {
  if (!form.subscription_id) return

  const subscription = subscriptions.value.find(s => s.id === form.subscription_id)
  if (!subscription) return

  const spot = adSpots.value.find(s => s.id === subscription.spot_id)
  if (spot) {
    form.amount = parseFloat(spot.price || 0)
  }

  // Find the most recent payment for this subscription
  const subscriptionPayments = payments.value.filter(p => p.subscription_id === form.subscription_id)
  if (subscriptionPayments.length > 0) {
    // Sort by created_at in descending order
    subscriptionPayments.sort((a, b) => {
      const dateA = a.created_at?.toDate ? a.created_at.toDate() : new Date(a.created_at)
      const dateB = b.created_at?.toDate ? b.created_at.toDate() : new Date(b.created_at)
      return dateB - dateA
    })

    // Set the payment_id to the most recent payment
    form.payment_id = subscriptionPayments[0].id
  }
}

// Update due date based on issue date
const updateDueDate = () => {
  if (!form.issue_date) return

  const issueDate = new Date(form.issue_date)
  const dueDate = new Date(issueDate)
  dueDate.setDate(dueDate.getDate() + 30)
  form.due_date = dueDate.toISOString().split('T')[0]
}

// Validate form
const validateForm = () => {
  let isValid = true

  // Reset errors
  Object.keys(errors).forEach(key => {
    errors[key] = ''
  })

  // Validate invoice_number
  if (!form.invoice_number) {
    errors.invoice_number = 'Invoice number is required'
    isValid = false
  }

  // Validate subscription_id
  if (!form.subscription_id) {
    errors.subscription_id = 'Subscription is required'
    isValid = false
  }

  // Validate amount
  if (!form.amount || form.amount <= 0) {
    errors.amount = 'Amount must be greater than 0'
    isValid = false
  }

  // Validate issue_date
  if (!form.issue_date) {
    errors.issue_date = 'Issue date is required'
    isValid = false
  }

  // Validate due_date
  if (!form.due_date) {
    errors.due_date = 'Due date is required'
    isValid = false
  } else if (form.issue_date && new Date(form.due_date) < new Date(form.issue_date)) {
    errors.due_date = 'Due date must be after issue date'
    isValid = false
  }

  // Validate status
  if (!form.status) {
    errors.status = 'Status is required'
    isValid = false
  }

  return isValid
}

// Submit form
const submitForm = async () => {
  // Validate form
  if (!validateForm()) {
    return
  }

  isSubmitting.value = true
  formError.value = ''

  try {
    // Convert date strings to Date objects
    const formData = {
      ...form,
      issue_date: new Date(form.issue_date),
      due_date: new Date(form.due_date),
      amount: parseFloat(form.amount.toString())
    }

    if (props.isEdit) {
      // Update existing invoice
      const updatedInvoice = await updateInvoice(props.invoice.id, formData)
      if (updatedInvoice) {
        emit('updated', updatedInvoice)
      } else {
        formError.value = invoiceError.value || 'Failed to update invoice'
      }
    } else {
      // Create new invoice
      const newInvoice = await createInvoice(formData)
      if (newInvoice) {
        emit('created', newInvoice)
      } else {
        formError.value = invoiceError.value || 'Failed to create invoice'
      }
    }
  } catch (err) {
    console.error('Error submitting form:', err)
    formError.value = err.message || 'An unexpected error occurred'
  } finally {
    isSubmitting.value = false
  }
}
</script>
