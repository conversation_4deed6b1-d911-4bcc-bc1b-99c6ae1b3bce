<template>
  <div class="fixed inset-0 bg-gray-900/90 backdrop-blur-sm flex items-center justify-center z-50 p-4">
    <div class="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 rounded-2xl shadow-2xl max-w-4xl w-full mx-4 border border-white/10 overflow-hidden">
      <!-- Header -->
      <div class="px-6 py-4 border-b border-white/10 bg-gradient-to-r from-emerald-600/10 to-teal-600/10">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center mr-3 shadow-lg">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-5 h-5 text-white">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
              </svg>
            </div>
            <div>
              <h3 class="text-xl font-bold text-white">Create New Advert</h3>
              <p class="text-sm text-emerald-400">Set up your advertising campaign in 3 simple steps</p>
            </div>
          </div>
          <button
            @click="$emit('close')"
            class="text-gray-400 hover:text-white transition-colors duration-200 p-2 rounded-lg hover:bg-white/10"
          >
            <Icon name="mdi:close" class="h-6 w-6" />
          </button>
        </div>
      </div>

      <!-- Progress Indicator -->
      <div class="px-6 py-4 bg-gray-800/50">
        <div class="flex items-center justify-between">
          <div v-for="(step, index) in steps" :key="index" class="flex items-center">
            <div class="flex items-center">
              <div 
                class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300"
                :class="currentStep > index ? 'bg-emerald-500 text-white' : currentStep === index ? 'bg-emerald-500/20 text-emerald-400 border-2 border-emerald-500' : 'bg-gray-700 text-gray-400'"
              >
                <Icon v-if="currentStep > index" name="mdi:check" class="w-4 h-4" />
                <span v-else>{{ index + 1 }}</span>
              </div>
              <div class="ml-3">
                <div class="text-sm font-medium" :class="currentStep >= index ? 'text-white' : 'text-gray-400'">
                  {{ step.title }}
                </div>
                <div class="text-xs" :class="currentStep >= index ? 'text-emerald-400' : 'text-gray-500'">
                  {{ step.subtitle }}
                </div>
              </div>
            </div>
            <div v-if="index < steps.length - 1" class="w-16 h-0.5 mx-4" :class="currentStep > index ? 'bg-emerald-500' : 'bg-gray-700'"></div>
          </div>
        </div>
      </div>

      <!-- Form Content -->
      <div class="px-6 py-6 max-h-[70vh] overflow-y-auto">
        <form @submit.prevent="handleNext" class="space-y-6">
          <!-- Step 1: Ad Details -->
          <div v-if="currentStep === 0" class="space-y-6">
            <div class="text-center mb-6">
              <h4 class="text-lg font-semibold text-white mb-2">Ad Spot Details</h4>
              <p class="text-gray-400">Configure your advertising space and pricing</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Name -->
              <div>
                <label for="name" class="block text-sm font-medium text-emerald-400 mb-2">
                  Ad Spot Name <span class="text-red-400">*</span>
                </label>
                <input
                  type="text"
                  id="name"
                  v-model="form.name"
                  class="w-full px-4 py-3 rounded-xl bg-gray-800/50 border border-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-colors duration-200"
                  :class="{ 'border-red-500': errors.name }"
                  placeholder="Homepage Banner"
                  required
                />
                <p v-if="errors.name" class="mt-1 text-sm text-red-400">{{ errors.name }}</p>
              </div>

              <!-- Position -->
              <div>
                <label for="position" class="block text-sm font-medium text-emerald-400 mb-2">
                  Position <span class="text-red-400">*</span>
                </label>
                <select
                  id="position"
                  v-model="form.position"
                  class="w-full px-4 py-3 rounded-xl bg-gray-800/50 border border-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400"
                  :class="{ 'border-red-500': errors.position }"
                  required
                >
                  <option value="">Select Position</option>
                  <option value="top">Top (Header Area)</option>
                  <option value="middle">Middle (Content Area)</option>
                  <option value="bottom">Bottom (Footer Area)</option>
                </select>
                <p v-if="errors.position" class="mt-1 text-sm text-red-400">{{ errors.position }}</p>
              </div>

              <!-- Dimensions -->
              <div>
                <label for="dimensions" class="block text-sm font-medium text-emerald-400 mb-2">
                  Dimensions
                </label>
                <input
                  type="text"
                  id="dimensions"
                  v-model="form.dimensions"
                  class="w-full px-4 py-3 rounded-xl bg-gray-800/50 border border-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400"
                  :class="{ 'border-red-500': errors.dimensions }"
                  placeholder="728x90 (optional)"
                />
                <p v-if="errors.dimensions" class="mt-1 text-sm text-red-400">{{ errors.dimensions }}</p>
              </div>

              <!-- Price -->
              <div>
                <label for="price" class="block text-sm font-medium text-emerald-400 mb-2">
                  Price (daily) <span class="text-red-400">*</span>
                </label>
                <input
                  type="number"
                  id="price"
                  v-model="form.price"
                  min="0"
                  step="0.01"
                  class="w-full px-4 py-3 rounded-xl bg-gray-800/50 border border-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400"
                  :class="{ 'border-red-500': errors.price }"
                  placeholder="99.99"
                  required
                />
                <p v-if="errors.price" class="mt-1 text-sm text-red-400">{{ errors.price }}</p>
              </div>
            </div>

            <!-- Description -->
            <div>
              <label for="description" class="block text-sm font-medium text-emerald-400 mb-2">
                Description
              </label>
              <textarea
                id="description"
                v-model="form.description"
                rows="3"
                class="w-full px-4 py-3 rounded-xl bg-gray-800/50 border border-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400"
                :class="{ 'border-red-500': errors.description }"
                placeholder="Describe the ad placement and visibility..."
              ></textarea>
              <p v-if="errors.description" class="mt-1 text-sm text-red-400">{{ errors.description }}</p>
            </div>
          </div>

          <!-- Step 2: Campaign Details -->
          <div v-if="currentStep === 1" class="space-y-6">
            <div class="text-center mb-6">
              <h4 class="text-lg font-semibold text-white mb-2">Campaign Configuration</h4>
              <p class="text-gray-400">Set up advertiser details and campaign duration</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Advertiser Selection -->
              <div class="md:col-span-2">
                <label for="advertiser" class="block text-sm font-medium text-emerald-400 mb-2">
                  Advertiser <span class="text-red-400">*</span>
                </label>
                <select
                  id="advertiser"
                  v-model="form.advertiser_id"
                  class="w-full px-4 py-3 rounded-xl bg-gray-800/50 border border-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400"
                  :class="{ 'border-red-500': errors.advertiser_id }"
                  :disabled="isLoading"
                  required
                >
                  <option value="">
                    {{ isLoading ? 'Loading advertisers...' : 
                       users.length === 0 ? 'No users loaded' :
                       advertisers.length === 0 ? `Found ${users.length} users, 0 advertisers - showing all` : 
                       'Select an advertiser' }}
                  </option>
                  <!-- Show advertisers if found, otherwise show all users for debugging -->
                  <option v-for="user in (advertisers.length > 0 ? advertisers : users)" :key="user.id" :value="user.id">
                    {{ user.name || user.displayName || user.email || user.id }}
                    {{ advertisers.length === 0 ? ` (role: ${user.role || user.type || 'none'})` : '' }}
                  </option>
                </select>
                <p v-if="errors.advertiser_id" class="mt-1 text-sm text-red-400">{{ errors.advertiser_id }}</p>
                <p v-if="form.advertiser_id && form.advertiser_name" class="mt-1 text-sm text-gray-400">
                  ID: {{ form.advertiser_id }}
                </p>
                <!-- Debug info -->
                <div class="mt-2 text-xs text-gray-500">
                  <div>Total users loaded: {{ users.length }}</div>
                  <div>Advertisers found: {{ advertisers.length }}</div>
                  <div v-if="isLoading" class="text-yellow-400">Loading users...</div>
                  <div v-if="!isLoading && advertisers.length === 0 && users.length > 0" class="text-orange-400">
                    No advertisers found in {{ users.length }} users
                  </div>
                </div>
              </div>

              <!-- Start Date -->
              <div>
                <label for="start_date" class="block text-sm font-medium text-emerald-400 mb-2">
                  Start Date <span class="text-red-400">*</span>
                </label>
                <input
                  type="date"
                  id="start_date"
                  v-model="form.start_date"
                  class="w-full px-4 py-3 rounded-xl bg-gray-800/50 border border-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400"
                  :class="{ 'border-red-500': errors.start_date }"
                  :min="today"
                  required
                />
                <p v-if="errors.start_date" class="mt-1 text-sm text-red-400">{{ errors.start_date }}</p>
              </div>

              <!-- End Date -->
              <div>
                <label for="end_date" class="block text-sm font-medium text-emerald-400 mb-2">
                  End Date <span class="text-red-400">*</span>
                </label>
                <input
                  type="date"
                  id="end_date"
                  v-model="form.end_date"
                  class="w-full px-4 py-3 rounded-xl bg-gray-800/50 border border-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400"
                  :class="{ 'border-red-500': errors.end_date }"
                  :min="form.start_date || today"
                  required
                />
                <p v-if="errors.end_date" class="mt-1 text-sm text-red-400">{{ errors.end_date }}</p>
              </div>

              <!-- Target URL -->
              <div>
                <label for="target_url" class="block text-sm font-medium text-emerald-400 mb-2">
                  Click-through URL (Optional)
                </label>
                <input
                  type="url"
                  id="target_url"
                  v-model="form.target_url"
                  class="w-full px-4 py-3 rounded-xl bg-gray-800/50 border border-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400"
                  :class="{ 'border-red-500': errors.target_url }"
                  placeholder="https://example.com/landing-page"
                />
                <p v-if="errors.target_url" class="mt-1 text-sm text-red-400">{{ errors.target_url }}</p>
                <p class="mt-1 text-xs text-gray-500">
                  When users click on the ad, they'll be taken to this URL. Leave empty for display-only ads.
                </p>
              </div>
            </div>

            <!-- Ad Creative Upload -->
            <div class="mt-6">
              <label class="block text-sm font-medium text-emerald-400 mb-2">
                Ad Creative <span class="text-red-400">*</span>
              </label>
              <div 
                class="border-2 border-dashed border-gray-700 rounded-xl p-6 text-center hover:border-emerald-500/50 transition-all duration-200 cursor-pointer bg-gray-800/30"
                @click="triggerFileInput"
                @drop.prevent="handleFileDrop"
                @dragover.prevent
                @dragenter.prevent
              >
                <input
                  ref="fileInput"
                  type="file"
                  class="hidden"
                  accept="image/*"
                  @change="handleFileSelect"
                />
                <div v-if="!form.ad_creative_file && !form.ad_creative_url">
                  <Icon name="mdi:cloud-upload-outline" class="h-12 w-12 text-gray-500 mx-auto mb-3" />
                  <p class="text-gray-300 font-medium">
                    Drop your ad image here or click to browse
                  </p>
                  <p class="text-gray-500 text-sm mt-1">
                    PNG, JPG, GIF up to 10MB
                  </p>
                </div>
                <div v-else class="flex items-center justify-center">
                  <div class="relative">
                    <img 
                      v-if="previewUrl" 
                      :src="previewUrl" 
                      alt="Ad preview" 
                      class="max-h-32 rounded-lg shadow-lg"
                    />
                    <button
                      @click.stop="removeFile"
                      type="button"
                      class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                    >
                      <Icon name="mdi:close" class="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
              <p v-if="errors.ad_creative" class="mt-1 text-sm text-red-400">{{ errors.ad_creative }}</p>
            </div>

            <!-- Campaign Cost Summary -->
            <div class="bg-gradient-to-br from-emerald-500/10 to-teal-500/10 rounded-xl p-6 border border-emerald-500/20">
              <h5 class="text-lg font-semibold text-white mb-4 flex items-center">
                <Icon name="mdi:calculator" class="w-5 h-5 mr-2 text-emerald-400" />
                Campaign Cost Summary
              </h5>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                <div>
                  <div class="text-2xl font-bold text-emerald-400">${{ form.price || '0.00' }}</div>
                  <div class="text-sm text-gray-400">Daily Rate</div>
                </div>
                <div>
                  <div class="text-2xl font-bold text-teal-400">{{ campaignDays }}</div>
                  <div class="text-sm text-gray-400">Campaign Days</div>
                </div>
                <div>
                  <div class="text-2xl font-bold text-white">${{ totalCost }}</div>
                  <div class="text-sm text-gray-400">Total Cost</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Step 3: Payment & Activation -->
          <div v-if="currentStep === 2" class="space-y-6">
            <div class="text-center mb-6">
              <h4 class="text-lg font-semibold text-white mb-2">Payment & Activation</h4>
              <p class="text-gray-400">Review your order and complete the setup</p>
            </div>

            <!-- Order Summary -->
            <div class="bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-xl p-6 border border-white/10">
              <h5 class="text-lg font-semibold text-white mb-4">Order Summary</h5>
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span class="text-gray-400">Ad Spot:</span>
                  <span class="text-white font-medium">{{ form.name }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-400">Position:</span>
                  <span class="text-white">{{ positionLabel }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-400">Dimensions:</span>
                  <span class="text-white">{{ form.dimensions }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-400">Advertiser:</span>
                  <span class="text-white">{{ form.advertiser_name }} ({{ form.advertiser_id }})</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-400">Campaign Period:</span>
                  <span class="text-white">{{ formatDate(form.start_date) }} - {{ formatDate(form.end_date) }}</span>
                </div>
                <div class="border-t border-gray-700 pt-3 mt-3">
                  <div class="flex justify-between text-lg font-semibold">
                    <span class="text-emerald-400">Total Cost:</span>
                    <span class="text-white">${{ totalCost }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Terms and Conditions -->
            <div class="space-y-4">
              <div class="flex items-start">
                <input
                  type="checkbox"
                  id="terms_accepted"
                  v-model="form.terms_accepted"
                  class="mt-1 h-4 w-4 text-emerald-500 focus:ring-emerald-400 border-gray-600 rounded bg-gray-800"
                  required
                />
                <label for="terms_accepted" class="ml-3 text-sm text-gray-300">
                  I agree to the <a href="#" class="text-emerald-400 hover:text-emerald-300">Terms of Service</a> and 
                  <a href="#" class="text-emerald-400 hover:text-emerald-300">Advertising Guidelines</a>
                  <span class="text-red-400">*</span>
                </label>
              </div>

              <div class="flex items-start">
                <input
                  type="checkbox"
                  id="auto_activate"
                  v-model="form.auto_activate"
                  class="mt-1 h-4 w-4 text-emerald-500 focus:ring-emerald-400 border-gray-600 rounded bg-gray-800"
                />
                <label for="auto_activate" class="ml-3 text-sm text-gray-300">
                  Automatically activate campaign on start date
                </label>
              </div>
            </div>
          </div>

          <!-- Error Display -->
          <div v-if="formError" class="p-4 bg-red-500/10 border border-red-500/20 rounded-xl">
            <p class="text-sm text-red-400 flex items-center">
              <Icon name="mdi:alert-circle" class="w-4 h-4 mr-2" />
              {{ formError }}
            </p>
          </div>
        </form>
      </div>

      <!-- Footer Actions -->
      <div class="px-6 py-4 border-t border-white/10 bg-gray-800/50">
        <div class="flex items-center justify-between">
          <button
            v-if="currentStep > 0"
            @click="previousStep"
            type="button"
            class="px-6 py-2 border border-gray-600 rounded-lg text-gray-300 hover:text-white hover:border-gray-500 transition-colors duration-200 flex items-center"
          >
            <Icon name="mdi:arrow-left" class="w-4 h-4 mr-2" />
            Previous
          </button>
          <div v-else></div>

          <div class="flex items-center space-x-3">
            <button
              @click="$emit('close')"
              type="button"
              class="px-6 py-2 border border-gray-600 rounded-lg text-gray-300 hover:text-white hover:border-gray-500 transition-colors duration-200"
            >
              Cancel
            </button>

            <button
              v-if="currentStep < steps.length - 1"
              @click="nextStep"
              type="button"
              class="px-6 py-2 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-[1.02] shadow-lg flex items-center"
              :disabled="!canProceed"
            >
              Next
              <Icon name="mdi:arrow-right" class="w-4 h-4 ml-2" />
            </button>

            <button
              v-else
              @click="submitForm"
              type="button"
              class="px-6 py-2 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-[1.02] shadow-lg flex items-center"
              :disabled="isSubmitting || !form.terms_accepted"
            >
              <Icon v-if="isSubmitting" name="mdi:loading" class="animate-spin w-4 h-4 mr-2" />
              <Icon v-else name="mdi:check" class="w-4 h-4 mr-2" />
              {{ isSubmitting ? 'Creating...' : 'Create Advert' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive, ref, onMounted, watch } from 'vue';
import { useAds } from '~/composables/useAds';
import { useUserManagement } from '~/composables/user-management';
import { useFirebase } from '~/composables/useFirebase';
import type { IUnifiedAdCreate } from '~/types/ads';

const emit = defineEmits(['close', 'created']);

// Initialize composables
const { createAd, error: adsError } = useAds();
const { users, fetchUsers, isLoading } = useUserManagement();

// Filter users to get advertisers only
const advertisers = computed(() => {
  console.log('Computing advertisers from users:', users.value);
  
  const filtered = users.value.filter(user => {
    // Check multiple possible role formats
    const hasAdvertiserRole = 
      (user.roles && Array.isArray(user.roles) && user.roles.includes('Advertiser')) ||
      (user.roles && Array.isArray(user.roles) && user.roles.includes('advertiser')) ||
      (user.role === 'advertiser') ||
      (user.role === 'Advertiser') ||
      (user.type === 'advertiser') ||
      (user.type === 'Advertiser') ||
      (user.userType === 'advertiser') ||
      (user.userType === 'Advertiser');
    
    console.log(`User ${user.name || user.email}:`, {
      roles: user.roles,
      role: user.role,
      type: user.type,
      userType: user.userType,
      hasAdvertiserRole
    });
    
    return hasAdvertiserRole;
  });
  
  console.log('Filtered advertisers:', filtered);
  return filtered;
});

// Load advertisers on mount with error handling
onMounted(async () => {
  console.log('UnifiedAdvertForm: Loading users...');
  try {
    await fetchUsers();
    console.log('UnifiedAdvertForm: Users loaded successfully:', users.value.length);
  } catch (error) {
    console.error('UnifiedAdvertForm: Error loading users:', error);
  }
});

// Steps configuration
const steps = [
  { title: 'Ad Details', subtitle: 'Configure spot & pricing' },
  { title: 'Campaign', subtitle: 'Set advertiser & dates' },
  { title: 'Payment', subtitle: 'Review & activate' }
];

// Current step
const currentStep = ref(0);

// Form state
const form = reactive({
  // Ad Details (Step 1)
  name: '',
  description: '',
  position: '',
  price: 0,
  dimensions: '',
  
  // Campaign Details (Step 2)
  advertiser_id: '',
  advertiser_name: '',
  start_date: '',
  end_date: '',
  ad_creative_url: '',
  ad_creative_file: null as File | null,
  target_url: '',
  
  // Payment & Activation (Step 3)
  terms_accepted: false,
  auto_activate: true,
  
  // Default values
  status: 'pending',
  payment_status: 'pending'
});

// Form validation errors
const errors = reactive({
  name: '',
  description: '',
  position: '',
  price: '',
  dimensions: '',
  advertiser_id: '',
  advertiser_name: '',
  start_date: '',
  end_date: '',
  ad_creative: '',
  target_url: ''
});

// Form submission state
const isSubmitting = ref(false);
const formError = ref('');

// File upload refs
const fileInput = ref<HTMLInputElement>();
const previewUrl = ref('');

// Watch for advertiser selection to auto-populate name
watch(() => form.advertiser_id, (newId) => {
  console.log('🔄 Advertiser ID changed to:', newId);
  
  if (newId) {
    // Look in the users array (which contains all users, including advertisers)
    const selectedUser = users.value.find(u => u.id === newId);
    console.log('👤 Selected user:', selectedUser);
    
    if (selectedUser) {
      form.advertiser_name = selectedUser.name || selectedUser.email || 'Unknown User';
      console.log('✅ Auto-populated advertiser_name:', form.advertiser_name);
    } else {
      console.log('⚠️ User not found with ID:', newId);
    }
  } else {
    form.advertiser_name = '';
    console.log('🧹 Cleared advertiser_name');
  }
});

// Today's date for date inputs
const today = computed(() => {
  return new Date().toISOString().split('T')[0];
});

// Position label for display
const positionLabel = computed(() => {
  const positions = {
    'top': 'Top (Header Area)',
    'middle': 'Middle (Content Area)',
    'bottom': 'Bottom (Footer Area)'
  };
  return positions[form.position] || form.position;
});

// Campaign duration calculation
const campaignDays = computed(() => {
  if (!form.start_date || !form.end_date) return 0;
  
  const start = new Date(form.start_date);
  const end = new Date(form.end_date);
  const diffTime = Math.abs(end.getTime() - start.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end days
});

// Total cost calculation
const totalCost = computed(() => {
  const price = parseFloat(form.price) || 0;
  return (price * campaignDays.value).toFixed(2);
});

// Check if can proceed to next step
const canProceed = computed(() => {
  if (currentStep.value === 0) {
    return form.name && form.position && form.price > 0;
  } else if (currentStep.value === 1) {
    return form.advertiser_id && form.start_date && form.end_date && (form.ad_creative_file || form.ad_creative_url);
  }
  return true;
});

// Format date for display
const formatDate = (dateString: string) => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

// Step navigation
const nextStep = () => {
  if (validateCurrentStep() && currentStep.value < steps.length - 1) {
    currentStep.value++;
  }
};

const previousStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--;
    formError.value = '';
  }
};

// Validate current step
const validateCurrentStep = () => {
  // Reset errors
  Object.keys(errors).forEach(key => {
    errors[key] = '';
  });

  let isValid = true;

  if (currentStep.value === 0) {
    // Validate Step 1: Ad Details
    if (!form.name.trim()) {
      errors.name = 'Ad spot name is required';
      isValid = false;
    }

    if (!form.position) {
      errors.position = 'Position is required';
      isValid = false;
    }


    if (!form.price || form.price <= 0) {
      errors.price = 'Price must be greater than 0';
      isValid = false;
    }
  } else if (currentStep.value === 1) {
    // Validate Step 2: Campaign Details
    if (!form.advertiser_id.trim()) {
      errors.advertiser_id = 'Please select an advertiser';
      isValid = false;
    } else {
      // Verify the selected user actually exists in our users array
      const selectedUser = users.value.find(u => u.id === form.advertiser_id);
      if (!selectedUser) {
        errors.advertiser_id = 'Selected advertiser is no longer available';
        isValid = false;
      } else if (!form.advertiser_name) {
        errors.advertiser_id = 'Advertiser name could not be determined';
        isValid = false;
      }
    }

    if (!form.start_date) {
      errors.start_date = 'Start date is required';
      isValid = false;
    }

    if (!form.end_date) {
      errors.end_date = 'End date is required';
      isValid = false;
    }

    if (form.start_date && form.end_date && new Date(form.end_date) <= new Date(form.start_date)) {
      errors.end_date = 'End date must be after start date';
      isValid = false;
    }
    
    if (!form.ad_creative_file && !form.ad_creative_url) {
      errors.ad_creative = 'Please upload an ad creative image';
      isValid = false;
    }

    // Validate target URL if provided
    if (form.target_url && form.target_url.trim()) {
      try {
        new URL(form.target_url);
      } catch {
        errors.target_url = 'Please enter a valid URL (e.g., https://example.com)';
        isValid = false;
      }
    }
  }

  return isValid;
};

// Submit form
const submitForm = async () => {
  if (!validateCurrentStep() || !form.terms_accepted) {
    return;
  }

  isSubmitting.value = true;
  formError.value = '';

  try {
    // Verify the selected user exists
    const selectedUser = users.value.find(u => u.id === form.advertiser_id);
    if (!selectedUser) {
      formError.value = 'Selected advertiser is no longer available. Please refresh and try again.';
      return;
    }
    
    console.log('🎯 Creating ad with selected user:', selectedUser);
    console.log('📋 Form data:', {
      advertiser_id: form.advertiser_id,
      advertiser_name: form.advertiser_name,
      selectedUser: {
        id: selectedUser.id,
        name: selectedUser.name,
        email: selectedUser.email
      }
    });
    
    // Upload image if provided
    let imageUrl = form.ad_creative_url;
    if (form.ad_creative_file) {
      try {
        // Get Firebase storage
        const { storage } = useFirebase();
        if (!storage) {
          throw new Error('Firebase storage is not available');
        }

        // Import Firebase storage functions
        const { ref, uploadBytesResumable, getDownloadURL } = await import('firebase/storage');

        // Create a storage reference for ad creatives
        const timestamp = Date.now();
        const fileName = `${timestamp}_${form.ad_creative_file.name}`;
        const storageRef = ref(storage, `ad-creatives/${fileName}`);

        console.log('📁 Uploading ad creative to Firebase Storage:', fileName);

        // Upload the image
        const uploadTask = uploadBytesResumable(storageRef, form.ad_creative_file);

        // Wait for the upload to complete
        imageUrl = await new Promise((resolve, reject) => {
          uploadTask.on(
            'state_changed',
            snapshot => {
              // Track upload progress
              const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
              console.log(`📊 Upload progress: ${progress.toFixed(1)}%`);
            },
            error => {
              console.error('❌ Upload error:', error);
              reject(error);
            },
            async () => {
              // Upload completed successfully
              try {
                const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
                console.log('✅ Ad creative uploaded successfully:', downloadURL);
                resolve(downloadURL);
              } catch (error) {
                console.error('❌ Error getting download URL:', error);
                reject(error);
              }
            }
          );
        });
      } catch (error) {
        console.error('❌ Error uploading ad creative:', error);
        formError.value = `Failed to upload image: ${error.message}`;
        return;
      }
    }
    
    // Prepare unified ad data
    const adData: IUnifiedAdCreate = {
      // Ad spot fields
      name: form.name,
      description: form.description,
      position: form.position as 'top' | 'middle' | 'bottom',
      price: parseFloat(form.price.toString()),
      dimensions: form.dimensions,
      
      // Campaign fields
      advertiser_id: form.advertiser_id,
      advertiser_name: form.advertiser_name,
      start_date: form.start_date,
      end_date: form.end_date,
      
      // Ad creative
      image_url: imageUrl,
      target_url: form.target_url || undefined,
      
      // Status fields
      status: form.auto_activate ? 'pending' : 'inactive',
      payment_status: 'pending'
    };

    // Create the unified ad
    const createdAd = await createAd(adData);
    
    if (createdAd) {
      emit('created', createdAd);
    } else {
      formError.value = adsError.value || 'Failed to create advert. Please try again.';
    }
  } catch (err: any) {
    console.error('Error creating advert:', err);
    formError.value = err.message || 'An unexpected error occurred';
  } finally {
    isSubmitting.value = false;
  }
};

// File handling functions
const triggerFileInput = () => {
  fileInput.value?.click();
};

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files[0]) {
    processFile(target.files[0]);
  }
};

const handleFileDrop = (event: DragEvent) => {
  if (event.dataTransfer?.files && event.dataTransfer.files[0]) {
    processFile(event.dataTransfer.files[0]);
  }
};

const processFile = (file: File) => {
  // Validate file type
  if (!file.type.startsWith('image/')) {
    errors.ad_creative = 'Please select a valid image file';
    return;
  }
  
  // Validate file size (10MB)
  if (file.size > 10 * 1024 * 1024) {
    errors.ad_creative = 'File size must be less than 10MB';
    return;
  }
  
  // Clear error
  errors.ad_creative = '';
  
  // Store file
  form.ad_creative_file = file;
  
  // Create preview
  const reader = new FileReader();
  reader.onload = (e) => {
    previewUrl.value = e.target?.result as string;
  };
  reader.readAsDataURL(file);
};

const removeFile = () => {
  form.ad_creative_file = null;
  form.ad_creative_url = '';
  previewUrl.value = '';
  errors.ad_creative = '';
  if (fileInput.value) {
    fileInput.value.value = '';
  }
};
</script>