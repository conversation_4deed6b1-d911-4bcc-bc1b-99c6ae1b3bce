<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full mx-4">
    <div class="p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
          {{ isEdit ? 'Edit Payment' : 'Create New Payment' }}
        </h3>
        <button
          @click="$emit('close')"
          class="text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400"
        >
          <Icon name="mdi:close" class="h-5 w-5" />
        </button>
      </div>

      <form @submit.prevent="submitForm" class="space-y-6">
        <!-- Subscription Selection -->
        <div>
          <label
            for="subscription_id"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            Subscription <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            <select
              id="subscription_id"
              v-model="form.subscription_id"
              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
              :class="{ 'border-red-500 dark:border-red-500': errors.subscription_id }"
              required
              @change="updateAmountFromSubscription"
            >
              <option value="" disabled>Select a subscription</option>
              <option
                v-for="subscription in filteredSubscriptions"
                :key="subscription.id"
                :value="subscription.id"
              >
                {{ getSubscriptionLabel(subscription) }}
              </option>
            </select>
          </div>
          <p v-if="errors.subscription_id" class="mt-1 text-sm text-red-600 dark:text-red-400">
            {{ errors.subscription_id }}
          </p>
        </div>

        <!-- Amount -->
        <div>
          <label
            for="amount"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            Amount ($) <span class="text-red-500">*</span>
          </label>
          <input
            type="number"
            id="amount"
            v-model="form.amount"
            min="0"
            step="0.01"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
            :class="{ 'border-red-500 dark:border-red-500': errors.amount }"
            placeholder="99.99"
            required
          />
          <p v-if="errors.amount" class="mt-1 text-sm text-red-600 dark:text-red-400">
            {{ errors.amount }}
          </p>
        </div>

        <!-- Payment Method -->
        <div>
          <label
            for="payment_method"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            Payment Method <span class="text-red-500">*</span>
          </label>
          <select
            id="payment_method"
            v-model="form.payment_method"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
            :class="{ 'border-red-500 dark:border-red-500': errors.payment_method }"
            required
          >
            <option value="" disabled>Select a payment method</option>
            <option value="credit_card">Credit Card</option>
            <option value="paypal">PayPal</option>
            <option value="bank_transfer">Bank Transfer</option>
            <option value="crypto">Cryptocurrency</option>
            <option value="other">Other</option>
          </select>
          <p v-if="errors.payment_method" class="mt-1 text-sm text-red-600 dark:text-red-400">
            {{ errors.payment_method }}
          </p>
        </div>

        <!-- Transaction ID -->
        <div>
          <label
            for="transaction_id"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            Transaction ID
          </label>
          <input
            type="text"
            id="transaction_id"
            v-model="form.transaction_id"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
            :class="{ 'border-red-500 dark:border-red-500': errors.transaction_id }"
            placeholder="txn_123456789"
          />
          <p v-if="errors.transaction_id" class="mt-1 text-sm text-red-600 dark:text-red-400">
            {{ errors.transaction_id }}
          </p>
        </div>

        <!-- Status -->
        <div>
          <label
            for="status"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            Status <span class="text-red-500">*</span>
          </label>
          <select
            id="status"
            v-model="form.status"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
            :class="{ 'border-red-500 dark:border-red-500': errors.status }"
            required
          >
            <option value="pending">Pending</option>
            <option value="successful">Successful</option>
            <option value="failed">Failed</option>
            <option value="refunded">Refunded</option>
          </select>
          <p v-if="errors.status" class="mt-1 text-sm text-red-600 dark:text-red-400">
            {{ errors.status }}
          </p>
        </div>

        <!-- Notes -->
        <div>
          <label
            for="notes"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            Notes
          </label>
          <textarea
            id="notes"
            v-model="form.notes"
            rows="3"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
            :class="{ 'border-red-500 dark:border-red-500': errors.notes }"
            placeholder="Additional notes about this payment"
          ></textarea>
          <p v-if="errors.notes" class="mt-1 text-sm text-red-600 dark:text-red-400">
            {{ errors.notes }}
          </p>
        </div>

        <!-- Error message -->
        <div
          v-if="formError"
          class="p-4 bg-red-50 dark:bg-red-900/30 border border-red-100 dark:border-red-800 rounded-md"
        >
          <p class="text-sm text-red-600 dark:text-red-400">{{ formError }}</p>
        </div>

        <!-- Form actions -->
        <div class="flex items-center justify-end space-x-3 pt-4">
          <button
            type="button"
            @click="$emit('close')"
            class="px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0072ff] dark:focus:ring-offset-gray-800"
          >
            Cancel
          </button>
          <button
            type="submit"
            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#0072ff] hover:bg-[#0054bb] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0072ff] dark:focus:ring-offset-gray-800"
            :disabled="isSubmitting"
          >
            <span v-if="isSubmitting" class="flex items-center">
              <Icon name="mdi:loading" class="animate-spin mr-2 h-4 w-4" />
              {{ isEdit ? 'Updating...' : 'Creating...' }}
            </span>
            <span v-else>
              {{ isEdit ? 'Update Payment' : 'Create Payment' }}
            </span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue';
import { useAdPayments } from '~/composables/useAdPayments';
import { useAds } from '~/composables/useAds';

const props = defineProps({
  payment: {
    type: Object,
    default: null,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['close', 'created', 'updated']);

// Initialize composables
const { createPayment, updatePayment, error: paymentError } = useAdPayments();
const { ads, fetchAds } = useAds();

// Map unified ads to subscriptions for compatibility
const subscriptions = computed(() => ads.value);

// Get unique ad spots from unified ads
const adSpots = computed(() => {
  const spotMap = new Map();
  ads.value.forEach(ad => {
    const spotId = ad.legacy_spot_id || ad.id;
    if (!spotMap.has(spotId)) {
      spotMap.set(spotId, {
        id: spotId,
        name: ad.name,
        description: ad.description,
        position: ad.position,
        price: ad.price,
        dimensions: ad.dimensions,
        status: ad.status
      });
    }
  });
  return Array.from(spotMap.values());
});

// State
const isSubmitting = ref(false);
const formError = ref('');

// Form state
const form = reactive({
  subscription_id: '',
  amount: 0,
  payment_method: '',
  transaction_id: '',
  status: 'pending',
  notes: '',
});

// Form validation
const errors = reactive({
  subscription_id: '',
  amount: '',
  payment_method: '',
  transaction_id: '',
  status: '',
  notes: '',
});

// Computed properties
const filteredSubscriptions = computed(() => {
  // For new payments, only show pending subscriptions
  // For editing, show all subscriptions
  if (!props.isEdit) {
    return subscriptions.value.filter(sub => sub.status === 'pending');
  }
  return subscriptions.value;
});

// Initialize form with payment data if editing
onMounted(async () => {
  try {
    // Fetch subscriptions and ad spots
    await fetchAds();

    if (props.isEdit && props.payment) {
      form.subscription_id = props.payment.subscription_id || '';
      form.amount = props.payment.amount || 0;
      form.payment_method = props.payment.payment_method || '';
      form.transaction_id = props.payment.transaction_id || '';
      form.status = props.payment.status || 'pending';
      form.notes = props.payment.notes || '';
    }
  } catch (err) {
    console.error('Error initializing form:', err);
    formError.value = 'Failed to load form data';
  }
});

// Get subscription label
const getSubscriptionLabel = subscription => {
  const spot = adSpots.value.find(s => s.id === subscription.spot_id);
  const spotName = spot ? spot.name : 'Unknown Spot';
  return `${spotName} - $${spot?.price || '0.00'} (${subscription.status})`;
};

// Update amount from selected subscription
const updateAmountFromSubscription = () => {
  if (!form.subscription_id) return;

  const subscription = subscriptions.value.find(s => s.id === form.subscription_id);
  if (!subscription) return;

  const spot = adSpots.value.find(s => s.id === subscription.spot_id);
  if (spot) {
    form.amount = parseFloat(spot.price || 0);
  }
};

// Validate form
const validateForm = () => {
  let isValid = true;

  // Reset errors
  Object.keys(errors).forEach(key => {
    errors[key] = '';
  });

  // Validate subscription_id
  if (!form.subscription_id) {
    errors.subscription_id = 'Subscription is required';
    isValid = false;
  }

  // Validate amount
  if (!form.amount || form.amount <= 0) {
    errors.amount = 'Amount must be greater than 0';
    isValid = false;
  }

  // Validate payment_method
  if (!form.payment_method) {
    errors.payment_method = 'Payment method is required';
    isValid = false;
  }

  // Validate status
  if (!form.status) {
    errors.status = 'Status is required';
    isValid = false;
  }

  return isValid;
};

// Submit form
const submitForm = async () => {
  // Validate form
  if (!validateForm()) {
    return;
  }

  isSubmitting.value = true;
  formError.value = '';

  try {
    // Convert numeric fields
    const formData = {
      ...form,
      amount: parseFloat(form.amount.toString()),
    };

    if (props.isEdit) {
      // Update existing payment
      const updatedPayment = await updatePayment(props.payment.id, formData);
      if (updatedPayment) {
        emit('updated', updatedPayment);
      } else {
        formError.value = paymentError.value || 'Failed to update payment';
      }
    } else {
      // Create new payment
      const newPayment = await createPayment(formData);
      if (newPayment) {
        emit('created', newPayment);
      } else {
        formError.value = paymentError.value || 'Failed to create payment';
      }
    }
  } catch (err) {
    console.error('Error submitting form:', err);
    formError.value = err.message || 'An unexpected error occurred';
  } finally {
    isSubmitting.value = false;
  }
};
</script>
