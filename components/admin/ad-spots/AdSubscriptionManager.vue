<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full mx-4">
    <div class="p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
          {{ isEdit ? 'Edit Subscription' : 'Create New Subscription' }}
        </h3>
        <button
          @click="$emit('close')"
          class="text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400"
        >
          <Icon name="mdi:close" class="h-5 w-5" />
        </button>
      </div>

      <form @submit.prevent="submitForm" class="space-y-6">
        <!-- Advertiser Selection -->
        <div>
          <label
            for="advertiser_id"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            Advertiser <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            <select
              id="advertiser_id"
              v-model="form.advertiser_id"
              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
              :class="{ 'border-red-500 dark:border-red-500': errors.advertiser_id }"
              required
            >
              <option value="" disabled>Select an advertiser</option>
              <option v-for="user in users" :key="user.id" :value="user.id">
                {{ user.name || user.displayName || user.email || user.id }}
              </option>
            </select>
          </div>
          <p v-if="errors.advertiser_id" class="mt-1 text-sm text-red-600 dark:text-red-400">
            {{ errors.advertiser_id }}
          </p>
        </div>

        <!-- Ad Spot Selection -->
        <div>
          <label
            for="spot_id"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            Ad Spot <span class="text-red-500">*</span>
          </label>
          <select
            id="spot_id"
            v-model="form.spot_id"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
            :class="{ 'border-red-500 dark:border-red-500': errors.spot_id }"
            required
            @change="updateDurationFromSpot"
          >
            <option value="" disabled>Select an ad spot</option>
            <option v-for="spot in availableSpots" :key="spot.id" :value="spot.id">
              {{ spot.name }} ({{ spot.position || spot.location }}) - ${{ spot.price }}
            </option>
          </select>
          <p v-if="errors.spot_id" class="mt-1 text-sm text-red-600 dark:text-red-400">
            {{ errors.spot_id }}
          </p>
        </div>

        <!-- Content Selection -->
        <div>
          <label
            for="content_id"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            Ad Content <span class="text-red-500">*</span>
          </label>
          <select
            id="content_id"
            v-model="form.content_id"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
            :class="{ 'border-red-500 dark:border-red-500': errors.content_id }"
            required
            :disabled="!form.spot_id"
          >
            <option value="" disabled>
              {{ !form.spot_id ? 'Select an ad spot first' : 'Select content to display' }}
            </option>
            <option v-for="content in adSpotContent" :key="content.id" :value="content.id">
              {{ content.title || content.name || 'Untitled' }} ({{
                content.contentType || content.type || 'content'
              }})
            </option>
          </select>
          <p v-if="errors.content_id" class="mt-1 text-sm text-red-600 dark:text-red-400">
            {{ errors.content_id }}
          </p>
          <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
            This is the content that will be displayed in the ad spot
          </p>
        </div>

        <!-- Start Date -->
        <div>
          <label
            for="start_date"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            Start Date <span class="text-red-500">*</span>
          </label>
          <input
            type="date"
            id="start_date"
            v-model="form.start_date"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
            :class="{ 'border-red-500 dark:border-red-500': errors.start_date }"
            :min="minStartDate"
            required
            @change="updateEndDate"
          />
          <p v-if="errors.start_date" class="mt-1 text-sm text-red-600 dark:text-red-400">
            {{ errors.start_date }}
          </p>
        </div>

        <!-- End Date -->
        <div>
          <label
            for="end_date"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            End Date <span class="text-red-500">*</span>
          </label>
          <input
            type="date"
            id="end_date"
            v-model="form.end_date"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
            :class="{ 'border-red-500 dark:border-red-500': errors.end_date }"
            :min="minEndDate"
            required
          />
          <p v-if="errors.end_date" class="mt-1 text-sm text-red-600 dark:text-red-400">
            {{ errors.end_date }}
          </p>
        </div>

        <!-- Status -->
        <div>
          <label
            for="status"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            Status <span class="text-red-500">*</span>
          </label>
          <select
            id="status"
            v-model="form.status"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
            :class="{ 'border-red-500 dark:border-red-500': errors.status }"
            required
          >
            <option value="pending">Pending</option>
            <option value="active">Active</option>
            <option value="cancelled">Cancelled</option>
            <option value="expired">Expired</option>
          </select>
          <p v-if="errors.status" class="mt-1 text-sm text-red-600 dark:text-red-400">
            {{ errors.status }}
          </p>
        </div>

        <!-- Error message -->
        <div
          v-if="formError"
          class="p-4 bg-red-50 dark:bg-red-900/30 border border-red-100 dark:border-red-800 rounded-md"
        >
          <p class="text-sm text-red-600 dark:text-red-400">{{ formError }}</p>
        </div>

        <!-- Form actions -->
        <div class="flex items-center justify-end space-x-3 pt-4">
          <button
            type="button"
            @click="$emit('close')"
            class="px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0072ff] dark:focus:ring-offset-gray-800"
          >
            Cancel
          </button>
          <button
            type="submit"
            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#0072ff] hover:bg-[#0054bb] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0072ff] dark:focus:ring-offset-gray-800"
            :disabled="isSubmitting"
          >
            <span v-if="isSubmitting" class="flex items-center">
              <Icon name="mdi:loading" class="animate-spin mr-2 h-4 w-4" />
              {{ isEdit ? 'Updating...' : 'Creating...' }}
            </span>
            <span v-else>
              {{ isEdit ? 'Update Subscription' : 'Create Subscription' }}
            </span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { collection, getDocs } from 'firebase/firestore';
import { computed, onMounted, reactive, ref, watch } from 'vue';
import { useAdContent } from '~/composables/useAdContent';
import { useAds } from '~/composables/useAds';

interface User {
  id: string;
  email?: string;
  displayName?: string;
}

const props = defineProps({
  subscription: {
    type: Object,
    default: null,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['close', 'created', 'updated']);

// Initialize composables
const { ads, createAd, updateAd, fetchAds, error: subscriptionError } = useAds();
const { fetchAdSpotContent } = useAdContent();

// Get unique ad spots from unified ads
const adSpots = computed(() => {
  const spotMap = new Map();
  ads.value.forEach(ad => {
    const spotId = ad.legacy_spot_id || ad.id;
    if (!spotMap.has(spotId)) {
      spotMap.set(spotId, {
        id: spotId,
        name: ad.name,
        description: ad.description,
        position: ad.position,
        price: ad.price,
        dimensions: ad.dimensions,
        status: ad.status
      });
    }
  });
  return Array.from(spotMap.values());
});
const { firestore } = useFirebase();

// State
const users = ref<User[]>([]);
const adSpotContent = ref<any[]>([]);
const isSubmitting = ref(false);
const formError = ref('');

// Form state
const form = reactive({
  advertiser_id: '',
  advertiser_name: '',
  spot_id: '',
  content_id: '',
  start_date: '',
  end_date: '',
  status: 'pending',
});

// Form validation
const errors = reactive({
  advertiser_id: '',
  advertiser_name: '',
  spot_id: '',
  content_id: '',
  start_date: '',
  end_date: '',
  status: '',
});

// Computed properties
const availableSpots = computed(() => {
  return adSpots.value.filter(
    spot =>
      spot.available_slots > 0 ||
      (props.isEdit && props.subscription && props.subscription.spot_id === spot.id)
  );
});

const minStartDate = computed(() => {
  const today = new Date();
  return today.toISOString().split('T')[0];
});

const minEndDate = computed(() => {
  if (!form.start_date) return minStartDate.value;
  return form.start_date;
});

// Initialize form with subscription data if editing
onMounted(async () => {
  console.log('AdSubscriptionManager mounted', {
    isEdit: props.isEdit,
    subscription: props.subscription,
  });

  try {
    // Fetch ad spots
    console.log('Fetching ad spots...');
    await fetchAds();
    console.log('Ad spots loaded:', adSpots.value.length);

    // Fetch users
    console.log('Fetching users...');
    await fetchUsers();
    console.log('Users loaded:', users.value.length);

    // Content will be fetched when spot is selected via watcher

    // Set default start date to today
    if (!props.isEdit) {
      const today = new Date();
      form.start_date = today.toISOString().split('T')[0];

      // Set default end date to 30 days from now
      const endDate = new Date(today);
      endDate.setDate(endDate.getDate() + 30);
      form.end_date = endDate.toISOString().split('T')[0];
    }

    if (props.isEdit && props.subscription) {
      form.advertiser_id = props.subscription.advertiser_id || props.subscription.user_id || '';
      form.advertiser_name = props.subscription.advertiser_name || '';
      form.spot_id = props.subscription.spot_id || '';
      form.content_id = props.subscription.content_id || '';

      // Convert Firestore timestamps to date strings
      if (props.subscription.start_date) {
        const startDate = props.subscription.start_date.toDate
          ? props.subscription.start_date.toDate()
          : new Date(props.subscription.start_date);
        form.start_date = startDate.toISOString().split('T')[0];
      }

      if (props.subscription.end_date) {
        const endDate = props.subscription.end_date.toDate
          ? props.subscription.end_date.toDate()
          : new Date(props.subscription.end_date);
        form.end_date = endDate.toISOString().split('T')[0];
      }

      form.status = props.subscription.status || 'pending';
    }

    console.log('Form initialized:', form);
  } catch (err) {
    console.error('Error initializing form:', err);
    formError.value = 'Failed to load form data';
  }
});

// Fetch users from Firestore
const fetchUsers = async () => {
  try {
    if (!firestore) {
      throw new Error('Firestore not initialized');
    }

    // Try both 'users' and 'user' collections as the codebase might use either
    let usersSnapshot;
    try {
      const usersCollection = collection(firestore, 'users');
      usersSnapshot = await getDocs(usersCollection);
    } catch (err) {
      console.log('Trying "user" collection instead of "users"');
      const userCollection = collection(firestore, 'user');
      usersSnapshot = await getDocs(userCollection);
    }

    users.value = usersSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    })) as User[];

    console.log('Loaded users:', users.value.length);

    // If no users found, add current user as fallback
    if (users.value.length === 0) {
      const { user: currentUser } = useAuth();
      if (currentUser.value) {
        users.value = [
          {
            id: currentUser.value.uid,
            email: currentUser.value.email,
            displayName: currentUser.value.displayName || currentUser.value.email,
          },
        ];
      }
    }
  } catch (err) {
    console.error('Error fetching users:', err);
    formError.value = 'Failed to load users';
  }
};

// Fetch content for the selected spot (both flyers and ad_content)
const fetchSpotContent = async (spotId: string) => {
  try {
    if (!spotId) {
      adSpotContent.value = [];
      return;
    }

    console.log('Fetching content for spot:', spotId);

    // Fetch both ad_content and flyers that could be used for this spot
    const [adContent, allFlyers] = await Promise.all([
      fetchAdSpotContent(spotId),
      firestore ? getDocs(collection(firestore, 'flyers')) : Promise.resolve({ docs: [] }),
    ]);

    // Map flyers to the same format as ad content
    const flyersData = allFlyers.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        title: data.title || data.name,
        name: data.name,
        type: 'flyer',
        contentType: 'flyer',
        status: data.status,
        ...data,
      };
    }) as any[];

    // Combine both types of content
    const allContent = [
      ...adContent.map(content => ({ ...content, type: 'ad_content' })),
      ...flyersData.filter(
        (flyer: any) =>
          flyer.title &&
          (!flyer.status || flyer.status === 'Approved' || flyer.status === 'approved')
      ),
    ];

    adSpotContent.value = allContent;

    console.log('Content loaded:', {
      adContent: adContent.length,
      flyers: flyersData.length,
      total: allContent.length,
    });
  } catch (err: any) {
    console.error('Error fetching content:', err);
    formError.value = `Failed to load content: ${err.message || err}`;
    adSpotContent.value = [];
  }
};

// Watch for advertiser_id changes to auto-populate name
watch(() => form.advertiser_id, (newId) => {
  if (newId) {
    const selectedUser = users.value.find(u => u.id === newId);
    if (selectedUser) {
      form.advertiser_name = selectedUser.name || selectedUser.displayName || selectedUser.email || 'Unknown User';
    }
  } else {
    form.advertiser_name = '';
  }
});

// Watch for spot_id changes to fetch content
watch(
  () => form.spot_id,
  newSpotId => {
    if (newSpotId) {
      fetchSpotContent(newSpotId);
      // Clear content selection when spot changes
      form.content_id = '';
      errors.content_id = '';
    } else {
      adSpotContent.value = [];
      form.content_id = '';
    }
  }
);

// Update end date based on start date and duration
const updateEndDate = () => {
  if (!form.start_date) return;

  const startDate = new Date(form.start_date);
  const selectedSpot = adSpots.value.find(spot => spot.id === form.spot_id);

  if (selectedSpot && selectedSpot.duration) {
    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + selectedSpot.duration);
    form.end_date = endDate.toISOString().split('T')[0];
  }
};

// Update duration from selected spot
const updateDurationFromSpot = () => {
  if (!form.spot_id) return;

  const selectedSpot = adSpots.value.find(spot => spot.id === form.spot_id);

  if (selectedSpot && selectedSpot.duration && form.start_date) {
    updateEndDate();
  }
};

// Validate form
const validateForm = () => {
  let isValid = true;

  // Reset errors
  Object.keys(errors).forEach(key => {
    (errors as any)[key] = '';
  });

  // Validate advertiser_id
  if (!form.advertiser_id) {
    errors.advertiser_id = 'Advertiser is required';
    isValid = false;
  }

  // Validate spot_id
  if (!form.spot_id) {
    errors.spot_id = 'Ad spot is required';
    isValid = false;
  }

  // Validate content_id
  if (!form.content_id) {
    errors.content_id = 'Ad Content is required';
    isValid = false;
  }

  // Validate start_date
  if (!form.start_date) {
    errors.start_date = 'Start date is required';
    isValid = false;
  }

  // Validate end_date
  if (!form.end_date) {
    errors.end_date = 'End date is required';
    isValid = false;
  } else if (form.start_date && new Date(form.end_date) <= new Date(form.start_date)) {
    errors.end_date = 'End date must be after start date';
    isValid = false;
  }

  // Validate status
  if (!form.status) {
    errors.status = 'Status is required';
    isValid = false;
  }

  return isValid;
};

// Submit form
const submitForm = async () => {
  // Validate form
  if (!validateForm()) {
    return;
  }

  isSubmitting.value = true;
  formError.value = '';

  try {
    // Convert date strings to Date objects
    const formData = {
      ...form,
      start_date: new Date(form.start_date),
      end_date: new Date(form.end_date),
    };

    if (props.isEdit) {
      // Update existing subscription
      // Find the spot details for the unified ad
      const spot = adSpots.value.find(s => s.id === form.spot_id);
      const user = users.value.find(u => u.id === form.advertiser_id);
      
      const unifiedData = {
        // Spot data
        name: spot?.name || '',
        description: spot?.description || '',
        position: spot?.position || 'middle',
        price: spot?.price || 0,
        dimensions: spot?.dimensions || '',
        // Subscription data
        advertiser_id: form.advertiser_id,
        advertiser_name: form.advertiser_name || user?.displayName || user?.email || '',
        start_date: form.start_date,
        end_date: form.end_date,
        status: form.status,
        payment_status: form.payment_status || 'pending'
      };
      
      const updatedSubscription = await updateAd(props.subscription.id, unifiedData);
      if (updatedSubscription) {
        emit('updated', updatedSubscription);
      } else {
        formError.value = subscriptionError.value || 'Failed to update subscription';
      }
    } else {
      // Create new subscription
      // Find the spot details for the unified ad
      const spot = adSpots.value.find(s => s.id === form.spot_id);
      const user = users.value.find(u => u.id === form.advertiser_id);
      
      const unifiedData = {
        // Spot data
        name: spot?.name || '',
        description: spot?.description || '',
        position: spot?.position || 'middle',
        price: spot?.price || 0,
        dimensions: spot?.dimensions || '',
        // Subscription data
        advertiser_id: form.advertiser_id,
        advertiser_name: form.advertiser_name || user?.displayName || user?.email || '',
        start_date: form.start_date,
        end_date: form.end_date,
        status: form.status,
        payment_status: form.payment_status || 'pending'
      };
      
      const newSubscription = await createAd(unifiedData);
      if (newSubscription) {
        emit('created', newSubscription);
      } else {
        formError.value = subscriptionError.value || 'Failed to create subscription';
      }
    }
  } catch (err: any) {
    console.error('Error submitting form:', err);
    formError.value = err.message || 'An unexpected error occurred';
  } finally {
    isSubmitting.value = false;
  }
};
</script>
