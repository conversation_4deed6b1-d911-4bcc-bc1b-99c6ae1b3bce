<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full mx-4">
    <div class="p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
          {{ isEdit ? 'Edit Ad Spot' : 'Create New Ad Spot' }}
        </h3>
        <button
          @click="$emit('close')"
          class="text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400"
        >
          <Icon name="mdi:close" class="h-5 w-5" />
        </button>
      </div>

      <form @submit.prevent="submitForm" class="space-y-6">
        <!-- Name -->
        <div>
          <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Name <span class="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="name"
            v-model="form.name"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
            :class="{ 'border-red-500 dark:border-red-500': errors.name }"
            placeholder="Homepage Banner"
            required
          />
          <p v-if="errors.name" class="mt-1 text-sm text-red-600 dark:text-red-400">
            {{ errors.name }}
          </p>
        </div>

        <!-- Description -->
        <div>
          <label
            for="description"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            Description
          </label>
          <textarea
            id="description"
            v-model="form.description"
            rows="3"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
            :class="{ 'border-red-500 dark:border-red-500': errors.description }"
            placeholder="Banner ad spot on the homepage"
          ></textarea>
          <p v-if="errors.description" class="mt-1 text-sm text-red-600 dark:text-red-400">
            {{ errors.description }}
          </p>
        </div>

        <!-- Position -->
        <div>
          <label
            for="position"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            Position <span class="text-red-500">*</span>
          </label>
          <select
            id="position"
            v-model="form.position"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
            :class="{ 'border-red-500 dark:border-red-500': errors.position }"
            required
          >
            <option value="">Select Position</option>
            <option value="top">Top (Header Area)</option>
            <option value="middle">Middle (Content Area)</option>
            <option value="bottom">Bottom (Footer Area)</option>
          </select>
          <p v-if="errors.position" class="mt-1 text-sm text-red-600 dark:text-red-400">
            {{ errors.position }}
          </p>
          <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
            Choose where this ad will appear on the homepage
          </p>
        </div>

        <!-- Price -->
        <div>
          <label
            for="price"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            Price (daily) <span class="text-red-500">*</span>
          </label>
          <input
            type="number"
            id="price"
            v-model="form.price"
            min="0"
            step="0.01"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
            :class="{ 'border-red-500 dark:border-red-500': errors.price }"
            placeholder="99.99"
            required
          />
          <p v-if="errors.price" class="mt-1 text-sm text-red-600 dark:text-red-400">
            {{ errors.price }}
          </p>
        </div>

        <!-- Campaign Period -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Start Date -->
          <div>
            <label
              for="start_date"
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >
              Start Date <span class="text-red-500">*</span>
            </label>
            <input
              type="date"
              id="start_date"
              v-model="form.start_date"
              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
              :class="{ 'border-red-500 dark:border-red-500': errors.start_date }"
              :min="today"
              required
            />
            <p v-if="errors.start_date" class="mt-1 text-sm text-red-600 dark:text-red-400">
              {{ errors.start_date }}
            </p>
          </div>

          <!-- End Date -->
          <div>
            <label
              for="end_date"
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >
              End Date <span class="text-red-500">*</span>
            </label>
            <input
              type="date"
              id="end_date"
              v-model="form.end_date"
              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
              :class="{ 'border-red-500 dark:border-red-500': errors.end_date }"
              :min="form.start_date || today"
              required
            />
            <p v-if="errors.end_date" class="mt-1 text-sm text-red-600 dark:text-red-400">
              {{ errors.end_date }}
            </p>
          </div>
        </div>

        <!-- Status and Payment Status -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Status -->
          <div>
            <label
              for="status"
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >
              Status <span class="text-red-500">*</span>
            </label>
            <select
              id="status"
              v-model="form.status"
              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
              :class="{ 'border-red-500 dark:border-red-500': errors.status }"
              required
            >
              <option value="pending">Pending</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
            <p v-if="errors.status" class="mt-1 text-sm text-red-600 dark:text-red-400">
              {{ errors.status }}
            </p>
          </div>

          <!-- Payment Status -->
          <div>
            <label
              for="payment_status"
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >
              Payment Status <span class="text-red-500">*</span>
            </label>
            <select
              id="payment_status"
              v-model="form.payment_status"
              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
              :class="{ 'border-red-500 dark:border-red-500': errors.payment_status }"
              required
            >
              <option value="pending">Pending</option>
              <option value="paid">Paid</option>
              <option value="overdue">Overdue</option>
            </select>
            <p v-if="errors.payment_status" class="mt-1 text-sm text-red-600 dark:text-red-400">
              {{ errors.payment_status }}
            </p>
          </div>
        </div>

        <!-- Dimensions -->
        <div>
          <label
            for="dimensions"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            Dimensions (e.g., 300x250)
          </label>
          <input
            type="text"
            id="dimensions"
            v-model="form.dimensions"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
            :class="{ 'border-red-500 dark:border-red-500': errors.dimensions }"
            placeholder="300x250"
          />
          <p v-if="errors.dimensions" class="mt-1 text-sm text-red-600 dark:text-red-400">
            {{ errors.dimensions }}
          </p>
        </div>

        <!-- Click-through URL -->
        <div>
          <label
            for="target_url"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            Click-through URL (Optional)
          </label>
          <input
            type="url"
            id="target_url"
            v-model="form.target_url"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
            :class="{ 'border-red-500 dark:border-red-500': errors.target_url }"
            placeholder="https://example.com/landing-page"
          />
          <p v-if="errors.target_url" class="mt-1 text-sm text-red-600 dark:text-red-400">
            {{ errors.target_url }}
          </p>
          <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
            When users click on the ad, they'll be taken to this URL
          </p>
        </div>

        <!-- Error message -->
        <div
          v-if="formError"
          class="p-4 bg-red-50 dark:bg-red-900/30 border border-red-100 dark:border-red-800 rounded-md"
        >
          <p class="text-sm text-red-600 dark:text-red-400">{{ formError }}</p>
        </div>

        <!-- Form actions -->
        <div class="flex items-center justify-end space-x-3 pt-4">
          <button
            type="button"
            @click="$emit('close')"
            class="px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0072ff] dark:focus:ring-offset-gray-800"
          >
            Cancel
          </button>
          <button
            type="submit"
            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#0072ff] hover:bg-[#0054bb] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0072ff] dark:focus:ring-offset-gray-800"
            :disabled="isSubmitting"
          >
            <span v-if="isSubmitting" class="flex items-center">
              <Icon name="mdi:loading" class="animate-spin mr-2 h-4 w-4" />
              {{ isEdit ? 'Updating...' : 'Creating...' }}
            </span>
            <span v-else>
              {{ isEdit ? 'Update Ad Spot' : 'Create Ad Spot' }}
            </span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue';
import { useAds } from '~/composables/useAds';

const props = defineProps({
  spot: {
    type: Object,
    default: null,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['close', 'created', 'updated']);

// Initialize composable
const { createAd, updateAd, error: adSpotError } = useAds();

// Form state
const form = reactive({
  name: '',
  description: '',
  position: '',
  price: 0,
  start_date: '',
  end_date: '',
  status: 'pending',
  payment_status: 'pending',
  dimensions: '',
  target_url: '',
});

// Form validation
const errors = reactive({
  name: '',
  description: '',
  position: '',
  price: '',
  start_date: '',
  end_date: '',
  status: '',
  payment_status: '',
  dimensions: '',
  target_url: '',
});

// Form submission state
const isSubmitting = ref(false);
const formError = ref('');

// Today's date for date inputs
const today = computed(() => {
  return new Date().toISOString().split('T')[0];
});

// Initialize form with spot data if editing
onMounted(() => {
  if (props.isEdit && props.spot) {
    form.name = props.spot.name || '';
    form.description = props.spot.description || '';
    form.position = props.spot.position || props.spot.location || '';
    form.price = props.spot.price || 0;
    form.start_date = props.spot.start_date || '';
    form.end_date = props.spot.end_date || '';
    form.status = props.spot.status || 'pending';
    form.payment_status = props.spot.payment_status || 'pending';
    form.dimensions = props.spot.dimensions || '';
    form.target_url = props.spot.target_url || '';
  }
});

// Validate form
const validateForm = () => {
  let isValid = true;

  // Reset errors
  Object.keys(errors).forEach(key => {
    errors[key] = '';
  });

  // Validate name
  if (!form.name.trim()) {
    errors.name = 'Name is required';
    isValid = false;
  }

  // Validate position
  if (!form.position.trim()) {
    errors.position = 'Position is required';
    isValid = false;
  }

  // Validate price
  if (form.price < 0) {
    errors.price = 'Price must be a positive number';
    isValid = false;
  }

  // Validate start date
  if (!form.start_date) {
    errors.start_date = 'Start date is required';
    isValid = false;
  }

  // Validate end date
  if (!form.end_date) {
    errors.end_date = 'End date is required';
    isValid = false;
  }

  // Validate date range
  if (form.start_date && form.end_date && new Date(form.end_date) <= new Date(form.start_date)) {
    errors.end_date = 'End date must be after start date';
    isValid = false;
  }

  // Validate status
  if (!form.status) {
    errors.status = 'Status is required';
    isValid = false;
  }

  // Validate payment status
  if (!form.payment_status) {
    errors.payment_status = 'Payment status is required';
    isValid = false;
  }

  return isValid;
};

// Submit form
const submitForm = async () => {
  // Validate form
  if (!validateForm()) {
    return;
  }

  isSubmitting.value = true;
  formError.value = '';

  try {
    // Convert numeric fields
    const formData = {
      ...form,
      price: parseFloat(form.price),
    };

    if (props.isEdit) {
      // Update existing ad spot using unified ads
      const updatedSpot = await updateAd(props.spot.id, formData);
      if (updatedSpot) {
        emit('updated', updatedSpot);
      } else {
        formError.value = adSpotError.value || 'Failed to update ad spot';
      }
    } else {
      // Create new ad spot using unified ads
      const newSpot = await createAd(formData);
      if (newSpot) {
        emit('created', newSpot);
      } else {
        formError.value = adSpotError.value || 'Failed to create ad spot';
      }
    }
  } catch (err) {
    console.error('Error submitting form:', err);
    formError.value = err.message || 'An unexpected error occurred';
  } finally {
    isSubmitting.value = false;
  }
};
</script>
