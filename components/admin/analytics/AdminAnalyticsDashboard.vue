<template>
  <div class="admin-analytics-dashboard">
    <div class="analytics-header">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-gray-800">Platform Analytics</h1>
        <NotificationBell />
      </div>

      <!-- Date Range Selector -->
      <div class="date-range-selector mb-6">
        <div class="flex items-center space-x-4">
          <div class="flex items-center">
            <label for="admin-start-date" class="mr-2 text-sm font-medium text-gray-700">From:</label>
            <input
              type="date"
              id="admin-start-date"
              class="border border-gray-300 rounded-md px-3 py-2 text-sm"
              :value="formatDate(startDate)"
              @change="updateStartDate"
            />
          </div>
          <div class="flex items-center">
            <label for="admin-end-date" class="mr-2 text-sm font-medium text-gray-700">To:</label>
            <input
              type="date"
              id="admin-end-date"
              class="border border-gray-300 rounded-md px-3 py-2 text-sm"
              :value="formatDate(endDate)"
              @change="updateEndDate"
            />
          </div>
          <div class="flex space-x-2">
            <button
              @click="setPresetRange('7d')"
              class="px-3 py-1 text-sm rounded-md"
              :class="activeRange === '7d' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'"
            >
              Last 7 days
            </button>
            <button
              @click="setPresetRange('30d')"
              class="px-3 py-1 text-sm rounded-md"
              :class="activeRange === '30d' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'"
            >
              Last 30 days
            </button>
            <button
              @click="setPresetRange('90d')"
              class="px-3 py-1 text-sm rounded-md"
              :class="activeRange === '90d' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'"
            >
              Last 90 days
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
      <p>{{ error }}</p>
    </div>

    <!-- Analytics Content -->
    <div v-else>
      <!-- Summary Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <div class="bg-white rounded-lg shadow p-4">
          <h3 class="text-sm font-medium text-gray-500">Total Users</h3>
          <p class="text-2xl font-bold text-gray-800">{{ summaryMetrics.total_users }}</p>
          <p class="text-sm text-gray-500">{{ summaryMetrics.new_users }} new in selected period</p>
        </div>

        <div class="bg-white rounded-lg shadow p-4">
          <h3 class="text-sm font-medium text-gray-500">Active Users</h3>
          <p class="text-2xl font-bold text-gray-800">{{ summaryMetrics.active_users }}</p>
          <p class="text-sm text-gray-500">
            {{ ((summaryMetrics.active_users / summaryMetrics.total_users) * 100).toFixed(1) }}% of total users
          </p>
        </div>

        <div class="bg-white rounded-lg shadow p-4">
          <h3 class="text-sm font-medium text-gray-500">Total Content</h3>
          <p class="text-2xl font-bold text-gray-800">{{ summaryMetrics.total_content }}</p>
          <p class="text-sm text-gray-500">{{ summaryMetrics.new_content }} new in selected period</p>
        </div>

        <div class="bg-white rounded-lg shadow p-4">
          <h3 class="text-sm font-medium text-gray-500">Engagement Rate</h3>
          <p class="text-2xl font-bold text-gray-800">{{ summaryMetrics.avg_engagement_rate.toFixed(1) }}%</p>
          <p class="text-sm text-gray-500">Platform average</p>
        </div>
      </div>

      <!-- Second Row of Summary Cards -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <div class="bg-white rounded-lg shadow p-4">
          <h3 class="text-sm font-medium text-gray-500">Total Views</h3>
          <p class="text-2xl font-bold text-gray-800">{{ summaryMetrics.total_views }}</p>
          <p class="text-sm text-gray-500">
            {{ (summaryMetrics.total_views / summaryMetrics.total_content).toFixed(1) }} views per content
          </p>
        </div>

        <div class="bg-white rounded-lg shadow p-4">
          <h3 class="text-sm font-medium text-gray-500">Total Downloads</h3>
          <p class="text-2xl font-bold text-gray-800">{{ summaryMetrics.total_downloads }}</p>
          <p class="text-sm text-gray-500">
            {{ ((summaryMetrics.total_downloads / summaryMetrics.total_views) * 100).toFixed(1) }}% conversion rate
          </p>
        </div>

        <div class="bg-white rounded-lg shadow p-4">
          <h3 class="text-sm font-medium text-gray-500">Contact Actions</h3>
          <p class="text-2xl font-bold text-gray-800">{{ summaryMetrics.total_contact_actions }}</p>
          <p class="text-sm text-gray-500">
            {{ ((summaryMetrics.total_contact_actions / summaryMetrics.total_views) * 100).toFixed(1) }}% of views
          </p>
        </div>
      </div>

      <!-- User Growth Chart -->
      <div class="bg-white rounded-lg shadow p-4 mb-8">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-medium text-gray-800">User Growth</h2>
          <div class="flex space-x-2">
            <button
              @click="userGrowthInterval = 'daily'"
              class="px-3 py-1 text-sm rounded-md"
              :class="userGrowthInterval === 'daily' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'"
            >
              Daily
            </button>
            <button
              @click="userGrowthInterval = 'weekly'"
              class="px-3 py-1 text-sm rounded-md"
              :class="userGrowthInterval === 'weekly' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'"
            >
              Weekly
            </button>
            <button
              @click="userGrowthInterval = 'monthly'"
              class="px-3 py-1 text-sm rounded-md"
              :class="userGrowthInterval === 'monthly' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'"
            >
              Monthly
            </button>
          </div>
        </div>
        <div class="h-64">
          <canvas ref="userGrowthChartRef"></canvas>
        </div>
      </div>

      <!-- Platform Activity Chart -->
      <div class="bg-white rounded-lg shadow p-4 mb-8">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-medium text-gray-800">Platform Activity</h2>
          <div class="flex space-x-2">
            <button
              @click="activityInterval = 'daily'"
              class="px-3 py-1 text-sm rounded-md"
              :class="activityInterval === 'daily' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'"
            >
              Daily
            </button>
            <button
              @click="activityInterval = 'weekly'"
              class="px-3 py-1 text-sm rounded-md"
              :class="activityInterval === 'weekly' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'"
            >
              Weekly
            </button>
            <button
              @click="activityInterval = 'monthly'"
              class="px-3 py-1 text-sm rounded-md"
              :class="activityInterval === 'monthly' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'"
            >
              Monthly
            </button>
          </div>
        </div>
        <div class="h-64">
          <canvas ref="activityChartRef"></canvas>
        </div>
      </div>

      <!-- Bottom Charts Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <!-- Content Engagement -->
        <div class="bg-white rounded-lg shadow p-4">
          <h2 class="text-lg font-medium text-gray-800 mb-4">Content Engagement by Type</h2>
          <div class="h-64">
            <canvas ref="contentEngagementChartRef"></canvas>
          </div>
        </div>

        <!-- Top Content -->
        <div class="bg-white rounded-lg shadow p-4">
          <h2 class="text-lg font-medium text-gray-800 mb-4">Top Performing Content</h2>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Views</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Engagement</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="(item, index) in topContent.slice(0, 5)" :key="index">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">{{ item.name }}</div>
                    <div class="text-sm text-gray-500">{{ item.company }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ item.views }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ item.engagement_rate.toFixed(1) }}%</div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Geographic Visualization -->
      <div class="mb-8">
        <h2 class="text-lg font-medium text-gray-800 mb-4">Geographic Distribution</h2>
        <GeographicMap />
      </div>

      <!-- Engagement Funnel -->
      <div class="mb-8">
        <h2 class="text-lg font-medium text-gray-800 mb-4">Platform Engagement Funnel</h2>
        <EngagementFunnel />
      </div>

      <!-- A/B Testing -->
      <div class="mb-8">
        <h2 class="text-lg font-medium text-gray-800 mb-4">A/B Testing Overview</h2>
        <ABTestResults />
      </div>

      <!-- Export Controls -->
      <div class="flex justify-end mb-8">
        <button
          @click="exportData"
          class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Export Data
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useAdminAnalytics } from '~/composables/useAdminAnalytics'
import Chart from 'chart.js/auto'
import moment from 'moment'
import GeographicMap from '~/components/analytics/GeographicMap.vue'
import EngagementFunnel from '~/components/analytics/EngagementFunnel.vue'
import ABTestResults from '~/components/analytics/ABTestResults.vue'
import NotificationBell from '~/components/analytics/NotificationBell.vue'

// Analytics composable
const {
  isLoading,
  error,
  startDate,
  endDate,
  summaryMetrics,
  userGrowth,
  contentEngagement,
  topContent,
  fetchPlatformAnalytics,
  getTimeSeriesData,
  getUserGrowthData,
  getContentEngagementData,
  getTopContentData,
  setDateRange,
  exportAsCSV
} = useAdminAnalytics()

// Chart references
const userGrowthChartRef = ref<HTMLCanvasElement | null>(null)
const activityChartRef = ref<HTMLCanvasElement | null>(null)
const contentEngagementChartRef = ref<HTMLCanvasElement | null>(null)

// Chart instances
let userGrowthChart: Chart | null = null
let activityChart: Chart | null = null
let contentEngagementChart: Chart | null = null

// Chart intervals
const userGrowthInterval = ref<'daily' | 'weekly' | 'monthly'>('daily')
const activityInterval = ref<'daily' | 'weekly' | 'monthly'>('daily')

// Active date range
const activeRange = ref<string>('30d')

// Format date for input
const formatDate = (date: Date) => {
  return moment(date).format('YYYY-MM-DD')
}

// Update start date
const updateStartDate = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.value) {
    activeRange.value = 'custom'
    setDateRange(new Date(target.value), endDate.value)
  }
}

// Update end date
const updateEndDate = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.value) {
    activeRange.value = 'custom'
    setDateRange(startDate.value, new Date(target.value))
  }
}

// Set preset date range
const setPresetRange = (range: string) => {
  activeRange.value = range
  const end = new Date()
  let start: Date

  switch (range) {
    case '7d':
      start = moment().subtract(7, 'days').toDate()
      break
    case '30d':
      start = moment().subtract(30, 'days').toDate()
      break
    case '90d':
      start = moment().subtract(90, 'days').toDate()
      break
    default:
      start = moment().subtract(30, 'days').toDate()
  }

  setDateRange(start, end)
}

// Initialize charts
const initCharts = () => {
  // User Growth Chart
  if (userGrowthChartRef.value) {
    const growthData = getUserGrowthData(userGrowthInterval.value)

    userGrowthChart = new Chart(userGrowthChartRef.value, {
      type: 'line',
      data: {
        labels: growthData.map(item => item.date),
        datasets: [
          {
            label: 'New Users',
            data: growthData.map(item => item.count),
            borderColor: '#3B82F6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.1
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    })
  }

  // Activity Chart
  if (activityChartRef.value) {
    const viewsData = getTimeSeriesData('views', activityInterval.value)
    const downloadsData = getTimeSeriesData('downloads', activityInterval.value)
    const contactActionsData = getTimeSeriesData('contact_actions', activityInterval.value)

    activityChart = new Chart(activityChartRef.value, {
      type: 'line',
      data: {
        labels: viewsData.map(item => item.date),
        datasets: [
          {
            label: 'Views',
            data: viewsData.map(item => item.value),
            borderColor: '#3B82F6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.1
          },
          {
            label: 'Downloads',
            data: downloadsData.map(item => item.value),
            borderColor: '#10B981',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            tension: 0.1
          },
          {
            label: 'Contact Actions',
            data: contactActionsData.map(item => item.value),
            borderColor: '#F59E0B',
            backgroundColor: 'rgba(245, 158, 11, 0.1)',
            tension: 0.1
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    })
  }

  // Content Engagement Chart
  if (contentEngagementChartRef.value) {
    const engagementData = getContentEngagementData()

    contentEngagementChart = new Chart(contentEngagementChartRef.value, {
      type: 'bar',
      data: {
        labels: engagementData.map(item => item.type),
        datasets: [
          {
            label: 'Views',
            data: engagementData.map(item => item.views),
            backgroundColor: '#3B82F6'
          },
          {
            label: 'Downloads',
            data: engagementData.map(item => item.downloads),
            backgroundColor: '#10B981'
          },
          {
            label: 'Contact Actions',
            data: engagementData.map(item => item.contact_actions),
            backgroundColor: '#F59E0B'
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    })
  }
}

// Update charts
const updateCharts = () => {
  // Destroy existing charts
  if (userGrowthChart) userGrowthChart.destroy()
  if (activityChart) activityChart.destroy()
  if (contentEngagementChart) contentEngagementChart.destroy()

  // Initialize new charts
  initCharts()
}

// Export data
const exportData = () => {
  const csvContent = exportAsCSV()
  if (!csvContent) return

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.setAttribute('href', url)
  link.setAttribute('download', `platform_analytics_${formatDate(startDate.value)}_to_${formatDate(endDate.value)}.csv`)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// Watch for changes in chart intervals
watch([userGrowthInterval, activityInterval], () => {
  updateCharts()
})

// Watch for changes in summary metrics
watch(summaryMetrics, () => {
  updateCharts()
})

// Fetch data and initialize charts on mount
onMounted(async () => {
  await fetchPlatformAnalytics()
  initCharts()
})
</script>

<style scoped>
.admin-analytics-dashboard {
  @apply p-6 bg-gray-50 rounded-lg;
}
</style>
