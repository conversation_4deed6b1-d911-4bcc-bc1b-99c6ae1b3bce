<template>
  <div class="claims-management">
    <!-- Header -->
    <div class="mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-2xl font-bold text-gray-900">Claims Management</h2>
          <p class="text-gray-600">Review and manage business card ownership claims</p>
        </div>
        
        <!-- Stats Summary -->
        <div class="flex space-x-4">
          <div class="text-center">
            <p class="text-2xl font-bold text-yellow-600">{{ pendingClaimsCount }}</p>
            <p class="text-sm text-gray-600">Pending</p>
          </div>
          <div class="text-center">
            <p class="text-2xl font-bold text-green-600">{{ approvedClaimsCount }}</p>
            <p class="text-sm text-gray-600">Approved Today</p>
          </div>
          <div class="text-center">
            <p class="text-2xl font-bold text-red-600">{{ rejectedClaimsCount }}</p>
            <p class="text-sm text-gray-600">Rejected Today</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="mb-6 flex flex-wrap items-center gap-4">
      <div class="flex items-center space-x-2">
        <label class="text-sm font-medium text-gray-700">Status:</label>
        <select
          v-model="selectedStatus"
          class="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">All</option>
          <option value="pending">Pending</option>
          <option value="approved">Approved</option>
          <option value="rejected">Rejected</option>
        </select>
      </div>

      <div class="flex items-center space-x-2">
        <label class="text-sm font-medium text-gray-700">Verification:</label>
        <select
          v-model="selectedVerification"
          class="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">All Methods</option>
          <option value="email">Email</option>
          <option value="phone">Phone</option>
          <option value="document">Document</option>
          <option value="manual">Manual</option>
        </select>
      </div>

      <div class="flex items-center space-x-2">
        <Icon name="mdi:magnify" class="w-4 h-4 text-gray-400" />
        <input
          v-model="searchQuery"
          type="text"
          placeholder="Search claims..."
          class="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      <button
        @click="refreshClaims"
        class="flex items-center px-3 py-1 text-sm text-blue-600 hover:text-blue-800"
      >
        <Icon name="mdi:refresh" class="w-4 h-4 mr-1" />
        Refresh
      </button>
    </div>

    <!-- Claims Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Claim Details
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Business Card
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Verification
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr
              v-for="claim in filteredClaims"
              :key="claim.id"
              class="hover:bg-gray-50"
            >
              <!-- Claim Details -->
              <td class="px-6 py-4">
                <div class="flex items-center space-x-3">
                  <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <Icon name="mdi:account" class="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <p class="text-sm font-medium text-gray-900">
                      Claim #{{ claim.id.substring(0, 8) }}
                    </p>
                    <p class="text-sm text-gray-600">
                      {{ formatDate(claim.submittedAt) }}
                    </p>
                    <p class="text-xs text-gray-500">
                      by User {{ claim.claimerUserId.substring(0, 8) }}
                    </p>
                  </div>
                </div>
              </td>

              <!-- Business Card -->
              <td class="px-6 py-4">
                <div>
                  <p class="text-sm font-medium text-gray-900">
                    {{ getBusinessCardName(claim.cardId) }}
                  </p>
                  <p class="text-sm text-gray-600">
                    {{ getBusinessCardCompany(claim.cardId) }}
                  </p>
                  <p class="text-xs text-gray-500">
                    Card ID: {{ claim.cardId.substring(0, 8) }}
                  </p>
                </div>
              </td>

              <!-- Verification -->
              <td class="px-6 py-4">
                <div class="flex items-center space-x-2">
                  <Icon
                    :name="getVerificationIcon(claim.verificationMethod)"
                    class="w-4 h-4 text-gray-600"
                  />
                  <span class="text-sm capitalize text-gray-900">
                    {{ claim.verificationMethod }}
                  </span>
                </div>
                <div v-if="claim.verificationData.contactInfo" class="mt-1">
                  <p class="text-xs text-gray-600">
                    {{ claim.verificationData.contactInfo }}
                  </p>
                </div>
                <div v-if="claim.evidence && claim.evidence.length > 0" class="mt-1">
                  <p class="text-xs text-blue-600">
                    {{ claim.evidence.length }} evidence file(s)
                  </p>
                </div>
              </td>

              <!-- Status -->
              <td class="px-6 py-4">
                <span
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="getStatusColor(claim.status)"
                >
                  {{ claim.status.charAt(0).toUpperCase() + claim.status.slice(1) }}
                </span>
                <div v-if="claim.processedAt" class="mt-1">
                  <p class="text-xs text-gray-500">
                    Processed {{ formatDate(claim.processedAt) }}
                  </p>
                </div>
              </td>

              <!-- Actions -->
              <td class="px-6 py-4">
                <div class="flex items-center space-x-2">
                  <button
                    v-if="claim.status === 'pending'"
                    @click="openClaimDetail(claim)"
                    class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    Review
                  </button>
                  
                  <button
                    @click="viewClaimHistory(claim.cardId)"
                    class="text-gray-600 hover:text-gray-800 text-sm"
                  >
                    <Icon name="mdi:history" class="w-4 h-4" />
                  </button>
                  
                  <button
                    @click="viewBusinessCard(claim.cardId)"
                    class="text-gray-600 hover:text-gray-800 text-sm"
                  >
                    <Icon name="mdi:card-account-details" class="w-4 h-4" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Empty State -->
      <div v-if="filteredClaims.length === 0" class="text-center py-12">
        <Icon name="mdi:clipboard-text-outline" class="w-12 h-12 text-gray-400 mx-auto mb-3" />
        <p class="text-gray-500">No claims found matching your criteria</p>
      </div>
    </div>

    <!-- Pagination -->
    <div v-if="totalPages > 1" class="mt-6 flex items-center justify-center">
      <nav class="flex space-x-1">
        <button
          v-for="page in visiblePages"
          :key="page"
          @click="currentPage = page"
          class="px-3 py-2 text-sm font-medium rounded-md"
          :class="page === currentPage
            ? 'bg-blue-600 text-white'
            : 'text-gray-700 hover:bg-gray-100'"
        >
          {{ page }}
        </button>
      </nav>
    </div>

    <!-- Claim Detail Modal -->
    <ClaimDetailModal
      v-if="selectedClaim"
      :claim="selectedClaim"
      @close="selectedClaim = null"
      @approve="handleApproveClaim"
      @reject="handleRejectClaim"
    />

    <!-- Claim History Modal -->
    <ClaimHistoryModal
      v-if="selectedCardForHistory"
      :card-id="selectedCardForHistory"
      @close="selectedCardForHistory = null"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useClaimingSystem, type ClaimRequest } from '~/composables/useClaimingSystem'

// Use claiming system
const {
  claimRequests,
  getClaimsForReview,
  approveClaim,
  rejectClaim,
  formatDate,
  getClaimStatusColor
} = useClaimingSystem()

// Component state
const selectedStatus = ref('')
const selectedVerification = ref('')
const searchQuery = ref('')
const currentPage = ref(1)
const itemsPerPage = 20
const selectedClaim = ref<ClaimRequest | null>(null)
const selectedCardForHistory = ref<string | null>(null)

// Sample business cards data for lookup
const businessCards = ref([
  { id: '1', name: 'John Smith', company: 'Tech Corp' },
  { id: '2', name: 'Sarah Johnson', company: 'Design Studio' },
  { id: '3', name: 'Mike Chen', company: 'Finance Co' }
])

// Computed properties
const filteredClaims = computed(() => {
  let claims = claimRequests.value

  // Filter by status
  if (selectedStatus.value) {
    claims = claims.filter(claim => claim.status === selectedStatus.value)
  }

  // Filter by verification method
  if (selectedVerification.value) {
    claims = claims.filter(claim => claim.verificationMethod === selectedVerification.value)
  }

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    claims = claims.filter(claim =>
      claim.id.toLowerCase().includes(query) ||
      claim.cardId.toLowerCase().includes(query) ||
      claim.claimerUserId.toLowerCase().includes(query)
    )
  }

  // Sort by submission date (newest first)
  claims.sort((a, b) => new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime())

  // Paginate
  const startIndex = (currentPage.value - 1) * itemsPerPage
  return claims.slice(startIndex, startIndex + itemsPerPage)
})

const pendingClaimsCount = computed(() =>
  claimRequests.value.filter(claim => claim.status === 'pending').length
)

const approvedClaimsCount = computed(() => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return claimRequests.value.filter(claim =>
    claim.status === 'approved' &&
    claim.processedAt &&
    new Date(claim.processedAt) >= today
  ).length
})

const rejectedClaimsCount = computed(() => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return claimRequests.value.filter(claim =>
    claim.status === 'rejected' &&
    claim.processedAt &&
    new Date(claim.processedAt) >= today
  ).length
})

const totalPages = computed(() =>
  Math.ceil(claimRequests.value.length / itemsPerPage)
)

const visiblePages = computed(() => {
  const pages = []
  const start = Math.max(1, currentPage.value - 2)
  const end = Math.min(totalPages.value, start + 4)
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

// Methods
const getBusinessCardName = (cardId: string): string => {
  const card = businessCards.value.find(c => c.id === cardId)
  return card?.name || 'Unknown'
}

const getBusinessCardCompany = (cardId: string): string => {
  const card = businessCards.value.find(c => c.id === cardId)
  return card?.company || 'Unknown'
}

const getVerificationIcon = (method: string): string => {
  const icons = {
    email: 'mdi:email',
    phone: 'mdi:phone',
    document: 'mdi:file-document',
    manual: 'mdi:account-check'
  }
  return icons[method] || 'mdi:help-circle'
}

const getStatusColor = (status: string): string => {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800'
    case 'approved':
      return 'bg-green-100 text-green-800'
    case 'rejected':
      return 'bg-red-100 text-red-800'
    case 'expired':
      return 'bg-gray-100 text-gray-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const openClaimDetail = (claim: ClaimRequest) => {
  selectedClaim.value = claim
}

const viewClaimHistory = (cardId: string) => {
  selectedCardForHistory.value = cardId
}

const viewBusinessCard = (cardId: string) => {
  // Navigate to business card detail page
  navigateTo(`/c/businesscards/${cardId}`)
}

const handleApproveClaim = async (claimId: string, notes?: string) => {
  try {
    await approveClaim(claimId, notes)
    selectedClaim.value = null
  } catch (error) {
    console.error('Failed to approve claim:', error)
    alert('Failed to approve claim')
  }
}

const handleRejectClaim = async (claimId: string, notes?: string) => {
  try {
    await rejectClaim(claimId, notes)
    selectedClaim.value = null
  } catch (error) {
    console.error('Failed to reject claim:', error)
    alert('Failed to reject claim')
  }
}

const refreshClaims = () => {
  // In real app, this would refresh data from server
  console.log('Refreshing claims...')
}

// Initialize with sample data
onMounted(() => {
  // Add some sample claims for demo
  // In real app, this would load from Firebase
})
</script>

<style scoped>
.claims-management {
  max-width: 100%;
}

/* Custom table styling */
table th {
  position: sticky;
  top: 0;
  background: #f9fafb;
  z-index: 10;
}

/* Responsive table */
@media (max-width: 768px) {
  .overflow-x-auto {
    -webkit-overflow-scrolling: touch;
  }
  
  table {
    min-width: 800px;
  }
}
</style>