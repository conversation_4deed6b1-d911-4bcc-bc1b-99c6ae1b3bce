<template>
  <div class="modal-content bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 rounded-lg shadow-2xl">
    <!-- Header with glass-morphism effect -->
    <div class="backdrop-blur-sm bg-white/5 rounded-t-xl p-6 border-b border-gray-700/50">
      <div class="flex items-center justify-between">
        <h2 class="text-2xl font-bold text-white">
          {{ isEditMode ? 'Edit Ad' : 'Create New Ad' }}
        </h2>
        <button
          @click="$emit('close')"
          class="text-gray-400 hover:text-gray-200 transition-colors"
          type="button"
        >
          <Icon name="mdi:close" class="h-6 w-6" />
        </button>
      </div>
    </div>

    <!-- Form Content -->
    <form @submit.prevent="handleSubmit" class="p-6">
      <!-- Advertiser Selection -->
      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-300 mb-2">
          Advertiser <span class="text-red-400">*</span>
        </label>
        <select
          v-model="form.advertiser_id"
          class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg 
                 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent
                 transition-all duration-200"
          :class="{ 'border-red-500': errors.advertiser_id }"
          required
        >
          <option value="">Select an advertiser</option>
          <option v-for="advertiser in advertisers" :key="advertiser.id" :value="advertiser.id">
            {{ advertiser.name }}
          </option>
        </select>
        <p v-if="errors.advertiser_id" class="text-red-400 text-sm mt-1">
          {{ errors.advertiser_id }}
        </p>
      </div>

      <!-- Ad Details -->
      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-300 mb-2">
          Ad Name <span class="text-red-400">*</span>
        </label>
        <input
          v-model="form.name"
          type="text"
          class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg 
                 text-gray-200 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 
                 focus:border-transparent transition-all duration-200"
          :class="{ 'border-red-500': errors.name }"
          placeholder="e.g., Summer Sale Banner"
          required
        />
        <p v-if="errors.name" class="text-red-400 text-sm mt-1">
          {{ errors.name }}
        </p>
      </div>

      <!-- Description -->
      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-300 mb-2">
          Description
        </label>
        <textarea
          v-model="form.description"
          rows="3"
          class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg 
                 text-gray-200 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 
                 focus:border-transparent transition-all duration-200"
          :class="{ 'border-red-500': errors.description }"
          placeholder="Describe the ad placement and purpose"
        ></textarea>
        <p v-if="errors.description" class="text-red-400 text-sm mt-1">
          {{ errors.description }}
        </p>
      </div>

      <!-- Ad Details Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <!-- Position -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Position <span class="text-red-400">*</span>
          </label>
          <select
            v-model="form.position"
            class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg 
                   text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent
                   transition-all duration-200"
            :class="{ 'border-red-500': errors.position }"
            required
          >
            <option value="">Select position</option>
            <option value="top">Top (Header Area)</option>
            <option value="middle">Middle (Content Area)</option>
            <option value="bottom">Bottom (Footer Area)</option>
          </select>
          <p v-if="errors.position" class="text-red-400 text-sm mt-1">
            {{ errors.position }}
          </p>
        </div>

        <!-- Price -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Price (daily) <span class="text-red-400">*</span>
          </label>
          <input
            v-model.number="form.price"
            type="number"
            step="0.01"
            min="0"
            class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg 
                   text-gray-200 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 
                   focus:border-transparent transition-all duration-200"
            :class="{ 'border-red-500': errors.price }"
            placeholder="99.99"
            required
          />
          <p v-if="errors.price" class="text-red-400 text-sm mt-1">
            {{ errors.price }}
          </p>
        </div>

        <!-- Dimensions -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Dimensions <span class="text-red-400">*</span>
          </label>
          <input
            v-model="form.dimensions"
            type="text"
            class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg 
                   text-gray-200 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 
                   focus:border-transparent transition-all duration-200"
            :class="{ 'border-red-500': errors.dimensions }"
            placeholder="e.g., 728x90"
            pattern="\d+x\d+"
            required
          />
          <p v-if="errors.dimensions" class="text-red-400 text-sm mt-1">
            {{ errors.dimensions }}
          </p>
          <p class="text-gray-500 text-xs mt-1">Format: widthxheight (e.g., 300x250)</p>
        </div>

        <!-- Status -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Status <span class="text-red-400">*</span>
          </label>
          <select
            v-model="form.status"
            class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg 
                   text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent
                   transition-all duration-200"
            :class="{ 'border-red-500': errors.status }"
            required
          >
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="pending">Pending</option>
          </select>
          <p v-if="errors.status" class="text-red-400 text-sm mt-1">
            {{ errors.status }}
          </p>
        </div>
      </div>

      <!-- Date Range -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <!-- Start Date -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Start Date <span class="text-red-400">*</span>
          </label>
          <input
            v-model="form.start_date"
            type="date"
            class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg 
                   text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent
                   transition-all duration-200"
            :class="{ 'border-red-500': errors.start_date }"
            required
          />
          <p v-if="errors.start_date" class="text-red-400 text-sm mt-1">
            {{ errors.start_date }}
          </p>
        </div>

        <!-- End Date -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">
            End Date <span class="text-red-400">*</span>
          </label>
          <input
            v-model="form.end_date"
            type="date"
            :min="form.start_date"
            class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg 
                   text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent
                   transition-all duration-200"
            :class="{ 'border-red-500': errors.end_date }"
            required
          />
          <p v-if="errors.end_date" class="text-red-400 text-sm mt-1">
            {{ errors.end_date }}
          </p>
        </div>
      </div>

      <!-- Payment Status (only in edit mode) -->
      <div v-if="isEditMode" class="mb-6">
        <label class="block text-sm font-medium text-gray-300 mb-2">
          Payment Status
        </label>
        <select
          v-model="form.payment_status"
          class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg 
                 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent
                 transition-all duration-200"
        >
          <option value="pending">Pending</option>
          <option value="paid">Paid</option>
          <option value="overdue">Overdue</option>
        </select>
      </div>

      <!-- Upload Area -->
      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-300 mb-2">
          Ad Creative
        </label>
        <div 
          class="border-2 border-dashed border-gray-700 rounded-lg p-6 text-center
                 hover:border-gray-600 transition-colors cursor-pointer bg-gray-800/50"
          @click="triggerFileInput"
          @drop.prevent="handleFileDrop"
          @dragover.prevent
          @dragenter.prevent
        >
          <Icon name="mdi:cloud-upload" class="h-12 w-12 text-gray-500 mx-auto mb-3" />
          <p class="text-gray-400">
            Drop files here or click to upload
          </p>
          <p class="text-gray-500 text-sm mt-1">
            PNG, JPG, GIF up to 10MB
          </p>
          <input
            ref="fileInput"
            type="file"
            class="hidden"
            accept="image/*"
            @change="handleFileSelect"
          />
        </div>
        <div v-if="uploadedFileName" class="mt-2 text-sm text-gray-400">
          Uploaded: {{ uploadedFileName }}
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex justify-end space-x-3 pt-6 border-t border-gray-700/50">
        <button
          type="button"
          @click="$emit('close')"
          class="px-4 py-2 text-gray-300 bg-gray-800 border border-gray-700 rounded-lg 
                 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500
                 transition-all duration-200"
        >
          Cancel
        </button>
        <button
          type="submit"
          :disabled="isLoading"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 
                 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 
                 focus:ring-offset-gray-900 disabled:opacity-50 disabled:cursor-not-allowed
                 transition-all duration-200 flex items-center"
        >
          <Icon v-if="isLoading" name="mdi:loading" class="animate-spin h-5 w-5 mr-2" />
          {{ isEditMode ? 'Update Ad' : 'Create Ad' }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import type { IUnifiedAd, IUnifiedAdCreate } from '~/types/ads'
import { useAds } from '~/composables/useAds'
import { useUserManagement } from '~/composables/user-management'

// Props and emits
const props = defineProps<{
  ad?: IUnifiedAd | null
  isEdit?: boolean
}>()

const emit = defineEmits<{
  close: []
  success: [ad: IUnifiedAd]
}>()

// Composables
const { createAd, updateAd } = useAds()
const { users, fetchUsers } = useUserManagement()

// Filter users to get advertisers only
const advertisers = computed(() => {
  return users.value.filter(user => 
    user.roles?.includes('Advertiser') || 
    user.role === 'advertiser' ||
    user.type === 'advertiser'
  )
})

// Component state
const isLoading = ref(false)
const uploadedFileName = ref('')
const fileInput = ref<HTMLInputElement>()

// Form state
const form = ref({
  advertiser_id: '',
  name: '',
  description: '',
  position: '' as 'top' | 'middle' | 'bottom' | '',
  price: 0,
  dimensions: '',
  status: 'active' as 'active' | 'inactive' | 'pending',
  start_date: '',
  end_date: '',
  payment_status: 'pending' as 'paid' | 'pending' | 'overdue'
})

// Form errors
const errors = ref<Record<string, string>>({})

// Computed
const isEditMode = computed(() => props.isEdit || !!props.ad)

// Initialize form with ad data if in edit mode
watch(() => props.ad, (newAd) => {
  if (newAd) {
    form.value = {
      advertiser_id: newAd.advertiser_id,
      name: newAd.name,
      description: newAd.description,
      position: newAd.position,
      price: newAd.price,
      dimensions: newAd.dimensions,
      status: newAd.status,
      start_date: newAd.start_date,
      end_date: newAd.end_date,
      payment_status: newAd.payment_status
    }
  }
}, { immediate: true })

// Load advertisers on mount
onMounted(async () => {
  await fetchUsers()
})

// Form validation
const validateForm = (): boolean => {
  errors.value = {}
  
  if (!form.value.advertiser_id) {
    errors.value.advertiser_id = 'Please select an advertiser'
  }
  
  if (!form.value.name) {
    errors.value.name = 'Ad name is required'
  }
  
  if (!form.value.position) {
    errors.value.position = 'Please select a position'
  }
  
  if (form.value.price <= 0) {
    errors.value.price = 'Price must be greater than 0'
  }
  
  if (!form.value.dimensions || !/^\d+x\d+$/.test(form.value.dimensions)) {
    errors.value.dimensions = 'Invalid format. Use widthxheight (e.g., 300x250)'
  }
  
  if (!form.value.start_date) {
    errors.value.start_date = 'Start date is required'
  }
  
  if (!form.value.end_date) {
    errors.value.end_date = 'End date is required'
  }
  
  if (form.value.start_date && form.value.end_date && form.value.start_date > form.value.end_date) {
    errors.value.end_date = 'End date must be after start date'
  }
  
  return Object.keys(errors.value).length === 0
}

// File handling
const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files[0]) {
    uploadedFileName.value = target.files[0].name
  }
}

const handleFileDrop = (event: DragEvent) => {
  if (event.dataTransfer?.files && event.dataTransfer.files[0]) {
    uploadedFileName.value = event.dataTransfer.files[0].name
  }
}

// Form submission
const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }
  
  isLoading.value = true
  
  try {
    // Find advertiser name
    const advertiser = advertisers.value.find(a => a.id === form.value.advertiser_id)
    const advertiser_name = advertiser?.name || ''
    
    const adData: IUnifiedAdCreate = {
      ...form.value,
      advertiser_name,
      position: form.value.position as 'top' | 'middle' | 'bottom'
    }
    
    let result: IUnifiedAd | null
    
    if (isEditMode.value && props.ad) {
      // Update existing ad
      result = await updateAd(props.ad.id, adData)
    } else {
      // Create new ad
      result = await createAd(adData)
    }
    
    if (result) {
      emit('success', result)
      emit('close')
    }
  } catch (error) {
    console.error('Error saving ad:', error)
    // Handle error with notification
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
/* Custom styles for date inputs in dark mode */
input[type="date"]::-webkit-calendar-picker-indicator {
  filter: invert(1);
  cursor: pointer;
}
</style>