<template>
  <div class="business-card-uploads-chart">
    <div v-if="loading" class="flex items-center justify-center h-64">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>
    <div v-else-if="!data || data.length === 0" class="flex items-center justify-center h-64 text-gray-500">
      <div class="text-center">
        <Icon name="mdi:chart-line" class="text-4xl mb-2 opacity-50" />
        <p>No upload data available for this period</p>
      </div>
    </div>
    <div v-else class="chart-container">
      <canvas ref="chartCanvas" class="chart-canvas"></canvas>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { Chart, registerables } from 'chart.js'

// Register Chart.js components
Chart.register(...registerables)

const chartCanvas = ref<HTMLCanvasElement | null>(null)
let chartInstance: Chart | null = null

const props = defineProps<{
  data?: Array<{
    date: string
    totalUploads: number
    approved?: number
    pending?: number
    rejected?: number
    label?: string
  }>
  period?: string
  loading?: boolean
  showBreakdown?: boolean
}>()

const createChart = async () => {
  await nextTick()
  
  if (!chartCanvas.value || !props.data || props.data.length === 0) {
    return
  }

  // Destroy existing chart if it exists
  if (chartInstance) {
    chartInstance.destroy()
  }

  const ctx = chartCanvas.value.getContext('2d')
  if (!ctx) return

  // Prepare data for Chart.js
  const labels = props.data.map(item => {
    if (item.label) return item.label
    const date = new Date(item.date)
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
  })
  
  const datasets = []
  
  if (props.showBreakdown && props.data.some(item => item.approved !== undefined)) {
    // Show breakdown of approved, pending, rejected
    datasets.push(
      {
        label: 'Approved',
        data: props.data.map(item => item.approved || 0),
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        tension: 0.3,
        fill: false,
        pointRadius: 4,
        pointHoverRadius: 6,
        pointBackgroundColor: 'rgb(34, 197, 94)',
        pointBorderColor: '#fff',
        pointBorderWidth: 2
      },
      {
        label: 'Pending',
        data: props.data.map(item => item.pending || 0),
        borderColor: 'rgb(251, 146, 60)',
        backgroundColor: 'rgba(251, 146, 60, 0.1)',
        tension: 0.3,
        fill: false,
        pointRadius: 4,
        pointHoverRadius: 6,
        pointBackgroundColor: 'rgb(251, 146, 60)',
        pointBorderColor: '#fff',
        pointBorderWidth: 2
      },
      {
        label: 'Rejected',
        data: props.data.map(item => item.rejected || 0),
        borderColor: 'rgb(239, 68, 68)',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        tension: 0.3,
        fill: false,
        pointRadius: 4,
        pointHoverRadius: 6,
        pointBackgroundColor: 'rgb(239, 68, 68)',
        pointBorderColor: '#fff',
        pointBorderWidth: 2
      }
    )
  } else {
    // Show total uploads only
    datasets.push({
      label: 'Total Uploads',
      data: props.data.map(item => item.totalUploads),
      borderColor: 'rgb(59, 130, 246)',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      tension: 0.3,
      fill: true,
      pointRadius: 4,
      pointHoverRadius: 6,
      pointBackgroundColor: 'rgb(59, 130, 246)',
      pointBorderColor: '#fff',
      pointBorderWidth: 2
    })
  }

  chartInstance = new Chart(ctx, {
    type: 'line',
    data: {
      labels,
      datasets
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: props.showBreakdown,
          position: 'bottom',
          labels: {
            padding: 15,
            usePointStyle: true,
            pointStyle: 'circle',
            font: {
              size: 12
            }
          }
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          padding: 12,
          cornerRadius: 8,
          titleFont: {
            size: 14,
            weight: 'normal'
          },
          bodyFont: {
            size: 16,
            weight: 'bold'
          },
          callbacks: {
            label: (context: any) => {
              const label = context.dataset.label || ''
              const value = context.parsed.y || 0
              return `${label}: ${value.toLocaleString()}`
            }
          },
          mode: 'index',
          intersect: false
        }
      },
      scales: {
        x: {
          grid: {
            display: false
          },
          ticks: {
            maxRotation: 0,
            autoSkip: true,
            maxTicksLimit: 7
          }
        },
        y: {
          beginAtZero: true,
          grid: {
            color: 'rgba(0, 0, 0, 0.05)'
          },
          ticks: {
            precision: 0,
            callback: function(value: any) {
              return value.toLocaleString()
            }
          }
        }
      },
      interaction: {
        intersect: false,
        mode: 'index'
      }
    }
  })
}

// Watch for data changes
watch(() => props.data, () => {
  if (!props.loading) {
    createChart()
  }
}, { deep: true })

// Watch for period changes
watch(() => props.period, () => {
  if (!props.loading && props.data) {
    createChart()
  }
})

onMounted(() => {
  if (!props.loading && props.data) {
    createChart()
  }
})

// Cleanup
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.destroy()
  }
})
</script>

<style scoped>
.business-card-uploads-chart {
  @apply min-h-[16rem];
}

.chart-container {
  @apply relative h-64;
}

.chart-canvas {
  @apply w-full h-full;
}
</style>