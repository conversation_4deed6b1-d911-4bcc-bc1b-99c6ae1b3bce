<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Business Card Moderation</h2>
        <p class="text-gray-600 dark:text-gray-400">Review and approve business cards for public display</p>
      </div>
      <div class="flex items-center space-x-4">
        <div class="text-sm text-gray-500">
          {{ pendingCards.length }} pending review{{ pendingCards.length !== 1 ? 's' : '' }}
        </div>
        <button
          @click="loadPendingCards"
          :disabled="loading"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
        >
          <Icon v-if="loading" name="mdi:loading" class="animate-spin mr-2" />
          Refresh
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading && pendingCards.length === 0" class="text-center py-12">
      <Icon name="mdi:loading" class="animate-spin text-4xl text-gray-400 mb-4" />
      <p class="text-gray-500">Loading pending business cards...</p>
    </div>

    <!-- Empty State -->
    <div v-else-if="!loading && pendingCards.length === 0" class="text-center py-12">
      <Icon name="mdi:check-circle" class="text-6xl text-green-400 mb-4" />
      <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">All caught up!</h3>
      <p class="text-gray-500">No business cards pending review at the moment.</p>
    </div>

    <!-- Business Cards Grid -->
    <div v-else class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
      <div
        v-for="card in pendingCards"
        :key="card.id"
        class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 overflow-hidden"
      >
        <!-- Card Image -->
        <div class="h-48 bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
          <img
            v-if="card.imageUrl"
            :src="card.imageUrl"
            :alt="`Business card for ${card.name || 'Unknown'}`"
            class="max-h-full max-w-full object-contain"
          />
          <div v-else class="text-center text-gray-400">
            <Icon name="mdi:card-account-details-outline" class="text-4xl mb-2" />
            <p class="text-sm">No image available</p>
          </div>
        </div>

        <!-- Card Details -->
        <div class="p-4">
          <div class="mb-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">
              {{ card.name || `${card.first_name || ''} ${card.last_name || ''}`.trim() || 'Unknown' }}
            </h3>
            <p v-if="card.company" class="text-sm text-gray-600 dark:text-gray-400">{{ card.company }}</p>
            <p v-if="card.position" class="text-sm text-gray-600 dark:text-gray-400">{{ card.position }}</p>
          </div>

          <!-- Contact Information -->
          <div class="space-y-2 mb-4">
            <div v-if="card.email" class="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <Icon name="mdi:email" class="mr-2 text-blue-500" />
              {{ card.email }}
            </div>
            <div v-if="card.phone" class="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <Icon name="mdi:phone" class="mr-2 text-green-500" />
              {{ card.phone }}
            </div>
            <div v-if="card.website" class="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <Icon name="mdi:web" class="mr-2 text-purple-500" />
              <a :href="card.website" target="_blank" class="hover:underline">{{ card.website }}</a>
            </div>
          </div>

          <!-- Metadata -->
          <div class="text-xs text-gray-500 mb-4 space-y-1">
            <div>Submitted: {{ formatDate(card.createdAt) }}</div>
            <div>Source: {{ card.source || 'manual' }}</div>
            <div v-if="card.userId">User ID: {{ card.userId }}</div>
          </div>

          <!-- Action Buttons -->
          <div class="flex space-x-2">
            <button
              @click="openApprovalModal(card, 'approve')"
              :disabled="processing"
              class="flex-1 px-3 py-2 bg-green-600 text-white text-sm rounded hover:bg-green-700 disabled:opacity-50 transition-colors"
            >
              <Icon name="mdi:check" class="mr-1" />
              Approve
            </button>
            <button
              @click="openApprovalModal(card, 'reject')"
              :disabled="processing"
              class="flex-1 px-3 py-2 bg-red-600 text-white text-sm rounded hover:bg-red-700 disabled:opacity-50 transition-colors"
            >
              <Icon name="mdi:close" class="mr-1" />
              Reject
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Approval/Rejection Modal -->
    <div
      v-if="showModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      @click="closeModal"
    >
      <div
        class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4"
        @click.stop
      >
        <h3 class="text-lg font-semibold mb-4">
          {{ modalAction === 'approve' ? 'Approve' : 'Reject' }} Business Card
        </h3>
        
        <div class="mb-4">
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
            {{ modalAction === 'approve' ? 'Approving' : 'Rejecting' }} business card for:
          </p>
          <p class="font-medium">
            {{ selectedCard?.name || `${selectedCard?.first_name || ''} ${selectedCard?.last_name || ''}`.trim() || 'Unknown' }}
          </p>
        </div>

        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Notes (optional)
          </label>
          <textarea
            v-model="moderationNotes"
            rows="3"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            :placeholder="`Add notes about this ${modalAction}...`"
          ></textarea>
        </div>

        <div class="flex space-x-3">
          <button
            @click="closeModal"
            :disabled="processing"
            class="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 transition-colors"
          >
            Cancel
          </button>
          <button
            @click="confirmAction"
            :disabled="processing"
            :class="[
              'flex-1 px-4 py-2 text-white rounded disabled:opacity-50 transition-colors',
              modalAction === 'approve' 
                ? 'bg-green-600 hover:bg-green-700' 
                : 'bg-red-600 hover:bg-red-700'
            ]"
          >
            <Icon v-if="processing" name="mdi:loading" class="animate-spin mr-2" />
            {{ modalAction === 'approve' ? 'Approve' : 'Reject' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useBusinessCards, type BusinessCard } from '~/composables/useBusinessCards'
import { useCurrentUser } from '~/composables/useCurrentUser'

// Composables
const { loadPendingBusinessCards, approveBusinessCard, rejectBusinessCard, businessCards, loading, error } = useBusinessCards()
const { currentUser } = useCurrentUser()

// State
const pendingCards = ref<BusinessCard[]>([])
const processing = ref(false)
const showModal = ref(false)
const selectedCard = ref<BusinessCard | null>(null)
const modalAction = ref<'approve' | 'reject'>('approve')
const moderationNotes = ref('')

// Methods
const loadPendingCards = async () => {
  await loadPendingBusinessCards()
  pendingCards.value = businessCards.value
}

const formatDate = (date: Date | string) => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(dateObj)
}

const openApprovalModal = (card: BusinessCard, action: 'approve' | 'reject') => {
  selectedCard.value = card
  modalAction.value = action
  moderationNotes.value = ''
  showModal.value = true
}

const closeModal = () => {
  showModal.value = false
  selectedCard.value = null
  moderationNotes.value = ''
}

const confirmAction = async () => {
  if (!selectedCard.value || !currentUser.value?.uid) return

  processing.value = true
  
  try {
    let success = false
    
    if (modalAction.value === 'approve') {
      success = await approveBusinessCard(
        selectedCard.value.id,
        currentUser.value.uid,
        moderationNotes.value || undefined
      )
    } else {
      success = await rejectBusinessCard(
        selectedCard.value.id,
        currentUser.value.uid,
        moderationNotes.value || undefined
      )
    }

    if (success) {
      // Remove the card from pending list
      pendingCards.value = pendingCards.value.filter(card => card.id !== selectedCard.value?.id)
      closeModal()
      
      // Show success message (you can implement a toast notification here)
      console.log(`Business card ${modalAction.value}d successfully`)
    } else {
      console.error(`Failed to ${modalAction.value} business card`)
    }
  } catch (err) {
    console.error(`Error ${modalAction.value}ing business card:`, err)
  } finally {
    processing.value = false
  }
}

// Load pending cards on mount
onMounted(() => {
  loadPendingCards()
})
</script>
