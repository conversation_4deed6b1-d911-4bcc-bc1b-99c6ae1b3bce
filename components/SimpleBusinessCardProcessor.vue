<template>
  <div class="simple-processor">
    <h3>Quick Business Card Scan</h3>
    
    <!-- Upload Area -->
    <div 
      class="upload-zone"
      :class="{ processing: isProcessing, success: hasResult }"
      @click="triggerUpload"
    >
      <input
        ref="fileInput"
        type="file"
        accept="image/*"
        @change="handleFileSelect"
        style="display: none"
      />
      
      <div v-if="!isProcessing && !hasResult" class="upload-prompt">
        <Icon name="material-symbols:add-a-photo" class="upload-icon" />
        <p>Click to upload business card</p>
      </div>
      
      <div v-if="isProcessing" class="processing-state">
        <div class="spinner" />
        <p>Processing... {{ progress }}%</p>
      </div>
      
      <div v-if="hasResult" class="success-state">
        <Icon name="material-symbols:check-circle" class="success-icon" />
        <p>Card processed successfully!</p>
        <button @click="reset" class="reset-button">Scan Another</button>
      </div>
    </div>
    
    <!-- Error Display -->
    <div v-if="error" class="error-display">
      {{ error }}
      <button @click="clearError" class="error-close">×</button>
    </div>
    
    <!-- Extracted Data Display -->
    <div v-if="extractedData" class="extracted-data">
      <h4>Extracted Information:</h4>
      <div class="data-grid">
        <div v-if="extractedData.fullName" class="data-item">
          <strong>Name:</strong> {{ extractedData.fullName }}
        </div>
        <div v-if="extractedData.company" class="data-item">
          <strong>Company:</strong> {{ extractedData.company }}
        </div>
        <div v-if="extractedData.jobTitle" class="data-item">
          <strong>Title:</strong> {{ extractedData.jobTitle }}
        </div>
        <div v-if="extractedData.email" class="data-item">
          <strong>Email:</strong> {{ extractedData.email }}
        </div>
        <div v-if="extractedData.phone" class="data-item">
          <strong>Phone:</strong> {{ extractedData.phone }}
        </div>
        <div v-if="extractedData.website" class="data-item">
          <strong>Website:</strong> {{ extractedData.website }}
        </div>
      </div>
      
      <!-- Action Buttons -->
      <div class="actions">
        <button @click="copyData" class="action-btn">
          <Icon name="material-symbols:content-copy" />
          Copy Data
        </button>
        <button @click="downloadVCard" class="action-btn">
          <Icon name="material-symbols:download" />
          Download vCard
        </button>
        <button @click="$emit('dataExtracted', extractedData)" class="action-btn primary">
          <Icon name="material-symbols:send" />
          Use Data
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useBusinessCardProcessor } from '~/composables/useBusinessCardProcessor'

// Emits
const emit = defineEmits<{
  dataExtracted: [data: any]
  error: [error: string]
}>()

// Business card processor
const {
  isProcessing,
  progress,
  error,
  hasResult,
  extractedData,
  processBusinessCard,
  reset,
  clearError,
  downloadVCard
} = useBusinessCardProcessor()

// Component state
const fileInput = ref<HTMLInputElement>()

// Methods
const triggerUpload = () => {
  if (!isProcessing.value) {
    fileInput.value?.click()
  }
}

const handleFileSelect = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (file) {
    try {
      await processBusinessCard(file)
      if (extractedData.value) {
        emit('dataExtracted', extractedData.value)
      }
    } catch (err: any) {
      emit('error', err.message)
    }
  }
}

const copyData = async () => {
  if (!extractedData.value) return
  
  const data = {
    name: extractedData.value.fullName,
    company: extractedData.value.company,
    title: extractedData.value.jobTitle,
    email: extractedData.value.email,
    phone: extractedData.value.phone,
    website: extractedData.value.website
  }
  
  try {
    await navigator.clipboard.writeText(JSON.stringify(data, null, 2))
    // Could show a toast notification here
  } catch (err) {
    console.error('Failed to copy data:', err)
  }
}
</script>

<style scoped>
.simple-processor {
  max-width: 500px;
  margin: 0 auto;
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  background: white;
}

.simple-processor h3 {
  text-align: center;
  margin-bottom: 1.5rem;
  color: #1f2937;
}

.upload-zone {
  border: 2px dashed #d1d5db;
  border-radius: 0.5rem;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 1rem;
}

.upload-zone:hover {
  border-color: #3b82f6;
  background-color: #f8fafc;
}

.upload-zone.processing {
  border-color: #f59e0b;
  background-color: #fffbeb;
  cursor: not-allowed;
}

.upload-zone.success {
  border-color: #10b981;
  background-color: #f0fdf4;
}

.upload-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.upload-icon {
  font-size: 2rem;
  color: #9ca3af;
}

.processing-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.spinner {
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.success-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.success-icon {
  font-size: 2rem;
  color: #10b981;
}

.reset-button {
  padding: 0.5rem 1rem;
  background: #6b7280;
  color: white;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
  font-size: 0.875rem;
}

.reset-button:hover {
  background: #4b5563;
}

.error-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.375rem;
  color: #dc2626;
  margin-bottom: 1rem;
  font-size: 0.875rem;
}

.error-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: #dc2626;
}

.extracted-data {
  margin-top: 1.5rem;
  padding: 1.5rem;
  background: #f9fafb;
  border-radius: 0.5rem;
}

.extracted-data h4 {
  margin-bottom: 1rem;
  color: #374151;
}

.data-grid {
  display: grid;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.data-item {
  display: flex;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.data-item strong {
  min-width: 80px;
  color: #374151;
}

.actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  background: white;
  color: #374151;
  cursor: pointer;
  font-size: 0.75rem;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #f9fafb;
}

.action-btn.primary {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.action-btn.primary:hover {
  background: #2563eb;
}
</style>
