<script setup lang="ts">
import { arrayRemove, arrayUnion, doc, increment, updateDoc } from 'firebase/firestore'
import type { Firestore } from 'firebase/firestore'
const { firestore } = useFirebase();
const uiButtonLike = defineProps({
  isLiked: {
    type: Boolean,
    default: false,
  },
  collection: {
    type: String,
    default: '',
  },
  id: {
    type: String,
    default: '',
  },
})
const { currentSpace: currentSpaceLikeButton } = space()
const { meUser: currentUserLikeButton } = useMe()
const likeEmit: any = getCurrentInstance()
async function heartclicked() {
  if (!firestore) {
    console.error('Firestore not initialized')
    return
  }

  const incrementRef = doc(firestore as Firestore, uiButtonLike.collection, uiButtonLike.id)
  const arrayRef = doc(firestore as Firestore, 'Likes', uiButtonLike.collection, currentUserLikeButton.value.id, currentSpaceLikeButton.value.id)

  if (uiButtonLike.isLiked) {
    await updateDoc(incrementRef, {
      likes: increment(1),
    })

    await updateDoc(arrayRef, {
      [uiButtonLike.collection]: arrayRemove(uiButtonLike.id),
    })
  }

  else {
    await updateDoc(incrementRef, {
      likes: increment(-1),
    })
    await updateDoc(arrayRef, {
      [uiButtonLike.collection]: arrayUnion(uiButtonLike.id),
    })
  }
  likeEmit.emit('update-like', uiButtonLike.isLiked)
}
</script>

<template>
  <div class="o_btn_icon_square" @click="heartclicked">
    <div v-if="isLiked" class="i-mdi-heart" />
    <div v-else class="i-mdi-heart-outline" />
  </div>
</template>
