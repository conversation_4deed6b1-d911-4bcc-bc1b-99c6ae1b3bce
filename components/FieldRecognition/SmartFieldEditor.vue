<template>
  <div class="smart-field-editor">
    <!-- Header with Language Detection -->
    <div class="editor-header bg-white rounded-lg shadow-sm border p-4 mb-4">
      <div class="flex justify-between items-center">
        <div class="flex items-center space-x-3">
          <Icon name="brain" class="w-5 h-5 text-blue-500" />
          <h3 class="text-lg font-semibold text-gray-900">Smart Field Recognition</h3>
          <span v-if="overallConfidence" 
                :class="confidenceColorClass(overallConfidence)"
                class="px-2 py-1 rounded-full text-xs font-medium">
            {{ Math.round(overallConfidence * 100) }}% confidence
          </span>
        </div>
        
        <div class="flex items-center space-x-2">
          <LanguageSelector 
            :detected-language="detectedLanguage"
            :confidence="languageConfidence"
            @language-changed="handleLanguageChange"
          />
          
          <button 
            @click="toggleLearningMode"
            :class="[
              'px-3 py-1 rounded text-sm font-medium transition-colors',
              isLearning ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'
            ]">
            <Icon :name="isLearning ? 'academic-cap' : 'academic-cap-off'" class="w-4 h-4 mr-1" />
            {{ isLearning ? 'Learning On' : 'Learning Off' }}
          </button>
        </div>
      </div>
      
      <!-- Processing Status -->
      <div v-if="isProcessing" class="mt-3 flex items-center space-x-2 text-sm text-gray-600">
        <Icon name="spinner" class="w-4 h-4 animate-spin" />
        <span>Processing fields with enhanced recognition...</span>
      </div>
    </div>

    <!-- Field Categories -->
    <div class="space-y-4">
      <!-- Primary Fields -->
      <FieldCategory
        title="Primary Information"
        icon="user"
        :fields="primaryFields"
        @field-updated="handleFieldUpdate"
        @field-corrected="handleFieldCorrection"
        :suggestions="suggestions.filter(s => ['name', 'email', 'phone'].includes(s.field_type))"
      />

      <!-- Professional Fields -->
      <FieldCategory
        title="Professional Details"
        icon="briefcase"
        :fields="professionalFields"
        @field-updated="handleFieldUpdate"
        @field-corrected="handleFieldCorrection"
        :suggestions="suggestions.filter(s => ['company', 'title'].includes(s.field_type))"
      />

      <!-- Contact Fields -->
      <FieldCategory
        title="Contact Information"
        icon="globe"
        :fields="contactFields"
        @field-updated="handleFieldUpdate"
        @field-corrected="handleFieldCorrection"
        :suggestions="suggestions.filter(s => ['website', 'address'].includes(s.field_type))"
      />

      <!-- Address Fields -->
      <AddressEditor
        v-if="addressFields.length > 0"
        :address-fields="addressFields"
        :address-format="detectedAddressFormat"
        @address-updated="handleAddressUpdate"
        @field-corrected="handleFieldCorrection"
      />

      <!-- Custom Fields -->
      <FieldCategory
        v-if="customFields.length > 0"
        title="Custom Fields"
        icon="tag"
        :fields="customFields"
        @field-updated="handleFieldUpdate"
        @field-corrected="handleFieldCorrection"
        :suggestions="suggestions.filter(s => s.field_type === 'custom')"
      />
    </div>

    <!-- Field Suggestions Panel -->
    <FieldSuggestions
      v-if="suggestions.length > 0"
      :suggestions="suggestions"
      @suggestion-applied="handleSuggestionApplied"
      @suggestion-dismissed="handleSuggestionDismissed"
    />

    <!-- Custom Field Mapping Rules -->
    <FieldMappingRules
      v-if="showCustomRules"
      :custom-rules="customRules"
      @rule-added="handleRuleAdded"
      @rule-updated="handleRuleUpdated"
      @rule-removed="handleRuleRemoved"
    />

    <!-- Action Buttons -->
    <div class="flex justify-between items-center mt-6 pt-4 border-t">
      <div class="flex space-x-2">
        <button
          @click="showCustomRules = !showCustomRules"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
          <Icon name="cog" class="w-4 h-4 mr-2" />
          Custom Rules ({{ customRuleCount }})
        </button>
        
        <button
          @click="resetAllFields"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
          <Icon name="refresh" class="w-4 h-4 mr-2" />
          Reset
        </button>
      </div>
      
      <div class="flex space-x-2">
        <button
          @click="saveFields"
          :disabled="!hasChanges"
          class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed">
          <Icon name="check" class="w-4 h-4 mr-2" />
          Save Changes
        </button>
      </div>
    </div>

    <!-- Performance Metrics -->
    <div v-if="showMetrics" class="mt-4 p-4 bg-gray-50 rounded-lg">
      <h4 class="text-sm font-medium text-gray-900 mb-2">Recognition Performance</h4>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        <div>
          <span class="text-gray-600">Processing Time:</span>
          <span class="font-medium ml-1">{{ processingTime }}ms</span>
        </div>
        <div>
          <span class="text-gray-600">Fields Detected:</span>
          <span class="font-medium ml-1">{{ totalFieldsDetected }}</span>
        </div>
        <div>
          <span class="text-gray-600">Accuracy:</span>
          <span class="font-medium ml-1">{{ Math.round(userAccuracy * 100) }}%</span>
        </div>
        <div>
          <span class="text-gray-600">Improvement:</span>
          <span :class="improvementRate >= 0 ? 'text-green-600' : 'text-red-600'" class="font-medium ml-1">
            {{ improvementRate >= 0 ? '+' : '' }}{{ Math.round(improvementRate * 100) }}%
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useFieldRecognition } from '~/composables/useFieldRecognition'
import { useFieldMapping } from '~/composables/useFieldMapping'
import { useAuth } from '~/composables/useAuth'
import type { 
  EnhancedOCRResult, 
  FieldClassification, 
  FieldCorrection,
  FieldSuggestion,
  FieldMappingRule
} from '~/types/field-recognition'

// Props
interface Props {
  ocrResult: EnhancedOCRResult | null
  showMetrics?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showMetrics: false
})

// Emits
const emit = defineEmits<{
  fieldsUpdated: [result: EnhancedOCRResult]
  fieldCorrected: [correction: FieldCorrection]
  processingComplete: [result: EnhancedOCRResult]
}>()

// Composables
const { isProcessing, recognizeFields } = useFieldRecognition()
const { 
  userPreferences, 
  customRules, 
  isLearning, 
  customRuleCount,
  overallAccuracy: userAccuracy,
  recentImprovementRate: improvementRate,
  initializeFieldMapping,
  applyFieldMapping,
  learnFromCorrection,
  generateFieldSuggestions,
  addOrUpdateCustomRule,
  removeCustomRule
} = useFieldMapping()
const { currentUser } = useAuth()

// Local state
const showCustomRules = ref(false)
const hasChanges = ref(false)
const suggestions = ref<FieldSuggestion[]>([])
const processedResult = ref<EnhancedOCRResult | null>(null)

// Reactive field collections
const primaryFields = computed(() => {
  if (!processedResult.value) return []
  return [
    ...processedResult.value.name,
    ...processedResult.value.email,
    ...processedResult.value.phone
  ]
})

const professionalFields = computed(() => {
  if (!processedResult.value) return []
  return [
    ...processedResult.value.company,
    ...processedResult.value.title
  ]
})

const contactFields = computed(() => {
  if (!processedResult.value) return []
  return [
    ...processedResult.value.website
  ]
})

const addressFields = computed(() => {
  if (!processedResult.value) return []
  return [
    ...processedResult.value.address.street,
    ...processedResult.value.address.city,
    ...processedResult.value.address.state,
    ...processedResult.value.address.postal_code
  ]
})

const customFields = computed(() => {
  return processedResult.value?.custom_fields || []
})

// Computed properties from OCR result
const detectedLanguage = computed(() => props.ocrResult?.language_detected || 'en')
const languageConfidence = computed(() => 0.8) // Would come from language detection
const overallConfidence = computed(() => processedResult.value?.overall_confidence || 0)
const detectedAddressFormat = computed(() => processedResult.value?.address.format_detected || 'us')
const processingTime = computed(() => processedResult.value?.processing_time || 0)
const totalFieldsDetected = computed(() => {
  if (!processedResult.value) return 0
  return primaryFields.value.length + professionalFields.value.length + 
         contactFields.value.length + addressFields.value.length + customFields.value.length
})

// Initialize field mapping when user changes
watch(() => currentUser.value?.uid, async (userId) => {
  if (userId) {
    await initializeFieldMapping(userId)
  }
})

// Process OCR result when it changes
watch(() => props.ocrResult, async (newResult) => {
  if (newResult && currentUser.value?.uid) {
    await processOCRResult(newResult)
  }
})

onMounted(async () => {
  if (currentUser.value?.uid) {
    await initializeFieldMapping(currentUser.value.uid)
    
    if (props.ocrResult) {
      await processOCRResult(props.ocrResult)
    }
  }
})

/**
 * Process OCR result with field mapping and generate suggestions
 */
const processOCRResult = async (result: EnhancedOCRResult) => {
  try {
    // Apply field mapping rules
    const enhancedResult = await applyFieldMapping(result, currentUser.value?.uid)
    processedResult.value = enhancedResult
    
    // Generate suggestions
    suggestions.value = generateFieldSuggestions(enhancedResult)
    
    emit('processingComplete', enhancedResult)
  } catch (error) {
    console.error('Error processing OCR result:', error)
    processedResult.value = result
  }
}

/**
 * Handle field updates
 */
const handleFieldUpdate = (field: FieldClassification, newValue: string) => {
  if (!processedResult.value) return
  
  // Update the field value
  field.value = newValue
  hasChanges.value = true
  
  emit('fieldsUpdated', processedResult.value)
}

/**
 * Handle field corrections (learning opportunity)
 */
const handleFieldCorrection = async (
  originalField: FieldClassification, 
  correctedValue: string, 
  correctedFieldType: string
) => {
  if (!processedResult.value || !isLearning.value) return
  
  const correction: FieldCorrection = {
    original_classification: originalField,
    corrected_value: correctedValue,
    corrected_field_type: correctedFieldType,
    timestamp: new Date(),
    card_language: processedResult.value.language_detected,
    card_layout: processedResult.value.layout_detected
  }
  
  await learnFromCorrection(correction)
  emit('fieldCorrected', correction)
  
  // Regenerate suggestions after learning
  suggestions.value = generateFieldSuggestions(processedResult.value)
}

/**
 * Handle address updates
 */
const handleAddressUpdate = (addressType: string, newValue: string) => {
  if (!processedResult.value) return
  
  // Update the appropriate address field
  const addressField = processedResult.value.address[addressType as keyof typeof processedResult.value.address]
  if (Array.isArray(addressField) && addressField.length > 0) {
    addressField[0].value = newValue
    hasChanges.value = true
    
    emit('fieldsUpdated', processedResult.value)
  }
}

/**
 * Handle suggestion application
 */
const handleSuggestionApplied = async (suggestion: FieldSuggestion) => {
  if (!processedResult.value) return
  
  // Find and update the relevant field
  const allFields = [
    ...primaryFields.value,
    ...professionalFields.value,
    ...contactFields.value,
    ...addressFields.value,
    ...customFields.value
  ]
  
  const targetField = allFields.find(f => f.fieldType === suggestion.field_type)
  if (targetField) {
    await handleFieldCorrection(targetField, suggestion.suggested_value, suggestion.field_type)
  }
  
  // Remove applied suggestion
  suggestions.value = suggestions.value.filter(s => s !== suggestion)
}

/**
 * Handle suggestion dismissal
 */
const handleSuggestionDismissed = (suggestion: FieldSuggestion) => {
  suggestions.value = suggestions.value.filter(s => s !== suggestion)
}

/**
 * Handle custom rule management
 */
const handleRuleAdded = async (rule: FieldMappingRule) => {
  await addOrUpdateCustomRule(rule)
}

const handleRuleUpdated = async (rule: FieldMappingRule) => {
  await addOrUpdateCustomRule(rule)
}

const handleRuleRemoved = async (ruleId: string) => {
  await removeCustomRule(ruleId)
}

/**
 * Toggle learning mode
 */
const toggleLearningMode = () => {
  isLearning.value = !isLearning.value
}

/**
 * Handle language change
 */
const handleLanguageChange = async (newLanguage: string) => {
  if (props.ocrResult) {
    // Re-process with new language
    const updatedResult = { ...props.ocrResult, language_detected: newLanguage }
    await processOCRResult(updatedResult)
  }
}

/**
 * Reset all fields to original values
 */
const resetAllFields = () => {
  if (props.ocrResult) {
    processedResult.value = { ...props.ocrResult }
    hasChanges.value = false
    suggestions.value = generateFieldSuggestions(props.ocrResult)
  }
}

/**
 * Save field changes
 */
const saveFields = () => {
  if (processedResult.value) {
    emit('fieldsUpdated', processedResult.value)
    hasChanges.value = false
  }
}

/**
 * Get confidence color class
 */
const confidenceColorClass = (confidence: number) => {
  if (confidence >= 0.8) return 'bg-green-100 text-green-800'
  if (confidence >= 0.6) return 'bg-yellow-100 text-yellow-800'
  return 'bg-red-100 text-red-800'
}
</script>

<style scoped>
.smart-field-editor {
  @apply max-w-4xl mx-auto;
}

.field-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.confidence-indicator {
  @apply flex items-center space-x-1 text-xs;
}

.confidence-bar {
  @apply w-12 h-2 bg-gray-200 rounded-full overflow-hidden;
}

.confidence-fill {
  @apply h-full transition-all duration-300;
}
</style>