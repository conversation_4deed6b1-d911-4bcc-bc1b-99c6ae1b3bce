<template>
  <div :class="containerClass">
    <!-- Confidence Bar -->
    <div :class="barClass">
      <div 
        :class="fillClass"
        :style="{ width: `${confidence * 100}%` }">
      </div>
    </div>
    
    <!-- Confidence Percentage -->
    <span v-if="showPercentage" :class="textClass">
      {{ Math.round(confidence * 100) }}%
    </span>
    
    <!-- Confidence Icon -->
    <Icon 
      v-if="showIcon"
      :name="confidenceIcon"
      :class="iconClass"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// Props
interface Props {
  confidence: number
  size?: 'sm' | 'md' | 'lg'
  showPercentage?: boolean
  showIcon?: boolean
  variant?: 'bar' | 'badge' | 'minimal'
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  showPercentage: true,
  showIcon: false,
  variant: 'bar'
})

// Computed classes
const containerClass = computed(() => {
  const base = 'flex items-center'
  const sizeClasses = {
    sm: 'space-x-1',
    md: 'space-x-2', 
    lg: 'space-x-3'
  }
  
  return `${base} ${sizeClasses[props.size]}`
})

const barClass = computed(() => {
  const base = 'bg-gray-200 rounded-full overflow-hidden'
  const sizeClasses = {
    sm: 'w-8 h-1',
    md: 'w-12 h-2',
    lg: 'w-16 h-3'
  }
  
  if (props.variant === 'minimal') {
    return `${base} ${sizeClasses.sm}`
  }
  
  return `${base} ${sizeClasses[props.size]}`
})

const fillClass = computed(() => {
  const base = 'h-full transition-all duration-300'
  
  if (props.confidence >= 0.8) {
    return `${base} bg-green-500`
  } else if (props.confidence >= 0.6) {
    return `${base} bg-yellow-500`
  } else if (props.confidence >= 0.4) {
    return `${base} bg-orange-500`
  } else {
    return `${base} bg-red-500`
  }
})

const textClass = computed(() => {
  const base = 'font-medium'
  const sizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  }
  
  const colorClass = props.confidence >= 0.7 ? 'text-green-700' : 
                     props.confidence >= 0.5 ? 'text-yellow-700' : 'text-red-700'
  
  return `${base} ${sizeClasses[props.size]} ${colorClass}`
})

const iconClass = computed(() => {
  const sizeClasses = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5'
  }
  
  const colorClass = props.confidence >= 0.7 ? 'text-green-500' : 
                     props.confidence >= 0.5 ? 'text-yellow-500' : 'text-red-500'
  
  return `${sizeClasses[props.size]} ${colorClass}`
})

const confidenceIcon = computed(() => {
  if (props.confidence >= 0.8) return 'check-circle'
  if (props.confidence >= 0.6) return 'exclamation-circle'
  if (props.confidence >= 0.4) return 'question-mark-circle'
  return 'x-circle'
})
</script>