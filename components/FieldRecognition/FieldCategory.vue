<template>
  <div class="field-category bg-white rounded-lg shadow-sm border">
    <!-- Category Header -->
    <div class="px-4 py-3 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-2">
          <Icon :name="icon" class="w-5 h-5 text-gray-600" />
          <h3 class="text-lg font-medium text-gray-900">{{ title }}</h3>
          <span class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-600 rounded-full">
            {{ fields.length }} field{{ fields.length !== 1 ? 's' : '' }}
          </span>
        </div>
        
        <button 
          @click="collapsed = !collapsed"
          class="p-1 rounded hover:bg-gray-100">
          <Icon :name="collapsed ? 'chevron-down' : 'chevron-up'" class="w-4 h-4 text-gray-500" />
        </button>
      </div>
    </div>

    <!-- Fields List -->
    <Transition name="collapse">
      <div v-show="!collapsed" class="p-4 space-y-3">
        <div v-if="fields.length === 0" class="text-center py-8 text-gray-500">
          <Icon name="inbox" class="w-8 h-8 mx-auto mb-2 text-gray-400" />
          <p class="text-sm">No {{ title.toLowerCase() }} detected</p>
          <button 
            @click="$emit('add-custom-field')"
            class="mt-2 text-sm text-blue-600 hover:text-blue-800">
            Add manually
          </button>
        </div>

        <FieldEditor
          v-for="(field, index) in fields"
          :key="`${field.fieldType}-${index}`"
          :field="field"
          :suggestions="getSuggestionsForField(field)"
          @field-updated="$emit('field-updated', field, $event)"
          @field-corrected="$emit('field-corrected', field, $event.value, $event.type)"
          @field-removed="removeField(index)"
        />

        <!-- Add Field Button -->
        <button
          v-if="canAddFields"
          @click="showAddDialog = true"
          class="w-full py-2 px-3 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-gray-400 hover:text-gray-800 transition-colors">
          <Icon name="plus" class="w-4 h-4 mr-2" />
          Add {{ getSingularFieldType() }}
        </button>
      </div>
    </Transition>

    <!-- Add Field Dialog -->
    <AddFieldDialog
      v-if="showAddDialog"
      :field-type="getDefaultFieldType()"
      @field-added="handleFieldAdded"
      @dialog-closed="showAddDialog = false"
    />

    <!-- Category Suggestions -->
    <div v-if="categorySuggestions.length > 0 && !collapsed" class="px-4 pb-4">
      <div class="border-t pt-3">
        <h4 class="text-sm font-medium text-gray-700 mb-2">Suggestions</h4>
        <div class="space-y-2">
          <div
            v-for="suggestion in categorySuggestions"
            :key="suggestion.field_type"
            class="flex items-center justify-between p-2 bg-blue-50 rounded-lg">
            <div class="flex-1">
              <div class="text-sm font-medium text-blue-900">
                {{ suggestion.suggested_value }}
              </div>
              <div class="text-xs text-blue-700">
                {{ suggestion.reasoning }}
              </div>
            </div>
            <div class="flex items-center space-x-2 ml-3">
              <FieldConfidenceIndicator :confidence="suggestion.confidence" size="sm" />
              <button
                @click="applySuggestion(suggestion)"
                class="px-2 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded hover:bg-blue-200">
                Apply
              </button>
              <button
                @click="dismissSuggestion(suggestion)"
                class="p-1 text-blue-400 hover:text-blue-600">
                <Icon name="x" class="w-3 h-3" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { FieldClassification, FieldSuggestion } from '~/types/field-recognition'

// Props
interface Props {
  title: string
  icon: string
  fields: FieldClassification[]
  suggestions?: FieldSuggestion[]
  canAddFields?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  suggestions: () => [],
  canAddFields: true
})

// Emits
const emit = defineEmits<{
  fieldUpdated: [field: FieldClassification, value: string]
  fieldCorrected: [field: FieldClassification, correction: { value: string; type: string }]
  addCustomField: []
  suggestionApplied: [suggestion: FieldSuggestion]
  suggestionDismissed: [suggestion: FieldSuggestion]
}>()

// Local state
const collapsed = ref(false)
const showAddDialog = ref(false)

// Computed
const categorySuggestions = computed(() => {
  return props.suggestions.filter(suggestion => 
    getFieldTypesForCategory().includes(suggestion.field_type)
  )
})

/**
 * Get field types that belong to this category
 */
const getFieldTypesForCategory = (): string[] => {
  const categoryMap: Record<string, string[]> = {
    'Primary Information': ['name', 'email', 'phone'],
    'Professional Details': ['company', 'title'],
    'Contact Information': ['website'],
    'Custom Fields': ['custom']
  }
  
  return categoryMap[props.title] || []
}

/**
 * Get default field type for adding new fields
 */
const getDefaultFieldType = (): string => {
  const types = getFieldTypesForCategory()
  return types[0] || 'custom'
}

/**
 * Get singular field type name for UI
 */
const getSingularFieldType = (): string => {
  const typeMap: Record<string, string> = {
    'Primary Information': 'Contact',
    'Professional Details': 'Detail',
    'Contact Information': 'Contact',
    'Custom Fields': 'Field'
  }
  
  return typeMap[props.title] || 'Field'
}

/**
 * Get suggestions for specific field
 */
const getSuggestionsForField = (field: FieldClassification): FieldSuggestion[] => {
  return props.suggestions.filter(s => s.field_type === field.fieldType)
}

/**
 * Remove field from list
 */
const removeField = (index: number) => {
  // Emit removal event to parent
  // Parent should handle actual removal from data
}

/**
 * Handle new field addition
 */
const handleFieldAdded = (newField: FieldClassification) => {
  // Emit to parent for handling
  showAddDialog.value = false
}

/**
 * Apply suggestion
 */
const applySuggestion = (suggestion: FieldSuggestion) => {
  emit('suggestionApplied', suggestion)
}

/**
 * Dismiss suggestion
 */
const dismissSuggestion = (suggestion: FieldSuggestion) => {
  emit('suggestionDismissed', suggestion)
}
</script>

<style scoped>
.collapse-enter-active,
.collapse-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.collapse-enter-from,
.collapse-leave-to {
  opacity: 0;
  max-height: 0;
}

.collapse-enter-to,
.collapse-leave-from {
  opacity: 1;
  max-height: 1000px;
}
</style>