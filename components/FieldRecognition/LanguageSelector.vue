<template>
  <div class="language-selector">
    <div class="flex items-center space-x-2">
      <!-- Language Flag/Icon -->
      <div class="flex items-center space-x-1">
        <span class="text-lg">{{ getLanguageFlag(detectedLanguage) }}</span>
        <span class="text-sm font-medium text-gray-700">
          {{ getLanguageName(detectedLanguage) }}
        </span>
      </div>
      
      <!-- Confidence Indicator -->
      <FieldConfidenceIndicator 
        :confidence="confidence" 
        size="sm" 
        :show-percentage="false"
        :show-icon="true"
        variant="minimal"
      />
      
      <!-- Change Language Button -->
      <Menu as="div" class="relative">
        <MenuButton
          class="flex items-center px-2 py-1 text-sm font-medium text-gray-600 bg-gray-100 rounded hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500">
          <Icon name="globe" class="w-4 h-4 mr-1" />
          Change
          <Icon name="chevron-down" class="w-3 h-3 ml-1" />
        </MenuButton>
        
        <Transition
          enter-active-class="transition duration-100 ease-out"
          enter-from-class="transform scale-95 opacity-0"
          enter-to-class="transform scale-100 opacity-100"
          leave-active-class="transition duration-75 ease-in"
          leave-from-class="transform scale-100 opacity-100"
          leave-to-class="transform scale-95 opacity-0">
          <MenuItems
            class="absolute right-0 z-10 mt-2 w-56 origin-top-right bg-white border border-gray-200 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
            <div class="py-1">
              <div class="px-3 py-2 text-xs font-medium text-gray-500 uppercase tracking-wide border-b">
                Supported Languages
              </div>
              
              <MenuItem
                v-for="language in supportedLanguages"
                :key="language.code"
                v-slot="{ active }"
                @click="selectLanguage(language.code)">
                <button
                  :class="[
                    active ? 'bg-gray-100' : '',
                    detectedLanguage === language.code ? 'bg-blue-50 text-blue-700' : 'text-gray-900',
                    'group flex items-center w-full px-3 py-2 text-sm'
                  ]">
                  <span class="mr-3 text-base">{{ getLanguageFlag(language.code) }}</span>
                  <div class="flex-1 text-left">
                    <div class="font-medium">{{ language.name }}</div>
                    <div class="text-xs text-gray-500">{{ language.code.toUpperCase() }}</div>
                  </div>
                  <Icon 
                    v-if="detectedLanguage === language.code"
                    name="check"
                    class="w-4 h-4 text-blue-600"
                  />
                </button>
              </MenuItem>
              
              <!-- Auto-detect option -->
              <div class="border-t">
                <MenuItem v-slot="{ active }" @click="enableAutoDetect">
                  <button
                    :class="[
                      active ? 'bg-gray-100' : '',
                      autoDetect ? 'bg-blue-50 text-blue-700' : 'text-gray-900',
                      'group flex items-center w-full px-3 py-2 text-sm'
                    ]">
                    <Icon name="sparkles" class="w-4 h-4 mr-3 text-gray-400" />
                    <div class="flex-1 text-left">
                      <div class="font-medium">Auto-detect</div>
                      <div class="text-xs text-gray-500">Automatically detect language</div>
                    </div>
                    <Icon 
                      v-if="autoDetect"
                      name="check"
                      class="w-4 h-4 text-blue-600"
                    />
                  </button>
                </MenuItem>
              </div>
            </div>
          </MenuItems>
        </Transition>
      </Menu>
    </div>
    
    <!-- Low Confidence Warning -->
    <div v-if="confidence < 0.6" class="mt-2 flex items-center space-x-2 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
      <Icon name="exclamation-triangle" class="w-4 h-4 text-yellow-600" />
      <div class="text-sm">
        <span class="font-medium text-yellow-800">Low confidence language detection.</span>
        <span class="text-yellow-700">Consider manually selecting the correct language for better accuracy.</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Menu, MenuButton, MenuItems, MenuItem } from '@headlessui/vue'
import { useLanguageDetection } from '~/composables/useLanguageDetection'

// Props
interface Props {
  detectedLanguage: string
  confidence: number
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  languageChanged: [language: string]
}>()

// Composables
const { getSupportedLanguages } = useLanguageDetection()

// Local state
const autoDetect = ref(true)

// Data
const supportedLanguages = getSupportedLanguages()

// Language flags/icons mapping
const languageFlags: Record<string, string> = {
  'en': '🇺🇸',
  'es': '🇪🇸', 
  'fr': '🇫🇷',
  'de': '🇩🇪',
  'zh': '🇨🇳',
  'ja': '🇯🇵',
  'ko': '🇰🇷',
  'it': '🇮🇹',
  'pt': '🇵🇹',
  'ru': '🇷🇺',
  'ar': '🇸🇦',
  'hi': '🇮🇳'
}

/**
 * Get language flag emoji
 */
const getLanguageFlag = (langCode: string): string => {
  return languageFlags[langCode] || '🌐'
}

/**
 * Get language display name
 */
const getLanguageName = (langCode: string): string => {
  const language = supportedLanguages.find(l => l.code === langCode)
  return language?.name || langCode.toUpperCase()
}

/**
 * Select a specific language
 */
const selectLanguage = (langCode: string) => {
  autoDetect.value = false
  emit('languageChanged', langCode)
}

/**
 * Enable auto-detection
 */
const enableAutoDetect = () => {
  autoDetect.value = true
  // Could trigger re-detection here
}
</script>

<style scoped>
.language-selector {
  @apply text-sm;
}
</style>