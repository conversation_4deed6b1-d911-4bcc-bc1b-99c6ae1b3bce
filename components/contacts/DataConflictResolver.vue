<template>
  <div class="conflict-resolver">
    <!-- Header -->
    <div class="mb-6">
      <h2 class="text-xl font-semibold text-white mb-2">Resolve Data Conflicts</h2>
      <p class="text-gray-400">
        Some information differs between the contact and business card. 
        Choose which values to keep for each field.
      </p>
    </div>

    <!-- Conflict Resolution Form -->
    <div class="space-y-6">
      <div
        v-for="conflict in conflicts"
        :key="conflict.field"
        class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-xl p-6 border border-white/10"
      >
        <!-- Field Header -->
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center">
            <Icon :name="getFieldIcon(conflict.field)" class="w-5 h-5 text-blue-400 mr-2" />
            <h3 class="text-lg font-medium text-white">{{ getFieldLabel(conflict.field) }}</h3>
          </div>
          <span class="text-xs text-gray-400 bg-yellow-100/10 text-yellow-400 px-2 py-1 rounded-full">
            Conflict
          </span>
        </div>

        <!-- Comparison Options -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Current Contact Value -->
          <div
            :class="[
              'conflict-option p-4 rounded-lg border-2 cursor-pointer transition-all duration-200',
              resolutions[conflict.field] === 'contact' 
                ? 'border-blue-500 bg-blue-500/10' 
                : 'border-gray-600 bg-gray-800/30 hover:border-gray-500'
            ]"
            @click="selectResolution(conflict.field, 'contact')"
          >
            <div class="flex items-start justify-between mb-2">
              <div class="flex items-center">
                <Icon name="mdi:account" class="w-4 h-4 text-green-400 mr-2" />
                <span class="text-sm font-medium text-white">Current Contact</span>
              </div>
              <div
                v-if="resolutions[conflict.field] === 'contact'"
                class="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center"
              >
                <Icon name="mdi:check" class="w-3 h-3 text-white" />
              </div>
            </div>
            
            <div class="space-y-1">
              <div
                v-if="conflict.field === 'address'"
                class="text-gray-300 text-sm"
              >
                <div v-if="conflict.contactValue?.street">{{ conflict.contactValue.street }}</div>
                <div v-if="conflict.contactValue?.city || conflict.contactValue?.state">
                  {{ [conflict.contactValue?.city, conflict.contactValue?.state].filter(Boolean).join(', ') }}
                </div>
                <div v-if="conflict.contactValue?.country">{{ conflict.contactValue.country }}</div>
                <div v-if="conflict.contactValue?.postalCode">{{ conflict.contactValue.postalCode }}</div>
              </div>
              <div v-else-if="Array.isArray(conflict.contactValue)" class="text-gray-300 text-sm">
                {{ conflict.contactValue.join(', ') }}
              </div>
              <div v-else class="text-gray-300 text-sm">
                {{ conflict.contactValue || 'Not provided' }}
              </div>
            </div>
            
            <div class="mt-2 text-xs text-gray-400">
              Last updated: {{ formatDate(contactData.updatedAt) }}
            </div>
          </div>

          <!-- Business Card Value -->
          <div
            :class="[
              'conflict-option p-4 rounded-lg border-2 cursor-pointer transition-all duration-200',
              resolutions[conflict.field] === 'card' 
                ? 'border-blue-500 bg-blue-500/10' 
                : 'border-gray-600 bg-gray-800/30 hover:border-gray-500'
            ]"
            @click="selectResolution(conflict.field, 'card')"
          >
            <div class="flex items-start justify-between mb-2">
              <div class="flex items-center">
                <Icon name="mdi:card-account-details" class="w-4 h-4 text-orange-400 mr-2" />
                <span class="text-sm font-medium text-white">Business Card</span>
              </div>
              <div
                v-if="resolutions[conflict.field] === 'card'"
                class="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center"
              >
                <Icon name="mdi:check" class="w-3 h-3 text-white" />
              </div>
            </div>
            
            <div class="space-y-1">
              <div
                v-if="conflict.field === 'address'"
                class="text-gray-300 text-sm"
              >
                <div v-if="conflict.cardValue?.street">{{ conflict.cardValue.street }}</div>
                <div v-if="conflict.cardValue?.city || conflict.cardValue?.state">
                  {{ [conflict.cardValue?.city, conflict.cardValue?.state].filter(Boolean).join(', ') }}
                </div>
                <div v-if="conflict.cardValue?.country">{{ conflict.cardValue.country }}</div>
                <div v-if="conflict.cardValue?.postalCode">{{ conflict.cardValue.postalCode }}</div>
              </div>
              <div v-else-if="Array.isArray(conflict.cardValue)" class="text-gray-300 text-sm">
                {{ conflict.cardValue.join(', ') }}
              </div>
              <div v-else class="text-gray-300 text-sm">
                {{ conflict.cardValue || 'Not provided' }}
              </div>
            </div>
            
            <div class="mt-2 text-xs text-gray-400">
              From business card
            </div>
          </div>
        </div>

        <!-- Custom Value Option -->
        <div
          v-if="allowCustomValues && customFields[conflict.field] !== undefined"
          class="mt-4"
        >
          <div class="flex items-center mb-2">
            <input
              :id="`custom-${conflict.field}`"
              v-model="resolutions[conflict.field]"
              type="radio"
              :value="'custom'"
              class="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 focus:ring-blue-500"
            >
            <label
              :for="`custom-${conflict.field}`"
              class="ml-2 text-sm font-medium text-white"
            >
              Use custom value
            </label>
          </div>
          
          <div v-if="resolutions[conflict.field] === 'custom'" class="mt-2">
            <input
              v-if="conflict.field !== 'address' && !Array.isArray(conflict.contactValue)"
              v-model="customFields[conflict.field]"
              type="text"
              class="w-full px-3 py-2 bg-gray-700/50 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              :placeholder="`Enter custom ${getFieldLabel(conflict.field).toLowerCase()}`"
            >
            
            <div v-else-if="conflict.field === 'address'" class="space-y-2">
              <input
                v-model="customFields.address.street"
                type="text"
                placeholder="Street"
                class="w-full px-3 py-2 bg-gray-700/50 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
              <div class="grid grid-cols-1 md:grid-cols-3 gap-2">
                <input
                  v-model="customFields.address.city"
                  type="text"
                  placeholder="City"
                  class="px-3 py-2 bg-gray-700/50 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                <input
                  v-model="customFields.address.state"
                  type="text"
                  placeholder="State"
                  class="px-3 py-2 bg-gray-700/50 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                <input
                  v-model="customFields.address.postalCode"
                  type="text"
                  placeholder="Postal Code"
                  class="px-3 py-2 bg-gray-700/50 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
              </div>
              <input
                v-model="customFields.address.country"
                type="text"
                placeholder="Country"
                class="w-full px-3 py-2 bg-gray-700/50 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
            </div>
            
            <input
              v-else-if="Array.isArray(conflict.contactValue)"
              v-model="customFields[conflict.field]"
              type="text"
              class="w-full px-3 py-2 bg-gray-700/50 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              :placeholder="`Enter tags separated by commas`"
            >
          </div>
        </div>
      </div>
    </div>

    <!-- Preserve Non-Conflicting Data Notice -->
    <div v-if="preservedFields.length > 0" class="mt-6 p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
      <div class="flex items-start">
        <Icon name="mdi:information" class="w-5 h-5 text-green-400 mr-2 mt-0.5" />
        <div class="flex-1">
          <h4 class="text-green-300 font-medium mb-1">Preserved Fields</h4>
          <p class="text-green-200 text-sm mb-2">
            The following fields have no conflicts and will be preserved:
          </p>
          <div class="flex flex-wrap gap-2">
            <span
              v-for="field in preservedFields"
              :key="field"
              class="inline-flex items-center px-2 py-1 rounded-md text-xs bg-green-500/20 text-green-300"
            >
              {{ getFieldLabel(field) }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div class="flex justify-end space-x-3 mt-8">
      <button
        @click="$emit('cancel')"
        type="button"
        class="px-6 py-2 bg-white/10 hover:bg-white/20 text-white font-medium rounded-lg transition-colors border border-white/20"
      >
        Cancel
      </button>
      <button
        @click="applyResolutions"
        :disabled="!areAllConflictsResolved"
        type="button"
        class="px-6 py-2 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Apply Changes
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import type { Contact } from '~/composables/useContacts'
import type { BusinessCard } from '~/composables/useBusinessCards'

// Props
const props = defineProps<{
  contactData: Contact
  cardData: BusinessCard
  allowCustomValues?: boolean
}>()

// Emits
const emit = defineEmits<{
  resolved: [resolvedData: any]
  cancel: []
}>()

// Types
interface DataConflict {
  field: string
  contactValue: any
  cardValue: any
}

// State
const resolutions = ref<Record<string, 'contact' | 'card' | 'custom'>>({})
const customFields = ref<Record<string, any>>({})

// Field mappings and configurations
const fieldMappings = {
  'first_name': 'firstName',
  'last_name': 'lastName',
  'job_title': 'jobTitle',
  'postal_code': 'postalCode'
}

const fieldLabels: Record<string, string> = {
  first_name: 'First Name',
  last_name: 'Last Name',
  email: 'Email',
  phone: 'Phone',
  company: 'Company',
  job_title: 'Job Title',
  website: 'Website',
  address: 'Address',
  tags: 'Tags'
}

const fieldIcons: Record<string, string> = {
  first_name: 'mdi:account',
  last_name: 'mdi:account',
  email: 'mdi:email',
  phone: 'mdi:phone',
  company: 'mdi:domain',
  job_title: 'mdi:briefcase',
  website: 'mdi:web',
  address: 'mdi:map-marker',
  tags: 'mdi:tag-multiple'
}

// Computed
const conflicts = computed<DataConflict[]>(() => {
  const conflictList: DataConflict[] = []
  
  // Define fields to check for conflicts
  const fieldsToCheck = [
    'first_name', 'last_name', 'email', 'phone', 
    'company', 'job_title', 'website', 'address', 'tags'
  ]
  
  fieldsToCheck.forEach(field => {
    let contactValue: any
    let cardValue: any
    
    // Get contact value
    if (field === 'address') {
      contactValue = props.contactData.address
      cardValue = extractCardAddress()
    } else if (field === 'tags') {
      contactValue = props.contactData.tags || []
      cardValue = props.cardData.tags || (typeof props.cardData.tags === 'string' 
        ? props.cardData.tags.split(',').map(t => t.trim()).filter(Boolean)
        : [])
    } else {
      // Handle field name variations
      const contactFieldName = field
      const cardFieldName = fieldMappings[field] || field
      
      contactValue = props.contactData[contactFieldName] || props.contactData[cardFieldName]
      cardValue = props.cardData[cardFieldName] || props.cardData[contactFieldName]
    }
    
    // Check if values are different and both exist
    if (hasConflict(contactValue, cardValue)) {
      conflictList.push({
        field,
        contactValue,
        cardValue
      })
    }
  })
  
  return conflictList
})

const preservedFields = computed(() => {
  const allFields = [
    'first_name', 'last_name', 'email', 'phone', 
    'company', 'job_title', 'website', 'address', 'tags'
  ]
  
  const conflictFields = conflicts.value.map(c => c.field)
  return allFields.filter(field => !conflictFields.includes(field))
})

const areAllConflictsResolved = computed(() => {
  return conflicts.value.every(conflict => 
    resolutions.value[conflict.field] && 
    (resolutions.value[conflict.field] !== 'custom' || customFields.value[conflict.field])
  )
})

// Methods
const extractCardAddress = () => {
  const card = props.cardData
  return {
    street: card.address || card.address_street,
    city: card.city || card.address_city,
    state: card.state || card.address_state,
    country: card.country || card.address_country,
    postalCode: card.postal_code || card.address_zip || card.zip
  }
}

const hasConflict = (contactValue: any, cardValue: any): boolean => {
  // Both must exist to have a conflict
  if (!contactValue || !cardValue) return false
  
  // Handle arrays (tags)
  if (Array.isArray(contactValue) && Array.isArray(cardValue)) {
    return JSON.stringify(contactValue.sort()) !== JSON.stringify(cardValue.sort())
  }
  
  // Handle objects (address)
  if (typeof contactValue === 'object' && typeof cardValue === 'object') {
    // Check if any address field differs
    const contactFields = Object.values(contactValue).filter(Boolean)
    const cardFields = Object.values(cardValue).filter(Boolean)
    
    if (contactFields.length === 0 || cardFields.length === 0) return false
    
    return JSON.stringify(contactValue) !== JSON.stringify(cardValue)
  }
  
  // Handle strings/primitives
  return String(contactValue).trim() !== String(cardValue).trim()
}

const getFieldLabel = (field: string): string => {
  return fieldLabels[field] || field.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const getFieldIcon = (field: string): string => {
  return fieldIcons[field] || 'mdi:help-circle'
}

const selectResolution = (field: string, source: 'contact' | 'card' | 'custom') => {
  resolutions.value[field] = source
  
  // Initialize custom field if custom is selected
  if (source === 'custom' && customFields.value[field] === undefined) {
    if (field === 'address') {
      customFields.value[field] = {
        street: '',
        city: '',
        state: '',
        country: '',
        postalCode: ''
      }
    } else if (field === 'tags') {
      customFields.value[field] = ''
    } else {
      customFields.value[field] = ''
    }
  }
}

const formatDate = (date: Date | string): string => {
  try {
    const d = typeof date === 'string' ? new Date(date) : date
    return d.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  } catch {
    return 'Unknown date'
  }
}

const applyResolutions = () => {
  const resolvedData = { ...props.contactData }
  
  conflicts.value.forEach(conflict => {
    const resolution = resolutions.value[conflict.field]
    let valueToUse: any
    
    if (resolution === 'contact') {
      valueToUse = conflict.contactValue
    } else if (resolution === 'card') {
      valueToUse = conflict.cardValue
    } else if (resolution === 'custom') {
      valueToUse = customFields.value[conflict.field]
      
      // Convert tags string back to array
      if (conflict.field === 'tags' && typeof valueToUse === 'string') {
        valueToUse = valueToUse.split(',').map(t => t.trim()).filter(Boolean)
      }
    }
    
    // Apply the resolved value
    if (conflict.field === 'address') {
      resolvedData.address = valueToUse
    } else {
      const fieldName = fieldMappings[conflict.field] || conflict.field
      resolvedData[fieldName] = valueToUse
      
      // Also set the alternative field name if it exists
      if (fieldMappings[conflict.field]) {
        resolvedData[conflict.field] = valueToUse
      }
    }
  })
  
  emit('resolved', resolvedData)
}

// Initialize
onMounted(() => {
  // Set default resolutions to 'card' (new data takes precedence)
  conflicts.value.forEach(conflict => {
    resolutions.value[conflict.field] = 'card'
  })
})
</script>

<style scoped>
.conflict-option {
  position: relative;
}

.conflict-option:hover {
  transform: translateY(-1px);
}
</style>