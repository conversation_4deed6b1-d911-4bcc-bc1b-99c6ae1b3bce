<template>
  <div class="audience-builder">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
      <div>
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
          Audience Builder
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mt-1">
          Create precise audience segments for your advertising campaigns
        </p>
      </div>
      
      <div class="flex items-center space-x-3">
        <!-- Save/Load Presets -->
        <div class="relative">
          <button
            @click="showPresets = !showPresets"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700"
          >
            <Icon name="heroicons:bookmark" class="w-4 h-4 mr-2" />
            Presets
          </button>
          
          <!-- Presets Dropdown -->
          <div
            v-if="showPresets"
            class="absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50"
          >
            <div class="p-3 border-b border-gray-200 dark:border-gray-700">
              <div class="flex items-center justify-between">
                <h4 class="font-medium text-gray-900 dark:text-white">Saved Presets</h4>
                <button
                  @click="showSavePresetModal = true"
                  class="text-blue-600 hover:text-blue-700 text-sm"
                >
                  Save Current
                </button>
              </div>
            </div>
            
            <div class="max-h-48 overflow-y-auto">
              <div
                v-for="preset in savedPresets"
                :key="preset.id"
                class="flex items-center justify-between p-3 hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                <div class="flex-1">
                  <div class="font-medium text-gray-900 dark:text-white text-sm">
                    {{ preset.name }}
                  </div>
                  <div class="text-xs text-gray-500 dark:text-gray-400">
                    {{ formatPresetDescription(preset) }}
                  </div>
                </div>
                
                <div class="flex items-center space-x-1">
                  <button
                    @click="loadPreset(preset)"
                    class="p-1 text-blue-600 hover:text-blue-700"
                  >
                    <Icon name="heroicons:arrow-down-tray" class="w-4 h-4" />
                  </button>
                  <button
                    @click="deletePreset(preset.id)"
                    class="p-1 text-red-600 hover:text-red-700"
                  >
                    <Icon name="heroicons:trash" class="w-4 h-4" />
                  </button>
                </div>
              </div>
              
              <div
                v-if="savedPresets.length === 0"
                class="p-4 text-center text-gray-500 dark:text-gray-400 text-sm"
              >
                No saved presets yet
              </div>
            </div>
          </div>
        </div>
        
        <!-- Save Segment Button -->
        <button
          @click="saveSegment"
          :disabled="!canSaveSegment || isLoading"
          class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
        >
          <Icon name="heroicons:check" class="w-4 h-4 mr-2" />
          {{ editingSegment ? 'Update Segment' : 'Save Segment' }}
        </button>
      </div>
    </div>

    <!-- Segment Basic Info -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Segment Name *
          </label>
          <input
            v-model="segmentName"
            type="text"
            placeholder="e.g., Tech-Savvy Millennials in NYC"
            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            required
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Tags (optional)
          </label>
          <input
            v-model="segmentTags"
            type="text"
            placeholder="tech, millennials, urban (comma-separated)"
            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>
      </div>
      
      <div class="mt-4">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Description (optional)
        </label>
        <textarea
          v-model="segmentDescription"
          rows="2"
          placeholder="Describe this audience segment and its purpose..."
          class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
        ></textarea>
      </div>
    </div>

    <!-- Targeting Options Tabs -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
      <!-- Tab Navigation -->
      <div class="border-b border-gray-200 dark:border-gray-700">
        <nav class="flex space-x-8 px-6" aria-label="Tabs">
          <button
            v-for="tab in targetingTabs"
            :key="tab.id"
            @click="activeTab = tab.id"
            :class="[
              'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2',
              activeTab === tab.id
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
            ]"
          >
            <Icon :name="tab.icon" class="w-5 h-5" />
            <span>{{ tab.name }}</span>
            <span
              v-if="getTabBadgeCount(tab.id) > 0"
              class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
            >
              {{ getTabBadgeCount(tab.id) }}
            </span>
          </button>
        </nav>
      </div>

      <!-- Tab Content -->
      <div class="p-6">
        <!-- Geographic Targeting -->
        <div v-if="activeTab === 'geographic'" class="tab-content">
          <GeographicTargeting
            v-model="geographicCriteria"
            @audienceEstimate="handleGeographicEstimate"
          />
        </div>

        <!-- Demographic Targeting -->
        <div v-if="activeTab === 'demographic'" class="tab-content">
          <DemographicFilters v-model="demographicCriteria" />
        </div>

        <!-- Behavioral Targeting -->
        <div v-if="activeTab === 'behavioral'" class="tab-content">
          <BehavioralTargeting v-model="behavioralCriteria" />
        </div>

        <!-- Combined Targeting Summary -->
        <div v-if="activeTab === 'summary'" class="tab-content">
          <div class="space-y-6">
            <!-- Targeting Summary -->
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Targeting Summary
              </h3>
              
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Geographic Summary -->
                <div
                  v-if="hasGeographicTargeting"
                  class="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg"
                >
                  <div class="flex items-center space-x-2 mb-2">
                    <Icon name="heroicons:map-pin" class="w-5 h-5 text-green-600 dark:text-green-400" />
                    <h4 class="font-medium text-green-900 dark:text-green-100">Geographic</h4>
                  </div>
                  <p class="text-sm text-green-700 dark:text-green-200">
                    {{ geographicSummary }}
                  </p>
                </div>

                <!-- Demographic Summary -->
                <div
                  v-if="hasDemographicTargeting"
                  class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg"
                >
                  <div class="flex items-center space-x-2 mb-2">
                    <Icon name="heroicons:users" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    <h4 class="font-medium text-blue-900 dark:text-blue-100">Demographic</h4>
                  </div>
                  <p class="text-sm text-blue-700 dark:text-blue-200">
                    {{ demographicSummary }}
                  </p>
                </div>

                <!-- Behavioral Summary -->
                <div
                  v-if="hasBehavioralTargeting"
                  class="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg"
                >
                  <div class="flex items-center space-x-2 mb-2">
                    <Icon name="heroicons:chart-bar" class="w-5 h-5 text-purple-600 dark:text-purple-400" />
                    <h4 class="font-medium text-purple-900 dark:text-purple-100">Behavioral</h4>
                  </div>
                  <p class="text-sm text-purple-700 dark:text-purple-200">
                    {{ behavioralSummary }}
                  </p>
                </div>
              </div>
            </div>

            <!-- Audience Estimation -->
            <div class="p-6 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="text-center">
                  <div class="text-2xl font-bold text-gray-900 dark:text-white">
                    {{ formatNumber(finalAudienceEstimate.size) }}
                  </div>
                  <div class="text-sm text-gray-600 dark:text-gray-400">
                    Estimated Reach
                  </div>
                </div>
                
                <div class="text-center">
                  <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {{ (finalAudienceEstimate.confidence * 100).toFixed(0) }}%
                  </div>
                  <div class="text-sm text-gray-600 dark:text-gray-400">
                    Confidence
                  </div>
                </div>
                
                <div class="text-center">
                  <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                    ${{ finalAudienceEstimate.costEstimate.cpm.toFixed(2) }}
                  </div>
                  <div class="text-sm text-gray-600 dark:text-gray-400">
                    Estimated CPM
                  </div>
                </div>
                
                <div class="text-center">
                  <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">
                    {{ finalAudienceEstimate.competitiveness.toUpperCase() }}
                  </div>
                  <div class="text-sm text-gray-600 dark:text-gray-400">
                    Competition Level
                  </div>
                </div>
              </div>
              
              <!-- Cost Breakdown -->
              <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <h4 class="font-medium text-gray-900 dark:text-white mb-3">
                  Estimated Daily Costs
                </h4>
                <div class="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <span class="text-gray-600 dark:text-gray-400">Conservative:</span>
                    <span class="font-medium ml-2">${finalAudienceEstimate.costEstimate.daily}</span>
                  </div>
                  <div>
                    <span class="text-gray-600 dark:text-gray-400">Weekly:</span>
                    <span class="font-medium ml-2">${finalAudienceEstimate.costEstimate.weekly}</span>
                  </div>
                  <div>
                    <span class="text-gray-600 dark:text-gray-400">Monthly:</span>
                    <span class="font-medium ml-2">${finalAudienceEstimate.costEstimate.monthly}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Optimization Recommendations -->
            <div v-if="recommendations.length > 0">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Optimization Recommendations
              </h3>
              
              <div class="space-y-3">
                <div
                  v-for="recommendation in recommendations"
                  :key="recommendation.id"
                  class="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg"
                >
                  <div class="flex items-start space-x-3">
                    <Icon name="heroicons:light-bulb" class="w-5 h-5 text-yellow-600 dark:text-yellow-400 flex-shrink-0 mt-0.5" />
                    <div>
                      <h4 class="font-medium text-yellow-900 dark:text-yellow-100">
                        {{ recommendation.title }}
                      </h4>
                      <p class="text-sm text-yellow-700 dark:text-yellow-200 mt-1">
                        {{ recommendation.description }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Save Preset Modal -->
    <div
      v-if="showSavePresetModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      @click.self="showSavePresetModal = false"
    >
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Save Targeting Preset
        </h3>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Preset Name
            </label>
            <input
              v-model="newPresetName"
              type="text"
              placeholder="e.g., Tech Millennials NYC"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Description
            </label>
            <textarea
              v-model="newPresetDescription"
              rows="3"
              placeholder="Describe this targeting preset..."
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            ></textarea>
          </div>
        </div>
        
        <div class="flex justify-end space-x-3 mt-6">
          <button
            @click="showSavePresetModal = false"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300"
          >
            Cancel
          </button>
          <button
            @click="savePreset"
            :disabled="!newPresetName.trim()"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            Save Preset
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useAudienceSegmentation } from '~/composables/useAudienceSegmentation';
import GeographicTargeting from './GeographicTargeting.vue';
import DemographicFilters from './DemographicFilters.vue';
import BehavioralTargeting from './BehavioralTargeting.vue';
import type {
  AudienceSegment,
  GeographicCriteria,
  DemographicCriteria,
  BehavioralCriteria,
  AudienceEstimation
} from '~/types/audience-segmentation';

// Props and emits
interface Props {
  editingSegment?: AudienceSegment | null;
}

interface Emits {
  (e: 'segmentSaved', segment: AudienceSegment): void;
  (e: 'segmentUpdated', segment: AudienceSegment): void;
}

const props = withDefaults(defineProps<Props>(), {
  editingSegment: null
});

const emits = defineEmits<Emits>();

// Composables
const {
  createSegment,
  updateSegment,
  isLoading,
  error
} = useAudienceSegmentation();

// State
const activeTab = ref('geographic');
const showPresets = ref(false);
const showSavePresetModal = ref(false);

// Segment basic info
const segmentName = ref('');
const segmentDescription = ref('');
const segmentTags = ref('');

// Targeting criteria
const geographicCriteria = ref<GeographicCriteria>({ type: 'radius', locations: [] });
const demographicCriteria = ref<DemographicCriteria>({});
const behavioralCriteria = ref<BehavioralCriteria>({});

// Audience estimation
const geographicEstimate = ref({ size: 0, confidence: 0.8 });

// Presets
const savedPresets = ref<Array<{
  id: string;
  name: string;
  description: string;
  criteria: {
    geographic?: GeographicCriteria;
    demographic?: DemographicCriteria;
    behavioral?: BehavioralCriteria;
  };
}>>([]);

const newPresetName = ref('');
const newPresetDescription = ref('');

// Tab configuration
const targetingTabs = [
  {
    id: 'geographic',
    name: 'Geographic',
    icon: 'heroicons:map-pin'
  },
  {
    id: 'demographic',
    name: 'Demographic',
    icon: 'heroicons:users'
  },
  {
    id: 'behavioral',
    name: 'Behavioral',
    icon: 'heroicons:chart-bar'
  },
  {
    id: 'summary',
    name: 'Summary',
    icon: 'heroicons:clipboard-document-check'
  }
];

// Computed properties
const canSaveSegment = computed(() => {
  return segmentName.value.trim().length > 0 && (
    hasGeographicTargeting.value ||
    hasDemographicTargeting.value ||
    hasBehavioralTargeting.value
  );
});

const hasGeographicTargeting = computed(() => {
  return geographicCriteria.value.locations.length > 0;
});

const hasDemographicTargeting = computed(() => {
  return Object.keys(demographicCriteria.value).length > 0;
});

const hasBehavioralTargeting = computed(() => {
  return Object.keys(behavioralCriteria.value).length > 0;
});

const geographicSummary = computed(() => {
  if (!hasGeographicTargeting.value) return '';
  
  const locations = geographicCriteria.value.locations;
  if (locations.length === 1) {
    return `Targeting ${locations[0].name}`;
  } else if (locations.length <= 3) {
    return `Targeting ${locations.map(l => l.name).join(', ')}`;
  } else {
    return `Targeting ${locations.length} locations`;
  }
});

const demographicSummary = computed(() => {
  if (!hasDemographicTargeting.value) return '';
  
  const criteria = demographicCriteria.value;
  const parts = [];
  
  if (criteria.ageRange) {
    parts.push(`Ages ${criteria.ageRange.min}-${criteria.ageRange.max}`);
  }
  if (criteria.gender && criteria.gender.length < 4) {
    parts.push(`${criteria.gender.join(', ')}`);
  }
  if (criteria.income) {
    parts.push('Income filtered');
  }
  
  return parts.join(', ') || 'Multiple demographic filters';
});

const behavioralSummary = computed(() => {
  if (!hasBehavioralTargeting.value) return '';
  
  const criteria = behavioralCriteria.value;
  const parts = [];
  
  if (criteria.interests && criteria.interests.length > 0) {
    parts.push(`${criteria.interests.length} interests`);
  }
  if (criteria.deviceTypes && criteria.deviceTypes.length < 3) {
    parts.push(`${criteria.deviceTypes.join(', ')} users`);
  }
  if (criteria.engagementLevel && criteria.engagementLevel.length < 3) {
    parts.push(`${criteria.engagementLevel.join('/')} engagement`);
  }
  
  return parts.join(', ') || 'Multiple behavioral filters';
});

const finalAudienceEstimate = computed<AudienceEstimation>(() => {
  let baseSize = geographicEstimate.value.size;
  let confidence = geographicEstimate.value.confidence;
  let baseCPM = 2.0;

  // Apply demographic multiplier
  if (hasDemographicTargeting.value) {
    baseSize *= 0.6; // Demographic filtering typically reduces audience
    confidence *= 0.9;
    baseCPM *= 1.2;
  }

  // Apply behavioral multiplier
  if (hasBehavioralTargeting.value) {
    baseSize *= 0.4; // Behavioral targeting is more specific
    confidence *= 0.8;
    baseCPM *= 1.4;
  }

  // Ensure minimum audience size
  baseSize = Math.max(1000, Math.round(baseSize));

  return {
    segmentId: 'temp',
    estimatedSize: baseSize,
    confidence: Math.max(0.1, confidence),
    costEstimate: {
      currency: 'USD',
      cpm: baseCPM,
      daily: Math.round(baseSize * baseCPM / 1000),
      weekly: Math.round(baseSize * baseCPM / 1000 * 7),
      monthly: Math.round(baseSize * baseCPM / 1000 * 30)
    },
    reach: {
      potential: baseSize,
      effective: Math.round(baseSize * 0.7)
    },
    competitiveness: baseCPM > 3 ? 'high' : baseCPM > 2 ? 'medium' : 'low',
    recommendations: [],
    calculatedAt: new Date() as any
  };
});

const recommendations = computed(() => {
  const recs = [];
  
  if (!hasGeographicTargeting.value) {
    recs.push({
      id: 'add-geo',
      title: 'Add Geographic Targeting',
      description: 'Geographic targeting can significantly improve ad relevance and reduce costs.'
    });
  }
  
  if (finalAudienceEstimate.value.estimatedSize < 10000) {
    recs.push({
      id: 'expand-audience',
      title: 'Consider Expanding Audience',
      description: 'Small audiences may have limited delivery. Consider broadening your targeting criteria.'
    });
  }
  
  if (finalAudienceEstimate.value.costEstimate.cpm > 4) {
    recs.push({
      id: 'reduce-specificity',
      title: 'Reduce Targeting Specificity',
      description: 'High CPM detected. Consider reducing the number of targeting criteria to lower costs.'
    });
  }
  
  if (hasGeographicTargeting.value && hasDemographicTargeting.value && hasBehavioralTargeting.value) {
    recs.push({
      id: 'test-combinations',
      title: 'Test Different Combinations',
      description: 'Consider creating separate segments to test which targeting performs best.'
    });
  }
  
  return recs;
});

// Methods
const getTabBadgeCount = (tabId: string) => {
  switch (tabId) {
    case 'geographic':
      return geographicCriteria.value.locations.length;
    case 'demographic':
      return Object.keys(demographicCriteria.value).length;
    case 'behavioral':
      return (behavioralCriteria.value.interests?.length || 0) +
             (behavioralCriteria.value.deviceTypes?.length || 0) +
             (behavioralCriteria.value.behaviors?.length || 0);
    default:
      return 0;
  }
};

const handleGeographicEstimate = (estimate: { size: number; confidence: number }) => {
  geographicEstimate.value = estimate;
};

const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

const saveSegment = async () => {
  if (!canSaveSegment.value) return;

  try {
    const segmentData = {
      name: segmentName.value.trim(),
      description: segmentDescription.value.trim() || undefined,
      type: getSegmentType(),
      criteria: {
        ...(hasGeographicTargeting.value && { geographic: geographicCriteria.value }),
        ...(hasDemographicTargeting.value && { demographic: demographicCriteria.value }),
        ...(hasBehavioralTargeting.value && { behavioral: behavioralCriteria.value })
      },
      size: {
        estimated: finalAudienceEstimate.value.estimatedSize,
        confidence: finalAudienceEstimate.value.confidence,
        lastUpdated: new Date() as any
      },
      tags: segmentTags.value.split(',').map(tag => tag.trim()).filter(Boolean),
      status: 'active' as const
    };

    if (props.editingSegment) {
      const updatedSegment = await updateSegment(props.editingSegment.id, segmentData);
      emits('segmentUpdated', updatedSegment as any);
    } else {
      const segmentId = await createSegment(segmentData as any);
      emits('segmentSaved', { id: segmentId, ...segmentData } as any);
    }

    // Reset form
    resetForm();
  } catch (err) {
    console.error('Error saving segment:', err);
  }
};

const getSegmentType = () => {
  if (hasGeographicTargeting.value && hasDemographicTargeting.value && hasBehavioralTargeting.value) {
    return 'combined';
  } else if (hasBehavioralTargeting.value) {
    return 'behavioral';
  } else if (hasDemographicTargeting.value) {
    return 'demographic';
  } else {
    return 'geographic';
  }
};

const resetForm = () => {
  segmentName.value = '';
  segmentDescription.value = '';
  segmentTags.value = '';
  geographicCriteria.value = { type: 'radius', locations: [] };
  demographicCriteria.value = {};
  behavioralCriteria.value = {};
  activeTab.value = 'geographic';
};

const savePreset = () => {
  if (!newPresetName.value.trim()) return;

  const preset = {
    id: `preset_${Date.now()}`,
    name: newPresetName.value.trim(),
    description: newPresetDescription.value.trim(),
    criteria: {
      ...(hasGeographicTargeting.value && { geographic: geographicCriteria.value }),
      ...(hasDemographicTargeting.value && { demographic: demographicCriteria.value }),
      ...(hasBehavioralTargeting.value && { behavioral: behavioralCriteria.value })
    }
  };

  savedPresets.value.push(preset);
  
  // Save to localStorage
  localStorage.setItem('audiencePresets', JSON.stringify(savedPresets.value));

  // Reset modal
  newPresetName.value = '';
  newPresetDescription.value = '';
  showSavePresetModal.value = false;
  showPresets.value = false;
};

const loadPreset = (preset: any) => {
  if (preset.criteria.geographic) {
    geographicCriteria.value = preset.criteria.geographic;
  }
  if (preset.criteria.demographic) {
    demographicCriteria.value = preset.criteria.demographic;
  }
  if (preset.criteria.behavioral) {
    behavioralCriteria.value = preset.criteria.behavioral;
  }
  
  showPresets.value = false;
  activeTab.value = 'geographic';
};

const deletePreset = (presetId: string) => {
  savedPresets.value = savedPresets.value.filter(p => p.id !== presetId);
  localStorage.setItem('audiencePresets', JSON.stringify(savedPresets.value));
};

const formatPresetDescription = (preset: any) => {
  const parts = [];
  if (preset.criteria.geographic?.locations?.length) {
    parts.push(`${preset.criteria.geographic.locations.length} locations`);
  }
  if (preset.criteria.demographic && Object.keys(preset.criteria.demographic).length) {
    parts.push('demographics');
  }
  if (preset.criteria.behavioral && Object.keys(preset.criteria.behavioral).length) {
    parts.push('behaviors');
  }
  return parts.join(', ') || 'No targeting';
};

// Load presets from localStorage on mount
const loadSavedPresets = () => {
  try {
    const saved = localStorage.getItem('audiencePresets');
    if (saved) {
      savedPresets.value = JSON.parse(saved);
    }
  } catch (err) {
    console.error('Error loading saved presets:', err);
  }
};

// Initialize editing segment
watch(
  () => props.editingSegment,
  (segment) => {
    if (segment) {
      segmentName.value = segment.name;
      segmentDescription.value = segment.description || '';
      segmentTags.value = segment.tags?.join(', ') || '';
      
      if (segment.criteria.geographic) {
        geographicCriteria.value = segment.criteria.geographic;
      }
      if (segment.criteria.demographic) {
        demographicCriteria.value = segment.criteria.demographic;
      }
      if (segment.criteria.behavioral) {
        behavioralCriteria.value = segment.criteria.behavioral;
      }
    }
  },
  { immediate: true }
);

// Load presets on component mount
onMounted(() => {
  loadSavedPresets();
});
</script>

<style scoped>
.tab-content {
  @apply min-h-[400px];
}

/* Close dropdown when clicking outside */
.audience-builder {
  position: relative;
}
</style>