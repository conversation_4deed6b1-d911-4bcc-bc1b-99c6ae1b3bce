<template>
  <div class="geographic-targeting">
    <!-- Header with controls -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
          Geographic Targeting
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Select locations to target with your ads
        </p>
      </div>
      
      <div class="flex space-x-2">
        <button
          @click="toggleDrawingMode('radius')"
          :class="[
            'px-3 py-2 rounded-lg text-sm font-medium transition-colors',
            drawingMode === 'radius'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300'
          ]"
        >
          <Icon name="heroicons:map-pin" class="w-4 h-4 mr-1" />
          Radius
        </button>
        
        <button
          @click="toggleDrawingMode('polygon')"
          :class="[
            'px-3 py-2 rounded-lg text-sm font-medium transition-colors',
            drawingMode === 'polygon'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300'
          ]"
        >
          <Icon name="heroicons:pencil" class="w-4 h-4 mr-1" />
          Polygon
        </button>
        
        <button
          @click="clearSelections"
          class="px-3 py-2 rounded-lg text-sm font-medium bg-red-100 text-red-700 hover:bg-red-200 dark:bg-red-900 dark:text-red-300"
        >
          <Icon name="heroicons:trash" class="w-4 h-4 mr-1" />
          Clear
        </button>
      </div>
    </div>

    <!-- Location Search -->
    <div class="mb-4">
      <div class="relative">
        <input
          v-model="locationSearch"
          @input="searchLocations"
          type="text"
          placeholder="Search for cities, states, or countries..."
          class="w-full px-4 py-2 pl-10 pr-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-600 dark:text-white"
        />
        <Icon 
          name="heroicons:magnifying-glass" 
          class="absolute left-3 top-2.5 w-5 h-5 text-gray-400" 
        />
        
        <!-- Search Results Dropdown -->
        <div
          v-if="searchResults.length > 0 && showSearchResults"
          class="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg max-h-60 overflow-y-auto"
        >
          <div
            v-for="result in searchResults"
            :key="result.id"
            @click="selectLocation(result)"
            class="px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-100 dark:border-gray-700 last:border-b-0"
          >
            <div class="flex items-center justify-between">
              <div>
                <div class="font-medium text-gray-900 dark:text-white">
                  {{ result.name }}
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">
                  {{ result.metadata?.country }}
                  <span v-if="result.metadata?.state">
                    , {{ result.metadata.state }}
                  </span>
                </div>
              </div>
              <div class="text-xs text-gray-400">
                {{ formatPopulation(result.metadata?.population) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Map Container -->
    <div class="relative">
      <div
        ref="mapContainer"
        class="w-full h-96 bg-gray-100 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
      />
      
      <!-- Map Loading State -->
      <div
        v-if="mapLoading"
        class="absolute inset-0 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center"
      >
        <div class="text-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p class="text-sm text-gray-600 dark:text-gray-400">Loading map...</p>
        </div>
      </div>
    </div>

    <!-- Radius Controls (shown when radius mode is active) -->
    <div
      v-if="drawingMode === 'radius' && selectedRadiusCenter"
      class="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg"
    >
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        Radius: {{ radiusKm }} km
      </label>
      <input
        v-model.number="radiusKm"
        @input="updateRadiusCircle"
        type="range"
        min="1"
        max="500"
        step="1"
        class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
      />
      <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
        <span>1 km</span>
        <span>500 km</span>
      </div>
    </div>

    <!-- Selected Locations List -->
    <div
      v-if="selectedLocations.length > 0"
      class="mt-6"
    >
      <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">
        Selected Locations ({{ selectedLocations.length }})
      </h4>
      
      <div class="space-y-2">
        <div
          v-for="location in selectedLocations"
          :key="location.id"
          class="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg"
        >
          <div class="flex items-center space-x-3">
            <Icon
              :name="getLocationTypeIcon(location.type)"
              class="w-5 h-5 text-blue-600 dark:text-blue-400"
            />
            <div>
              <div class="font-medium text-gray-900 dark:text-white">
                {{ location.name }}
              </div>
              <div class="text-sm text-gray-600 dark:text-gray-400">
                {{ getLocationDescription(location) }}
              </div>
            </div>
          </div>
          
          <button
            @click="removeLocation(location.id)"
            class="p-1 text-gray-400 hover:text-red-600 transition-colors"
          >
            <Icon name="heroicons:x-mark" class="w-4 h-4" />
          </button>
        </div>
      </div>
      
      <!-- Estimated Audience -->
      <div class="mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <Icon name="heroicons:users" class="w-5 h-5 text-green-600 dark:text-green-400" />
            <span class="font-medium text-green-900 dark:text-green-100">
              Estimated Audience
            </span>
          </div>
          <div class="text-right">
            <div class="font-bold text-green-900 dark:text-green-100">
              {{ formatNumber(estimatedAudience) }}
            </div>
            <div class="text-xs text-green-600 dark:text-green-400">
              {{ (confidence * 100).toFixed(0) }}% confidence
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Advanced Options -->
    <div class="mt-6">
      <button
        @click="showAdvancedOptions = !showAdvancedOptions"
        class="flex items-center space-x-2 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700"
      >
        <Icon
          :name="showAdvancedOptions ? 'heroicons:chevron-up' : 'heroicons:chevron-down'"
          class="w-4 h-4"
        />
        <span>Advanced Options</span>
      </button>
      
      <div
        v-if="showAdvancedOptions"
        class="mt-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg space-y-4"
      >
        <!-- Exclusions -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Exclude Locations
          </label>
          <p class="text-xs text-gray-500 dark:text-gray-400 mb-2">
            Add locations to exclude from targeting
          </p>
          <div class="space-y-2">
            <div
              v-for="exclusion in excludedLocations"
              :key="exclusion.id"
              class="flex items-center justify-between p-2 bg-red-50 dark:bg-red-900/20 rounded"
            >
              <span class="text-sm text-red-900 dark:text-red-100">
                {{ exclusion.name }}
              </span>
              <button
                @click="removeExclusion(exclusion.id)"
                class="text-red-600 hover:text-red-700"
              >
                <Icon name="heroicons:x-mark" class="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
        
        <!-- Location Type Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Location Types
          </label>
          <div class="flex flex-wrap gap-2">
            <label
              v-for="type in locationTypes"
              :key="type.value"
              class="flex items-center space-x-2"
            >
              <input
                v-model="selectedLocationTypes"
                :value="type.value"
                type="checkbox"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span class="text-sm text-gray-700 dark:text-gray-300">
                {{ type.label }}
              </span>
            </label>
          </div>
        </div>
        
        <!-- Population Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Minimum Population
          </label>
          <input
            v-model.number="minPopulation"
            type="number"
            min="0"
            step="1000"
            placeholder="e.g., 10000"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed } from 'vue';
import { useGeospatialIndex } from '~/composables/useGeospatialIndex';
import type { GeographicLocation, GeographicCriteria } from '~/types/audience-segmentation';

// Props and emits
interface Props {
  modelValue?: GeographicCriteria;
  readonly?: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: GeographicCriteria): void;
  (e: 'audienceEstimate', estimate: { size: number; confidence: number }): void;
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
});

const emits = defineEmits<Emits>();

// Composables
const { 
  searchLocations: searchGeoLocations, 
  calculateGeographicAudienceSize,
  searchResults: geoSearchResults,
  isLoading: geoLoading 
} = useGeospatialIndex();

// State
const mapContainer = ref<HTMLDivElement>();
const locationSearch = ref('');
const showSearchResults = ref(false);
const mapLoading = ref(true);
const drawingMode = ref<'radius' | 'polygon' | null>(null);
const showAdvancedOptions = ref(false);

// Map instance (will be initialized with Leaflet)
let map: any = null;
let drawnLayers: any[] = [];

// Location management
const selectedLocations = ref<GeographicLocation[]>([]);
const excludedLocations = ref<GeographicLocation[]>([]);
const searchResults = ref<GeographicLocation[]>([]);
const estimatedAudience = ref(0);
const confidence = ref(0.8);

// Radius controls
const selectedRadiusCenter = ref<{ lat: number; lng: number } | null>(null);
const radiusKm = ref(50);
let radiusCircle: any = null;

// Polygon controls
let drawingPolygon: any = null;
const polygonPoints = ref<{ lat: number; lng: number }[]>([]);

// Filters
const selectedLocationTypes = ref(['country', 'state', 'city']);
const minPopulation = ref(0);

const locationTypes = [
  { value: 'country', label: 'Countries' },
  { value: 'state', label: 'States/Provinces' },
  { value: 'city', label: 'Cities' },
  { value: 'postal', label: 'Postal Codes' }
];

/**
 * Initialize the map using Leaflet
 */
const initializeMap = async () => {
  if (typeof window === 'undefined') return;

  try {
    // Import Leaflet dynamically (for SSR compatibility)
    const L = (await import('leaflet')).default;
    
    // Fix default markers
    delete (L.Icon.Default.prototype as any)._getIconUrl;
    L.Icon.Default.mergeOptions({
      iconRetinaUrl: '/leaflet/marker-icon-2x.png',
      iconUrl: '/leaflet/marker-icon.png',
      shadowUrl: '/leaflet/marker-shadow.png',
    });

    // Initialize map
    map = L.map(mapContainer.value!, {
      center: [40.7128, -74.0060], // Default to NYC
      zoom: 4,
      zoomControl: true,
      attributionControl: true
    });

    // Add tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© OpenStreetMap contributors'
    }).addTo(map);

    // Add drawing controls
    setupDrawingControls(L);
    
    mapLoading.value = false;
  } catch (error) {
    console.error('Error initializing map:', error);
    mapLoading.value = false;
  }
};

/**
 * Setup drawing controls for the map
 */
const setupDrawingControls = (L: any) => {
  // Handle map clicks for radius mode
  map.on('click', (e: any) => {
    if (drawingMode.value === 'radius') {
      handleRadiusClick(e, L);
    } else if (drawingMode.value === 'polygon') {
      handlePolygonClick(e, L);
    }
  });
};

/**
 * Handle radius drawing clicks
 */
const handleRadiusClick = (e: any, L: any) => {
  const { lat, lng } = e.latlng;
  
  // Remove existing radius circle
  if (radiusCircle) {
    map.removeLayer(radiusCircle);
  }
  
  // Set center point
  selectedRadiusCenter.value = { lat, lng };
  
  // Create new radius circle
  radiusCircle = L.circle([lat, lng], {
    radius: radiusKm.value * 1000, // Convert to meters
    fillColor: '#3b82f6',
    fillOpacity: 0.2,
    color: '#3b82f6',
    weight: 2
  }).addTo(map);
  
  // Add to selected locations
  const location: GeographicLocation = {
    id: `radius_${Date.now()}`,
    name: `${radiusKm.value}km radius`,
    type: 'custom',
    center: new GeoPoint(lat, lng),
    radius: radiusKm.value
  };
  
  addLocation(location);
};

/**
 * Handle polygon drawing clicks
 */
const handlePolygonClick = (e: any, L: any) => {
  const { lat, lng } = e.latlng;
  
  polygonPoints.value.push({ lat, lng });
  
  // Remove existing drawing polygon
  if (drawingPolygon) {
    map.removeLayer(drawingPolygon);
  }
  
  // Create polygon preview
  if (polygonPoints.value.length >= 2) {
    drawingPolygon = L.polygon(
      polygonPoints.value.map(p => [p.lat, p.lng]),
      {
        fillColor: '#3b82f6',
        fillOpacity: 0.2,
        color: '#3b82f6',
        weight: 2
      }
    ).addTo(map);
  }
  
  // Add marker for each point
  L.marker([lat, lng]).addTo(map);
};

/**
 * Complete polygon drawing
 */
const completePolygon = () => {
  if (polygonPoints.value.length < 3) return;
  
  const location: GeographicLocation = {
    id: `polygon_${Date.now()}`,
    name: `Custom area (${polygonPoints.value.length} points)`,
    type: 'custom',
    polygon: polygonPoints.value.map(p => new GeoPoint(p.lat, p.lng))
  };
  
  addLocation(location);
  
  // Reset polygon drawing
  polygonPoints.value = [];
  if (drawingPolygon) {
    map.removeLayer(drawingPolygon);
    drawingPolygon = null;
  }
  
  drawingMode.value = null;
};

/**
 * Update radius circle when radius changes
 */
const updateRadiusCircle = () => {
  if (radiusCircle && selectedRadiusCenter.value) {
    radiusCircle.setRadius(radiusKm.value * 1000);
    
    // Update location in selected locations
    const index = selectedLocations.value.findIndex(loc => 
      loc.type === 'custom' && loc.radius
    );
    
    if (index !== -1) {
      selectedLocations.value[index] = {
        ...selectedLocations.value[index],
        name: `${radiusKm.value}km radius`,
        radius: radiusKm.value
      };
      
      updateAudienceEstimate();
    }
  }
};

/**
 * Toggle drawing mode
 */
const toggleDrawingMode = (mode: 'radius' | 'polygon') => {
  if (drawingMode.value === mode) {
    // Exit current mode
    drawingMode.value = null;
    
    if (mode === 'polygon' && polygonPoints.value.length >= 3) {
      completePolygon();
    }
  } else {
    drawingMode.value = mode;
    
    if (mode === 'radius') {
      // Clear polygon state
      polygonPoints.value = [];
      if (drawingPolygon) {
        map.removeLayer(drawingPolygon);
        drawingPolygon = null;
      }
    } else if (mode === 'polygon') {
      // Clear radius state
      selectedRadiusCenter.value = null;
      if (radiusCircle) {
        map.removeLayer(radiusCircle);
        radiusCircle = null;
      }
    }
  }
};

/**
 * Search for locations
 */
const searchLocations = async () => {
  if (!locationSearch.value.trim()) {
    searchResults.value = [];
    showSearchResults.value = false;
    return;
  }
  
  try {
    const results = await searchGeoLocations(
      locationSearch.value,
      {
        minPopulation: minPopulation.value
      },
      10
    );
    
    // Filter by selected location types
    searchResults.value = results.filter(result => 
      selectedLocationTypes.value.includes(result.type)
    );
    
    showSearchResults.value = true;
  } catch (error) {
    console.error('Error searching locations:', error);
  }
};

/**
 * Select a location from search results
 */
const selectLocation = (location: GeographicLocation) => {
  addLocation(location);
  locationSearch.value = '';
  searchResults.value = [];
  showSearchResults.value = false;
  
  // Pan map to selected location
  if (location.center && map) {
    map.setView([location.center.latitude, location.center.longitude], 8);
  }
};

/**
 * Add a location to selected locations
 */
const addLocation = (location: GeographicLocation) => {
  // Check if already selected
  const exists = selectedLocations.value.find(loc => loc.id === location.id);
  if (exists) return;
  
  selectedLocations.value.push(location);
  updateAudienceEstimate();
  updateModelValue();
};

/**
 * Remove a location from selected locations
 */
const removeLocation = (locationId: string) => {
  selectedLocations.value = selectedLocations.value.filter(loc => loc.id !== locationId);
  
  // Remove from map if it's a drawn shape
  if (locationId.startsWith('radius_') && radiusCircle) {
    map.removeLayer(radiusCircle);
    radiusCircle = null;
    selectedRadiusCenter.value = null;
  }
  
  updateAudienceEstimate();
  updateModelValue();
};

/**
 * Remove an exclusion
 */
const removeExclusion = (locationId: string) => {
  excludedLocations.value = excludedLocations.value.filter(loc => loc.id !== locationId);
  updateModelValue();
};

/**
 * Clear all selections
 */
const clearSelections = () => {
  selectedLocations.value = [];
  excludedLocations.value = [];
  estimatedAudience.value = 0;
  
  // Clear map layers
  if (radiusCircle) {
    map.removeLayer(radiusCircle);
    radiusCircle = null;
  }
  if (drawingPolygon) {
    map.removeLayer(drawingPolygon);
    drawingPolygon = null;
  }
  
  selectedRadiusCenter.value = null;
  polygonPoints.value = [];
  drawingMode.value = null;
  
  updateModelValue();
};

/**
 * Update audience size estimate
 */
const updateAudienceEstimate = async () => {
  if (selectedLocations.value.length === 0) {
    estimatedAudience.value = 0;
    confidence.value = 0;
    return;
  }
  
  try {
    const criteria: GeographicCriteria = {
      type: 'radius', // This will be determined by the location types
      locations: selectedLocations.value,
      exclusions: excludedLocations.value.length > 0 ? excludedLocations.value : undefined
    };
    
    const size = await calculateGeographicAudienceSize(criteria);
    estimatedAudience.value = size;
    confidence.value = 0.85; // Base confidence for geographic targeting
    
    emits('audienceEstimate', { size, confidence: confidence.value });
  } catch (error) {
    console.error('Error calculating audience size:', error);
  }
};

/**
 * Update the model value
 */
const updateModelValue = () => {
  if (selectedLocations.value.length === 0) {
    emits('update:modelValue', {
      type: 'radius',
      locations: []
    });
    return;
  }
  
  // Determine criteria type based on locations
  const hasRadius = selectedLocations.value.some(loc => loc.radius);
  const hasPolygon = selectedLocations.value.some(loc => loc.polygon);
  
  let type: 'radius' | 'polygon' | 'administrative' = 'administrative';
  if (hasRadius) type = 'radius';
  else if (hasPolygon) type = 'polygon';
  
  const criteria: GeographicCriteria = {
    type,
    locations: selectedLocations.value,
    exclusions: excludedLocations.value.length > 0 ? excludedLocations.value : undefined
  };
  
  emits('update:modelValue', criteria);
};

/**
 * Get location type icon
 */
const getLocationTypeIcon = (type: string) => {
  switch (type) {
    case 'country':
      return 'heroicons:globe-americas';
    case 'state':
      return 'heroicons:map';
    case 'city':
      return 'heroicons:building-office-2';
    case 'custom':
      return 'heroicons:map-pin';
    default:
      return 'heroicons:map-pin';
  }
};

/**
 * Get location description
 */
const getLocationDescription = (location: GeographicLocation) => {
  if (location.radius) {
    return `${location.radius}km radius`;
  }
  if (location.polygon) {
    return `Custom area (${location.polygon.length} points)`;
  }
  if (location.metadata) {
    const parts = [];
    if (location.metadata.state) parts.push(location.metadata.state);
    if (location.metadata.country) parts.push(location.metadata.country);
    return parts.join(', ');
  }
  return location.type;
};

/**
 * Format population number
 */
const formatPopulation = (population?: number) => {
  if (!population) return '';
  return population.toLocaleString();
};

/**
 * Format number with abbreviations
 */
const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

// Initialize model value from props
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue?.locations) {
      selectedLocations.value = [...newValue.locations];
      if (newValue.exclusions) {
        excludedLocations.value = [...newValue.exclusions];
      }
      updateAudienceEstimate();
    }
  },
  { immediate: true }
);

// Close search results when clicking outside
const handleClickOutside = (event: Event) => {
  if (!event.target) return;
  
  const target = event.target as Element;
  const searchContainer = target.closest('.geographic-targeting');
  
  if (!searchContainer) {
    showSearchResults.value = false;
  }
};

// Lifecycle
onMounted(() => {
  initializeMap();
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  if (map) {
    map.remove();
  }
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
/* Leaflet CSS will be imported globally */
.geographic-targeting {
  @apply w-full;
}

/* Custom range slider styling */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  @apply w-4 h-4 bg-blue-600 rounded-full cursor-pointer;
}

input[type="range"]::-moz-range-thumb {
  @apply w-4 h-4 bg-blue-600 rounded-full cursor-pointer border-0;
}
</style>