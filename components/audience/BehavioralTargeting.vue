<template>
  <div class="behavioral-targeting">
    <!-- Header -->
    <div class="mb-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
        Behavioral Targeting
      </h3>
      <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
        Target users based on their interests, behaviors, and online activity
      </p>
    </div>

    <div class="space-y-6">
      <!-- Interests & Categories -->
      <div class="behavioral-section">
        <div class="flex items-center justify-between mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Interests & Categories
          </label>
          <button
            @click="showInterestSearch = !showInterestSearch"
            class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700"
          >
            {{ showInterestSearch ? 'Hide' : 'Add Custom' }}
          </button>
        </div>

        <!-- Interest Search -->
        <div v-if="showInterestSearch" class="mb-4">
          <div class="relative">
            <input
              v-model="interestSearch"
              @input="searchInterests"
              @keydown.enter.prevent="addCustomInterest"
              type="text"
              placeholder="Search or add custom interests..."
              class="w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            />
            <Icon name="heroicons:magnifying-glass" class="absolute left-3 top-2.5 w-5 h-5 text-gray-400" />
            
            <!-- Interest suggestions -->
            <div
              v-if="interestSuggestions.length > 0"
              class="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg max-h-40 overflow-y-auto"
            >
              <div
                v-for="suggestion in interestSuggestions"
                :key="suggestion"
                @click="addInterest(suggestion)"
                class="px-4 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer text-sm"
              >
                {{ suggestion }}
              </div>
            </div>
          </div>
        </div>

        <!-- Interest Categories -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div
            v-for="category in interestCategories"
            :key="category.name"
            class="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
          >
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center space-x-2">
                <Icon :name="category.icon" class="w-5 h-5 text-gray-600 dark:text-gray-400" />
                <h4 class="font-medium text-gray-900 dark:text-white">
                  {{ category.name }}
                </h4>
              </div>
              <span class="text-xs text-gray-500 dark:text-gray-400">
                {{ getSelectedInterestsInCategory(category.interests).length }}/{{ category.interests.length }}
              </span>
            </div>

            <div class="space-y-2 max-h-32 overflow-y-auto">
              <label
                v-for="interest in category.interests"
                :key="interest"
                class="flex items-center space-x-2 cursor-pointer text-sm hover:bg-gray-50 dark:hover:bg-gray-800 p-1 rounded"
              >
                <input
                  v-model="selectedInterests"
                  :value="interest"
                  type="checkbox"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span class="text-gray-700 dark:text-gray-300">
                  {{ interest }}
                </span>
              </label>
            </div>
          </div>
        </div>

        <!-- Selected Interests Summary -->
        <div v-if="selectedInterests.length > 0" class="mt-4">
          <div class="flex flex-wrap gap-2">
            <span
              v-for="interest in selectedInterests"
              :key="interest"
              class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
            >
              {{ interest }}
              <button
                @click="removeInterest(interest)"
                class="ml-1 text-blue-600 hover:text-blue-800 dark:text-blue-300"
              >
                <Icon name="heroicons:x-mark" class="w-3 h-3" />
              </button>
            </span>
          </div>
        </div>
      </div>

      <!-- Device & Platform Preferences -->
      <div class="behavioral-section">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Device & Platform Preferences
        </label>

        <div class="space-y-4">
          <!-- Device Types -->
          <div>
            <label class="block text-xs text-gray-600 dark:text-gray-400 mb-2">
              Device Types
            </label>
            <div class="grid grid-cols-3 gap-3">
              <label
                v-for="device in deviceOptions"
                :key="device.value"
                :class="[
                  'flex items-center justify-center p-3 border rounded-lg cursor-pointer transition-colors',
                  selectedDeviceTypes.includes(device.value)
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                    : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'
                ]"
              >
                <input
                  v-model="selectedDeviceTypes"
                  :value="device.value"
                  type="checkbox"
                  class="sr-only"
                />
                <div class="text-center">
                  <Icon :name="device.icon" class="w-6 h-6 mx-auto mb-1 text-gray-600 dark:text-gray-400" />
                  <div class="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {{ device.label }}
                  </div>
                </div>
              </label>
            </div>
          </div>

          <!-- Operating Systems -->
          <div>
            <label class="block text-xs text-gray-600 dark:text-gray-400 mb-2">
              Operating Systems
            </label>
            <div class="flex flex-wrap gap-2">
              <label
                v-for="os in osOptions"
                :key="os.value"
                class="flex items-center space-x-2 cursor-pointer"
              >
                <input
                  v-model="selectedOperatingSystems"
                  :value="os.value"
                  type="checkbox"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span class="text-sm text-gray-700 dark:text-gray-300">
                  {{ os.label }}
                </span>
              </label>
            </div>
          </div>

          <!-- Browsers -->
          <div>
            <label class="block text-xs text-gray-600 dark:text-gray-400 mb-2">
              Browsers
            </label>
            <div class="flex flex-wrap gap-2">
              <label
                v-for="browser in browserOptions"
                :key="browser.value"
                class="flex items-center space-x-2 cursor-pointer"
              >
                <input
                  v-model="selectedBrowsers"
                  :value="browser.value"
                  type="checkbox"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span class="text-sm text-gray-700 dark:text-gray-300">
                  {{ browser.label }}
                </span>
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- User Engagement Patterns -->
      <div class="behavioral-section">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          User Engagement Patterns
        </label>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Engagement Level -->
          <div>
            <label class="block text-xs text-gray-600 dark:text-gray-400 mb-2">
              Engagement Level
            </label>
            <div class="space-y-2">
              <label
                v-for="level in engagementLevels"
                :key="level.value"
                class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer"
              >
                <input
                  v-model="selectedEngagementLevels"
                  :value="level.value"
                  type="checkbox"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <div class="flex-1">
                  <div class="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {{ level.label }}
                  </div>
                  <div class="text-xs text-gray-500 dark:text-gray-400">
                    {{ level.description }}
                  </div>
                </div>
              </label>
            </div>
          </div>

          <!-- Visit Frequency -->
          <div>
            <label class="block text-xs text-gray-600 dark:text-gray-400 mb-2">
              Visit Frequency
            </label>
            <div class="space-y-2">
              <label
                v-for="frequency in visitFrequencies"
                :key="frequency.value"
                class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer"
              >
                <input
                  v-model="selectedVisitFrequencies"
                  :value="frequency.value"
                  type="checkbox"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <div class="flex-1">
                  <div class="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {{ frequency.label }}
                  </div>
                  <div class="text-xs text-gray-500 dark:text-gray-400">
                    {{ frequency.description }}
                  </div>
                </div>
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- Time on Site -->
      <div class="behavioral-section">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Time on Site
        </label>

        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-xs text-gray-600 dark:text-gray-400 mb-1">
              Minimum Time (seconds)
            </label>
            <input
              v-model.number="timeOnSite.min"
              @input="updateTimeOnSite"
              type="number"
              min="0"
              step="30"
              placeholder="0"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>
          
          <div>
            <label class="block text-xs text-gray-600 dark:text-gray-400 mb-1">
              Maximum Time (seconds)
            </label>
            <input
              v-model.number="timeOnSite.max"
              @input="updateTimeOnSite"
              type="number"
              min="0"
              step="30"
              placeholder="No limit"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>
        </div>

        <!-- Time presets -->
        <div class="mt-3 flex flex-wrap gap-2">
          <button
            v-for="preset in timePresets"
            :key="preset.label"
            @click="setTimePreset(preset)"
            class="px-3 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 transition-colors"
          >
            {{ preset.label }}
          </button>
        </div>
      </div>

      <!-- Advanced Behavioral Patterns -->
      <div class="behavioral-section">
        <div class="flex items-center justify-between mb-3">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Advanced Behavioral Patterns
          </label>
          <button
            @click="showAdvancedBehaviors = !showAdvancedBehaviors"
            class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700"
          >
            {{ showAdvancedBehaviors ? 'Hide' : 'Show Advanced' }}
          </button>
        </div>

        <div v-if="showAdvancedBehaviors" class="space-y-4">
          <!-- Custom Behavior Patterns -->
          <div>
            <label class="block text-xs text-gray-600 dark:text-gray-400 mb-2">
              Custom Behavior Patterns
            </label>
            <div class="space-y-2">
              <div
                v-for="(pattern, index) in customBehaviorPatterns"
                :key="index"
                class="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
              >
                <div class="flex-1 grid grid-cols-3 gap-3">
                  <select
                    v-model="pattern.type"
                    class="text-sm border border-gray-300 rounded px-2 py-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  >
                    <option value="page_view">Page View</option>
                    <option value="click">Click</option>
                    <option value="interaction">Interaction</option>
                    <option value="purchase">Purchase</option>
                    <option value="signup">Sign Up</option>
                    <option value="download">Download</option>
                  </select>
                  
                  <input
                    v-model="pattern.action"
                    type="text"
                    placeholder="Action description"
                    class="text-sm border border-gray-300 rounded px-2 py-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                  
                  <select
                    v-model="pattern.frequency"
                    class="text-sm border border-gray-300 rounded px-2 py-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  >
                    <option value="once">Once</option>
                    <option value="multiple">Multiple times</option>
                    <option value="regular">Regularly</option>
                  </select>
                </div>
                
                <button
                  @click="removeCustomPattern(index)"
                  class="text-red-600 hover:text-red-700 dark:text-red-400"
                >
                  <Icon name="heroicons:trash" class="w-4 h-4" />
                </button>
              </div>
              
              <button
                @click="addCustomPattern"
                class="w-full px-3 py-2 text-sm text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
              >
                Add Custom Pattern
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Behavioral Impact Summary -->
    <div class="mt-6 p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
      <div class="flex items-start space-x-3">
        <Icon name="heroicons:chart-bar" class="w-5 h-5 text-purple-600 dark:text-purple-400 flex-shrink-0 mt-0.5" />
        <div>
          <h4 class="font-medium text-purple-900 dark:text-purple-100 mb-1">
            Behavioral Targeting Impact
          </h4>
          <p class="text-sm text-purple-700 dark:text-purple-200">
            {{ getBehavioralImpactMessage() }}
          </p>
          <div class="mt-2 flex items-center space-x-4 text-xs text-purple-600 dark:text-purple-300">
            <div class="flex items-center space-x-1">
              <Icon name="heroicons:tag" class="w-3 h-3" />
              <span>{{ selectedInterests.length }} interests</span>
            </div>
            <div class="flex items-center space-x-1">
              <Icon name="heroicons:device-phone-mobile" class="w-3 h-3" />
              <span>{{ selectedDeviceTypes.length }} device types</span>
            </div>
            <div class="flex items-center space-x-1">
              <Icon name="heroicons:chart-pie" class="w-3 h-3" />
              <span>~{{ estimatedReduction }}% audience reduction</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Clear All Button -->
    <div class="mt-4 flex justify-end">
      <button
        @click="clearAllFilters"
        v-if="hasAnyFilters"
        class="px-4 py-2 text-sm font-medium text-red-600 hover:text-red-700 dark:text-red-400 transition-colors"
      >
        Clear All Behavioral Filters
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import type { BehavioralCriteria, UserBehaviorPattern } from '~/types/audience-segmentation';

// Props and emits
interface Props {
  modelValue?: BehavioralCriteria;
  readonly?: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: BehavioralCriteria): void;
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
});

const emits = defineEmits<Emits>();

// State
const showInterestSearch = ref(false);
const showAdvancedBehaviors = ref(false);
const interestSearch = ref('');
const interestSuggestions = ref<string[]>([]);

// Interest state
const selectedInterests = ref<string[]>([]);

// Device & Platform state
const selectedDeviceTypes = ref<string[]>([]);
const selectedOperatingSystems = ref<string[]>([]);
const selectedBrowsers = ref<string[]>([]);

// Engagement state
const selectedEngagementLevels = ref<string[]>([]);
const selectedVisitFrequencies = ref<string[]>([]);

// Time on site state
const timeOnSite = ref({ min: undefined as number | undefined, max: undefined as number | undefined });

// Custom behavior patterns
const customBehaviorPatterns = ref<Array<{
  type: string;
  action: string;
  frequency: string;
}>>([]);

// Options and data
const interestCategories = [
  {
    name: 'Business & Professional',
    icon: 'heroicons:briefcase',
    interests: [
      'Business Development', 'Marketing', 'Sales', 'Finance', 'Consulting',
      'Real Estate', 'Insurance', 'Banking', 'Investment', 'Entrepreneurship'
    ]
  },
  {
    name: 'Technology',
    icon: 'heroicons:computer-desktop',
    interests: [
      'Software Development', 'Web Development', 'Mobile Apps', 'AI/ML',
      'Cloud Computing', 'Cybersecurity', 'DevOps', 'Data Science', 'Blockchain'
    ]
  },
  {
    name: 'Health & Wellness',
    icon: 'heroicons:heart',
    interests: [
      'Fitness', 'Nutrition', 'Mental Health', 'Yoga', 'Medical',
      'Supplements', 'Weight Loss', 'Meditation', 'Healthcare'
    ]
  },
  {
    name: 'Education & Learning',
    icon: 'heroicons:academic-cap',
    interests: [
      'Online Courses', 'Professional Development', 'Certification',
      'Language Learning', 'Skill Development', 'Training', 'Coaching'
    ]
  },
  {
    name: 'Lifestyle & Entertainment',
    icon: 'heroicons:film',
    interests: [
      'Travel', 'Food & Dining', 'Fashion', 'Music', 'Movies',
      'Gaming', 'Sports', 'Books', 'Art', 'Photography'
    ]
  },
  {
    name: 'Shopping & E-commerce',
    icon: 'heroicons:shopping-bag',
    interests: [
      'Online Shopping', 'Fashion Shopping', 'Electronics', 'Home & Garden',
      'Luxury Goods', 'Deal Hunting', 'Product Reviews', 'Brand Loyalty'
    ]
  }
];

const deviceOptions = [
  { value: 'mobile', label: 'Mobile', icon: 'heroicons:device-phone-mobile' },
  { value: 'tablet', label: 'Tablet', icon: 'heroicons:device-tablet' },
  { value: 'desktop', label: 'Desktop', icon: 'heroicons:computer-desktop' }
];

const osOptions = [
  { value: 'ios', label: 'iOS' },
  { value: 'android', label: 'Android' },
  { value: 'windows', label: 'Windows' },
  { value: 'macos', label: 'macOS' },
  { value: 'linux', label: 'Linux' }
];

const browserOptions = [
  { value: 'chrome', label: 'Chrome' },
  { value: 'safari', label: 'Safari' },
  { value: 'firefox', label: 'Firefox' },
  { value: 'edge', label: 'Edge' },
  { value: 'other', label: 'Other' }
];

const engagementLevels = [
  {
    value: 'low',
    label: 'Low Engagement',
    description: 'Quick visits, minimal interaction'
  },
  {
    value: 'medium',
    label: 'Medium Engagement',
    description: 'Regular visits, some interaction'
  },
  {
    value: 'high',
    label: 'High Engagement',
    description: 'Frequent visits, active interaction'
  }
];

const visitFrequencies = [
  {
    value: 'new',
    label: 'New Visitors',
    description: 'First-time visitors'
  },
  {
    value: 'returning',
    label: 'Returning Visitors',
    description: 'Previous visitors returning'
  },
  {
    value: 'frequent',
    label: 'Frequent Visitors',
    description: 'Regular, loyal visitors'
  }
];

const timePresets = [
  { label: 'Quick Visit (0-30s)', min: 0, max: 30 },
  { label: 'Short Visit (30s-2m)', min: 30, max: 120 },
  { label: 'Medium Visit (2m-5m)', min: 120, max: 300 },
  { label: 'Long Visit (5m+)', min: 300, max: undefined },
  { label: 'Very Long Visit (10m+)', min: 600, max: undefined }
];

// Interest suggestions based on search
const allAvailableInterests = computed(() => {
  return interestCategories.flatMap(category => category.interests);
});

// Computed behavioral criteria
const behavioralCriteria = computed<BehavioralCriteria>(() => {
  const criteria: BehavioralCriteria = {};

  // Interests
  if (selectedInterests.value.length > 0) {
    criteria.interests = [...selectedInterests.value];
  }

  // Device types
  if (selectedDeviceTypes.value.length > 0 && selectedDeviceTypes.value.length < 3) {
    criteria.deviceTypes = selectedDeviceTypes.value as any[];
  }

  // Operating systems
  if (selectedOperatingSystems.value.length > 0 && selectedOperatingSystems.value.length < 5) {
    criteria.operatingSystems = selectedOperatingSystems.value as any[];
  }

  // Browsers
  if (selectedBrowsers.value.length > 0 && selectedBrowsers.value.length < 5) {
    criteria.browsers = selectedBrowsers.value as any[];
  }

  // Engagement level
  if (selectedEngagementLevels.value.length > 0 && selectedEngagementLevels.value.length < 3) {
    criteria.engagementLevel = selectedEngagementLevels.value as any[];
  }

  // Visit frequency
  if (selectedVisitFrequencies.value.length > 0 && selectedVisitFrequencies.value.length < 3) {
    criteria.visitFrequency = selectedVisitFrequencies.value as any[];
  }

  // Time on site
  if (timeOnSite.value.min !== undefined || timeOnSite.value.max !== undefined) {
    criteria.timeOnSite = {
      min: timeOnSite.value.min,
      max: timeOnSite.value.max
    };
  }

  // Custom behavior patterns
  if (customBehaviorPatterns.value.length > 0) {
    criteria.behaviors = customBehaviorPatterns.value.map(pattern => ({
      type: pattern.type as any,
      action: pattern.action,
      frequency: pattern.frequency === 'once' ? { min: 1, max: 1, period: 'week' as const } :
                pattern.frequency === 'multiple' ? { min: 2, max: 10, period: 'week' as const } :
                { min: 1, max: 1, period: 'day' as const }
    }));
  }

  return criteria;
});

// Computed properties for UI
const hasAnyFilters = computed(() => {
  return Object.keys(behavioralCriteria.value).length > 0;
});

const estimatedReduction = computed(() => {
  let reduction = 0;
  
  // Interest-based reduction
  if (selectedInterests.value.length > 0) {
    reduction += Math.min(60, selectedInterests.value.length * 5); // Up to 60% for interests
  }
  
  // Device type reduction
  if (selectedDeviceTypes.value.length > 0 && selectedDeviceTypes.value.length < 3) {
    const deviceShares = { mobile: 0.6, desktop: 0.3, tablet: 0.1 };
    const selectedShare = selectedDeviceTypes.value.reduce((sum, device) => 
      sum + (deviceShares[device as keyof typeof deviceShares] || 0.1), 0);
    reduction += (1 - selectedShare) * 100;
  }
  
  // Engagement level reduction
  if (selectedEngagementLevels.value.length > 0 && selectedEngagementLevels.value.length < 3) {
    reduction += (1 - (selectedEngagementLevels.value.length / 3)) * 20;
  }
  
  // Custom patterns add specificity
  if (customBehaviorPatterns.value.length > 0) {
    reduction += customBehaviorPatterns.value.length * 10;
  }
  
  return Math.min(90, Math.round(reduction));
});

// Methods
const searchInterests = () => {
  if (!interestSearch.value.trim()) {
    interestSuggestions.value = [];
    return;
  }
  
  const searchTerm = interestSearch.value.toLowerCase();
  interestSuggestions.value = allAvailableInterests.value
    .filter(interest => 
      interest.toLowerCase().includes(searchTerm) &&
      !selectedInterests.value.includes(interest)
    )
    .slice(0, 5);
};

const addInterest = (interest: string) => {
  if (!selectedInterests.value.includes(interest)) {
    selectedInterests.value.push(interest);
  }
  interestSearch.value = '';
  interestSuggestions.value = [];
};

const addCustomInterest = () => {
  const customInterest = interestSearch.value.trim();
  if (customInterest && !selectedInterests.value.includes(customInterest)) {
    selectedInterests.value.push(customInterest);
    interestSearch.value = '';
    interestSuggestions.value = [];
  }
};

const removeInterest = (interest: string) => {
  selectedInterests.value = selectedInterests.value.filter(i => i !== interest);
};

const getSelectedInterestsInCategory = (categoryInterests: string[]) => {
  return selectedInterests.value.filter(interest => categoryInterests.includes(interest));
};

const updateTimeOnSite = () => {
  updateModelValue();
};

const setTimePreset = (preset: { label: string; min: number; max?: number }) => {
  timeOnSite.value = { min: preset.min, max: preset.max };
  updateModelValue();
};

const addCustomPattern = () => {
  customBehaviorPatterns.value.push({
    type: 'page_view',
    action: '',
    frequency: 'once'
  });
};

const removeCustomPattern = (index: number) => {
  customBehaviorPatterns.value.splice(index, 1);
};

const getBehavioralImpactMessage = () => {
  const interestCount = selectedInterests.value.length;
  const deviceCount = selectedDeviceTypes.value.length;
  const patternCount = customBehaviorPatterns.value.length;
  
  if (interestCount === 0 && deviceCount === 0 && patternCount === 0) {
    return 'No behavioral filters applied. Your ads will reach users with all interests and behaviors.';
  } else if (interestCount <= 3 && deviceCount >= 2) {
    return 'Light behavioral targeting applied. This maintains broad reach while adding some behavioral relevance.';
  } else if (interestCount <= 8 || patternCount <= 2) {
    return 'Moderate behavioral targeting. This provides good balance between precision and audience size.';
  } else {
    return 'Heavy behavioral targeting applied. This will significantly increase ad relevance but reduce audience size.';
  }
};

const clearAllFilters = () => {
  selectedInterests.value = [];
  selectedDeviceTypes.value = [];
  selectedOperatingSystems.value = [];
  selectedBrowsers.value = [];
  selectedEngagementLevels.value = [];
  selectedVisitFrequencies.value = [];
  timeOnSite.value = { min: undefined, max: undefined };
  customBehaviorPatterns.value = [];
  updateModelValue();
};

const updateModelValue = () => {
  emits('update:modelValue', behavioralCriteria.value);
};

// Watch for external model value changes
watch(
  () => props.modelValue,
  (newValue) => {
    if (!newValue) return;
    
    // Update interests
    if (newValue.interests) {
      selectedInterests.value = [...newValue.interests];
    }
    
    // Update device types
    if (newValue.deviceTypes) {
      selectedDeviceTypes.value = [...newValue.deviceTypes];
    }
    
    // Update operating systems
    if (newValue.operatingSystems) {
      selectedOperatingSystems.value = [...newValue.operatingSystems];
    }
    
    // Update browsers
    if (newValue.browsers) {
      selectedBrowsers.value = [...newValue.browsers];
    }
    
    // Update engagement levels
    if (newValue.engagementLevel) {
      selectedEngagementLevels.value = [...newValue.engagementLevel];
    }
    
    // Update visit frequencies
    if (newValue.visitFrequency) {
      selectedVisitFrequencies.value = [...newValue.visitFrequency];
    }
    
    // Update time on site
    if (newValue.timeOnSite) {
      timeOnSite.value = {
        min: newValue.timeOnSite.min,
        max: newValue.timeOnSite.max
      };
    }
    
    // Update behavior patterns
    if (newValue.behaviors) {
      customBehaviorPatterns.value = newValue.behaviors.map(behavior => ({
        type: behavior.type,
        action: behavior.action,
        frequency: behavior.frequency?.min === 1 && behavior.frequency?.max === 1 ? 'once' :
                 behavior.frequency?.period === 'day' ? 'regular' : 'multiple'
      }));
    }
  },
  { immediate: true, deep: true }
);

// Watch for changes in reactive data and emit updates
watch(
  [
    selectedInterests,
    selectedDeviceTypes,
    selectedOperatingSystems,
    selectedBrowsers,
    selectedEngagementLevels,
    selectedVisitFrequencies,
    customBehaviorPatterns
  ],
  () => {
    updateModelValue();
  },
  { deep: true }
);
</script>

<style scoped>
.behavioral-section {
  @apply pb-6 border-b border-gray-200 dark:border-gray-700 last:border-b-0;
}
</style>