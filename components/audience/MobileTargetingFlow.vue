<template>
  <div class="mobile-targeting-flow">
    <!-- Progress Header -->
    <div class="sticky top-0 z-40 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
      <div class="flex items-center justify-between">
        <button
          class="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
          @click="$emit('close')"
        >
          <Icon name="heroicons:x-mark" class="w-6 h-6" />
        </button>
        
        <div class="flex-1 mx-4">
          <div class="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
            <span>Step {{ currentStep }} of {{ totalSteps }}</span>
            <span>{{ Math.round((currentStep / totalSteps) * 100) }}%</span>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              class="bg-blue-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${(currentStep / totalSteps) * 100}%` }"
            ></div>
          </div>
        </div>
        
        <button
          class="text-sm font-medium text-blue-600 dark:text-blue-400"
          @click="saveAndExit"
        >
          Save
        </button>
      </div>
    </div>

    <!-- Step Content -->
    <div class="flex-1 overflow-y-auto">
      <!-- Step 1: Audience Name -->
      <div v-if="currentStep === 1" class="step-content">
        <div class="px-4 py-6">
          <div class="text-center mb-8">
            <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon name="heroicons:users" class="w-8 h-8 text-blue-600 dark:text-blue-400" />
            </div>
            <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-2">
              Name Your Audience
            </h2>
            <p class="text-gray-600 dark:text-gray-400">
              Give your audience segment a descriptive name
            </p>
          </div>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Audience Name *
              </label>
              <input
                ref="nameInput"
                v-model="audienceName"
                type="text"
                placeholder="e.g., Tech Millennials in NYC"
                class="w-full px-4 py-3 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                @keydown.enter="nextStep"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Description (Optional)
              </label>
              <textarea
                v-model="audienceDescription"
                rows="3"
                placeholder="Describe this audience and its purpose..."
                class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-600 dark:text-white"
              ></textarea>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 2: Location Selection -->
      <div v-if="currentStep === 2" class="step-content">
        <div class="px-4 py-6">
          <div class="text-center mb-6">
            <div class="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon name="heroicons:map-pin" class="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
            <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-2">
              Choose Locations
            </h2>
            <p class="text-gray-600 dark:text-gray-400">
              Where should your ads be shown?
            </p>
          </div>

          <!-- Quick Location Options -->
          <div class="mb-6">
            <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Quick Select
            </h3>
            <div class="grid grid-cols-2 gap-3">
              <button
                v-for="quickLocation in quickLocationOptions"
                :key="quickLocation.id"
                class="p-4 border-2 rounded-xl text-left transition-all" :class="[
                  isLocationSelected(quickLocation.id)
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
                ]"
                @click="selectQuickLocation(quickLocation)"
              >
                <div class="flex items-center space-x-3">
                  <Icon :name="quickLocation.icon" class="w-6 h-6 text-gray-600 dark:text-gray-400" />
                  <div>
                    <div class="font-medium text-gray-900 dark:text-white">
                      {{ quickLocation.name }}
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                      {{ quickLocation.description }}
                    </div>
                  </div>
                </div>
              </button>
            </div>
          </div>

          <!-- Location Search -->
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Search Specific Locations
            </label>
            <div class="relative">
              <input
                v-model="locationSearch"
                type="text"
                placeholder="Search cities, states, countries..."
                class="w-full px-4 py-3 pl-12 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                @input="performLocationSearch"
              />
              <Icon name="heroicons:magnifying-glass" class="absolute left-4 top-3.5 w-5 h-5 text-gray-400" />
            </div>
            
            <!-- Search Results -->
            <div
              v-if="locationSearchResults.length > 0"
              class="mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-lg max-h-48 overflow-y-auto"
            >
              <button
                v-for="result in locationSearchResults"
                :key="result.id"
                class="w-full px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-100 dark:border-gray-700 last:border-b-0"
                @click="selectSearchLocation(result)"
              >
                <div class="font-medium text-gray-900 dark:text-white">
                  {{ result.name }}
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">
                  {{ result.metadata?.country }}
                  <span v-if="result.metadata?.state">, {{ result.metadata.state }}</span>
                </div>
              </button>
            </div>
          </div>

          <!-- Selected Locations -->
          <div v-if="selectedLocations.length > 0">
            <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Selected Locations ({{ selectedLocations.length }})
            </h3>
            <div class="space-y-2">
              <div
                v-for="location in selectedLocations"
                :key="location.id"
                class="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg"
              >
                <div class="flex items-center space-x-2">
                  <Icon name="heroicons:map-pin" class="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  <span class="font-medium text-gray-900 dark:text-white">
                    {{ location.name }}
                  </span>
                </div>
                <button
                  class="p-1 text-gray-400 hover:text-red-600"
                  @click="removeLocation(location.id)"
                >
                  <Icon name="heroicons:x-mark" class="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 3: Demographics -->
      <div v-if="currentStep === 3" class="step-content">
        <div class="px-4 py-6">
          <div class="text-center mb-6">
            <div class="w-16 h-16 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon name="heroicons:user-group" class="w-8 h-8 text-purple-600 dark:text-purple-400" />
            </div>
            <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-2">
              Who's Your Audience?
            </h2>
            <p class="text-gray-600 dark:text-gray-400">
              Select age range and other demographics
            </p>
          </div>

          <!-- Age Range -->
          <div class="mb-8">
            <div class="flex items-center justify-between mb-4">
              <label class="text-lg font-medium text-gray-900 dark:text-white">
                Age Range
              </label>
              <span class="text-sm text-gray-600 dark:text-gray-400">
                {{ demographicFilters.ageRange.min }} - {{ demographicFilters.ageRange.max }} years
              </span>
            </div>
            
            <!-- Age Slider -->
            <div class="px-4 mb-4">
              <div class="relative">
                <input
                  v-model.number="demographicFilters.ageRange.min"
                  type="range"
                  min="13"
                  max="80"
                  class="absolute w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700 z-10"
                  @input="updateAgeRange"
                />
                <input
                  v-model.number="demographicFilters.ageRange.max"
                  type="range"
                  min="13"
                  max="80"
                  class="absolute w-full h-2 bg-transparent appearance-none cursor-pointer z-20"
                  @input="updateAgeRange"
                />
              </div>
            </div>
            
            <!-- Age Presets -->
            <div class="flex flex-wrap gap-2">
              <button
                v-for="preset in agePresets"
                :key="preset.label"
                class="px-4 py-2 text-sm font-medium rounded-full transition-colors" :class="[
                  isAgePresetActive(preset)
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
                ]"
                @click="setAgePreset(preset)"
              >
                {{ preset.label }}
              </button>
            </div>
          </div>

          <!-- Gender -->
          <div class="mb-8">
            <label class="block text-lg font-medium text-gray-900 dark:text-white mb-4">
              Gender
            </label>
            <div class="grid grid-cols-2 gap-3">
              <button
                v-for="gender in genderOptions"
                :key="gender.value"
                class="p-4 border-2 rounded-xl text-center transition-all" :class="[
                  demographicFilters.selectedGenders.includes(gender.value)
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                    : 'border-gray-200 dark:border-gray-700'
                ]"
                @click="toggleGender(gender.value)"
              >
                <div class="font-medium text-gray-900 dark:text-white">
                  {{ gender.label }}
                </div>
              </button>
            </div>
          </div>

          <!-- Skip Demographics Button -->
          <button
            class="w-full py-3 text-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
            @click="skipDemographics"
          >
            Skip Demographics (Target Everyone)
          </button>
        </div>
      </div>

      <!-- Step 4: Interests -->
      <div v-if="currentStep === 4" class="step-content">
        <div class="px-4 py-6">
          <div class="text-center mb-6">
            <div class="w-16 h-16 bg-orange-100 dark:bg-orange-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon name="heroicons:heart" class="w-8 h-8 text-orange-600 dark:text-orange-400" />
            </div>
            <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-2">
              What Are They Into?
            </h2>
            <p class="text-gray-600 dark:text-gray-400">
              Select interests and behaviors
            </p>
          </div>

          <!-- Interest Categories -->
          <div class="space-y-6">
            <div
              v-for="category in interestCategories"
              :key="category.name"
              class="border border-gray-200 dark:border-gray-700 rounded-xl p-4"
            >
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center space-x-2">
                  <Icon :name="category.icon" class="w-5 h-5 text-gray-600 dark:text-gray-400" />
                  <h3 class="font-medium text-gray-900 dark:text-white">
                    {{ category.name }}
                  </h3>
                </div>
                <span class="text-xs text-gray-500 dark:text-gray-400">
                  {{ getSelectedInterestsInCategory(category.interests).length }}/{{ category.interests.length }}
                </span>
              </div>

              <div class="grid grid-cols-1 gap-2">
                <button
                  v-for="interest in category.interests.slice(0, showAllInterests[category.name] ? category.interests.length : 3)"
                  :key="interest"
                  class="p-3 text-left rounded-lg transition-all" :class="[
                    behavioralFilters.selectedInterests.includes(interest)
                      ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800'
                      : 'bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700'
                  ]"
                  @click="toggleInterest(interest)"
                >
                  <div class="flex items-center justify-between">
                    <span class="font-medium text-gray-900 dark:text-white">
                      {{ interest }}
                    </span>
                    <Icon
                      v-if="behavioralFilters.selectedInterests.includes(interest)"
                      name="heroicons:check-circle"
                      class="w-5 h-5 text-blue-600 dark:text-blue-400"
                    />
                  </div>
                </button>
              </div>

              <button
                v-if="category.interests.length > 3"
                class="w-full mt-2 py-2 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700"
                @click="showAllInterests[category.name] = !showAllInterests[category.name]"
              >
                {{ showAllInterests[category.name] ? 'Show Less' : `Show ${category.interests.length - 3} More` }}
              </button>
            </div>
          </div>

          <!-- Selected Interests Summary -->
          <div
            v-if="behavioralFilters.selectedInterests.length > 0"
            class="mt-6 p-4 bg-orange-50 dark:bg-orange-900/20 rounded-xl"
          >
            <h4 class="font-medium text-orange-900 dark:text-orange-100 mb-2">
              Selected Interests ({{ behavioralFilters.selectedInterests.length }})
            </h4>
            <div class="flex flex-wrap gap-2">
              <span
                v-for="interest in behavioralFilters.selectedInterests.slice(0, 5)"
                :key="interest"
                class="px-2 py-1 text-xs font-medium bg-orange-100 dark:bg-orange-800 text-orange-800 dark:text-orange-200 rounded-full"
              >
                {{ interest }}
              </span>
              <span
                v-if="behavioralFilters.selectedInterests.length > 5"
                class="px-2 py-1 text-xs font-medium bg-orange-100 dark:bg-orange-800 text-orange-800 dark:text-orange-200 rounded-full"
              >
                +{{ behavioralFilters.selectedInterests.length - 5 }} more
              </span>
            </div>
          </div>

          <!-- Skip Interests Button -->
          <button
            class="w-full mt-6 py-3 text-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
            @click="skipInterests"
          >
            Skip Interests (Target All Interests)
          </button>
        </div>
      </div>

      <!-- Step 5: Summary & Save -->
      <div v-if="currentStep === 5" class="step-content">
        <div class="px-4 py-6">
          <div class="text-center mb-6">
            <div class="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon name="heroicons:check-circle" class="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
            <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-2">
              You're All Set!
            </h2>
            <p class="text-gray-600 dark:text-gray-400">
              Review your audience settings
            </p>
          </div>

          <!-- Audience Summary -->
          <div class="space-y-4 mb-8">
            <!-- Name -->
            <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-xl">
              <div class="font-medium text-gray-900 dark:text-white mb-1">
                Audience Name
              </div>
              <div class="text-gray-600 dark:text-gray-400">
                {{ audienceName || 'Unnamed Audience' }}
              </div>
            </div>

            <!-- Locations -->
            <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-xl">
              <div class="font-medium text-gray-900 dark:text-white mb-2">
                Locations ({{ selectedLocations.length }})
              </div>
              <div class="space-y-1">
                <div
                  v-for="location in selectedLocations.slice(0, 3)"
                  :key="location.id"
                  class="text-sm text-gray-600 dark:text-gray-400"
                >
                  {{ location.name }}
                </div>
                <div
                  v-if="selectedLocations.length > 3"
                  class="text-sm text-gray-500 dark:text-gray-500"
                >
                  +{{ selectedLocations.length - 3 }} more locations
                </div>
              </div>
            </div>

            <!-- Demographics -->
            <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-xl">
              <div class="font-medium text-gray-900 dark:text-white mb-2">
                Demographics
              </div>
              <div class="space-y-1 text-sm text-gray-600 dark:text-gray-400">
                <div>Ages {{ demographicFilters.ageRange.min }} - {{ demographicFilters.ageRange.max }}</div>
                <div v-if="demographicFilters.selectedGenders.length > 0">
                  {{ demographicFilters.selectedGenders.join(', ') }}
                </div>
                <div v-else>All genders</div>
              </div>
            </div>

            <!-- Interests -->
            <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-xl">
              <div class="font-medium text-gray-900 dark:text-white mb-2">
                Interests ({{ behavioralFilters.selectedInterests.length }})
              </div>
              <div v-if="behavioralFilters.selectedInterests.length > 0" class="flex flex-wrap gap-1">
                <span
                  v-for="interest in behavioralFilters.selectedInterests.slice(0, 4)"
                  :key="interest"
                  class="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 rounded-full"
                >
                  {{ interest }}
                </span>
                <span
                  v-if="behavioralFilters.selectedInterests.length > 4"
                  class="px-2 py-1 text-xs bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-full"
                >
                  +{{ behavioralFilters.selectedInterests.length - 4 }}
                </span>
              </div>
              <div v-else class="text-sm text-gray-600 dark:text-gray-400">
                All interests
              </div>
            </div>
          </div>

          <!-- Estimated Audience -->
          <div class="p-6 bg-gradient-to-r from-blue-50 to-green-50 dark:from-blue-900/20 dark:to-green-900/20 rounded-xl mb-6">
            <div class="text-center">
              <div class="text-3xl font-bold text-gray-900 dark:text-white mb-1">
                {{ formatNumber(estimatedAudienceSize) }}
              </div>
              <div class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Estimated Audience Size
              </div>
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <div class="font-medium text-gray-900 dark:text-white">
                    ${{ estimatedCPM.toFixed(2) }}
                  </div>
                  <div class="text-gray-600 dark:text-gray-400">Est. CPM</div>
                </div>
                <div>
                  <div class="font-medium text-gray-900 dark:text-white">
                    {{ (confidence * 100).toFixed(0) }}%
                  </div>
                  <div class="text-gray-600 dark:text-gray-400">Confidence</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Navigation Footer -->
    <div class="sticky bottom-0 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 px-4 py-4">
      <div class="flex items-center justify-between">
        <button
          v-if="currentStep > 1"
          class="flex items-center space-x-2 px-6 py-3 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
          @click="previousStep"
        >
          <Icon name="heroicons:chevron-left" class="w-5 h-5" />
          <span>Back</span>
        </button>
        <div v-else class="w-20"></div>

        <button
          :disabled="!canProceed"
          class="flex items-center space-x-2 px-8 py-3 bg-blue-600 text-white font-medium rounded-xl hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          @click="currentStep === totalSteps ? createAudience() : nextStep()"
        >
          <span>{{ currentStep === totalSteps ? 'Create Audience' : 'Next' }}</span>
          <Icon
            :name="currentStep === totalSteps ? 'heroicons:check' : 'heroicons:chevron-right'"
            class="w-5 h-5"
          />
        </button>
      </div>
    </div>

    <!-- Loading Overlay -->
    <div
      v-if="isCreating"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    >
      <div class="bg-white dark:bg-gray-800 rounded-xl p-6 text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p class="text-gray-900 dark:text-white font-medium">
          Creating your audience...
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted } from 'vue';

import { useAudienceEstimation } from '~/composables/useAudienceEstimation';
import { useAudienceSegmentation } from '~/composables/useAudienceSegmentation';
import { useGeospatialIndex } from '~/composables/useGeospatialIndex';
import type { GeographicLocation } from '~/types/audience-segmentation';

// Emits
interface Emits {
  (e: 'close'): void;
  (e: 'audienceCreated', audienceId: string): void;
}

const emits = defineEmits<Emits>();

// Composables
const { createSegment } = useAudienceSegmentation();
const { searchLocations } = useGeospatialIndex();
const { estimateAudience } = useAudienceEstimation();

// Flow state
const currentStep = ref(1);
const totalSteps = 5;
const isCreating = ref(false);

// Form inputs
const audienceName = ref('');
const audienceDescription = ref('');
const nameInput = ref<HTMLInputElement>();

// Location state
const locationSearch = ref('');
const locationSearchResults = ref<GeographicLocation[]>([]);
const selectedLocations = ref<GeographicLocation[]>([]);

// Demographic state
const demographicFilters = ref({
  ageRange: { min: 18, max: 65 },
  selectedGenders: [] as string[]
});

// Behavioral state
const behavioralFilters = ref({
  selectedInterests: [] as string[]
});

const showAllInterests = ref<Record<string, boolean>>({});

// Estimation state
const estimatedAudienceSize = ref(0);
const estimatedCPM = ref(2.5);
const confidence = ref(0.8);

// Options data
const quickLocationOptions = [
  {
    id: 'worldwide',
    name: 'Worldwide',
    description: 'Target all countries',
    icon: 'heroicons:globe-americas'
  },
  {
    id: 'usa',
    name: 'United States',
    description: 'All US states',
    icon: 'heroicons:flag'
  },
  {
    id: 'canada',
    name: 'Canada',
    description: 'All provinces',
    icon: 'heroicons:flag'
  },
  {
    id: 'english-speaking',
    name: 'English Speaking',
    description: 'US, UK, CA, AU',
    icon: 'heroicons:language'
  }
];

const agePresets = [
  { label: 'Gen Z', min: 18, max: 26 },
  { label: 'Millennials', min: 27, max: 42 },
  { label: 'Gen X', min: 43, max: 58 },
  { label: 'Boomers', min: 59, max: 77 },
  { label: 'Young Adults', min: 18, max: 34 },
  { label: 'All Adults', min: 18, max: 80 }
];

const genderOptions = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
  { value: 'other', label: 'Other' },
  { value: 'not_specified', label: 'Not Specified' }
];

const interestCategories = [
  {
    name: '💼 Business',
    icon: 'heroicons:briefcase',
    interests: ['Marketing', 'Sales', 'Finance', 'Real Estate', 'Consulting', 'Entrepreneurship']
  },
  {
    name: '💻 Technology',
    icon: 'heroicons:computer-desktop',
    interests: ['Software', 'AI/ML', 'Cybersecurity', 'Mobile Apps', 'Web Development', 'Gaming']
  },
  {
    name: '❤️ Health & Fitness',
    icon: 'heroicons:heart',
    interests: ['Fitness', 'Nutrition', 'Yoga', 'Mental Health', 'Weight Loss', 'Meditation']
  },
  {
    name: '🎯 Lifestyle',
    icon: 'heroicons:sparkles',
    interests: ['Travel', 'Food', 'Fashion', 'Music', 'Movies', 'Photography', 'Art']
  },
  {
    name: '🛒 Shopping',
    icon: 'heroicons:shopping-bag',
    interests: ['Online Shopping', 'Fashion', 'Electronics', 'Home & Garden', 'Luxury Goods']
  },
  {
    name: '📚 Education',
    icon: 'heroicons:academic-cap',
    interests: ['Online Learning', 'Professional Development', 'Certification', 'Language Learning']
  }
];

// Computed properties
const canProceed = computed(() => {
  switch (currentStep.value) {
    case 1:
      return audienceName.value.trim().length > 0;
    case 2:
      return selectedLocations.value.length > 0;
    case 3:
    case 4:
    case 5:
      return true;
    default:
      return false;
  }
});

// Methods
const nextStep = async() => {
  if (!canProceed.value || currentStep.value >= totalSteps) return;
  
  currentStep.value++;
  
  // Focus first input on new step
  await nextTick();
  if (currentStep.value === 1 && nameInput.value) {
    nameInput.value.focus();
  }
  
  // Update estimation when moving to summary
  if (currentStep.value === 5) {
    await updateAudienceEstimation();
  }
};

const previousStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
  }
};

const saveAndExit = () => {
  // TODO: Save draft
  emits('close');
};

// Location methods
const performLocationSearch = async() => {
  if (!locationSearch.value.trim()) {
    locationSearchResults.value = [];
    return;
  }
  
  try {
    const results = await searchLocations(locationSearch.value, {}, 5);
    locationSearchResults.value = results;
  } catch (error) {
    console.error('Error searching locations:', error);
  }
};

const selectQuickLocation = (quickLocation: any) => {
  // Convert quick location to GeographicLocation format
  const location: GeographicLocation = {
    id: quickLocation.id,
    name: quickLocation.name,
    type: quickLocation.id === 'worldwide' ? 'country' : 'country',
    metadata: { description: quickLocation.description }
  };
  
  if (isLocationSelected(quickLocation.id)) {
    removeLocation(quickLocation.id);
  } else {
    selectedLocations.value.push(location);
  }
};

const selectSearchLocation = (location: GeographicLocation) => {
  if (!selectedLocations.value.find(l => l.id === location.id)) {
    selectedLocations.value.push(location);
  }
  locationSearch.value = '';
  locationSearchResults.value = [];
};

const isLocationSelected = (locationId: string): boolean => {
  return selectedLocations.value.some(l => l.id === locationId);
};

const removeLocation = (locationId: string) => {
  selectedLocations.value = selectedLocations.value.filter(l => l.id !== locationId);
};

// Demographic methods
const updateAgeRange = () => {
  if (demographicFilters.value.ageRange.min > demographicFilters.value.ageRange.max) {
    demographicFilters.value.ageRange.max = demographicFilters.value.ageRange.min;
  }
};

const setAgePreset = (preset: { min: number; max: number }) => {
  demographicFilters.value.ageRange = { min: preset.min, max: preset.max };
};

const isAgePresetActive = (preset: { min: number; max: number }): boolean => {
  return demographicFilters.value.ageRange.min === preset.min && 
         demographicFilters.value.ageRange.max === preset.max;
};

const toggleGender = (gender: string) => {
  const index = demographicFilters.value.selectedGenders.indexOf(gender);
  if (index === -1) {
    demographicFilters.value.selectedGenders.push(gender);
  } else {
    demographicFilters.value.selectedGenders.splice(index, 1);
  }
};

const skipDemographics = () => {
  demographicFilters.value.ageRange = { min: 18, max: 80 };
  demographicFilters.value.selectedGenders = [];
  nextStep();
};

// Interest methods
const toggleInterest = (interest: string) => {
  const index = behavioralFilters.value.selectedInterests.indexOf(interest);
  if (index === -1) {
    behavioralFilters.value.selectedInterests.push(interest);
  } else {
    behavioralFilters.value.selectedInterests.splice(index, 1);
  }
};

const getSelectedInterestsInCategory = (categoryInterests: string[]): string[] => {
  return behavioralFilters.value.selectedInterests.filter(interest => 
    categoryInterests.includes(interest)
  );
};

const skipInterests = () => {
  behavioralFilters.value.selectedInterests = [];
  nextStep();
};

// Estimation methods
const updateAudienceEstimation = async() => {
  try {
    const criteria = {
      geographic: {
        type: 'administrative' as const,
        locations: selectedLocations.value
      },
      demographic: {
        ageRange: demographicFilters.value.ageRange,
        ...(demographicFilters.value.selectedGenders.length > 0 && {
          gender: demographicFilters.value.selectedGenders as any[]
        })
      },
      ...(behavioralFilters.value.selectedInterests.length > 0 && {
        behavioral: {
          interests: behavioralFilters.value.selectedInterests
        }
      })
    };

    const estimation = await estimateAudience(criteria);
    estimatedAudienceSize.value = estimation.estimatedSize;
    estimatedCPM.value = estimation.costEstimate.cpm;
    confidence.value = estimation.confidence;
  } catch (error) {
    console.error('Error updating audience estimation:', error);
  }
};

const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`;
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`;
  }
  return num.toString();
};

// Create audience
const createAudience = async() => {
  if (!canProceed.value) return;

  isCreating.value = true;

  try {
    const segmentData = {
      name: audienceName.value.trim(),
      description: audienceDescription.value.trim() || undefined,
      type: 'combined' as const,
      criteria: {
        geographic: {
          type: 'administrative' as const,
          locations: selectedLocations.value
        },
        demographic: {
          ageRange: demographicFilters.value.ageRange,
          ...(demographicFilters.value.selectedGenders.length > 0 && {
            gender: demographicFilters.value.selectedGenders as any[]
          })
        },
        ...(behavioralFilters.value.selectedInterests.length > 0 && {
          behavioral: {
            interests: behavioralFilters.value.selectedInterests
          }
        })
      },
      size: {
        estimated: estimatedAudienceSize.value,
        confidence: confidence.value,
        lastUpdated: new Date() as any
      },
      status: 'active' as const
    };

    const audienceId = await createSegment(segmentData as any);
    emits('audienceCreated', audienceId);
  } catch (error) {
    console.error('Error creating audience:', error);
    // TODO: Show error message
  } finally {
    isCreating.value = false;
  }
};

// Initialize
onMounted(() => {
  // Focus name input on mount
  nextTick(() => {
    if (nameInput.value) {
      nameInput.value.focus();
    }
  });
});
</script>

<style scoped>
.mobile-targeting-flow {
  @apply h-full flex flex-col bg-white dark:bg-gray-900;
}

.step-content {
  @apply flex-1 min-h-0;
}

/* Custom range slider styling for mobile */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  @apply h-2 bg-gray-200 rounded-lg dark:bg-gray-700;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  @apply w-6 h-6 bg-blue-600 rounded-full cursor-pointer shadow-lg;
}

input[type="range"]::-moz-range-thumb {
  @apply w-6 h-6 bg-blue-600 rounded-full cursor-pointer shadow-lg border-0;
}

/* Touch targets for mobile */
button {
  min-height: 44px;
}

/* Smooth transitions */
.step-content {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>