<template>
  <div class="demographic-filters">
    <!-- Header -->
    <div class="mb-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
        Demographic Targeting
      </h3>
      <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
        Target users based on age, gender, income, and other demographics
      </p>
    </div>

    <div class="space-y-6">
      <!-- Age Range -->
      <div class="demographic-section">
        <div class="flex items-center justify-between mb-3">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Age Range
          </label>
          <button
            @click="clearAgeRange"
            v-if="demographics.ageRange"
            class="text-xs text-red-600 hover:text-red-700 dark:text-red-400"
          >
            Clear
          </button>
        </div>
        
        <div class="space-y-4">
          <!-- Age Range Slider -->
          <div class="px-3">
            <div class="relative">
              <!-- Dual range slider -->
              <input
                v-model.number="ageRange.min"
                @input="updateAgeRange"
                type="range"
                min="13"
                max="80"
                step="1"
                class="absolute w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700 z-10"
                :style="{ background: ageRangeGradient }"
              />
              <input
                v-model.number="ageRange.max"
                @input="updateAgeRange"
                type="range"
                min="13"
                max="80"
                step="1"
                class="absolute w-full h-2 bg-transparent appearance-none cursor-pointer z-20"
              />
            </div>
            
            <!-- Age labels -->
            <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-2">
              <span>13</span>
              <span>{{ ageRange.min }} - {{ ageRange.max }}</span>
              <span>80+</span>
            </div>
          </div>
          
          <!-- Age presets -->
          <div class="flex flex-wrap gap-2">
            <button
              v-for="preset in agePresets"
              :key="preset.label"
              @click="setAgePreset(preset)"
              :class="[
                'px-3 py-1 text-xs font-medium rounded-full transition-colors',
                isAgePresetActive(preset)
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
              ]"
            >
              {{ preset.label }}
            </button>
          </div>
        </div>
      </div>

      <!-- Gender -->
      <div class="demographic-section">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Gender
        </label>
        
        <div class="grid grid-cols-2 sm:grid-cols-4 gap-3">
          <label
            v-for="gender in genderOptions"
            :key="gender.value"
            class="flex items-center space-x-2 cursor-pointer"
          >
            <input
              v-model="selectedGenders"
              :value="gender.value"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="text-sm text-gray-700 dark:text-gray-300">
              {{ gender.label }}
            </span>
          </label>
        </div>
      </div>

      <!-- Income Level -->
      <div class="demographic-section">
        <div class="flex items-center justify-between mb-3">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Income Level
          </label>
          <div class="flex items-center space-x-2">
            <select
              v-model="incomeCurrency"
              class="text-xs border border-gray-300 rounded px-2 py-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              <option value="USD">USD</option>
              <option value="EUR">EUR</option>
              <option value="GBP">GBP</option>
              <option value="CAD">CAD</option>
            </select>
          </div>
        </div>
        
        <!-- Income range inputs -->
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-xs text-gray-600 dark:text-gray-400 mb-1">
              Minimum Income
            </label>
            <div class="relative">
              <span class="absolute left-3 top-2 text-gray-500 text-sm">
                {{ getCurrencySymbol(incomeCurrency) }}
              </span>
              <input
                v-model.number="incomeRange.min"
                @input="updateIncomeRange"
                type="number"
                min="0"
                step="1000"
                placeholder="0"
                class="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
          </div>
          
          <div>
            <label class="block text-xs text-gray-600 dark:text-gray-400 mb-1">
              Maximum Income
            </label>
            <div class="relative">
              <span class="absolute left-3 top-2 text-gray-500 text-sm">
                {{ getCurrencySymbol(incomeCurrency) }}
              </span>
              <input
                v-model.number="incomeRange.max"
                @input="updateIncomeRange"
                type="number"
                min="0"
                step="1000"
                placeholder="No limit"
                class="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
          </div>
        </div>
        
        <!-- Income presets -->
        <div class="mt-3 flex flex-wrap gap-2">
          <button
            v-for="preset in incomePresets"
            :key="preset.label"
            @click="setIncomePreset(preset)"
            class="px-3 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 transition-colors"
          >
            {{ preset.label }}
          </button>
        </div>
      </div>

      <!-- Education Level -->
      <div class="demographic-section">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Education Level
        </label>
        
        <div class="space-y-2">
          <label
            v-for="education in educationOptions"
            :key="education.value"
            class="flex items-center space-x-3 cursor-pointer p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800"
          >
            <input
              v-model="selectedEducation"
              :value="education.value"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <div class="flex-1">
              <div class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ education.label }}
              </div>
              <div class="text-xs text-gray-500 dark:text-gray-400">
                {{ education.description }}
              </div>
            </div>
          </label>
        </div>
      </div>

      <!-- Employment & Occupation -->
      <div class="demographic-section">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Occupation Categories
        </label>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
          <label
            v-for="occupation in occupationOptions"
            :key="occupation.value"
            class="flex items-center space-x-2 cursor-pointer p-2 rounded hover:bg-gray-50 dark:hover:bg-gray-800"
          >
            <input
              v-model="selectedOccupations"
              :value="occupation.value"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="text-sm text-gray-700 dark:text-gray-300">
              {{ occupation.label }}
            </span>
          </label>
        </div>
      </div>

      <!-- Life Stage & Family -->
      <div class="demographic-section">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Life Stage
        </label>
        
        <div class="space-y-4">
          <!-- Marital Status -->
          <div>
            <label class="block text-xs text-gray-600 dark:text-gray-400 mb-2">
              Marital Status
            </label>
            <div class="flex flex-wrap gap-2">
              <label
                v-for="status in maritalStatusOptions"
                :key="status.value"
                class="flex items-center space-x-2 cursor-pointer"
              >
                <input
                  v-model="selectedMaritalStatus"
                  :value="status.value"
                  type="checkbox"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span class="text-sm text-gray-700 dark:text-gray-300">
                  {{ status.label }}
                </span>
              </label>
            </div>
          </div>
          
          <!-- Parental Status -->
          <div>
            <label class="block text-xs text-gray-600 dark:text-gray-400 mb-2">
              Parental Status
            </label>
            <div class="flex flex-wrap gap-2">
              <label
                v-for="status in parentalStatusOptions"
                :key="status.value"
                class="flex items-center space-x-2 cursor-pointer"
              >
                <input
                  v-model="selectedParentalStatus"
                  :value="status.value"
                  type="checkbox"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span class="text-sm text-gray-700 dark:text-gray-300">
                  {{ status.label }}
                </span>
              </label>
            </div>
          </div>
          
          <!-- Home Ownership -->
          <div>
            <label class="block text-xs text-gray-600 dark:text-gray-400 mb-2">
              Home Ownership
            </label>
            <div class="flex flex-wrap gap-2">
              <label
                v-for="ownership in homeOwnershipOptions"
                :key="ownership.value"
                class="flex items-center space-x-2 cursor-pointer"
              >
                <input
                  v-model="selectedHomeOwnership"
                  :value="ownership.value"
                  type="checkbox"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span class="text-sm text-gray-700 dark:text-gray-300">
                  {{ ownership.label }}
                </span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Audience Impact Summary -->
    <div class="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
      <div class="flex items-start space-x-3">
        <Icon name="heroicons:information-circle" class="w-5 h-5 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
        <div>
          <h4 class="font-medium text-blue-900 dark:text-blue-100 mb-1">
            Targeting Impact
          </h4>
          <p class="text-sm text-blue-700 dark:text-blue-200">
            {{ getTargetingImpactMessage() }}
          </p>
          <div class="mt-2 flex items-center space-x-4 text-xs text-blue-600 dark:text-blue-300">
            <div class="flex items-center space-x-1">
              <Icon name="heroicons:funnel" class="w-3 h-3" />
              <span>{{ getFilterCount() }} filters active</span>
            </div>
            <div class="flex items-center space-x-1">
              <Icon name="heroicons:users" class="w-3 h-3" />
              <span>~{{ estimatedReduction }}% audience reduction</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Clear All Button -->
    <div class="mt-4 flex justify-end">
      <button
        @click="clearAllFilters"
        v-if="hasAnyFilters"
        class="px-4 py-2 text-sm font-medium text-red-600 hover:text-red-700 dark:text-red-400 transition-colors"
      >
        Clear All Demographic Filters
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import type { DemographicCriteria } from '~/types/audience-segmentation';

// Props and emits
interface Props {
  modelValue?: DemographicCriteria;
  readonly?: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: DemographicCriteria): void;
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
});

const emits = defineEmits<Emits>();

// Age range state
const ageRange = ref({ min: 18, max: 65 });
const agePresets = [
  { label: 'Gen Z (18-26)', min: 18, max: 26 },
  { label: 'Millennials (27-42)', min: 27, max: 42 },
  { label: 'Gen X (43-58)', min: 43, max: 58 },
  { label: 'Boomers (59-77)', min: 59, max: 77 },
  { label: 'Young Adults (18-34)', min: 18, max: 34 },
  { label: 'Middle-aged (35-54)', min: 35, max: 54 },
  { label: 'Seniors (55+)', min: 55, max: 80 }
];

// Gender state
const selectedGenders = ref<string[]>([]);
const genderOptions = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
  { value: 'other', label: 'Other' },
  { value: 'not_specified', label: 'Not Specified' }
];

// Income state
const incomeRange = ref({ min: undefined as number | undefined, max: undefined as number | undefined });
const incomeCurrency = ref('USD');
const incomePresets = [
  { label: 'Low Income (<$30K)', min: 0, max: 30000 },
  { label: 'Middle Income ($30K-$75K)', min: 30000, max: 75000 },
  { label: 'Upper Middle ($75K-$150K)', min: 75000, max: 150000 },
  { label: 'High Income ($150K+)', min: 150000, max: undefined },
  { label: 'Affluent ($250K+)', min: 250000, max: undefined }
];

// Education state
const selectedEducation = ref<string[]>([]);
const educationOptions = [
  { 
    value: 'high_school', 
    label: 'High School', 
    description: 'High school diploma or equivalent' 
  },
  { 
    value: 'some_college', 
    label: 'Some College', 
    description: 'Some college education, no degree' 
  },
  { 
    value: 'bachelors', 
    label: "Bachelor's Degree", 
    description: 'Undergraduate degree' 
  },
  { 
    value: 'masters', 
    label: "Master's Degree", 
    description: 'Graduate degree' 
  },
  { 
    value: 'doctorate', 
    label: 'Doctorate', 
    description: 'PhD or professional doctorate' 
  }
];

// Occupation state
const selectedOccupations = ref<string[]>([]);
const occupationOptions = [
  { value: 'management', label: 'Management & Executive' },
  { value: 'professional', label: 'Professional & Technical' },
  { value: 'sales', label: 'Sales & Marketing' },
  { value: 'administrative', label: 'Administrative Support' },
  { value: 'education', label: 'Education & Training' },
  { value: 'healthcare', label: 'Healthcare & Medical' },
  { value: 'finance', label: 'Finance & Accounting' },
  { value: 'technology', label: 'Technology & IT' },
  { value: 'creative', label: 'Arts & Creative' },
  { value: 'service', label: 'Service Industry' },
  { value: 'manufacturing', label: 'Manufacturing & Labor' },
  { value: 'retired', label: 'Retired' },
  { value: 'student', label: 'Student' },
  { value: 'unemployed', label: 'Unemployed' }
];

// Life stage state
const selectedMaritalStatus = ref<string[]>([]);
const maritalStatusOptions = [
  { value: 'single', label: 'Single' },
  { value: 'married', label: 'Married' },
  { value: 'divorced', label: 'Divorced' },
  { value: 'widowed', label: 'Widowed' }
];

const selectedParentalStatus = ref<string[]>([]);
const parentalStatusOptions = [
  { value: 'no_children', label: 'No Children' },
  { value: 'children_at_home', label: 'Children at Home' },
  { value: 'children_away', label: 'Children Living Away' }
];

const selectedHomeOwnership = ref<string[]>([]);
const homeOwnershipOptions = [
  { value: 'rent', label: 'Rent' },
  { value: 'own', label: 'Own' },
  { value: 'other', label: 'Other' }
];

// Computed demographic criteria
const demographics = computed<DemographicCriteria>(() => {
  const criteria: DemographicCriteria = {};

  // Age range
  if (ageRange.value.min !== 18 || ageRange.value.max !== 65) {
    criteria.ageRange = {
      min: ageRange.value.min,
      max: ageRange.value.max
    };
  }

  // Gender
  if (selectedGenders.value.length > 0 && selectedGenders.value.length < 4) {
    criteria.gender = selectedGenders.value as any[];
  }

  // Income
  if (incomeRange.value.min !== undefined || incomeRange.value.max !== undefined) {
    criteria.income = {
      currency: incomeCurrency.value,
      min: incomeRange.value.min,
      max: incomeRange.value.max
    };
  }

  // Education
  if (selectedEducation.value.length > 0 && selectedEducation.value.length < 5) {
    criteria.education = selectedEducation.value as any[];
  }

  // Occupation
  if (selectedOccupations.value.length > 0) {
    criteria.occupation = selectedOccupations.value;
  }

  // Marital status
  if (selectedMaritalStatus.value.length > 0 && selectedMaritalStatus.value.length < 4) {
    criteria.maritalStatus = selectedMaritalStatus.value as any[];
  }

  // Parental status
  if (selectedParentalStatus.value.length > 0 && selectedParentalStatus.value.length < 3) {
    criteria.parentalStatus = selectedParentalStatus.value as any[];
  }

  // Home ownership
  if (selectedHomeOwnership.value.length > 0 && selectedHomeOwnership.value.length < 3) {
    criteria.homeOwnership = selectedHomeOwnership.value as any[];
  }

  return criteria;
});

// Computed properties for UI
const ageRangeGradient = computed(() => {
  const minPercent = ((ageRange.value.min - 13) / (80 - 13)) * 100;
  const maxPercent = ((ageRange.value.max - 13) / (80 - 13)) * 100;
  return `linear-gradient(to right, #e5e7eb 0%, #e5e7eb ${minPercent}%, #3b82f6 ${minPercent}%, #3b82f6 ${maxPercent}%, #e5e7eb ${maxPercent}%, #e5e7eb 100%)`;
});

const hasAnyFilters = computed(() => {
  return Object.keys(demographics.value).length > 0;
});

const estimatedReduction = computed(() => {
  let reduction = 0;
  
  // Calculate estimated audience reduction based on filters
  if (demographics.value.ageRange) {
    const ageSpan = demographics.value.ageRange.max - demographics.value.ageRange.min;
    const totalAgeSpan = 80 - 13;
    reduction += (1 - (ageSpan / totalAgeSpan)) * 30; // Up to 30% reduction
  }
  
  if (demographics.value.gender && demographics.value.gender.length < 4) {
    reduction += (1 - (demographics.value.gender.length / 4)) * 20; // Up to 20% reduction
  }
  
  if (demographics.value.income) {
    reduction += 25; // Income filtering typically reduces by ~25%
  }
  
  if (demographics.value.education && demographics.value.education.length < 5) {
    reduction += (1 - (demographics.value.education.length / 5)) * 15; // Up to 15% reduction
  }
  
  if (demographics.value.occupation && demographics.value.occupation.length < 14) {
    reduction += (1 - (demographics.value.occupation.length / 14)) * 20; // Up to 20% reduction
  }
  
  return Math.min(95, Math.round(reduction)); // Cap at 95% reduction
});

// Methods
const updateAgeRange = () => {
  // Ensure min is not greater than max
  if (ageRange.value.min > ageRange.value.max) {
    ageRange.value.max = ageRange.value.min;
  }
  updateModelValue();
};

const clearAgeRange = () => {
  ageRange.value = { min: 18, max: 65 };
  updateModelValue();
};

const setAgePreset = (preset: { label: string; min: number; max: number }) => {
  ageRange.value = { min: preset.min, max: preset.max };
  updateModelValue();
};

const isAgePresetActive = (preset: { min: number; max: number }) => {
  return ageRange.value.min === preset.min && ageRange.value.max === preset.max;
};

const updateIncomeRange = () => {
  updateModelValue();
};

const setIncomePreset = (preset: { label: string; min: number; max?: number }) => {
  incomeRange.value = { min: preset.min, max: preset.max };
  updateModelValue();
};

const getCurrencySymbol = (currency: string) => {
  const symbols: Record<string, string> = {
    USD: '$',
    EUR: '€',
    GBP: '£',
    CAD: 'C$'
  };
  return symbols[currency] || '$';
};

const getFilterCount = () => {
  return Object.keys(demographics.value).length;
};

const getTargetingImpactMessage = () => {
  const filterCount = getFilterCount();
  
  if (filterCount === 0) {
    return 'No demographic filters applied. Your ads will reach all age groups and demographics.';
  } else if (filterCount <= 2) {
    return 'Light demographic targeting applied. This will moderately narrow your audience while maintaining good reach.';
  } else if (filterCount <= 4) {
    return 'Moderate demographic targeting. This provides a good balance between precision and reach.';
  } else {
    return 'Heavy demographic targeting applied. This will significantly narrow your audience but increase relevance.';
  }
};

const clearAllFilters = () => {
  ageRange.value = { min: 18, max: 65 };
  selectedGenders.value = [];
  incomeRange.value = { min: undefined, max: undefined };
  selectedEducation.value = [];
  selectedOccupations.value = [];
  selectedMaritalStatus.value = [];
  selectedParentalStatus.value = [];
  selectedHomeOwnership.value = [];
  updateModelValue();
};

const updateModelValue = () => {
  emits('update:modelValue', demographics.value);
};

// Watch for external model value changes
watch(
  () => props.modelValue,
  (newValue) => {
    if (!newValue) return;
    
    // Update age range
    if (newValue.ageRange) {
      ageRange.value = { ...newValue.ageRange };
    }
    
    // Update gender
    if (newValue.gender) {
      selectedGenders.value = [...newValue.gender];
    }
    
    // Update income
    if (newValue.income) {
      incomeRange.value = {
        min: newValue.income.min,
        max: newValue.income.max
      };
      incomeCurrency.value = newValue.income.currency;
    }
    
    // Update education
    if (newValue.education) {
      selectedEducation.value = [...newValue.education];
    }
    
    // Update occupation
    if (newValue.occupation) {
      selectedOccupations.value = [...newValue.occupation];
    }
    
    // Update marital status
    if (newValue.maritalStatus) {
      selectedMaritalStatus.value = [...newValue.maritalStatus];
    }
    
    // Update parental status
    if (newValue.parentalStatus) {
      selectedParentalStatus.value = [...newValue.parentalStatus];
    }
    
    // Update home ownership
    if (newValue.homeOwnership) {
      selectedHomeOwnership.value = [...newValue.homeOwnership];
    }
  },
  { immediate: true, deep: true }
);

// Watch for changes in reactive data and emit updates
watch(
  [
    selectedGenders,
    selectedEducation, 
    selectedOccupations,
    selectedMaritalStatus,
    selectedParentalStatus,
    selectedHomeOwnership
  ],
  () => {
    updateModelValue();
  },
  { deep: true }
);
</script>

<style scoped>
.demographic-section {
  @apply pb-6 border-b border-gray-200 dark:border-gray-700 last:border-b-0;
}

/* Custom range slider styling */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  @apply w-4 h-4 bg-blue-600 rounded-full cursor-pointer shadow-md;
  position: relative;
  z-index: 30;
}

input[type="range"]::-moz-range-thumb {
  @apply w-4 h-4 bg-blue-600 rounded-full cursor-pointer border-0 shadow-md;
  position: relative;
  z-index: 30;
}

input[type="range"]::-webkit-slider-track {
  @apply h-2 bg-gray-200 rounded-lg dark:bg-gray-700;
}

input[type="range"]::-moz-range-track {
  @apply h-2 bg-gray-200 rounded-lg dark:bg-gray-700 border-0;
}
</style>