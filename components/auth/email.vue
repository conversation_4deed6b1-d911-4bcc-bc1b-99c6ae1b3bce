<script setup lang="ts">
import moment from 'moment'
const { $swal } = useNuxtApp();
const currentUser: any = useState("currentUser", () => {
    return {};
  });

  const currentClient: any = useState("currentClient", () => {
    return {};
  });

  const currentSpace: any = useState("currentSpace", () => {
    return {};
  });
definePageMeta({
  layout: 'clean',
})

const email = ref('')
const submit = async () => {
let user = await queryByWhereLimit('user', 'email', '==', email.value, 1)
console.log('suer', user)


if(user.length > 0){
    currentUser.value = user[0]
    currentClient.value = user[0]
    submitSpace()
}else{
	let payload = {
		created_at: new Date(),
    created_date: moment(new Date()).format("YYYY-MM-DD"),
    last_action: new Date(),
    last_action_date: moment(new Date()).format("YYYY-MM-DD,HH:mm:ss"),
	email: email.value,
	password: email.value,
	confirm_password: email.value,
	first_name: '',
	last_name: '',
	}
	console.log('create', payload)
	let createUser: any = await registerUser(email.value, email.value, payload)
	console.log('create', createUser)
	if(createUser.id){
		setTimeout(()=> {
			submit()

		}, 1000)
	}
}
}

const submitSpace = async () => {
    console.log('SPACE', currentUser.value)
let spce = await queryByWhereLimit('spaces', 'access_uid', 'array-contains', currentUser.value.id, 1)
console.log('spce', spce)


if(spce.length > 0){
    currentSpace.value = spce[0]
}else{
	let payload: any = {
	email: email.value,
	password: email.value,
	confirm_password: email.value,
	name: email.value,
    ...extraInfo()
	}
	console.log('create', payload)
	let createSpace: any = await add('spaces', payload)
	console.log('create', createSpace)
	if(createSpace){
		setTimeout(()=> {
			submitSpace()

		}, 1000)
	}
}
}

</script>
<template>
    <div class="h-full min-h-screen pb-12 font-sans md:p-4 theme_200 flex items-center justify-center w-full">
	  <div id="login-area" class="relative py-12 w-full">
	    <div class="container xl:max-w-6xl mx-auto px-4 w-full">
	      <div class="flex flex-wrap flex-row -mx-4 justify-center w-full">
	        <div class="max-w-full w-full md:w-2/3 lg:w-1/2 px-6 sm:px-12">
	          <div class="relative">
	            <div class="o_card">
	              <div id="login-form">
	                <h1 class="text-2xl leading-normal mb-6 font-bold text-gray-800  dark:text-gray-300 text-center">Enter Email</h1>
	                <hr class="block w-12 h-0.5 mx-auto my-5 bg-gray-700 border-gray-700">
	                <p class="text-gray-500 mb-6">Enter your email address and we'll send you an email with instructions to login.</p>
	                <div class="mb-6">
	                  <input name="email" v-model="email" class="w-full leading-5 relative py-2 px-4 rounded text-gray-800 bg-white border border-gray-300 overflow-x-auto focus:outline-none focus:border-gray-400 focus:ring-0 dark:text-gray-300 dark:bg-gray-700 dark:border-gray-700 dark:focus:border-gray-600" placeholder="Email address" aria-label="email" type="email" required>
	                </div>
	                <div class="text-center">
	                  <button @click="submit" class="py-2 px-4 inline-block text-center mb-3 rounded leading-5 text-gray-100 bg-primary_focus border border-primary_focus hover:text-white hover:bg-primary hover:ring-0 hover:border-primary focus:bg-primary focus:border-primary focus:outline-none focus:ring-0">
	                  	<svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" class="inline-block w-4 h-4 ltr:mr-2 rtl:ml-2 bi bi-key" viewBox="0 0 16 16">
											  <path d="M0 8a4 4 0 0 1 7.465-2H14a.5.5 0 0 1 .354.146l1.5 1.5a.5.5 0 0 1 0 .708l-1.5 1.5a.5.5 0 0 1-.708 0L13 9.207l-.646.647a.5.5 0 0 1-.708 0L11 9.207l-.646.647a.5.5 0 0 1-.708 0L9 9.207l-.646.647A.5.5 0 0 1 8 10h-.535A4 4 0 0 1 0 8zm4-3a3 3 0 1 0 2.712 4.285A.5.5 0 0 1 7.163 9h.63l.853-.854a.5.5 0 0 1 .708 0l.646.647.646-.647a.5.5 0 0 1 .708 0l.646.647.646-.647a.5.5 0 0 1 .708 0l.646.647.793-.793-1-1h-6.63a.5.5 0 0 1-.451-.285A3 3 0 0 0 4 5z"/>
											  <path d="M4 8a1 1 0 1 1-2 0 1 1 0 0 1 2 0z"/>
											</svg>
		                  Submit
		                </button>
	                </div>
				</div>
	            </div>
	          </div>
	        </div>
	      </div>
	    </div>
	  </div>
</div>
</template>
