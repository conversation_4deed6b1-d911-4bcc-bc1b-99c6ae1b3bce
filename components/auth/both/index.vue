<script setup lang="ts">
const showAuth = ref('Login')
const showLogin = useState('showLogin', () => {return false});

const loginAuthEmit = (data) => {
    if(data === 'loggedIn'){
        showLogin.value = false
    }else if(data === 'register'){
        showAuth.value = 'register'
    }
}
</script>


<template>
    <div>
        <auth-both-login v-if="showAuth == 'Login'" @loginAuthEmit="loginAuthEmit" @goToRegister='showAuth = "Register"' :reroute_emit="true"/>
        <auth-both-register v-if="showAuth == 'Register'" @loginAuthEmit="loginAuthEmit" @goToLogin="showAuth = 'Login'" :reroute_emit="true"/>

    </div>
</template>