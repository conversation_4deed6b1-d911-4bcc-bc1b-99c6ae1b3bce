<script setup lang="ts">
const { $swal } = useNuxtApp()
const { mainSpace: mainSpaceRegister } = space()
const { meUser: mainUserRegister } = useMe()
const registerRouter = useRouter()
defineProps({
  reroute_emit: {
    type: Boolean,
    default: ()=> false
  }
})
 
const form = ref({
	username: '',
	first_name: '',
	last_name: '',
	email: '',
	password: '',
	confirm_password: '',
	accept_terms_conditions: false,
	coins: 100.00,
})
const loginAuthEmit = getCurrentInstance()

async function register() {
	const payload = {
		...form.value,
		...extraInfo(),
	}

	const result = await $fetch<any>(
		`/api/firestore/register?email=${form.value.email}&password=${form.value.password}`,
		{
			method: 'POST',
			body: payload,
		},
	)

	if (result.result) {
		if (result.result.code === 'auth/user-not-found') {
		
			$swal.fire({
				title: 'Error!',
				text: `${result.result.error}`,
				icon: 'error',
				confirmButtonText: 'Cool'
			})
			// registerRouter.push('/c/auth/register')
		}
		else if (result.result.error) {
			
			$swal.fire({
				title: 'Error!',
				text: `${result.result.error}`,
				icon: 'error',
				confirmButtonText: 'Cool'
			})
			// registerRouter.push('/c/auth/register')
		}
		else if (result.result.id) {
			mainUserRegister.value = result.result
			loginAuthEmit?.emit('loginAuthEmit', 'loggedIn')

		}
	}
	else {
	
	}
}
</script>
<template>

	<div
		class="flex items-center justify-center h-screen font-sans text-base font-normal theme_300">

		<div>
			<div class="bg-gray-100 md:py-12 dark:bg-gray-900 dark:bg-opacity-40">
				<div class="container py-4 mx-auto xl:max-w-6xl">
					<div class="flex flex-row flex-wrap -mx-4">
						<div class="flex-shrink w-full max-w-full px-4">
							<!-- register form -->
							<div class="w-full max-w-full px-2 mb-12 sm:px-12 lg:pr-20 lg:mb-0">
								<div class="relative">
									<div class="p-6 bg-white rounded-lg shadow-xl sm:py-8 sm:px-12 dark:bg-gray-800">
										<div id="register-form">
											<div class="text-center">
												<h1
													class="mb-3 text-2xl font-bold leading-normal text-center text-gray-800 dark:text-gray-300">
													Join Now</h1>
											</div>
											<hr class="block w-12 h-0.5 mx-auto my-5 bg-gray-700 border-gray-700">
											<div class="mb-4">
												<label for="inputname" class="o_label">First Name</label>
												<input v-model="form.first_name" name="name" id="inputname"
													class="o_input"
													aria-label="text" type="text" required>
											</div>
											<div class="mb-4">
												<label for="inputname" class="o_label">Last Name</label>
												<input v-model="form.last_name" name="name" id="inputname"
													class="o_input"
													aria-label="text" type="text" required>
											</div>
											<div class="mb-4">
												<label for="inputemail" class="o_label">Email</label>
												<input v-model="form.email" name="email" id="inputemail"
													class="o_input"
													aria-label="email" type="email" required>
											</div>
											<div class="mb-4">
												<label for="inputpass" class="o_label">Password</label>
												<input v-model="form.password" id="inputpass"
													class="o_input"
													aria-label="password" type="password" required>
											</div>
											<div class="mb-4">
												<label for="inputrepeatpass" class="o_label">Repeat
													Password</label>
												<input v-model="form.confirm_password" id="inputrepeatpass"
													class="o_input"
													aria-label="password" type="password" required>
											</div>
											<div class="mt-6 mb-4">
												<input v-model="form.accept_terms_conditions"
													class="w-5 h-5 border border-gray-300 rounded text-primarys_focus form-checkbox dark:bg-gray-700 dark:border-gray-700 focus:outline-none"
													type="checkbox" id="terms" required>
												<label class="ltr:ml-2 rtl:mr-2" for="terms">
													I agree to the <a href="#">Terms and Conditions</a>
												</label>
											</div>
											<div class="grid">
												<button @click="register"
													class="inline-block px-4 py-2 leading-normal text-center text-gray-100 border rounded bg-primarys_focus border-primarys_focus hover:text-white hover:bg-primarys hover:ring-0 hover:border-primarys focus:bg-primarys focus:border-primarys focus:outline-none focus:ring-0">
													<svg xmlns="http://www.w3.org/2000/svg" fill="currentColor"
														class="inline-block w-4 h-4 ltr:mr-2 rtl:ml-2 bi bi-box-arrow-in-right"
														viewBox="0 0 16 16">
														<path fill-rule="evenodd"
															d="M6 3.5a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-8a.5.5 0 0 1-.5-.5v-2a.5.5 0 0 0-1 0v2A1.5 1.5 0 0 0 6.5 14h8a1.5 1.5 0 0 0 1.5-1.5v-9A1.5 1.5 0 0 0 14.5 2h-8A1.5 1.5 0 0 0 5 3.5v2a.5.5 0 0 0 1 0v-2z" />
														<path fill-rule="evenodd"
															d="M11.854 8.354a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H1.5a.5.5 0 0 0 0 1h8.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3z" />
													</svg>Register
												</button>
											</div>
											<div class="mt-6 cursor-pointer">
												<div  v-if="reroute_emit" class="mb-4 text-center">Already have an account? <div @click="$emit('goToLogin')"  class="hover:text-primarys_focus">Login</div>
												
												</div>
												<div v-else class="mb-4 text-center">Already have an account? <NuxtLink
														to='/c/auth/login'><div class="hover:text-primarys_focus">Login</div>
													</NuxtLink>
												</div>
											</div>
										</div>
										<!-- <div class="mt-4">
			              <p class="mb-3 text-center"><span>Or</span></p>
			              <div class="mb-6 text-center sm:space-x-4">
			                <a class="block p-2 mb-3 leading-5 text-gray-100 bg-indigo-900 border border-indigo-900 rounded sm:inline-block lg:rounded-full hover:text-white hover:opacity-90 hover:ring-0 hover:border-indigo-900 focus:bg-indigo-900 focus:border-indigo-800 focus:outline-none focus:ring-0" href="#">
			                  <svg xmlns="http://www.w3.org/2000/svg" class="inline-block w-4 h-4 mx-1" fill="currentColor" viewBox="0 0 512 512"><path d="M455.27,32H56.73A24.74,24.74,0,0,0,32,56.73V455.27A24.74,24.74,0,0,0,56.73,480H256V304H202.45V240H256V189c0-57.86,40.13-89.36,91.82-89.36,24.73,0,51.33,1.86,57.51,2.68v60.43H364.15c-28.12,0-33.48,13.3-33.48,32.9V240h67l-8.75,64H330.67V480h124.6A24.74,24.74,0,0,0,480,455.27V56.73A24.74,24.74,0,0,0,455.27,32Z"/></svg>
			                  <span class="inline-block lg:hidden">Login with FB</span>
			                </a>
			                <a class="block p-2 mb-3 leading-5 text-gray-100 border rounded bg-primarys_focus border-primarys_focus sm:inline-block lg:rounded-full hover:text-white hover:bg-primarys hover:ring-0 hover:border-primarys focus:bg-primarys focus:border-primarys focus:outline-none focus:ring-0" href="#">
			                  <svg xmlns="http://www.w3.org/2000/svg" class="inline-block w-4 h-4 mx-1" fill="currentColor" viewBox="0 0 512 512"><path d="M496,109.5a201.8,201.8,0,0,1-56.55,15.3,97.51,97.51,0,0,0,43.33-53.6,197.74,197.74,0,0,1-62.56,23.5A99.14,99.14,0,0,0,348.31,64c-54.42,0-98.46,43.4-98.46,96.9a93.21,93.21,0,0,0,2.54,22.1,280.7,280.7,0,0,1-203-101.3A95.69,95.69,0,0,0,36,130.4C36,164,53.53,193.7,80,211.1A97.5,97.5,0,0,1,35.22,199v1.2c0,47,34,86.1,79,95a100.76,100.76,0,0,1-25.94,3.4,94.38,94.38,0,0,1-18.51-1.8c12.51,38.5,48.92,66.5,92.05,67.3A199.59,199.59,0,0,1,39.5,405.6,203,203,0,0,1,16,404.2,278.68,278.68,0,0,0,166.74,448c181.36,0,280.44-147.7,280.44-275.8,0-4.2-.11-8.4-.31-12.5A198.48,198.48,0,0,0,496,109.5Z"/></svg>
			                  <span class="inline-block lg:hidden">Login with Twitter</span>
			                </a>
			              </div>
			              <p class="mb-4 text-center">Already have an account?  <a class="hover:text-indigo-700" href="login-ilustration.html">Login</a></p>
			            </div> -->
									</div>
								</div>
							</div>
						</div>
					
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
