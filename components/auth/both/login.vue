<script setup lang="ts">
const { meUser: mainUserLogin } = useMe()
const { $swal } = useNuxtApp()
const darkMode = useState('darkMode', () => true)

const formLogin: any = ref({
  email: '',
  password: '',
  remember: false,
})

defineProps({
  reroute_emit: {
    type: Boolean,
    default: ()=> false
  }
})
const loginAuthEmit = getCurrentInstance()

async function loginUser() {
  const result: any = await $fetch<any>(`/api/firestore/login?email=${formLogin.value.email}&password=${formLogin.value.password}`)
  console.log(result)

  if (result) {
    if (result.result) {
      if (result.result.code === 'auth/user-not-found') {
        $swal.fire({
          title: 'Error!',
          text: `${result.result.error} : ${result.result.code}`,
          icon: 'error',
          confirmButtonText: 'Cool'
        })
        loginAuthEmit?.emit('loginAuthEmit', 'register')
      }
      else if (result.result.error) {

        $swal.fire({
          title: 'Error!',
          text: `${result.result.error}`,
          icon: 'error',
          confirmButtonText: 'Cool'
        })
        loginAuthEmit?.emit('loginAuthEmit', 'register')

      }
      else if (result.result.id) {
        mainUserLogin.value = result.result

        loginAuthEmit?.emit('loginAuthEmit', 'loggedIn')

      }
    }
    else {
      $swal.fire({
        title: 'Error!',
        text: 'Do you want to continue',
        icon: 'error',
        confirmButtonText: 'Cool'
      })
    }
  } else {
    $swal.fire({
      title: 'Error!',
      text: 'Do you want to continue',
      icon: 'error',
      confirmButtonText: 'Cool'
    })
  }


}
</script>
<template>

  <div class="flex items-center justify-center h-screen font-sans text-base font-normal theme_300"
   >
    <div class="py-8 md:py-12 theme_300 dark:bg-opacity-40"  :class="{ dark: darkMode }">
      <div class="container px-4 mx-auto xl:max-w-6xl">
        <div class="flex flex-row flex-wrap -mx-4">
          <div class="flex-shrink w-full max-w-full px-4 ">

            <div class="w-full max-w-full px-2 mb-12 sm:px-12 lg:pr-20 lg:mb-0">
              <div class="relative">
                <div class="p-6 rounded-lg shadow-xl sm:py-8 sm:px-12 theme_100">
                  <div id="login-form">
                    <div class="text-center">
                      <h1 class="mb-3 text-2xl font-bold leading-normal text-center text-gray-800 dark:text-gray-300">
                        Welcome Back</h1>
                    </div>
                    <hr class="block w-12 h-0.5 mx-auto my-5 bg-base_300 border-base_200">
                    <div class="mb-6">
                      <label for="inputemail" class="o_label">Email</label>
                      <input v-model="formLogin.email" name="email" id="inputemail"
                        class="o_input"
                        aria-label="email" type="email" required>
                    </div>
                    <div class="mb-6">
                      <div class="flex flex-row flex-wrap">
                        <div class="flex-shrink w-1/2 max-w-full">
                          <label for="inputpass" class="o_label">Password</label>
                        </div>
                        <div class="flex-shrink w-1/2 max-w-full ltr:text-right rtl:text-left">
                          <a class="text-sm cursor-pointer hover:text-primarys">Forgot password?</a>
                        </div>
                      </div>
                      <input v-model="formLogin.password" id="inputpass"
                        class="o_input"
                        aria-label="password" type="password" required>
                    </div>
                    <div class="mb-6">
                      <input
                        class="w-5 h-5 border border-gray-300 rounded text-primarys_focus form-checkbox dark:bg-gray-700 dark:border-gray-700 focus:outline-none"
                        type="checkbox" value="" id="remember" required>
                      <label class="ltr:ml-2 rtl:mr-2" for="remember">
                        Remember me
                      </label>
                    </div>
                    <div class="grid">
                      <button @click="loginUser"
                        class="inline-block px-4 py-2 leading-normal text-center text-gray-100 border rounded bg-primarys_focus border-primarys_focus hover:text-white hover:bg-primarys hover:ring-0 hover:border-primarys focus:bg-primarys focus:border-primarys focus:outline-none focus:ring-0">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                          class="inline-block w-4 h-4 ltr:mr-2 rtl:ml-2 bi bi-box-arrow-in-right" viewBox="0 0 16 16">
                          <path fill-rule="evenodd"
                            d="M6 3.5a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-8a.5.5 0 0 1-.5-.5v-2a.5.5 0 0 0-1 0v2A1.5 1.5 0 0 0 6.5 14h8a1.5 1.5 0 0 0 1.5-1.5v-9A1.5 1.5 0 0 0 14.5 2h-8A1.5 1.5 0 0 0 5 3.5v2a.5.5 0 0 0 1 0v-2z" />
                          <path fill-rule="evenodd"
                            d="M11.854 8.354a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H1.5a.5.5 0 0 0 0 1h8.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3z" />
                        </svg>Login
                      </button>
                    </div>
                    <div class="mt-6">
                      
                      <div v-if="reroute_emit" class="mb-4 text-center">Don't have an account? <div @click="$emit('goToRegister')"  class="hover:text-primarys_focus">Register</div>
                      </div>
                      <div v-else class="mb-4 text-center">Don't have an account? <NuxtLink to='/c/auth/register'><div
                            class="hover:text-primarys_focus">Register</div></NuxtLink>
                      </div>
                    </div>
                  </div>

                </div>
              </div>
            </div>
          </div>
          
        </div>
      </div>
    </div>
  </div>
</template>
