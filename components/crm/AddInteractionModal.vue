<template>
  <modal-small
    :open="open"
    title="Add Interaction"
    buttontext="Save Interaction"
    :showButtons="true"
    @submit="handleSubmit"
    @close="handleClose"
  >
    <template #body>
      <form @submit.prevent="handleSubmit" class="space-y-4">
        <!-- Contact Selection -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Contact <span class="text-red-400">*</span>
          </label>
          <select
            v-model="form.contactId"
            required
            class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="" disabled>Select a contact</option>
            <option v-for="contact in sortedContacts" :key="contact.id" :value="contact.id">
              {{ getFullName(contact) }}
              <span v-if="contact.businessName"> - {{ contact.businessName }}</span>
            </option>
          </select>
        </div>

        <!-- Interaction Type -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Type <span class="text-red-400">*</span>
          </label>
          <div class="grid grid-cols-3 gap-2">
            <button
              v-for="type in interactionTypes"
              :key="type.value"
              type="button"
              @click="form.type = type.value"
              :class="[
                'p-3 rounded-lg border transition-all duration-200 flex flex-col items-center space-y-1',
                form.type === type.value
                  ? 'bg-gradient-to-r from-blue-500/20 to-blue-600/20 border-blue-400/50 text-white'
                  : 'bg-gray-800/30 border-white/10 text-gray-400 hover:border-white/20 hover:text-white'
              ]"
            >
              <Icon :name="type.icon" class="w-6 h-6" />
              <span class="text-xs font-medium">{{ type.label }}</span>
            </button>
          </div>
        </div>

        <!-- Title -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Title <span class="text-red-400">*</span>
          </label>
          <input
            v-model="form.title"
            type="text"
            required
            placeholder="e.g., Follow-up call about proposal"
            class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <!-- Description -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Description
          </label>
          <textarea
            v-model="form.description"
            rows="4"
            placeholder="Add details about the interaction..."
            class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
          />
        </div>

        <!-- Date and Time -->
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">
              Date
            </label>
            <input
              v-model="form.date"
              type="date"
              class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">
              Time
            </label>
            <input
              v-model="form.time"
              type="time"
              class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        <!-- Additional Fields based on Type -->
        <div v-if="form.type === 'call' || form.type === 'meeting'" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">
              Duration (minutes)
            </label>
            <input
              v-model.number="form.duration"
              type="number"
              min="1"
              placeholder="30"
              class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        <div v-if="form.type === 'meeting'">
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Location
          </label>
          <input
            v-model="form.location"
            type="text"
            placeholder="Office, Zoom, Coffee Shop, etc."
            class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <div v-if="form.type === 'email'">
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Subject
          </label>
          <input
            v-model="form.subject"
            type="text"
            placeholder="Email subject line"
            class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <!-- Tags -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Tags
          </label>
          <input
            v-model="tagInput"
            @keydown.enter.prevent="addTag"
            type="text"
            placeholder="Type and press Enter to add tags"
            class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <div v-if="form.tags.length > 0" class="flex flex-wrap gap-2 mt-2">
            <span
              v-for="(tag, index) in form.tags"
              :key="index"
              class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-500/20 text-blue-300 border border-blue-400/30"
            >
              {{ tag }}
              <button
                type="button"
                @click="removeTag(index)"
                class="ml-2 text-blue-400 hover:text-blue-300"
              >
                <Icon name="mdi:close" class="w-3 h-3" />
              </button>
            </span>
          </div>
        </div>

        <!-- Priority -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Priority
          </label>
          <div class="flex space-x-2">
            <button
              v-for="priority in priorities"
              :key="priority.value"
              type="button"
              @click="form.priority = priority.value"
              :class="[
                'flex-1 px-3 py-2 rounded-lg border transition-all duration-200 text-sm font-medium',
                form.priority === priority.value
                  ? priority.activeClass
                  : 'bg-gray-800/30 border-white/10 text-gray-400 hover:border-white/20'
              ]"
            >
              {{ priority.label }}
            </button>
          </div>
        </div>
      </form>
    </template>
  </modal-small>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useContacts } from '~/composables/useContacts'
import { useBusinesses } from '~/composables/useBusinesses'
import { useActivities } from '~/composables/useActivities'
import { useCurrentUser } from '~/composables/useCurrentUser'

interface Props {
  open: boolean
  preselectedContactId?: string
}

const props = defineProps<Props>()
const emit = defineEmits(['close', 'success'])

// Composables
const { contacts, getFullName } = useContacts()
const { businesses } = useBusinesses()
const { createActivity } = useActivities()
const { currentUser } = useCurrentUser()

// Form state
const form = ref({
  contactId: props.preselectedContactId || '',
  type: 'note' as 'note' | 'call' | 'email' | 'meeting' | 'task' | 'message',
  title: '',
  description: '',
  date: new Date().toISOString().split('T')[0],
  time: new Date().toTimeString().slice(0, 5),
  duration: null as number | null,
  location: '',
  subject: '',
  tags: [] as string[],
  priority: 'medium' as 'low' | 'medium' | 'high'
})

const tagInput = ref('')

// Options
const interactionTypes = [
  { value: 'note', label: 'Note', icon: 'mdi:note-text' },
  { value: 'call', label: 'Call', icon: 'mdi:phone' },
  { value: 'email', label: 'Email', icon: 'mdi:email' },
  { value: 'meeting', label: 'Meeting', icon: 'mdi:calendar-account' },
  { value: 'message', label: 'Message', icon: 'mdi:message-text' },
  { value: 'task', label: 'Task', icon: 'mdi:check-circle' }
]

const priorities = [
  { value: 'low', label: 'Low', activeClass: 'bg-gray-600/20 border-gray-400/50 text-gray-300' },
  { value: 'medium', label: 'Medium', activeClass: 'bg-yellow-500/20 border-yellow-400/50 text-yellow-300' },
  { value: 'high', label: 'High', activeClass: 'bg-red-500/20 border-red-400/50 text-red-300' }
]

// Enhanced contacts with business names
const sortedContacts = computed(() => {
  return contacts.value
    .map(contact => {
      const business = contact.businessId 
        ? businesses.value.find(b => b.id === contact.businessId)
        : null
      return {
        ...contact,
        businessName: business?.name || null
      }
    })
    .sort((a, b) => {
      const nameA = getFullName(a).toLowerCase()
      const nameB = getFullName(b).toLowerCase()
      return nameA.localeCompare(nameB)
    })
})

// Methods
const addTag = () => {
  const tag = tagInput.value.trim()
  if (tag && !form.value.tags.includes(tag)) {
    form.value.tags.push(tag)
    tagInput.value = ''
  }
}

const removeTag = (index: number) => {
  form.value.tags.splice(index, 1)
}

const handleSubmit = async () => {
  if (!form.value.contactId || !form.value.title) {
    return
  }

  try {
    // Combine date and time
    const dateTime = new Date(`${form.value.date}T${form.value.time}`)

    // Build metadata based on type
    const metadata: any = {
      tags: form.value.tags,
      priority: form.value.priority
    }

    if (form.value.duration) {
      metadata.duration = form.value.duration
    }
    if (form.value.location) {
      metadata.location = form.value.location
    }
    if (form.value.subject) {
      metadata.subject = form.value.subject
    }

    // Get contact's business if exists
    const contact = contacts.value.find(c => c.id === form.value.contactId)
    const associatedEntities = [
      { entityType: 'contact', entityId: form.value.contactId }
    ]
    
    if (contact?.businessId) {
      associatedEntities.push({ 
        entityType: 'business', 
        entityId: contact.businessId 
      })
    }

    // Create the activity
    await createActivity({
      type: form.value.type,
      title: form.value.title,
      description: form.value.description,
      createdAt: dateTime,
      metadata,
      associatedEntities,
      status: 'completed',
      integration: 'manual'
    })

    // Reset form
    resetForm()
    
    // Notify success
    emit('success')
    emit('close')
  } catch (error) {
    console.error('Failed to create interaction:', error)
    // You could show an error notification here
  }
}

const handleClose = () => {
  resetForm()
  emit('close')
}

const resetForm = () => {
  form.value = {
    contactId: props.preselectedContactId || '',
    type: 'note',
    title: '',
    description: '',
    date: new Date().toISOString().split('T')[0],
    time: new Date().toTimeString().slice(0, 5),
    duration: null,
    location: '',
    subject: '',
    tags: [],
    priority: 'medium'
  }
  tagInput.value = ''
}

// Watch for prop changes
watch(() => props.preselectedContactId, (newId) => {
  if (newId) {
    form.value.contactId = newId
  }
})
</script>