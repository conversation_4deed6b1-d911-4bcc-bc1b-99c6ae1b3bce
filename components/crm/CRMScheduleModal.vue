<template>
  <Modal v-model:show="localShow" title="Schedule Follow-up" size="md">
    <div class="space-y-4">
      <!-- Contact Selection -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">
          Contact
        </label>
        <select 
          v-model="selectedContactId"
          required
          class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">Select a contact...</option>
          <option 
            v-for="contact in recentContacts" 
            :key="contact.id" 
            :value="contact.id"
          >
            {{ contact.firstName }} {{ contact.lastName }}
          </option>
        </select>
      </div>
      
      <!-- Follow-up Type -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">
          Follow-up Type
        </label>
        <select 
          v-model="followUpType"
          class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="call">Phone Call</option>
          <option value="email">Email</option>
          <option value="meeting">In-Person Meeting</option>
          <option value="task">General Task</option>
        </select>
      </div>
      
      <!-- Date & Time -->
      <div class="grid grid-cols-2 gap-3">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            Date
          </label>
          <input
            v-model="followUpDate"
            type="date"
            :min="today"
            required
            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            Time
          </label>
          <input
            v-model="followUpTime"
            type="time"
            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>
      
      <!-- Quick Time Presets -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Quick Presets
        </label>
        <div class="grid grid-cols-3 gap-2">
          <button
            v-for="preset in timePresets"
            :key="preset.label"
            @click="applyPreset(preset)"
            class="p-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            {{ preset.label }}
          </button>
        </div>
      </div>
      
      <!-- Priority -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">
          Priority
        </label>
        <div class="flex space-x-3">
          <label 
            v-for="priority in priorities"
            :key="priority.value"
            class="flex items-center"
          >
            <input
              v-model="followUpPriority"
              type="radio"
              :value="priority.value"
              class="mr-2 text-blue-600"
            />
            <span :class="priority.class">{{ priority.label }}</span>
          </label>
        </div>
      </div>
      
      <!-- Notes -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">
          Notes (Optional)
        </label>
        <textarea
          v-model="notes"
          placeholder="Add any additional details..."
          rows="3"
          class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
        />
      </div>
    </div>
    
    <!-- Modal Actions -->
    <template #actions>
      <button
        @click="closeModal"
        class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
      >
        Cancel
      </button>
      <button
        @click="scheduleFollowUp"
        :disabled="!canSchedule"
        class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 
               disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        Schedule
      </button>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useContacts } from '~/composables/useContacts'
import { useActivities } from '~/composables/useActivities'
import { useHaptic } from '~/composables/useHaptic'

// Props & Emits
interface Props {
  show: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:show': [value: boolean]
  'follow-up-scheduled': [followUp: any]
}>()

// Composables
const { contacts } = useContacts()
const { createActivity } = useActivities()
const { triggerHaptic } = useHaptic()

// Local state
const localShow = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const selectedContactId = ref('')
const followUpType = ref('call')
const followUpDate = ref('')
const followUpTime = ref('09:00')
const followUpPriority = ref<'low' | 'medium' | 'high'>('medium')
const notes = ref('')

// Computed
const today = computed(() => {
  return new Date().toISOString().split('T')[0]
})

const recentContacts = computed(() => {
  return contacts.value
    .filter(c => c.type === 'contact' || c.type === 'lead')
    .slice(0, 10)
})

const canSchedule = computed(() => {
  return selectedContactId.value && followUpDate.value
})

// Configuration
const timePresets = [
  { label: 'Tomorrow', days: 1 },
  { label: 'Next Week', days: 7 },
  { label: 'Next Month', days: 30 }
]

const priorities = [
  { value: 'low', label: 'Low', class: 'text-gray-600' },
  { value: 'medium', label: 'Medium', class: 'text-yellow-600' },
  { value: 'high', label: 'High', class: 'text-red-600' }
]

// Methods
const applyPreset = (preset: typeof timePresets[0]) => {
  const date = new Date()
  date.setDate(date.getDate() + preset.days)
  followUpDate.value = date.toISOString().split('T')[0]
  triggerHaptic('light')
}

const scheduleFollowUp = async () => {
  if (!canSchedule.value) return
  
  try {
    const dueDateTime = new Date(`${followUpDate.value}T${followUpTime.value}`)
    
    const taskData = {
      type: 'task' as const,
      title: `${followUpType.value.charAt(0).toUpperCase() + followUpType.value.slice(1)} follow-up`,
      description: notes.value || `Follow up with contact via ${followUpType.value}`,
      dueDate: dueDateTime,
      priority: followUpPriority.value,
      status: 'pending' as const,
      associatedEntities: [{
        entityType: 'contact' as const,
        entityId: selectedContactId.value
      }]
    }
    
    await createActivity(taskData)
    
    emit('follow-up-scheduled', taskData)
    closeModal()
    triggerHaptic('success')
  } catch (error) {
    console.error('Failed to schedule follow-up:', error)
    triggerHaptic('error')
  }
}

const closeModal = () => {
  selectedContactId.value = ''
  followUpType.value = 'call'
  followUpDate.value = ''
  followUpTime.value = '09:00'
  followUpPriority.value = 'medium'
  notes.value = ''
  localShow.value = false
}

// Initialize default date (tomorrow)
onMounted(() => {
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  followUpDate.value = tomorrow.toISOString().split('T')[0]
})
</script>