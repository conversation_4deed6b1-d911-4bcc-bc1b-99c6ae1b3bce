<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
    <div class="flex items-center justify-between mb-4">
      <img 
        v-if="logo"
        :src="logo" 
        :alt="name"
        class="h-10 w-auto"
      >
      <div v-else class="h-10 w-10 bg-gray-200 rounded flex items-center justify-center">
        <UIcon name="i-heroicons-building-office-2" class="h-6 w-6 text-gray-500" />
      </div>
    </div>
    
    <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ name }}</h3>
    <p class="text-sm text-gray-600 mb-4">{{ description }}</p>
    
    <UButton
      color="primary"
      variant="soft"
      block
      @click="$emit('connect')"
    >
      Connect {{ name }}
    </UButton>
  </div>
</template>

<script setup lang="ts">
interface Props {
  name: string
  logo?: string
  description: string
}

defineProps<Props>()
defineEmits<{
  connect: []
}>()
</script>