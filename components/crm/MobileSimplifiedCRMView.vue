<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useCurrentUser } from '~/composables/useCurrentUser'

interface Activity {
  id: string
  type: string
  title: string
  description?: string
  createdAt: Date
  status?: string
}

const props = defineProps<{
  recentActivities?: Activity[]
}>()

// Composables
const { currentUser } = useCurrentUser()

// State
const showCreateModal = ref(false)

// Computed
const displayActivities = computed(() => {
  if (!props.recentActivities) return []
  return props.recentActivities.slice(0, 5)
})

const getActivityIcon = (type: string) => {
  switch (type) {
    case 'call':
      return 'mdi:phone'
    case 'email':
      return 'mdi:email'
    case 'meeting':
      return 'mdi:account-group'
    case 'task':
      return 'mdi:check-circle'
    case 'note':
      return 'mdi:note-text'
    default:
      return 'mdi:calendar'
  }
}

const getActivityColor = (type: string) => {
  switch (type) {
    case 'call':
      return 'text-blue-400'
    case 'email':
      return 'text-purple-400'
    case 'meeting':
      return 'text-yellow-400'
    case 'task':
      return 'text-green-400'
    case 'note':
      return 'text-gray-400'
    default:
      return 'text-blue-400'
  }
}

const formatDate = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) return 'Today'
  if (days === 1) return 'Yesterday'
  if (days < 7) return `${days} days ago`
  
  return date.toLocaleDateString()
}
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="text-center">
      <div class="w-16 h-16 bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-4">
        <Icon name="mdi:view-dashboard" class="text-white text-2xl" />
      </div>
      <h1 class="text-2xl font-bold text-white mb-2">CRM Dashboard</h1>
      <p class="text-gray-300 text-sm px-4">Manage your business relationships on the go</p>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-2 gap-3">
      <NuxtLink
        to="/c/calendar"
        class="p-4 bg-gradient-to-br from-purple-600/20 to-pink-600/20 border border-purple-400/30 rounded-lg text-center hover:from-purple-600/30 hover:to-pink-600/30 transition-all duration-200"
      >
        <Icon name="mdi:calendar" class="text-2xl text-purple-400 mb-2" />
        <p class="text-white font-medium text-sm">Calendar</p>
        <p class="text-purple-300 text-xs">View events</p>
      </NuxtLink>
      
      <NuxtLink
        to="/c/businesscards/list-enhanced"
        class="p-4 bg-gradient-to-br from-blue-600/20 to-cyan-600/20 border border-blue-400/30 rounded-lg text-center hover:from-blue-600/30 hover:to-cyan-600/30 transition-all duration-200"
      >
        <Icon name="mdi:card-account-details" class="text-2xl text-blue-400 mb-2" />
        <p class="text-white font-medium text-sm">Contacts</p>
        <p class="text-blue-300 text-xs">Manage leads</p>
      </NuxtLink>
      
      <NuxtLink
        to="/c/businesscards-scan"
        class="p-4 bg-gradient-to-br from-green-600/20 to-emerald-600/20 border border-green-400/30 rounded-lg text-center hover:from-green-600/30 hover:to-emerald-600/30 transition-all duration-200"
      >
        <Icon name="mdi:camera" class="text-2xl text-green-400 mb-2" />
        <p class="text-white font-medium text-sm">Scan Card</p>
        <p class="text-green-300 text-xs">Add contact</p>
      </NuxtLink>
      
      <button
        class="p-4 bg-gradient-to-br from-orange-600/20 to-red-600/20 border border-orange-400/30 rounded-lg text-center hover:from-orange-600/30 hover:to-red-600/30 transition-all duration-200"
        @click="showCreateModal = true"
      >
        <Icon name="mdi:plus" class="text-2xl text-orange-400 mb-2" />
        <p class="text-white font-medium text-sm">Quick Note</p>
        <p class="text-orange-300 text-xs">Add reminder</p>
      </button>
    </div>

    <!-- Recent Activities -->
    <div class="bg-white/10 backdrop-blur-md rounded-lg border border-white/20 p-4">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-white">Recent Activities</h3>
        <NuxtLink
          to="/c/calendar"
          class="text-purple-400 hover:text-purple-300 text-sm font-medium"
        >
          View All
        </NuxtLink>
      </div>

      <div v-if="displayActivities.length === 0" class="text-center py-8">
        <Icon name="mdi:calendar-blank" class="text-4xl text-gray-600 mb-2" />
        <p class="text-gray-400 text-sm">No recent activities</p>
        <button
          class="mt-2 text-purple-400 hover:text-purple-300 text-sm font-medium"
          @click="showCreateModal = true"
        >
          Create your first note
        </button>
      </div>

      <div v-else class="space-y-3">
        <div
          v-for="activity in displayActivities"
          :key="activity.id"
          class="flex items-center space-x-3 p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors"
        >
          <div
            class="w-10 h-10 rounded-full flex items-center justify-center"
            :class="`bg-${activity.type === 'call' ? 'blue' : activity.type === 'email' ? 'purple' : activity.type === 'meeting' ? 'yellow' : activity.type === 'task' ? 'green' : 'gray'}-500/20`"
          >
            <Icon
              :name="getActivityIcon(activity.type)"
              :class="getActivityColor(activity.type)"
              class="text-lg"
            />
          </div>
          <div class="flex-1 min-w-0">
            <p class="text-white font-medium text-sm truncate">{{ activity.title }}</p>
            <p v-if="activity.description" class="text-gray-300 text-xs truncate">{{ activity.description }}</p>
            <p class="text-gray-400 text-xs">{{ formatDate(activity.createdAt) }}</p>
          </div>
          <div v-if="activity.status === 'pending'" class="w-2 h-2 bg-yellow-400 rounded-full"></div>
          <div v-else-if="activity.status === 'completed'" class="w-2 h-2 bg-green-400 rounded-full"></div>
        </div>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-2 gap-3">
      <div class="bg-gradient-to-br from-blue-600/20 to-cyan-600/20 border border-blue-400/30 rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-2xl font-bold text-blue-400">{{ displayActivities.filter(a => a.status === 'pending').length }}</p>
            <p class="text-blue-200 text-sm">Pending Tasks</p>
          </div>
          <Icon name="mdi:clock" class="text-blue-400 text-xl" />
        </div>
      </div>

      <div class="bg-gradient-to-br from-green-600/20 to-emerald-600/20 border border-green-400/30 rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-2xl font-bold text-green-400">{{ displayActivities.filter(a => a.status === 'completed').length }}</p>
            <p class="text-green-200 text-sm">Completed</p>
          </div>
          <Icon name="mdi:check-circle" class="text-green-400 text-xl" />
        </div>
      </div>
    </div>

    <!-- Navigation Menu -->
    <div class="bg-white/10 backdrop-blur-md rounded-lg border border-white/20 p-4">
      <h4 class="text-white font-medium mb-3">Navigation</h4>
      <div class="space-y-2">
        <NuxtLink
          to="/c/crm-dashboard"
          class="flex items-center space-x-3 p-2 rounded-lg hover:bg-white/10 transition-colors text-gray-300 hover:text-white"
        >
          <Icon name="mdi:view-dashboard" class="text-lg" />
          <span class="text-sm">Full Dashboard</span>
        </NuxtLink>
        <NuxtLink
          to="/c/profile"
          class="flex items-center space-x-3 p-2 rounded-lg hover:bg-white/10 transition-colors text-gray-300 hover:text-white"
        >
          <Icon name="mdi:account" class="text-lg" />
          <span class="text-sm">Profile</span>
        </NuxtLink>
        <NuxtLink
          to="/c/analytics"
          class="flex items-center space-x-3 p-2 rounded-lg hover:bg-white/10 transition-colors text-gray-300 hover:text-white"
        >
          <Icon name="mdi:chart-line" class="text-lg" />
          <span class="text-sm">Analytics</span>
        </NuxtLink>
      </div>
    </div>
  </div>

  <!-- Quick Create Modal -->
  <CreateActivityModal
    :open="showCreateModal"
    :contacts="[]"
    :preselected-date="new Date()"
    @close="showCreateModal = false"
    @success="showCreateModal = false"
  />
</template>