<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useCRM } from '~/composables/useCRM';
import { useNotifications } from '~/composables/useNotifications';
import type { ICRMTaskInput, ICRMClient } from '~/types/crm';

interface Props {
  show?: boolean;
  clientId?: string;
  existingTask?: any; // For editing existing tasks
}

interface Emits {
  (e: 'close'): void;
  (e: 'created', task: any): void;
  (e: 'updated', task: any): void;
}

const props = withDefaults(defineProps<Props>(), {
  show: false
});

const emit = defineEmits<Emits>();

const { 
  createTask, 
  updateTask, 
  getClients, 
  clients, 
  loading 
} = useCRM();

const { showSuccess, showError } = useNotifications();

// Form state
const task = ref<ICRMTaskInput>({
  client_id: props.clientId || '',
  title: '',
  description: '',
  due_date: new Date(),
  priority: 'medium'
});

const creating = ref(false);
const today = new Date().toISOString().split('T')[0];

// Priority options
const priorityOptions = [
  { value: 'low', label: 'Low', color: 'gray' },
  { value: 'medium', label: 'Medium', color: 'yellow' },
  { value: 'high', label: 'High', color: 'red' }
];

// Computed
const isEditing = computed(() => !!props.existingTask);
const modalTitle = computed(() => isEditing.value ? 'Edit Task' : 'Create Task');
const submitButtonText = computed(() => isEditing.value ? 'Update Task' : 'Create Task');

const selectedClient = computed(() => 
  clients.value.find(client => client.id === task.value.client_id)
);

// Methods
onMounted(async () => {
  await loadClients();
  
  if (props.existingTask) {
    // Populate form with existing task data
    task.value = {
      client_id: props.existingTask.client_id,
      title: props.existingTask.title,
      description: props.existingTask.description || '',
      due_date: props.existingTask.due_date.toDate(),
      reminder_date: props.existingTask.reminder_date?.toDate(),
      priority: props.existingTask.priority
    };
  }
});

const loadClients = async () => {
  try {
    await getClients();
  } catch (err: any) {
    showError(`Failed to load clients: ${err.message}`);
  }
};

const closeModal = () => {
  emit('close');
  resetForm();
};

const resetForm = () => {
  task.value = {
    client_id: props.clientId || '',
    title: '',
    description: '',
    due_date: new Date(),
    priority: 'medium'
  };
};

const validateForm = (): string | null => {
  if (!task.value.title.trim()) {
    return 'Task title is required';
  }
  
  if (!task.value.client_id) {
    return 'Please select a client';
  }
  
  if (!task.value.due_date) {
    return 'Due date is required';
  }
  
  // Check if due date is in the future
  const now = new Date();
  now.setHours(0, 0, 0, 0);
  const dueDate = new Date(task.value.due_date);
  dueDate.setHours(0, 0, 0, 0);
  
  if (dueDate < now) {
    return 'Due date cannot be in the past';
  }
  
  // Check reminder date if provided
  if (task.value.reminder_date) {
    const reminderDate = new Date(task.value.reminder_date);
    if (reminderDate > new Date(task.value.due_date)) {
      return 'Reminder date cannot be after due date';
    }
  }
  
  return null;
};

const submitTask = async () => {
  const validationError = validateForm();
  if (validationError) {
    showError(validationError);
    return;
  }
  
  creating.value = true;
  
  try {
    if (isEditing.value) {
      // Update existing task
      const updatedTask = await updateTask(props.existingTask.id, {
        title: task.value.title,
        description: task.value.description,
        due_date: new Date(task.value.due_date),
        reminder_date: task.value.reminder_date ? new Date(task.value.reminder_date) : undefined,
        priority: task.value.priority
      });
      
      if (updatedTask) {
        showSuccess('Task updated successfully!');
        emit('updated', updatedTask);
        closeModal();
      }
    } else {
      // Create new task
      const newTask = await createTask(task.value);
      
      if (newTask) {
        showSuccess('Task created successfully!');
        emit('created', newTask);
        closeModal();
      }
    }
  } catch (err: any) {
    showError(`Failed to ${isEditing.value ? 'update' : 'create'} task: ${err.message}`);
  } finally {
    creating.value = false;
  }
};

const formatDate = (date: string | Date): string => {
  if (!date) return '';
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toISOString().split('T')[0];
};
</script>

<template>
  <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Backdrop -->
      <div 
        class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
        @click="closeModal"
      ></div>
      
      <!-- Modal content -->
      <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
        <form @submit.prevent="submitTask">
          <div class="px-6 py-4">
            <div class="flex items-center justify-between mb-6">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                {{ modalTitle }}
              </h3>
              <button
                type="button"
                @click="closeModal"
                class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div class="space-y-6">
              <!-- Task Title -->
              <div>
                <label for="task-title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Task Title <span class="text-red-500">*</span>
                </label>
                <input
                  id="task-title"
                  v-model="task.title"
                  type="text"
                  placeholder="Follow up on proposal"
                  required
                  class="block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
              
              <!-- Description -->
              <div>
                <label for="task-description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Description
                </label>
                <textarea
                  id="task-description"
                  v-model="task.description"
                  rows="3"
                  placeholder="Additional details..."
                  class="block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                ></textarea>
              </div>
              
              <!-- Client Selection -->
              <div>
                <label for="task-client" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Related Client <span class="text-red-500">*</span>
                </label>
                <select
                  id="task-client"
                  v-model="task.client_id"
                  required
                  class="block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                >
                  <option value="">Select a client</option>
                  <option v-for="client in clients" :key="client.id" :value="client.id">
                    {{ client.name }} {{ client.company ? `(${client.company})` : '' }}
                  </option>
                </select>
                <div v-if="selectedClient" class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                  <div class="flex items-center space-x-4">
                    <span v-if="selectedClient.email">📧 {{ selectedClient.email }}</span>
                    <span v-if="selectedClient.phone">📞 {{ selectedClient.phone }}</span>
                  </div>
                </div>
              </div>
              
              <!-- Due Date and Reminder -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label for="due-date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Due Date <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="due-date"
                    v-model="task.due_date"
                    type="date"
                    :min="today"
                    required
                    class="block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                </div>
                
                <div>
                  <label for="reminder-date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Reminder Date
                  </label>
                  <input
                    id="reminder-date"
                    v-model="task.reminder_date"
                    type="date"
                    :min="today"
                    :max="formatDate(task.due_date)"
                    class="block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                </div>
              </div>
              
              <!-- Priority -->
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Priority
                </label>
                <div class="flex space-x-4">
                  <label
                    v-for="option in priorityOptions"
                    :key="option.value"
                    class="flex items-center cursor-pointer"
                  >
                    <input
                      v-model="task.priority"
                      type="radio"
                      :value="option.value"
                      class="mr-2 text-blue-600 focus:ring-blue-500"
                    />
                    <span 
                      :class="[
                        'px-2 py-1 rounded-full text-xs font-medium',
                        option.color === 'gray' ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300' :
                        option.color === 'yellow' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                        'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                      ]"
                    >
                      {{ option.label }}
                    </span>
                  </label>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Footer -->
          <div class="px-6 py-4 bg-gray-50 dark:bg-gray-700 flex justify-end space-x-3">
            <button
              type="button"
              @click="closeModal"
              :disabled="creating"
              class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:opacity-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600"
            >
              Cancel
            </button>
            <button
              type="submit"
              :disabled="creating"
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg v-if="creating" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ submitButtonText }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>