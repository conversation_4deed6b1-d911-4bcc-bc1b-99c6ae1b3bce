<template>
  <UModal :model-value="modelValue" @update:model-value="$emit('update:modelValue', $event)">
    <UCard>
      <template #header>
        <h3 class="text-lg font-semibold">Configure {{ connection.name }}</h3>
      </template>

      <UForm :state="form" @submit="save" class="space-y-6">
        <!-- Basic Settings -->
        <div>
          <h4 class="text-sm font-medium text-gray-900 mb-3">Sync Settings</h4>
          
          <div class="space-y-4">
            <UFormGroup label="Sync Enabled">
              <UToggle v-model="form.config.syncEnabled" />
            </UFormGroup>

            <UFormGroup label="Sync Direction" name="syncDirection">
              <USelectMenu
                v-model="form.config.syncDirection"
                :options="syncDirectionOptions"
                option-attribute="label"
                value-attribute="value"
              />
            </UFormGroup>

            <UFormGroup label="Sync Frequency" name="syncFrequency">
              <USelectMenu
                v-model="form.config.syncFrequency"
                :options="syncFrequencyOptions"
                option-attribute="label"
                value-attribute="value"
              />
            </UFormGroup>
          </div>
        </div>

        <!-- Entity Types -->
        <div>
          <h4 class="text-sm font-medium text-gray-900 mb-3">Entity Types to Sync</h4>
          
          <div class="space-y-2">
            <UCheckbox
              v-for="entityType in availableEntityTypes"
              :key="entityType.value"
              v-model="form.config.entityTypes"
              :value="entityType.value"
              :label="entityType.label"
            />
          </div>
        </div>

        <!-- Conflict Resolution -->
        <div>
          <h4 class="text-sm font-medium text-gray-900 mb-3">Conflict Resolution</h4>
          
          <URadioGroup
            v-model="form.config.conflictResolution"
            :options="conflictResolutionOptions"
          />
        </div>

        <!-- Webhook Settings -->
        <div v-if="supportsWebhooks">
          <h4 class="text-sm font-medium text-gray-900 mb-3">Real-time Updates</h4>
          
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <p class="text-sm font-medium text-gray-900">Enable Webhooks</p>
              <p class="text-sm text-gray-600">Receive real-time updates when data changes in {{ connection.crmType }}</p>
            </div>
            <UToggle v-model="webhooksEnabled" @change="toggleWebhooks" />
          </div>
          
          <div v-if="webhookUrl" class="mt-3">
            <UFormGroup label="Webhook URL" help="Configure this URL in your CRM">
              <div class="flex gap-2">
                <UInput :model-value="webhookUrl" readonly />
                <UButton
                  icon="i-heroicons-clipboard-document"
                  color="gray"
                  variant="solid"
                  @click="copyWebhookUrl"
                />
              </div>
            </UFormGroup>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex justify-end gap-3 pt-4 border-t">
          <UButton color="gray" variant="ghost" @click="$emit('update:modelValue', false)">
            Cancel
          </UButton>
          <UButton type="submit" :loading="saving">
            Save Configuration
          </UButton>
        </div>
      </UForm>
    </UCard>
  </UModal>
</template>

<script setup lang="ts">
import { useCRMIntegration } from '~/composables/useCRMIntegration'
import type { CRMConnection } from '~/types/crm'

const props = defineProps<{
  modelValue: boolean
  connection: CRMConnection
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  save: [updates: Partial<CRMConnection>]
}>()

const toast = useToast()
const { createWebhook, deleteWebhook } = useCRMIntegration()

const form = reactive({
  config: {
    syncEnabled: props.connection.config?.syncEnabled ?? false,
    syncDirection: props.connection.config?.syncDirection ?? 'bidirectional',
    syncFrequency: props.connection.config?.syncFrequency ?? 'hourly',
    entityTypes: props.connection.config?.entityTypes ?? [],
    conflictResolution: props.connection.config?.conflictResolution ?? 'newest_wins'
  }
})

const saving = ref(false)
const webhooksEnabled = ref(false)
const webhookUrl = ref('')

const syncDirectionOptions = [
  { label: 'To CRM Only', value: 'to_crm' },
  { label: 'From CRM Only', value: 'from_crm' },
  { label: 'Bidirectional', value: 'bidirectional' }
]

const syncFrequencyOptions = [
  { label: 'Real-time', value: 'realtime' },
  { label: 'Every Hour', value: 'hourly' },
  { label: 'Daily', value: 'daily' },
  { label: 'Manual Only', value: 'manual' }
]

const conflictResolutionOptions = [
  { label: 'Local Data Wins', value: 'local_wins' },
  { label: 'CRM Data Wins', value: 'crm_wins' },
  { label: 'Newest Data Wins', value: 'newest_wins' },
  { label: 'Manual Review', value: 'manual' }
]

const availableEntityTypes = computed(() => {
  const types = {
    salesforce: [
      { label: 'Contacts', value: 'Contact' },
      { label: 'Leads', value: 'Lead' },
      { label: 'Accounts', value: 'Account' },
      { label: 'Opportunities', value: 'Opportunity' }
    ],
    hubspot: [
      { label: 'Contacts', value: 'contacts' },
      { label: 'Companies', value: 'companies' },
      { label: 'Deals', value: 'deals' },
      { label: 'Tickets', value: 'tickets' }
    ],
    pipedrive: [
      { label: 'Persons', value: 'persons' },
      { label: 'Organizations', value: 'organizations' },
      { label: 'Deals', value: 'deals' },
      { label: 'Activities', value: 'activities' }
    ]
  }
  
  return types[props.connection.crmType] || []
})

const supportsWebhooks = computed(() => {
  return ['hubspot', 'pipedrive'].includes(props.connection.crmType)
})

const save = async () => {
  saving.value = true
  try {
    await emit('save', { config: form.config })
  } finally {
    saving.value = false
  }
}

const toggleWebhooks = async (enabled: boolean) => {
  try {
    if (enabled) {
      webhookUrl.value = await createWebhook(props.connection.id)
    } else {
      await deleteWebhook(props.connection.id)
      webhookUrl.value = ''
    }
  } catch (error) {
    webhooksEnabled.value = !enabled // Revert
    toast.add({
      title: 'Error',
      description: error.message,
      color: 'red'
    })
  }
}

const copyWebhookUrl = () => {
  navigator.clipboard.writeText(webhookUrl.value)
  toast.add({
    title: 'Copied',
    description: 'Webhook URL copied to clipboard',
    color: 'green'
  })
}
</script>