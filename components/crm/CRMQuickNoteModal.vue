<template>
  <Modal v-model:show="localShow" title="Quick Note" size="md">
    <div class="space-y-4">
      <!-- Contact Selection -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">
          Associate with Contact (Optional)
        </label>
        <select 
          v-model="selectedContactId"
          class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">Select a contact...</option>
          <option 
            v-for="contact in recentContacts" 
            :key="contact.id" 
            :value="contact.id"
          >
            {{ contact.firstName }} {{ contact.lastName }}
          </option>
        </select>
      </div>
      
      <!-- Note Content -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">
          Note
        </label>
        <textarea
          v-model="noteContent"
          placeholder="Type your note or tap the microphone to use voice input..."
          rows="4"
          class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
          :disabled="isRecording"
        />
      </div>
      
      <!-- Voice Input Controls -->
      <div class="flex items-center space-x-3">
        <button
          @click="toggleVoiceInput"
          :disabled="!speechSupported"
          :class="[
            'flex-1 p-3 rounded-lg font-medium transition-colors',
            isRecording 
              ? 'bg-red-600 text-white hover:bg-red-700' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200',
            !speechSupported && 'opacity-50 cursor-not-allowed'
          ]"
        >
          <Icon 
            :name="isRecording ? 'mdi:stop' : 'mdi:microphone'" 
            class="mr-2" 
            size="18"
          />
          {{ isRecording ? 'Stop Recording' : 'Voice Input' }}
        </button>
        
        <button
          v-if="noteContent"
          @click="clearNote"
          class="p-3 text-gray-500 hover:text-gray-700"
          aria-label="Clear note"
        >
          <Icon name="mdi:close" size="18" />
        </button>
      </div>
      
      <!-- Voice Recording Status -->
      <div v-if="isRecording" class="text-center">
        <div class="inline-flex items-center space-x-2 text-red-600">
          <div class="w-2 h-2 bg-red-600 rounded-full animate-pulse"></div>
          <span class="text-sm font-medium">Recording... Speak now</span>
        </div>
      </div>
    </div>
    
    <!-- Modal Actions -->
    <template #actions>
      <button
        @click="closeModal"
        class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
      >
        Cancel
      </button>
      <button
        @click="saveNote"
        :disabled="!noteContent.trim()"
        class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 
               disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        Save Note
      </button>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useContacts } from '~/composables/useContacts'
import { useActivities } from '~/composables/useActivities'
import { useHaptic } from '~/composables/useHaptic'

// Props & Emits
interface Props {
  show: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:show': [value: boolean]
  'note-saved': [note: any]
}>()

// Composables
const { contacts } = useContacts()
const { createActivity } = useActivities()
const { triggerHaptic } = useHaptic()

// Local state
const localShow = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const selectedContactId = ref('')
const noteContent = ref('')
const isRecording = ref(false)
const speechSupported = ref(false)

// Speech recognition
let recognition: any = null

// Recent contacts for quick selection
const recentContacts = computed(() => {
  return contacts.value
    .filter(c => c.type === 'contact' || c.type === 'lead')
    .slice(0, 10)
})

// Initialize speech recognition
onMounted(() => {
  if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
    speechSupported.value = true
    const SpeechRecognition = (window as any).webkitSpeechRecognition || (window as any).SpeechRecognition
    recognition = new SpeechRecognition()
    
    recognition.continuous = false
    recognition.interimResults = false
    recognition.lang = 'en-US'
    
    recognition.onresult = (event: any) => {
      const transcript = event.results[0][0].transcript
      noteContent.value = (noteContent.value + ' ' + transcript).trim()
      isRecording.value = false
      triggerHaptic('success')
    }
    
    recognition.onerror = () => {
      isRecording.value = false
      triggerHaptic('error')
    }
    
    recognition.onend = () => {
      isRecording.value = false
    }
  }
})

// Methods
const toggleVoiceInput = () => {
  if (!speechSupported.value || !recognition) return
  
  if (isRecording.value) {
    recognition.stop()
    isRecording.value = false
  } else {
    recognition.start()
    isRecording.value = true
    triggerHaptic('medium')
  }
}

const clearNote = () => {
  noteContent.value = ''
  triggerHaptic('light')
}

const saveNote = async () => {
  if (!noteContent.value.trim()) return
  
  try {
    const noteData = {
      type: 'note' as const,
      content: noteContent.value.trim(),
      associatedEntities: selectedContactId.value ? [{
        entityType: 'contact' as const,
        entityId: selectedContactId.value
      }] : [],
      title: `Quick Note - ${new Date().toLocaleDateString()}`
    }
    
    await createActivity(noteData)
    
    emit('note-saved', noteData)
    closeModal()
    triggerHaptic('success')
  } catch (error) {
    console.error('Failed to save note:', error)
    triggerHaptic('error')
  }
}

const closeModal = () => {
  noteContent.value = ''
  selectedContactId.value = ''
  if (isRecording.value) {
    recognition?.stop()
    isRecording.value = false
  }
  localShow.value = false
}
</script>