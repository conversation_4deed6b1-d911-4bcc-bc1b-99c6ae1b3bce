<template>
  <div class="interaction-timeline">
    <!-- Header with filters -->
    <div v-if="!compact" class="timeline-header">
      <h3 class="timeline-title">Activity Timeline</h3>
      <div class="filter-chips">
        <button
          v-for="type in activityTypes"
          :key="type.value"
          @click="toggleFilter(type.value)"
          :class="['filter-chip', { active: selectedFilters.includes(type.value) }]"
          :style="selectedFilters.includes(type.value) ? `background: ${type.color}20; border-color: ${type.color};` : ''"
        >
          <Icon :name="type.icon" :style="{ color: selectedFilters.includes(type.value) ? type.color : '#94a3b8' }" />
          <span>{{ type.label }}</span>
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="timeline-loading">
      <div class="loading-skeleton" v-for="i in 3" :key="i">
        <div class="skeleton-icon"></div>
        <div class="skeleton-content">
          <div class="skeleton-title"></div>
          <div class="skeleton-text"></div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else-if="filteredActivities.length === 0" class="timeline-empty">
      <Icon name="mdi:calendar-blank" size="48" class="empty-icon" />
      <p class="empty-title">No activities yet</p>
      <p class="empty-text">
        {{ selectedFilters.length > 0 ? 'No activities match your filters' : 'Start tracking interactions with this contact' }}
      </p>
    </div>

    <!-- Timeline -->
    <div v-else class="timeline-container" :class="{ compact }">
      <TransitionGroup name="timeline-item" tag="div" class="timeline-list">
        <div
          v-for="(activity, index) in displayedActivities"
          :key="activity.id"
          class="timeline-item"
          :class="{ 'fade-in': true }"
          :style="{ animationDelay: `${index * 50}ms` }"
        >
          <!-- Timeline Line -->
          <div class="timeline-line" v-if="!compact"></div>
          
          <!-- Activity Icon -->
          <div 
            class="activity-icon"
            :style="{ background: `linear-gradient(135deg, ${getActivityColor(activity.type)}, ${getActivityColor(activity.type)}88)` }"
          >
            <Icon :name="getActivityIcon(activity.type)" size="20" />
          </div>

          <!-- Activity Content -->
          <div class="activity-content">
            <div class="activity-header">
              <h4 class="activity-title">{{ activity.title }}</h4>
              <span class="activity-time">{{ formatTime(activity.createdAt || activity.timestamp) }}</span>
            </div>
            
            <p v-if="!compact || index < 2" class="activity-description">
              {{ activity.description || activity.content }}
            </p>

            <div v-if="!compact && activity.metadata" class="activity-metadata">
              <span v-if="activity.metadata.duration" class="metadata-item">
                <Icon name="mdi:clock-outline" size="14" />
                {{ activity.metadata.duration }}{{ typeof activity.metadata.duration === 'number' ? ' min' : '' }}
              </span>
              <span v-if="activity.metadata.location" class="metadata-item">
                <Icon name="mdi:map-marker-outline" size="14" />
                {{ activity.metadata.location }}
              </span>
              <span v-if="activity.metadata.attachments" class="metadata-item">
                <Icon name="mdi:paperclip" size="14" />
                {{ activity.metadata.attachments }} files
              </span>
              <span v-if="activity.metadata.subject" class="metadata-item">
                <Icon name="mdi:text-subject" size="14" />
                {{ activity.metadata.subject }}
              </span>
            </div>

            <div v-if="(activity.tags && activity.tags.length > 0) || (activity.metadata?.tags && activity.metadata.tags.length > 0)" class="activity-tags">
              <span v-for="tag in (activity.tags || activity.metadata?.tags || [])" :key="tag" class="activity-tag">
                {{ tag }}
              </span>
            </div>

            <!-- Priority indicator -->
            <div 
              v-if="activity.metadata?.priority"
              class="activity-priority"
              :class="getPriorityClass(activity.metadata.priority)"
            >
              <Icon name="mdi:flag" size="12" />
              <span>{{ activity.metadata.priority }} priority</span>
            </div>
          </div>
        </div>
      </TransitionGroup>

      <!-- Show More Button -->
      <button
        v-if="compact && filteredActivities.length > 3"
        @click="showAll = !showAll"
        class="show-more-btn"
      >
        <span>{{ showAll ? 'Show less' : `Show ${filteredActivities.length - 3} more` }}</span>
        <Icon :name="showAll ? 'mdi:chevron-up' : 'mdi:chevron-down'" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { format, formatDistanceToNow, isToday, isYesterday } from 'date-fns'

interface Activity {
  id: string
  type: 'note' | 'call' | 'email' | 'meeting' | 'task'
  title: string
  description?: string
  content?: string // Support legacy field
  createdAt?: Date | string
  timestamp?: Date | string // Support legacy field
  metadata?: {
    duration?: string | number
    location?: string
    attachments?: number
    subject?: string
    tags?: string[]
    priority?: 'low' | 'medium' | 'high'
  }
  tags?: string[]
}

interface Props {
  activities?: Activity[]
  loading?: boolean
  compact?: boolean
  businessCardId?: string // Support legacy prop
}

const props = withDefaults(defineProps<Props>(), {
  activities: () => [],
  loading: false,
  compact: false
})

// If businessCardId is provided, use the legacy composable
const legacyActivities = ref<Activity[]>([])
if (props.businessCardId) {
  import('~/composables/useCRMInteractions').then(({ getInteractionsForCard }) => {
    legacyActivities.value = getInteractionsForCard(props.businessCardId!)
  })
}

const selectedFilters = ref<string[]>([])
const showAll = ref(false)

const activityTypes = [
  { value: 'note', label: 'Notes', icon: 'mdi:note-text', color: '#3b82f6' },
  { value: 'call', label: 'Calls', icon: 'mdi:phone', color: '#10b981' },
  { value: 'email', label: 'Emails', icon: 'mdi:email', color: '#8b5cf6' },
  { value: 'meeting', label: 'Meetings', icon: 'mdi:calendar-clock', color: '#f59e0b' },
  { value: 'task', label: 'Tasks', icon: 'mdi:checkbox-marked-circle', color: '#ef4444' }
]

const toggleFilter = (type: string) => {
  const index = selectedFilters.value.indexOf(type)
  if (index > -1) {
    selectedFilters.value.splice(index, 1)
  } else {
    selectedFilters.value.push(type)
  }
}

// Use either provided activities or legacy activities
const allActivities = computed(() => {
  return props.businessCardId ? legacyActivities.value : props.activities
})

const filteredActivities = computed(() => {
  if (selectedFilters.value.length === 0) {
    return allActivities.value
  }
  return allActivities.value.filter(activity => 
    selectedFilters.value.includes(activity.type)
  )
})

const displayedActivities = computed(() => {
  if (!props.compact || showAll.value) {
    return filteredActivities.value
  }
  return filteredActivities.value.slice(0, 3)
})

const getActivityIcon = (type: string) => {
  const activityType = activityTypes.find(t => t.value === type)
  return activityType?.icon || 'mdi:circle'
}

const getActivityColor = (type: string) => {
  const activityType = activityTypes.find(t => t.value === type)
  return activityType?.color || '#64748b'
}

const formatTime = (date: Date | string | undefined) => {
  if (!date) return ''
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  if (isToday(dateObj)) {
    return `Today at ${format(dateObj, 'h:mm a')}`
  } else if (isYesterday(dateObj)) {
    return `Yesterday at ${format(dateObj, 'h:mm a')}`
  } else {
    const distance = formatDistanceToNow(dateObj, { addSuffix: true })
    if (distance.includes('month') || distance.includes('year')) {
      return format(dateObj, 'MMM d, yyyy')
    }
    return distance
  }
}

const getPriorityClass = (priority: string) => {
  const classes = {
    low: 'priority-low',
    medium: 'priority-medium',
    high: 'priority-high'
  }
  return classes[priority as keyof typeof classes] || classes.medium
}
</script>

<style scoped>
.interaction-timeline {
  width: 100%;
}

/* Header */
.timeline-header {
  margin-bottom: 1.5rem;
}

.timeline-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #f1f5f9;
  margin-bottom: 1rem;
}

.filter-chips {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.filter-chip {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(100, 116, 139, 0.3);
  border-radius: 9999px;
  color: #94a3b8;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-chip:hover {
  background: rgba(51, 65, 85, 0.5);
  border-color: rgba(148, 163, 184, 0.5);
}

.filter-chip.active {
  color: #f1f5f9;
}

/* Loading State */
.timeline-loading {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.loading-skeleton {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.5), rgba(15, 23, 42, 0.5));
  border-radius: 0.75rem;
}

.skeleton-icon {
  width: 3rem;
  height: 3rem;
  background: rgba(51, 65, 85, 0.5);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.skeleton-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.skeleton-title {
  width: 40%;
  height: 1.25rem;
  background: rgba(51, 65, 85, 0.5);
  border-radius: 0.25rem;
  animation: pulse 2s infinite;
}

.skeleton-text {
  width: 80%;
  height: 1rem;
  background: rgba(51, 65, 85, 0.3);
  border-radius: 0.25rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Empty State */
.timeline-empty {
  text-align: center;
  padding: 3rem 1rem;
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.3), rgba(15, 23, 42, 0.3));
  border-radius: 1rem;
  border: 1px solid rgba(51, 65, 85, 0.3);
}

.empty-icon {
  color: #475569;
  margin-bottom: 1rem;
}

.empty-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #cbd5e1;
  margin-bottom: 0.5rem;
}

.empty-text {
  color: #64748b;
  font-size: 0.875rem;
}

/* Timeline Container */
.timeline-container {
  position: relative;
}

.timeline-container.compact .timeline-list {
  gap: 0.75rem;
}

.timeline-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Timeline Item */
.timeline-item {
  position: relative;
  display: flex;
  gap: 1rem;
  opacity: 0;
}

.timeline-item.fade-in {
  animation: fadeIn 0.5s ease forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
  from {
    opacity: 0;
    transform: translateY(10px);
  }
}

/* Timeline Line */
.timeline-line {
  position: absolute;
  left: 1.5rem;
  top: 3rem;
  width: 2px;
  height: calc(100% + 1.5rem);
  background: linear-gradient(to bottom, rgba(51, 65, 85, 0.5), transparent);
}

/* Activity Icon */
.activity-icon {
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  z-index: 1;
  transition: transform 0.2s ease;
}

.timeline-item:hover .activity-icon {
  transform: scale(1.1);
}

/* Activity Content */
.activity-content {
  flex: 1;
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8), rgba(15, 23, 42, 0.8));
  border: 1px solid rgba(51, 65, 85, 0.3);
  border-radius: 0.75rem;
  padding: 1rem;
  transition: all 0.2s ease;
}

.timeline-item:hover .activity-content {
  border-color: rgba(100, 116, 139, 0.5);
  transform: translateX(2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: start;
  margin-bottom: 0.5rem;
  gap: 1rem;
}

.activity-title {
  font-weight: 600;
  color: #f1f5f9;
  font-size: 1rem;
}

.activity-time {
  font-size: 0.75rem;
  color: #64748b;
  white-space: nowrap;
}

.activity-description {
  color: #cbd5e1;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 0.75rem;
}

/* Metadata */
.activity-metadata {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.75rem;
  flex-wrap: wrap;
}

.metadata-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: #94a3b8;
}

.metadata-item :deep(.icon) {
  color: #64748b;
}

/* Tags */
.activity-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-bottom: 0.75rem;
}

.activity-tag {
  padding: 0.25rem 0.75rem;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 9999px;
  font-size: 0.75rem;
  color: #60a5fa;
}

/* Priority */
.activity-priority {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  transition: all 0.2s ease;
}

.activity-priority.priority-low {
  background: rgba(100, 116, 139, 0.1);
  border: 1px solid rgba(100, 116, 139, 0.3);
  color: #94a3b8;
}

.activity-priority.priority-medium {
  background: rgba(251, 191, 36, 0.1);
  border: 1px solid rgba(251, 191, 36, 0.3);
  color: #fbbf24;
}

.activity-priority.priority-high {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #ef4444;
}

/* Show More Button */
.show-more-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 1rem auto 0;
  padding: 0.5rem 1rem;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(51, 65, 85, 0.5);
  border-radius: 0.5rem;
  color: #94a3b8;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.show-more-btn:hover {
  background: rgba(51, 65, 85, 0.5);
  color: #cbd5e1;
  border-color: rgba(100, 116, 139, 0.5);
}

/* Mobile Responsive */
@media (max-width: 640px) {
  .filter-chips {
    gap: 0.375rem;
  }
  
  .filter-chip {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
  }
  
  .activity-header {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .activity-time {
    font-size: 0.7rem;
  }
  
  .timeline-item {
    gap: 0.75rem;
  }
  
  .activity-icon {
    width: 2.5rem;
    height: 2.5rem;
  }
  
  .timeline-line {
    left: 1.25rem;
  }
}

/* Transitions */
.timeline-item-enter-active,
.timeline-item-leave-active {
  transition: all 0.3s ease;
}

.timeline-item-enter-from {
  opacity: 0;
  transform: translateX(-10px);
}

.timeline-item-leave-to {
  opacity: 0;
  transform: translateX(10px);
}
</style>