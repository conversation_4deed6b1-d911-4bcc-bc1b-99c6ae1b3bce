<template>
  <div class="crm-floating-actions">
    <!-- Quick Actions Expandable Menu -->
    <transition-group 
      name="fab-action" 
      tag="div" 
      class="flex flex-col items-center space-y-3"
    >
      <button
        v-if="showActions"
        v-for="action in quickActions"
        :key="action.id"
        @click="handleAction(action)"
        :aria-label="action.label"
        class="fab-action-button w-12 h-12 bg-white shadow-lg rounded-full 
               flex items-center justify-center text-gray-700 hover:bg-blue-50
               transition-all duration-200 hover:scale-105"
      >
        <Icon :name="action.icon" size="20" />
      </button>
    </transition-group>
    
    <!-- Main FAB Toggle -->
    <button 
      @click="toggleActions"
      :aria-label="showActions ? 'Close quick actions' : 'Open quick actions'"
      :aria-expanded="showActions"
      class="main-fab w-14 h-14 bg-blue-600 hover:bg-blue-700 rounded-full 
             shadow-xl flex items-center justify-center text-white
             transition-all duration-300 hover:scale-110 active:scale-95"
    >
      <Icon 
        name="mdi:plus" 
        size="24"
        :class="{ 'rotate-45': showActions }"
        class="transition-transform duration-300"
      />
    </button>
    
    <!-- Action Modals -->
    <CRMQuickNoteModal 
      v-model:show="showQuickNote"
      @note-saved="handleNoteSaved"
    />
    
    <CRMScheduleModal 
      v-model:show="showSchedule"
      @follow-up-scheduled="handleFollowUpScheduled"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useHaptic } from '~/composables/useHaptic'

// Props
interface Props {
  showOnPages?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  showOnPages: () => ['/c/crm-dashboard', '/c/businesscards/list-enhanced', '/c/businesscards-scan']
})

// Composables
const route = useRoute()
const router = useRouter()
const { triggerHaptic } = useHaptic()

// State
const showActions = ref(false)
const showQuickNote = ref(false)
const showSchedule = ref(false)

// Show FAB only on CRM-related pages
const shouldShow = computed(() => {
  return props.showOnPages.some(page => route.path.includes(page))
})

// Quick actions configuration
const quickActions = [
  {
    id: 'scan-card',
    label: 'Scan Business Card',
    icon: 'mdi:camera',
    action: 'scan'
  },
  {
    id: 'quick-note',
    label: 'Quick Note',
    icon: 'mdi:note-plus',
    action: 'note'
  },
  {
    id: 'schedule-followup',
    label: 'Schedule Follow-up',
    icon: 'mdi:calendar-plus',
    action: 'schedule'
  }
]

// Methods
const toggleActions = () => {
  showActions.value = !showActions.value
  triggerHaptic('light')
}

const handleAction = (action: typeof quickActions[0]) => {
  triggerHaptic('medium')
  showActions.value = false
  
  switch (action.action) {
    case 'scan':
      router.push('/c/businesscards-scan')
      break
    case 'note':
      showQuickNote.value = true
      break
    case 'schedule':
      showSchedule.value = true
      break
  }
}

const handleNoteSaved = (note: any) => {
  // Handle note saved
  console.log('Note saved:', note)
  triggerHaptic('heavy')
}

const handleFollowUpScheduled = (followUp: any) => {
  // Handle follow-up scheduled
  console.log('Follow-up scheduled:', followUp)
  triggerHaptic('heavy')
}

// Close actions when clicking outside
const closeOnClickOutside = (event: MouseEvent) => {
  const target = event.target as Element
  if (!target.closest('.crm-floating-actions')) {
    showActions.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', closeOnClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', closeOnClickOutside)
})
</script>

<style scoped>
.crm-floating-actions {
  position: fixed;
  bottom: 5rem; /* Above mobile navigation */
  right: 1rem;
  z-index: 50;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* FAB Action Transitions */
.fab-action-enter-active,
.fab-action-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.fab-action-enter-from {
  opacity: 0;
  transform: translateY(20px) scale(0.8);
}

.fab-action-leave-to {
  opacity: 0;
  transform: translateY(20px) scale(0.8);
}

.fab-action-move {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Staggered animation for multiple actions */
.fab-action-button:nth-child(1) { transition-delay: 0.1s; }
.fab-action-button:nth-child(2) { transition-delay: 0.2s; }
.fab-action-button:nth-child(3) { transition-delay: 0.3s; }

/* Main FAB pulse animation when actions are open */
.main-fab {
  position: relative;
}

.main-fab::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background: linear-gradient(45deg, #3b82f6, #1d4ed8);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.main-fab:hover::before {
  opacity: 1;
}

/* Touch optimization for mobile */
@media (hover: none) {
  .fab-action-button:hover,
  .main-fab:hover {
    transform: none;
  }
  
  .fab-action-button:active {
    transform: scale(0.95);
  }
  
  .main-fab:active {
    transform: scale(0.95);
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .fab-action-enter-active,
  .fab-action-leave-active,
  .main-fab,
  .fab-action-button {
    transition: none;
  }
}
</style>