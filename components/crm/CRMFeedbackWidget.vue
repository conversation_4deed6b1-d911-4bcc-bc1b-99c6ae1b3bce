<template>
  <div class="crm-feedback-widget">
    <!-- Feedback Trigger Button -->
    <button
      v-if="!showFeedback"
      @click="showFeedback = true"
      class="feedback-trigger fixed bottom-4 left-4 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors z-40"
      aria-label="Share feedback about CRM features"
    >
      <Icon name="mdi:comment-question" size="20" />
    </button>
    
    <!-- Feedback Panel -->
    <transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 translate-y-4 scale-95"
      enter-to-class="opacity-100 translate-y-0 scale-100"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100 translate-y-0 scale-100"
      leave-to-class="opacity-0 translate-y-4 scale-95"
    >
      <div
        v-if="showFeedback"
        class="feedback-panel fixed bottom-20 left-4 w-80 bg-white rounded-lg shadow-xl border border-gray-200 p-4 z-50"
        @click.stop
      >
        <!-- Header -->
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-800">CRM Feedback</h3>
          <button
            @click="showFeedback = false"
            class="text-gray-400 hover:text-gray-600 transition-colors"
            aria-label="Close feedback"
          >
            <Icon name="mdi:close" size="20" />
          </button>
        </div>
        
        <!-- Feedback Form -->
        <form @submit.prevent="submitFeedback" class="space-y-4">
          <!-- Feature Selection -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Which CRM feature are you providing feedback on?
            </label>
            <select
              v-model="feedback.feature"
              required
              class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Select a feature...</option>
              <option value="navigation">Navigation & Access</option>
              <option value="dashboard">Dashboard Integration</option>
              <option value="mobile-menu">Mobile Menu</option>
              <option value="floating-actions">Quick Actions (FAB)</option>
              <option value="performance">Performance & Speed</option>
              <option value="overall">Overall Experience</option>
            </select>
          </div>
          
          <!-- Rating -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              How would you rate this feature?
            </label>
            <div class="flex space-x-1">
              <button
                v-for="star in 5"
                :key="star"
                type="button"
                @click="feedback.rating = star"
                :class="[
                  'text-2xl transition-colors',
                  star <= feedback.rating ? 'text-yellow-400' : 'text-gray-300'
                ]"
                :aria-label="`Rate ${star} stars`"
              >
                ★
              </button>
            </div>
            <span class="text-xs text-gray-500">{{ getRatingText(feedback.rating) }}</span>
          </div>
          
          <!-- Comment -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Tell us more (optional)
            </label>
            <textarea
              v-model="feedback.comment"
              placeholder="What did you like or what could be improved?"
              rows="3"
              class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
            />
          </div>
          
          <!-- Improvement Suggestions -->
          <div v-if="feedback.rating <= 3">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              What would make this better?
            </label>
            <div class="space-y-2">
              <label
                v-for="improvement in improvementOptions"
                :key="improvement.value"
                class="flex items-center"
              >
                <input
                  v-model="feedback.improvements"
                  type="checkbox"
                  :value="improvement.value"
                  class="mr-2 text-blue-600"
                />
                <span class="text-sm text-gray-700">{{ improvement.label }}</span>
              </label>
            </div>
          </div>
          
          <!-- Submit Button -->
          <div class="flex space-x-2 pt-2">
            <button
              type="button"
              @click="showFeedback = false"
              class="flex-1 px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              :disabled="!feedback.feature || !feedback.rating || isSubmitting"
              class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {{ isSubmitting ? 'Sending...' : 'Send Feedback' }}
            </button>
          </div>
        </form>
        
        <!-- Thank You Message -->
        <div v-if="feedbackSubmitted" class="text-center py-4">
          <Icon name="mdi:check-circle" class="text-green-500 text-3xl mb-2" />
          <p class="text-sm text-gray-600">Thank you for your feedback!</p>
        </div>
      </div>
    </transition>
    
    <!-- Overlay -->
    <div
      v-if="showFeedback"
      @click="showFeedback = false"
      class="fixed inset-0 bg-black bg-opacity-10 z-40"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useCRMAnalytics } from '~/composables/useCRMAnalytics'

// Props
interface Props {
  autoShow?: boolean
  showAfterActions?: number
}

const props = withDefaults(defineProps<Props>(), {
  autoShow: false,
  showAfterActions: 5
})

// Composables
const { trackUserFeedback } = useCRMAnalytics()

// State
const showFeedback = ref(props.autoShow)
const isSubmitting = ref(false)
const feedbackSubmitted = ref(false)
const actionCount = ref(0)

// Feedback data
const feedback = ref({
  feature: '',
  rating: 0,
  comment: '',
  improvements: [] as string[]
})

// Configuration
const improvementOptions = [
  { value: 'faster', label: 'Make it faster' },
  { value: 'easier', label: 'Make it easier to use' },
  { value: 'clearer', label: 'Make it clearer/more obvious' },
  { value: 'mobile', label: 'Better mobile experience' },
  { value: 'accessibility', label: 'Better accessibility' },
  { value: 'customization', label: 'More customization options' }
]

// Computed
const getRatingText = (rating: number) => {
  const texts = {
    1: 'Poor',
    2: 'Fair', 
    3: 'Good',
    4: 'Very Good',
    5: 'Excellent'
  }
  return texts[rating] || ''
}

// Methods
const submitFeedback = async () => {
  if (!feedback.value.feature || !feedback.value.rating) return
  
  isSubmitting.value = true
  
  try {
    await trackUserFeedback({
      rating: feedback.value.rating,
      feature: feedback.value.feature,
      comment: feedback.value.comment,
      improvements: feedback.value.improvements
    })
    
    feedbackSubmitted.value = true
    
    // Reset form after delay
    setTimeout(() => {
      resetFeedbackForm()
      showFeedback.value = false
    }, 2000)
    
  } catch (error) {
    console.error('Failed to submit feedback:', error)
  } finally {
    isSubmitting.value = false
  }
}

const resetFeedbackForm = () => {
  feedback.value = {
    feature: '',
    rating: 0,
    comment: '',
    improvements: []
  }
  feedbackSubmitted.value = false
}

// Auto-show logic
const incrementActionCount = () => {
  actionCount.value++
  if (actionCount.value >= props.showAfterActions && !feedbackSubmitted.value) {
    showFeedback.value = true
    actionCount.value = 0 // Reset counter
  }
}

// Expose method for parent components
defineExpose({
  show: () => { showFeedback.value = true },
  incrementActionCount
})

// Auto-show based on user activity
if (props.autoShow) {
  // Watch for CRM actions and show feedback after threshold
  onMounted(() => {
    // Listen for CRM events to trigger feedback prompt
    const handleCRMAction = () => incrementActionCount()
    
    // Add event listeners for CRM interactions
    document.addEventListener('crm:navigation', handleCRMAction)
    document.addEventListener('crm:action-completed', handleCRMAction)
    
    onUnmounted(() => {
      document.removeEventListener('crm:navigation', handleCRMAction)
      document.removeEventListener('crm:action-completed', handleCRMAction)
    })
  })
}
</script>

<style scoped>
.feedback-trigger {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
}

.feedback-panel {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Mobile optimizations */
@media (max-width: 640px) {
  .feedback-panel {
    width: calc(100vw - 2rem);
    left: 1rem;
    right: 1rem;
  }
}
</style>