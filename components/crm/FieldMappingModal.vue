<template>
  <UModal :model-value="modelValue" @update:model-value="$emit('update:modelValue', $event)" :ui="{ width: 'sm:max-w-2xl' }">
    <UCard>
      <template #header>
        <h3 class="text-lg font-semibold">
          {{ mapping ? 'Edit' : 'Add' }} Field Mapping - {{ entityTypeLabel }}
        </h3>
      </template>

      <div v-if="loading" class="flex justify-center py-8">
        <UIcon name="i-heroicons-arrow-path" class="animate-spin h-8 w-8 text-primary-500" />
      </div>

      <UForm v-else :state="form" @submit="save" class="space-y-6">
        <!-- Field Selection -->
        <div class="grid grid-cols-2 gap-6">
          <!-- Local Field -->
          <div>
            <h4 class="text-sm font-medium text-gray-900 mb-3">Covalonic Field</h4>
            
            <UFormGroup label="Field" name="localField" required>
              <USelectMenu
                v-model="form.localField"
                :options="localFields"
                option-attribute="label"
                value-attribute="value"
                searchable
                search-placeholder="Search fields..."
              >
                <template #option="{ option }">
                  <div>
                    <p class="text-sm font-medium">{{ option.label }}</p>
                    <p class="text-xs text-gray-500">{{ option.type }}</p>
                  </div>
                </template>
              </USelectMenu>
            </UFormGroup>
          </div>

          <!-- CRM Field -->
          <div>
            <h4 class="text-sm font-medium text-gray-900 mb-3">{{ connection.crmType }} Field</h4>
            
            <UFormGroup label="Field" name="crmField" required>
              <USelectMenu
                v-model="form.crmField"
                :options="crmFields"
                option-attribute="label"
                value-attribute="name"
                searchable
                search-placeholder="Search fields..."
              >
                <template #option="{ option }">
                  <div>
                    <p class="text-sm font-medium">{{ option.label }}</p>
                    <div class="flex items-center gap-2 text-xs text-gray-500">
                      <span>{{ option.type }}</span>
                      <UBadge v-if="option.required" color="red" variant="subtle" size="xs">Required</UBadge>
                      <UBadge v-if="option.readOnly" color="gray" variant="subtle" size="xs">Read Only</UBadge>
                    </div>
                  </div>
                </template>
              </USelectMenu>
            </UFormGroup>
          </div>
        </div>

        <!-- Sync Direction -->
        <UFormGroup label="Sync Direction" name="syncDirection">
          <URadioGroup
            v-model="form.syncDirection"
            :options="syncDirectionOptions"
          />
          <template #help>
            <p class="text-xs text-gray-500 mt-1">
              {{ getSyncDirectionHelp(form.syncDirection) }}
            </p>
          </template>
        </UFormGroup>

        <!-- Transformation -->
        <UFormGroup label="Field Transformation" name="transformation">
          <USelectMenu
            v-model="form.transformation"
            :options="transformationOptions"
            option-attribute="label"
            value-attribute="value"
            placeholder="No transformation"
          />
          <template #help>
            <p class="text-xs text-gray-500 mt-1">
              Apply a transformation when syncing data between systems
            </p>
          </template>
        </UFormGroup>

        <!-- Preview -->
        <div v-if="form.localField && form.crmField" class="p-4 bg-gray-50 rounded-lg">
          <h5 class="text-sm font-medium text-gray-900 mb-2">Mapping Preview</h5>
          <div class="flex items-center justify-between text-sm">
            <span class="font-mono">{{ form.localField }}</span>
            <UIcon :name="getDirectionIcon(form.syncDirection)" class="h-5 w-5 text-gray-400" />
            <span class="font-mono">{{ form.crmField }}</span>
          </div>
          <p v-if="form.transformation" class="text-xs text-gray-500 mt-2 text-center">
            Transform: {{ form.transformation }}
          </p>
        </div>

        <!-- Actions -->
        <div class="flex justify-end gap-3 pt-4 border-t">
          <UButton color="gray" variant="ghost" @click="$emit('update:modelValue', false)">
            Cancel
          </UButton>
          <UButton type="submit" :loading="saving">
            {{ mapping ? 'Update' : 'Create' }} Mapping
          </UButton>
        </div>
      </UForm>
    </UCard>
  </UModal>
</template>

<script setup lang="ts">
import { useCRMIntegration } from '~/composables/useCRMIntegration'
import type { CRMConnection, FieldMapping, CRMField } from '~/types/crm'

const props = defineProps<{
  modelValue: boolean
  connection: CRMConnection
  entityType: string
  mapping?: FieldMapping | null
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  save: [mapping: Omit<FieldMapping, 'id'>]
}>()

const { getAvailableFields } = useCRMIntegration()

const form = reactive({
  connectionId: props.connection.id,
  entityType: props.entityType,
  localField: props.mapping?.localField || '',
  crmField: props.mapping?.crmField || '',
  transformation: props.mapping?.transformation || '',
  required: props.mapping?.required || false,
  syncDirection: props.mapping?.syncDirection || 'bidirectional' as const,
  active: props.mapping?.active ?? true
})

const loading = ref(true)
const saving = ref(false)
const localFields = ref<Array<{ label: string; value: string; type: string }>>([])
const crmFields = ref<CRMField[]>([])

const entityTypeLabel = computed(() => {
  const tab = props.connection.config?.entityTypes.find(t => t === props.entityType)
  return tab || props.entityType
})

const syncDirectionOptions = [
  { label: 'To CRM Only', value: 'to_crm' },
  { label: 'From CRM Only', value: 'from_crm' },
  { label: 'Bidirectional', value: 'bidirectional' }
]

const transformationOptions = [
  { label: 'Uppercase', value: 'uppercase' },
  { label: 'Lowercase', value: 'lowercase' },
  { label: 'Date ISO Format', value: 'date_iso' },
  { label: 'Number', value: 'number' },
  { label: 'String', value: 'string' },
  { label: 'Boolean', value: 'boolean' }
]

onMounted(async () => {
  try {
    // Load local fields based on entity type
    localFields.value = getLocalFieldsForEntity(props.entityType)
    
    // Load CRM fields
    crmFields.value = await getAvailableFields(props.connection.id, props.entityType)
  } catch (error) {
    console.error('Error loading fields:', error)
  } finally {
    loading.value = false
  }
})

const getLocalFieldsForEntity = (entityType: string): Array<{ label: string; value: string; type: string }> => {
  // Map entity types to local fields
  const fieldMap = {
    'contacts': [
      { label: 'First Name', value: 'firstName', type: 'string' },
      { label: 'Last Name', value: 'lastName', type: 'string' },
      { label: 'Email', value: 'email', type: 'email' },
      { label: 'Phone', value: 'phone', type: 'phone' },
      { label: 'Company', value: 'company', type: 'string' },
      { label: 'Job Title', value: 'jobTitle', type: 'string' },
      { label: 'Industry', value: 'industry', type: 'string' },
      { label: 'Website', value: 'website', type: 'url' },
      { label: 'LinkedIn', value: 'linkedin', type: 'url' },
      { label: 'Notes', value: 'notes', type: 'string' },
      { label: 'Tags', value: 'tags', type: 'array' },
      { label: 'Created Date', value: 'createdAt', type: 'date' },
      { label: 'Updated Date', value: 'updatedAt', type: 'date' }
    ],
    'companies': [
      { label: 'Company Name', value: 'name', type: 'string' },
      { label: 'Industry', value: 'industry', type: 'string' },
      { label: 'Website', value: 'website', type: 'url' },
      { label: 'Phone', value: 'phone', type: 'phone' },
      { label: 'Address', value: 'address', type: 'string' },
      { label: 'City', value: 'city', type: 'string' },
      { label: 'State', value: 'state', type: 'string' },
      { label: 'Country', value: 'country', type: 'string' },
      { label: 'Postal Code', value: 'postalCode', type: 'string' },
      { label: 'Number of Employees', value: 'employeeCount', type: 'number' },
      { label: 'Annual Revenue', value: 'annualRevenue', type: 'number' },
      { label: 'Description', value: 'description', type: 'string' }
    ],
    'deals': [
      { label: 'Deal Name', value: 'name', type: 'string' },
      { label: 'Amount', value: 'amount', type: 'number' },
      { label: 'Stage', value: 'stage', type: 'string' },
      { label: 'Close Date', value: 'closeDate', type: 'date' },
      { label: 'Probability', value: 'probability', type: 'number' },
      { label: 'Contact', value: 'contactId', type: 'reference' },
      { label: 'Company', value: 'companyId', type: 'reference' },
      { label: 'Description', value: 'description', type: 'string' },
      { label: 'Next Step', value: 'nextStep', type: 'string' }
    ]
  }

  // Map CRM-specific entity types to generic ones
  const entityTypeMap: Record<string, string> = {
    'Contact': 'contacts',
    'persons': 'contacts',
    'Lead': 'contacts',
    'Account': 'companies',
    'organizations': 'companies',
    'Opportunity': 'deals',
    'Deal': 'deals'
  }

  const mappedType = entityTypeMap[entityType] || entityType
  return fieldMap[mappedType] || []
}

const getSyncDirectionHelp = (direction: string) => {
  switch (direction) {
    case 'to_crm':
      return 'Data flows from Covalonic to ' + props.connection.crmType
    case 'from_crm':
      return 'Data flows from ' + props.connection.crmType + ' to Covalonic'
    case 'bidirectional':
      return 'Data syncs in both directions with conflict resolution'
    default:
      return ''
  }
}

const getDirectionIcon = (direction: string) => {
  switch (direction) {
    case 'to_crm':
      return 'i-heroicons-arrow-right'
    case 'from_crm':
      return 'i-heroicons-arrow-left'
    case 'bidirectional':
      return 'i-heroicons-arrows-right-left'
    default:
      return 'i-heroicons-minus'
  }
}

const save = () => {
  saving.value = true
  
  // Set required flag based on CRM field
  const crmField = crmFields.value.find(f => f.name === form.crmField)
  if (crmField) {
    form.required = crmField.required
  }
  
  emit('save', {
    connectionId: form.connectionId,
    entityType: form.entityType,
    localField: form.localField,
    crmField: form.crmField,
    transformation: form.transformation || undefined,
    required: form.required,
    syncDirection: form.syncDirection,
    active: form.active
  })
  
  saving.value = false
}
</script>