<template>
  <modal-small
    :open="open"
    title="Schedule Follow-up"
    buttontext="Schedule"
    :showButtons="true"
    @submit="handleSubmit"
    @close="handleClose"
  >
    <template #body>
      <form @submit.prevent="handleSubmit" class="space-y-4">
        <!-- Contact Selection -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Contact <span class="text-red-400">*</span>
          </label>
          <select
            v-model="form.contactId"
            required
            class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="" disabled>
              {{ contactsLoading ? 'Loading contacts...' : (sortedContacts.length === 0 ? 'No contacts available' : 'Select a contact') }}
            </option>
            <option v-for="contact in sortedContacts" :key="contact.id" :value="contact.id">
              {{ getFullName(contact) }}
              <span v-if="contact.businessName"> - {{ contact.businessName }}</span>
            </option>
          </select>
        </div>

        <!-- Follow-up Type -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Follow-up Type <span class="text-red-400">*</span>
          </label>
          <div class="grid grid-cols-3 gap-2">
            <button
              v-for="type in followUpTypes"
              :key="type.value"
              type="button"
              @click="form.type = type.value"
              :class="[
                'p-3 rounded-lg border transition-all duration-200 flex flex-col items-center space-y-1',
                form.type === type.value
                  ? 'bg-gradient-to-r from-green-500/20 to-green-600/20 border-green-400/50 text-white'
                  : 'bg-gray-800/30 border-white/10 text-gray-400 hover:border-white/20 hover:text-white'
              ]"
            >
              <Icon :name="type.icon" class="w-6 h-6" />
              <span class="text-xs font-medium">{{ type.label }}</span>
            </button>
          </div>
        </div>

        <!-- Title -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Task Title <span class="text-red-400">*</span>
          </label>
          <input
            v-model="form.title"
            type="text"
            required
            placeholder="e.g., Follow up on proposal discussion"
            class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <!-- Description -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Notes
          </label>
          <textarea
            v-model="form.description"
            rows="3"
            placeholder="Add any notes or context for the follow-up..."
            class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
          />
        </div>

        <!-- Due Date and Time -->
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">
              Due Date <span class="text-red-400">*</span>
            </label>
            <input
              v-model="form.dueDate"
              type="date"
              required
              :min="minDate"
              class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">
              Time
            </label>
            <input
              v-model="form.dueTime"
              type="time"
              class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        <!-- Quick Date Options -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Quick Select
          </label>
          <div class="flex flex-wrap gap-2">
            <button
              v-for="option in quickDateOptions"
              :key="option.label"
              type="button"
              @click="setQuickDate(option.days)"
              class="px-3 py-1 text-xs font-medium rounded-full bg-gray-800/30 border border-white/10 text-gray-300 hover:bg-gray-700/30 hover:border-white/20 hover:text-white transition-all duration-200"
            >
              {{ option.label }}
            </button>
          </div>
        </div>

        <!-- Priority -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Priority
          </label>
          <div class="flex space-x-2">
            <button
              v-for="priority in priorities"
              :key="priority.value"
              type="button"
              @click="form.priority = priority.value"
              :class="[
                'flex-1 px-3 py-2 rounded-lg border transition-all duration-200 text-sm font-medium',
                form.priority === priority.value
                  ? priority.activeClass
                  : 'bg-gray-800/30 border-white/10 text-gray-400 hover:border-white/20'
              ]"
            >
              {{ priority.label }}
            </button>
          </div>
        </div>

        <!-- Reminder -->
        <div>
          <label class="flex items-center space-x-3 cursor-pointer">
            <input
              v-model="form.reminder"
              type="checkbox"
              class="w-4 h-4 rounded border-gray-600 text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-800"
            />
            <span class="text-sm text-gray-300">Send reminder notification</span>
          </label>
          <div v-if="form.reminder" class="mt-2">
            <select
              v-model="form.reminderTime"
              class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            >
              <option value="0">At time of task</option>
              <option value="15">15 minutes before</option>
              <option value="30">30 minutes before</option>
              <option value="60">1 hour before</option>
              <option value="1440">1 day before</option>
            </select>
          </div>
        </div>

        <!-- Recurring -->
        <div>
          <label class="flex items-center space-x-3 cursor-pointer">
            <input
              v-model="form.recurring"
              type="checkbox"
              class="w-4 h-4 rounded border-gray-600 text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-800"
            />
            <span class="text-sm text-gray-300">Make this a recurring follow-up</span>
          </label>
          <div v-if="form.recurring" class="mt-2">
            <select
              v-model="form.recurringFrequency"
              class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            >
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="biweekly">Every 2 weeks</option>
              <option value="monthly">Monthly</option>
              <option value="quarterly">Quarterly</option>
            </select>
          </div>
        </div>
      </form>
    </template>
  </modal-small>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, toRef } from 'vue'
import { useContacts } from '~/composables/useContacts'
import { useBusinesses } from '~/composables/useBusinesses'
import { useActivities } from '~/composables/useActivities'
import { useCurrentUser } from '~/composables/useCurrentUser'
import type { Contact } from '~/composables/useContacts'

interface Props {
  open: boolean
  preselectedContactId?: string
  contacts?: Contact[]
}

const props = defineProps<Props>()
const emit = defineEmits(['close', 'success'])

// Composables
const { contacts, getFullName, subscribeToContacts, loading: contactsLoading } = useContacts()
const { businesses, subscribeToBusinesses, loading: businessesLoading } = useBusinesses()
const { createTask } = useActivities()
const { currentUser } = useCurrentUser()

// Form state
const form = ref({
  contactId: props.preselectedContactId || '',
  type: 'call' as 'call' | 'email' | 'meeting',
  title: '',
  description: '',
  dueDate: '',
  dueTime: '09:00',
  priority: 'medium' as 'low' | 'medium' | 'high',
  reminder: true,
  reminderTime: '15',
  recurring: false,
  recurringFrequency: 'weekly'
})

// Options
const followUpTypes = [
  { value: 'call', label: 'Call', icon: 'mdi:phone' },
  { value: 'email', label: 'Email', icon: 'mdi:email' },
  { value: 'meeting', label: 'Meeting', icon: 'mdi:calendar-account' }
]

const priorities = [
  { value: 'low', label: 'Low', activeClass: 'bg-gray-600/20 border-gray-400/50 text-gray-300' },
  { value: 'medium', label: 'Medium', activeClass: 'bg-yellow-500/20 border-yellow-400/50 text-yellow-300' },
  { value: 'high', label: 'High', activeClass: 'bg-red-500/20 border-red-400/50 text-red-300' }
]

const quickDateOptions = [
  { label: 'Tomorrow', days: 1 },
  { label: 'In 3 days', days: 3 },
  { label: 'Next week', days: 7 },
  { label: 'In 2 weeks', days: 14 },
  { label: 'Next month', days: 30 }
]

// Computed
const minDate = computed(() => {
  return new Date().toISOString().split('T')[0]
})

// Create reactive reference to props.contacts
const propsContacts = toRef(props, 'contacts')

// Enhanced contacts with business names
const sortedContacts = computed(() => {
  // Debug logging
  console.log('=== sortedContacts computed ===')
  console.log('propsContacts.value:', propsContacts.value)
  console.log('contacts from composable:', contacts)
  console.log('contacts.value from composable:', contacts.value)
  console.log('businesses from composable:', businesses)
  console.log('businesses.value from composable:', businesses.value)
  
  // Use props.contacts if provided, otherwise fall back to composable contacts
  // Note: contacts from useContacts() is already a computed ref, so we need to access its value
  const contactList = propsContacts.value || contacts.value || []
  
  console.log('Final contactList:', contactList)
  console.log('contactList length:', contactList?.length || 0)
  console.log('Is contactList an array?', Array.isArray(contactList))
  
  if (!contactList || !Array.isArray(contactList)) {
    console.error('ScheduleFollowupModal - contactList is not an array:', contactList)
    return []
  }
  
  const result = contactList
    .map(contact => {
      const business = contact.businessId && businesses.value
        ? businesses.value.find(b => b.id === contact.businessId)
        : null
      return {
        ...contact,
        businessName: business?.name || null
      }
    })
    .sort((a, b) => {
      const nameA = getFullName(a).toLowerCase()
      const nameB = getFullName(b).toLowerCase()
      return nameA.localeCompare(nameB)
    })
    
  console.log('sortedContacts result:', result)
  console.log('sortedContacts result length:', result.length)
  console.log('=== end sortedContacts computed ===')
  
  return result
})

// Methods
const setQuickDate = (days: number) => {
  const date = new Date()
  date.setDate(date.getDate() + days)
  form.value.dueDate = date.toISOString().split('T')[0]
}

const handleSubmit = async () => {
  if (!form.value.contactId || !form.value.title || !form.value.dueDate) {
    return
  }

  try {
    // Combine date and time
    const dueDateTime = new Date(`${form.value.dueDate}T${form.value.dueTime}`)

    // Build metadata
    const metadata: any = {
      priority: form.value.priority,
      followUpType: form.value.type
    }

    if (form.value.reminder) {
      metadata.reminder = {
        enabled: true,
        minutesBefore: parseInt(form.value.reminderTime)
      }
    }

    if (form.value.recurring) {
      metadata.recurring = {
        enabled: true,
        frequency: form.value.recurringFrequency
      }
    }

    // Get contact's business if exists
    const contact = sortedContacts.value.find(c => c.id === form.value.contactId)
    const associatedEntities = [
      { entityType: 'contact', entityId: form.value.contactId }
    ]
    
    if (contact?.businessId) {
      associatedEntities.push({ 
        entityType: 'business', 
        entityId: contact.businessId 
      })
    }

    // Create the task
    await createTask({
      title: form.value.title,
      description: form.value.description,
      dueDate: dueDateTime,
      type: form.value.type,
      status: 'pending',
      priority: form.value.priority,
      associatedEntities,
      metadata
    })

    // Reset form
    resetForm()
    
    // Notify success
    emit('success')
    emit('close')
  } catch (error) {
    console.error('Failed to create follow-up:', error)
    // You could show an error notification here
  }
}

const handleClose = () => {
  resetForm()
  emit('close')
}

const resetForm = () => {
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  
  form.value = {
    contactId: props.preselectedContactId || '',
    type: 'call',
    title: '',
    description: '',
    dueDate: tomorrow.toISOString().split('T')[0],
    dueTime: '09:00',
    priority: 'medium',
    reminder: true,
    reminderTime: '15',
    recurring: false,
    recurringFrequency: 'weekly'
  }
}

// Initialize subscriptions and set default date on mount
onMounted(() => {
  console.log('ScheduleFollowupModal - onMounted')
  console.log('ScheduleFollowupModal - currentUser:', currentUser.value)
  console.log('ScheduleFollowupModal - contacts on mount:', contacts.value)
  
  // Always subscribe to ensure we have contacts
  if (currentUser.value?.uid) {
    console.log('ScheduleFollowupModal - subscribing to contacts for user:', currentUser.value.uid)
    subscribeToContacts()
    subscribeToBusinesses()
  }
  
  // Set default due date to tomorrow
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  form.value.dueDate = tomorrow.toISOString().split('T')[0]
})

// Watch for prop changes
watch(() => props.preselectedContactId, (newId) => {
  if (newId) {
    form.value.contactId = newId
  }
})

// Watch for modal open to ensure contacts are loaded
watch(() => props.open, (isOpen) => {
  if (isOpen) {
    console.log('=== ScheduleFollowupModal Debug ===')
    console.log('Modal opened:', isOpen)
    console.log('Props contacts:', props.contacts)
    console.log('Props contacts length:', props.contacts?.length || 0)
    console.log('Composable contacts:', contacts.value)
    console.log('Composable contacts length:', contacts.value?.length || 0)
    console.log('ContactsLoading:', contactsLoading.value)
    console.log('SortedContacts computed:', sortedContacts.value)
    console.log('SortedContacts length:', sortedContacts.value?.length || 0)
    console.log('Current user:', currentUser.value)
    console.log('=================================')
    
    // Always ensure subscription when modal opens
    if (currentUser.value?.uid) {
      console.log('ScheduleFollowupModal - Ensuring subscription on modal open...')
      subscribeToContacts()
      subscribeToBusinesses()
    }
  }
})
</script>