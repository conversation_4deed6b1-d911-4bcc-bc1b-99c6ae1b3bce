<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useCRM } from '~/composables/useCRM';
import { useNotifications } from '~/composables/useNotifications';
import TaskReminder from './TaskReminder.vue';
import type { ICRMTask } from '~/types/crm';

interface Props {
  clientId?: string; // Optional filter by specific client
  limit?: number;    // Max number of tasks to show
  showHeader?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  limit: 10,
  showHeader: true
});

const { 
  getTasks, 
  updateTask, 
  deleteTask, 
  tasks, 
  loading,
  error 
} = useCRM();

const { showSuccess, showError } = useNotifications();

// Component state
const activeFilter = ref<'all' | 'today' | 'overdue' | 'upcoming' | 'completed'>('all');
const searchQuery = ref('');
const showCreateTask = ref(false);
const editingTask = ref<ICRMTask | null>(null);
const completingTaskId = ref<string | null>(null);

// Filter options with computed counts
const filters = computed(() => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);
  
  return [
    { 
      label: 'All', 
      value: 'all' as const,
      count: tasks.value.length
    },
    { 
      label: 'Today', 
      value: 'today' as const,
      count: tasks.value.filter(task => {
        const dueDate = task.due_date.toDate();
        return dueDate >= today && dueDate < tomorrow && task.status !== 'completed';
      }).length
    },
    { 
      label: 'Overdue', 
      value: 'overdue' as const,
      count: tasks.value.filter(task => {
        return task.due_date.toDate() < now && task.status !== 'completed';
      }).length
    },
    { 
      label: 'Upcoming', 
      value: 'upcoming' as const,
      count: tasks.value.filter(task => {
        return task.due_date.toDate() >= tomorrow && task.status !== 'completed';
      }).length
    },
    { 
      label: 'Completed', 
      value: 'completed' as const,
      count: tasks.value.filter(task => task.status === 'completed').length
    }
  ];
});

// Computed properties
const filteredTasks = computed(() => {
  let filtered = [...tasks.value];
  
  // Filter by client if specified
  if (props.clientId) {
    filtered = filtered.filter(task => task.client_id === props.clientId);
  }
  
  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(task =>
      task.title.toLowerCase().includes(query) ||
      task.description?.toLowerCase().includes(query)
    );
  }
  
  // Filter by status
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);
  
  switch (activeFilter.value) {
    case 'today':
      filtered = filtered.filter(task => {
        const dueDate = task.due_date.toDate();
        return dueDate >= today && dueDate < tomorrow && task.status !== 'completed';
      });
      break;
    case 'overdue':
      filtered = filtered.filter(task => {
        return task.due_date.toDate() < now && task.status !== 'completed';
      });
      break;
    case 'upcoming':
      filtered = filtered.filter(task => {
        return task.due_date.toDate() >= tomorrow && task.status !== 'completed';
      });
      break;
    case 'completed':
      filtered = filtered.filter(task => task.status === 'completed');
      break;
  }
  
  // Sort by due date, then by priority
  filtered.sort((a, b) => {
    // Completed tasks go to the bottom
    if (a.status === 'completed' && b.status !== 'completed') return 1;
    if (b.status === 'completed' && a.status !== 'completed') return -1;
    
    // Sort by due date
    const dateA = a.due_date.toDate().getTime();
    const dateB = b.due_date.toDate().getTime();
    if (dateA !== dateB) return dateA - dateB;
    
    // Sort by priority (high > medium > low)
    const priorityOrder = { high: 3, medium: 2, low: 1 };
    return priorityOrder[b.priority] - priorityOrder[a.priority];
  });
  
  // Apply limit
  return filtered.slice(0, props.limit);
});

const overdueCount = computed(() => {
  const now = new Date();
  return tasks.value.filter(task => {
    return task.due_date.toDate() < now && task.status !== 'completed';
  }).length;
});

// Methods
onMounted(() => {
  loadTasks();
});

const loadTasks = async () => {
  try {
    await getTasks();
  } catch (err: any) {
    showError(`Failed to load tasks: ${err.message}`);
  }
};

const openCreateTask = () => {
  editingTask.value = null;
  showCreateTask.value = true;
};

const openEditTask = (task: ICRMTask) => {
  editingTask.value = task;
  showCreateTask.value = true;
};

const handleTaskCreated = (newTask: ICRMTask) => {
  showCreateTask.value = false;
  loadTasks(); // Refresh the list
};

const handleTaskUpdated = (updatedTask: ICRMTask) => {
  showCreateTask.value = false;
  editingTask.value = null;
  loadTasks(); // Refresh the list
};

const completeTask = async (taskId: string) => {
  completingTaskId.value = taskId;
  
  try {
    const task = tasks.value.find(t => t.id === taskId);
    if (!task) return;
    
    const success = await updateTask(taskId, {
      status: task.status === 'completed' ? 'pending' : 'completed',
      completed_at: task.status === 'completed' ? undefined : new Date()
    });
    
    if (success) {
      const action = task.status === 'completed' ? 'reopened' : 'completed';
      showSuccess(`Task ${action} successfully!`);
      await loadTasks(); // Refresh the list
    }
  } catch (err: any) {
    showError(`Failed to update task: ${err.message}`);
  } finally {
    completingTaskId.value = null;
  }
};

const deleteTaskAction = async (taskId: string) => {
  if (!confirm('Are you sure you want to delete this task?')) return;
  
  try {
    const success = await deleteTask(taskId);
    if (success) {
      showSuccess('Task deleted successfully!');
      await loadTasks(); // Refresh the list
    }
  } catch (err: any) {
    showError(`Failed to delete task: ${err.message}`);
  }
};

const formatDate = (date: any): string => {
  if (!date) return '';
  
  const dateObj = date.toDate ? date.toDate() : new Date(date);
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const taskDate = new Date(dateObj.getFullYear(), dateObj.getMonth(), dateObj.getDate());
  
  const diffInDays = Math.floor((taskDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
  
  if (diffInDays === 0) return 'Today';
  if (diffInDays === 1) return 'Tomorrow';
  if (diffInDays === -1) return 'Yesterday';
  if (diffInDays < 0) return `${Math.abs(diffInDays)} days ago`;
  if (diffInDays <= 7) return `In ${diffInDays} days`;
  
  return dateObj.toLocaleDateString();
};

const formatDateTime = (date: any): string => {
  if (!date) return '';
  
  const dateObj = date.toDate ? date.toDate() : new Date(date);
  return dateObj.toLocaleString();
};

const isOverdue = (task: ICRMTask): boolean => {
  return task.due_date.toDate() < new Date() && task.status !== 'completed';
};

const getPriorityColor = (priority: string): string => {
  switch (priority) {
    case 'high': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
    case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
    case 'low': return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
  }
};
</script>

<template>
  <div class="space-y-4">
    <!-- Header -->
    <div v-if="showHeader" class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
          Tasks
        </h2>
        <div v-if="overdueCount > 0" class="flex items-center space-x-1">
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
            {{ overdueCount }} overdue
          </span>
        </div>
      </div>
      
      <div class="flex items-center space-x-3">
        <!-- Search -->
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search tasks..."
            class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>
        
        <!-- Add Task Button -->
        <button
          @click="openCreateTask"
          class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          Add Task
        </button>
      </div>
    </div>
    
    <!-- Filter Tabs -->
    <div class="border-b border-gray-200 dark:border-gray-700">
      <nav class="flex space-x-8">
        <button
          v-for="filter in filters"
          :key="filter.value"
          @click="activeFilter = filter.value"
          :class="[
            'py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap',
            activeFilter === filter.value
              ? 'border-blue-500 text-blue-600 dark:text-blue-400'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
          ]"
        >
          {{ filter.label }}
          <span v-if="filter.count > 0" class="ml-1 text-xs">
            ({{ filter.count }})
          </span>
        </button>
      </nav>
    </div>
    
    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>
    
    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
      <p class="text-red-600 dark:text-red-400">{{ error }}</p>
      <button
        @click="loadTasks"
        class="mt-2 text-sm text-red-600 hover:text-red-500 underline"
      >
        Try again
      </button>
    </div>
    
    <!-- Empty State -->
    <div v-else-if="filteredTasks.length === 0" class="text-center py-8">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No tasks found</h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        {{ searchQuery ? 'No tasks match your search criteria.' : `No ${activeFilter} tasks found.` }}
      </p>
      <div v-if="!searchQuery && activeFilter === 'all'" class="mt-6">
        <button
          @click="openCreateTask"
          class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          Create your first task
        </button>
      </div>
    </div>
    
    <!-- Task List -->
    <div v-else class="space-y-3">
      <div
        v-for="task in filteredTasks"
        :key="task.id"
        :class="[
          'p-4 border rounded-lg hover:shadow-md transition-shadow',
          isOverdue(task) 
            ? 'border-red-300 bg-red-50 dark:border-red-800 dark:bg-red-900/20' 
            : task.status === 'completed'
            ? 'border-green-300 bg-green-50 dark:border-green-800 dark:bg-green-900/20'
            : 'border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800'
        ]"
      >
        <div class="flex items-start space-x-3">
          <!-- Task Completion Checkbox -->
          <div class="flex-shrink-0 pt-1">
            <input
              type="checkbox"
              :checked="task.status === 'completed'"
              @change="completeTask(task.id)"
              :disabled="completingTaskId === task.id"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
          </div>
          
          <!-- Task Content -->
          <div class="flex-1 min-w-0">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <h4 :class="[
                  'text-sm font-medium',
                  task.status === 'completed' 
                    ? 'line-through text-gray-500 dark:text-gray-400' 
                    : 'text-gray-900 dark:text-white'
                ]">
                  {{ task.title }}
                </h4>
                
                <div class="flex items-center space-x-4 mt-1">
                  <!-- Priority Badge -->
                  <span :class="[
                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                    getPriorityColor(task.priority)
                  ]">
                    {{ task.priority }}
                  </span>
                  
                  <!-- Due Date -->
                  <span :class="[
                    'text-xs',
                    isOverdue(task) 
                      ? 'text-red-600 dark:text-red-400 font-medium'
                      : task.status === 'completed'
                      ? 'text-green-600 dark:text-green-400'
                      : 'text-gray-500 dark:text-gray-400'
                  ]">
                    Due {{ formatDate(task.due_date) }}
                  </span>
                  
                  <!-- Completed Date -->
                  <span v-if="task.status === 'completed' && task.completed_at" class="text-xs text-green-600 dark:text-green-400">
                    Completed {{ formatDate(task.completed_at) }}
                  </span>
                </div>
                
                <!-- Description -->
                <p v-if="task.description" :class="[
                  'text-sm mt-2',
                  task.status === 'completed' 
                    ? 'text-gray-400 dark:text-gray-500' 
                    : 'text-gray-600 dark:text-gray-400'
                ]">
                  {{ task.description }}
                </p>
              </div>
              
              <!-- Actions -->
              <div class="flex items-center space-x-2 ml-4">
                <button
                  @click="openEditTask(task)"
                  class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  title="Edit task"
                >
                  <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                </button>
                
                <button
                  @click="deleteTaskAction(task.id)"
                  class="text-gray-400 hover:text-red-600 dark:hover:text-red-400"
                  title="Delete task"
                >
                  <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Task Creation/Edit Modal -->
    <TaskReminder
      :show="showCreateTask"
      :client-id="clientId"
      :existing-task="editingTask"
      @close="showCreateTask = false; editingTask = null"
      @created="handleTaskCreated"
      @updated="handleTaskUpdated"
    />
  </div>
</template>