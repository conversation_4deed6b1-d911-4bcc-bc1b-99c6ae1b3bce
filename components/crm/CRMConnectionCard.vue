<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div class="flex items-start justify-between">
      <div class="flex items-start space-x-4">
        <div class="flex-shrink-0">
          <img 
            v-if="logo"
            :src="logo" 
            :alt="connection.crmType"
            class="h-10 w-auto"
          >
          <div v-else class="h-10 w-10 bg-gray-200 rounded flex items-center justify-center">
            <UIcon name="i-heroicons-building-office-2" class="h-6 w-6 text-gray-500" />
          </div>
        </div>
        
        <div>
          <h3 class="text-lg font-semibold text-gray-900">{{ connection.name }}</h3>
          <div class="mt-1 flex items-center space-x-4 text-sm">
            <span class="flex items-center">
              <span 
                class="w-2 h-2 rounded-full mr-2"
                :class="{
                  'bg-green-500': connection.status === 'connected',
                  'bg-red-500': connection.status === 'error',
                  'bg-gray-500': connection.status === 'disconnected'
                }"
              />
              <span class="capitalize">{{ connection.status }}</span>
            </span>
            <span v-if="connection.lastSync" class="text-gray-500">
              Last synced {{ formatRelativeTime(connection.lastSync) }}
            </span>
          </div>
          
          <div v-if="connection.config" class="mt-2 flex flex-wrap gap-2">
            <UBadge 
              v-if="connection.config.syncEnabled" 
              color="green" 
              variant="subtle"
              size="xs"
            >
              Sync Enabled
            </UBadge>
            <UBadge 
              variant="subtle"
              size="xs"
            >
              {{ connection.config.syncDirection }}
            </UBadge>
            <UBadge 
              variant="subtle"
              size="xs"
            >
              {{ connection.config.syncFrequency }}
            </UBadge>
          </div>
        </div>
      </div>
      
      <UDropdown 
        :items="dropdownItems"
        :popper="{ placement: 'bottom-end' }"
      >
        <UButton 
          color="gray" 
          variant="ghost" 
          icon="i-heroicons-ellipsis-vertical" 
          size="sm"
        />
      </UDropdown>
    </div>

    <!-- Sync Status -->
    <div v-if="syncStatus.status !== 'idle'" class="mt-4 p-3 bg-gray-50 rounded-lg">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm font-medium text-gray-700">
          {{ syncStatus.message || 'Syncing...' }}
        </span>
        <span class="text-sm text-gray-500">
          {{ syncStatus.progress }}%
        </span>
      </div>
      <UProgress :value="syncStatus.progress" size="sm" />
    </div>

    <!-- Error Message -->
    <div v-if="connection.lastError" class="mt-4">
      <UAlert
        color="red"
        variant="subtle"
        :title="connection.lastError"
        icon="i-heroicons-exclamation-triangle"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { formatRelativeTime } from '~/utils/date'
import { useCRMIntegration } from '~/composables/useCRMIntegration'
import type { CRMConnection } from '~/types/crm'

const props = defineProps<{
  connection: CRMConnection
}>()

const emit = defineEmits<{
  configure: []
  sync: []
  delete: []
}>()

const { useSyncStatus } = useCRMIntegration()
const syncStatus = useSyncStatus(props.connection.id)

const logo = computed(() => {
  const logos = {
    salesforce: '/images/crm/salesforce-logo.svg',
    hubspot: '/images/crm/hubspot-logo.svg',
    pipedrive: '/images/crm/pipedrive-logo.svg'
  }
  return logos[props.connection.crmType]
})

const dropdownItems = computed(() => [
  [{
    label: 'Configure',
    icon: 'i-heroicons-cog-6-tooth',
    click: () => emit('configure')
  }],
  [{
    label: 'Sync Now',
    icon: 'i-heroicons-arrow-path',
    click: () => emit('sync')
  }],
  [{
    label: 'View Logs',
    icon: 'i-heroicons-document-text',
    to: `/c/integrations/crm/${props.connection.id}/logs`
  }],
  [{
    label: 'Field Mappings',
    icon: 'i-heroicons-arrows-right-left',
    to: `/c/integrations/crm/${props.connection.id}/mappings`
  }],
  [{
    label: 'Delete',
    icon: 'i-heroicons-trash',
    color: 'red' as const,
    click: () => emit('delete')
  }]
])
</script>