<script setup lang="ts">
import { ref, computed } from 'vue';
import { useFirebase } from '~/composables/useFirebase';
import { ref as storageRef, uploadBytesResumable, getDownloadURL } from 'firebase/storage';
import { useOCRServerProcessing } from '~/composables/useOCRServerProcessing';

const props = defineProps({
  maxSize: {
    type: Number,
    default: 10, // MB
  },
  autoProcessOCR: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['upload-complete', 'upload-error', 'ocr-complete', 'ocr-error']);

const { storage, user } = useFirebase();
const { processBusinessCardOnServer } = useOCRServerProcessing();

// State
const fileInput = ref<HTMLInputElement | null>(null);
const isDragging = ref(false);
const selectedFile = ref<File | null>(null);
const preview = ref<string | null>(null);
const uploading = ref(false);
const uploadProgress = ref(0);
const error = ref<string | null>(null);
const uploadTask = ref<any>(null);
const useServerProcessing = ref(false);
const processingOCR = ref(false);

// Computed
const progressPercentage = computed(() => Math.round(uploadProgress.value));

// Methods
const onFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  if (file) {
    validateAndSetFile(file);
  }
};

const validateAndSetFile = (file: File) => {
  error.value = null;
  
  // Validate file type
  if (!['image/jpeg', 'image/png'].includes(file.type)) {
    error.value = 'Please select a JPEG or PNG image';
    return;
  }
  
  // Validate file size
  if (file.size > props.maxSize * 1024 * 1024) {
    error.value = `File size must be less than ${props.maxSize}MB`;
    return;
  }
  
  selectedFile.value = file;
  
  // Create preview
  const reader = new FileReader();
  reader.onload = (e) => {
    preview.value = e.target?.result as string;
  };
  reader.readAsDataURL(file);
};

const onDragOver = (e: DragEvent) => {
  e.preventDefault();
  isDragging.value = true;
};

const onDragLeave = (e: DragEvent) => {
  e.preventDefault();
  isDragging.value = false;
};

const onDrop = (e: DragEvent) => {
  e.preventDefault();
  isDragging.value = false;
  
  const file = e.dataTransfer?.files[0];
  if (file) {
    validateAndSetFile(file);
  }
};

const uploadFile = async () => {
  if (!selectedFile.value || !user.value || !storage) {
    error.value = 'Unable to upload file. Please try again.';
    return;
  }
  
  try {
    uploading.value = true;
    error.value = null;
    
    // Create storage path
    const timestamp = Date.now();
    const filename = selectedFile.value.name.replace(/[^a-zA-Z0-9.-]/g, '_');
    const path = `business_cards/${user.value.uid}/${timestamp}_${filename}`;
    
    // Create storage reference
    const fileRef = storageRef(storage, path);
    
    // Start upload
    uploadTask.value = uploadBytesResumable(fileRef, selectedFile.value);
    
    // Monitor upload progress
    uploadTask.value.on('state_changed',
      (snapshot: any) => {
        uploadProgress.value = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
      },
      (uploadError: any) => {
        error.value = `Upload failed: ${uploadError.message}`;
        uploading.value = false;
        uploadTask.value = null;
      },
      async () => {
        // Upload completed successfully
        try {
          const downloadURL = await getDownloadURL(uploadTask.value.snapshot.ref);
          
          const uploadData = {
            url: downloadURL,
            path: path,
            filename: selectedFile.value!.name,
            size: selectedFile.value!.size,
            type: selectedFile.value!.type,
          };
          
          emit('upload-complete', uploadData);
          
          // Process OCR if enabled
          if (useServerProcessing.value || props.autoProcessOCR) {
            await processOCR(path);
          } else {
            // Reset state if not processing OCR
            resetUpload();
          }
        } catch (urlError: any) {
          error.value = `Failed to get download URL: ${urlError.message}`;
        } finally {
          uploading.value = false;
          uploadTask.value = null;
        }
      }
    );
  } catch (err: any) {
    error.value = `Upload error: ${err.message}`;
    uploading.value = false;
    uploadTask.value = null;
  }
};

const cancelUpload = () => {
  if (uploadTask.value) {
    uploadTask.value.cancel();
    uploadTask.value = null;
  }
  resetUpload();
};

const resetUpload = () => {
  selectedFile.value = null;
  preview.value = null;
  uploading.value = false;
  uploadProgress.value = 0;
  error.value = null;
  if (fileInput.value) {
    fileInput.value.value = '';
  }
};

const removeFile = () => {
  resetUpload();
};

const triggerFileInput = () => {
  fileInput.value?.click();
};

const processOCR = async (storagePath: string) => {
  processingOCR.value = true;
  try {
    const result = await processBusinessCardOnServer({ storagePath });
    
    if (result) {
      emit('ocr-complete', result);
      // Reset state after successful OCR
      resetUpload();
    }
  } catch (ocrError: any) {
    console.error('OCR processing failed:', ocrError);
    error.value = `OCR failed: ${ocrError.message}`;
    emit('ocr-error', ocrError);
  } finally {
    processingOCR.value = false;
  }
};
</script>

<template>
  <div class="max-w-2xl mx-auto p-4 md:p-6">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div class="p-6">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Upload Business Card
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          Upload a photo of a business card to quickly capture contact information
        </p>
        
        <!-- Processing Options -->
        <div class="mb-6 bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
          <label class="flex items-center space-x-3 cursor-pointer">
            <input
              type="checkbox"
              v-model="useServerProcessing"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <div>
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                Use server-side OCR processing
              </span>
              <span class="text-xs text-gray-500 dark:text-gray-400 block">
                More accurate with Google Cloud Vision API (uses API credits)
              </span>
            </div>
          </label>
        </div>
        
        <!-- File upload area -->
        <div v-if="!selectedFile">
          <div
            @click="triggerFileInput"
            @dragover="onDragOver"
            @dragleave="onDragLeave"
            @drop="onDrop"
            :class="[
              'border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors',
              isDragging 
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
            ]"
          >
            <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
            
            <p class="text-gray-700 dark:text-gray-300 font-medium mb-2">
              {{ isDragging ? 'Drop your file here' : 'Click to upload or drag and drop' }}
            </p>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              JPEG or PNG up to {{ maxSize }}MB
            </p>
          </div>
          
          <input
            ref="fileInput"
            type="file"
            accept="image/jpeg,image/png"
            @change="onFileSelect"
            class="hidden"
          />
        </div>
        
        <!-- Preview and upload section -->
        <div v-else class="space-y-4">
          <!-- Preview -->
          <div class="relative">
            <img
              :src="preview"
              alt="Business card preview"
              class="w-full h-auto rounded-lg shadow-sm"
            />
            
            <!-- Remove button -->
            <button
              v-if="!uploading"
              @click="removeFile"
              class="absolute top-2 right-2 p-2 bg-white dark:bg-gray-800 rounded-full shadow-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <svg class="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <!-- File info -->
          <div class="flex items-center justify-between text-sm">
            <span class="text-gray-600 dark:text-gray-400">
              {{ selectedFile.name }}
            </span>
            <span class="text-gray-500 dark:text-gray-500">
              {{ (selectedFile.size / 1024 / 1024).toFixed(2) }} MB
            </span>
          </div>
          
          <!-- Progress bar -->
          <div v-if="uploading" class="space-y-2">
            <div class="flex items-center justify-between text-sm">
              <span class="text-gray-600 dark:text-gray-400">Uploading...</span>
              <span class="text-gray-900 dark:text-white font-medium">{{ progressPercentage }}%</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                class="bg-gradient-to-r from-blue-500 to-cyan-500 h-2 rounded-full transition-all duration-300"
                :style="{ width: `${progressPercentage}%` }"
              />
            </div>
          </div>
          
          <!-- OCR Processing indicator -->
          <div v-if="processingOCR" class="space-y-2">
            <div class="flex items-center space-x-2 text-sm">
              <svg class="animate-spin h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span class="text-gray-600 dark:text-gray-400">Processing business card with OCR...</span>
            </div>
          </div>
          
          <!-- Action buttons -->
          <div class="flex gap-3">
            <button
              v-if="!uploading && !processingOCR"
              @click="uploadFile"
              class="flex-1 px-4 py-2 bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-lg hover:from-blue-600 hover:to-cyan-600 transition-colors font-medium"
            >
              Upload Business Card
            </button>
            
            <button
              v-if="uploading"
              @click="cancelUpload"
              class="flex-1 px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors font-medium"
            >
              Cancel Upload
            </button>
          </div>
        </div>
        
        <!-- Error message -->
        <div v-if="error" class="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <p class="text-sm text-red-600 dark:text-red-400">{{ error }}</p>
        </div>
      </div>
    </div>
  </div>
</template>