<template>
  <div class="crm-dashboard space-y-6">
    <!-- Search Bar -->
    <div
      class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-xl p-4 border border-white/10 hover:border-white/20 transition-all duration-300"
    >
      <div class="flex items-center space-x-4">
        <div class="flex-1">
          <div class="relative">
            <Icon
              name="mdi:magnify"
              class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"
            />
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search contacts, companies, tags, or notes..."
              class="w-full pl-10 pr-4 py-2 bg-gray-800/50 border border-white/10 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400"
              @input="handleSearch"
            />
          </div>
        </div>
        <button
          v-if="searchQuery"
          class="text-gray-400 hover:text-white transition-colors"
          @click="clearSearch"
        >
          <Icon name="mdi:close" class="w-5 h-5" />
        </button>
      </div>

      <!-- Search Results Summary -->
      <div v-if="searchResults && searchQuery" class="mt-3 text-sm text-gray-300">
        Found {{ searchResults.total }} results
        <span v-if="searchResults.total > 0">
          ({{ searchResults.contacts.length }} contacts,
          {{ searchResults.activities.length }} activities, {{ searchResults.tasks.length }} tasks)
        </span>
      </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <div
        class="relative bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-white/20 transition-all duration-300 hover:shadow-2xl transform hover:scale-[1.02]"
      >
        <div
          class="absolute inset-0 bg-gradient-to-br from-blue-400/5 to-purple-400/5 rounded-xl opacity-0 hover:opacity-100 transition-opacity duration-300"
        ></div>
        <div class="relative z-10">
          <div class="flex items-center">
            <div
              class="w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg"
            >
              <Icon name="mdi:card-account-details" class="w-7 h-7 text-white" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-blue-400">Total Contacts</p>
              <p class="text-2xl font-bold text-white">{{ stats.totalContacts }}</p>
            </div>
          </div>
        </div>
      </div>

      <div
        class="relative bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-white/20 transition-all duration-300 hover:shadow-2xl transform hover:scale-[1.02]"
      >
        <div
          class="absolute inset-0 bg-gradient-to-br from-green-400/5 to-blue-400/5 rounded-xl opacity-0 hover:opacity-100 transition-opacity duration-300"
        ></div>
        <div class="relative z-10">
          <div class="flex items-center">
            <div
              class="w-14 h-14 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg"
            >
              <Icon name="mdi:trending-up" class="w-7 h-7 text-white" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-green-400">Hot Leads</p>
              <p class="text-2xl font-bold text-white">{{ stats.hotLeads }}</p>
            </div>
          </div>
        </div>
      </div>

      <div
        class="relative bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-white/20 transition-all duration-300 hover:shadow-2xl transform hover:scale-[1.02]"
      >
        <div
          class="absolute inset-0 bg-gradient-to-br from-yellow-400/5 to-orange-400/5 rounded-xl opacity-0 hover:opacity-100 transition-opacity duration-300"
        ></div>
        <div class="relative z-10">
          <div class="flex items-center">
            <div
              class="w-14 h-14 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl flex items-center justify-center shadow-lg"
            >
              <Icon name="mdi:clock-alert" class="w-7 h-7 text-white" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-yellow-400">Overdue Follow-ups</p>
              <p class="text-2xl font-bold text-white">{{ stats.overdueFollowUps }}</p>
            </div>
          </div>
        </div>
      </div>

      <div
        class="relative bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-white/20 transition-all duration-300 hover:shadow-2xl transform hover:scale-[1.02]"
      >
        <div
          class="absolute inset-0 bg-gradient-to-br from-purple-400/5 to-pink-400/5 rounded-xl opacity-0 hover:opacity-100 transition-opacity duration-300"
        ></div>
        <div class="relative z-10">
          <div class="flex items-center">
            <div
              class="w-14 h-14 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg"
            >
              <Icon name="mdi:calendar-check" class="w-7 h-7 text-white" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-purple-400">This Week</p>
              <p class="text-2xl font-bold text-white">{{ stats.upcomingThisWeek }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Recent Activity -->
      <div class="lg:col-span-2 space-y-6">
        <div
          class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-xl border border-white/10 hover:border-white/20 transition-all duration-300"
        >
          <div class="p-6 border-b border-white/10">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-white">Recent Activity</h3>
              <button
                class="text-sm text-blue-400 hover:text-blue-300 font-medium transition-colors"
                @click="showActivityModal = true"
              >
                View All
              </button>
            </div>
          </div>

          <div class="p-6">
            <div v-if="loading" class="text-center py-8">
              <Icon name="mdi:loading" class="w-8 h-8 text-gray-400 mx-auto mb-3 animate-spin" />
              <p class="text-gray-300">Loading activities...</p>
            </div>

            <div v-else-if="recentActivities.length === 0" class="text-center py-8">
              <Icon name="mdi:history" class="w-12 h-12 text-gray-400 mx-auto mb-3" />
              <p class="text-gray-300">No recent activity</p>
              <button
                class="mt-2 text-sm text-blue-400 hover:text-blue-300 transition-colors"
                @click="showAddInteractionModal = true"
              >
                Add first activity
              </button>
            </div>

            <div v-else class="space-y-4">
              <div
                v-for="activity in recentActivities.slice(0, 5)"
                :key="activity.id"
                class="p-4 rounded-xl bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-400/20 hover:from-blue-500/20 hover:to-purple-500/20 cursor-pointer transition-all duration-200"
                @click="handleActivityClick(activity)"
              >
                <div class="flex items-start space-x-3">
                  <div class="flex-shrink-0">
                    <div
                      class="w-10 h-10 rounded-full bg-gradient-to-br flex items-center justify-center shadow-lg"
                      :class="getActivityGradient(activity.type)"
                    >
                      <Icon :name="getActivityIcon(activity.type)" class="w-5 h-5 text-white" />
                    </div>
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="flex items-start justify-between gap-2">
                      <div class="flex-1">
                        <p class="text-sm font-medium text-blue-300">{{ activity.title }}</p>
                        <p class="text-xs text-white mt-0.5">
                          <Icon name="mdi:account" class="w-3 h-3 inline mr-1 text-gray-400" />
                          {{ getActivityContactName(activity) }}
                          <span v-if="getActivityBusinessName(activity)" class="text-gray-400">
                            • {{ getActivityBusinessName(activity) }}
                          </span>
                        </p>
                      </div>
                      <span class="text-xs text-gray-400 whitespace-nowrap">
                        {{ formatRelativeTime(activity.createdAt) }}
                      </span>
                    </div>
                    <p class="text-sm text-gray-300 truncate mt-2">{{ activity.description }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Interaction Timeline -->
        <div
          class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-xl border border-white/10 hover:border-white/20 transition-all duration-300"
        >
          <div class="p-6 border-b border-white/10">
            <h3 class="text-lg font-semibold text-white">Interaction Timeline</h3>
          </div>
          <div class="p-6">
            <CrmInteractionTimeline
              :activities="
                activities.filter(
                  a =>
                    selectedContactId &&
                    a.associatedEntities.some(
                      e => e.entityType === 'contact' && e.entityId === selectedContactId
                    )
                )
              "
              :loading="loading"
              :compact="false"
              class="max-h-[400px] overflow-y-auto"
            />
          </div>
        </div>
      </div>

      <!-- Follow-ups Sidebar -->
      <div class="space-y-6">
        <!-- Overdue Follow-ups -->
        <div
          class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-xl border border-white/10 hover:border-white/20 transition-all duration-300"
        >
          <div class="p-4 border-b border-white/10">
            <h3 class="text-lg font-semibold text-red-400">Overdue Follow-ups</h3>
          </div>

          <div class="p-4">
            <div v-if="loading" class="text-center py-4">
              <Icon name="mdi:loading" class="w-6 h-6 text-gray-400 mx-auto animate-spin" />
            </div>

            <div v-else-if="overdueFollowUps.length === 0" class="text-center py-4">
              <Icon name="mdi:check-circle" class="w-8 h-8 text-green-400 mx-auto mb-2" />
              <p class="text-sm text-gray-300">All caught up!</p>
            </div>

            <div v-else class="space-y-3">
              <div
                v-for="task in overdueFollowUps.slice(0, 3)"
                :key="task.id"
                class="p-3 bg-red-500/20 rounded-lg border border-red-400/20 cursor-pointer hover:bg-red-500/30 transition-all duration-200"
                @click="handleTaskClick(task)"
              >
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <p class="text-sm font-medium text-white">{{ task.title }}</p>
                    <p class="text-xs text-white mt-0.5">
                      <Icon name="mdi:account" class="w-3 h-3 inline mr-1 text-gray-400" />
                      {{ getTaskContactName(task) }}
                      <span v-if="getTaskBusinessName(task)" class="text-gray-400">
                        • {{ getTaskBusinessName(task) }}
                      </span>
                    </p>
                    <p class="text-xs text-red-400 mt-1">Due: {{ formatDate(task.dueDate) }}</p>
                  </div>
                  <button
                    class="text-green-400 hover:text-green-300 transition-colors"
                    title="Mark complete"
                    @click.stop="completeFollowUp(task.id)"
                  >
                    <Icon name="mdi:check" class="w-4 h-4" />
                  </button>
                </div>
              </div>

              <button
                v-if="overdueFollowUps.length > 3"
                class="w-full text-sm text-red-400 hover:text-red-300 font-medium transition-colors"
                @click="showFollowUpsModal = true"
              >
                View {{ overdueFollowUps.length - 3 }} more overdue
              </button>
            </div>
          </div>
        </div>

        <!-- Upcoming Follow-ups -->
        <div
          class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-xl border border-white/10 hover:border-white/20 transition-all duration-300"
        >
          <div class="p-4 border-b border-white/10">
            <h3 class="text-lg font-semibold text-blue-400">Upcoming This Week</h3>
          </div>

          <div class="p-4">
            <div v-if="loading" class="text-center py-4">
              <Icon name="mdi:loading" class="w-6 h-6 text-gray-400 mx-auto animate-spin" />
            </div>

            <div v-else-if="upcomingFollowUps.length === 0" class="text-center py-4">
              <Icon name="mdi:calendar-outline" class="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p class="text-sm text-gray-300">No upcoming follow-ups</p>
              <button
                class="mt-2 text-sm text-blue-400 hover:text-blue-300 transition-colors"
                @click="() => {
                  console.log('Opening follow-up modal - contacts:', contacts);
                  console.log('Opening follow-up modal - contacts length:', contacts?.length || 0);
                  showAddFollowUpModal = true;
                }"
              >
                Schedule one
              </button>
            </div>

            <div v-else class="space-y-3">
              <div
                v-for="task in upcomingFollowUps.slice(0, 4)"
                :key="task.id"
                class="p-3 bg-blue-500/20 rounded-lg border border-blue-400/20 cursor-pointer hover:bg-blue-500/30 transition-all duration-200"
                @click="handleTaskClick(task)"
              >
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <p class="text-sm font-medium text-white">{{ task.title }}</p>
                    <p class="text-xs text-white mt-0.5">
                      <Icon name="mdi:account" class="w-3 h-3 inline mr-1 text-gray-400" />
                      {{ getTaskContactName(task) }}
                      <span v-if="getTaskBusinessName(task)" class="text-gray-400">
                        • {{ getTaskBusinessName(task) }}
                      </span>
                    </p>
                    <div class="flex items-center mt-1">
                      <Icon :name="getFollowUpIcon(task.type)" class="w-3 h-3 text-gray-400 mr-1" />
                      <p class="text-xs text-gray-300">
                        {{ formatDate(task.dueDate) }}
                      </p>
                    </div>
                  </div>
                  <div class="flex space-x-1">
                    <button
                      class="text-green-400 hover:text-green-300 transition-colors"
                      title="Mark complete"
                      @click.stop="completeFollowUp(task.id)"
                    >
                      <Icon name="mdi:check" class="w-4 h-4" />
                    </button>
                    <button
                      class="text-blue-400 hover:text-blue-300 transition-colors"
                      title="Edit"
                      @click.stop="editFollowUp(task)"
                    >
                      <Icon name="mdi:pencil" class="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div
          class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-xl border border-white/10 hover:border-white/20 transition-all duration-300"
        >
          <div class="p-4 border-b border-white/10">
            <h3 class="text-lg font-semibold text-white">Quick Actions</h3>
          </div>

          <div class="p-4 space-y-3">
            <button
              class="w-full flex items-center px-3 py-2 text-sm text-left rounded-lg bg-gradient-to-r from-blue-500/10 to-blue-600/10 hover:from-blue-500/20 hover:to-blue-600/20 border border-blue-400/20 transition-all duration-200 text-white"
              @click="showAddInteractionModal = true"
            >
              <Icon name="mdi:note-plus" class="w-4 h-4 mr-3 text-blue-400" />
              Add Interaction
            </button>

            <button
              class="w-full flex items-center px-3 py-2 text-sm text-left rounded-lg bg-gradient-to-r from-green-500/10 to-green-600/10 hover:from-green-500/20 hover:to-green-600/20 border border-green-400/20 transition-all duration-200 text-white"
              @click="() => {
                console.log('Opening follow-up modal from quick actions - contacts:', contacts);
                console.log('Opening follow-up modal from quick actions - contacts length:', contacts?.length || 0);
                showAddFollowUpModal = true;
              }"
            >
              <Icon name="mdi:calendar-plus" class="w-4 h-4 mr-3 text-green-400" />
              Schedule Follow-up
            </button>

            <NuxtLink
              to="/c/businesscards-scan"
              class="w-full flex items-center px-3 py-2 text-sm text-left rounded-lg bg-gradient-to-r from-purple-500/10 to-purple-600/10 hover:from-purple-500/20 hover:to-purple-600/20 border border-purple-400/20 transition-all duration-200 text-white"
            >
              <Icon name="mdi:camera" class="w-4 h-4 mr-3 text-purple-400" />
              Scan Business Card
            </NuxtLink>

            <NuxtLink
              to="/c/businesscards/list-enhanced"
              class="w-full flex items-center px-3 py-2 text-sm text-left rounded-lg bg-gradient-to-r from-orange-500/10 to-orange-600/10 hover:from-orange-500/20 hover:to-orange-600/20 border border-orange-400/20 transition-all duration-200 text-white"
            >
              <Icon name="mdi:map" class="w-4 h-4 mr-3 text-orange-400" />
              View Map
            </NuxtLink>

            <NuxtLink
              to="/c/calendar"
              class="w-full flex items-center px-3 py-2 text-sm text-left rounded-lg bg-gradient-to-r from-pink-500/10 to-pink-600/10 hover:from-pink-500/20 hover:to-pink-600/20 border border-pink-400/20 transition-all duration-200 text-white"
            >
              <Icon name="mdi:calendar" class="w-4 h-4 mr-3 text-pink-400" />
              View Calendar
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>

    <!-- Engagement Overview Chart -->
    <div
      class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-xl border border-white/10 hover:border-white/20 transition-all duration-300"
    >
      <div class="p-6 border-b border-white/10">
        <h3 class="text-lg font-semibold text-white">Engagement Overview</h3>
      </div>

      <div class="p-6">
        <div v-if="loading" class="text-center py-8">
          <Icon name="mdi:loading" class="w-8 h-8 text-gray-400 mx-auto mb-3 animate-spin" />
          <p class="text-gray-300">Calculating engagement metrics...</p>
        </div>

        <div v-else class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Hot Contacts -->
          <div class="text-center">
            <div class="relative inline-flex">
              <svg class="w-20 h-20 transform -rotate-90">
                <circle
                  cx="40"
                  cy="40"
                  r="30"
                  stroke="rgba(239, 68, 68, 0.2)"
                  stroke-width="8"
                  fill="transparent"
                />
                <circle
                  cx="40"
                  cy="40"
                  r="30"
                  stroke="#ef4444"
                  stroke-width="8"
                  fill="transparent"
                  :stroke-dasharray="188.4"
                  :stroke-dashoffset="188.4 - (188.4 * engagementStats.hot) / 100"
                  class="transition-all duration-300"
                />
              </svg>
              <div class="absolute inset-0 flex items-center justify-center">
                <span class="text-sm font-bold text-red-400">{{ engagementStats.hot }}%</span>
              </div>
            </div>
            <p class="mt-2 text-sm font-medium text-white">Hot</p>
            <p class="text-xs text-gray-400">{{ hotContacts.length }} contacts</p>
          </div>

          <!-- Warm Contacts -->
          <div class="text-center">
            <div class="relative inline-flex">
              <svg class="w-20 h-20 transform -rotate-90">
                <circle
                  cx="40"
                  cy="40"
                  r="30"
                  stroke="rgba(245, 158, 11, 0.2)"
                  stroke-width="8"
                  fill="transparent"
                />
                <circle
                  cx="40"
                  cy="40"
                  r="30"
                  stroke="#f59e0b"
                  stroke-width="8"
                  fill="transparent"
                  :stroke-dasharray="188.4"
                  :stroke-dashoffset="188.4 - (188.4 * engagementStats.warm) / 100"
                  class="transition-all duration-300"
                />
              </svg>
              <div class="absolute inset-0 flex items-center justify-center">
                <span class="text-sm font-bold text-yellow-400">{{ engagementStats.warm }}%</span>
              </div>
            </div>
            <p class="mt-2 text-sm font-medium text-white">Warm</p>
            <p class="text-xs text-gray-400">{{ warmContacts.length }} contacts</p>
          </div>

          <!-- Cold Contacts -->
          <div class="text-center">
            <div class="relative inline-flex">
              <svg class="w-20 h-20 transform -rotate-90">
                <circle
                  cx="40"
                  cy="40"
                  r="30"
                  stroke="rgba(107, 114, 128, 0.2)"
                  stroke-width="8"
                  fill="transparent"
                />
                <circle
                  cx="40"
                  cy="40"
                  r="30"
                  stroke="#6b7280"
                  stroke-width="8"
                  fill="transparent"
                  :stroke-dasharray="188.4"
                  :stroke-dashoffset="188.4 - (188.4 * engagementStats.cold) / 100"
                  class="transition-all duration-300"
                />
              </svg>
              <div class="absolute inset-0 flex items-center justify-center">
                <span class="text-sm font-bold text-gray-400">{{ engagementStats.cold }}%</span>
              </div>
            </div>
            <p class="mt-2 text-sm font-medium text-white">Cold</p>
            <p class="text-xs text-gray-400">{{ coldContacts.length }} contacts</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Modals -->
    <CrmAddInteractionModal
      :open="showAddInteractionModal"
      :preselected-contact-id="selectedContactId"
      @close="showAddInteractionModal = false"
      @success="handleInteractionSuccess"
    />

    <CrmScheduleFollowupModal
      :open="showAddFollowUpModal"
      :preselected-contact-id="selectedContactId"
      :contacts="contacts"
      @close="showAddFollowUpModal = false"
      @success="handleFollowUpSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { useRouter } from '#app';
import { computed, onMounted, ref, watch } from 'vue';

import { useActivities } from '~/composables/useActivities';
import { useBusinesses } from '~/composables/useBusinesses';
import { useContacts } from '~/composables/useContacts';
import { useCurrentUser } from '~/composables/useCurrentUser';

const router = useRouter();
const { currentUser } = useCurrentUser();

// Composables
const { contacts, leads, subscribeToContacts, getFullName, getContact } = useContacts();

const { businesses, subscribeToBusinesses } = useBusinesses();

const {
  activities,
  tasks,
  pendingTasks,
  overdueTasks,
  subscribeToActivities,
  completeTask,
  createActivity,
} = useActivities();

// Initialize subscriptions
onMounted(() => {
  if (currentUser.value?.uid) {
    // Subscribe to user's contacts and leads
    subscribeToContacts({ ownerId: currentUser.value.uid });

    // Subscribe to user's businesses
    subscribeToBusinesses({ ownerId: currentUser.value.uid });

    // Subscribe to user's activities
    subscribeToActivities({ ownerId: currentUser.value.uid });
  }
});

// Cache for fetched contacts not owned by current user
const externalContactsCache = ref(new Map<string, any>());

// Fetch contact details for activities
const fetchMissingContacts = async () => {
  // Get unique contact IDs from activities and tasks
  const contactIds = new Set<string>();
  
  activities.value.forEach(activity => {
    activity.associatedEntities.forEach((entity: any) => {
      if (entity.entityType === 'contact' && entity.entityId) {
        contactIds.add(entity.entityId);
      }
    });
  });

  tasks.value.forEach(task => {
    task.associatedEntities.forEach((entity: any) => {
      if (entity.entityType === 'contact' && entity.entityId) {
        contactIds.add(entity.entityId);
      }
    });
  });

  // Fetch any missing contacts
  for (const contactId of contactIds) {
    if (!contactMap.value.has(contactId) && !externalContactsCache.value.has(contactId)) {
      try {
        const contact = await getContact(contactId);
        if (contact) {
          externalContactsCache.value.set(contactId, contact);
        }
      } catch (error) {
        console.error(`Failed to fetch contact ${contactId}:`, error);
      }
    }
  }
};

// Watch for changes in activities and tasks to fetch missing contacts
watch([activities, tasks], () => {
  fetchMissingContacts();
}, { immediate: true });

// Debug contacts loading
watch(contacts, (newContacts) => {
  console.log('CRMDashboard - contacts updated:', newContacts);
  console.log('CRMDashboard - contacts length:', newContacts?.length || 0);
}, { immediate: true });

// Loading state
const loading = ref(false);

// Search functionality
const searchQuery = ref('');
const searchResults = ref<any>(null);
const isSearching = ref(false);

// Selected contact for timeline
const selectedContactId = ref<string | null>(null);

// Modal states
const showActivityModal = ref(false);
const showFollowUpsModal = ref(false);
const showAddInteractionModal = ref(false);
const showAddFollowUpModal = ref(false);

// Contact lookup map for performance
const contactMap = computed(() => {
  const map = new Map();
  contacts.value.forEach(contact => {
    map.set(contact.id, contact);
  });
  return map;
});

// Get contact name by ID
const getContactName = (contactId: string) => {
  // First check owned contacts
  const contact = contactMap.value.get(contactId);
  if (contact) {
    return getFullName(contact);
  }
  
  // Then check external contacts cache
  const externalContact = externalContactsCache.value.get(contactId);
  if (externalContact) {
    return getFullName(externalContact);
  }
  
  return 'Unknown Contact';
};

// Calculate engagement levels based on activity history
const calculateEngagementLevel = (contactId: string) => {
  const contactActivities = activities.value.filter(a =>
    a.associatedEntities.some(e => e.entityType === 'contact' && e.entityId === contactId)
  );
  const recentActivities = contactActivities.filter(a => {
    const days = Math.floor((Date.now() - new Date(a.createdAt).getTime()) / (1000 * 60 * 60 * 24));
    return days <= 30;
  });

  if (recentActivities.length >= 5) return 'hot';
  if (recentActivities.length >= 2) return 'warm';
  return 'cold';
};

// Enhanced contacts with engagement levels
const enhancedContacts = computed(() => {
  return contacts.value.map(contact => ({
    ...contact,
    engagementLevel: calculateEngagementLevel(contact.id),
  }));
});

// Computed stats
const stats = computed(() => ({
  totalContacts: contacts.value.length,
  hotLeads: enhancedContacts.value.filter(c => c.engagementLevel === 'hot' && c.type === 'lead')
    .length,
  overdueFollowUps: overdueTasks.value.length,
  upcomingThisWeek: pendingTasks.value.filter(t => {
    const dueDate = new Date(t.dueDate);
    const now = new Date();
    const diffDays = Math.floor((dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    return diffDays >= 0 && diffDays <= 7;
  }).length,
}));

const recentActivities = computed(() => {
  const sorted = activities.value
    .slice()
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

  // If searching, filter to matched activities
  if (searchQuery.value && searchResults.value) {
    const matchedIds = new Set(searchResults.value.activities.map(a => a.id));
    return sorted.filter(a => matchedIds.has(a.id));
  }

  return sorted.slice(0, 10);
});

const overdueFollowUps = computed(() => {
  const overdue = overdueTasks.value;

  // If searching, filter to matched tasks
  if (searchQuery.value && searchResults.value) {
    const matchedIds = new Set(searchResults.value.tasks.map(t => t.id));
    return overdue.filter(t => matchedIds.has(t.id));
  }

  return overdue;
});

const upcomingFollowUps = computed(() => {
  const upcoming = pendingTasks.value.filter(t => {
    const dueDate = new Date(t.dueDate);
    const now = new Date();
    const diffDays = Math.floor((dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    return diffDays >= 0 && diffDays <= 7;
  });

  // If searching, filter to matched tasks
  if (searchQuery.value && searchResults.value) {
    const matchedIds = new Set(searchResults.value.tasks.map(t => t.id));
    return upcoming.filter(t => matchedIds.has(t.id));
  }

  return upcoming;
});

const hotContacts = computed(() => enhancedContacts.value.filter(c => c.engagementLevel === 'hot'));
const warmContacts = computed(() =>
  enhancedContacts.value.filter(c => c.engagementLevel === 'warm')
);
const coldContacts = computed(() =>
  enhancedContacts.value.filter(c => c.engagementLevel === 'cold')
);

const engagementStats = computed(() => {
  const total = enhancedContacts.value.length;
  if (total === 0) return { hot: 0, warm: 0, cold: 0 };

  return {
    hot: Math.round((hotContacts.value.length / total) * 100),
    warm: Math.round((warmContacts.value.length / total) * 100),
    cold: Math.round((coldContacts.value.length / total) * 100),
  };
});

// Search functionality
const handleSearch = async () => {
  if (!searchQuery.value.trim()) {
    searchResults.value = null;
    return;
  }

  isSearching.value = true;

  // Search contacts
  const matchedContacts = contacts.value.filter(
    c =>
      getFullName(c).toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      c.email?.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      c.company?.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      c.tags?.some(tag => tag.toLowerCase().includes(searchQuery.value.toLowerCase()))
  );

  // Search activities
  const matchedActivities = activities.value.filter(
    a =>
      a.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      a.description?.toLowerCase().includes(searchQuery.value.toLowerCase())
  );

  // Search tasks
  const matchedTasks = tasks.value.filter(
    t =>
      t.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      t.description?.toLowerCase().includes(searchQuery.value.toLowerCase())
  );

  searchResults.value = {
    total: matchedContacts.length + matchedActivities.length + matchedTasks.length,
    contacts: matchedContacts,
    activities: matchedActivities,
    tasks: matchedTasks,
  };

  isSearching.value = false;
};

const clearSearch = () => {
  searchQuery.value = '';
  searchResults.value = null;
};

// Methods
const getActivityIcon = (type: string): string => {
  const icons = {
    note: 'mdi:note-text',
    meeting: 'mdi:calendar-account',
    call: 'mdi:phone',
    email: 'mdi:email',
    message: 'mdi:message-text',
    task: 'mdi:check-circle',
  };
  return icons[type] || 'mdi:note-text';
};

const getActivityGradient = (type: string): string => {
  const gradients = {
    note: 'from-blue-500 to-blue-600',
    meeting: 'from-green-500 to-green-600',
    call: 'from-purple-500 to-purple-600',
    email: 'from-yellow-500 to-yellow-600',
    message: 'from-pink-500 to-pink-600',
    task: 'from-gray-500 to-gray-600',
  };
  return gradients[type] || 'from-blue-500 to-blue-600';
};

const getFollowUpIcon = (type: string): string => {
  const icons = {
    call: 'mdi:phone',
    email: 'mdi:email',
    meeting: 'mdi:calendar-account',
    task: 'mdi:check-circle',
    reminder: 'mdi:bell',
  };
  return icons[type] || 'mdi:bell';
};

// Date formatting utilities
const formatRelativeTime = (date: Date | string) => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  const diffMs = now.getTime() - dateObj.getTime();
  const diffSecs = Math.floor(diffMs / 1000);
  const diffMins = Math.floor(diffSecs / 60);
  const diffHours = Math.floor(diffMins / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffSecs < 60) return 'just now';
  if (diffMins < 60) return `${diffMins}m ago`;
  if (diffHours < 24) return `${diffHours}h ago`;
  if (diffDays < 30) return `${diffDays}d ago`;
  return dateObj.toLocaleDateString();
};

const formatDate = (date: Date | string) => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: dateObj.getFullYear() !== new Date().getFullYear() ? 'numeric' : undefined,
  });
};

// Helper functions for activities and tasks
const handleActivityClick = (activity: any) => {
  const entity = activity.associatedEntities.find((e: any) => e.entityType === 'contact');
  if (entity) {
    navigateToContact(entity.entityId);
  }
};

const handleTaskClick = (task: any) => {
  const entity = task.associatedEntities.find((e: any) => e.entityType === 'contact');
  if (entity) {
    navigateToContact(entity.entityId);
  }
};

const getActivityContactName = (activity: any) => {
  const entity = activity.associatedEntities.find((e: any) => e.entityType === 'contact');
  return entity ? getContactName(entity.entityId) : 'Unknown';
};

const getActivityBusinessName = (activity: any) => {
  const contactEntity = activity.associatedEntities.find((e: any) => e.entityType === 'contact');
  if (!contactEntity) return null;

  // Check both owned and external contacts
  const contact = contactMap.value.get(contactEntity.entityId) || externalContactsCache.value.get(contactEntity.entityId);
  if (!contact?.businessId) return null;

  const business = businesses.value.find(b => b.id === contact.businessId);
  return business?.name || null;
};

const getTaskContactName = (task: any) => {
  const entity = task.associatedEntities.find((e: any) => e.entityType === 'contact');
  return entity ? getContactName(entity.entityId) : 'Unknown';
};

const getTaskBusinessName = (task: any) => {
  const contactEntity = task.associatedEntities.find((e: any) => e.entityType === 'contact');
  if (!contactEntity) return null;

  // Check both owned and external contacts
  const contact = contactMap.value.get(contactEntity.entityId) || externalContactsCache.value.get(contactEntity.entityId);
  if (!contact?.businessId) return null;

  const business = businesses.value.find(b => b.id === contact.businessId);
  return business?.name || null;
};

const completeFollowUp = async (taskId: string) => {
  try {
    await completeTask(taskId);
  } catch (error) {
    console.error('Failed to complete task:', error);
  }
};

const editFollowUp = (task: any) => {
  // Navigate to the contact with the task selected
  const entity = task.associatedEntities.find((e: any) => e.entityType === 'contact');
  if (entity) {
    router.push(`/c/contacts/${entity.entityId}?taskId=${task.id}`);
  }
};

const navigateToContact = (contactId: string) => {
  if (contactId) {
    router.push(`/c/contacts/${contactId}`);
  }
};

// Select first hot contact for timeline on mount
watch(
  hotContacts,
  newHotContacts => {
    if (newHotContacts.length > 0 && !selectedContactId.value) {
      selectedContactId.value = newHotContacts[0].id;
    } else if (contacts.value.length > 0 && !selectedContactId.value) {
      selectedContactId.value = contacts.value[0].id;
    }
  },
  { immediate: true }
);

// Update selected contact when activities change
watch(recentActivities, newActivities => {
  if (newActivities.length > 0 && !selectedContactId.value) {
    const entity = newActivities[0].associatedEntities.find(e => e.entityType === 'contact');
    if (entity) {
      selectedContactId.value = entity.entityId;
    }
  }
});

// Success handlers for modals
const handleInteractionSuccess = () => {
  // The activity will automatically appear in the timeline due to real-time subscriptions
  // You could add a success notification here if needed
  console.log('Interaction added successfully');
};

const handleFollowUpSuccess = () => {
  // The follow-up will automatically appear in the upcoming tasks due to real-time subscriptions
  // You could add a success notification here if needed
  console.log('Follow-up scheduled successfully');
};
</script>

<style scoped>
.crm-dashboard {
  max-width: 100%;
}

/* Glassmorphism enhancement */
.backdrop-blur-sm {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Custom scrollbar for all scrollable areas */
.crm-dashboard ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.crm-dashboard ::-webkit-scrollbar-track {
  background: rgba(31, 41, 55, 0.5);
  border-radius: 3px;
}

.crm-dashboard ::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.8);
  border-radius: 3px;
}

.crm-dashboard ::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.9);
}

/* Smooth transitions */
.crm-dashboard * {
  transition-property: background-color, border-color, color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Loading animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
