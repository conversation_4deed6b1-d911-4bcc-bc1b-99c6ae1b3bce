<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { useCRM } from '~/composables/useCRM';
import type { ICRMClient, ICRMBusiness } from '~/types/crm';
import type { VisionOCRResult } from '~/types/ocr';

interface Props {
  ocrResult?: VisionOCRResult;
  businessCardUrl?: string;
  existingClient?: ICRMClient;
}

const props = defineProps<Props>();

const emit = defineEmits(['save', 'cancel', 'error']);

const { 
  createClient, 
  updateClient, 
  getBusinesses, 
  createBusiness,
  businesses,
  loading: crmLoading,
  error: crmError
} = useCRM();

// Form state
const form = ref<Partial<ICRMClient>>({
  name: '',
  email: '',
  phone: '',
  title: '',
  company: '',
  address: '',
  notes: '',
  business_id: '',
  source: 'business_card' as const
});

// UI state
const saving = ref(false);
const errors = ref<Record<string, string>>({});
const showNewBusinessForm = ref(false);
const newBusinessName = ref('');
const searchQuery = ref('');
const selectedBusiness = ref<ICRMBusiness | null>(null);

// Validation state
const isValid = computed(() => {
  // Must have a name
  if (!form.value.name?.trim()) return false;
  
  // Must have at least one contact method
  if (!form.value.email && !form.value.phone) return false;
  
  // Email must be valid if provided
  if (form.value.email && !isValidEmail(form.value.email)) return false;
  
  // Phone must be valid if provided
  if (form.value.phone && !isValidPhone(form.value.phone)) return false;
  
  // No errors
  return Object.keys(errors.value).length === 0;
});

// Filtered businesses for search
const filteredBusinesses = computed(() => {
  if (!searchQuery.value) return businesses.value;
  
  const query = searchQuery.value.toLowerCase();
  return businesses.value.filter(b => 
    b.name.toLowerCase().includes(query) ||
    b.address?.toLowerCase().includes(query)
  );
});

// Initialize form with OCR data or existing client
onMounted(() => {
  if (props.existingClient) {
    // Editing existing client
    form.value = { ...props.existingClient };
    if (props.existingClient.business_id) {
      selectedBusiness.value = businesses.value.find(b => b.id === props.existingClient.business_id) || null;
    }
  } else if (props.ocrResult) {
    // New client from OCR
    form.value = {
      name: props.ocrResult.name || '',
      email: props.ocrResult.email || '',
      phone: props.ocrResult.phone || '',
      title: props.ocrResult.title || '',
      company: props.ocrResult.company || '',
      address: formatAddress(props.ocrResult.address),
      source: 'business_card',
      business_card_id: props.businessCardUrl
    };
    
    // Try to match company to existing business
    if (props.ocrResult.company) {
      const matchedBusiness = businesses.value.find(b => 
        b.name.toLowerCase() === props.ocrResult.company.toLowerCase()
      );
      if (matchedBusiness) {
        selectedBusiness.value = matchedBusiness;
        form.value.business_id = matchedBusiness.id;
      }
    }
  }
  
  // Load businesses
  loadBusinesses();
});

// Load businesses list
const loadBusinesses = async () => {
  try {
    await getBusinesses();
  } catch (error) {
    console.error('Failed to load businesses:', error);
  }
};

// Form validation
const validateField = (field: string, value: any) => {
  delete errors.value[field];
  
  switch (field) {
    case 'name':
      if (!value?.trim()) {
        errors.value.name = 'Name is required';
      }
      break;
    case 'email':
      if (value && !isValidEmail(value)) {
        errors.value.email = 'Invalid email format';
      }
      break;
    case 'phone':
      if (value && !isValidPhone(value)) {
        errors.value.phone = 'Invalid phone format';
      }
      break;
  }
};

// Watch form fields for validation
watch(() => form.value.name, (val) => validateField('name', val));
watch(() => form.value.email, (val) => validateField('email', val));
watch(() => form.value.phone, (val) => validateField('phone', val));

// Business selection
const selectBusiness = (business: ICRMBusiness) => {
  selectedBusiness.value = business;
  form.value.business_id = business.id;
  searchQuery.value = business.name;
};

// Create new business
const createNewBusinessAndLink = async () => {
  if (!newBusinessName.value.trim()) return;
  
  try {
    const business = await createBusiness({
      name: newBusinessName.value.trim(),
      address: form.value.address || ''
    });
    
    selectBusiness(business);
    showNewBusinessForm.value = false;
    newBusinessName.value = '';
  } catch (error) {
    console.error('Failed to create business:', error);
    errors.value.business = 'Failed to create business';
  }
};

// Save client
const save = async () => {
  // Validate all fields
  Object.keys(form.value).forEach(field => {
    validateField(field, form.value[field as keyof ICRMClient]);
  });
  
  if (!isValid.value) return;
  
  saving.value = true;
  errors.value = {};
  
  try {
    let result;
    if (props.existingClient?.id) {
      // Update existing
      result = await updateClient(props.existingClient.id, form.value);
    } else {
      // Create new
      result = await createClient(form.value as Omit<ICRMClient, 'id' | 'user_id' | 'created_at' | 'updated_at'>);
    }
    
    emit('save', result);
  } catch (error: any) {
    console.error('Failed to save client:', error);
    errors.value.save = error.message || 'Failed to save client';
    emit('error', error);
  } finally {
    saving.value = false;
  }
};

// Cancel editing
const cancel = () => {
  emit('cancel');
};

// Helper functions
function isValidEmail(email: string): boolean {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(email);
}

function isValidPhone(phone: string): boolean {
  // Basic phone validation - at least 10 digits
  const digits = phone.replace(/\D/g, '');
  return digits.length >= 10;
}

function formatAddress(address: any): string {
  if (!address) return '';
  if (typeof address === 'string') return address;
  
  const parts = [];
  if (address.street) parts.push(address.street);
  if (address.city) parts.push(address.city);
  if (address.state) parts.push(address.state);
  if (address.postalCode) parts.push(address.postalCode);
  if (address.country) parts.push(address.country);
  
  return parts.join(', ');
}
</script>

<template>
  <div class="max-w-6xl mx-auto p-4 md:p-6">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div class="p-6">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          {{ existingClient ? 'Edit Contact' : 'Review & Save Contact' }}
        </h2>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- Left column: Form -->
          <div class="space-y-6">
            <!-- Personal Information -->
            <div>
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Personal Information</h3>
              
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Name <span class="text-red-500">*</span>
                  </label>
                  <input
                    v-model="form.name"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    :class="{ 'border-red-500': errors.name }"
                  />
                  <p v-if="errors.name" class="mt-1 text-sm text-red-600">{{ errors.name }}</p>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Title
                  </label>
                  <input
                    v-model="form.title"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Company
                  </label>
                  <input
                    v-model="form.company"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                </div>
              </div>
            </div>
            
            <!-- Contact Information -->
            <div>
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Contact Information</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">At least one contact method is required</p>
              
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Email
                  </label>
                  <input
                    v-model="form.email"
                    type="email"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    :class="{ 'border-red-500': errors.email }"
                  />
                  <p v-if="errors.email" class="mt-1 text-sm text-red-600">{{ errors.email }}</p>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Phone
                  </label>
                  <input
                    v-model="form.phone"
                    type="tel"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    :class="{ 'border-red-500': errors.phone }"
                  />
                  <p v-if="errors.phone" class="mt-1 text-sm text-red-600">{{ errors.phone }}</p>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Address
                  </label>
                  <textarea
                    v-model="form.address"
                    rows="2"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                </div>
              </div>
            </div>
            
            <!-- Business Association -->
            <div>
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Business Association</h3>
              
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Link to Business
                  </label>
                  <div class="relative">
                    <input
                      v-model="searchQuery"
                      type="text"
                      placeholder="Search businesses..."
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    />
                    
                    <!-- Business dropdown -->
                    <div v-if="searchQuery && filteredBusinesses.length > 0" class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-48 overflow-auto">
                      <button
                        v-for="business in filteredBusinesses"
                        :key="business.id"
                        @click="selectBusiness(business)"
                        class="w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700"
                      >
                        <div class="font-medium text-gray-900 dark:text-white">{{ business.name }}</div>
                        <div v-if="business.address" class="text-sm text-gray-500 dark:text-gray-400">{{ business.address }}</div>
                      </button>
                    </div>
                  </div>
                  
                  <!-- Selected business -->
                  <div v-if="selectedBusiness" class="mt-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                    <div class="flex justify-between items-start">
                      <div>
                        <p class="font-medium text-gray-900 dark:text-white">{{ selectedBusiness.name }}</p>
                        <p v-if="selectedBusiness.address" class="text-sm text-gray-500 dark:text-gray-400">{{ selectedBusiness.address }}</p>
                      </div>
                      <button
                        @click="selectedBusiness = null; form.business_id = ''; searchQuery = ''"
                        class="text-gray-400 hover:text-gray-600"
                      >
                        <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  </div>
                  
                  <!-- Create new business option -->
                  <button
                    v-if="!showNewBusinessForm"
                    @click="showNewBusinessForm = true"
                    class="mt-2 text-sm text-blue-600 hover:text-blue-500"
                  >
                    + Create new business
                  </button>
                  
                  <!-- New business form -->
                  <div v-if="showNewBusinessForm" class="mt-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-md space-y-2">
                    <input
                      v-model="newBusinessName"
                      type="text"
                      placeholder="Business name"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white"
                    />
                    <div class="flex space-x-2">
                      <button
                        @click="createNewBusinessAndLink"
                        :disabled="!newBusinessName.trim()"
                        class="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Create
                      </button>
                      <button
                        @click="showNewBusinessForm = false; newBusinessName = ''"
                        class="px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Notes -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Notes
              </label>
              <textarea
                v-model="form.notes"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                placeholder="Add any additional notes..."
              />
            </div>
          </div>
          
          <!-- Right column: Business card preview -->
          <div v-if="businessCardUrl" class="lg:pl-8">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Business Card</h3>
            <div class="relative">
              <img
                :src="businessCardUrl"
                alt="Business card"
                class="w-full rounded-lg shadow-lg"
              />
              <div class="absolute top-2 right-2 bg-white dark:bg-gray-800 rounded-full p-2 shadow-lg">
                <svg class="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Action buttons -->
        <div class="mt-8 flex justify-end space-x-4">
          <button
            @click="cancel"
            class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
          >
            Cancel
          </button>
          <button
            @click="save"
            :disabled="!isValid || saving"
            class="px-6 py-2 bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-md hover:from-blue-600 hover:to-cyan-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span v-if="saving" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Saving...
            </span>
            <span v-else>{{ existingClient ? 'Update' : 'Save' }} Contact</span>
          </button>
        </div>
        
        <!-- Error message -->
        <div v-if="errors.save" class="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <p class="text-sm text-red-600 dark:text-red-400">{{ errors.save }}</p>
        </div>
      </div>
    </div>
  </div>
</template>