<script setup lang="ts">
const darkMode = useState("darkMode", () => true);
</script>

<template>
  <div
    class="flex px-4 py-3 text-sm rounded-full focus:outline-none hover:text-primarys cursor-pointer"
    id="icon-dark"
  >
    <div class="relative inline-block">
      <Icon
        name="mdi:star-face"
        size="24"
        @click="$router.push('/c/dashboard-admin')"
      />
    </div>
  </div>
</template>
