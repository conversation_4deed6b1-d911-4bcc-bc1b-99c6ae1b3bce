<script setup lang="ts">
//get notifications from firebase firestore
import { collection, query, where, getDocs } from "firebase/firestore";

const { currentSpace } = space()
const currentClient: any = useState("currentClient", () => {
    return {};
  });
const { updateToId: updateToIdNotification } = database()

const open = ref(false)
const notifications: any = ref([])

const getNotifications = async ()=> {
  if(currentSpace.value.id && currentClient.value.id){
    const { firestore } = useFirebase();
    const q = query(collection(firestore, "notifications"),where('status', '==', 'unread'), where('space_own', '==', currentSpace.value.id), where('notification_id', 'array-contains', currentClient.value.id));

const querySnapshot = await getDocs(q);
querySnapshot.forEach((doc) => {
  console.log(doc.id, " => ", doc.data());
  notifications.value.push({ id: doc.id, ...doc.data() })
});
  }
}
watch(()=> currentSpace.value.id, ()=> {
  getNotifications()
})
onMounted(()=> {
  getNotifications()
})


const deleteAll = async () => {
  for (let i = 0; i < notifications.value.length; i++) {
    await updateToIdNotification(notifications.value[i].id, 'notifications',
    { status: 'read'})
  }
  notifications.value = []
}
</script>

<template>
  <div class="relative">
    <button 
      @click="open = !open"
      class="flex items-center p-2 rounded-full focus:outline-none hover:bg-white/10 transition-all duration-200 group" 
      title="Notifications"
    >
      <div class="w-10 h-10 bg-gradient-to-br from-red-500 to-pink-600 rounded-full flex items-center justify-center shadow-lg border-2 border-white/20 group-hover:border-white/40 transition-all duration-200 relative">
        <Icon
          :name="open ? 'line-md:bell' : 'line-md:bell-twotone'"
          class="text-white text-xl"
        />
        <!-- Notification badge -->
        <span v-if="notifications.length > 0"
          class="absolute -top-1 -right-1 flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-gradient-to-r from-orange-500 to-red-600 rounded-full border-2 border-white shadow-lg">
          {{ notifications.length > 99 ? '99+' : notifications.length }}
        </span>
      </div>
    </button>
    <onClickOutside @trigger="open = false">
      <div v-show="open"
        class="w-80 origin-top-right absolute ltr:right-0 rtl:left-0 rounded-lg top-full z-50 py-0.5 ltr:text-left rtl:text-right bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-lg -ml-32"
        style="display: none;">
        <div class="p-4 font-normal border-b border-gray-200 dark:border-gray-700">
          <div class="relative flex justify-between items-center">
            <div class="font-bold text-gray-800 dark:text-gray-200 text-lg">Notifications</div>
            <div class="ltr:right-0 rtl:left-0">
              <button
                @click="deleteAll"
                class="inline-flex items-center justify-center p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#0072ff] focus:ring-opacity-50"
                title="Clear all"
              >
                <Icon name="mdi:trash-can-outline" class="text-gray-600 dark:text-gray-400 hover:text-[#0072ff] dark:hover:text-[#82aae3]" size="18" />
              </button>
            </div>
          </div>
        </div>
        <div class="overflow-y-auto max-h-72 scrollbars show">
          <div class="relative" v-for="(noti, index) in notifications" :key="index">
            <card-notification :noti="noti" />
          </div>
          <div v-if="notifications.length === 0" class="p-4 text-center text-gray-500 dark:text-gray-400">
            <Icon name="mdi:bell-off-outline" size="32" class="mx-auto mb-2 opacity-50" />
            <p>No new notifications</p>
          </div>
        </div>
        <div class="p-3 font-normal text-center border-t border-gray-200 dark:border-gray-700">
          <a href="#" class="text-[#0072ff] dark:text-[#82aae3] hover:underline font-medium">Show all Notifications</a>
        </div>
      </div>

    </onClickOutside>

  </div>
</template>