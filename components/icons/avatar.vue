<script setup lang="ts">
let uiAvatar = defineProps({
  avatar: {
    type: String,
    default: '',
  },
  name: {
    type: String,
    default: '',
  },
  icon: {
    type: String,
    default: '',
  },
  showName: {
    type: Boolean,
    default: false,
  },
  classes: {
    type: String,
    default: 'h-8 w-8 mr-2',
  },

})

const src = computed(()=> {
  if(uiAvatar.avatar == '' || uiAvatar.avatar == '/') {
    return false
  } else {
    return uiAvatar.avatar
  }
}
)
</script>

<template>
    <div class="flex items-center justify-center rounded-full dark:bg-base_dark_200 bg-base_200">
      <div v-if="src" class="flex justify-center rounded">
        <img :src="src" :class="classes">
      </div>
      <div v-else-if="icon" class="flex justify-center rounded">
        <Icon :name="icon" :class="classes" />
      </div>
      <div v-else class="flex items-center justify-center w-full h-full px-2 text-3xl rounded-full" :class="classes">
        <p>{{ name.charAt(0) }}</p>
      </div>
      <div v-show="showName">
        {{ name }}
      </div>
    </div>
</template>
