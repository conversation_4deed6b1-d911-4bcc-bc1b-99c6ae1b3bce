<script setup lang="ts">
// No state needed for this component
</script>

<template>
  <button 
    @click="$router.push('/auth/login')"
    class="flex items-center p-2 rounded-full focus:outline-none hover:bg-white/10 transition-all duration-200 group" 
    title="Login"
  >
    <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg border-2 border-white/20 group-hover:border-white/40 transition-all duration-200">
      <Icon
        name="mdi:login"
        class="text-white text-xl"
      />
    </div>
  </button>
</template>
