<script setup lang="ts">
const darkMode = useState("darkMode", () => true);
</script>

<template>
  <button 
    @click="darkMode = !darkMode"
    class="flex items-center p-2 rounded-full focus:outline-none hover:bg-white/10 transition-all duration-200 group" 
    title="Toggle Dark Mode"
  >
    <div class="w-10 h-10 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-full flex items-center justify-center shadow-lg border-2 border-white/20 group-hover:border-white/40 transition-all duration-200">
      <Icon
        :name="
          darkMode
            ? 'line-md:moon-filled-alt-loop'
            : 'line-md:sun-rising-filled-loop'
        "
        class="text-white text-xl"
      />
    </div>
  </button>
</template>
