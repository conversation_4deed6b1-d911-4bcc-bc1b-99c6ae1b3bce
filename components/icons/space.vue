<script setup lang="ts">
import { collection, onSnapshot, query, where } from 'firebase/firestore'

// Use the new Firebase initialization
const { firestore } = useFirebase();
const currentUser: object = useState('currentUser', () => {
        return {}
    })
    const userSpaces = useState<any>("userSpaces", () => {
    return [];
  });
const { currentSpace  } = space()

const firstMount = useState('firstMount', ()=> true)

const open = ref(false)
const create_open = ref(false)

if (currentUser.value.id) {
  if(isSuperAdmin()){
    const q = query(collection(firestore, 'spaces'))
  const unsubscribe = onSnapshot(q, (querySnapshot) => {
    const result: any = []
    querySnapshot.forEach((doc) => {
      result.push({ id: doc.id, ...doc.data() })
    })
    if(firstMount.value){
      currentSpace.value = result[0]
      firstMount.value = false
    }
    if(result.length > 0){
      changeSpace(result[0])
    }else{
      changeSpace({
        id: 1
      })

    }

    userSpaces.value = result
  })
  }else{
    const q = query(collection(firestore, 'spaces'), where('access_uid', 'array-contains', currentUser.value.id))
  const unsubscribe = onSnapshot(q, (querySnapshot) => {
    const result: any = []
    querySnapshot.forEach((doc) => {
      result.push({ id: doc.id, ...doc.data() })
    })
    if(firstMount.value){
      currentSpace.value = result[0]
      firstMount.value = false
    }
    if(result.length > 0){
      changeSpace(result[0])
    }else{
      changeSpace({
        id: 1
      })

    }

    userSpaces.value = result
  })
  }


}
const router = useRouter()

const changeSpace = (space: any) => {
  currentSpace.value = space
  open.value = false
  router.push('/c/dashboard')
}
</script>

<template>
  <div class="relative hover:text-primarys">
    <a class="flex px-4 py-3 text-sm rounded-full group focus:outline-none" id="notify" @click="open = !open">
      <div class="relative inline-block">
        <Icon name="material-symbols:account-balance-rounded" class="cursor-pointer" size="24px" />

      </div>
    </a>
    <onClickOutside @trigger="open = false">
      <div v-show="open"
        class="w-72 origin-top-right absolute ltr:right-0 rtl:left-0 rounded top-full z-50 py-0.5 ltr:text-left rtl:text-right bg-white dark:bg-gray-800 border dark:border-gray-700 shadow-md -ml-24"
        style="display: none;">
        <div class="p-3 font-normal border-b border-gray-200 dark:border-gray-700">
          <div class="relative flex justify-between">
            <div class="font-bold">Accounts</div>
            <div class="ltr:right-0 rtl:left-0">
              <a href="#" class="inline-block ltr:mr-2 rtl:ml-2" title="Clear all">
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" class="inline-block w-4 h-4 bi bi-trash"
                  viewBox="0 0 16 16">
                  <path
                    d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z" />
                  <path fill-rule="evenodd"
                    d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z" />
                </svg>
                <!-- <i class="fas fa-trash-alt"></i> -->
              </a>
            </div>
          </div>
        </div>
        <div class="overflow-y-auto max-h-60 scrollbars show">
          <a class="relative cursor-pointer" v-for="(space, index) in sortableAB(userSpaces, 'name')" :key="index"
            @click="changeSpace(space)">
            <div
              class="flex flex-row flex-wrap items-center py-2 border-b border-gray-200 dark:border-gray-700 dark:bg-gray-900 dark:bg-opacity-40 dark:hover:bg-opacity-20 hover:bg-gray-100 bg-gray-50">
              <div class="flex-shrink w-1/4 max-w-full px-2 text-center">
                <div class="flex justify-center w-8 h-8 mx-auto rounded-full">
                  <div v-if="space.logo">
                    <div v-if="space.logo.src">
                      <div v-if="space.logo.src != '/profile.gif'">
                      <img :src="space.logo.src" :name="space.name" class="self-center h-8" />

                    </div>
                    <icons-avatar v-else avatar="" :name="space.name" />

                    </div>
                    <icons-avatar v-else avatar="" :name="space.name" />
                  </div>
                  <icons-avatar v-else avatar="" :name="space.name" />


                  <!-- <i class="self-center fas fa-calendar"></i> -->
                </div>
              </div>
              <div class="flex-shrink w-3/4 max-w-full px-2">
                <div class="text-sm font-bold">{{ space.name }}</div>
                <div class="h-8 mt-1 text-sm text-gray-500 truncate" v-html="space.description" v-show="space.description"></div>
                <div class="mt-1 text-sm text-gray-500">{{ space.created_date }}</div>
              </div>
            </div>
          </a>
        </div>
        <div @click="create_open = !create_open">
          <div class="p-3 font-normal text-center cursor-pointer">
            <a class="hover:underline">Create Account</a>

          </div>
        </div>
      </div>
    </onClickOutside>
<space-forms-quick-create v-if="create_open" :open="create_open"/>
  </div>
</template>
