<script setup lang="ts">
const showAll = useState("showAll", () => false);
</script>

<template>
  <div
    class="flex px-3 py-2 text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-[#0072ff] focus:ring-opacity-50 hover:text-[#0072ff] dark:hover:text-[#82aae3] cursor-pointer transition-colors duration-200"
    id="icon-star"
  >
    <div class="relative inline-block" :class="{ 'text-[#0072ff] dark:text-[#82aae3]': showAll }">
      <Icon
        name="mdi:star-face"
        size="24"
        @click="showAll = !showAll"
        class="text-gray-700 dark:text-gray-300 hover:text-[#0072ff] dark:hover:text-[#82aae3]"
        :class="{ 'text-[#0072ff] dark:text-[#82aae3]': showAll }"
      />
    </div>
  </div>
</template>
