<script setup lang="ts">
</script>

<template>
  <button 
    @click="$router.push('/c/dashboard')"
    class="flex items-center p-2 rounded-full focus:outline-none hover:bg-white/10 transition-all duration-200 group" 
    title="Go to Dashboard"
  >
    <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center shadow-lg border-2 border-white/20 group-hover:border-white/40 transition-all duration-200">
      <Icon
        name="material-symbols:dashboard"
        class="text-white text-xl"
      />
    </div>
  </button>
</template>
