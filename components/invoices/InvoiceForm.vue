<template>
  <div class="max-w-4xl mx-auto">
    <form @submit.prevent="saveInvoice">
      <!-- Invoice Number and Status -->
      <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 mb-6">
        <div class="flex justify-between items-center">
          <div>
            <div class="text-sm text-gray-600 dark:text-gray-400 mb-1">Invoice Number</div>
            <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {{ invoiceNumber || 'Generating...' }}
            </div>
          </div>
          <div v-if="invoice.status">
            <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full"
                  :class="getStatusClasses(invoice.status)">
              {{ invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1) }}
            </span>
          </div>
        </div>
      </div>

      <!-- Client Selection -->
      <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 mb-6">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Client Information
        </h2>
        
        <ClientSelector
          v-model="invoice.client_id"
          @client-selected="onClientSelect"
          class="mb-4"
        />
        
        <div v-if="selectedClient" class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div class="font-medium text-gray-900 dark:text-gray-100">{{ selectedClient.name }}</div>
          <div v-if="selectedClient.company" class="text-gray-600 dark:text-gray-400">{{ selectedClient.company }}</div>
          <div v-if="selectedClient.email" class="text-sm text-gray-600 dark:text-gray-400">📧 {{ selectedClient.email }}</div>
          <div v-if="selectedClient.address" class="text-sm text-gray-600 dark:text-gray-400">📍 {{ selectedClient.address }}</div>
        </div>
      </div>

      <!-- Invoice Details -->
      <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 mb-6">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Invoice Details
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- Issue Date -->
          <div>
            <label for="issue_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Issue Date *
            </label>
            <input
              v-model="invoice.issue_date"
              type="date"
              id="issue_date"
              :max="today"
              required
              class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 
                     rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent
                     bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            />
          </div>

          <!-- Due Date -->
          <div>
            <label for="due_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Due Date *
            </label>
            <input
              v-model="invoice.due_date"
              type="date"
              id="due_date"
              :min="invoice.issue_date"
              required
              class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 
                     rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent
                     bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            />
          </div>

          <!-- Currency -->
          <div>
            <label for="currency" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Currency
            </label>
            <select
              v-model="invoice.currency"
              id="currency"
              class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 
                     rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent
                     bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            >
              <option value="USD">USD ($)</option>
              <option value="EUR">EUR (€)</option>
              <option value="GBP">GBP (£)</option>
              <option value="CAD">CAD (C$)</option>
              <option value="AUD">AUD (A$)</option>
            </select>
          </div>
        </div>

        <!-- Payment Terms -->
        <div class="mt-4">
          <label for="terms" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Payment Terms
          </label>
          <select
            v-model="invoice.terms"
            id="terms"
            class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 
                   rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent
                   bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
          >
            <option value="Due upon receipt">Due upon receipt</option>
            <option value="Net 15">Net 15</option>
            <option value="Net 30">Net 30</option>
            <option value="Net 45">Net 45</option>
            <option value="Net 60">Net 60</option>
          </select>
        </div>
      </div>

      <!-- Line Items -->
      <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 mb-6">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Line Items
          </h2>
          <button
            type="button"
            @click="addLineItem"
            class="inline-flex items-center px-3 py-2 text-sm font-medium text-blue-700 
                   bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 
                   dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-700 dark:hover:bg-blue-900/30"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            Add Item
          </button>
        </div>
        
        <div class="space-y-4">
          <InvoiceItemEditor
            v-for="(item, index) in invoice.line_items"
            :key="item.id"
            v-model="invoice.line_items[index]"
            @remove="removeLineItem(index)"
            :currency="invoice.currency"
          />
        </div>

        <!-- Add first item if none exist -->
        <div v-if="invoice.line_items.length === 0" class="text-center py-8">
          <div class="text-gray-400 mb-4">
            <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <p class="text-gray-600 dark:text-gray-400 mb-4">No items added yet</p>
          <button
            type="button"
            @click="addLineItem"
            class="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-700 
                   bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 
                   dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-700 dark:hover:bg-blue-900/30"
          >
            Add First Item
          </button>
        </div>

        <!-- Totals -->
        <div v-if="invoice.line_items.length > 0" class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-600">
          <div class="flex justify-end">
            <div class="w-80">
              <!-- Subtotal -->
              <div class="flex justify-between py-2">
                <span class="text-gray-600 dark:text-gray-400">Subtotal:</span>
                <span class="font-medium text-gray-900 dark:text-gray-100">
                  {{ formatCurrency(subtotal, invoice.currency) }}
                </span>
              </div>

              <!-- Discount -->
              <div class="flex justify-between items-center py-2">
                <div class="flex items-center space-x-2">
                  <span class="text-gray-600 dark:text-gray-400">Discount:</span>
                  <input
                    v-model.number="invoice.discount_rate"
                    type="number"
                    min="0"
                    max="100"
                    step="0.1"
                    class="w-16 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 
                           rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent
                           bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  />
                  <span class="text-gray-600 dark:text-gray-400">%</span>
                </div>
                <span class="font-medium text-red-600 dark:text-red-400">
                  -{{ formatCurrency(discountAmount, invoice.currency) }}
                </span>
              </div>

              <!-- Tax -->
              <div class="flex justify-between items-center py-2">
                <div class="flex items-center space-x-2">
                  <span class="text-gray-600 dark:text-gray-400">Tax:</span>
                  <input
                    v-model.number="invoice.tax_rate"
                    type="number"
                    min="0"
                    max="30"
                    step="0.1"
                    class="w-16 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 
                           rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent
                           bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  />
                  <span class="text-gray-600 dark:text-gray-400">%</span>
                </div>
                <span class="font-medium text-gray-900 dark:text-gray-100">
                  {{ formatCurrency(taxAmount, invoice.currency) }}
                </span>
              </div>

              <!-- Total -->
              <div class="flex justify-between py-3 border-t border-gray-200 dark:border-gray-600">
                <span class="text-lg font-semibold text-gray-900 dark:text-gray-100">Total:</span>
                <span class="text-xl font-bold text-blue-600 dark:text-blue-400">
                  {{ formatCurrency(total, invoice.currency) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Notes and Terms -->
      <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 mb-6">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Additional Information
        </h2>
        
        <div class="space-y-4">
          <!-- Notes -->
          <div>
            <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Notes
            </label>
            <textarea
              v-model="invoice.notes"
              id="notes"
              rows="3"
              class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 
                     rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent
                     bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              placeholder="Any additional notes for the client..."
            />
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex justify-between items-center">
        <NuxtLink
          to="/c/invoices"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 
                 rounded-lg hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-300 
                 dark:border-gray-600 dark:hover:bg-gray-700"
        >
          Cancel
        </NuxtLink>
        
        <div class="flex space-x-3">
          <button
            type="button"
            @click="saveDraft"
            :disabled="!isValid || saving"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 
                   rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed
                   dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700"
          >
            <span v-if="saving && draftMode">Saving Draft...</span>
            <span v-else>Save Draft</span>
          </button>
          
          <button
            type="submit"
            :disabled="!isValid || saving"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent 
                   rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed
                   focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            <span v-if="saving && !draftMode">Creating Invoice...</span>
            <span v-else>Create Invoice</span>
          </button>
        </div>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import type { IInvoice, ILineItem, ICRMClient } from '~/types/crm'
import { formatCurrency, calculateTotals } from '~/utils/calculations'
import ClientSelector from '~/components/invoices/ClientSelector.vue'
import InvoiceItemEditor from '~/components/invoices/InvoiceItemEditor.vue'

// Composables
const router = useRouter()
const { createInvoice, generateInvoiceNumber } = useInvoices()
const { showSuccess, showError } = useNotifications()

// State
const saving = ref(false)
const draftMode = ref(false)
const invoiceNumber = ref('')
const selectedClient = ref<ICRMClient | null>(null)

const invoice = ref<Partial<IInvoice>>({
  client_id: '',
  client_details: {
    name: '',
    email: '',
    address: '',
    phone: ''
  },
  issue_date: new Date().toISOString().split('T')[0],
  due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
  line_items: [],
  currency: 'USD',
  terms: 'Net 30',
  notes: '',
  discount_rate: 0,
  tax_rate: 0,
  status: 'draft'
})

// Computed
const today = computed(() => new Date().toISOString().split('T')[0])

const subtotal = computed(() => {
  return invoice.value.line_items?.reduce((sum, item) => sum + (item.amount || 0), 0) || 0
})

const discountAmount = computed(() => {
  return Math.round(subtotal.value * (invoice.value.discount_rate || 0) / 100 * 100) / 100
})

const taxAmount = computed(() => {
  const taxableAmount = subtotal.value - discountAmount.value
  return Math.round(taxableAmount * (invoice.value.tax_rate || 0) / 100 * 100) / 100
})

const total = computed(() => {
  return Math.round((subtotal.value - discountAmount.value + taxAmount.value) * 100) / 100
})

const isValid = computed(() => {
  return invoice.value.client_id &&
         invoice.value.issue_date &&
         invoice.value.due_date &&
         invoice.value.line_items &&
         invoice.value.line_items.length > 0 &&
         invoice.value.line_items.every(item => item.description && item.quantity > 0 && item.unit_price > 0)
})

// Methods
const generateNumber = async () => {
  try {
    invoiceNumber.value = await generateInvoiceNumber()
  } catch (error) {
    console.error('Error generating invoice number:', error)
    invoiceNumber.value = 'INV-' + Date.now()
  }
}

const onClientSelect = (client: ICRMClient) => {
  selectedClient.value = client
  invoice.value.client_details = {
    name: client.name,
    email: client.email || '',
    address: client.address || '',
    phone: client.phone || ''
  }
}

const addLineItem = () => {
  const newItem: ILineItem = {
    id: 'temp-' + Date.now(),
    description: '',
    quantity: 1,
    unit_price: 0,
    amount: 0
  }
  
  if (!invoice.value.line_items) {
    invoice.value.line_items = []
  }
  invoice.value.line_items.push(newItem)
}

const removeLineItem = (index: number) => {
  invoice.value.line_items?.splice(index, 1)
}

const getStatusClasses = (status: string): string => {
  const classes = {
    draft: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
    sent: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    partial: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    paid: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const saveDraft = async () => {
  draftMode.value = true
  await saveInvoice()
  draftMode.value = false
}

const saveInvoice = async () => {
  if (!isValid.value) return

  try {
    saving.value = true

    // Calculate totals
    const calculatedTotals = calculateTotals(
      invoice.value.line_items || [],
      invoice.value.tax_rate || 0,
      discountAmount.value
    )

    // Prepare invoice data
    const invoiceData: Omit<IInvoice, 'id' | 'created_at' | 'updated_at'> = {
      invoice_number: invoiceNumber.value,
      client_id: invoice.value.client_id!,
      client_details: invoice.value.client_details!,
      issue_date: new Date(invoice.value.issue_date!),
      due_date: new Date(invoice.value.due_date!),
      line_items: invoice.value.line_items!,
      subtotal: calculatedTotals.subtotal,
      discount_rate: invoice.value.discount_rate || 0,
      discount_amount: discountAmount.value,
      tax_rate: invoice.value.tax_rate || 0,
      tax_amount: calculatedTotals.tax,
      total: calculatedTotals.total,
      currency: invoice.value.currency || 'USD',
      terms: invoice.value.terms || 'Net 30',
      notes: invoice.value.notes || '',
      status: draftMode.value ? 'draft' : 'sent',
      payment_status: {
        amount_paid: 0,
        amount_due: calculatedTotals.total
      },
      created_by: 'user'
    }

    const newInvoice = await createInvoice(invoiceData)
    
    if (newInvoice) {
      showSuccess(`Invoice ${draftMode.value ? 'draft saved' : 'created'} successfully!`)
      await router.push(`/c/invoices/${newInvoice.id}`)
    }
  } catch (error) {
    console.error('Error saving invoice:', error)
    showError(`Failed to ${draftMode.value ? 'save draft' : 'create invoice'}. Please try again.`)
  } finally {
    saving.value = false
  }
}

// Watch for line item changes to recalculate amounts
watch(() => invoice.value.line_items, (items) => {
  items?.forEach(item => {
    item.amount = Math.round((item.quantity || 0) * (item.unit_price || 0) * 100) / 100
  })
}, { deep: true })

// Lifecycle
onMounted(() => {
  generateNumber()
  // Add first line item by default
  addLineItem()
})
</script>