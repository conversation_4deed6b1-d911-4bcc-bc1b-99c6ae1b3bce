<template>
  <div class="space-y-4">
    <div>
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        Select Client *
      </label>
      
      <!-- Search input -->
      <div class="relative">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="Search clients or type to create new..."
          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 
                 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent
                 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
          @focus="showDropdown = true"
          @blur="handleBlur"
        />
        
        <!-- Search icon -->
        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
          <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
      </div>

      <!-- Dropdown -->
      <div 
        v-if="showDropdown" 
        class="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 
               border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-60 overflow-y-auto"
      >
        <!-- Loading -->
        <div v-if="loading" class="p-4 text-center text-gray-500">
          <div class="animate-spin inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full"></div>
          <span class="ml-2">Loading clients...</span>
        </div>

        <!-- Create new client option -->
        <div 
          v-else-if="searchQuery && !filteredClients.some(c => c.name.toLowerCase() === searchQuery.toLowerCase())"
          class="p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-200 dark:border-gray-600"
          @mousedown.prevent="createNewClient"
        >
          <div class="flex items-center">
            <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mr-3">
              <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
            </div>
            <div>
              <div class="font-medium text-gray-900 dark:text-gray-100">
                Create "{{ searchQuery }}"
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400">
                Add as new client
              </div>
            </div>
          </div>
        </div>

        <!-- Existing clients -->
        <div 
          v-for="client in filteredClients" 
          :key="client.id"
          class="p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
          @mousedown.prevent="selectClient(client)"
        >
          <div class="flex items-center">
            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mr-3">
              <span class="text-sm font-medium text-blue-600 dark:text-blue-400">
                {{ client.name.charAt(0).toUpperCase() }}
              </span>
            </div>
            <div>
              <div class="font-medium text-gray-900 dark:text-gray-100">
                {{ client.name }}
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400">
                {{ client.email }}
                <span v-if="client.company" class="ml-2">• {{ client.company }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- No results -->
        <div 
          v-if="!loading && filteredClients.length === 0 && !searchQuery"
          class="p-4 text-center text-gray-500 dark:text-gray-400"
        >
          No clients found. Start typing to search or create a new one.
        </div>
      </div>
    </div>

    <!-- Selected client display -->
    <div 
      v-if="selectedClient" 
      class="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg"
    >
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mr-3">
            <span class="text-lg font-medium text-blue-600 dark:text-blue-400">
              {{ selectedClient.name.charAt(0).toUpperCase() }}
            </span>
          </div>
          <div>
            <div class="font-medium text-gray-900 dark:text-gray-100">
              {{ selectedClient.name }}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {{ selectedClient.email }}
              <span v-if="selectedClient.company" class="ml-2">• {{ selectedClient.company }}</span>
            </div>
            <div v-if="selectedClient.phone" class="text-sm text-gray-600 dark:text-gray-400">
              📞 {{ selectedClient.phone }}
            </div>
          </div>
        </div>
        <button
          @click="clearSelection"
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Validation error -->
    <div v-if="error" class="text-sm text-red-600 dark:text-red-400">
      {{ error }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue'
import type { ICRMClient } from '~/types/crm'

interface Props {
  modelValue?: string | null
  error?: string
}

interface Emits {
  (e: 'update:modelValue', value: string | null): void
  (e: 'clientSelected', client: ICRMClient): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const { clients, isLoading: loading, getClients, createClient } = useCRM()

const searchQuery = ref('')
const showDropdown = ref(false)
const selectedClient = ref<ICRMClient | null>(null)

// Load clients on mount
onMounted(() => {
  getClients()
})

// Watch for prop changes
watch(() => props.modelValue, (newValue) => {
  if (newValue && clients.value.length > 0) {
    const client = clients.value.find(c => c.id === newValue)
    if (client) {
      selectedClient.value = client
      searchQuery.value = client.name
    }
  } else if (!newValue) {
    selectedClient.value = null
    searchQuery.value = ''
  }
}, { immediate: true })

// Filtered clients based on search
const filteredClients = computed(() => {
  if (!searchQuery.value) return clients.value.slice(0, 10) // Show first 10 by default
  
  const query = searchQuery.value.toLowerCase()
  return clients.value.filter(client => 
    client.name.toLowerCase().includes(query) ||
    client.email.toLowerCase().includes(query) ||
    client.company?.toLowerCase().includes(query) ||
    client.phone?.includes(query)
  ).slice(0, 10) // Limit results
})

// Select existing client
const selectClient = (client: ICRMClient) => {
  selectedClient.value = client
  searchQuery.value = client.name
  showDropdown.value = false
  
  emit('update:modelValue', client.id)
  emit('clientSelected', client)
}

// Create new client
const createNewClient = async () => {
  try {
    const newClientData = {
      name: searchQuery.value,
      email: '', // Will be filled later in invoice form
      phone: '',
      company: '',
      address: '',
      tags: [],
      status: 'active' as const,
      source: 'invoice' as const,
      last_contact: new Date(),
      next_followup: null
    }
    
    const newClient = await createClient(newClientData)
    selectClient(newClient)
  } catch (error) {
    console.error('Error creating client:', error)
    // For now, create a temporary client if creation fails
    const tempClient: ICRMClient = {
      id: 'temp_' + Date.now(),
      user_id: 'current_user',
      name: searchQuery.value,
      email: '',
      phone: '',
      company: '',
      address: '',
      tags: [],
      status: 'active',
      source: 'manual',
      last_contact: new Date(),
      next_followup: null,
      created_at: { toDate: () => new Date() } as any,
      updated_at: { toDate: () => new Date() } as any
    }
    selectClient(tempClient)
  }
  
  showDropdown.value = false
}

// Clear selection
const clearSelection = () => {
  selectedClient.value = null
  searchQuery.value = ''
  emit('update:modelValue', null)
}

// Handle input blur
const handleBlur = () => {
  // Delay hiding dropdown to allow for click events
  setTimeout(() => {
    showDropdown.value = false
  }, 150)
}

// Expose selected client for parent access
defineExpose({
  selectedClient: computed(() => selectedClient.value),
  clearSelection
})
</script>