<template>
  <div class="space-y-4">
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
        Line Items
      </h3>
      <button
        @click="addItem"
        class="inline-flex items-center px-3 py-2 text-sm font-medium text-blue-600 
               bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 
               dark:bg-blue-900/20 dark:border-blue-700 dark:text-blue-400 
               dark:hover:bg-blue-900/30 transition-colors"
      >
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
        </svg>
        Add Item
      </button>
    </div>

    <!-- Line items table -->
    <div class="overflow-x-auto">
      <table class="w-full border border-gray-200 dark:border-gray-700 rounded-lg">
        <thead>
          <tr class="bg-gray-50 dark:bg-gray-700">
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Description
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-20">
              Qty
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-24">
              Unit
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-32">
              Rate
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-32">
              Amount
            </th>
            <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-16">
              Actions
            </th>
          </tr>
        </thead>
        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
          <tr 
            v-for="(item, index) in items" 
            :key="item.id || index"
            class="hover:bg-gray-50 dark:hover:bg-gray-700/50"
          >
            <!-- Description -->
            <td class="px-4 py-3">
              <textarea
                v-model="item.description"
                placeholder="Item description..."
                rows="2"
                class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 
                       rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent
                       bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
                       resize-none"
                @input="updateItem(index)"
              />
              <div v-if="getItemError(index, 'description')" class="text-xs text-red-600 dark:text-red-400 mt-1">
                {{ getItemError(index, 'description') }}
              </div>
            </td>

            <!-- Quantity -->
            <td class="px-4 py-3">
              <input
                v-model.number="item.quantity"
                type="number"
                min="0"
                step="0.01"
                placeholder="0"
                class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 
                       rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent
                       bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                @input="updateItem(index)"
              />
              <div v-if="getItemError(index, 'quantity')" class="text-xs text-red-600 dark:text-red-400 mt-1">
                {{ getItemError(index, 'quantity') }}
              </div>
            </td>

            <!-- Unit -->
            <td class="px-4 py-3">
              <select
                v-model="item.unit"
                class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 
                       rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent
                       bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                @change="updateItem(index)"
              >
                <option value="units">Units</option>
                <option value="hours">Hours</option>
                <option value="days">Days</option>
                <option value="weeks">Weeks</option>
                <option value="months">Months</option>
                <option value="items">Items</option>
                <option value="services">Services</option>
                <option value="licenses">Licenses</option>
              </select>
            </td>

            <!-- Rate -->
            <td class="px-4 py-3">
              <div class="relative">
                <span class="absolute left-3 top-2 text-sm text-gray-500 dark:text-gray-400">$</span>
                <input
                  v-model.number="item.unit_price"
                  type="number"
                  min="0"
                  step="0.01"
                  placeholder="0.00"
                  class="w-full pl-8 pr-3 py-2 text-sm border border-gray-300 dark:border-gray-600 
                         rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent
                         bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  @input="updateItem(index)"
                />
              </div>
              <div v-if="getItemError(index, 'unit_price')" class="text-xs text-red-600 dark:text-red-400 mt-1">
                {{ getItemError(index, 'unit_price') }}
              </div>
            </td>

            <!-- Amount -->
            <td class="px-4 py-3">
              <div class="flex items-center text-sm font-medium text-gray-900 dark:text-gray-100">
                ${{ formatAmount(calculateLineAmount(item)) }}
              </div>
            </td>

            <!-- Actions -->
            <td class="px-4 py-3 text-center">
              <button
                @click="removeItem(index)"
                class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                :disabled="items.length <= 1"
                :class="{ 'opacity-50 cursor-not-allowed': items.length <= 1 }"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Add item button for mobile -->
    <div class="md:hidden">
      <button
        @click="addItem"
        class="w-full inline-flex items-center justify-center px-4 py-2 text-sm font-medium 
               text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100
               dark:bg-blue-900/20 dark:border-blue-700 dark:text-blue-400 
               dark:hover:bg-blue-900/30 transition-colors"
      >
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
        </svg>
        Add Another Item
      </button>
    </div>

    <!-- Summary -->
    <div class="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
      <div class="text-right space-y-2">
        <div class="flex justify-between text-sm">
          <span class="text-gray-600 dark:text-gray-400">Items:</span>
          <span class="text-gray-900 dark:text-gray-100">{{ items.length }}</span>
        </div>
        <div class="flex justify-between text-sm">
          <span class="text-gray-600 dark:text-gray-400">Subtotal:</span>
          <span class="text-gray-900 dark:text-gray-100">${{ formatAmount(subtotal) }}</span>
        </div>
        <div v-if="hasErrors" class="text-xs text-red-600 dark:text-red-400">
          Please fix validation errors above
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, nextTick } from 'vue'
import type { ILineItem } from '~/types/crm'
import { calculateLineAmount, generateLineItemId, validateLineItem } from '~/utils/calculations'

interface Props {
  modelValue: Partial<ILineItem>[]
  errors?: Record<string, string>
}

interface Emits {
  (e: 'update:modelValue', value: Partial<ILineItem>[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const items = ref<Partial<ILineItem>[]>([])
const validationErrors = ref<Record<string, Record<string, string>>>({})

// Initialize items
const initializeItems = () => {
  if (props.modelValue && props.modelValue.length > 0) {
    items.value = [...props.modelValue]
  } else {
    // Start with one empty item
    items.value = [createEmptyItem()]
  }
}

// Create empty item
const createEmptyItem = (): Partial<ILineItem> => ({
  id: generateLineItemId(),
  description: '',
  quantity: 1,
  unit_price: 0,
  unit: 'units',
  amount: 0
})

// Watch for prop changes
watch(() => props.modelValue, (newValue) => {
  if (newValue && newValue.length > 0) {
    items.value = [...newValue]
  }
}, { immediate: true, deep: true })

// Initialize on mount
initializeItems()

// Calculate line amount
const calculateLineAmountLocal = (item: Partial<ILineItem>): number => {
  return calculateLineAmount(item)
}

// Format amount for display
const formatAmount = (amount: number): string => {
  return amount.toFixed(2)
}

// Add new item
const addItem = () => {
  items.value.push(createEmptyItem())
  updateParent()
}

// Remove item
const removeItem = (index: number) => {
  if (items.value.length > 1) {
    items.value.splice(index, 1)
    // Remove validation errors for this item
    delete validationErrors.value[index]
    updateParent()
  }
}

// Update item and recalculate
const updateItem = (index: number) => {
  const item = items.value[index]
  if (item) {
    // Calculate amount
    item.amount = calculateLineAmountLocal(item)
    
    // Validate item
    validateItem(index, item)
    
    updateParent()
  }
}

// Validate individual item
const validateItem = (index: number, item: Partial<ILineItem>) => {
  const errors: Record<string, string> = {}
  
  if (!item.description?.trim()) {
    errors.description = 'Description is required'
  }
  
  if (!item.quantity || item.quantity <= 0) {
    errors.quantity = 'Quantity must be greater than 0'
  }
  
  if (item.unit_price === undefined || item.unit_price < 0) {
    errors.unit_price = 'Rate must be 0 or greater'
  }
  
  if (Object.keys(errors).length > 0) {
    validationErrors.value[index] = errors
  } else {
    delete validationErrors.value[index]
  }
}

// Get validation error for specific item and field
const getItemError = (index: number, field: string): string => {
  return validationErrors.value[index]?.[field] || ''
}

// Update parent component
const updateParent = () => {
  emit('update:modelValue', [...items.value])
}

// Computed properties
const subtotal = computed(() => {
  return items.value.reduce((sum, item) => sum + (item.amount || 0), 0)
})

const hasErrors = computed(() => {
  return Object.keys(validationErrors.value).length > 0
})

// Validate all items
const validateAllItems = () => {
  items.value.forEach((item, index) => {
    validateItem(index, item)
  })
}

// Watch items for changes
watch(items, () => {
  validateAllItems()
}, { deep: true })

// Expose validation state
defineExpose({
  hasErrors,
  validateAllItems,
  subtotal
})
</script>