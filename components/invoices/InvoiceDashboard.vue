<template>
  <div class="invoice-dashboard">
    <!-- Dashboard Header -->
    <div class="dashboard-header bg-white border-b border-gray-200 px-6 py-4">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Invoice Management</h1>
          <p class="text-gray-600 mt-1">Manage your advertising invoices and billing</p>
        </div>
        
        <div class="header-actions flex items-center gap-3">
          <button
            @click="refreshInvoices"
            :disabled="isLoading"
            class="refresh-btn p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <Icon name="refresh-cw" class="w-5 h-5" :class="{ 'animate-spin': isLoading }" />
          </button>
          
          <button
            @click="exportInvoices"
            class="export-btn bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
          >
            <Icon name="download" class="w-4 h-4" />
            Export
          </button>
        </div>
      </div>
    </div>

    <!-- Summary Cards -->
    <div class="summary-section p-6 bg-gray-50">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="summary-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <Icon name="file-text" class="w-4 h-4 text-blue-600" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total Invoices</p>
              <p class="text-2xl font-bold text-gray-900">{{ summary.total_invoices || 0 }}</p>
            </div>
          </div>
        </div>

        <div class="summary-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <Icon name="dollar-sign" class="w-4 h-4 text-green-600" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total Amount</p>
              <p class="text-2xl font-bold text-gray-900">{{ formatCurrency(summary.total_amount || 0) }}</p>
            </div>
          </div>
        </div>

        <div class="summary-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <Icon name="check-circle" class="w-4 h-4 text-green-600" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Paid Invoices</p>
              <p class="text-2xl font-bold text-gray-900">{{ summary.paid_invoices || 0 }}</p>
            </div>
          </div>
        </div>

        <div class="summary-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                <Icon name="alert-circle" class="w-4 h-4 text-red-600" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Overdue</p>
              <p class="text-2xl font-bold text-gray-900">{{ summary.overdue_invoices || 0 }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters and Search -->
    <div class="filters-section bg-white border-b border-gray-200 px-6 py-4">
      <div class="flex items-center justify-between">
        <div class="filters flex items-center gap-4">
          <div class="filter-group">
            <label class="text-sm font-medium text-gray-700">Status</label>
            <select 
              v-model="filters.status"
              @change="applyFilters"
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="all">All Statuses</option>
              <option value="draft">Draft</option>
              <option value="sent">Sent</option>
              <option value="viewed">Viewed</option>
              <option value="paid">Paid</option>
              <option value="overdue">Overdue</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>

          <div class="filter-group">
            <label class="text-sm font-medium text-gray-700">Sort By</label>
            <select 
              v-model="filters.sortBy"
              @change="applyFilters"
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="created_at">Date Created</option>
              <option value="invoice_date">Invoice Date</option>
              <option value="due_date">Due Date</option>
              <option value="total_amount">Amount</option>
              <option value="invoice_number">Invoice Number</option>
            </select>
          </div>

          <div class="filter-group">
            <label class="text-sm font-medium text-gray-700">Order</label>
            <select 
              v-model="filters.sortOrder"
              @change="applyFilters"
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="desc">Newest First</option>
              <option value="asc">Oldest First</option>
            </select>
          </div>
        </div>

        <div class="search-box">
          <div class="relative">
            <input
              v-model="searchQuery"
              @input="debouncedSearch"
              type="text"
              placeholder="Search invoices..."
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Icon name="search" class="h-5 w-5 text-gray-400" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Invoices Table -->
    <div class="invoices-section flex-1 overflow-hidden">
      <!-- Loading State -->
      <div v-if="isLoading" class="loading-state flex items-center justify-center py-12">
        <div class="text-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p class="text-gray-600 mt-4">Loading invoices...</p>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="error-state p-6">
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
          <div class="flex items-center">
            <Icon name="alert-circle" class="w-5 h-5 text-red-500 mr-2" />
            <span class="text-red-700">{{ error }}</span>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else-if="invoices.length === 0" class="empty-state text-center py-12">
        <Icon name="file-text" class="w-16 h-16 text-gray-300 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">No Invoices Found</h3>
        <p class="text-gray-600 mb-6">
          {{ searchQuery ? 'No invoices match your search criteria.' : 'You don\'t have any invoices yet.' }}
        </p>
        <button 
          v-if="!searchQuery"
          @click="navigateToAdvertising"
          class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
        >
          Start Advertising
        </button>
      </div>

      <!-- Invoices Table -->
      <div v-else class="invoices-table">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Invoice
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <!-- TODO: Implement InvoiceTableRow component -->
              <!-- <InvoiceTableRow
                v-for="invoice in invoices"
                :key="invoice.id"
                :invoice="invoice"
                @view="viewInvoice"
                @download="downloadInvoice"
                @regenerate-pdf="regeneratePDF"
              /> -->
              
              <!-- Temporary inline invoice rows -->
              <tr v-for="invoice in invoices" :key="invoice.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">{{ invoice.invoice_number }}</div>
                  <div class="text-sm text-gray-500">{{ invoice.subscription_name }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ formatDate(invoice.invoice_date) }}</div>
                  <div class="text-sm text-gray-500">Due: {{ formatDate(invoice.due_date) }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">{{ formatCurrency(invoice.total_amount) }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="[
                    'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                    getStatusColor(invoice.status)
                  ]">
                    {{ getStatusLabel(invoice.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button @click="viewInvoice(invoice)" class="text-blue-600 hover:text-blue-900 mr-3">View</button>
                  <button @click="downloadInvoice(invoice)" class="text-green-600 hover:text-green-900">Download</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div v-if="pagination.total > pagination.limit" class="pagination bg-white px-6 py-4 border-t border-gray-200">
          <div class="flex items-center justify-between">
            <div class="text-sm text-gray-700">
              Showing {{ pagination.offset + 1 }} to {{ Math.min(pagination.offset + pagination.limit, pagination.total) }} 
              of {{ pagination.total }} results
            </div>
            
            <div class="flex items-center gap-2">
              <button
                @click="previousPage"
                :disabled="pagination.offset === 0"
                class="pagination-btn px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              
              <button
                @click="nextPage"
                :disabled="!pagination.has_more"
                class="pagination-btn px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Invoice Detail Modal -->
    <!-- TODO: Implement InvoiceDetailModal component -->
    <!-- <InvoiceDetailModal
      v-if="selectedInvoice"
      :invoice="selectedInvoice"
      @close="selectedInvoice = null"
      @regenerate-pdf="regeneratePDF"
    /> -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { Icon } from '#components'
import type { AdInvoice } from '~/types/invoicing'

// Simple debounce implementation since lodash-es is not available
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// Import child components
// TODO: Create InvoiceTableRow component
// import InvoiceTableRow from './InvoiceTableRow.vue'
// TODO: Create InvoiceDetailModal component  
// import InvoiceDetailModal from './InvoiceDetailModal.vue'

// Use the automated invoicing composable
const {
  isLoading,
  error,
  invoices,
  getUserInvoices,
  calculateInvoiceMetrics,
  formatCurrency
} = useAutomatedInvoicing()

// Local state
const selectedInvoice = ref<AdInvoice | null>(null)
const searchQuery = ref('')
const summary = ref({
  total_invoices: 0,
  total_amount: 0,
  paid_invoices: 0,
  paid_amount: 0,
  overdue_invoices: 0,
  overdue_amount: 0
})

const filters = ref({
  status: 'all',
  sortBy: 'created_at',
  sortOrder: 'desc'
})

const pagination = ref({
  total: 0,
  limit: 20,
  offset: 0,
  has_more: false
})

// Methods
const loadInvoices = async () => {
  try {
    const response = await $fetch('/api/invoices', {
      query: {
        status: filters.value.status,
        sortBy: filters.value.sortBy,
        sortOrder: filters.value.sortOrder,
        limit: pagination.value.limit,
        offset: pagination.value.offset,
        search: searchQuery.value
      }
    })

    if (response.success) {
      invoices.value = response.invoices
      summary.value = response.summary
      pagination.value = {
        ...pagination.value,
        ...response.pagination
      }
    }
  } catch (err: any) {
    error.value = err.message || 'Failed to load invoices'
  }
}

const refreshInvoices = async () => {
  pagination.value.offset = 0
  await loadInvoices()
}

const applyFilters = async () => {
  pagination.value.offset = 0
  await loadInvoices()
}

const debouncedSearch = debounce(async () => {
  pagination.value.offset = 0
  await loadInvoices()
}, 300)

const previousPage = async () => {
  if (pagination.value.offset > 0) {
    pagination.value.offset = Math.max(0, pagination.value.offset - pagination.value.limit)
    await loadInvoices()
  }
}

const nextPage = async () => {
  if (pagination.value.has_more) {
    pagination.value.offset += pagination.value.limit
    await loadInvoices()
  }
}

const viewInvoice = (invoice: AdInvoice) => {
  selectedInvoice.value = invoice
}

const downloadInvoice = async (invoice: AdInvoice) => {
  if (invoice.pdf_url) {
    window.open(invoice.pdf_url, '_blank')
  } else {
    // Generate PDF if not available
    await regeneratePDF(invoice)
  }
}

const regeneratePDF = async (invoice: AdInvoice) => {
  try {
    const response = await $fetch(`/api/invoices/${invoice.id}/regenerate-pdf`, {
      method: 'POST'
    })

    if (response.success) {
      // Update the invoice with new PDF URL
      const invoiceIndex = invoices.value.findIndex(inv => inv.id === invoice.id)
      if (invoiceIndex !== -1) {
        invoices.value[invoiceIndex].pdf_url = response.pdf_url
      }

      // Open the new PDF
      window.open(response.pdf_url, '_blank')
    }
  } catch (err: any) {
    error.value = err.message || 'Failed to regenerate PDF'
  }
}

const exportInvoices = async () => {
  try {
    const response = await $fetch('/api/invoices/export', {
      method: 'POST',
      body: {
        format: 'csv',
        filters: filters.value
      }
    })

    if (response.success && response.download_url) {
      window.open(response.download_url, '_blank')
    }
  } catch (err: any) {
    error.value = err.message || 'Failed to export invoices'
  }
}

const navigateToAdvertising = () => {
  navigateTo('/c/advertise')
}

// Utility functions for the temporary inline table
const formatDate = (date: Date | string): string => {
  if (!date) return '-'
  const d = typeof date === 'string' ? new Date(date) : date
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(d)
}

const getStatusColor = (status: string): string => {
  const colors = {
    draft: 'bg-gray-100 text-gray-800',
    sent: 'bg-blue-100 text-blue-800',
    viewed: 'bg-yellow-100 text-yellow-800',
    paid: 'bg-green-100 text-green-800',
    overdue: 'bg-red-100 text-red-800',
    cancelled: 'bg-gray-100 text-gray-800'
  }
  return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'
}

const getStatusLabel = (status: string): string => {
  const labels = {
    draft: 'Draft',
    sent: 'Sent',
    viewed: 'Viewed',
    paid: 'Paid',
    overdue: 'Overdue',
    cancelled: 'Cancelled'
  }
  return labels[status as keyof typeof labels] || status
}

// Lifecycle
onMounted(() => {
  loadInvoices()
})
</script>

<style scoped>
.invoice-dashboard {
  @apply flex flex-col h-full bg-gray-50;
}

.dashboard-header {
  @apply bg-white border-b border-gray-200 px-6 py-4;
}

.summary-section {
  @apply p-6 bg-gray-50;
}

.summary-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.filters-section {
  @apply bg-white border-b border-gray-200 px-6 py-4;
}

.filter-group {
  @apply flex flex-col;
}

.invoices-section {
  @apply flex-1 overflow-hidden;
}

.invoices-table {
  @apply h-full overflow-auto;
}

.pagination {
  @apply bg-white px-6 py-4 border-t border-gray-200;
}

.pagination-btn {
  @apply px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .filters {
    @apply flex-col gap-2;
  }
  
  .filter-group {
    @apply w-full;
  }
  
  .search-box {
    @apply w-full mt-4;
  }
}
</style>
