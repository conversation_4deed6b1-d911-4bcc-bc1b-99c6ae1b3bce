<template>
  <div class="bg-white p-8 max-w-4xl mx-auto" id="invoice-preview">
    <!-- Header -->
    <div class="flex justify-between items-start mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 mb-2">INVOICE</h1>
        <div class="text-lg font-semibold text-gray-700">{{ invoice.invoice_number }}</div>
      </div>
      
      <!-- Company Logo/Info -->
      <div class="text-right">
        <div class="text-2xl font-bold text-blue-600 mb-2">Covalonic</div>
        <div class="text-sm text-gray-600">
          Business Solutions Platform<br>
          <EMAIL><br>
          www.covalonic.com
        </div>
      </div>
    </div>

    <!-- Invoice Info & Client Details -->
    <div class="grid grid-cols-2 gap-8 mb-8">
      <!-- Bill To -->
      <div>
        <h3 class="text-lg font-semibold text-gray-900 mb-3">Bill To:</h3>
        <div class="text-gray-700">
          <div class="font-medium text-lg">{{ invoice.client_details?.name || 'No client selected' }}</div>
          <div v-if="invoice.client_details?.company" class="mt-1">{{ invoice.client_details.company }}</div>
          <div v-if="invoice.client_details?.email" class="mt-1">{{ invoice.client_details.email }}</div>
          <div v-if="invoice.client_details?.phone" class="mt-1">{{ invoice.client_details.phone }}</div>
          <div v-if="invoice.client_details?.address" class="mt-2 whitespace-pre-line">{{ invoice.client_details.address }}</div>
        </div>
      </div>

      <!-- Invoice Details -->
      <div>
        <div class="space-y-2">
          <div class="flex justify-between">
            <span class="font-medium text-gray-700">Issue Date:</span>
            <span class="text-gray-900">{{ formatDate(invoice.issue_date) }}</span>
          </div>
          <div class="flex justify-between">
            <span class="font-medium text-gray-700">Due Date:</span>
            <span class="text-gray-900">{{ formatDate(invoice.due_date) }}</span>
          </div>
          <div class="flex justify-between">
            <span class="font-medium text-gray-700">Terms:</span>
            <span class="text-gray-900">{{ invoice.terms }}</span>
          </div>
          <div class="flex justify-between">
            <span class="font-medium text-gray-700">Status:</span>
            <span class="px-2 py-1 text-xs font-semibold rounded-full"
                  :class="getStatusClasses(invoice.status)">
              {{ invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1) }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Line Items Table -->
    <div class="mb-8">
      <table class="w-full border-collapse">
        <thead>
          <tr class="border-b-2 border-gray-300">
            <th class="text-left py-3 font-semibold text-gray-900">Description</th>
            <th class="text-center py-3 font-semibold text-gray-900 w-20">Qty</th>
            <th class="text-right py-3 font-semibold text-gray-900 w-24">Rate</th>
            <th class="text-right py-3 font-semibold text-gray-900 w-24">Amount</th>
          </tr>
        </thead>
        <tbody>
          <tr 
            v-for="item in invoice.line_items" 
            :key="item.id"
            class="border-b border-gray-200"
          >
            <td class="py-3 text-gray-700">{{ item.description }}</td>
            <td class="py-3 text-center text-gray-700">{{ item.quantity }}</td>
            <td class="py-3 text-right text-gray-700">{{ formatCurrency(item.unit_price, invoice.currency) }}</td>
            <td class="py-3 text-right font-medium text-gray-900">{{ formatCurrency(item.amount, invoice.currency) }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Totals -->
    <div class="flex justify-end mb-8">
      <div class="w-80">
        <div class="space-y-2">
          <!-- Subtotal -->
          <div class="flex justify-between py-2">
            <span class="text-gray-700">Subtotal:</span>
            <span class="font-medium text-gray-900">{{ formatCurrency(invoice.subtotal, invoice.currency) }}</span>
          </div>
          
          <!-- Discount -->
          <div v-if="invoice.discount_rate > 0" class="flex justify-between py-2">
            <span class="text-gray-700">Discount ({{ invoice.discount_rate }}%):</span>
            <span class="font-medium text-red-600">-{{ formatCurrency(invoice.discount_amount, invoice.currency) }}</span>
          </div>
          
          <!-- Tax -->
          <div v-if="invoice.tax_rate > 0" class="flex justify-between py-2">
            <span class="text-gray-700">Tax ({{ invoice.tax_rate }}%):</span>
            <span class="font-medium text-gray-900">{{ formatCurrency(invoice.tax_amount, invoice.currency) }}</span>
          </div>
          
          <!-- Total -->
          <div class="flex justify-between py-3 border-t-2 border-gray-300">
            <span class="text-xl font-bold text-gray-900">Total:</span>
            <span class="text-xl font-bold text-blue-600">{{ formatCurrency(invoice.total, invoice.currency) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Payment Information -->
    <div v-if="invoice.payment_status.amount_paid > 0" class="mb-8 p-4 bg-green-50 border border-green-200 rounded-lg">
      <h3 class="font-semibold text-green-800 mb-2">Payment Information</h3>
      <div class="grid grid-cols-2 gap-4 text-sm">
        <div>
          <span class="text-green-700">Amount Paid:</span>
          <span class="font-medium text-green-900 ml-2">{{ formatCurrency(invoice.payment_status.amount_paid, invoice.currency) }}</span>
        </div>
        <div>
          <span class="text-green-700">Amount Due:</span>
          <span class="font-medium text-green-900 ml-2">{{ formatCurrency(amountDue, invoice.currency) }}</span>
        </div>
        <div v-if="invoice.payment_status.last_payment_date" class="col-span-2">
          <span class="text-green-700">Last Payment:</span>
          <span class="font-medium text-green-900 ml-2">{{ formatDate(invoice.payment_status.last_payment_date) }}</span>
        </div>
      </div>
    </div>

    <!-- Notes -->
    <div v-if="invoice.notes" class="mb-8">
      <h3 class="font-semibold text-gray-900 mb-2">Notes:</h3>
      <p class="text-gray-700 whitespace-pre-wrap">{{ invoice.notes }}</p>
    </div>

    <!-- Footer -->
    <div class="text-center text-sm text-gray-500 border-t border-gray-200 pt-4">
      Thank you for your business!<br>
      This invoice was generated on {{ new Date().toLocaleDateString() }}
    </div>

    <!-- Action Buttons (only show when not printing) -->
    <div class="mt-8 flex justify-center space-x-4 print:hidden">
      <button
        @click="downloadPDF"
        class="px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent 
               rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
      >
        📄 Download PDF
      </button>
      
      <button
        @click="printInvoice"
        class="px-6 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 
               rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
      >
        🖨️ Print
      </button>
      
      <button
        v-if="onSend"
        @click="onSend"
        class="px-6 py-2 text-sm font-medium text-white bg-green-600 border border-transparent 
               rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
      >
        📧 Send Invoice
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { IInvoice } from '~/types/crm'
import { formatCurrency } from '~/utils/calculations'

// Props
interface Props {
  invoice: IInvoice
  onSend?: () => void
}

const props = defineProps<Props>()

// Computed
const amountDue = computed(() => {
  return props.invoice.payment_status.amount_due || 
         (props.invoice.total - props.invoice.payment_status.amount_paid)
})

// Methods
const formatDate = (date: Date | { toDate: () => Date } | null | undefined): string => {
  if (!date) return 'Not set'
  const d = date instanceof Date ? date : date.toDate()
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const getStatusClasses = (status: string): string => {
  const classes = {
    draft: 'bg-gray-100 text-gray-800',
    sent: 'bg-blue-100 text-blue-800',
    partial: 'bg-yellow-100 text-yellow-800',
    paid: 'bg-green-100 text-green-800',
    overdue: 'bg-red-100 text-red-800',
    cancelled: 'bg-gray-100 text-gray-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const printInvoice = () => {
  window.print()
}

const downloadPDF = async () => {
  // This would integrate with a PDF generation service or library
  try {
    // For now, we'll use the browser's print to PDF functionality
    const printWindow = window.open('', '_blank')
    if (printWindow) {
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Invoice ${props.invoice.invoice_number}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
              .print-content { max-width: 800px; margin: 0 auto; }
              /* Add print-specific styles */
              @media print {
                body { margin: 0; }
                .no-print { display: none; }
              }
            </style>
          </head>
          <body>
            <div class="print-content">
              ${document.getElementById('invoice-preview')?.innerHTML}
            </div>
          </body>
        </html>
      `)
      printWindow.document.close()
      printWindow.print()
    }
  } catch (error) {
    console.error('Error generating PDF:', error)
  }
}
</script>

<style scoped>
@media print {
  .print\:hidden {
    display: none !important;
  }
}
</style>