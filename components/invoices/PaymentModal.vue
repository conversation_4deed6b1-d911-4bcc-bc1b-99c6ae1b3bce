<template>
  <div 
    v-if="show" 
    class="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50 flex items-center justify-center p-4"
    @click.self="$emit('close')"
  >
    <div class="bg-white dark:bg-gray-900 rounded-lg max-w-md w-full">
      <div class="p-6">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Record Payment
          </h3>
          <button
            @click="$emit('close')"
            class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Invoice Summary -->
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mb-6">
          <div class="text-center">
            <div class="text-sm text-gray-600 dark:text-gray-400 mb-1">Invoice {{ invoice.invoice_number }}</div>
            <div class="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              {{ formatCurrency(invoice.total, invoice.currency) }}
            </div>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <div class="text-gray-600 dark:text-gray-400">Amount Paid</div>
                <div class="font-medium text-green-600 dark:text-green-400">
                  {{ formatCurrency(invoice.payment_status.amount_paid, invoice.currency) }}
                </div>
              </div>
              <div>
                <div class="text-gray-600 dark:text-gray-400">Amount Due</div>
                <div class="font-medium text-gray-900 dark:text-gray-100">
                  {{ formatCurrency(amountDue, invoice.currency) }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Payment Form -->
        <form @submit.prevent="submitPayment">
          <div class="space-y-4">
            <!-- Payment Amount -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Payment Amount *
              </label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span class="text-gray-500 text-sm">{{ getCurrencySymbol(invoice.currency) }}</span>
                </div>
                <input
                  v-model.number="payment.amount"
                  type="number"
                  step="0.01"
                  :max="amountDue"
                  min="0.01"
                  required
                  class="block w-full pl-8 pr-3 py-2 border border-gray-300 dark:border-gray-600 
                         rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent
                         bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  placeholder="0.00"
                />
              </div>
              <div class="mt-1 flex justify-between text-xs text-gray-500">
                <button
                  type="button"
                  @click="payment.amount = amountDue"
                  class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  Pay full amount
                </button>
                <span>Maximum: {{ formatCurrency(amountDue, invoice.currency) }}</span>
              </div>
            </div>

            <!-- Payment Date -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Payment Date *
              </label>
              <input
                v-model="payment.date"
                type="date"
                :max="today"
                required
                class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 
                       rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent
                       bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>

            <!-- Payment Method -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Payment Method *
              </label>
              <select
                v-model="payment.method"
                required
                class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 
                       rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent
                       bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                <option value="">Select payment method</option>
                <option value="cash">Cash</option>
                <option value="check">Check</option>
                <option value="bank_transfer">Bank Transfer</option>
                <option value="credit_card">Credit Card</option>
                <option value="paypal">PayPal</option>
                <option value="stripe">Stripe</option>
                <option value="other">Other</option>
              </select>
            </div>

            <!-- Reference Number -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Reference Number
              </label>
              <input
                v-model="payment.reference"
                type="text"
                class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 
                       rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent
                       bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                placeholder="Check #, transaction ID, etc."
              />
            </div>

            <!-- Notes -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Notes
              </label>
              <textarea
                v-model="payment.notes"
                rows="2"
                class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 
                       rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent
                       bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                placeholder="Optional payment notes..."
              />
            </div>
          </div>

          <!-- Actions -->
          <div class="flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              @click="$emit('close')"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 
                     rounded-lg hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-300 
                     dark:border-gray-600 dark:hover:bg-gray-700"
            >
              Cancel
            </button>
            <button
              type="submit"
              :disabled="!isValid || processing"
              class="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent 
                     rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed
                     focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
            >
              <span v-if="processing">Recording...</span>
              <span v-else>Record Payment</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import type { IInvoice, IPaymentRecord } from '~/types/crm'
import { formatCurrency } from '~/utils/calculations'

// Props & Emits
interface Props {
  invoice: IInvoice
  show: boolean
}

const props = defineProps<Props>()

interface Emits {
  (e: 'close'): void
  (e: 'payment-recorded', payment: IPaymentRecord): void
}

const emit = defineEmits<Emits>()

// Composables
const { recordPayment } = useInvoices()
const { showError } = useNotifications()

// State
const processing = ref(false)
const payment = ref({
  amount: 0,
  date: new Date().toISOString().split('T')[0],
  method: '',
  reference: '',
  notes: ''
})

// Computed
const amountDue = computed(() => {
  return props.invoice.payment_status.amount_due || 
         (props.invoice.total - props.invoice.payment_status.amount_paid)
})

const today = computed(() => {
  return new Date().toISOString().split('T')[0]
})

const isValid = computed(() => {
  return payment.value.amount > 0 && 
         payment.value.amount <= amountDue.value &&
         payment.value.date &&
         payment.value.method
})

// Methods
const getCurrencySymbol = (currency: string): string => {
  const symbols: { [key: string]: string } = {
    USD: '$',
    EUR: '€',
    GBP: '£',
    CAD: 'C$',
    AUD: 'A$'
  }
  return symbols[currency] || '$'
}

const submitPayment = async () => {
  if (!isValid.value) return

  try {
    processing.value = true

    const paymentRecord: Omit<IPaymentRecord, 'id' | 'created_at'> = {
      invoice_id: props.invoice.id,
      amount: payment.value.amount,
      method: payment.value.method,
      reference: payment.value.reference || undefined,
      notes: payment.value.notes || undefined,
      payment_date: new Date(payment.value.date)
    }

    const success = await recordPayment(props.invoice.id, paymentRecord)
    
    if (success) {
      emit('payment-recorded', { ...paymentRecord, id: '', created_at: new Date() } as IPaymentRecord)
      resetForm()
    }
  } catch (error) {
    console.error('Error recording payment:', error)
    showError('Failed to record payment. Please try again.')
  } finally {
    processing.value = false
  }
}

const resetForm = () => {
  payment.value = {
    amount: 0,
    date: new Date().toISOString().split('T')[0],
    method: '',
    reference: '',
    notes: ''
  }
}

// Watch for modal show/hide to reset form
watch(() => props.show, (show) => {
  if (show) {
    resetForm()
    // Pre-fill with remaining amount
    payment.value.amount = amountDue.value
  }
})
</script>