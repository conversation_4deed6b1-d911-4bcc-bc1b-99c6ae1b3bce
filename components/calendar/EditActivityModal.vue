<template>
  <div
    v-if="open && activity"
    class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
    @click="handleClose"
  >
    <div
      class="bg-gradient-to-br from-gray-900/95 via-gray-800/95 to-gray-900/95 rounded-xl border border-white/10 p-6 max-w-md w-full shadow-2xl"
      @click.stop
    >
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-xl font-bold text-white">Edit Activity</h3>
        <button
          class="p-2 rounded-lg hover:bg-white/10 text-gray-400 hover:text-white transition-colors duration-200"
          @click="handleClose"
        >
          <Icon name="mdi:close" class="text-xl" />
        </button>
      </div>

      <form class="space-y-4" @submit.prevent="handleSubmit">
        <!-- Activity Type (Read Only) -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Activity Type
          </label>
          <div class="px-3 py-2 bg-gray-800/30 border border-white/10 rounded-lg text-gray-400 flex items-center">
            <Icon :name="getActivityIcon(form.type)" class="mr-2" />
            <span class="capitalize">{{ form.type }}</span>
          </div>
        </div>

        <!-- Contact Selection -->
        <div v-if="contacts && contacts.length > 0">
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Contact <span class="text-gray-500 text-sm">(optional)</span>
          </label>
          <select
            v-model="form.contactId"
            class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">No contact (personal activity)</option>
            <option v-for="contact in sortedContacts" :key="contact.id" :value="contact.id">
              {{ getFullName(contact) }}
              <span v-if="contact.businessName"> - {{ contact.businessName }}</span>
            </option>
          </select>
        </div>

        <!-- Title -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Title <span class="text-red-400">*</span>
          </label>
          <input
            v-model="form.title"
            type="text"
            required
            placeholder="e.g., Follow-up call about proposal"
            class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <!-- Description -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2"> Description </label>
          <textarea
            v-model="form.description"
            rows="3"
            placeholder="Add details about the activity..."
            class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
          />
        </div>

        <!-- Date and Time -->
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2"> Date </label>
            <input
              v-model="form.date"
              type="date"
              class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2"> Time </label>
            <input
              v-model="form.time"
              type="time"
              class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        <!-- Priority (for tasks) -->
        <div v-if="form.type === 'task'">
          <label class="block text-sm font-medium text-gray-300 mb-2"> Priority </label>
          <div class="flex space-x-2">
            <button
              v-for="priority in priorities"
              :key="priority.value"
              type="button"
              class="px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200"
              :class="[
                form.priority === priority.value
                  ? `bg-gradient-to-r ${priority.color} text-white`
                  : 'bg-gray-800/30 border border-white/10 text-gray-400 hover:border-white/20 hover:text-white',
              ]"
              @click="form.priority = priority.value"
            >
              {{ priority.label }}
            </button>
          </div>
        </div>

        <!-- Status (for tasks) -->
        <div v-if="form.type === 'task'">
          <label class="block text-sm font-medium text-gray-300 mb-2"> Status </label>
          <div class="flex space-x-2">
            <button
              v-for="status in statuses"
              :key="status.value"
              type="button"
              class="px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200"
              :class="[
                form.status === status.value
                  ? `bg-gradient-to-r ${status.color} text-white`
                  : 'bg-gray-800/30 border border-white/10 text-gray-400 hover:border-white/20 hover:text-white',
              ]"
              @click="form.status = status.value"
            >
              {{ status.label }}
            </button>
          </div>
        </div>

        <!-- Location (for meetings) -->
        <div v-if="form.type === 'meeting'">
          <label class="block text-sm font-medium text-gray-300 mb-2"> Location </label>
          <input
            v-model="form.location"
            type="text"
            placeholder="Meeting location"
            class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <!-- Actions -->
        <div class="flex justify-between pt-4">
          <button
            type="button"
            class="px-4 py-2 rounded-lg bg-red-600/20 hover:bg-red-600/30 text-red-400 hover:text-red-300 transition-colors duration-200 border border-red-500/30"
            @click="handleDelete"
          >
            Delete
          </button>
          <div class="flex space-x-3">
            <button
              type="button"
              class="px-4 py-2 rounded-lg bg-gray-700/50 hover:bg-gray-600/50 text-gray-300 hover:text-white transition-colors duration-200"
              @click="handleClose"
            >
              Cancel
            </button>
            <button
              type="submit"
              :disabled="!form.title || !form.type"
              class="px-4 py-2 rounded-lg bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Save Changes
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useActivities } from '~/composables/useActivities';
import { useContacts } from '~/composables/useContacts';
import { useCurrentUser } from '~/composables/useCurrentUser';

interface Props {
  open: boolean;
  activity: any;
  contacts: any[];
}

const props = defineProps<Props>();
const emit = defineEmits<{
  close: [];
  success: [];
  delete: [];
}>();

const { getFullName } = useContacts();
const { updateActivity, deleteActivity } = useActivities();
const { currentUser } = useCurrentUser();

// Priority levels
const priorities = [
  { value: 'low', label: 'Low', color: 'from-green-500 to-green-600' },
  { value: 'medium', label: 'Medium', color: 'from-yellow-500 to-yellow-600' },
  { value: 'high', label: 'High', color: 'from-red-500 to-red-600' },
];

// Status options
const statuses = [
  { value: 'pending', label: 'Pending', color: 'from-yellow-500 to-yellow-600' },
  { value: 'in-progress', label: 'In Progress', color: 'from-blue-500 to-blue-600' },
  { value: 'completed', label: 'Completed', color: 'from-green-500 to-green-600' },
  { value: 'cancelled', label: 'Cancelled', color: 'from-gray-500 to-gray-600' },
];

// Form state
const form = ref({
  type: '',
  contactId: '',
  title: '',
  description: '',
  date: '',
  time: '',
  priority: 'medium',
  status: 'pending',
  location: '',
});

// Format date for input
const formatDateForInput = (date: Date | string | null): string => {
  if (!date) return '';
  const d = new Date(date);
  return d.toISOString().split('T')[0];
};

// Format time for input
const formatTimeForInput = (date: Date | string | null): string => {
  if (!date) return '';
  const d = new Date(date);
  return d.toTimeString().slice(0, 5);
};

// Sorted contacts
const sortedContacts = computed(() => {
  return [...props.contacts].sort((a, b) => {
    const nameA = getFullName(a).toLowerCase();
    const nameB = getFullName(b).toLowerCase();
    return nameA.localeCompare(nameB);
  });
});

// Contact is now optional for all activity types
// const requiresContact = computed(() => {
//   return !['note', 'task'].includes(form.value.type);
// });

// Get activity icon
const getActivityIcon = (type: string) => {
  switch (type) {
    case 'call':
      return 'mdi:phone';
    case 'email':
      return 'mdi:email';
    case 'meeting':
      return 'mdi:account-group';
    case 'task':
      return 'mdi:check-circle';
    case 'note':
      return 'mdi:note-text';
    case 'message':
      return 'mdi:message-text';
    default:
      return 'mdi:calendar';
  }
};

// Watch for activity changes and populate form
watch(
  () => props.activity,
  (newActivity) => {
    if (newActivity && newActivity.data) {
      const activityData = newActivity.data;
      
      // Determine the appropriate date field based on activity type
      let dateToUse = newActivity.date || activityData.createdAt;
      if (activityData.type === 'task' && activityData.dueDate) {
        dateToUse = activityData.dueDate;
      } else if (activityData.type === 'meeting' && activityData.startTime) {
        dateToUse = activityData.startTime;
      } else if (activityData.scheduledDate) {
        dateToUse = activityData.scheduledDate;
      }
      
      form.value = {
        type: activityData.type || newActivity.type || '',
        title: activityData.title || newActivity.title || '',
        description: activityData.description || newActivity.description || '',
        date: formatDateForInput(dateToUse),
        time: formatTimeForInput(dateToUse),
        priority: activityData.priority || 'medium',
        status: activityData.status || 'pending',
        location: activityData.location || '',
        contactId: '',
      };
      
      // Extract contact ID from associatedEntities if available
      if (activityData.associatedEntities && activityData.associatedEntities.length > 0) {
        const contactEntity = activityData.associatedEntities.find((e: any) => e.entityType === 'contact');
        if (contactEntity) {
          form.value.contactId = contactEntity.entityId;
        }
      }
    }
  },
  { immediate: true }
);

// Handle form submission
const handleSubmit = async () => {
  try {
    if (!props.activity?.data?.id && !props.activity?.id) {
      throw new Error('No activity ID found');
    }

    const activityId = props.activity.data?.id || props.activity.id;

    // Prepare activity data
    const activityDate = form.value.date ? new Date(form.value.date) : new Date();
    if (form.value.time) {
      const [hours, minutes] = form.value.time.split(':').map(Number);
      activityDate.setHours(hours, minutes, 0, 0);
    }

    const updateData: any = {
      title: form.value.title,
      description: form.value.description,
      associatedEntities: form.value.contactId ? [
        {
          entityType: 'contact' as const,
          entityId: form.value.contactId,
        },
      ] : [],
    };

    // Add type-specific date fields
    if (form.value.type === 'task') {
      updateData.dueDate = activityDate;
      updateData.priority = form.value.priority;
      updateData.status = form.value.status;
    } else if (form.value.type === 'meeting') {
      updateData.startTime = activityDate;
      updateData.endTime = new Date(activityDate.getTime() + 60 * 60 * 1000); // 1 hour default
      updateData.location = form.value.location;
    } else if (form.value.type === 'call') {
      // For calls, we'll add a scheduledDate field
      updateData.scheduledDate = activityDate;
    } else if (form.value.type === 'email') {
      // For emails, we'll add a scheduledDate field
      updateData.scheduledDate = activityDate;
    } else if (form.value.type === 'note') {
      // For notes, we'll add a scheduledDate field
      updateData.scheduledDate = activityDate;
    } else if (form.value.type === 'message') {
      // For messages, we'll add a scheduledDate field
      updateData.scheduledDate = activityDate;
    }

    await updateActivity(activityId, updateData);
    emit('success');
    handleClose();
  } catch (error) {
    console.error('Error updating activity:', error);
  }
};

// Handle delete
const handleDelete = async () => {
  if (confirm('Are you sure you want to delete this activity?')) {
    try {
      const activityId = props.activity.data?.id || props.activity.id;
      await deleteActivity(activityId);
      emit('delete');
      handleClose();
    } catch (error) {
      console.error('Error deleting activity:', error);
    }
  }
};

// Handle close
const handleClose = () => {
  emit('close');
};
</script>