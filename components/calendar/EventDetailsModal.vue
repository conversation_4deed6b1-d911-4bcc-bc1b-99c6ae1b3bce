<script setup lang="ts">
import { computed, defineProps, defineEmits } from 'vue';
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue';

const props = defineProps<{
  open: boolean;
  event: any;
}>();

const emit = defineEmits<{
  close: [];
  edit: [];
}>();

// Format date for display
const formatDate = (date: Date | null) => {
  if (!date) return '';
  return new Date(date).toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

const formatTime = (date: Date | null) => {
  if (!date) return '';
  return new Date(date).toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
  });
};

// Get theme color classes
const themeClasses = computed(() => {
  if (!props.event) return 'from-gray-600 to-gray-700';
  
  switch (props.event.theme) {
    case 'green':
      return 'from-green-600 to-emerald-600';
    case 'blue':
      return 'from-blue-600 to-cyan-600';
    case 'red':
      return 'from-red-600 to-orange-600';
    case 'purple':
      return 'from-purple-600 to-pink-600';
    case 'yellow':
      return 'from-yellow-600 to-amber-600';
    default:
      return 'from-gray-600 to-gray-700';
  }
});

// Get status badge color
const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'in-progress':
      return 'bg-blue-100 text-blue-800';
    case 'completed':
      return 'bg-green-100 text-green-800';
    case 'cancelled':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

// Get priority badge color
const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'high':
      return 'bg-red-100 text-red-800';
    case 'medium':
      return 'bg-yellow-100 text-yellow-800';
    case 'low':
      return 'bg-green-100 text-green-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};
</script>

<template>
  <TransitionRoot :show="open" as="template">
    <Dialog @close="emit('close')" class="relative z-50">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black/50 backdrop-blur-sm" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4">
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel
              class="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-left align-middle shadow-2xl transition-all border border-white/10"
            >
              <!-- Header with gradient -->
              <div :class="`bg-gradient-to-r ${themeClasses} p-6`">
                <div class="flex items-start justify-between">
                  <div class="flex items-center">
                    <div
                      class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center mr-4"
                    >
                      <Icon :name="event?.icon || 'mdi:calendar'" class="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <DialogTitle as="h3" class="text-2xl font-bold text-white">
                        {{ event?.title || 'Event Details' }}
                      </DialogTitle>
                      <p class="text-white/80 text-sm mt-1">{{ event?.type || 'Event' }}</p>
                    </div>
                  </div>
                  <button
                    @click="emit('close')"
                    class="text-white/80 hover:text-white transition-colors"
                  >
                    <Icon name="mdi:close" class="h-6 w-6" />
                  </button>
                </div>
              </div>

              <!-- Content -->
              <div class="p-6 space-y-6">
                <!-- Date and Time -->
                <div
                  class="bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-xl p-4 border border-white/5"
                >
                  <div class="flex items-center mb-2">
                    <Icon name="mdi:calendar-clock" class="h-5 w-5 text-purple-400 mr-2" />
                    <h4 class="text-sm font-semibold text-gray-300">Date & Time</h4>
                  </div>
                  <p class="text-white font-medium">{{ formatDate(event?.date) }}</p>
                  <p class="text-gray-400 text-sm">{{ formatTime(event?.date) }}</p>
                </div>

                <!-- Description -->
                <div
                  v-if="event?.description"
                  class="bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-xl p-4 border border-white/5"
                >
                  <div class="flex items-center mb-2">
                    <Icon name="mdi:text" class="h-5 w-5 text-purple-400 mr-2" />
                    <h4 class="text-sm font-semibold text-gray-300">Description</h4>
                  </div>
                  <p class="text-gray-300">{{ event.description }}</p>
                </div>

                <!-- Status and Priority (for tasks) -->
                <div
                  v-if="event?.data?.status || event?.data?.priority"
                  class="flex gap-4"
                >
                  <div
                    v-if="event.data.status"
                    class="bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-xl p-4 border border-white/5 flex-1"
                  >
                    <div class="flex items-center mb-2">
                      <Icon name="mdi:progress-check" class="h-5 w-5 text-purple-400 mr-2" />
                      <h4 class="text-sm font-semibold text-gray-300">Status</h4>
                    </div>
                    <span
                      :class="getStatusColor(event.data.status)"
                      class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium capitalize"
                    >
                      {{ event.data.status }}
                    </span>
                  </div>
                  
                  <div
                    v-if="event.data.priority"
                    class="bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-xl p-4 border border-white/5 flex-1"
                  >
                    <div class="flex items-center mb-2">
                      <Icon name="mdi:flag" class="h-5 w-5 text-purple-400 mr-2" />
                      <h4 class="text-sm font-semibold text-gray-300">Priority</h4>
                    </div>
                    <span
                      :class="getPriorityColor(event.data.priority)"
                      class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium capitalize"
                    >
                      {{ event.data.priority }}
                    </span>
                  </div>
                </div>

                <!-- Due Date (for tasks) -->
                <div
                  v-if="event?.data?.dueDate"
                  class="bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-xl p-4 border border-white/5"
                >
                  <div class="flex items-center mb-2">
                    <Icon name="mdi:calendar-alert" class="h-5 w-5 text-purple-400 mr-2" />
                    <h4 class="text-sm font-semibold text-gray-300">Due Date</h4>
                  </div>
                  <p class="text-white font-medium">{{ formatDate(event.data.dueDate) }}</p>
                </div>

                <!-- Location (for meetings) -->
                <div
                  v-if="event?.data?.location"
                  class="bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-xl p-4 border border-white/5"
                >
                  <div class="flex items-center mb-2">
                    <Icon name="mdi:map-marker" class="h-5 w-5 text-purple-400 mr-2" />
                    <h4 class="text-sm font-semibold text-gray-300">Location</h4>
                  </div>
                  <p class="text-gray-300">{{ event.data.location }}</p>
                </div>

                <!-- Additional Info -->
                <div
                  v-if="event?.user"
                  class="bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-xl p-4 border border-white/5"
                >
                  <div class="flex items-center mb-2">
                    <Icon name="mdi:account" class="h-5 w-5 text-purple-400 mr-2" />
                    <h4 class="text-sm font-semibold text-gray-300">Created By</h4>
                  </div>
                  <div class="flex items-center">
                    <img
                      v-if="event.user.avatar"
                      :src="event.user.avatar"
                      :alt="event.user.name"
                      class="w-8 h-8 rounded-full mr-2"
                    />
                    <div
                      v-else
                      class="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center mr-2"
                    >
                      <span class="text-white text-xs font-bold">
                        {{ event.user.name?.charAt(0)?.toUpperCase() || 'U' }}
                      </span>
                    </div>
                    <p class="text-gray-300">{{ event.user.name }}</p>
                  </div>
                </div>
              </div>

              <!-- Footer Actions -->
              <div class="p-6 border-t border-white/10">
                <div class="flex justify-between items-center">
                  <div class="text-xs text-gray-500">
                    <span v-if="!event?.editable" class="flex items-center">
                      <Icon name="mdi:lock" class="mr-1" />
                      This event cannot be edited
                    </span>
                  </div>
                  <div class="flex gap-3">
                    <button
                      @click="emit('close')"
                      class="px-4 py-2 text-gray-300 hover:text-white bg-gray-800/50 hover:bg-gray-700/50 rounded-lg font-medium transition-all duration-200 border border-white/10"
                    >
                      Close
                    </button>
                    <button
                      v-if="event?.editable"
                      @click="emit('edit')"
                      class="px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-xl flex items-center"
                    >
                      <Icon name="mdi:pencil" class="mr-2" />
                      Edit Event
                    </button>
                  </div>
                </div>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>