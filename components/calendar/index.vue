<script setup lang="ts">
const month_names: any = ref([
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
]);

let month: any = ref(null);
let year: any = ref(null);

const no_of_days: any = ref([]);
const blankdays: any = ref([]);
const days = ref(["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]);

const events = ref([]);

const openModal = ref(false);

const initDate = () => {
  let today = new Date();
  month.value = today.getMonth();
  year.value = today.getFullYear();
};

const isToday = (date: number | undefined) => {
  const today = new Date();
  const d = new Date(year.value, month.value, date);

  return today.toDateString() === d.toDateString() ? true : false;
};

// Show what's planned for that day
const showDayModal = (day: any) => {
  console.log(month.value, day);
};

const getNoOfDays = () => {
  let daysInMonth = new Date(year.value, month.value + 1, 0).getDate();

  // find where to start calendar day of week
  let dayOfWeek = new Date(year.value, month.value).getDay();
  let blankdaysArray = [];
  for (var i = 1; i <= dayOfWeek; i++) {
    blankdaysArray.push(i);
  }

  let daysArray = [];
  for (var i = 1; i <= daysInMonth; i++) {
    daysArray.push(i);
  }

  blankdays.value = blankdaysArray;
  no_of_days.value = daysArray;
};

onMounted(() => {
  initDate();
  getNoOfDays();
});
</script>

<template>
  <main id="calendar">
    <div class="o_container border rounded border-gray-600">
      <div class="theme_300 rounded-lg shadow overflow-hidden">
        <div class="flex items-center justify-between py-2 px-6">
          <div>
            <span
              v-text="month_names[month]"
              class="text-lg font-bold "
            />
            <span
              v-text="year"
              class="ml-1 text-lg text-gray-600 font-normal"
            />
          </div>
          <div class="border rounded-lg px-1 pt-1">
            <button
              type="button"
              class="leading-none rounded-lg transition ease-in-out duration-100 inline-flex cursor-pointer hover:bg-gray-200 p-1 items-center focus:outline-none"
              :class="{ 'cursor-not-allowed opacity-25': month === 0 }"
              :disabled="month === 0"
              @click="month-- && getNoOfDays()"
            >
              <svg
                class="h-6 w-6 text-gray-500 inline-flex leading-none"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>
            <div class="border-r inline-flex h-6"></div>
            <button
              type="button"
              class="leading-none rounded-lg transition ease-in-out duration-100 inline-flex items-center cursor-pointer hover:bg-gray-200 p-1 focus:outline-none"
              :class="{ 'cursor-not-allowed opacity-25': month === 11 }"
              :disabled="month === 11"
              @click="month++ && getNoOfDays()"
            >
              <svg
                class="h-6 w-6 text-gray-500 inline-flex leading-none"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          </div>
        </div>
        <div class="-mx-1 -mb-1">
          <div class="flex flex-wrap -mb-8" style="margin-bottom: -40px">
            <template v-for="(day, index) in days" :key="index">
              <div class="px-2 py-2 w-1/7">
                <div
                  v-text="day"
                  class="text-gray-600 text-sm uppercase tracking-wide font-bold text-center"
                ></div>
              </div>
            </template>
          </div>

          <div class="flex flex-wrap border-t border-l">
            <template v-for="(blankday, index) in blankdays" :key="index">
              <div
                class="text-center border-r border-b px-4 pt-2 h-32 w-1/7"
              >
            </div>
            </template>
            <template v-for="(date, dateIndex) in no_of_days" :key="dateIndex">
              <div class="px-4 pt-2 border-r border-b relative h-32 w-1/7">
                <div
                  @click="showDayModal(date)"
                  v-text="date"
                  class="inline-flex w-6 h-6 items-center justify-center cursor-pointer text-center leading-none rounded-full transition ease-in-out duration-100"
                  :class="{
                    'bg-primarys text-primarys_content': isToday(date) === true,
                    'hover:bg-secondary hover:text-secondary_content': isToday(date) === false,
                  }"
                ></div>
                <div class="overflow-y-auto mt-1 h-20">
                  <div
                    class="absolute top-0 right-0 mt-2 mr-2 inline-flex items-center justify-center rounded-full text-sm w-6 h-6 bg-gray-700 text-white leading-none"
                    v-show="
                      events.filter(
                        (e) =>
                          e.event_date ===
                          new Date(year, month, date).toDateString()
                      ).length
                    "
                    v-text="
                      events.filter(
                        (e) =>
                          e.event_date ===
                          new Date(year, month, date).toDateString()
                      ).length
                    "
                  ></div>
                  <template
                    v-for="(event, index) in events.filter(
                      (e) =>
                        new Date(e.event_date).toDateString() ===
                        new Date(year, month, date).toDateString()
                    )" :key="index"
                  >
                    <div
                      class="px-2 py-1 rounded-lg mt-1 overflow-hidden border"
                      :class="{
                        'border-blue-200 text-blue-800 bg-blue-100':
                          event.event_theme === 'blue',
                        'border-red-200 text-red-800 bg-red-100':
                          event.event_theme === 'red',
                        'border-yellow-200 text-yellow-800 bg-yellow-100':
                          event.event_theme === 'yellow',
                        'border-green-200 text-green-800 bg-green-100':
                          event.event_theme === 'green',
                        'border-purple-200 text-purple-800 bg-purple-100':
                          event.event_theme === 'purple',
                      }"
                    >
                      <p
                        v-text="event.event_title"
                        class="text-sm truncate leading-tight"
                      />
                    </div>
                  </template>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
  </main>
</template>
