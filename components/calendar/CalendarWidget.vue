<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

interface Event {
  id: string
  title: string
  date: Date
  type: string
  theme: string
  description?: string
  icon?: string
  editable?: boolean
  data?: any
}

interface Activity {
  id: string
  type: string
  title: string
  content: string
  timestamp: Date
  user?: {
    name: string
    avatar?: string
  }
}

interface Subscription {
  id: string
  ad_spot_name?: string
  status: string
  nextPaymentDate?: Date
  price?: number
}

const props = defineProps<{
  events?: Event[]
  activities?: Activity[]
  subscriptions?: Subscription[]
}>()

const emit = defineEmits<{
  dayClick: [date: Date]
  eventClick: [event: Event]
}>()

const month_names = ref([
  "January", "February", "March", "April", "May", "June",
  "July", "August", "September", "October", "November", "December"
])

const days = ref(["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"])
const month = ref<number>(0)
const year = ref<number>(0)
const no_of_days = ref<number[]>([])
const blankdays = ref<number[]>([])
const selectedDate = ref<Date | null>(null)
const showDayModal = ref(false)

// Initialize calendar
const initDate = () => {
  const today = new Date()
  month.value = today.getMonth()
  year.value = today.getFullYear()
}

// Check if date is today
const isToday = (date: number) => {
  const today = new Date()
  const d = new Date(year.value, month.value, date)
  return today.toDateString() === d.toDateString()
}

// Get events for a specific date
const getEventsForDate = (date: number): Event[] => {
  const d = new Date(year.value, month.value, date)
  const dateStr = d.toDateString()

  const events: Event[] = []

  // Add calendar events
  if (props.events) {
    events.push(...props.events.filter(e => e.date.toDateString() === dateStr))
  }

  // Add activities as events
  if (props.activities) {
    const activityEvents = props.activities
      .filter(a => a.timestamp.toDateString() === dateStr)
      .map(a => ({
        id: a.id,
        title: `${a.type === 'upload' ? 'Uploaded' : 'Viewed'} ${a.content}`,
        date: a.timestamp,
        type: 'activity',
        theme: 'blue',
        description: a.title
      }))
    events.push(...activityEvents)
  }

  return events
}

// Get number of events for a date
const getEventCountForDate = (date: number) => {
  return getEventsForDate(date).length
}

// Navigate months
const previousMonth = () => {
  if (month.value === 0) {
    month.value = 11
    year.value--
  } else {
    month.value--
  }
  getNoOfDays()
}

const nextMonth = () => {
  if (month.value === 11) {
    month.value = 0
    year.value++
  } else {
    month.value++
  }
  getNoOfDays()
}

// Open day modal
const openDayModal = (date: number) => {
  const clickedDate = new Date(year.value, month.value, date)
  selectedDate.value = clickedDate
  showDayModal.value = true

  // Emit day click event for parent to handle
  emit('dayClick', clickedDate)
}

// Handle event click
const handleEventClick = (event: Event) => {
  emit('eventClick', event)
}

// Get events for selected date
const selectedDateEvents = computed(() => {
  if (!selectedDate.value) return []
  return getEventsForDate(selectedDate.value.getDate())
})

// Get theme colors
const getThemeColors = (theme: string) => {
  switch (theme) {
    case 'blue':
      return 'border-blue-400/30 text-blue-300 bg-blue-400/10'
    case 'green':
      return 'border-green-400/30 text-green-300 bg-green-400/10'
    case 'red':
      return 'border-red-400/30 text-red-300 bg-red-400/10'
    case 'purple':
      return 'border-purple-400/30 text-purple-300 bg-purple-400/10'
    case 'yellow':
      return 'border-yellow-400/30 text-yellow-300 bg-yellow-400/10'
    default:
      return 'border-gray-400/30 text-gray-300 bg-gray-400/10'
  }
}

// Calculate calendar days
const getNoOfDays = () => {
  const daysInMonth = new Date(year.value, month.value + 1, 0).getDate()
  const dayOfWeek = new Date(year.value, month.value).getDay()

  const blankdaysArray: number[] = []
  for (let i = 0; i < dayOfWeek; i++) {
    blankdaysArray.push(i)
  }

  const daysArray: number[] = []
  for (let i = 1; i <= daysInMonth; i++) {
    daysArray.push(i)
  }

  blankdays.value = blankdaysArray
  no_of_days.value = daysArray
}

// Upcoming events
const upcomingEvents = computed(() => {
  const today = new Date()
  const allEvents: Event[] = []

  if (props.events) {
    allEvents.push(...props.events)
  }

  // Add upcoming subscription payments
  if (props.subscriptions) {
    props.subscriptions.forEach(sub => {
      if (sub.nextPaymentDate && sub.status === 'active') {
        allEvents.push({
          id: `sub-${sub.id}`,
          title: `Payment Due - ${sub.ad_spot_name || 'Subscription'}`,
          date: sub.nextPaymentDate,
          type: 'payment',
          theme: 'green',
          description: `$${sub.price || 0} payment`
        })
      }
    })
  }

  return allEvents
    .filter(e => e.date > today)
    .sort((a, b) => a.date.getTime() - b.date.getTime())
    .slice(0, 5)
})

onMounted(() => {
  initDate()
  getNoOfDays()
})
</script>

<template>
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Calendar Grid -->
    <div class="lg:col-span-2">
      <div class="bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-xl border border-white/5 overflow-hidden">
        <!-- Calendar Header -->
        <div class="p-4 border-b border-white/10 flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <h4 class="text-lg font-bold text-white">
              {{ month_names[month] }} {{ year }}
            </h4>
          </div>
          <div class="flex items-center space-x-2">
            <button
              class="p-2 rounded-lg bg-gradient-to-r from-purple-600/20 to-pink-600/20 hover:from-purple-600/30 hover:to-pink-600/30 text-purple-300 transition-all duration-200 border border-purple-400/20"
              @click="previousMonth"
            >
              <Icon name="mdi:chevron-left" class="text-xl" />
            </button>
            <button
              class="p-2 rounded-lg bg-gradient-to-r from-purple-600/20 to-pink-600/20 hover:from-purple-600/30 hover:to-pink-600/30 text-purple-300 transition-all duration-200 border border-purple-400/20"
              @click="nextMonth"
            >
              <Icon name="mdi:chevron-right" class="text-xl" />
            </button>
          </div>
        </div>

        <!-- Days of Week -->
        <div class="grid grid-cols-7 border-b border-white/10">
          <div
            v-for="day in days"
            :key="day"
            class="p-3 text-center text-xs font-medium text-gray-400 uppercase tracking-wider"
          >
            {{ day }}
          </div>
        </div>

        <!-- Calendar Days -->
        <div class="grid grid-cols-7">
          <!-- Blank days -->
          <div
            v-for="blankday in blankdays"
            :key="`blank-${blankday}`"
            class="p-4 border-r border-b border-white/5 bg-gray-900/30"
          ></div>

          <!-- Calendar days -->
          <div
            v-for="date in no_of_days"
            :key="date"
            class="relative p-4 border-r border-b border-white/5 hover:bg-purple-600/10 cursor-pointer transition-all duration-200 group"
            :class="{
              'bg-gradient-to-br from-purple-600/20 to-pink-600/20': isToday(date)
            }"
            @click="openDayModal(date)"
          >
            <div class="flex justify-between items-start">
              <span
                class="text-sm font-medium"
                :class="{
                  'text-purple-300': isToday(date),
                  'text-gray-300 group-hover:text-white': !isToday(date)
                }"
              >
                {{ date }}
              </span>
              <span
                v-if="getEventCountForDate(date) > 0"
                class="flex items-center justify-center w-5 h-5 text-xs font-medium rounded-full bg-gradient-to-r from-purple-600 to-pink-600 text-white"
              >
                {{ getEventCountForDate(date) }}
              </span>
            </div>

            <!-- Event indicators -->
            <div class="mt-2 space-y-1">
              <div
                v-for="(event, idx) in getEventsForDate(date).slice(0, 2)"
                :key="event.id"
                :class="getThemeColors(event.theme)"
                class="text-xs px-2 py-1 rounded border truncate cursor-pointer hover:opacity-80 transition-opacity"
                @click.stop="handleEventClick(event)"
              >
                {{ event.title }}
              </div>
              <div
                v-if="getEventsForDate(date).length > 2"
                class="text-xs text-gray-400 pl-2"
              >
                +{{ getEventsForDate(date).length - 2 }} more
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Upcoming Events Sidebar -->
    <div class="lg:col-span-1">
      <div class="bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-xl border border-white/5 p-6">
        <h4 class="text-lg font-bold text-white mb-4 flex items-center">
          <div class="w-3 h-3 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mr-2"></div>
          Upcoming Events
        </h4>

        <div class="space-y-3">
          <div
            v-for="event in upcomingEvents"
            :key="event.id"
            class="p-3 rounded-lg bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-400/20"
          >
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <p class="text-sm font-medium text-purple-300">{{ event.title }}</p>
                <p class="text-xs text-gray-400 mt-1">
                  {{ event.date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' }) }}
                </p>
                <p v-if="event.description" class="text-xs text-gray-500 mt-1">
                  {{ event.description }}
                </p>
              </div>
              <div
                :class="{
                  'text-green-400': event.theme === 'green',
                  'text-blue-400': event.theme === 'blue',
                  'text-red-400': event.theme === 'red',
                  'text-purple-400': event.theme === 'purple',
                  'text-yellow-400': event.theme === 'yellow'
                }"
              >
                <Icon
                  :name="{
                    'payment': 'mdi:currency-usd',
                    'activity': 'mdi:eye',
                    'subscription': 'mdi:calendar-clock'
                  }[event.type] || 'mdi:calendar'"
                  class="text-xl"
                />
              </div>
            </div>
          </div>

          <div v-if="upcomingEvents.length === 0" class="text-center py-8">
            <Icon name="mdi:calendar-blank" class="text-4xl text-gray-600 mb-2" />
            <p class="text-sm text-gray-400">No upcoming events</p>
          </div>
        </div>

        <!-- Active Subscriptions Summary -->
        <div v-if="subscriptions && subscriptions.length > 0" class="mt-6 pt-6 border-t border-white/10">
          <h5 class="text-sm font-medium text-gray-300 mb-3">Active Subscriptions</h5>
          <div class="space-y-2">
            <div
              v-for="sub in subscriptions.filter(s => s.status === 'active')"
              :key="sub.id"
              class="flex items-center justify-between text-sm"
            >
              <span class="text-gray-400">{{ sub.ad_spot_name || 'Subscription' }}</span>
              <span class="text-green-400 font-medium">${{ sub.price || 0 }}/mo</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Day Modal -->
  <div
    v-if="showDayModal"
    class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
    @click="showDayModal = false"
  >
    <div
      class="bg-gradient-to-br from-gray-900/95 via-gray-800/95 to-gray-900/95 rounded-xl border border-white/10 p-6 max-w-md w-full shadow-2xl"
      @click.stop
    >
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-xl font-bold text-white">
          {{ selectedDate?.toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' }) }}
        </h3>
        <button
          class="p-2 rounded-lg hover:bg-white/10 text-gray-400 hover:text-white transition-colors duration-200"
          @click="showDayModal = false"
        >
          <Icon name="mdi:close" class="text-xl" />
        </button>
      </div>

      <div class="space-y-3 max-h-96 overflow-y-auto">
        <div
          v-for="event in selectedDateEvents"
          :key="event.id"
          :class="getThemeColors(event.theme)"
          class="p-4 rounded-lg border cursor-pointer hover:opacity-80 transition-opacity"
          @click="handleEventClick(event)"
        >
          <div class="flex items-start justify-between">
            <div>
              <p class="font-medium">{{ event.title }}</p>
              <p v-if="event.description" class="text-sm opacity-80 mt-1">
                {{ event.description }}
              </p>
            </div>
            <div class="flex items-center">
              <Icon
                :name="{
                  'payment': 'mdi:currency-usd',
                  'activity': 'mdi:eye',
                  'subscription': 'mdi:calendar-clock'
                }[event.type] || 'mdi:calendar'"
                class="text-xl opacity-60 mr-2"
              />
              <Icon
                v-if="event.editable"
                name="mdi:pencil"
                class="text-sm opacity-60"
                title="Editable"
              />
            </div>
          </div>
        </div>

        <div v-if="selectedDateEvents.length === 0" class="text-center py-8">
          <Icon name="mdi:calendar-blank" class="text-4xl text-gray-600 mb-2" />
          <p class="text-gray-400">No events on this date</p>
        </div>
      </div>
    </div>
  </div>
</template>