<template>
  <div
    v-if="open"
    class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
    @click="handleClose"
  >
    <div
      class="bg-gradient-to-br from-gray-900/95 via-gray-800/95 to-gray-900/95 rounded-xl border border-white/10 p-6 max-w-md w-full shadow-2xl"
      @click.stop
    >
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-xl font-bold text-white">Create Activity</h3>
        <button
          class="p-2 rounded-lg hover:bg-white/10 text-gray-400 hover:text-white transition-colors duration-200"
          @click="handleClose"
        >
          <Icon name="mdi:close" class="text-xl" />
        </button>
      </div>

      <form class="space-y-4" @submit.prevent="handleSubmit">
        <!-- Activity Type -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-3">
            Activity Type <span class="text-red-400">*</span>
          </label>
          <div class="grid grid-cols-2 gap-2">
            <button
              v-for="type in activityTypes"
              :key="type.value"
              type="button"
              class="p-3 rounded-lg border transition-all duration-200 flex flex-col items-center space-y-2"
              :class="[
                form.type === type.value
                  ? 'bg-gradient-to-r from-blue-500/20 to-blue-600/20 border-blue-400/50 text-white'
                  : 'bg-gray-800/30 border-white/10 text-gray-400 hover:border-white/20 hover:text-white',
              ]"
              @click="form.type = type.value"
            >
              <Icon :name="type.icon" class="w-6 h-6" />
              <span class="text-xs font-medium">{{ type.label }}</span>
            </button>
          </div>
        </div>

        <!-- Contact Selection -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Contact <span class="text-gray-500 text-sm">(optional)</span>
          </label>
          <select
            v-model="form.contactId"
            class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">No contact (personal activity)</option>
            <option v-for="contact in sortedContacts" :key="contact.id" :value="contact.id">
              {{ getFullName(contact) }}
              <span v-if="contact.businessName"> - {{ contact.businessName }}</span>
            </option>
          </select>
          <p class="text-xs text-gray-500 mt-1">
            Leave empty to create a personal activity or select a contact to associate it
          </p>
        </div>

        <!-- Title -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Title <span class="text-red-400">*</span>
          </label>
          <input
            v-model="form.title"
            type="text"
            required
            placeholder="e.g., Follow-up call about proposal"
            class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <!-- Description -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2"> Description </label>
          <textarea
            v-model="form.description"
            rows="3"
            placeholder="Add details about the activity..."
            class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
          />
        </div>

        <!-- Date and Time -->
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2"> Date </label>
            <input
              v-model="form.date"
              type="date"
              class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2"> Time </label>
            <input
              v-model="form.time"
              type="time"
              class="w-full px-3 py-2 bg-gray-800/50 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        <!-- Priority (for tasks) -->
        <div v-if="form.type === 'task'">
          <label class="block text-sm font-medium text-gray-300 mb-2"> Priority </label>
          <div class="flex space-x-2">
            <button
              v-for="priority in priorities"
              :key="priority.value"
              type="button"
              class="px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200"
              :class="[
                form.priority === priority.value
                  ? `bg-gradient-to-r ${priority.color} text-white`
                  : 'bg-gray-800/30 border border-white/10 text-gray-400 hover:border-white/20 hover:text-white',
              ]"
              @click="form.priority = priority.value"
            >
              {{ priority.label }}
            </button>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            class="px-4 py-2 rounded-lg bg-gray-700/50 hover:bg-gray-600/50 text-gray-300 hover:text-white transition-colors duration-200"
            @click="handleClose"
          >
            Cancel
          </button>
          <button
            type="submit"
            :disabled="!form.title || !form.type"
            class="px-4 py-2 rounded-lg bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Create Activity
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';

import { useActivities } from '~/composables/useActivities';
import { useContacts } from '~/composables/useContacts';
import { useCurrentUser } from '~/composables/useCurrentUser';

interface Props {
  open: boolean;
  preselectedDate?: Date | null;
  contacts: any[];
}

const props = defineProps<Props>();
const emit = defineEmits<{
  close: [];
  success: [];
}>();

const { getFullName } = useContacts();
const { createActivity } = useActivities();
const { currentUser } = useCurrentUser();

// Activity types
const activityTypes = [
  { value: 'note', label: 'Note', icon: 'mdi:note-text' },
  { value: 'task', label: 'Task', icon: 'mdi:check-circle' },
  { value: 'call', label: 'Call', icon: 'mdi:phone' },
  { value: 'email', label: 'Email', icon: 'mdi:email' },
  { value: 'meeting', label: 'Meeting', icon: 'mdi:account-group' },
  { value: 'message', label: 'Message', icon: 'mdi:message-text' },
];

// Priority levels
const priorities = [
  { value: 'low', label: 'Low', color: 'from-green-500 to-green-600' },
  { value: 'medium', label: 'Medium', color: 'from-yellow-500 to-yellow-600' },
  { value: 'high', label: 'High', color: 'from-red-500 to-red-600' },
];

// Form state
const form = ref({
  type: 'note',
  contactId: '',
  title: '',
  description: '',
  date: '',
  time: '',
  priority: 'medium',
});

// Format date for input
const formatDateForInput = (date: Date): string => {
  return date.toISOString().split('T')[0];
};

// Sorted contacts
const sortedContacts = computed(() => {
  return [...props.contacts].sort((a, b) => {
    const nameA = getFullName(a).toLowerCase();
    const nameB = getFullName(b).toLowerCase();
    return nameA.localeCompare(nameB);
  });
});

// Contact is now optional for all activity types
// const requiresContact = computed(() => {
//   // Notes and tasks can be personal, other activities typically need a contact
//   return !['note', 'task'].includes(form.value.type);
// });

// Watch for preselected date
watch(
  () => props.preselectedDate,
  newDate => {
    if (newDate) {
      form.value.date = formatDateForInput(newDate);
    }
  },
  { immediate: true }
);

// Handle form submission
const handleSubmit = async () => {
  try {
    if (!currentUser.value?.uid) {
      throw new Error('User not authenticated');
    }

    // Prepare activity data
    const activityDate = form.value.date ? new Date(form.value.date) : new Date();
    if (form.value.time) {
      const [hours, minutes] = form.value.time.split(':').map(Number);
      activityDate.setHours(hours, minutes, 0, 0);
    }

    const activityData: any = {
      type: form.value.type as any,
      title: form.value.title,
      description: form.value.description,
      associatedEntities: form.value.contactId ? [
        {
          entityType: 'contact' as const,
          entityId: form.value.contactId,
        },
      ] : [],
      ownerId: currentUser.value.uid,
      createdBy: currentUser.value.uid,
      ...(form.value.type === 'task' && {
        dueDate: activityDate,
        priority: form.value.priority as any,
        status: 'pending' as any,
      }),
      ...(form.value.type === 'meeting' && {
        startTime: activityDate,
        endTime: new Date(activityDate.getTime() + 60 * 60 * 1000), // 1 hour default
      }),
    };
    
    // Add scheduledDate for other activity types
    if (!['task', 'meeting'].includes(form.value.type)) {
      activityData.scheduledDate = activityDate;
    }

    await createActivity(activityData);

    emit('success');
    resetForm();
  } catch (error) {
    console.error('Error creating activity:', error);
  }
};

// Reset form
const resetForm = () => {
  form.value = {
    type: 'note',
    contactId: '',
    title: '',
    description: '',
    date: '',
    time: '',
    priority: 'medium',
  };
};

// Handle close
const handleClose = () => {
  emit('close');
  resetForm();
};
</script>
