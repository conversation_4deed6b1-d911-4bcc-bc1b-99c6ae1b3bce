<template>
  <div class="ad-preview">
    <div class="preview-container" :class="deviceClass" :style="containerStyle">
      <div class="preview-frame" :style="frameStyle">
        <div 
          v-if="adContent?.content?.text" 
          v-html="adContent.content.text"
          class="ad-content"
        ></div>
        
        <div v-else class="empty-state">
          <Icon name="image" class="w-16 h-16 text-gray-300" />
          <p class="text-gray-500 mt-2">No content to preview</p>
        </div>
      </div>
      
      <!-- Device Chrome -->
      <div v-if="device === 'mobile'" class="mobile-chrome">
        <div class="notch"></div>
        <div class="home-indicator"></div>
      </div>
    </div>
    
    <!-- Device Info -->
    <div class="device-info text-center mt-4 text-sm text-gray-600">
      <span class="capitalize">{{ device }}</span> Preview
      <span v-if="dimensions"> • {{ dimensions.width }} × {{ dimensions.height }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { AdContent } from '~/composables/useAdContentManagement'

interface Props {
  adContent?: Partial<AdContent>
  device?: 'desktop' | 'mobile' | 'tablet'
  maxWidth?: number
  maxHeight?: number
}

const props = withDefaults(defineProps<Props>(), {
  device: 'desktop',
  maxWidth: 400,
  maxHeight: 600,
})

// Device configurations
const deviceConfigs = {
  desktop: { width: 800, height: 600, scale: 0.5 },
  tablet: { width: 768, height: 1024, scale: 0.4 },
  mobile: { width: 375, height: 667, scale: 0.8 }
}

// Computed properties
const currentDevice = computed(() => deviceConfigs[props.device])

const dimensions = computed(() => ({
  width: currentDevice.value.width,
  height: currentDevice.value.height
}))

const deviceClass = computed(() => `preview-${props.device}`)

const containerStyle = computed(() => ({
  maxWidth: `${props.maxWidth}px`,
  maxHeight: `${props.maxHeight}px`,
}))

const frameStyle = computed(() => {
  const scale = Math.min(
    props.maxWidth / currentDevice.value.width,
    props.maxHeight / currentDevice.value.height,
    currentDevice.value.scale
  )
  
  return {
    width: `${currentDevice.value.width}px`,
    height: `${currentDevice.value.height}px`,
    transform: `scale(${scale})`,
    transformOrigin: 'top center'
  }
})
</script>

<style scoped>
.ad-preview {
  @apply flex flex-col items-center;
}

.preview-container {
  @apply relative mx-auto;
}

.preview-frame {
  @apply bg-white border shadow-lg relative overflow-hidden;
}

.preview-desktop .preview-frame {
  @apply rounded-lg;
}

.preview-tablet .preview-frame {
  @apply rounded-xl;
}

.preview-mobile .preview-frame {
  @apply rounded-3xl;
}

.ad-content {
  @apply w-full h-full;
}

.ad-content :deep(*) {
  @apply max-w-full;
}

.empty-state {
  @apply w-full h-full flex flex-col items-center justify-center;
}

.mobile-chrome {
  @apply absolute inset-0 pointer-events-none;
}

.notch {
  @apply absolute top-0 left-1/2 transform -translate-x-1/2 w-32 h-6 bg-black rounded-b-2xl;
}

.home-indicator {
  @apply absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black rounded-full opacity-60;
}
</style>