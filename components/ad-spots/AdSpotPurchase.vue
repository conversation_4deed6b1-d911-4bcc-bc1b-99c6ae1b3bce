<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
    <!-- Step Progress Bar -->
    <div class="px-6 pt-6">
      <div class="flex items-center justify-between mb-6">
        <div
          v-for="(step, index) in steps"
          :key="index"
          class="flex flex-col items-center relative"
          :class="{ 'flex-1': true }"
        >
          <!-- Step connector line -->
          <div
            v-if="index < steps.length - 1"
            class="absolute top-4 w-full h-0.5 left-1/2"
            :class="{
              'bg-[#0072ff]': currentStep > index,
              'bg-gray-300 dark:bg-gray-600': currentStep <= index,
            }"
          ></div>

          <!-- Step circle -->
          <div
            class="w-8 h-8 rounded-full flex items-center justify-center z-10"
            :class="{
              'bg-[#0072ff] text-white': currentStep >= index,
              'bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-400': currentStep < index,
            }"
          >
            <span v-if="currentStep > index">
              <Icon name="mdi:check" class="h-5 w-5" />
            </span>
            <span v-else>{{ index + 1 }}</span>
          </div>

          <!-- Step label -->
          <div
            class="text-xs mt-2 font-medium"
            :class="{
              'text-[#0072ff]': currentStep >= index,
              'text-gray-500 dark:text-gray-400': currentStep < index,
            }"
          >
            {{ step.label }}
          </div>
        </div>
      </div>
    </div>

    <!-- Step Content -->
    <div class="p-6">
      <!-- Step 1: Ad Details -->
      <div v-if="currentStep === 0" class="space-y-6">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">Enter Ad Details</h2>
        <p class="text-gray-600 dark:text-gray-400">
          Provide information about your advertisement:
        </p>

        <form @submit.prevent class="space-y-6">
          <!-- Ad Title -->
          <div>
            <label
              for="ad-title"
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >
              Ad Title <span class="text-red-500">*</span>
            </label>
            <input
              id="ad-title"
              v-model="adDetails.title"
              type="text"
              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
              :class="{ 'border-red-500 dark:border-red-500': errors.title }"
              placeholder="Enter a title for your advertisement"
              required
            />
            <p v-if="errors.title" class="mt-1 text-sm text-red-600 dark:text-red-400">
              {{ errors.title }}
            </p>
          </div>

          <!-- Ad Description -->
          <div>
            <label
              for="ad-description"
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >
              Ad Description <span class="text-red-500">*</span>
            </label>
            <textarea
              id="ad-description"
              v-model="adDetails.description"
              rows="4"
              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
              :class="{ 'border-red-500 dark:border-red-500': errors.description }"
              placeholder="Describe your advertisement"
              required
            ></textarea>
            <p v-if="errors.description" class="mt-1 text-sm text-red-600 dark:text-red-400">
              {{ errors.description }}
            </p>
          </div>

          <!-- Ad Image Upload -->
          <div>
            <label
              for="ad-image"
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >
              Ad Image <span class="text-red-500">*</span>
            </label>
            <div
              class="border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-md p-6 text-center"
              :class="{ 'border-red-500 dark:border-red-500': errors.image_url }"
            >
              <div v-if="adDetails.image_url">
                <div class="relative mx-auto w-full max-w-md">
                  <img
                    :src="imagePreview"
                    alt="Ad preview"
                    class="mx-auto max-h-48 object-contain"
                  />
                  <button
                    @click="removeImage"
                    class="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors duration-200"
                  >
                    <Icon name="mdi:close" class="h-4 w-4" />
                  </button>
                </div>
              </div>
              <div v-else>
                <Icon name="mdi:image-plus" class="mx-auto h-12 w-12 text-gray-400" />
                <div class="mt-2">
                  <label
                    for="file-upload"
                    class="cursor-pointer text-[#0072ff] dark:text-[#82aae3] hover:text-[#0054bb] dark:hover:text-white"
                  >
                    <span>Upload an image</span>
                    <input
                      id="file-upload"
                      type="file"
                      class="sr-only"
                      accept="image/*"
                      @change="handleImageUpload"
                    />
                  </label>
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    PNG, JPG, GIF up to 5MB
                  </p>
                </div>
              </div>
            </div>
            <p v-if="errors.image_url" class="mt-1 text-sm text-red-600 dark:text-red-400">
              {{ errors.image_url }}
            </p>
          </div>

          <!-- Ad Cost -->
          <div>
            <label
              for="ad-cost"
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >
              Ad Cost <span class="text-red-500">*</span>
            </label>
            <input
              id="ad-cost"
              v-model="adDetails.cost"
              type="number"
              step="0.01"
              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
              :class="{ 'border-red-500 dark:border-red-500': errors.cost }"
              placeholder="Enter the cost of your advertisement"
              required
            />
            <p v-if="errors.cost" class="mt-1 text-sm text-red-600 dark:text-red-400">
              {{ errors.cost }}
            </p>
          </div>

          <!-- Client ID -->
          <div>
            <label
              for="client-id"
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >
              Client ID <span class="text-red-500">*</span>
            </label>
            <input
              id="client-id"
              v-model="adDetails.client_id"
              type="text"
              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
              :class="{ 'border-red-500 dark:border-red-500': errors.client_id }"
              placeholder="Enter your client ID"
              required
            />
            <p v-if="errors.client_id" class="mt-1 text-sm text-red-600 dark:text-red-400">
              {{ errors.client_id }}
            </p>
          </div>

          <!-- Ad Start Date -->
          <div>
            <label
              for="start-date"
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >
              Start Date <span class="text-red-500">*</span>
            </label>
            <input
              id="start-date"
              v-model="adDetails.start_date"
              type="date"
              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
              :class="{ 'border-red-500 dark:border-red-500': errors.start_date }"
              required
            />
            <p v-if="errors.start_date" class="mt-1 text-sm text-red-600 dark:text-red-400">
              {{ errors.start_date }}
            </p>
          </div>

          <!-- Ad End Date -->
          <div>
            <label
              for="end-date"
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >
              End Date <span class="text-red-500">*</span>
            </label>
            <input
              id="end-date"
              v-model="adDetails.end_date"
              type="date"
              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
              :class="{ 'border-red-500 dark:border-red-500': errors.end_date }"
              required
            />
            <p v-if="errors.end_date" class="mt-1 text-sm text-red-600 dark:text-red-400">
              {{ errors.end_date }}
            </p>
          </div>
        </form>
      </div>

      <!-- Step 2: Payment Method -->
      <div v-else-if="currentStep === 1" class="space-y-6">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">
          Select Payment Method
        </h2>
        <p class="text-gray-600 dark:text-gray-400">Choose how you'd like to pay:</p>

        <!-- Order Summary -->
        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-md mb-6">
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Order Summary</h3>
          <div class="space-y-2">
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Ad Title:</span>
              <span class="text-gray-900 dark:text-gray-100">{{ adDetails.title }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Description:</span>
              <span class="text-gray-900 dark:text-gray-100">{{ adDetails.description }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Image:</span>
              <span class="text-gray-900 dark:text-gray-100">Uploaded</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Cost:</span>
              <span class="text-gray-900 dark:text-gray-100 font-bold">${{ adDetails.cost }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Client ID:</span>
              <span class="text-gray-900 dark:text-gray-100">{{ adDetails.client_id }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Duration:</span>
              <span class="text-gray-900 dark:text-gray-100"
                >{{ getDuration(adDetails.start_date, adDetails.end_date) }} days</span
              >
            </div>
          </div>
        </div>

        <!-- Payment Methods -->
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Select Payment Method
            </label>

            <!-- Stripe -->
            <div
              class="border rounded-md p-4 cursor-pointer transition-all duration-200"
              :class="{
                'border-[#0072ff] bg-blue-50 dark:bg-blue-900/20': paymentMethod === 'stripe',
                'border-gray-200 dark:border-gray-700 hover:border-[#0072ff] hover:bg-blue-50 dark:hover:bg-blue-900/10':
                  paymentMethod !== 'stripe',
              }"
              @click="setPaymentMethod('stripe')"
            >
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <Icon name="mdi:credit-card" class="h-6 w-6 text-[#0072ff] dark:text-[#82aae3]" />
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                    Credit Card (Stripe)
                  </h3>
                  <p class="text-xs text-gray-500 dark:text-gray-400">
                    Pay securely with your credit card
                  </p>
                </div>
                <div class="ml-auto">
                  <div
                    class="h-5 w-5 rounded-full border-2 flex items-center justify-center"
                    :class="{
                      'border-[#0072ff]': paymentMethod === 'stripe',
                      'border-gray-300 dark:border-gray-600': paymentMethod !== 'stripe',
                    }"
                  >
                    <div
                      v-if="paymentMethod === 'stripe'"
                      class="h-2.5 w-2.5 rounded-full bg-[#0072ff]"
                    ></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- PayPal -->
            <div
              class="border rounded-md p-4 cursor-pointer transition-all duration-200 mt-2"
              :class="{
                'border-[#0072ff] bg-blue-50 dark:bg-blue-900/20': paymentMethod === 'paypal',
                'border-gray-200 dark:border-gray-700 hover:border-[#0072ff] hover:bg-blue-50 dark:hover:bg-blue-900/10':
                  paymentMethod !== 'paypal',
              }"
              @click="setPaymentMethod('paypal')"
            >
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <Icon name="mdi:paypal" class="h-6 w-6 text-[#0072ff] dark:text-[#82aae3]" />
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100">PayPal</h3>
                  <p class="text-xs text-gray-500 dark:text-gray-400">
                    Pay with your PayPal account
                  </p>
                </div>
                <div class="ml-auto">
                  <div
                    class="h-5 w-5 rounded-full border-2 flex items-center justify-center"
                    :class="{
                      'border-[#0072ff]': paymentMethod === 'paypal',
                      'border-gray-300 dark:border-gray-600': paymentMethod !== 'paypal',
                    }"
                  >
                    <div
                      v-if="paymentMethod === 'paypal'"
                      class="h-2.5 w-2.5 rounded-full bg-[#0072ff]"
                    ></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- PayFast (for South Africa) -->
            <div
              v-if="country === 'South Africa'"
              class="border rounded-md p-4 cursor-pointer transition-all duration-200 mt-2"
              :class="{
                'border-[#0072ff] bg-blue-50 dark:bg-blue-900/20': paymentMethod === 'payfast',
                'border-gray-200 dark:border-gray-700 hover:border-[#0072ff] hover:bg-blue-50 dark:hover:bg-blue-900/10':
                  paymentMethod !== 'payfast',
              }"
              @click="setPaymentMethod('payfast')"
            >
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <Icon name="mdi:cash" class="h-6 w-6 text-[#0072ff] dark:text-[#82aae3]" />
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100">PayFast</h3>
                  <p class="text-xs text-gray-500 dark:text-gray-400">
                    Pay with PayFast (South Africa)
                  </p>
                </div>
                <div class="ml-auto">
                  <div
                    class="h-5 w-5 rounded-full border-2 flex items-center justify-center"
                    :class="{
                      'border-[#0072ff]': paymentMethod === 'payfast',
                      'border-gray-300 dark:border-gray-600': paymentMethod !== 'payfast',
                    }"
                  >
                    <div
                      v-if="paymentMethod === 'payfast'"
                      class="h-2.5 w-2.5 rounded-full bg-[#0072ff]"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Terms and Conditions -->
          <div class="mt-4">
            <div class="flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="terms"
                  v-model="acceptedTerms"
                  type="checkbox"
                  class="h-4 w-4 text-[#0072ff] border-gray-300 rounded focus:ring-[#0072ff]"
                />
              </div>
              <div class="ml-3 text-sm">
                <label for="terms" class="font-medium text-gray-700 dark:text-gray-300">
                  I accept the terms and conditions
                </label>
                <p class="text-gray-500 dark:text-gray-400">
                  By proceeding, you agree to our
                  <a href="#" class="text-[#0072ff] hover:text-[#0054bb]">Terms of Service</a> and
                  <a href="#" class="text-[#0072ff] hover:text-[#0054bb]">Privacy Policy</a>.
                </p>
              </div>
            </div>
            <p v-if="termsError" class="mt-1 text-sm text-red-600 dark:text-red-400">
              {{ termsError }}
            </p>
          </div>
        </div>
      </div>

      <!-- Step 3: Payment Processing -->
      <div v-else-if="currentStep === 2" class="space-y-6">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">Process Payment</h2>
        <p class="text-gray-600 dark:text-gray-400">Complete your payment:</p>

        <!-- Order Summary -->
        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-md mb-6">
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Order Summary</h3>
          <div class="space-y-2">
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Ad Title:</span>
              <span class="text-gray-900 dark:text-gray-100">{{ adDetails.title }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Description:</span>
              <span class="text-gray-900 dark:text-gray-100">{{ adDetails.description }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Image:</span>
              <span class="text-gray-900 dark:text-gray-100">Uploaded</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Cost:</span>
              <span class="text-gray-900 dark:text-gray-100 font-bold">${{ adDetails.cost }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Client ID:</span>
              <span class="text-gray-900 dark:text-gray-100">{{ adDetails.client_id }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Duration:</span>
              <span class="text-gray-900 dark:text-gray-100"
                >{{ getDuration(adDetails.start_date, adDetails.end_date) }} days</span
              >
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Payment Method:</span>
              <span class="text-gray-900 dark:text-gray-100">{{ paymentMethod }}</span>
            </div>
          </div>
        </div>

        <!-- Payment Processing UI -->
        <div class="text-center py-6">
          <!-- Processing State -->
          <div v-if="paymentStatus === 'processing'" class="space-y-4">
            <Icon name="mdi:loading" class="animate-spin h-12 w-12 text-[#0072ff] mx-auto" />
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Processing Payment</h3>
            <p class="text-gray-500 dark:text-gray-400">
              Please wait while we process your payment...
            </p>
          </div>

          <!-- Success State -->
          <div v-else-if="paymentStatus === 'success'" class="space-y-4">
            <div
              class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 dark:bg-green-900"
            >
              <Icon name="mdi:check" class="h-8 w-8 text-green-600 dark:text-green-300" />
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
              Payment Successful!
            </h3>
            <p class="text-gray-500 dark:text-gray-400">
              Your payment has been processed successfully.
            </p>
          </div>

          <!-- Error State -->
          <div v-else-if="paymentStatus === 'error'" class="space-y-4">
            <div
              class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900"
            >
              <Icon name="mdi:alert" class="h-8 w-8 text-red-600 dark:text-red-300" />
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Payment Failed</h3>
            <p class="text-red-600 dark:text-red-400">
              {{ paymentError || 'There was an error processing your payment.' }}
            </p>
            <button
              @click="handlePayment"
              class="mt-4 px-4 py-2 bg-[#0072ff] text-white rounded-md hover:bg-[#0054bb] transition-colors duration-200"
            >
              Try Again
            </button>
          </div>

          <!-- Initial State -->
          <div v-else class="space-y-4">
            <div
              class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900"
            >
              <Icon name="mdi:credit-card" class="h-8 w-8 text-[#0072ff] dark:text-[#82aae3]" />
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
              Ready to Process Payment
            </h3>
            <p class="text-gray-500 dark:text-gray-400">
              Click the button below to process your payment.
            </p>
            <button
              @click="handlePayment"
              class="mt-4 px-4 py-2 bg-[#0072ff] text-white rounded-md hover:bg-[#0054bb] transition-colors duration-200"
            >
              Process Payment
            </button>
          </div>
        </div>
      </div>

      <!-- Step 4: Confirmation -->
      <div v-else-if="currentStep === 3" class="space-y-6">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">Confirmation</h2>
        <p class="text-gray-600 dark:text-gray-400">Your ad purchase is complete!</p>

        <div class="text-center py-6">
          <div
            class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 dark:bg-green-900 mb-6"
          >
            <Icon name="mdi:check" class="h-10 w-10 text-green-600 dark:text-green-300" />
          </div>

          <h3 class="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            Thank You for Your Purchase!
          </h3>
          <p class="text-gray-600 dark:text-gray-400 mb-6">
            Your ad has been successfully purchased and will be active soon.
          </p>

          <!-- Order Details -->
          <div class="bg-gray-50 dark:bg-gray-700 p-6 rounded-md mb-6 text-left max-w-md mx-auto">
            <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Order Details</h4>
            <div class="space-y-3">
              <div class="flex justify-between">
                <span class="text-gray-600 dark:text-gray-400">Order ID:</span>
                <span class="text-gray-900 dark:text-gray-100">{{ orderId }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600 dark:text-gray-400">Ad Title:</span>
                <span class="text-gray-900 dark:text-gray-100">{{ adDetails.title }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600 dark:text-gray-400">Description:</span>
                <span class="text-gray-900 dark:text-gray-100">{{ adDetails.description }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600 dark:text-gray-400">Location:</span>
                <span class="text-gray-900 dark:text-gray-100">{{ adDetails.location }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600 dark:text-gray-400">Duration:</span>
                <span class="text-gray-900 dark:text-gray-100"
                  >{{ getDuration(adDetails.start_date, adDetails.end_date) }} days</span
                >
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600 dark:text-gray-400">Start Date:</span>
                <span class="text-gray-900 dark:text-gray-100">{{
                  formatDate(adDetails.start_date)
                }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600 dark:text-gray-400">End Date:</span>
                <span class="text-gray-900 dark:text-gray-100">{{
                  formatDate(adDetails.end_date)
                }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600 dark:text-gray-400">Payment Method:</span>
                <span class="text-gray-900 dark:text-gray-100">{{ paymentMethod }}</span>
              </div>
              <div
                class="flex justify-between border-t pt-2 mt-2 border-gray-200 dark:border-gray-600"
              >
                <span class="text-gray-800 dark:text-gray-200 font-medium">Total:</span>
                <span class="text-gray-900 dark:text-gray-100 font-bold"
                  >${{ adDetails.cost }}</span
                >
              </div>
            </div>
          </div>

          <!-- Next Steps -->
          <div class="space-y-4">
            <p class="text-gray-700 dark:text-gray-300">What's Next?</p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
              <NuxtLink
                to="/c/ad-spots/subscriptions"
                class="px-4 py-2 bg-[#0072ff] text-white rounded-md hover:bg-[#0054bb] transition-colors duration-200"
              >
                View My Subscriptions
              </NuxtLink>
              <NuxtLink
                to="/c/dashboard"
                class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200"
              >
                Return to Dashboard
              </NuxtLink>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Step Navigation -->
    <div class="px-6 py-4 bg-gray-50 dark:bg-gray-700 flex justify-between">
      <button
        v-if="currentStep > 0"
        @click="prevStep"
        class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200"
      >
        Back
      </button>
      <div v-else></div>

      <button
        @click="nextStep"
        :disabled="!canProceed"
        class="px-4 py-2 bg-[#0072ff] text-white rounded-md hover:bg-[#0054bb] transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {{ currentStep === steps.length - 1 ? 'Finish' : 'Next' }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue';
import { useAds } from '~/composables/useAds';
import { usePaymentProcessing } from '~/composables/usePaymentProcessing';
import { useUserProfile } from '~/composables/useUserProfile';

// Initialize composables
const { createAd, isLoading, error } = useAds();
const { paymentMethod, processPayment } = usePaymentProcessing();
const { userProfile } = useUserProfile();

// Get user's country for payment method filtering
const country = computed(() => userProfile.value?.country || '');

// Define steps
const steps = [
  { label: 'Ad Details' },
  { label: 'Payment Method' },
  { label: 'Process Payment' },
  { label: 'Confirmation' },
];

// State
const currentStep = ref(0);
const adDetails = reactive({
  client_id: '',
  client_name: '',
  cost: '',
  title: '',
  description: '',
  image_url: '',
  link_url: '',
  start_date: '',
  end_date: '',
});
const errors = reactive({
  title: '',
  description: '',
  image_url: '',
  cost: '',
  client_id: '',
  start_date: '',
  end_date: '',
});
const imagePreview = ref('');
const acceptedTerms = ref(false);
const termsError = ref('');
const paymentStatus = ref<'idle' | 'processing' | 'success' | 'error'>('idle');
const paymentError = ref('');
const orderId = ref('');

// Computed properties
const canProceed = computed(() => {
  switch (currentStep.value) {
    case 0:
      return (
        adDetails.title &&
        adDetails.description &&
        adDetails.image_url &&
        adDetails.cost &&
        adDetails.client_id &&
        adDetails.start_date &&
        adDetails.end_date
      );
    case 1:
      return !!paymentMethod.value && acceptedTerms.value;
    case 2:
      return paymentStatus.value === 'success';
    case 3:
      return true;
    default:
      return false;
  }
});

// Methods
const nextStep = async () => {
  if (currentStep.value < steps.length - 1 && canProceed.value) {
    // Validate current step before proceeding
    if (validateCurrentStep()) {
      if (currentStep.value === 0) {
        // Create the ad in Firestore
        await createAd({ ...adDetails, status: 'pending' });
      }
      currentStep.value++;
    }
  }
};

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--;
  }
};

const validateCurrentStep = () => {
  // Clear previous errors
  Object.keys(errors).forEach(key => {
    errors[key] = '';
  });
  termsError.value = '';

  // Validate based on current step
  switch (currentStep.value) {
    case 0: // Ad Details
      let isValid = true;

      // Validate title
      if (!adDetails.title.trim()) {
        errors.title = 'Title is required';
        isValid = false;
      } else if (adDetails.title.length > 100) {
        errors.title = 'Title must be less than 100 characters';
        isValid = false;
      }

      // Validate description
      if (!adDetails.description.trim()) {
        errors.description = 'Description is required';
        isValid = false;
      } else if (adDetails.description.length > 500) {
        errors.description = 'Description must be less than 500 characters';
        isValid = false;
      }

      // Validate image
      if (!adDetails.image_url) {
        errors.image_url = 'Image is required';
        isValid = false;
      }

      // Validate cost
      if (!adDetails.cost) {
        errors.cost = 'Cost is required';
        isValid = false;
      }

      // Validate client ID
      if (!adDetails.client_id) {
        errors.client_id = 'Client ID is required';
        isValid = false;
      }

      // Validate start date
      if (!adDetails.start_date) {
        errors.start_date = 'Start date is required';
        isValid = false;
      }

      // Validate end date
      if (!adDetails.end_date) {
        errors.end_date = 'End date is required';
        isValid = false;
      }

      return isValid;
    case 1: // Payment Method
      if (!paymentMethod.value) {
        return false;
      }

      if (!acceptedTerms.value) {
        termsError.value = 'You must accept the terms and conditions to proceed';
        return false;
      }

      return true;
    default:
      return true;
  }
};

const handleImageUpload = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    const file = input.files[0];

    // Validate file type
    if (!file.type.match('image.*')) {
      errors.image_url = 'Please upload an image file';
      return;
    }

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      errors.image_url = 'Image size should be less than 5MB';
      return;
    }

    // Simulate upload and get URL (replace with actual upload logic)
    const reader = new FileReader();
    reader.onload = e => {
      adDetails.image_url = e.target?.result as string;
      imagePreview.value = adDetails.image_url;
    };
    reader.readAsDataURL(file);

    // Clear any previous error
    errors.image_url = '';
  }
};

const removeImage = () => {
  adDetails.image_url = '';
  imagePreview.value = '';
};

const handlePayment = async () => {
  try {
    // Set payment status to processing
    paymentStatus.value = 'processing';

    // Create payment payload
    const paymentPayload = {
      amount: adDetails.cost,
      currency: 'USD',
      paymentMethod: paymentMethod.value,
      description: `Ad: ${adDetails.title}`,
      metadata: { title: adDetails.title },
    };

    // Process payment
    const result = await processPayment(paymentPayload);

    // Handle success
    if (result.success) {
      paymentStatus.value = 'success';
      orderId.value = result.paymentId || `ORD-${Date.now()}`;

      // Wait a moment before proceeding to confirmation
      setTimeout(() => {
        nextStep();
      }, 1500);
    } else {
      // Handle error
      paymentStatus.value = 'error';
      paymentError.value = result.error || 'Payment processing failed';
    }
  } catch (error) {
    // Handle exception
    paymentStatus.value = 'error';
    paymentError.value = error instanceof Error ? error.message : 'An unexpected error occurred';
  }
};

const setPaymentMethod = (method: 'stripe' | 'paypal' | 'payfast') => {
  paymentMethod.value = method;
};

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(date);
};

const getDuration = (startDate: string, endDate: string) => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const diffTime = Math.abs(end.getTime() - start.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
};

// Fetch ad spots on mount
onMounted(async () => {
  // await fetchAdSpots()
});
</script>
