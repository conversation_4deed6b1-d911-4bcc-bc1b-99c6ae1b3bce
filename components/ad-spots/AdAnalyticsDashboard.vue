<template>
  <div class="ad-analytics-dashboard">
    <!-- Header with date range selector -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Ad Performance Analytics</h1>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Track the performance of your advertising spots
        </p>
      </div>
      
      <!-- Date Range Selector -->
      <div class="mt-4 md:mt-0 flex space-x-2">
        <div class="flex space-x-2 items-center">
          <label for="start-date" class="text-sm text-gray-700 dark:text-gray-300">From</label>
          <input
            id="start-date"
            v-model="startDateInput"
            type="date"
            class="px-2 py-1 border border-gray-300 dark:border-gray-700 rounded-md text-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
          />
        </div>
        <div class="flex space-x-2 items-center">
          <label for="end-date" class="text-sm text-gray-700 dark:text-gray-300">To</label>
          <input
            id="end-date"
            v-model="endDateInput"
            type="date"
            class="px-2 py-1 border border-gray-300 dark:border-gray-700 rounded-md text-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
          />
        </div>
        <button
          @click="applyDateRange"
          class="px-3 py-1 bg-[#0072ff] text-white rounded-md text-sm hover:bg-[#0054bb] transition-colors duration-200"
        >
          Apply
        </button>
      </div>
    </div>
    
    <!-- Date Range Presets -->
    <div class="flex flex-wrap gap-2 mb-6">
      <button
        v-for="preset in datePresets"
        :key="preset.label"
        @click="selectDatePreset(preset.days)"
        class="px-3 py-1 text-sm rounded-md transition-colors duration-200"
        :class="activeDatePreset === preset.days ? 'bg-[#0072ff] text-white' : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'"
      >
        {{ preset.label }}
      </button>
    </div>
    
    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#0072ff]"></div>
    </div>
    
    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-md mb-6">
      <p>{{ error }}</p>
      <button 
        @click="fetchData" 
        class="mt-2 text-sm font-medium text-red-700 dark:text-red-300 hover:text-red-900 dark:hover:text-red-200"
      >
        Try Again
      </button>
    </div>
    
    <!-- Empty State -->
    <div v-else-if="!hasData" class="text-center py-12 bg-gray-50 dark:bg-gray-800 rounded-lg">
      <Icon name="mdi:chart-line" class="h-16 w-16 text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">No Analytics Data Available</h3>
      <p class="mt-2 text-gray-500 dark:text-gray-400">
        There is no analytics data available for your ad spots in the selected date range.
      </p>
      <p class="mt-1 text-gray-500 dark:text-gray-400">
        Try selecting a different date range or check back later.
      </p>
    </div>
    
    <!-- Analytics Content -->
    <div v-else>
      <!-- Summary Metrics Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Views</h3>
          <p class="text-2xl font-bold text-gray-800 dark:text-gray-100">{{ formatNumber(summaryMetrics.views) }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ formatNumber(summaryMetrics.views / daysBetween) }} per day
          </p>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Clicks</h3>
          <p class="text-2xl font-bold text-gray-800 dark:text-gray-100">{{ formatNumber(summaryMetrics.clicks) }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ formatNumber(summaryMetrics.ctr, { style: 'percent', maximumFractionDigits: 2 }) }} CTR
          </p>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Conversions</h3>
          <p class="text-2xl font-bold text-gray-800 dark:text-gray-100">{{ formatNumber(summaryMetrics.conversions) }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ formatNumber(summaryMetrics.conversionRate, { style: 'percent', maximumFractionDigits: 2 }) }} conversion rate
          </p>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">ROI</h3>
          <p class="text-2xl font-bold" :class="summaryMetrics.roi >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
            {{ formatNumber(summaryMetrics.roi, { style: 'percent', maximumFractionDigits: 1 }) }}
          </p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            Cost: {{ formatNumber(summaryMetrics.cost, { style: 'currency', currency: 'USD' }) }}
          </p>
        </div>
      </div>
      
      <!-- Performance Chart -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4 mb-8">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-medium text-gray-800 dark:text-gray-200">Performance Over Time</h2>
          <div class="flex space-x-2">
            <button
              v-for="interval in chartIntervals"
              :key="interval.value"
              @click="chartInterval = interval.value"
              class="px-3 py-1 text-sm rounded-md transition-colors duration-200"
              :class="chartInterval === interval.value ? 'bg-[#0072ff] text-white' : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'"
            >
              {{ interval.label }}
            </button>
          </div>
        </div>
        <div class="h-64">
          <canvas ref="performanceChartRef"></canvas>
        </div>
      </div>
      
      <!-- Performance by Ad Spot -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4 mb-8">
        <h2 class="text-lg font-medium text-gray-800 dark:text-gray-200 mb-4">Performance by Ad Spot</h2>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead>
              <tr>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Ad Spot</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Views</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Clicks</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">CTR</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Conversions</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Cost</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">ROI</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
              <tr v-for="spot in performanceByAdSpot" :key="spot.subscription_id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                <td class="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">{{ spot.ad_spot_name }}</td>
                <td class="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">{{ formatNumber(spot.views) }}</td>
                <td class="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">{{ formatNumber(spot.clicks) }}</td>
                <td class="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">{{ formatNumber(spot.ctr, { style: 'percent', maximumFractionDigits: 2 }) }}</td>
                <td class="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">{{ formatNumber(spot.conversions) }}</td>
                <td class="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">{{ formatNumber(spot.cost, { style: 'currency', currency: 'USD' }) }}</td>
                <td class="px-4 py-3 text-sm" :class="spot.roi >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
                  {{ formatNumber(spot.roi, { style: 'percent', maximumFractionDigits: 1 }) }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      
      <!-- Export Controls -->
      <div class="flex justify-end mb-8">
        <button
          @click="exportData"
          class="bg-[#0072ff] text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-[#0054bb] focus:outline-none focus:ring-2 focus:ring-[#0072ff] focus:ring-offset-2"
        >
          Export Data
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useAdAnalytics } from '~/composables/useAdAnalytics'
import Chart from 'chart.js/auto'
import moment from 'moment'

// Props
const props = defineProps({
  userId: {
    type: String,
    default: null
  },
  subscriptionId: {
    type: String,
    default: null
  }
})

// Analytics composable
const {
  isLoading,
  error,
  startDate,
  endDate,
  dailyStats,
  summaryMetrics,
  fetchSubscriptionAnalytics,
  fetchUserAnalytics,
  getTimeSeriesData,
  getPerformanceByAdSpot,
  setDateRange,
  exportAsCSV
} = useAdAnalytics()

// Chart references
const performanceChartRef = ref<HTMLCanvasElement | null>(null)

// Chart instances
let performanceChart: Chart | null = null

// Chart interval
const chartInterval = ref<'daily' | 'weekly' | 'monthly'>('daily')

// Date inputs
const startDateInput = ref(moment(startDate.value).format('YYYY-MM-DD'))
const endDateInput = ref(moment(endDate.value).format('YYYY-MM-DD'))

// Active date preset
const activeDatePreset = ref(30) // Default to 30 days

// Date presets
const datePresets = [
  { label: 'Last 7 days', days: 7 },
  { label: 'Last 30 days', days: 30 },
  { label: 'Last 90 days', days: 90 },
  { label: 'Year to date', days: 'ytd' }
]

// Chart intervals
const chartIntervals = [
  { label: 'Daily', value: 'daily' },
  { label: 'Weekly', value: 'weekly' },
  { label: 'Monthly', value: 'monthly' }
]

// Computed properties
const hasData = computed(() => dailyStats.value.length > 0)

const performanceByAdSpot = computed(() => getPerformanceByAdSpot())

const daysBetween = computed(() => {
  const start = moment(startDate.value)
  const end = moment(endDate.value)
  return end.diff(start, 'days') + 1
})

// Methods
const fetchData = async () => {
  if (props.subscriptionId) {
    await fetchSubscriptionAnalytics(props.subscriptionId)
  } else if (props.userId) {
    await fetchUserAnalytics(props.userId)
  }
  
  updateCharts()
}

const selectDatePreset = (days: number | string) => {
  const end = new Date()
  let start: Date
  
  if (days === 'ytd') {
    // Year to date
    start = new Date(end.getFullYear(), 0, 1) // January 1st of current year
    activeDatePreset.value = 'ytd'
  } else {
    // Last X days
    start = new Date()
    start.setDate(end.getDate() - (days as number))
    activeDatePreset.value = days as number
  }
  
  startDateInput.value = moment(start).format('YYYY-MM-DD')
  endDateInput.value = moment(end).format('YYYY-MM-DD')
  
  setDateRange(start, end)
  fetchData()
}

const applyDateRange = () => {
  const start = new Date(startDateInput.value)
  const end = new Date(endDateInput.value)
  
  // Reset active preset
  activeDatePreset.value = null
  
  setDateRange(start, end)
  fetchData()
}

const initCharts = () => {
  if (performanceChartRef.value) {
    const ctx = performanceChartRef.value.getContext('2d')
    if (!ctx) return
    
    performanceChart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: [],
        datasets: []
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    })
  }
}

const updateCharts = () => {
  if (!performanceChart) return
  
  // Get time series data
  const viewsData = getTimeSeriesData('views', chartInterval.value)
  const clicksData = getTimeSeriesData('clicks', chartInterval.value)
  const conversionsData = getTimeSeriesData('conversions', chartInterval.value)
  
  // Update performance chart
  performanceChart.data.labels = viewsData.map(item => item.date)
  performanceChart.data.datasets = [
    {
      label: 'Views',
      data: viewsData.map(item => item.value),
      borderColor: '#3B82F6',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      tension: 0.1
    },
    {
      label: 'Clicks',
      data: clicksData.map(item => item.value),
      borderColor: '#10B981',
      backgroundColor: 'rgba(16, 185, 129, 0.1)',
      tension: 0.1
    },
    {
      label: 'Conversions',
      data: conversionsData.map(item => item.value),
      borderColor: '#F59E0B',
      backgroundColor: 'rgba(245, 158, 11, 0.1)',
      tension: 0.1
    }
  ]
  
  performanceChart.update()
}

const formatNumber = (value: number, options: Intl.NumberFormatOptions = {}) => {
  if (value === undefined || value === null) return '0'
  
  if (options.style === 'percent') {
    value = value / 100
  }
  
  return new Intl.NumberFormat('en-US', options).format(value)
}

const exportData = () => {
  const csvContent = exportAsCSV()
  if (!csvContent) return
  
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.setAttribute('href', url)
  link.setAttribute('download', `ad_analytics_${moment(startDate.value).format('YYYY-MM-DD')}_to_${moment(endDate.value).format('YYYY-MM-DD')}.csv`)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// Watch for changes in chart interval
watch(chartInterval, () => {
  updateCharts()
})

// Fetch data and initialize charts on mount
onMounted(async () => {
  await fetchData()
  initCharts()
})
</script>

<style scoped>
.ad-analytics-dashboard {
  @apply p-6 bg-gray-50 dark:bg-gray-900 rounded-lg;
}
</style>
