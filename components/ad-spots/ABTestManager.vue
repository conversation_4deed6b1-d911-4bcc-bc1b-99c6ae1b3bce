<template>
  <div class="ab-test-manager">
    <!-- Header -->
    <div class="manager-header">
      <div>
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">A/B Testing</h2>
        <p class="text-gray-600 dark:text-gray-400 mt-1">
          Create and manage split tests to optimize your ad performance
        </p>
      </div>
      <button @click="showCreateModal = true" class="btn-primary">
        <Icon name="ph:plus" />
        Create Test
      </button>
    </div>

    <!-- Active Tests -->
    <div v-if="activeTests.length > 0" class="tests-section">
      <h3 class="section-title">Active Tests</h3>
      <div class="tests-grid">
        <div
          v-for="test in activeTests"
          :key="test.id"
          class="test-card active"
        >
          <div class="test-header">
            <h4 class="test-name">{{ test.name }}</h4>
            <div class="test-status active">
              <Icon name="ph:circle-fill" size="8" />
              Running
            </div>
          </div>

          <div class="test-content">
            <!-- Progress Bar -->
            <div class="progress-section">
              <div class="progress-info">
                <span class="text-sm text-gray-600 dark:text-gray-400">
                  {{ formatNumber(test.metrics.totalImpressions) }} impressions
                </span>
                <span class="text-sm text-gray-600 dark:text-gray-400">
                  {{ getTestProgress(test) }}% to significance
                </span>
              </div>
              <div class="progress-bar">
                <div
                  class="progress-fill"
                  :style="{ width: `${getTestProgress(test)}%` }"
                />
              </div>
            </div>

            <!-- Variations Performance -->
            <div class="variations-grid">
              <div
                v-for="variation in getTestVariations(test)"
                :key="variation.id"
                class="variation-card"
                :class="{ winner: variation.isWinning }"
              >
                <div class="variation-header">
                  <span class="variation-name">{{ variation.name }}</span>
                  <Icon
                    v-if="variation.isWinning"
                    name="ph:crown"
                    class="text-yellow-500"
                  />
                </div>
                <div class="variation-metrics">
                  <div class="metric">
                    <span class="metric-value">{{ variation.conversionRate }}%</span>
                    <span class="metric-label">Conversion</span>
                  </div>
                  <div class="metric">
                    <span class="metric-value">{{ variation.confidence }}%</span>
                    <span class="metric-label">Confidence</span>
                  </div>
                </div>
                <div
                  v-if="variation.improvement"
                  class="improvement"
                  :class="variation.improvement > 0 ? 'positive' : 'negative'"
                >
                  <Icon :name="variation.improvement > 0 ? 'ph:arrow-up' : 'ph:arrow-down'" />
                  {{ Math.abs(variation.improvement) }}%
                </div>
              </div>
            </div>
          </div>

          <div class="test-actions">
            <button @click="viewTestDetails(test)" class="action-btn">
              <Icon name="ph:chart-line" />
              View Details
            </button>
            <button @click="pauseTest(test)" class="action-btn">
              <Icon name="ph:pause" />
              Pause
            </button>
            <button
              v-if="test.winner"
              @click="applyWinner(test)"
              class="action-btn primary"
            >
              <Icon name="ph:check" />
              Apply Winner
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Completed Tests -->
    <div v-if="completedTests.length > 0" class="tests-section">
      <h3 class="section-title">Completed Tests</h3>
      <div class="tests-table">
        <table>
          <thead>
            <tr>
              <th>Test Name</th>
              <th>Duration</th>
              <th>Impressions</th>
              <th>Winner</th>
              <th>Improvement</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="test in completedTests" :key="test.id">
              <td class="font-medium">{{ test.name }}</td>
              <td>{{ formatDuration(test) }}</td>
              <td>{{ formatNumber(test.metrics.totalImpressions) }}</td>
              <td>
                <span v-if="test.winner" class="winner-badge">
                  {{ getWinnerName(test) }}
                </span>
                <span v-else class="no-winner">No winner</span>
              </td>
              <td>
                <span
                  v-if="getWinnerImprovement(test)"
                  class="improvement-badge"
                  :class="getWinnerImprovement(test) > 0 ? 'positive' : 'negative'"
                >
                  {{ getWinnerImprovement(test) > 0 ? '+' : '' }}{{ getWinnerImprovement(test) }}%
                </span>
                <span v-else>-</span>
              </td>
              <td>
                <div class="table-actions">
                  <button @click="viewTestDetails(test)" class="table-action-btn">
                    <Icon name="ph:eye" />
                  </button>
                  <button @click="duplicateTest(test)" class="table-action-btn">
                    <Icon name="ph:copy" />
                  </button>
                  <button @click="deleteTest(test)" class="table-action-btn danger">
                    <Icon name="ph:trash" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="!activeTests.length && !completedTests.length && !isLoading" class="empty-state">
      <Icon name="ph:chart-line" size="48" />
      <h3>No A/B Tests Yet</h3>
      <p>Create your first test to start optimizing your ad performance</p>
      <button @click="showCreateModal = true" class="btn-primary mt-4">
        Create Your First Test
      </button>
    </div>

    <!-- Create Test Modal -->
    <Teleport to="body">
      <Transition name="modal">
        <div v-if="showCreateModal" class="modal-overlay" @click="showCreateModal = false">
          <div class="modal-content" @click.stop>
            <div class="modal-header">
              <h3>Create A/B Test</h3>
              <button @click="showCreateModal = false" class="close-btn">
                <Icon name="ph:x" />
              </button>
            </div>

            <div class="modal-body">
              <form @submit.prevent="createTest" class="test-form">
                <div class="form-group">
                  <label>Test Name</label>
                  <input
                    v-model="newTest.name"
                    type="text"
                    placeholder="e.g., Homepage Banner CTA Test"
                    required
                    class="form-input"
                  />
                </div>

                <div class="form-group">
                  <label>Description</label>
                  <textarea
                    v-model="newTest.description"
                    placeholder="What are you testing?"
                    rows="3"
                    class="form-textarea"
                  />
                </div>

                <div class="form-group">
                  <label>Ad to Test</label>
                  <select v-model="newTest.adId" required class="form-select">
                    <option value="">Select an ad</option>
                    <option v-for="ad in availableAds" :key="ad.id" :value="ad.id">
                      {{ ad.title }}
                    </option>
                  </select>
                </div>

                <div class="variations-section">
                  <div class="section-header">
                    <h4>Test Variations</h4>
                    <button
                      type="button"
                      @click="addVariation"
                      class="btn-secondary small"
                    >
                      <Icon name="ph:plus" />
                      Add Variation
                    </button>
                  </div>

                  <div class="variations-list">
                    <div
                      v-for="(variation, index) in newTest.variations"
                      :key="variation.id"
                      class="variation-form"
                    >
                      <div class="variation-header">
                        <input
                          v-model="variation.name"
                          type="text"
                          :placeholder="`Variation ${index === 0 ? 'A (Control)' : String.fromCharCode(65 + index)}`"
                          required
                          class="variation-name-input"
                        />
                        <button
                          v-if="index > 1"
                          type="button"
                          @click="removeVariation(index)"
                          class="remove-btn"
                        >
                          <Icon name="ph:x" />
                        </button>
                      </div>

                      <div class="traffic-allocation">
                        <label>Traffic Allocation</label>
                        <div class="allocation-input">
                          <input
                            v-model.number="variation.trafficAllocation"
                            type="number"
                            min="0"
                            max="100"
                            required
                            class="form-input"
                          />
                          <span class="allocation-suffix">%</span>
                        </div>
                      </div>

                      <div v-if="index === 0" class="control-badge">
                        Control Group
                      </div>
                    </div>
                  </div>

                  <div
                    v-if="totalTrafficAllocation !== 100"
                    class="allocation-warning"
                  >
                    <Icon name="ph:warning" />
                    Traffic allocation must equal 100% (currently {{ totalTrafficAllocation }}%)
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-group">
                    <label>Minimum Sample Size</label>
                    <input
                      v-model.number="newTest.minimumSampleSize"
                      type="number"
                      min="100"
                      placeholder="1000"
                      class="form-input"
                    />
                  </div>

                  <div class="form-group">
                    <label>Confidence Threshold</label>
                    <select v-model.number="newTest.confidenceThreshold" class="form-select">
                      <option :value="0.90">90%</option>
                      <option :value="0.95">95% (Recommended)</option>
                      <option :value="0.99">99%</option>
                    </select>
                  </div>
                </div>

                <div class="form-actions">
                  <button type="button" @click="showCreateModal = false" class="btn-secondary">
                    Cancel
                  </button>
                  <button
                    type="submit"
                    :disabled="!isValidTest || isCreating"
                    class="btn-primary"
                  >
                    <Icon v-if="isCreating" name="ph:spinner" class="animate-spin" />
                    <span v-else>Create Test</span>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </Transition>
    </Teleport>

    <!-- Test Details Modal -->
    <!-- ABTestDetails component not implemented yet
    <Teleport to="body">
      <Transition name="modal">
        <ABTestDetails
          v-if="selectedTest"
          :test="selectedTest"
          @close="selectedTest = null"
          @update="refreshTests"
        />
      </Transition>
    </Teleport>
    -->
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useABTesting } from '~/composables/useABTesting';
import { useAdContentManagement } from '~/composables/useAdContentManagement';
import type { ABTest } from '~/types/ad-content';
// import ABTestDetails from './ABTestDetails.vue'; // Component not implemented yet

const props = defineProps<{
  adId?: string;
}>();

const {
  abTests,
  activeTests,
  completedTests,
  isLoading,
  createABTest,
  stopTest,
  fetchTestsForAd,
} = useABTesting();

const { adContents, fetchAds } = useAdContentManagement();

// State
const showCreateModal = ref(false);
const selectedTest = ref<ABTest | null>(null);
const isCreating = ref(false);

// New test form
const newTest = ref({
  name: '',
  description: '',
  adId: props.adId || '',
  variations: [
    { id: 'control', name: 'Original', trafficAllocation: 50 },
    { id: 'variant1', name: 'Variant A', trafficAllocation: 50 },
  ],
  minimumSampleSize: 1000,
  confidenceThreshold: 0.95,
});

// Computed
const availableAds = computed(() => adContents.value.filter(ad => ad.status === 'active'));

const totalTrafficAllocation = computed(() =>
  newTest.value.variations.reduce((sum, v) => sum + v.trafficAllocation, 0)
);

const isValidTest = computed(() => {
  return (
    newTest.value.name &&
    newTest.value.adId &&
    newTest.value.variations.length >= 2 &&
    totalTrafficAllocation.value === 100 &&
    newTest.value.variations.every(v => v.name && v.trafficAllocation > 0)
  );
});

// Methods
const getTestProgress = (test: ABTest): number => {
  const minSample = test.minimumSampleSize || 1000;
  const current = test.metrics.totalImpressions;
  return Math.min(100, Math.round((current / minSample) * 100));
};

const getTestVariations = (test: ABTest) => {
  return test.variations.map(variation => {
    const metrics = test.metrics.variationMetrics[variation.variationId] || {
      impressions: 0,
      clicks: 0,
      conversions: 0,
      ctr: 0,
      conversionRate: 0,
      confidenceLevel: 0,
    };

    const isControl = test.variations[0].variationId === variation.variationId;
    const controlMetrics = test.metrics.variationMetrics[test.variations[0].variationId];
    
    let improvement = 0;
    if (!isControl && controlMetrics && controlMetrics.conversions > 0) {
      improvement = ((metrics.conversionRate - controlMetrics.conversionRate) / controlMetrics.conversionRate) * 100;
    }

    return {
      id: variation.variationId,
      name: variation.name,
      conversionRate: (metrics.conversionRate * 100).toFixed(2),
      confidence: Math.round((metrics.confidenceLevel || 0) * 100),
      improvement: improvement ? Math.round(improvement) : null,
      isWinning: test.winner === variation.variationId,
    };
  });
};

const formatDuration = (test: ABTest): string => {
  if (!test.startDate || !test.endDate) return '-';
  const start = test.startDate.toDate ? test.startDate.toDate() : new Date(test.startDate);
  const end = test.endDate.toDate ? test.endDate.toDate() : new Date(test.endDate);
  const days = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
  return `${days} days`;
};

const formatNumber = (num: number): string => {
  return new Intl.NumberFormat().format(num);
};

const getWinnerName = (test: ABTest): string => {
  if (!test.winner) return '';
  const variation = test.variations.find(v => v.variationId === test.winner);
  return variation?.name || '';
};

const getWinnerImprovement = (test: ABTest): number | null => {
  if (!test.winner) return null;
  const variations = getTestVariations(test);
  const winner = variations.find(v => v.id === test.winner);
  return winner?.improvement || null;
};

const addVariation = () => {
  const variantNumber = newTest.value.variations.length;
  const remainingAllocation = 100 - totalTrafficAllocation.value;
  
  newTest.value.variations.push({
    id: `variant${variantNumber}`,
    name: `Variant ${String.fromCharCode(64 + variantNumber)}`,
    trafficAllocation: remainingAllocation > 0 ? remainingAllocation : 0,
  });
};

const removeVariation = (index: number) => {
  newTest.value.variations.splice(index, 1);
};

const createTest = async () => {
  if (!isValidTest.value) return;

  isCreating.value = true;
  try {
    await createABTest(newTest.value.adId, {
      name: newTest.value.name,
      description: newTest.value.description,
      variations: newTest.value.variations.map((v, index) => ({
        name: v.name,
        variationId: v.id,
        trafficAllocation: v.trafficAllocation,
      })),
      minimumSampleSize: newTest.value.minimumSampleSize,
      confidenceThreshold: newTest.value.confidenceThreshold,
    });

    showCreateModal.value = false;
    resetForm();
  } catch (error) {
    console.error('Failed to create test:', error);
  } finally {
    isCreating.value = false;
  }
};

const resetForm = () => {
  newTest.value = {
    name: '',
    description: '',
    adId: props.adId || '',
    variations: [
      { id: 'control', name: 'Original', trafficAllocation: 50 },
      { id: 'variant1', name: 'Variant A', trafficAllocation: 50 },
    ],
    minimumSampleSize: 1000,
    confidenceThreshold: 0.95,
  };
};

const viewTestDetails = (test: ABTest) => {
  selectedTest.value = test;
};

const pauseTest = async (test: ABTest) => {
  if (confirm('Are you sure you want to pause this test?')) {
    await stopTest(test.id);
  }
};

const applyWinner = async (test: ABTest) => {
  // TODO: Implement applying winner variation
  console.log('Apply winner:', test.winner);
};

const duplicateTest = async (test: ABTest) => {
  // TODO: Implement test duplication
  console.log('Duplicate test:', test);
};

const deleteTest = async (test: ABTest) => {
  if (confirm(`Are you sure you want to delete "${test.name}"?`)) {
    // TODO: Implement test deletion
    console.log('Delete test:', test);
  }
};

const refreshTests = async () => {
  if (props.adId) {
    await fetchTestsForAd(props.adId);
  }
};

// Load data on mount
onMounted(async () => {
  await fetchAds();
  if (props.adId) {
    await fetchTestsForAd(props.adId);
  }
});
</script>

<style scoped>
.ab-test-manager {
  @apply space-y-6;
}

.manager-header {
  @apply flex items-start justify-between;
}

/* Sections */
.tests-section {
  @apply space-y-4;
}

.section-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

/* Test Cards */
.tests-grid {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-4;
}

.test-card {
  @apply bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 space-y-4;
}

.test-card.active {
  @apply border-blue-200 dark:border-blue-800;
}

.test-header {
  @apply flex items-center justify-between;
}

.test-name {
  @apply font-semibold text-gray-900 dark:text-white;
}

.test-status {
  @apply flex items-center gap-1 text-sm font-medium px-2 py-1 rounded-full;
}

.test-status.active {
  @apply bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300;
}

/* Progress */
.progress-section {
  @apply space-y-2;
}

.progress-info {
  @apply flex items-center justify-between text-sm;
}

.progress-bar {
  @apply w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden;
}

.progress-fill {
  @apply h-full bg-blue-500 transition-all duration-300;
}

/* Variations */
.variations-grid {
  @apply grid grid-cols-2 gap-2;
}

.variation-card {
  @apply bg-gray-50 dark:bg-gray-700 rounded-lg p-3 space-y-2;
}

.variation-card.winner {
  @apply bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700;
}

.variation-header {
  @apply flex items-center justify-between;
}

.variation-name {
  @apply font-medium text-sm text-gray-900 dark:text-white;
}

.variation-metrics {
  @apply flex items-center gap-4;
}

.metric {
  @apply text-center;
}

.metric-value {
  @apply block text-lg font-semibold text-gray-900 dark:text-white;
}

.metric-label {
  @apply block text-xs text-gray-500 dark:text-gray-400;
}

.improvement {
  @apply flex items-center gap-1 text-sm font-medium;
}

.improvement.positive {
  @apply text-green-600 dark:text-green-400;
}

.improvement.negative {
  @apply text-red-600 dark:text-red-400;
}

/* Test Actions */
.test-actions {
  @apply flex items-center gap-2;
}

.action-btn {
  @apply flex items-center gap-1 px-3 py-1.5 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors;
}

.action-btn.primary {
  @apply bg-blue-500 text-white hover:bg-blue-600;
}

/* Table */
.tests-table {
  @apply overflow-x-auto;
}

.tests-table table {
  @apply w-full;
}

.tests-table th {
  @apply text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider p-3 bg-gray-50 dark:bg-gray-700;
}

.tests-table td {
  @apply p-3 text-sm text-gray-900 dark:text-white border-t border-gray-200 dark:border-gray-700;
}

.winner-badge {
  @apply inline-flex items-center px-2 py-1 text-xs font-medium bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 rounded-full;
}

.no-winner {
  @apply text-gray-500 dark:text-gray-400;
}

.improvement-badge {
  @apply font-medium;
}

.improvement-badge.positive {
  @apply text-green-600 dark:text-green-400;
}

.improvement-badge.negative {
  @apply text-red-600 dark:text-red-400;
}

.table-actions {
  @apply flex items-center gap-1;
}

.table-action-btn {
  @apply p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors;
}

.table-action-btn.danger {
  @apply hover:bg-red-100 dark:hover:bg-red-900 hover:text-red-600 dark:hover:text-red-400;
}

/* Empty State */
.empty-state {
  @apply text-center py-12 text-gray-500 dark:text-gray-400;
}

.empty-state h3 {
  @apply text-lg font-semibold text-gray-900 dark:text-white mt-4 mb-2;
}

/* Modal */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.modal-content {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto;
}

.modal-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700;
}

.modal-header h3 {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.close-btn {
  @apply p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors;
}

.modal-body {
  @apply p-6;
}

/* Form */
.test-form {
  @apply space-y-6;
}

.form-group {
  @apply space-y-1;
}

.form-group label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.form-input,
.form-select,
.form-textarea {
  @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

.form-row {
  @apply grid grid-cols-2 gap-4;
}

/* Variations Form */
.variations-section {
  @apply space-y-3;
}

.section-header {
  @apply flex items-center justify-between;
}

.section-header h4 {
  @apply font-medium text-gray-900 dark:text-white;
}

.variations-list {
  @apply space-y-3;
}

.variation-form {
  @apply bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-3;
}

.variation-header {
  @apply flex items-center gap-2;
}

.variation-name-input {
  @apply flex-1 px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-sm;
}

.remove-btn {
  @apply p-1 text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900 rounded transition-colors;
}

.traffic-allocation {
  @apply space-y-1;
}

.traffic-allocation label {
  @apply text-xs text-gray-600 dark:text-gray-400;
}

.allocation-input {
  @apply flex items-center gap-1;
}

.allocation-input input {
  @apply w-20;
}

.allocation-suffix {
  @apply text-gray-500 dark:text-gray-400;
}

.control-badge {
  @apply inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-full;
}

.allocation-warning {
  @apply flex items-center gap-2 p-3 bg-yellow-50 dark:bg-yellow-900 text-yellow-700 dark:text-yellow-300 rounded-lg text-sm;
}

.form-actions {
  @apply flex items-center justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700;
}

/* Buttons */
.btn-primary {
  @apply flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors;
}

.btn-secondary.small {
  @apply px-3 py-1 text-sm;
}

/* Transitions */
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}
</style>