<template>
  <div
    class="relative rounded-lg overflow-hidden shadow-lg bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 border-2 border-yellow-200 dark:border-yellow-700"
  >
    <!-- Sponsored Badge -->
    <div class="absolute top-2 right-2 z-10">
      <span
        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-500 text-white"
      >
        <Icon name="mdi:star" class="mr-1 h-3 w-3" />
        Sponsored
      </span>
    </div>

    <!-- Ad Content -->
    <div class="relative h-96 flex flex-col">
      <!-- Image Section -->
      <div class="flex-1 relative overflow-hidden">
        <img
          v-if="getImageUrl(ad)"
          :src="getImageUrl(ad)"
          :alt="ad.name"
          class="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
          @error="handleImageError"
        />
        <div
          v-else
          class="w-full h-full bg-gradient-to-br from-yellow-200 to-yellow-300 dark:from-yellow-800 dark:to-yellow-900 flex items-center justify-center"
        >
          <div class="text-center">
            <Icon name="mdi:abacus" size="48" class="text-yellow-600 dark:text-yellow-400 mb-2" />
            <p class="text-yellow-700 dark:text-yellow-300 font-medium">{{ ad.name }}</p>
          </div>
        </div>

        <!-- Overlay for better text readability -->
        <div
          class="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"
        ></div>
      </div>

      <!-- Content Section -->
      <div class="absolute bottom-0 left-0 right-0 p-4 text-white">
        <h3 class="text-lg font-bold mb-1 line-clamp-2">{{ ad.name }}</h3>
        <p v-if="ad.description" class="text-sm opacity-90 line-clamp-2 mb-2">
          {{ ad.description }}
        </p>

        <!-- Ad Info -->
        <div class="flex items-center justify-between text-xs opacity-75">
          <span v-if="ad.advertiser_name">Advertiser: {{ ad.advertiser_name }}</span>
          <span v-if="ad.start_date && ad.end_date">{{ ad.start_date }} - {{ ad.end_date }}</span>
        </div>
      </div>
    </div>

    <!-- Click Handler -->
    <div
      class="absolute inset-0 cursor-pointer"
      :title="`View ${ad.name}`"
      @click="handleClick"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import type { IUnifiedAd } from '~/types/ads';

const props = defineProps<{ ad: IUnifiedAd }>();
const emit = defineEmits<{ (e: 'click', ad: IUnifiedAd): void }>();

// Track ad view when component is mounted
onMounted(() => {
  trackAdView();
});

// Get image URL from unified Ad entity
const getImageUrl = (ad: IUnifiedAd) => {
  if (ad.image_url) return ad.image_url;
  return null;
};

// Handle image loading errors
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  img.style.display = 'none';
};

// Handle click events
const handleClick = () => {
  // Track ad click for analytics
  trackAdClick();
  // Emit click event to parent
  emit('click', props.ad);
  // Navigate to target URL if available
  if (props.ad.target_url) {
    window.open(props.ad.target_url, '_blank');
  }
};

// Track ad view for analytics
const trackAdView = async () => {
  try {
    await $fetch('/api/analytics/ad-view', {
      method: 'POST',
      body: {
        ad_id: props.ad.id,
        advertiser_id: props.ad.advertiser_id,
        timestamp: new Date().toISOString(),
        user_agent: navigator.userAgent,
        referrer: document.referrer,
      },
    });
  } catch (error) {
    // Optionally handle error silently - analytics shouldn't break user experience
    console.warn('Failed to track ad view:', error);
  }
};

// Track ad click for analytics
const trackAdClick = async () => {
  try {
    await $fetch('/api/analytics/ad-click', {
      method: 'POST',
      body: {
        ad_id: props.ad.id,
        advertiser_id: props.ad.advertiser_id,
        timestamp: new Date().toISOString(),
        user_agent: navigator.userAgent,
        referrer: document.referrer,
      },
    });
  } catch (error) {
    // Optionally handle error silently - analytics shouldn't break user experience
    console.warn('Failed to track ad click:', error);
  }
};
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-clamp: 2;
}
</style>
