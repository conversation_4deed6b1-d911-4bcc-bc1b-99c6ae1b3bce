<template>
  <div class="ad-content-editor">
    <!-- Toolbar -->
    <div class="editor-toolbar bg-white border-b p-4 flex flex-wrap gap-2 items-center">
      <!-- Text Formatting -->
      <div class="toolbar-group border-r pr-4 mr-4">
        <button
          v-for="format in textFormats"
          :key="format.name"
          :class="[
            'toolbar-btn',
            { active: editor?.isActive(format.name) }
          ]"
          @click="format.action"
          :title="format.title"
        >
          <Icon :name="format.icon" />
        </button>
      </div>

      <!-- Headings -->
      <div class="toolbar-group border-r pr-4 mr-4">
        <select
          v-model="selectedHeading"
          @change="setHeading"
          class="toolbar-select"
        >
          <option value="">Paragraph</option>
          <option v-for="level in [1, 2, 3, 4, 5, 6]" :key="level" :value="level">
            Heading {{ level }}
          </option>
        </select>
      </div>

      <!-- Text Alignment -->
      <div class="toolbar-group border-r pr-4 mr-4">
        <button
          v-for="align in alignments"
          :key="align.name"
          :class="[
            'toolbar-btn',
            { active: editor?.isActive({ textAlign: align.name }) }
          ]"
          @click="() => editor?.chain().focus().setTextAlign(align.name).run()"
          :title="align.title"
        >
          <Icon :name="align.icon" />
        </button>
      </div>

      <!-- Colors -->
      <div class="toolbar-group border-r pr-4 mr-4">
        <div class="flex gap-2">
          <!-- Text Color -->
          <div class="color-picker-wrapper">
            <button
              class="toolbar-btn color-btn"
              @click="showTextColorPicker = !showTextColorPicker"
              title="Text Color"
            >
              <Icon name="palette" />
              <div 
                class="color-indicator" 
                :style="{ backgroundColor: currentTextColor }"
              ></div>
            </button>
            <div v-if="showTextColorPicker" class="color-picker-dropdown">
              <div class="color-grid">
                <button
                  v-for="color in colorPalette"
                  :key="color"
                  class="color-swatch"
                  :style="{ backgroundColor: color }"
                  @click="setTextColor(color)"
                ></button>
              </div>
            </div>
          </div>

          <!-- Highlight Color -->
          <div class="color-picker-wrapper">
            <button
              class="toolbar-btn color-btn"
              @click="showHighlightColorPicker = !showHighlightColorPicker"
              title="Highlight Color"
            >
              <Icon name="brush" />
              <div 
                class="color-indicator" 
                :style="{ backgroundColor: currentHighlightColor }"
              ></div>
            </button>
            <div v-if="showHighlightColorPicker" class="color-picker-dropdown">
              <div class="color-grid">
                <button
                  v-for="color in colorPalette"
                  :key="color"
                  class="color-swatch"
                  :style="{ backgroundColor: color }"
                  @click="setHighlightColor(color)"
                ></button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Lists -->
      <div class="toolbar-group border-r pr-4 mr-4">
        <button
          v-for="list in lists"
          :key="list.name"
          :class="[
            'toolbar-btn',
            { active: editor?.isActive(list.name) }
          ]"
          @click="list.action"
          :title="list.title"
        >
          <Icon :name="list.icon" />
        </button>
      </div>

      <!-- Media -->
      <div class="toolbar-group border-r pr-4 mr-4">
        <button
          class="toolbar-btn"
          @click="openImageDialog"
          title="Insert Image"
        >
          <Icon name="image" />
        </button>
        <button
          class="toolbar-btn"
          @click="openVideoDialog"
          title="Insert Video"
        >
          <Icon name="video" />
        </button>
        <button
          class="toolbar-btn"
          @click="insertLink"
          title="Insert Link"
        >
          <Icon name="link" />
        </button>
      </div>

      <!-- Undo/Redo -->
      <div class="toolbar-group">
        <button
          class="toolbar-btn"
          @click="() => editor?.chain().focus().undo().run()"
          :disabled="!editor?.can().undo()"
          title="Undo"
        >
          <Icon name="undo" />
        </button>
        <button
          class="toolbar-btn"
          @click="() => editor?.chain().focus().redo().run()"
          :disabled="!editor?.can().redo()"
          title="Redo"
        >
          <Icon name="redo" />
        </button>
      </div>

      <!-- Word Count -->
      <div class="ml-auto text-sm text-gray-500">
        {{ wordCount }} words, {{ characterCount }} characters
      </div>
    </div>

    <!-- Editor Content -->
    <div class="editor-container">
      <EditorContent 
        :editor="editor" 
        class="editor-content"
        :class="{ 'focus-within': isFocused }"
      />
      
      <!-- Placeholder when empty -->
      <div v-if="isEmpty" class="editor-placeholder">
        Start typing your ad content...
      </div>
    </div>

    <!-- Character Count Indicator -->
    <div class="editor-footer bg-gray-50 px-4 py-2 flex justify-between items-center text-sm">
      <div class="flex gap-4">
        <span class="text-gray-600">
          Words: <strong>{{ wordCount }}</strong>
        </span>
        <span class="text-gray-600">
          Characters: <strong>{{ characterCount }}</strong>
        </span>
      </div>
      
      <div v-if="maxLength" class="character-limit">
        <span 
          :class="[
            'font-medium',
            characterCount > maxLength * 0.9 ? 'text-orange-600' : 'text-gray-600',
            characterCount > maxLength ? 'text-red-600' : ''
          ]"
        >
          {{ characterCount }}/{{ maxLength }}
        </span>
      </div>
    </div>

    <!-- Image Upload Dialog -->
    <div v-if="showImageDialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-lg font-semibold mb-4">Insert Image</h3>
        
        <div class="space-y-4">
          <!-- Upload -->
          <div>
            <label class="block text-sm font-medium mb-2">Upload Image</label>
            <input
              type="file"
              accept="image/*"
              @change="handleImageUpload"
              class="w-full p-2 border rounded"
            />
          </div>
          
          <!-- URL -->
          <div>
            <label class="block text-sm font-medium mb-2">Or use URL</label>
            <input
              v-model="imageUrl"
              type="url"
              placeholder="https://example.com/image.jpg"
              class="w-full p-2 border rounded"
            />
          </div>
          
          <!-- Alt Text -->
          <div>
            <label class="block text-sm font-medium mb-2">Alt Text</label>
            <input
              v-model="imageAlt"
              type="text"
              placeholder="Describe the image"
              class="w-full p-2 border rounded"
            />
          </div>
        </div>
        
        <div class="flex gap-2 mt-6">
          <button
            @click="insertImage"
            :disabled="!imageUrl"
            class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
          >
            Insert
          </button>
          <button
            @click="closeImageDialog"
            class="px-4 py-2 border rounded hover:bg-gray-50"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>

    <!-- Video Dialog -->
    <div v-if="showVideoDialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-lg font-semibold mb-4">Insert Video</h3>
        
        <div class="space-y-4">
          <!-- YouTube URL -->
          <div>
            <label class="block text-sm font-medium mb-2">YouTube URL</label>
            <input
              v-model="videoUrl"
              type="url"
              placeholder="https://www.youtube.com/watch?v=..."
              class="w-full p-2 border rounded"
            />
          </div>
          
          <!-- Video dimensions -->
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-2">Width</label>
              <input
                v-model.number="videoWidth"
                type="number"
                min="200"
                max="800"
                class="w-full p-2 border rounded"
              />
            </div>
            <div>
              <label class="block text-sm font-medium mb-2">Height</label>
              <input
                v-model.number="videoHeight"
                type="number"
                min="150"
                max="600"
                class="w-full p-2 border rounded"
              />
            </div>
          </div>
        </div>
        
        <div class="flex gap-2 mt-6">
          <button
            @click="insertVideo"
            :disabled="!videoUrl"
            class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
          >
            Insert
          </button>
          <button
            @click="closeVideoDialog"
            class="px-4 py-2 border rounded hover:bg-gray-50"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useEditor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import Image from '@tiptap/extension-image'
import Link from '@tiptap/extension-link'
import TextAlign from '@tiptap/extension-text-align'
import { TextStyle } from '@tiptap/extension-text-style'
import { Color } from '@tiptap/extension-color'
import { Highlight } from '@tiptap/extension-highlight'
import { CharacterCount } from '@tiptap/extension-character-count'
import { Placeholder } from '@tiptap/extension-placeholder'
import Youtube from '@tiptap/extension-youtube'

interface Props {
  modelValue?: string
  placeholder?: string
  maxLength?: number
  readonly?: boolean
  autoFocus?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'focus'): void
  (e: 'blur'): void
  (e: 'change', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: 'Start typing...',
  readonly: false,
  autoFocus: false,
})

const emit = defineEmits<Emits>()

// Reactive state
const isFocused = ref(false)
const showImageDialog = ref(false)
const showVideoDialog = ref(false)
const showTextColorPicker = ref(false)
const showHighlightColorPicker = ref(false)
const selectedHeading = ref('')

// Image dialog state
const imageUrl = ref('')
const imageAlt = ref('')

// Video dialog state
const videoUrl = ref('')
const videoWidth = ref(640)
const videoHeight = ref(360)

// Color state
const currentTextColor = ref('#000000')
const currentHighlightColor = ref('#ffff00')

// Color palette
const colorPalette = [
  '#000000', '#ffffff', '#ff0000', '#00ff00', '#0000ff',
  '#ffff00', '#ff00ff', '#00ffff', '#ff8000', '#8000ff',
  '#0080ff', '#80ff00', '#ff0080', '#808080', '#c0c0c0',
  '#800000', '#008000', '#000080', '#808000', '#800080'
]

// Initialize Tiptap editor
const editor = useEditor({
  content: props.modelValue,
  extensions: [
    StarterKit,
    Image.configure({
      inline: false,
      HTMLAttributes: {
        class: 'max-w-full h-auto rounded',
      },
    }),
    Link.configure({
      openOnClick: false,
      HTMLAttributes: {
        class: 'text-blue-600 underline',
      },
    }),
    TextAlign.configure({
      types: ['heading', 'paragraph'],
    }),
    TextStyle,
    Color.configure({
      types: ['textStyle'],
    }),
    Highlight.configure({
      multicolor: true,
    }),
    CharacterCount.configure({
      limit: props.maxLength,
    }),
    Placeholder.configure({
      placeholder: props.placeholder,
    }),
    Youtube.configure({
      controls: false,
      nocookie: true,
    }),
  ],
  editable: !props.readonly,
  autofocus: props.autoFocus,
  onUpdate: ({ editor }) => {
    const html = editor.getHTML()
    emit('update:modelValue', html)
    emit('change', html)
  },
  onFocus: () => {
    isFocused.value = true
    emit('focus')
  },
  onBlur: () => {
    isFocused.value = false
    emit('blur')
  },
})

// Computed properties
const wordCount = computed(() => {
  return editor.value?.storage.characterCount?.words() || 0
})

const characterCount = computed(() => {
  return editor.value?.storage.characterCount?.characters() || 0
})

const isEmpty = computed(() => {
  return editor.value?.isEmpty || false
})

// Toolbar configurations
const textFormats = [
  {
    name: 'bold',
    icon: 'bold',
    title: 'Bold',
    action: () => editor.value?.chain().focus().toggleBold().run(),
  },
  {
    name: 'italic',
    icon: 'italic',
    title: 'Italic',
    action: () => editor.value?.chain().focus().toggleItalic().run(),
  },
  {
    name: 'underline',
    icon: 'underline',
    title: 'Underline',
    action: () => editor.value?.chain().focus().toggleUnderline().run(),
  },
  {
    name: 'strike',
    icon: 'strikethrough',
    title: 'Strikethrough',
    action: () => editor.value?.chain().focus().toggleStrike().run(),
  },
  {
    name: 'code',
    icon: 'code',
    title: 'Code',
    action: () => editor.value?.chain().focus().toggleCode().run(),
  },
]

const alignments = [
  {
    name: 'left',
    icon: 'align-left',
    title: 'Align Left',
  },
  {
    name: 'center',
    icon: 'align-center',
    title: 'Align Center',
  },
  {
    name: 'right',
    icon: 'align-right',
    title: 'Align Right',
  },
  {
    name: 'justify',
    icon: 'align-justify',
    title: 'Justify',
  },
]

const lists = [
  {
    name: 'bulletList',
    icon: 'list-unordered',
    title: 'Bullet List',
    action: () => editor.value?.chain().focus().toggleBulletList().run(),
  },
  {
    name: 'orderedList',
    icon: 'list-ordered',
    title: 'Numbered List',
    action: () => editor.value?.chain().focus().toggleOrderedList().run(),
  },
]

// Methods
const setHeading = () => {
  if (selectedHeading.value === '') {
    editor.value?.chain().focus().setParagraph().run()
  } else {
    editor.value?.chain().focus().toggleHeading({ level: parseInt(selectedHeading.value) }).run()
  }
}

const setTextColor = (color: string) => {
  editor.value?.chain().focus().setColor(color).run()
  currentTextColor.value = color
  showTextColorPicker.value = false
}

const setHighlightColor = (color: string) => {
  editor.value?.chain().focus().toggleHighlight({ color }).run()
  currentHighlightColor.value = color
  showHighlightColorPicker.value = false
}

const openImageDialog = () => {
  showImageDialog.value = true
  imageUrl.value = ''
  imageAlt.value = ''
}

const closeImageDialog = () => {
  showImageDialog.value = false
}

const handleImageUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (file) {
    // Here you would upload the file to your storage service
    // For now, we'll create a temporary URL
    const url = URL.createObjectURL(file)
    imageUrl.value = url
  }
}

const insertImage = () => {
  if (imageUrl.value) {
    editor.value?.chain().focus().setImage({
      src: imageUrl.value,
      alt: imageAlt.value,
    }).run()
    closeImageDialog()
  }
}

const openVideoDialog = () => {
  showVideoDialog.value = true
  videoUrl.value = ''
}

const closeVideoDialog = () => {
  showVideoDialog.value = false
}

const insertVideo = () => {
  if (videoUrl.value) {
    editor.value?.chain().focus().setYoutubeVideo({
      src: videoUrl.value,
      width: videoWidth.value,
      height: videoHeight.value,
    }).run()
    closeVideoDialog()
  }
}

const insertLink = () => {
  const url = window.prompt('Enter URL:')
  if (url) {
    editor.value?.chain().focus().setLink({ href: url }).run()
  }
}

// Watch for external content changes
watch(() => props.modelValue, (newValue) => {
  if (editor.value && editor.value.getHTML() !== newValue) {
    editor.value.commands.setContent(newValue, false)
  }
})

// Clean up on unmount
onBeforeUnmount(() => {
  editor.value?.destroy()
})

// Close color pickers when clicking outside
onMounted(() => {
  const handleClickOutside = (event: MouseEvent) => {
    const target = event.target as Element
    if (!target.closest('.color-picker-wrapper')) {
      showTextColorPicker.value = false
      showHighlightColorPicker.value = false
    }
  }
  
  document.addEventListener('click', handleClickOutside)
  
  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
  })
})
</script>

<style scoped>
.ad-content-editor {
  @apply border border-gray-300 rounded-lg overflow-hidden bg-white;
}

.editor-toolbar {
  @apply flex flex-wrap items-center gap-2 p-4 bg-gray-50 border-b;
}

.toolbar-group {
  @apply flex items-center gap-1;
}

.toolbar-btn {
  @apply p-2 rounded hover:bg-gray-200 transition-colors border-none bg-transparent cursor-pointer flex items-center justify-center;
  min-width: 32px;
  height: 32px;
}

.toolbar-btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.toolbar-btn.active {
  @apply bg-blue-100 text-blue-600;
}

.toolbar-select {
  @apply px-3 py-1 border rounded text-sm min-w-32;
}

.color-picker-wrapper {
  @apply relative;
}

.color-btn {
  @apply relative;
}

.color-indicator {
  @apply absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-3 h-1 rounded-full;
}

.color-picker-dropdown {
  @apply absolute top-full left-0 mt-1 p-2 bg-white border rounded shadow-lg z-10;
  min-width: 200px;
}

.color-grid {
  @apply grid grid-cols-5 gap-1;
}

.color-swatch {
  @apply w-6 h-6 rounded border border-gray-300 cursor-pointer hover:scale-110 transition-transform;
}

.editor-container {
  @apply relative min-h-48;
}

.editor-content {
  @apply p-4 min-h-48 text-gray-900 dark:text-white max-w-none;
}

.editor-content:focus-within {
  @apply outline-none;
}

.editor-placeholder {
  @apply absolute top-4 left-4 text-gray-400 pointer-events-none;
}

.editor-footer {
  @apply bg-gray-50 px-4 py-2 text-sm flex justify-between items-center;
}

/* ProseMirror styles */
:deep(.ProseMirror) {
  outline: none;
  min-height: 200px;
}

:deep(.ProseMirror p.is-editor-empty:first-child::before) {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}

:deep(.ProseMirror img) {
  @apply max-w-full h-auto rounded;
}

:deep(.ProseMirror a) {
  @apply text-blue-600 underline;
}

:deep(.ProseMirror ul, .ProseMirror ol) {
  @apply pl-6;
}

:deep(.ProseMirror li) {
  @apply mb-1;
}

:deep(.ProseMirror blockquote) {
  @apply border-l-4 border-gray-300 pl-4 italic;
}

:deep(.ProseMirror code) {
  @apply bg-gray-100 px-1 py-0.5 rounded text-sm;
}

:deep(.ProseMirror pre) {
  @apply bg-gray-100 p-3 rounded text-sm overflow-x-auto;
}

:deep(.ProseMirror .youtube-wrapper) {
  @apply text-center;
}

:deep(.ProseMirror iframe) {
  @apply max-w-full;
}
</style>