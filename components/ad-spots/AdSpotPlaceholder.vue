<template>
  <!-- Loading State -->
  <div v-if="isLoading" class="flex justify-center items-center py-8">
    <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
  </div>

  <!-- Ad Content Grid (similar to flyers) -->
  <div v-else-if="hasAdContent" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
    <div
      v-for="(ad, index) in adContent"
      :key="`ad-${ad.id}-${index}`"
      class="relative rounded-lg overflow-hidden shadow-lg transition-transform duration-300 hover:transform hover:scale-[1.02]"
    >
      <AdSpotsAdSpotDisplay :ad="ad" @click="handleAdClick" />
    </div>
  </div>

  <!-- Hide component completely when no ads are available -->
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue';
import { useAds } from '~/composables/useAds';

const props = defineProps({
  position: {
    type: String,
    required: true,
    validator: (value: string) => ['top', 'middle', 'bottom'].includes(value),
  },
});

const { activeAds, isLoading, fetchAds } = useAds();

// Filter active ads by position (only show paid, approved, and current ads)
const adContent = computed(() => {
  const targetPosition = props.position.toLowerCase();
  return activeAds.value
    .filter(ad => {
      const position = (ad.position || ad.location || '').toLowerCase();
      if (targetPosition === 'top') {
        return position === 'top' || position === 'home page' || position.includes('home');
      } else if (targetPosition === 'middle') {
        return position === 'middle' || position === 'center';
      } else if (targetPosition === 'bottom') {
        return position === 'bottom' || position === 'footer';
      }
      return false;
    })
    .slice(0, 3);
});

const hasAdContent = computed(() => adContent.value.length > 0);

const handleAdClick = ad => {
  // Additional analytics or tracking can be added here
};

onMounted(() => {
  fetchAds();
});
</script>
