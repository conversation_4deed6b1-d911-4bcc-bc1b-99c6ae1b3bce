<template>
  <div class="bulk-ad-manager">
    <!-- Header -->
    <div class="manager-header">
      <div>
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Bulk Ad Manager</h2>
        <p class="text-gray-600 dark:text-gray-400 mt-1">
          Create and manage multiple ads at once
        </p>
      </div>
      <div class="header-actions">
        <button @click="downloadTemplate" class="btn-secondary">
          <Icon name="ph:download" />
          Download Template
        </button>
        <button @click="showUploadModal = true" class="btn-primary">
          <Icon name="ph:upload" />
          Upload CSV
        </button>
      </div>
    </div>

    <!-- Active Operations -->
    <div v-if="activeOperations.length > 0" class="operations-section">
      <h3 class="section-title">Active Operations</h3>
      <div class="operations-list">
        <div
          v-for="operation in activeOperations"
          :key="operation.id"
          class="operation-card"
        >
          <div class="operation-header">
            <h4 class="operation-name">{{ getOperationName(operation) }}</h4>
            <div class="operation-status" :class="operation.status">
              <Icon :name="getStatusIcon(operation.status)" />
              {{ formatStatus(operation.status) }}
            </div>
          </div>

          <div class="operation-progress">
            <div class="progress-info">
              <span>{{ operation.processedItems }} / {{ operation.totalItems }} ads</span>
              <span>{{ getProgressPercentage(operation) }}%</span>
            </div>
            <div class="progress-bar">
              <div
                class="progress-fill"
                :style="{ width: `${getProgressPercentage(operation)}%` }"
              />
            </div>
          </div>

          <div v-if="operation.errorCount > 0" class="operation-errors">
            <Icon name="ph:warning" />
            {{ operation.errorCount }} errors
            <button @click="showErrors(operation)" class="view-errors-btn">
              View Details
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Bulk Actions -->
    <div class="bulk-actions-section">
      <h3 class="section-title">Quick Actions</h3>
      <div class="actions-grid">
        <div class="action-card">
          <Icon name="ph:copy" size="24" />
          <h4>Duplicate Ads</h4>
          <p>Create copies of existing ads with modifications</p>
          <button @click="showDuplicateModal = true" class="action-btn">
            Duplicate
          </button>
        </div>

        <div class="action-card">
          <Icon name="ph:calendar" size="24" />
          <h4>Schedule Ads</h4>
          <p>Set start and end dates for multiple ads</p>
          <button @click="showScheduleModal = true" class="action-btn">
            Schedule
          </button>
        </div>

        <div class="action-card">
          <Icon name="ph:pencil" size="24" />
          <h4>Bulk Edit</h4>
          <p>Update properties across multiple ads</p>
          <button @click="showEditModal = true" class="action-btn">
            Edit
          </button>
        </div>

        <div class="action-card">
          <Icon name="ph:pause" size="24" />
          <h4>Status Update</h4>
          <p>Activate, pause, or archive ads in bulk</p>
          <button @click="showStatusModal = true" class="action-btn">
            Update Status
          </button>
        </div>
      </div>
    </div>

    <!-- Recent Operations -->
    <div v-if="recentOperations.length > 0" class="operations-history">
      <h3 class="section-title">Recent Operations</h3>
      <div class="history-table">
        <table>
          <thead>
            <tr>
              <th>Operation</th>
              <th>Date</th>
              <th>Items</th>
              <th>Success</th>
              <th>Errors</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="operation in recentOperations" :key="operation.id">
              <td class="font-medium">{{ getOperationName(operation) }}</td>
              <td>{{ formatDate(operation.completedAt) }}</td>
              <td>{{ operation.totalItems }}</td>
              <td class="text-green-600 dark:text-green-400">
                {{ operation.successCount }}
              </td>
              <td :class="operation.errorCount > 0 ? 'text-red-600 dark:text-red-400' : ''">
                {{ operation.errorCount }}
              </td>
              <td>
                <div class="table-actions">
                  <button
                    v-if="operation.errorCount > 0"
                    @click="showErrors(operation)"
                    class="table-action-btn"
                  >
                    <Icon name="ph:list" />
                  </button>
                  <button @click="downloadReport(operation)" class="table-action-btn">
                    <Icon name="ph:download" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Upload Modal -->
    <Teleport to="body">
      <Transition name="modal">
        <div v-if="showUploadModal" class="modal-overlay" @click="showUploadModal = false">
          <div class="modal-content" @click.stop>
            <div class="modal-header">
              <h3>Upload Bulk Ads</h3>
              <button @click="showUploadModal = false" class="close-btn">
                <Icon name="ph:x" />
              </button>
            </div>

            <div class="modal-body">
              <div class="upload-area" @drop="handleDrop" @dragover.prevent @dragenter.prevent>
                <input
                  ref="fileInput"
                  type="file"
                  accept=".csv,.xlsx"
                  @change="handleFileSelect"
                  class="hidden"
                />

                <Icon name="ph:cloud-arrow-up" size="48" />
                <h4>Drop your CSV file here</h4>
                <p>or</p>
                <button @click="$refs.fileInput?.click()" class="btn-secondary">
                  Browse Files
                </button>

                <div class="upload-info">
                  <p>Supported formats: CSV, XLSX</p>
                  <p>Maximum 1000 ads per upload</p>
                </div>
              </div>

              <div v-if="uploadedFile" class="file-preview">
                <div class="file-info">
                  <Icon name="ph:file-csv" size="24" />
                  <div>
                    <p class="file-name">{{ uploadedFile.name }}</p>
                    <p class="file-size">{{ formatFileSize(uploadedFile.size) }}</p>
                  </div>
                  <button @click="uploadedFile = null" class="remove-file-btn">
                    <Icon name="ph:x" />
                  </button>
                </div>

                <div v-if="parsedData.length > 0" class="preview-section">
                  <h5>Preview ({{ parsedData.length }} ads)</h5>
                  <div class="preview-table">
                    <table>
                      <thead>
                        <tr>
                          <th v-for="header in previewHeaders" :key="header">
                            {{ header }}
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="(row, index) in parsedData.slice(0, 5)" :key="index">
                          <td v-for="header in previewHeaders" :key="header">
                            {{ row[header] || '-' }}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <p v-if="parsedData.length > 5" class="more-rows">
                      And {{ parsedData.length - 5 }} more...
                    </p>
                  </div>
                </div>
              </div>

              <div v-if="uploadedFile && parsedData.length > 0" class="upload-actions">
                <button @click="showUploadModal = false" class="btn-secondary">
                  Cancel
                </button>
                <button @click="processUpload" :disabled="isProcessing" class="btn-primary">
                  <Icon v-if="isProcessing" name="ph:spinner" class="animate-spin" />
                  <span v-else>Create {{ parsedData.length }} Ads</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </Transition>
    </Teleport>

    <!-- Error Details Modal -->
    <Teleport to="body">
      <Transition name="modal">
        <div v-if="showErrorModal" class="modal-overlay" @click="showErrorModal = false">
          <div class="modal-content" @click.stop>
            <div class="modal-header">
              <h3>Operation Errors</h3>
              <button @click="showErrorModal = false" class="close-btn">
                <Icon name="ph:x" />
              </button>
            </div>

            <div class="modal-body">
              <div class="errors-list">
                <div
                  v-for="(error, index) in selectedErrors"
                  :key="index"
                  class="error-item"
                >
                  <div class="error-header">
                    <span class="error-row">Row {{ error.row }}</span>
                    <Icon name="ph:warning-circle" class="text-red-500" />
                  </div>
                  <p class="error-message">{{ error.message }}</p>
                </div>
              </div>
            </div>

            <div class="modal-footer">
              <button @click="downloadErrorReport" class="btn-secondary">
                <Icon name="ph:download" />
                Download Error Report
              </button>
              <button @click="showErrorModal = false" class="btn-primary">
                Close
              </button>
            </div>
          </div>
        </div>
      </Transition>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import Papa from 'papaparse';
import { useAdContentManagement } from '~/composables/useAdContentManagement';
import { useCurrentUser } from '~/composables/useCurrentUser';
import type { BulkAdData, BulkAdOperation } from '~/types/ad-content';

const { bulkCreateAds } = useAdContentManagement();
const { userProfile } = useCurrentUser();

// State
const showUploadModal = ref(false);
const showErrorModal = ref(false);
const showDuplicateModal = ref(false);
const showScheduleModal = ref(false);
const showEditModal = ref(false);
const showStatusModal = ref(false);

const uploadedFile = ref<File | null>(null);
const parsedData = ref<BulkAdData[]>([]);
const isProcessing = ref(false);
const selectedErrors = ref<Array<{ row: number; message: string }>>([]);

// Mock data for demo
const activeOperations = ref<BulkAdOperation[]>([]);
const recentOperations = ref<BulkAdOperation[]>([
  {
    id: 'op1',
    type: 'create',
    status: 'completed',
    totalItems: 50,
    processedItems: 50,
    successCount: 48,
    errorCount: 2,
    errors: [
      { row: 15, message: 'Invalid image URL' },
      { row: 32, message: 'Missing required field: title' },
    ],
    createdAt: new Date(Date.now() - 86400000),
    completedAt: new Date(Date.now() - 86000000),
  },
]);

// Computed
const previewHeaders = computed(() => {
  if (parsedData.value.length === 0) return [];
  return Object.keys(parsedData.value[0]).slice(0, 5);
});

// Methods
const handleDrop = (event: DragEvent) => {
  event.preventDefault();
  const files = event.dataTransfer?.files;
  if (files && files.length > 0) {
    handleFile(files[0]);
  }
};

const handleFileSelect = (event: Event) => {
  const files = (event.target as HTMLInputElement).files;
  if (files && files.length > 0) {
    handleFile(files[0]);
  }
};

const handleFile = (file: File) => {
  if (!file.name.match(/\.(csv|xlsx)$/)) {
    alert('Please upload a CSV or XLSX file');
    return;
  }

  uploadedFile.value = file;
  parseFile(file);
};

const parseFile = (file: File) => {
  Papa.parse(file, {
    header: true,
    complete: (results) => {
      parsedData.value = results.data as BulkAdData[];
    },
    error: (error) => {
      console.error('Error parsing file:', error);
      alert('Error parsing file. Please check the format.');
    },
  });
};

const processUpload = async () => {
  if (!userProfile.value?.id || parsedData.value.length === 0) return;

  isProcessing.value = true;
  try {
    const operation = await bulkCreateAds(userProfile.value.id, parsedData.value);
    activeOperations.value.push(operation);
    
    // Simulate progress updates
    const interval = setInterval(() => {
      const op = activeOperations.value.find(o => o.id === operation.id);
      if (op && op.processedItems < op.totalItems) {
        op.processedItems = Math.min(op.processedItems + 10, op.totalItems);
      } else {
        clearInterval(interval);
        moveToRecent(operation.id);
      }
    }, 1000);

    showUploadModal.value = false;
    resetUpload();
  } catch (error) {
    console.error('Failed to process upload:', error);
  } finally {
    isProcessing.value = false;
  }
};

const resetUpload = () => {
  uploadedFile.value = null;
  parsedData.value = [];
};

const downloadTemplate = () => {
  const template = [
    ['title', 'text', 'imageUrl', 'ctaText', 'ctaUrl', 'startDate', 'endDate', 'budget'],
    ['Summer Sale', 'Get 50% off all items!', 'https://example.com/image.jpg', 'Shop Now', 'https://example.com/sale', '2024-06-01', '2024-08-31', '1000'],
  ];

  const csv = Papa.unparse(template);
  const blob = new Blob([csv], { type: 'text/csv' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'bulk_ads_template.csv';
  a.click();
  URL.revokeObjectURL(url);
};

const getOperationName = (operation: BulkAdOperation): string => {
  const names = {
    create: 'Bulk Create',
    update: 'Bulk Update',
    delete: 'Bulk Delete',
    duplicate: 'Bulk Duplicate',
  };
  return names[operation.type] || operation.type;
};

const getStatusIcon = (status: string): string => {
  const icons = {
    pending: 'ph:clock',
    processing: 'ph:spinner',
    completed: 'ph:check-circle',
    failed: 'ph:x-circle',
  };
  return icons[status] || 'ph:circle';
};

const formatStatus = (status: string): string => {
  return status.charAt(0).toUpperCase() + status.slice(1);
};

const getProgressPercentage = (operation: BulkAdOperation): number => {
  if (operation.totalItems === 0) return 0;
  return Math.round((operation.processedItems / operation.totalItems) * 100);
};

const formatDate = (date: any): string => {
  if (!date) return '-';
  const d = date.toDate ? date.toDate() : new Date(date);
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(d);
};

const formatFileSize = (bytes: number): string => {
  const sizes = ['Bytes', 'KB', 'MB'];
  if (bytes === 0) return '0 Bytes';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

const showErrors = (operation: BulkAdOperation) => {
  selectedErrors.value = operation.errors || [];
  showErrorModal.value = true;
};

const downloadReport = (operation: BulkAdOperation) => {
  // Generate report data
  const report = {
    operation: getOperationName(operation),
    date: formatDate(operation.completedAt),
    totalItems: operation.totalItems,
    successCount: operation.successCount,
    errorCount: operation.errorCount,
    errors: operation.errors,
  };

  const json = JSON.stringify(report, null, 2);
  const blob = new Blob([json], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `operation_report_${operation.id}.json`;
  a.click();
  URL.revokeObjectURL(url);
};

const downloadErrorReport = () => {
  const csv = Papa.unparse(selectedErrors.value);
  const blob = new Blob([csv], { type: 'text/csv' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'error_report.csv';
  a.click();
  URL.revokeObjectURL(url);
};

const moveToRecent = (operationId: string) => {
  const index = activeOperations.value.findIndex(op => op.id === operationId);
  if (index !== -1) {
    const [operation] = activeOperations.value.splice(index, 1);
    operation.status = 'completed';
    operation.completedAt = new Date();
    recentOperations.value.unshift(operation);
  }
};
</script>

<style scoped>
.bulk-ad-manager {
  @apply space-y-6;
}

.manager-header {
  @apply flex items-start justify-between;
}

.header-actions {
  @apply flex items-center gap-3;
}

/* Sections */
.operations-section,
.bulk-actions-section,
.operations-history {
  @apply space-y-4;
}

.section-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

/* Operations */
.operations-list {
  @apply space-y-3;
}

.operation-card {
  @apply bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 space-y-3;
}

.operation-header {
  @apply flex items-center justify-between;
}

.operation-name {
  @apply font-medium text-gray-900 dark:text-white;
}

.operation-status {
  @apply flex items-center gap-1 text-sm font-medium px-2 py-1 rounded-full;
}

.operation-status.processing {
  @apply bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300;
}

.operation-status.completed {
  @apply bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300;
}

.operation-progress {
  @apply space-y-2;
}

.progress-info {
  @apply flex items-center justify-between text-sm text-gray-600 dark:text-gray-400;
}

.progress-bar {
  @apply w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden;
}

.progress-fill {
  @apply h-full bg-blue-500 transition-all duration-300;
}

.operation-errors {
  @apply flex items-center gap-2 p-2 bg-red-50 dark:bg-red-900 text-red-700 dark:text-red-300 rounded-md text-sm;
}

.view-errors-btn {
  @apply ml-auto text-xs underline hover:no-underline;
}

/* Actions Grid */
.actions-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4;
}

.action-card {
  @apply bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 text-center space-y-2;
}

.action-card h4 {
  @apply font-medium text-gray-900 dark:text-white;
}

.action-card p {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.action-btn {
  @apply px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors text-sm;
}

/* Table */
.history-table {
  @apply overflow-x-auto;
}

.history-table table {
  @apply w-full;
}

.history-table th {
  @apply text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider p-3 bg-gray-50 dark:bg-gray-700;
}

.history-table td {
  @apply p-3 text-sm text-gray-900 dark:text-white border-t border-gray-200 dark:border-gray-700;
}

.table-actions {
  @apply flex items-center gap-1;
}

.table-action-btn {
  @apply p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors;
}

/* Upload Area */
.upload-area {
  @apply border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center space-y-4;
}

.upload-area h4 {
  @apply text-lg font-medium text-gray-900 dark:text-white;
}

.upload-info {
  @apply text-sm text-gray-500 dark:text-gray-400 space-y-1;
}

/* File Preview */
.file-preview {
  @apply mt-4 space-y-4;
}

.file-info {
  @apply flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg;
}

.file-name {
  @apply font-medium text-gray-900 dark:text-white;
}

.file-size {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

.remove-file-btn {
  @apply ml-auto p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors;
}

/* Preview Table */
.preview-section {
  @apply space-y-2;
}

.preview-section h5 {
  @apply font-medium text-gray-900 dark:text-white;
}

.preview-table {
  @apply overflow-x-auto;
}

.preview-table table {
  @apply w-full text-sm;
}

.preview-table th {
  @apply text-left font-medium text-gray-700 dark:text-gray-300 p-2 border-b border-gray-200 dark:border-gray-700;
}

.preview-table td {
  @apply p-2 text-gray-600 dark:text-gray-400 border-b border-gray-100 dark:border-gray-800;
}

.more-rows {
  @apply text-sm text-gray-500 dark:text-gray-400 text-center mt-2;
}

.upload-actions {
  @apply flex items-center justify-end gap-3 mt-6;
}

/* Errors */
.errors-list {
  @apply space-y-3 max-h-96 overflow-y-auto;
}

.error-item {
  @apply bg-red-50 dark:bg-red-900 rounded-lg p-3;
}

.error-header {
  @apply flex items-center justify-between mb-1;
}

.error-row {
  @apply text-sm font-medium text-red-700 dark:text-red-300;
}

.error-message {
  @apply text-sm text-red-600 dark:text-red-400;
}

/* Modal */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.modal-content {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto;
}

.modal-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700;
}

.modal-header h3 {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.close-btn {
  @apply p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors;
}

.modal-body {
  @apply p-6;
}

.modal-footer {
  @apply flex items-center justify-end gap-3 p-6 border-t border-gray-200 dark:border-gray-700;
}

/* Buttons */
.btn-primary {
  @apply flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors;
}

/* Transitions */
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}
</style>