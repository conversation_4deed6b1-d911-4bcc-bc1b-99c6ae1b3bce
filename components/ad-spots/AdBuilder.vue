<template>
  <div class="ad-builder">
    <!-- Header -->
    <div class="builder-header bg-white border-b p-4 flex justify-between items-center">
      <div class="flex items-center gap-4">
        <h1 class="text-xl font-semibold">Ad Builder</h1>
        <div class="flex items-center gap-2 text-sm text-gray-600">
          <Icon name="save" class="w-4 h-4" />
          <span v-if="lastSaved">Last saved: {{ formatDate(lastSaved) }}</span>
          <span v-else>Not saved</span>
        </div>
      </div>
      
      <div class="flex items-center gap-3">
        <!-- Device Preview Toggle -->
        <div class="device-toggle flex border rounded">
          <button
            v-for="device in devices"
            :key="device.name"
            :class="[
              'px-3 py-1 text-sm',
              selectedDevice === device.name 
                ? 'bg-blue-600 text-white' 
                : 'hover:bg-gray-100'
            ]"
            @click="selectedDevice = device.name"
            :title="device.label"
          >
            <Icon :name="device.icon" class="w-4 h-4" />
          </button>
        </div>
        
        <!-- Actions -->
        <button
          @click="saveAd"
          :disabled="saving"
          class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
        >
          <Icon name="save" class="w-4 h-4" />
          {{ saving ? 'Saving...' : 'Save' }}
        </button>
        
        <button
          @click="previewAd"
          class="px-4 py-2 border rounded hover:bg-gray-50 flex items-center gap-2"
        >
          <Icon name="eye" class="w-4 h-4" />
          Preview
        </button>
      </div>
    </div>

    <div class="builder-content flex h-screen-minus-header">
      <!-- Sidebar -->
      <div class="builder-sidebar w-80 bg-gray-50 border-r flex flex-col">
        <!-- Tabs -->
        <div class="sidebar-tabs border-b bg-white">
          <div class="flex">
            <button
              v-for="tab in sidebarTabs"
              :key="tab.id"
              :class="[
                'flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors',
                activeTab === tab.id
                  ? 'border-blue-600 text-blue-600 bg-blue-50'
                  : 'border-transparent text-gray-600 hover:text-gray-900'
              ]"
              @click="activeTab = tab.id"
            >
              <Icon :name="tab.icon" class="w-4 h-4 mr-2" />
              {{ tab.label }}
            </button>
          </div>
        </div>

        <!-- Tab Content -->
        <div class="flex-1 overflow-y-auto p-4">
          <!-- Templates Tab -->
          <div v-if="activeTab === 'templates'" class="space-y-4">
            <div class="mb-4">
              <input
                v-model="templateSearch"
                type="text"
                placeholder="Search templates..."
                class="w-full p-2 border rounded text-sm"
              />
            </div>
            
            <!-- Template Categories -->
            <div class="space-y-2">
              <h3 class="font-medium text-sm text-gray-700">Categories</h3>
              <div class="space-y-1">
                <button
                  v-for="category in templateCategories"
                  :key="category"
                  :class="[
                    'w-full text-left px-3 py-2 rounded text-sm transition-colors',
                    selectedCategory === category
                      ? 'bg-blue-100 text-blue-700'
                      : 'hover:bg-gray-100'
                  ]"
                  @click="selectedCategory = category"
                >
                  {{ category }}
                </button>
              </div>
            </div>
            
            <!-- Template Grid -->
            <div class="space-y-3">
              <h3 class="font-medium text-sm text-gray-700">Templates</h3>
              <div class="grid grid-cols-2 gap-3">
                <div
                  v-for="template in filteredTemplates"
                  :key="template.id"
                  class="template-card border rounded p-2 cursor-pointer hover:shadow-md transition-shadow"
                  @click="selectTemplate(template)"
                  :title="template.description"
                >
                  <div class="aspect-video bg-gray-100 rounded mb-2 flex items-center justify-center">
                    <img
                      v-if="template.thumbnail"
                      :src="template.thumbnail"
                      :alt="template.name"
                      class="w-full h-full object-cover rounded"
                    />
                    <Icon v-else name="image" class="w-8 h-8 text-gray-400" />
                  </div>
                  <div class="text-xs">
                    <div class="font-medium truncate">{{ template.name }}</div>
                    <div class="text-gray-500 flex items-center justify-between mt-1">
                      <span>{{ template.category }}</span>
                      <div class="flex items-center gap-1">
                        <Icon name="star" class="w-3 h-3" />
                        <span>{{ template.rating.toFixed(1) }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Elements Tab -->
          <div v-if="activeTab === 'elements'" class="space-y-4">
            <div class="space-y-3">
              <h3 class="font-medium text-sm text-gray-700">Content Elements</h3>
              <div class="space-y-2">
                <div
                  v-for="element in contentElements"
                  :key="element.type"
                  class="element-item p-3 border rounded cursor-move hover:bg-gray-50 transition-colors"
                  draggable="true"
                  @dragstart="startDragElement(element, $event)"
                >
                  <div class="flex items-center gap-3">
                    <Icon :name="element.icon" class="w-5 h-5 text-gray-600" />
                    <div>
                      <div class="font-medium text-sm">{{ element.name }}</div>
                      <div class="text-xs text-gray-500">{{ element.description }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Properties Tab -->
          <div v-if="activeTab === 'properties'" class="space-y-4">
            <div v-if="selectedElement">
              <h3 class="font-medium text-sm text-gray-700 mb-3">
                {{ selectedElement.type }} Properties
              </h3>
              
              <!-- Common Properties -->
              <div class="space-y-4">
                <!-- Position -->
                <div class="space-y-2">
                  <label class="text-xs font-medium text-gray-700">Position</label>
                  <div class="grid grid-cols-2 gap-2">
                    <div>
                      <label class="text-xs text-gray-500">X</label>
                      <input
                        v-model.number="selectedElement.position.x"
                        type="number"
                        class="w-full p-1 border rounded text-xs"
                        @input="updateElement"
                      />
                    </div>
                    <div>
                      <label class="text-xs text-gray-500">Y</label>
                      <input
                        v-model.number="selectedElement.position.y"
                        type="number"
                        class="w-full p-1 border rounded text-xs"
                        @input="updateElement"
                      />
                    </div>
                  </div>
                </div>
                
                <!-- Size -->
                <div class="space-y-2">
                  <label class="text-xs font-medium text-gray-700">Size</label>
                  <div class="grid grid-cols-2 gap-2">
                    <div>
                      <label class="text-xs text-gray-500">Width</label>
                      <input
                        v-model.number="selectedElement.position.width"
                        type="number"
                        class="w-full p-1 border rounded text-xs"
                        @input="updateElement"
                      />
                    </div>
                    <div>
                      <label class="text-xs text-gray-500">Height</label>
                      <input
                        v-model.number="selectedElement.position.height"
                        type="number"
                        class="w-full p-1 border rounded text-xs"
                        @input="updateElement"
                      />
                    </div>
                  </div>
                </div>

                <!-- Text Properties -->
                <div v-if="selectedElement.type === 'text'" class="space-y-3">
                  <div>
                    <label class="text-xs font-medium text-gray-700">Content</label>
                    <textarea
                      v-model="selectedElement.content.text"
                      class="w-full p-2 border rounded text-xs"
                      rows="3"
                      @input="updateElement"
                    ></textarea>
                  </div>
                  
                  <div class="grid grid-cols-2 gap-2">
                    <div>
                      <label class="text-xs font-medium text-gray-700">Font Size</label>
                      <input
                        v-model="selectedElement.styles.fontSize"
                        type="text"
                        class="w-full p-1 border rounded text-xs"
                        placeholder="16px"
                        @input="updateElement"
                      />
                    </div>
                    <div>
                      <label class="text-xs font-medium text-gray-700">Font Weight</label>
                      <select
                        v-model="selectedElement.styles.fontWeight"
                        class="w-full p-1 border rounded text-xs"
                        @change="updateElement"
                      >
                        <option value="normal">Normal</option>
                        <option value="bold">Bold</option>
                        <option value="lighter">Lighter</option>
                      </select>
                    </div>
                  </div>
                  
                  <div>
                    <label class="text-xs font-medium text-gray-700">Text Color</label>
                    <input
                      v-model="selectedElement.styles.color"
                      type="color"
                      class="w-full h-8 border rounded"
                      @input="updateElement"
                    />
                  </div>
                  
                  <div>
                    <label class="text-xs font-medium text-gray-700">Text Align</label>
                    <select
                      v-model="selectedElement.styles.textAlign"
                      class="w-full p-1 border rounded text-xs"
                      @change="updateElement"
                    >
                      <option value="left">Left</option>
                      <option value="center">Center</option>
                      <option value="right">Right</option>
                    </select>
                  </div>
                </div>

                <!-- Image Properties -->
                <div v-if="selectedElement.type === 'image'" class="space-y-3">
                  <div>
                    <label class="text-xs font-medium text-gray-700">Image URL</label>
                    <input
                      v-model="selectedElement.content.src"
                      type="url"
                      class="w-full p-1 border rounded text-xs"
                      placeholder="https://example.com/image.jpg"
                      @input="updateElement"
                    />
                  </div>
                  
                  <div>
                    <label class="text-xs font-medium text-gray-700">Alt Text</label>
                    <input
                      v-model="selectedElement.content.alt"
                      type="text"
                      class="w-full p-1 border rounded text-xs"
                      placeholder="Image description"
                      @input="updateElement"
                    />
                  </div>
                </div>

                <!-- Button Properties -->
                <div v-if="selectedElement.type === 'button'" class="space-y-3">
                  <div>
                    <label class="text-xs font-medium text-gray-700">Button Text</label>
                    <input
                      v-model="selectedElement.content.text"
                      type="text"
                      class="w-full p-1 border rounded text-xs"
                      placeholder="Click me"
                      @input="updateElement"
                    />
                  </div>
                  
                  <div>
                    <label class="text-xs font-medium text-gray-700">Link URL</label>
                    <input
                      v-model="selectedElement.interactions.link"
                      type="url"
                      class="w-full p-1 border rounded text-xs"
                      placeholder="https://example.com"
                      @input="updateElement"
                    />
                  </div>
                  
                  <div>
                    <label class="text-xs font-medium text-gray-700">Background Color</label>
                    <input
                      v-model="selectedElement.styles.backgroundColor"
                      type="color"
                      class="w-full h-8 border rounded"
                      @input="updateElement"
                    />
                  </div>
                </div>

                <!-- Delete Button -->
                <button
                  @click="deleteElement"
                  class="w-full px-3 py-2 bg-red-600 text-white rounded text-xs hover:bg-red-700"
                >
                  Delete Element
                </button>
              </div>
            </div>
            
            <div v-else class="text-center text-gray-500 py-8">
              <Icon name="cursor-click" class="w-8 h-8 mx-auto mb-2" />
              <p class="text-sm">Select an element to edit its properties</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Canvas -->
      <div class="builder-canvas flex-1 flex flex-col">
        <!-- Canvas Header -->
        <div class="canvas-header bg-white border-b p-4 flex justify-between items-center">
          <div class="flex items-center gap-4">
            <div class="flex items-center gap-2">
              <label class="text-sm font-medium">Zoom:</label>
              <select v-model="canvasZoom" class="border rounded px-2 py-1 text-sm">
                <option value="0.5">50%</option>
                <option value="0.75">75%</option>
                <option value="1">100%</option>
                <option value="1.25">125%</option>
                <option value="1.5">150%</option>
              </select>
            </div>
            
            <div class="text-sm text-gray-600">
              {{ currentAdDimensions.width }} × {{ currentAdDimensions.height }}
            </div>
          </div>
          
          <div class="flex items-center gap-2">
            <button
              @click="undoAction"
              :disabled="!canUndo"
              class="p-2 border rounded hover:bg-gray-50 disabled:opacity-50"
              title="Undo"
            >
              <Icon name="undo" class="w-4 h-4" />
            </button>
            <button
              @click="redoAction"
              :disabled="!canRedo"
              class="p-2 border rounded hover:bg-gray-50 disabled:opacity-50"
              title="Redo"
            >
              <Icon name="redo" class="w-4 h-4" />
            </button>
          </div>
        </div>

        <!-- Canvas Area -->
        <div class="canvas-area flex-1 bg-gray-100 p-8 overflow-auto">
          <div class="canvas-container mx-auto" :style="canvasContainerStyle">
            <div
              ref="canvas"
              class="canvas bg-white border shadow-lg relative overflow-hidden"
              :style="canvasStyle"
              @drop="handleDrop"
              @dragover.prevent
              @click="deselectElement"
            >
              <!-- Grid -->
              <div v-if="showGrid" class="canvas-grid absolute inset-0 pointer-events-none">
                <svg class="w-full h-full">
                  <defs>
                    <pattern
                      id="grid"
                      width="20"
                      height="20"
                      patternUnits="userSpaceOnUse"
                    >
                      <path
                        d="M 20 0 L 0 0 0 20"
                        fill="none"
                        stroke="#e5e7eb"
                        stroke-width="1"
                      />
                    </pattern>
                  </defs>
                  <rect width="100%" height="100%" fill="url(#grid)" />
                </svg>
              </div>

              <!-- Elements -->
              <div
                v-for="element in currentAdStructure.sections"
                :key="element.id"
                class="canvas-element absolute cursor-pointer"
                :class="{
                  'selected': selectedElement?.id === element.id,
                  'dragging': draggingElementId === element.id
                }"
                :style="getElementStyle(element)"
                @click.stop="selectElement(element)"
                @mousedown="startDragElement(element, $event)"
              >
                <!-- Element Content -->
                <div v-if="element.type === 'text'" class="w-full h-full flex items-center">
                  <div
                    :style="{
                      fontSize: element.styles?.fontSize || '16px',
                      fontWeight: element.styles?.fontWeight || 'normal',
                      color: element.styles?.color || '#000000',
                      textAlign: element.styles?.textAlign || 'left'
                    }"
                    class="w-full"
                  >
                    {{ element.content?.text || 'Text Element' }}
                  </div>
                </div>
                
                <div v-else-if="element.type === 'image'" class="w-full h-full">
                  <img
                    v-if="element.content?.src"
                    :src="element.content.src"
                    :alt="element.content?.alt || ''"
                    class="w-full h-full object-cover rounded"
                  />
                  <div v-else class="w-full h-full bg-gray-200 rounded flex items-center justify-center">
                    <Icon name="image" class="w-8 h-8 text-gray-400" />
                  </div>
                </div>
                
                <div v-else-if="element.type === 'button'" class="w-full h-full">
                  <button
                    :style="{
                      backgroundColor: element.styles?.backgroundColor || '#3b82f6',
                      color: element.styles?.color || '#ffffff',
                      borderRadius: element.styles?.borderRadius || '4px'
                    }"
                    class="w-full h-full px-4 py-2 font-medium text-sm"
                    @click.stop
                  >
                    {{ element.content?.text || 'Button' }}
                  </button>
                </div>
                
                <div v-else-if="element.type === 'divider'" class="w-full h-full">
                  <div
                    class="w-full"
                    :style="{
                      height: '2px',
                      backgroundColor: element.styles?.backgroundColor || '#e5e7eb',
                      marginTop: (element.position.height / 2) + 'px'
                    }"
                  ></div>
                </div>

                <!-- Selection Handles -->
                <div v-if="selectedElement?.id === element.id" class="selection-handles">
                  <div class="handle handle-nw"></div>
                  <div class="handle handle-ne"></div>
                  <div class="handle handle-sw"></div>
                  <div class="handle handle-se"></div>
                </div>
              </div>

              <!-- Drop Zone Indicator -->
              <div
                v-if="showDropZone"
                class="drop-zone absolute border-2 border-dashed border-blue-400 bg-blue-50 bg-opacity-50 rounded"
                :style="dropZoneStyle"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Preview Modal -->
    <div v-if="showPreview" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg max-w-4xl max-h-5xl w-full h-full m-4 flex flex-col">
        <div class="flex justify-between items-center p-4 border-b">
          <h3 class="text-lg font-semibold">Ad Preview</h3>
          <button
            @click="showPreview = false"
            class="text-gray-500 hover:text-gray-700"
          >
            <Icon name="x" class="w-6 h-6" />
          </button>
        </div>
        
        <div class="flex-1 p-4 bg-gray-100">
          <div class="max-w-md mx-auto">
            <AdPreview
              :ad-content="currentAdContent"
              :device="selectedDevice"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { AdContent, CreateAdContentData } from '~/composables/useAdContentManagement'
import type { AdTemplate, TemplateSection } from '~/composables/useAdTemplates'

interface Props {
  initialContent?: AdContent
  templateId?: string
}

const props = defineProps<Props>()

// Composables
const { user } = useCurrentUser()
const { createAdContent, updateAdContent } = useAdContentManagement()
const { templates, getTemplate, incrementUsage, applyCustomizations } = useAdTemplates()

// Reactive state
const activeTab = ref('templates')
const selectedDevice = ref<'desktop' | 'mobile' | 'tablet'>('desktop')
const selectedCategory = ref('All')
const templateSearch = ref('')
const selectedElement = ref<TemplateSection | null>(null)
const canvasZoom = ref(1)
const showGrid = ref(true)
const showDropZone = ref(false)
const dropZoneStyle = ref({})
const draggingElementId = ref<string | null>(null)
const showPreview = ref(false)
const saving = ref(false)
const lastSaved = ref<Date | null>(null)

// History for undo/redo
const history = ref<TemplateSection[][]>([])
const historyIndex = ref(-1)
const maxHistorySize = 50

// Current ad structure
const currentAdStructure = ref<{
  layout: string
  sections: TemplateSection[]
  styles: any
}>({
  layout: 'single',
  sections: [],
  styles: {
    colors: { primary: '#3b82f6', secondary: '#64748b', text: '#1f2937', background: '#ffffff' },
    typography: { headingFont: 'Inter', bodyFont: 'Inter', headingSize: '24px', bodySize: '16px' },
    spacing: { padding: '16px', margin: '8px', gap: '12px' },
    borderRadius: '8px',
    shadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
  }
})

// Current ad content
const currentAdContent = ref<Partial<AdContent>>({
  type: 'rich-text',
  title: 'Untitled Ad',
  content: { text: '', media: [], cta: undefined }
})

// Configuration
const devices = [
  { name: 'desktop', label: 'Desktop', icon: 'monitor', width: 800, height: 600 },
  { name: 'tablet', label: 'Tablet', icon: 'tablet', width: 768, height: 1024 },
  { name: 'mobile', label: 'Mobile', icon: 'smartphone', width: 375, height: 667 }
]

const sidebarTabs = [
  { id: 'templates', label: 'Templates', icon: 'template' },
  { id: 'elements', label: 'Elements', icon: 'square' },
  { id: 'properties', label: 'Properties', icon: 'settings' }
]

const contentElements = [
  { type: 'text', name: 'Text', description: 'Add text content', icon: 'type' },
  { type: 'image', name: 'Image', description: 'Add image', icon: 'image' },
  { type: 'button', name: 'Button', description: 'Add clickable button', icon: 'cursor-click' },
  { type: 'divider', name: 'Divider', description: 'Add separator line', icon: 'minus' },
  { type: 'spacer', name: 'Spacer', description: 'Add empty space', icon: 'square-dashed' }
]

// Computed properties
const currentDevice = computed(() => devices.find(d => d.name === selectedDevice.value)!)

const currentAdDimensions = computed(() => ({
  width: currentDevice.value.width,
  height: currentDevice.value.height
}))

const templateCategories = computed(() => {
  const categories = new Set(templates.value.map(t => t.category))
  return ['All', ...Array.from(categories).sort()]
})

const filteredTemplates = computed(() => {
  let filtered = templates.value

  if (selectedCategory.value !== 'All') {
    filtered = filtered.filter(t => t.category === selectedCategory.value)
  }

  if (templateSearch.value) {
    const search = templateSearch.value.toLowerCase()
    filtered = filtered.filter(t =>
      t.name.toLowerCase().includes(search) ||
      t.description.toLowerCase().includes(search) ||
      t.tags.some(tag => tag.toLowerCase().includes(search))
    )
  }

  return filtered.slice(0, 20) // Limit for performance
})

const canvasContainerStyle = computed(() => ({
  maxWidth: `${currentAdDimensions.value.width * canvasZoom.value}px`
}))

const canvasStyle = computed(() => ({
  width: `${currentAdDimensions.value.width}px`,
  height: `${currentAdDimensions.value.height}px`,
  transform: `scale(${canvasZoom.value})`,
  transformOrigin: 'top left'
}))

const canUndo = computed(() => historyIndex.value > 0)
const canRedo = computed(() => historyIndex.value < history.value.length - 1)

// Methods
const selectTemplate = async (template: AdTemplate) => {
  try {
    // Apply template to current structure
    currentAdStructure.value = {
      layout: template.structure.layout,
      sections: JSON.parse(JSON.stringify(template.structure.sections)),
      styles: JSON.parse(JSON.stringify(template.structure.styles))
    }

    // Update ad content
    currentAdContent.value = {
      ...currentAdContent.value,
      type: 'rich-text',
      template: {
        id: template.id,
        customizations: {}
      }
    }

    // Increment usage
    await incrementUsage(template.id)
    
    // Add to history
    addToHistory()
    
    // Switch to elements tab
    activeTab.value = 'elements'
  } catch (error) {
    console.error('Error applying template:', error)
  }
}

const createElement = (type: string): TemplateSection => {
  const id = `element_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  
  const baseElement: TemplateSection = {
    id,
    type: type as any,
    position: { x: 50, y: 50, width: 200, height: 100 },
    content: {},
    styles: {},
    constraints: {},
    interactions: {}
  }

  switch (type) {
    case 'text':
      baseElement.content = { text: 'Your text here' }
      baseElement.styles = {
        fontSize: '16px',
        fontWeight: 'normal',
        color: '#000000',
        textAlign: 'left'
      }
      break
    
    case 'image':
      baseElement.content = { src: '', alt: 'Image' }
      baseElement.position.height = 150
      break
    
    case 'button':
      baseElement.content = { text: 'Click me' }
      baseElement.styles = {
        backgroundColor: '#3b82f6',
        color: '#ffffff',
        borderRadius: '4px'
      }
      baseElement.interactions = { clickable: true, link: '' }
      baseElement.position.height = 40
      break
    
    case 'divider':
      baseElement.position.height = 20
      baseElement.styles = { backgroundColor: '#e5e7eb' }
      break
    
    case 'spacer':
      baseElement.position.height = 50
      break
  }

  return baseElement
}

const startDragElement = (element: any, event: MouseEvent | DragEvent) => {
  if ('dataTransfer' in event) {
    // Dragging from sidebar
    event.dataTransfer!.setData('text/plain', JSON.stringify({ type: 'create', elementType: element.type }))
  } else {
    // Dragging existing element on canvas
    draggingElementId.value = element.id
    // Implement element dragging logic here
  }
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  showDropZone.value = false
  
  try {
    const data = JSON.parse(event.dataTransfer!.getData('text/plain'))
    
    if (data.type === 'create') {
      const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
      const x = (event.clientX - rect.left) / canvasZoom.value
      const y = (event.clientY - rect.top) / canvasZoom.value
      
      const newElement = createElement(data.elementType)
      newElement.position.x = Math.max(0, x - newElement.position.width / 2)
      newElement.position.y = Math.max(0, y - newElement.position.height / 2)
      
      currentAdStructure.value.sections.push(newElement)
      selectedElement.value = newElement
      addToHistory()
    }
  } catch (error) {
    console.error('Error handling drop:', error)
  }
}

const selectElement = (element: TemplateSection) => {
  selectedElement.value = element
  activeTab.value = 'properties'
}

const deselectElement = () => {
  selectedElement.value = null
}

const updateElement = () => {
  if (selectedElement.value) {
    addToHistory()
  }
}

const deleteElement = () => {
  if (selectedElement.value) {
    const index = currentAdStructure.value.sections.findIndex(
      el => el.id === selectedElement.value!.id
    )
    if (index !== -1) {
      currentAdStructure.value.sections.splice(index, 1)
      selectedElement.value = null
      addToHistory()
    }
  }
}

const getElementStyle = (element: TemplateSection) => ({
  left: `${element.position.x}px`,
  top: `${element.position.y}px`,
  width: `${element.position.width}px`,
  height: `${element.position.height}px`,
  ...element.styles
})

const addToHistory = () => {
  // Remove any history after current index
  history.value = history.value.slice(0, historyIndex.value + 1)
  
  // Add current state
  history.value.push(JSON.parse(JSON.stringify(currentAdStructure.value.sections)))
  
  // Limit history size
  if (history.value.length > maxHistorySize) {
    history.value.shift()
  } else {
    historyIndex.value++
  }
}

const undoAction = () => {
  if (canUndo.value) {
    historyIndex.value--
    currentAdStructure.value.sections = JSON.parse(JSON.stringify(history.value[historyIndex.value]))
    selectedElement.value = null
  }
}

const redoAction = () => {
  if (canRedo.value) {
    historyIndex.value++
    currentAdStructure.value.sections = JSON.parse(JSON.stringify(history.value[historyIndex.value]))
    selectedElement.value = null
  }
}

const saveAd = async () => {
  if (!user.value) return
  
  saving.value = true
  
  try {
    const adData: CreateAdContentData = {
      advertiserId: user.value.uid,
      type: 'rich-text',
      title: currentAdContent.value.title || 'Untitled Ad',
      content: {
        text: generateHTMLFromStructure(),
        media: [],
        cta: currentAdContent.value.content?.cta
      },
      template: currentAdContent.value.template
    }

    if (props.initialContent?.id) {
      await updateAdContent(props.initialContent.id, adData)
    } else {
      await createAdContent(adData)
    }
    
    lastSaved.value = new Date()
  } catch (error) {
    console.error('Error saving ad:', error)
  } finally {
    saving.value = false
  }
}

const generateHTMLFromStructure = (): string => {
  // Convert current structure to HTML
  let html = `<div style="position: relative; width: ${currentAdDimensions.value.width}px; height: ${currentAdDimensions.value.height}px;">`
  
  currentAdStructure.value.sections.forEach(element => {
    const style = `position: absolute; left: ${element.position.x}px; top: ${element.position.y}px; width: ${element.position.width}px; height: ${element.position.height}px;`
    
    switch (element.type) {
      case 'text':
        html += `<div style="${style} font-size: ${element.styles?.fontSize}; color: ${element.styles?.color}; text-align: ${element.styles?.textAlign};">${element.content?.text || ''}</div>`
        break
      case 'image':
        html += `<img src="${element.content?.src || ''}" alt="${element.content?.alt || ''}" style="${style} object-fit: cover;" />`
        break
      case 'button':
        html += `<button style="${style} background-color: ${element.styles?.backgroundColor}; color: ${element.styles?.color}; border: none; border-radius: ${element.styles?.borderRadius};">${element.content?.text || 'Button'}</button>`
        break
      case 'divider':
        html += `<div style="${style} background-color: ${element.styles?.backgroundColor};"></div>`
        break
    }
  })
  
  html += '</div>'
  return html
}

const previewAd = () => {
  currentAdContent.value.content!.text = generateHTMLFromStructure()
  showPreview.value = true
}

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

// Initialize
onMounted(() => {
  // Initialize history
  addToHistory()
  
  // Load initial content if provided
  if (props.initialContent) {
    currentAdContent.value = { ...props.initialContent }
    // Parse existing content structure if available
  }
  
  // Load template if provided
  if (props.templateId && templates.value.length === 0) {
    // Wait for templates to load, then apply
    watch(templates, (newTemplates) => {
      if (newTemplates.length > 0) {
        const template = newTemplates.find(t => t.id === props.templateId)
        if (template) {
          selectTemplate(template)
        }
      }
    }, { immediate: true })
  }
})
</script>

<style scoped>
.ad-builder {
  @apply h-screen bg-white;
}

.builder-header {
  @apply border-b;
  height: 64px;
}

.builder-content {
  height: calc(100vh - 64px);
}

.builder-sidebar {
  @apply w-80 bg-gray-50 border-r;
}

.sidebar-tabs .active {
  @apply border-blue-600 text-blue-600 bg-blue-50;
}

.template-card:hover {
  @apply shadow-md;
}

.element-item {
  @apply transition-colors;
}

.element-item:hover {
  @apply bg-gray-100;
}

.canvas-area {
  @apply bg-gray-100;
}

.canvas {
  @apply bg-white border shadow-lg relative;
}

.canvas-element {
  @apply border border-transparent;
}

.canvas-element:hover {
  @apply border-blue-200;
}

.canvas-element.selected {
  @apply border-blue-500 border-2;
}

.canvas-element.dragging {
  @apply opacity-50;
}

.selection-handles {
  @apply absolute inset-0 pointer-events-none;
}

.handle {
  @apply absolute w-2 h-2 bg-blue-500 border border-white pointer-events-auto cursor-pointer;
}

.handle-nw {
  @apply -top-1 -left-1 cursor-nw-resize;
}

.handle-ne {
  @apply -top-1 -right-1 cursor-ne-resize;
}

.handle-sw {
  @apply -bottom-1 -left-1 cursor-sw-resize;
}

.handle-se {
  @apply -bottom-1 -right-1 cursor-se-resize;
}

.drop-zone {
  @apply pointer-events-none;
}

.device-toggle button {
  @apply transition-colors;
}

.device-toggle button:first-child {
  @apply rounded-l;
}

.device-toggle button:last-child {
  @apply rounded-r;
}

.h-screen-minus-header {
  height: calc(100vh - 64px);
}
</style>