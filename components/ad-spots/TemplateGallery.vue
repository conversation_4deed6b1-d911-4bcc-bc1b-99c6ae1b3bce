<template>
  <div class="template-gallery-modal">
    <div class="modal-overlay" @click="$emit('close')"></div>
    <div class="modal-container">
      <div class="modal-header">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Choose a Template</h2>
        <button @click="$emit('close')" class="close-btn">
          <Icon name="ph:x" size="24" />
        </button>
      </div>

      <div class="modal-body">
        <!-- Search and Filters -->
        <div class="filters-section">
          <div class="search-container">
            <Icon name="ph:magnifying-glass" class="search-icon" />
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search templates..."
              class="search-input"
            />
          </div>
          <div class="category-filters">
            <button
              v-for="category in categories"
              :key="category.id"
              @click="selectedCategory = category.id"
              :class="{ active: selectedCategory === category.id }"
              class="category-btn"
            >
              {{ category.name }}
              <span class="category-count">{{ category.count }}</span>
            </button>
          </div>
        </div>

        <!-- Templates Grid -->
        <div v-if="isLoading" class="loading-state">
          <Icon name="ph:spinner" class="animate-spin" size="32" />
          <p>Loading templates...</p>
        </div>

        <div v-else-if="filteredTemplates.length === 0" class="empty-state">
          <Icon name="ph:magnifying-glass-minus" size="48" />
          <p>No templates found</p>
          <button @click="resetFilters" class="btn-secondary mt-4">
            Clear Filters
          </button>
        </div>

        <div v-else class="templates-grid">
          <div
            v-for="template in filteredTemplates"
            :key="template.id"
            @click="selectTemplate(template)"
            class="template-card"
          >
            <div class="template-preview">
              <img
                v-if="template.thumbnail"
                :src="template.thumbnail"
                :alt="template.name"
                class="template-thumbnail"
              />
              <div v-else class="template-placeholder">
                <Icon name="ph:layout" size="48" />
              </div>
              <div class="template-overlay">
                <button class="preview-btn">
                  <Icon name="ph:eye" />
                  Preview
                </button>
              </div>
            </div>
            <div class="template-info">
              <h3 class="template-name">{{ template.name }}</h3>
              <p class="template-category">{{ template.category }}</p>
              <div class="template-stats">
                <span class="stat-item">
                  <Icon name="ph:users" size="14" />
                  {{ template.usage }} uses
                </span>
                <span v-if="template.performanceScore" class="stat-item">
                  <Icon name="ph:chart-line-up" size="14" />
                  {{ Math.round(template.performanceScore * 100) }}% performance
                </span>
              </div>
            </div>
            <div v-if="template.premiumOnly" class="premium-badge">
              <Icon name="ph:crown" />
              Premium
            </div>
          </div>
        </div>

        <!-- User Templates Section -->
        <div v-if="userTemplates.length > 0" class="user-templates-section">
          <h3 class="section-title">Your Templates</h3>
          <div class="templates-grid">
            <div
              v-for="template in userTemplates"
              :key="`user-${template.id}`"
              @click="selectTemplate(template)"
              class="template-card user-template"
            >
              <div class="template-preview">
                <img
                  v-if="template.thumbnail"
                  :src="template.thumbnail"
                  :alt="template.name"
                  class="template-thumbnail"
                />
                <div v-else class="template-placeholder">
                  <Icon name="ph:user" size="48" />
                </div>
                <div class="template-actions">
                  <button @click.stop="editTemplate(template)" class="action-btn">
                    <Icon name="ph:pencil" />
                  </button>
                  <button @click.stop="deleteUserTemplate(template)" class="action-btn danger">
                    <Icon name="ph:trash" />
                  </button>
                </div>
              </div>
              <div class="template-info">
                <h3 class="template-name">{{ template.name }}</h3>
                <p class="template-date">
                  Created {{ formatDate(template.createdAt) }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Template Preview Modal -->
    <Teleport to="body">
      <Transition name="preview">
        <div v-if="previewTemplate" class="preview-modal">
          <div class="preview-overlay" @click="previewTemplate = null"></div>
          <div class="preview-container">
            <div class="preview-header">
              <h3>{{ previewTemplate.name }}</h3>
              <button @click="previewTemplate = null" class="close-btn">
                <Icon name="ph:x" />
              </button>
            </div>
            <div class="preview-content">
              <AdPreview :template="previewTemplate" />
            </div>
            <div class="preview-footer">
              <button @click="previewTemplate = null" class="btn-secondary">
                Cancel
              </button>
              <button @click="confirmSelection(previewTemplate)" class="btn-primary">
                Use This Template
              </button>
            </div>
          </div>
        </div>
      </Transition>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useAdTemplates } from '~/composables/useAdTemplates';
import type { AdTemplate } from '~/types/ad-content';
import AdPreview from './AdPreview.vue';

const emit = defineEmits<{
  select: [template: AdTemplate];
  close: [];
}>();

const {
  templates,
  userTemplates,
  isLoading,
  fetchTemplates,
  fetchUserTemplates,
  deleteTemplate,
  incrementUsage,
  templatesByCategory,
} = useAdTemplates();

// State
const searchQuery = ref('');
const selectedCategory = ref('all');
const previewTemplate = ref<AdTemplate | null>(null);

// Categories with counts
const categories = computed(() => {
  const cats = [
    { id: 'all', name: 'All Templates', count: templates.value.length },
  ];

  Object.entries(templatesByCategory.value).forEach(([category, items]) => {
    cats.push({
      id: category,
      name: category.charAt(0).toUpperCase() + category.slice(1),
      count: items.length,
    });
  });

  return cats;
});

// Filtered templates
const filteredTemplates = computed(() => {
  let filtered = templates.value;

  // Filter by category
  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter(t => t.category === selectedCategory.value);
  }

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(t => {
      return (
        t.name.toLowerCase().includes(query) ||
        t.description?.toLowerCase().includes(query) ||
        t.tags?.some(tag => tag.toLowerCase().includes(query))
      );
    });
  }

  // Sort by usage and performance
  return filtered.sort((a, b) => {
    const scoreA = (a.performanceScore || 0) * 0.5 + (a.usage / 1000) * 0.5;
    const scoreB = (b.performanceScore || 0) * 0.5 + (b.usage / 1000) * 0.5;
    return scoreB - scoreA;
  });
});

// Methods
const selectTemplate = (template: AdTemplate) => {
  previewTemplate.value = template;
};

const confirmSelection = async (template: AdTemplate) => {
  await incrementUsage(template.id);
  emit('select', template);
  previewTemplate.value = null;
};

const editTemplate = (template: AdTemplate) => {
  // TODO: Implement template editing
  console.log('Edit template:', template);
};

const deleteUserTemplate = async (template: AdTemplate) => {
  if (confirm(`Are you sure you want to delete "${template.name}"?`)) {
    await deleteTemplate(template.id);
  }
};

const resetFilters = () => {
  searchQuery.value = '';
  selectedCategory.value = 'all';
};

const formatDate = (date: any) => {
  if (!date) return '';
  const d = date.toDate ? date.toDate() : new Date(date);
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  }).format(d);
};

// Load templates on mount
onMounted(async () => {
  await Promise.all([
    fetchTemplates(),
    fetchUserTemplates(),
  ]);
});
</script>

<style scoped>
.template-gallery-modal {
  @apply fixed inset-0 z-50;
}

.modal-overlay {
  @apply absolute inset-0 bg-black bg-opacity-50;
}

.modal-container {
  @apply absolute inset-4 md:inset-8 bg-white dark:bg-gray-800 rounded-lg shadow-2xl flex flex-col;
}

.modal-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700;
}

.close-btn {
  @apply p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors;
}

.modal-body {
  @apply flex-1 overflow-y-auto p-6;
}

/* Filters */
.filters-section {
  @apply mb-6 space-y-4;
}

.search-container {
  @apply relative;
}

.search-icon {
  @apply absolute left-3 top-1/2 -translate-y-1/2 text-gray-400;
}

.search-input {
  @apply w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

.category-filters {
  @apply flex items-center gap-2 overflow-x-auto pb-2;
}

.category-btn {
  @apply flex items-center gap-2 px-4 py-2 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors whitespace-nowrap;
}

.category-btn.active {
  @apply bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400;
}

.category-count {
  @apply text-xs bg-gray-200 dark:bg-gray-600 px-2 py-0.5 rounded-full;
}

/* Templates Grid */
.templates-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4;
}

.template-card {
  @apply relative bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden cursor-pointer hover:shadow-lg transition-all;
}

.template-preview {
  @apply relative aspect-[4/3] bg-gray-100 dark:bg-gray-700;
}

.template-thumbnail {
  @apply w-full h-full object-cover;
}

.template-placeholder {
  @apply flex items-center justify-center h-full text-gray-400;
}

.template-overlay {
  @apply absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-50 flex items-center justify-center opacity-0 hover:opacity-100 transition-all;
}

.preview-btn {
  @apply flex items-center gap-2 px-4 py-2 bg-white text-gray-900 rounded-lg font-medium;
}

.template-info {
  @apply p-4;
}

.template-name {
  @apply font-semibold text-gray-900 dark:text-white mb-1;
}

.template-category {
  @apply text-sm text-gray-500 dark:text-gray-400 mb-2;
}

.template-stats {
  @apply flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400;
}

.stat-item {
  @apply flex items-center gap-1;
}

.premium-badge {
  @apply absolute top-2 right-2 flex items-center gap-1 px-2 py-1 bg-yellow-100 dark:bg-yellow-900 text-yellow-700 dark:text-yellow-300 text-xs font-medium rounded-full;
}

/* User Templates */
.user-templates-section {
  @apply mt-8 pt-8 border-t border-gray-200 dark:border-gray-700;
}

.section-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white mb-4;
}

.user-template {
  @apply border-2 border-blue-200 dark:border-blue-800;
}

.template-actions {
  @apply absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center gap-2 opacity-0 hover:opacity-100 transition-opacity;
}

.action-btn {
  @apply p-2 bg-white dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors;
}

.action-btn.danger {
  @apply hover:bg-red-100 dark:hover:bg-red-900 hover:text-red-600 dark:hover:text-red-400;
}

.template-date {
  @apply text-xs text-gray-500 dark:text-gray-400;
}

/* States */
.loading-state,
.empty-state {
  @apply flex flex-col items-center justify-center py-16 text-gray-500 dark:text-gray-400;
}

/* Preview Modal */
.preview-modal {
  @apply fixed inset-0 z-[60];
}

.preview-overlay {
  @apply absolute inset-0 bg-black bg-opacity-50;
}

.preview-container {
  @apply absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-4xl bg-white dark:bg-gray-800 rounded-lg shadow-2xl;
}

.preview-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700;
}

.preview-header h3 {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.preview-content {
  @apply p-6 max-h-[60vh] overflow-y-auto;
}

.preview-footer {
  @apply flex items-center justify-end gap-3 p-4 border-t border-gray-200 dark:border-gray-700;
}

/* Buttons */
.btn-primary {
  @apply px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors;
}

.btn-secondary {
  @apply px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors;
}

/* Transitions */
.preview-enter-active,
.preview-leave-active {
  transition: opacity 0.3s ease;
}

.preview-enter-from,
.preview-leave-to {
  opacity: 0;
}
</style>