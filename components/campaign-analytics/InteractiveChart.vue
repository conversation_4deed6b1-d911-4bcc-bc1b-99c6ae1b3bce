<template>
  <div class="interactive-chart">
    <div class="chart-header mb-4">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {{ title }}
          </h3>
          <p v-if="subtitle" class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {{ subtitle }}
          </p>
        </div>
        
        <!-- Chart Controls -->
        <div class="flex items-center space-x-2">
          <!-- Chart Type Selector -->
          <div class="flex items-center space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            <button
              v-for="chartType in availableChartTypes"
              :key="chartType.value"
              @click="currentChartType = chartType.value"
              class="px-3 py-1 text-sm rounded-md transition-colors duration-200"
              :class="currentChartType === chartType.value 
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-gray-100 shadow-sm' 
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'"
            >
              <Icon :name="chartType.icon" class="w-4 h-4" />
            </button>
          </div>
          
          <!-- Time Range Selector -->
          <select 
            v-model="selectedTimeRange"
            class="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800"
          >
            <option value="1h">1 Hour</option>
            <option value="6h">6 Hours</option>
            <option value="24h">24 Hours</option>
            <option value="7d">7 Days</option>
            <option value="30d">30 Days</option>
          </select>
          
          <!-- Chart Actions -->
          <div class="flex items-center space-x-1">
            <button
              @click="toggleFullscreen"
              class="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              :title="isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'"
            >
              <Icon :name="isFullscreen ? 'mdi:fullscreen-exit' : 'mdi:fullscreen'" class="w-4 h-4" />
            </button>
            
            <button
              @click="exportChart"
              class="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              title="Export Chart"
            >
              <Icon name="mdi:download" class="w-4 h-4" />
            </button>
            
            <button
              @click="resetZoom"
              class="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              title="Reset Zoom"
            >
              <Icon name="mdi:magnify-minus" class="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
      
      <!-- Data Series Toggles -->
      <div class="flex flex-wrap items-center gap-3 mt-3">
        <button
          v-for="(series, index) in dataSeries"
          :key="series.name"
          @click="toggleSeries(index)"
          class="flex items-center space-x-2 px-3 py-1 rounded-full text-sm transition-all duration-200"
          :class="series.visible 
            ? 'bg-opacity-10 text-gray-900 dark:text-gray-100' 
            : 'bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400'"
          :style="series.visible ? { backgroundColor: series.color + '20', borderColor: series.color } : {}"
        >
          <div 
            class="w-3 h-3 rounded-full"
            :style="{ backgroundColor: series.visible ? series.color : '#9CA3AF' }"
          ></div>
          <span>{{ series.name }}</span>
          <span class="font-mono text-xs">{{ formatValue(series.currentValue) }}</span>
        </button>
      </div>
    </div>
    
    <!-- Chart Container -->
    <div 
      ref="chartContainer"
      class="chart-container relative"
      :class="{ 'fullscreen': isFullscreen }"
    >
      <canvas ref="chartRef" class="w-full h-full"></canvas>
      
      <!-- Loading Overlay -->
      <div 
        v-if="isLoading"
        class="absolute inset-0 bg-white bg-opacity-75 dark:bg-gray-800 dark:bg-opacity-75 flex items-center justify-center"
      >
        <div class="animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent"></div>
      </div>
      
      <!-- No Data Overlay -->
      <div 
        v-if="!isLoading && (!chartData || chartData.datasets.length === 0)"
        class="absolute inset-0 flex items-center justify-center text-gray-500 dark:text-gray-400"
      >
        <div class="text-center">
          <Icon name="mdi:chart-line" class="w-12 h-12 mx-auto mb-2 opacity-50" />
          <p class="text-sm">No data available</p>
        </div>
      </div>
      
      <!-- Drill-down Tooltip -->
      <div
        v-if="drilldownData"
        ref="tooltipRef"
        class="absolute z-10 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-3 max-w-xs"
        :style="tooltipPosition"
      >
        <div class="text-sm">
          <div class="font-medium text-gray-900 dark:text-gray-100 mb-2">
            {{ drilldownData.title }}
          </div>
          <div class="space-y-1">
            <div 
              v-for="item in drilldownData.items"
              :key="item.label"
              class="flex items-center justify-between"
            >
              <div class="flex items-center space-x-2">
                <div 
                  class="w-2 h-2 rounded-full"
                  :style="{ backgroundColor: item.color }"
                ></div>
                <span class="text-gray-700 dark:text-gray-300">{{ item.label }}</span>
              </div>
              <span class="font-mono text-gray-900 dark:text-gray-100">
                {{ formatValue(item.value) }}
              </span>
            </div>
          </div>
          <div v-if="drilldownData.timestamp" class="text-xs text-gray-500 dark:text-gray-400 mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
            {{ formatTimestamp(drilldownData.timestamp) }}
          </div>
        </div>
      </div>
    </div>
    
    <!-- Chart Statistics -->
    <div class="chart-stats mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
      <div 
        v-for="stat in chartStatistics"
        :key="stat.label"
        class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3"
      >
        <div class="text-xs text-gray-600 dark:text-gray-400">{{ stat.label }}</div>
        <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">
          {{ formatValue(stat.value) }}
        </div>
        <div 
          class="text-xs mt-1"
          :class="stat.change >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'"
        >
          <Icon :name="stat.change >= 0 ? 'mdi:trending-up' : 'mdi:trending-down'" class="w-3 h-3 inline mr-1" />
          {{ Math.abs(stat.change).toFixed(1) }}%
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import Chart from 'chart.js/auto'
import 'chartjs-adapter-date-fns'
import annotationPlugin from 'chartjs-plugin-annotation'

// Register Chart.js plugins
Chart.register(annotationPlugin)

interface ChartDataPoint {
  x: Date | number
  y: number
  metadata?: Record<string, any>
}

interface DataSeries {
  name: string
  data: ChartDataPoint[]
  color: string
  visible: boolean
  currentValue: number
  type?: 'line' | 'bar' | 'area'
}

interface DrilldownData {
  title: string
  items: Array<{
    label: string
    value: number
    color: string
  }>
  timestamp?: Date
}

interface ChartStatistic {
  label: string
  value: number
  change: number
}

interface Props {
  title: string
  subtitle?: string
  dataSeries: DataSeries[]
  height?: number
  enableDrilldown?: boolean
  enableZoom?: boolean
  enableBrush?: boolean
  showStatistics?: boolean
  chartType?: 'line' | 'bar' | 'area' | 'mixed'
}

const props = withDefaults(defineProps<Props>(), {
  height: 400,
  enableDrilldown: true,
  enableZoom: true,
  enableBrush: false,
  showStatistics: true,
  chartType: 'line'
})

const emit = defineEmits<{
  dataPointClick: [data: { seriesIndex: number; dataIndex: number; value: ChartDataPoint }]
  rangeSelect: [range: { start: Date; end: Date }]
  seriesToggle: [seriesIndex: number, visible: boolean]
}>()

// Component state
const chartRef = ref<HTMLCanvasElement | null>(null)
const chartContainer = ref<HTMLDivElement | null>(null)
const tooltipRef = ref<HTMLDivElement | null>(null)

let chartInstance: Chart | null = null
const isLoading = ref(false)
const isFullscreen = ref(false)
const currentChartType = ref(props.chartType)
const selectedTimeRange = ref('24h')

// Drilldown state
const drilldownData = ref<DrilldownData | null>(null)
const tooltipPosition = ref({ left: '0px', top: '0px' })

// Available chart types
const availableChartTypes = [
  { value: 'line', icon: 'mdi:chart-line', label: 'Line' },
  { value: 'bar', icon: 'mdi:chart-bar', label: 'Bar' },
  { value: 'area', icon: 'mdi:chart-areaspline', label: 'Area' },
  { value: 'mixed', icon: 'mdi:chart-multiple', label: 'Mixed' }
]

// Computed properties
const chartData = computed(() => {
  if (!props.dataSeries || props.dataSeries.length === 0) {
    return { labels: [], datasets: [] }
  }

  const datasets = props.dataSeries
    .filter(series => series.visible)
    .map((series, index) => {
      const baseConfig = {
        label: series.name,
        data: series.data,
        borderColor: series.color,
        backgroundColor: series.type === 'area' 
          ? series.color + '20' 
          : series.color,
        borderWidth: 2,
        pointRadius: 3,
        pointHoverRadius: 6,
        pointBackgroundColor: series.color,
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2,
        tension: 0.4,
        fill: series.type === 'area'
      }

      // Customize based on chart type
      if (currentChartType.value === 'bar' || series.type === 'bar') {
        return {
          ...baseConfig,
          type: 'bar' as const,
          borderRadius: 4,
          borderSkipped: false
        }
      } else if (currentChartType.value === 'area' || series.type === 'area') {
        return {
          ...baseConfig,
          type: 'line' as const,
          fill: true,
          backgroundColor: series.color + '20'
        }
      } else {
        return {
          ...baseConfig,
          type: 'line' as const
        }
      }
    })

  return { datasets }
})

const chartStatistics = computed<ChartStatistic[]>(() => {
  if (!props.showStatistics || props.dataSeries.length === 0) {
    return []
  }

  const visibleSeries = props.dataSeries.filter(s => s.visible)
  
  return visibleSeries.map(series => {
    const data = series.data
    const current = data[data.length - 1]?.y || 0
    const previous = data[data.length - 2]?.y || 0
    const change = previous !== 0 ? ((current - previous) / previous) * 100 : 0
    
    return {
      label: series.name,
      value: current,
      change
    }
  })
})

// Methods
const initChart = async () => {
  if (!chartRef.value) return

  const ctx = chartRef.value.getContext('2d')
  if (!ctx) return

  // Destroy existing chart
  if (chartInstance) {
    chartInstance.destroy()
  }

  chartInstance = new Chart(ctx, {
    type: 'line',
    data: chartData.value,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      animation: {
        duration: 300,
        easing: 'easeInOutQuart'
      },
      interaction: {
        mode: 'index',
        intersect: false
      },
      plugins: {
        legend: {
          display: false // We handle legend ourselves
        },
        tooltip: {
          enabled: false, // We use custom tooltip
          external: handleTooltip
        },
        annotation: {
          annotations: {
            // Dynamic annotations can be added here
          }
        }
      },
      scales: {
        x: {
          type: 'time',
          time: {
            unit: getTimeUnit(),
            displayFormats: {
              minute: 'HH:mm',
              hour: 'HH:mm',
              day: 'MMM dd',
              week: 'MMM dd',
              month: 'MMM yyyy'
            }
          },
          grid: {
            color: 'rgba(0, 0, 0, 0.1)',
            borderColor: 'rgba(0, 0, 0, 0.1)'
          },
          ticks: {
            color: '#6B7280'
          }
        },
        y: {
          beginAtZero: true,
          grid: {
            color: 'rgba(0, 0, 0, 0.1)',
            borderColor: 'rgba(0, 0, 0, 0.1)'
          },
          ticks: {
            color: '#6B7280',
            callback: function(value) {
              return formatValue(value as number)
            }
          }
        }
      },
      onHover: (event, elements) => {
        if (props.enableDrilldown && elements.length > 0) {
          chartRef.value!.style.cursor = 'pointer'
        } else {
          chartRef.value!.style.cursor = 'default'
        }
      },
      onClick: (event, elements) => {
        if (props.enableDrilldown && elements.length > 0) {
          handleChartClick(event, elements)
        }
      }
    }
  })

  // Enable zoom plugin if requested
  if (props.enableZoom) {
    await enableZoomPlugin()
  }
}

const enableZoomPlugin = async () => {
  // Dynamically import zoom plugin for better performance
  const { default: zoomPlugin } = await import('chartjs-plugin-zoom')
  Chart.register(zoomPlugin)
  
  if (chartInstance) {
    chartInstance.options.plugins!.zoom = {
      zoom: {
        wheel: {
          enabled: true,
        },
        pinch: {
          enabled: true
        },
        drag: {
          enabled: true,
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          borderColor: 'rgba(59, 130, 246, 0.3)',
          borderWidth: 1
        },
        mode: 'x',
        onZoom: ({ chart }) => {
          // Handle zoom events
          const range = getZoomRange(chart)
          if (range) {
            emit('rangeSelect', range)
          }
        }
      },
      pan: {
        enabled: true,
        mode: 'x'
      }
    }
    chartInstance.update()
  }
}

const handleTooltip = (context: any) => {
  if (!props.enableDrilldown) return

  const { chart, tooltip } = context
  
  if (tooltip.opacity === 0) {
    drilldownData.value = null
    return
  }

  const dataPoints = tooltip.dataPoints
  if (!dataPoints || dataPoints.length === 0) return

  const items = dataPoints.map((point: any) => ({
    label: point.dataset.label,
    value: point.parsed.y,
    color: point.dataset.borderColor
  }))

  drilldownData.value = {
    title: tooltip.title[0] || 'Data Point',
    items,
    timestamp: new Date(dataPoints[0].parsed.x)
  }

  // Position tooltip
  const position = Chart.helpers.getRelativePosition(tooltip, chart)
  tooltipPosition.value = {
    left: position.x + 'px',
    top: position.y + 'px'
  }
}

const handleChartClick = (event: any, elements: any[]) => {
  if (elements.length === 0) return

  const element = elements[0]
  const seriesIndex = element.datasetIndex
  const dataIndex = element.index
  const series = props.dataSeries[seriesIndex]
  const dataPoint = series.data[dataIndex]

  emit('dataPointClick', {
    seriesIndex,
    dataIndex,
    value: dataPoint
  })
}

const toggleSeries = (index: number) => {
  const series = props.dataSeries[index]
  series.visible = !series.visible
  emit('seriesToggle', index, series.visible)
  
  if (chartInstance) {
    chartInstance.update()
  }
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  
  nextTick(() => {
    if (chartInstance) {
      chartInstance.resize()
    }
  })
}

const exportChart = () => {
  if (!chartInstance) return

  const url = chartInstance.toBase64Image('image/png', 1)
  const link = document.createElement('a')
  link.download = `${props.title.toLowerCase().replace(/\s+/g, '-')}-chart.png`
  link.href = url
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

const resetZoom = () => {
  if (chartInstance && (chartInstance as any).resetZoom) {
    (chartInstance as any).resetZoom()
  }
}

const getTimeUnit = () => {
  switch (selectedTimeRange.value) {
    case '1h':
      return 'minute'
    case '6h':
      return 'hour'
    case '24h':
      return 'hour'
    case '7d':
      return 'day'
    case '30d':
      return 'day'
    default:
      return 'hour'
  }
}

const getZoomRange = (chart: Chart): { start: Date; end: Date } | null => {
  const xScale = chart.scales.x
  if (!xScale) return null

  return {
    start: new Date(xScale.min),
    end: new Date(xScale.max)
  }
}

const formatValue = (value: number): string => {
  if (value >= 1000000) {
    return (value / 1000000).toFixed(1) + 'M'
  } else if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'K'
  } else if (value < 1) {
    return value.toFixed(3)
  }
  return value.toFixed(0)
}

const formatTimestamp = (timestamp: Date): string => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(timestamp)
}

// Watchers
watch(() => props.dataSeries, () => {
  if (chartInstance) {
    chartInstance.data = chartData.value
    chartInstance.update('none')
  }
}, { deep: true })

watch(currentChartType, () => {
  initChart()
})

watch(selectedTimeRange, () => {
  if (chartInstance) {
    chartInstance.options.scales!.x!.time!.unit = getTimeUnit()
    chartInstance.update()
  }
})

// Lifecycle
onMounted(() => {
  initChart()
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.destroy()
    chartInstance = null
  }
})
</script>

<style scoped>
.interactive-chart {
  @apply w-full;
}

.chart-container {
  @apply relative bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4;
  height: v-bind('props.height + "px"');
}

.chart-container.fullscreen {
  @apply fixed inset-0 z-50 m-4;
  height: calc(100vh - 2rem);
}

.chart-stats {
  @apply mt-4;
}

/* Smooth transitions for tooltip */
.drilldown-tooltip {
  transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
}
</style>