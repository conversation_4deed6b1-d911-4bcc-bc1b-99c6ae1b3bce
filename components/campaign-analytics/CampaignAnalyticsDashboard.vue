<template>
  <div class="campaign-analytics-dashboard">
    <!-- Dashboard Header -->
    <div class="dashboard-header mb-6">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div class="flex items-center space-x-4">
          <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">
            Real-time Campaign Analytics
          </h1>
          <div class="flex items-center space-x-2">
            <div 
              class="w-3 h-3 rounded-full"
              :class="{
                'bg-green-500 animate-pulse': connectionHealth === 'good',
                'bg-yellow-500': connectionHealth === 'fair',
                'bg-red-500': connectionHealth === 'poor' || !isConnected,
                'bg-gray-400': connectionHealth === 'unknown'
              }"
            ></div>
            <span class="text-sm text-gray-600 dark:text-gray-400">
              {{ connectionStatus === 'connected' ? 'Live' : 'Offline' }}
            </span>
          </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="mt-4 lg:mt-0 flex items-center space-x-3">
          <!-- Alerts Indicator -->
          <button 
            @click="showAlertsPanel = !showAlertsPanel"
            class="relative p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <Icon name="mdi:bell" class="w-6 h-6" />
            <span 
              v-if="unreadAlertsCount > 0"
              class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center"
            >
              {{ unreadAlertsCount > 99 ? '99+' : unreadAlertsCount }}
            </span>
          </button>
          
          <!-- Dashboard Settings -->
          <button 
            @click="showSettingsPanel = !showSettingsPanel"
            class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <Icon name="mdi:cog" class="w-6 h-6" />
          </button>
          
          <!-- Export Data -->
          <button 
            @click="handleExportData"
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
          >
            <Icon name="mdi:download" class="w-4 h-4 mr-2" />
            Export
          </button>
        </div>
      </div>
      
      <!-- Campaign Selector -->
      <div class="mt-4 flex flex-wrap gap-3">
        <select 
          v-model="selectedCampaign"
          @change="handleCampaignChange"
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
        >
          <option value="">All Campaigns</option>
          <option v-for="campaign in availableCampaigns" :key="campaign.id" :value="campaign.id">
            {{ campaign.name }}
          </option>
        </select>
        
        <!-- Time Range Selector -->
        <div class="flex items-center space-x-2">
          <label class="text-sm text-gray-600 dark:text-gray-400">Range:</label>
          <select 
            v-model="selectedTimeRange"
            class="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800"
          >
            <option value="1h">Last Hour</option>
            <option value="6h">Last 6 Hours</option>
            <option value="24h">Last 24 Hours</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-4 border-blue-600 border-t-transparent"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="mb-6 p-4 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 rounded-lg">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="font-medium">Connection Error</h3>
          <p class="text-sm mt-1">{{ error }}</p>
        </div>
        <button 
          @click="reconnect"
          class="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
        >
          Retry
        </button>
      </div>
    </div>

    <!-- Real-time Metrics Grid -->
    <div class="metrics-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Impressions -->
      <div class="metric-card bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">Impressions</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-gray-100 mt-1">
              {{ formatNumber(aggregatedMetrics.impressions) }}
            </p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">
              +{{ formatNumber(realTimeMetrics.impressions) }} live
            </p>
          </div>
          <div class="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
            <Icon name="mdi:eye" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
        </div>
        <div class="mt-4 h-12">
          <MiniChart 
            :data="impressionChartData" 
            color="#3B82F6"
            type="area"
          />
        </div>
      </div>

      <!-- Clicks -->
      <div class="metric-card bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">Clicks</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-gray-100 mt-1">
              {{ formatNumber(aggregatedMetrics.clicks) }}
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {{ formatPercentage(aggregatedMetrics.ctr) }} CTR
            </p>
          </div>
          <div class="p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
            <Icon name="mdi:cursor-pointer" class="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
        </div>
        <div class="mt-4 h-12">
          <MiniChart 
            :data="clickChartData" 
            color="#10B981"
            type="line"
          />
        </div>
      </div>

      <!-- Conversions -->
      <div class="metric-card bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">Conversions</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-gray-100 mt-1">
              {{ formatNumber(aggregatedMetrics.conversions) }}
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {{ formatPercentage(aggregatedMetrics.conversionRate) }} rate
            </p>
          </div>
          <div class="p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg">
            <Icon name="mdi:target" class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
          </div>
        </div>
        <div class="mt-4 h-12">
          <MiniChart 
            :data="conversionChartData" 
            color="#F59E0B"
            type="bar"
          />
        </div>
      </div>

      <!-- Revenue -->
      <div class="metric-card bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">Revenue</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-gray-100 mt-1">
              {{ formatCurrency(aggregatedMetrics.revenue) }}
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {{ formatPercentage(realTimeMetrics.roas) }} ROAS
            </p>
          </div>
          <div class="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
            <Icon name="mdi:currency-usd" class="w-6 h-6 text-purple-600 dark:text-purple-400" />
          </div>
        </div>
        <div class="mt-4 h-12">
          <MiniChart 
            :data="revenueChartData" 
            color="#8B5CF6"
            type="area"
          />
        </div>
      </div>
    </div>

    <!-- Main Charts Section -->
    <div class="charts-section grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Performance Overview Chart -->
      <div class="chart-container bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Performance Overview
          </h3>
          <div class="flex items-center space-x-2">
            <button 
              v-for="chartType in chartTypes"
              :key="chartType.value"
              @click="selectedChartType = chartType.value"
              class="px-3 py-1 text-sm rounded-lg transition-colors duration-200"
              :class="selectedChartType === chartType.value 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600'"
            >
              {{ chartType.label }}
            </button>
          </div>
        </div>
        <div class="chart-wrapper h-80">
          <canvas ref="performanceChartRef"></canvas>
        </div>
      </div>

      <!-- Real-time Event Stream -->
      <div class="event-stream bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Live Event Stream
          </h3>
          <div class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span class="text-sm text-gray-500 dark:text-gray-400">Live</span>
          </div>
        </div>
        <div class="event-list h-72 overflow-y-auto space-y-2">
          <TransitionGroup name="event" tag="div">
            <div 
              v-for="event in recentEvents" 
              :key="event.id"
              class="event-item p-3 rounded-lg border-l-4 transition-all duration-300"
              :class="{
                'bg-blue-50 dark:bg-blue-900/20 border-blue-500': event.eventType === 'impression',
                'bg-green-50 dark:bg-green-900/20 border-green-500': event.eventType === 'click',
                'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-500': event.eventType === 'conversion',
                'bg-purple-50 dark:bg-purple-900/20 border-purple-500': event.eventType === 'view'
              }"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                  <Icon 
                    :name="getEventIcon(event.eventType)" 
                    class="w-4 h-4"
                    :class="{
                      'text-blue-600': event.eventType === 'impression',
                      'text-green-600': event.eventType === 'click',
                      'text-yellow-600': event.eventType === 'conversion',
                      'text-purple-600': event.eventType === 'view'
                    }"
                  />
                  <span class="text-sm font-medium text-gray-900 dark:text-gray-100 capitalize">
                    {{ event.eventType }}
                  </span>
                  <span v-if="event.value" class="text-xs text-gray-500 dark:text-gray-400">
                    {{ formatCurrency(event.value) }}
                  </span>
                </div>
                <span class="text-xs text-gray-500 dark:text-gray-400">
                  {{ formatTime(event.timestamp) }}
                </span>
              </div>
              <div class="mt-1 text-xs text-gray-600 dark:text-gray-400">
                <span class="font-mono">{{ event.adId.slice(-8) }}</span>
                <span class="mx-2">•</span>
                <span>{{ event.deviceInfo.type }}</span>
                <span v-if="event.location" class="mx-2">•</span>
                <span v-if="event.location">{{ event.location.country }}</span>
              </div>
            </div>
          </TransitionGroup>
        </div>
      </div>
    </div>

    <!-- Alerts Panel (Sliding Panel) -->
    <div 
      v-if="showAlertsPanel"
      class="fixed inset-y-0 right-0 w-96 bg-white dark:bg-gray-800 shadow-xl z-50 transform transition-transform duration-300 ease-in-out"
      :class="showAlertsPanel ? 'translate-x-0' : 'translate-x-full'"
    >
      <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
          Performance Alerts
        </h3>
        <button 
          @click="showAlertsPanel = false"
          class="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          <Icon name="mdi:close" class="w-5 h-5" />
        </button>
      </div>
      
      <div class="overflow-y-auto h-full pb-16">
        <div v-if="alerts.length === 0" class="p-4 text-center text-gray-500 dark:text-gray-400">
          <Icon name="mdi:check-circle" class="w-12 h-12 mx-auto mb-2 text-green-500" />
          <p>No active alerts</p>
        </div>
        
        <div v-else class="space-y-3 p-4">
          <div 
            v-for="alert in alerts.slice(0, 20)" 
            :key="alert.id"
            class="alert-item p-4 rounded-lg border-l-4 transition-all duration-200"
            :class="{
              'bg-red-50 dark:bg-red-900/20 border-red-500': alert.severity === 'critical',
              'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-500': alert.severity === 'high',
              'bg-blue-50 dark:bg-blue-900/20 border-blue-500': alert.severity === 'medium',
              'bg-gray-50 dark:bg-gray-900/20 border-gray-500': alert.severity === 'low',
              'opacity-60': alert.isRead
            }"
          >
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <div class="flex items-center space-x-2">
                  <Icon 
                    :name="getAlertIcon(alert.type)" 
                    class="w-4 h-4"
                    :class="{
                      'text-red-600': alert.severity === 'critical',
                      'text-yellow-600': alert.severity === 'high',
                      'text-blue-600': alert.severity === 'medium',
                      'text-gray-600': alert.severity === 'low'
                    }"
                  />
                  <span class="text-sm font-medium text-gray-900 dark:text-gray-100 capitalize">
                    {{ alert.type.replace('_', ' ') }}
                  </span>
                  <span 
                    class="px-2 py-1 text-xs rounded-full"
                    :class="{
                      'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300': alert.severity === 'critical',
                      'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300': alert.severity === 'high',
                      'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300': alert.severity === 'medium',
                      'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-300': alert.severity === 'low'
                    }"
                  >
                    {{ alert.severity }}
                  </span>
                </div>
                <p class="text-sm text-gray-700 dark:text-gray-300 mt-2">
                  {{ alert.message }}
                </p>
                <div v-if="alert.recommendedActions && alert.recommendedActions.length > 0" class="mt-2">
                  <p class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Recommended Actions:</p>
                  <ul class="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                    <li v-for="action in alert.recommendedActions" :key="action" class="flex items-start">
                      <Icon name="mdi:chevron-right" class="w-3 h-3 mt-0.5 mr-1 flex-shrink-0" />
                      {{ action }}
                    </li>
                  </ul>
                </div>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                  {{ formatTime(alert.timestamp) }}
                </p>
              </div>
              <button 
                v-if="!alert.isRead"
                @click="markAlertAsRead(alert.id)"
                class="ml-2 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <Icon name="mdi:check" class="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
        
        <div v-if="alerts.length > 0" class="p-4 border-t border-gray-200 dark:border-gray-700">
          <button 
            @click="clearAllAlerts"
            class="w-full px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            Clear All Alerts
          </button>
        </div>
      </div>
    </div>

    <!-- Overlay for alerts panel -->
    <div 
      v-if="showAlertsPanel"
      @click="showAlertsPanel = false"
      class="fixed inset-0 bg-black bg-opacity-50 z-40"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRealTimeAnalytics } from '~/composables/useRealTimeAnalytics'
import Chart from 'chart.js/auto'
import MiniChart from '~/components/campaign-analytics/MiniChart.vue'

// Props
interface Props {
  campaignId?: string
  autoConnect?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoConnect: true
})

// Real-time analytics composable
const {
  isConnected,
  connectionStatus,
  connectionHealth,
  realTimeMetrics,
  eventStream,
  alerts,
  impressionChart,
  clickChart,
  conversionChart,
  revenueChart,
  isLoading,
  error,
  unreadAlertsCount,
  criticalAlertsCount,
  initWebSocket,
  disconnect,
  subscribeToCampaign,
  unsubscribeFromCampaign,
  markAlertAsRead,
  clearAllAlerts,
  getAggregatedMetrics,
  exportRealTimeData
} = useRealTimeAnalytics()

// Component state
const selectedCampaign = ref(props.campaignId || '')
const selectedTimeRange = ref<'1h' | '6h' | '24h'>('24h')
const selectedChartType = ref<'performance' | 'funnel' | 'geographic'>('performance')
const showAlertsPanel = ref(false)
const showSettingsPanel = ref(false)

// Chart refs
const performanceChartRef = ref<HTMLCanvasElement | null>(null)
let performanceChart: Chart | null = null

// Mock data for campaigns (would come from API)
const availableCampaigns = ref([
  { id: 'campaign-1', name: 'Holiday Sale 2024' },
  { id: 'campaign-2', name: 'Brand Awareness Q1' },
  { id: 'campaign-3', name: 'Product Launch' }
])

// Chart configuration
const chartTypes = [
  { value: 'performance', label: 'Performance' },
  { value: 'funnel', label: 'Conversion Funnel' },
  { value: 'geographic', label: 'Geographic' }
]

// Computed properties
const aggregatedMetrics = computed(() => getAggregatedMetrics(selectedTimeRange.value))

const recentEvents = computed(() => eventStream.value.slice(0, 50))

const impressionChartData = computed(() => 
  impressionChart.value.slice(-20).map(point => ({
    x: point.timestamp,
    y: point.value
  }))
)

const clickChartData = computed(() => 
  clickChart.value.slice(-20).map(point => ({
    x: point.timestamp,
    y: point.value
  }))
)

const conversionChartData = computed(() => 
  conversionChart.value.slice(-20).map(point => ({
    x: point.timestamp,
    y: point.value
  }))
)

const revenueChartData = computed(() => 
  revenueChart.value.slice(-20).map(point => ({
    x: point.timestamp,
    y: point.value
  }))
)

// Methods
const handleCampaignChange = () => {
  // Unsubscribe from previous campaign
  if (props.campaignId) {
    unsubscribeFromCampaign(props.campaignId)
  }
  
  // Subscribe to new campaign if selected
  if (selectedCampaign.value) {
    subscribeToCampaign(selectedCampaign.value)
  }
}

const reconnect = () => {
  error.value = null
  initWebSocket()
}

const handleExportData = () => {
  const data = exportRealTimeData('json')
  const blob = new Blob([data], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `campaign-analytics-${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

const initPerformanceChart = () => {
  if (!performanceChartRef.value) return
  
  const ctx = performanceChartRef.value.getContext('2d')
  if (!ctx) return

  performanceChart = new Chart(ctx, {
    type: 'line',
    data: {
      labels: [],
      datasets: [
        {
          label: 'Impressions',
          data: [],
          borderColor: '#3B82F6',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          tension: 0.4,
          fill: true
        },
        {
          label: 'Clicks',
          data: [],
          borderColor: '#10B981',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          tension: 0.4,
          fill: true
        },
        {
          label: 'Conversions',
          data: [],
          borderColor: '#F59E0B',
          backgroundColor: 'rgba(245, 158, 11, 0.1)',
          tension: 0.4,
          fill: true,
          yAxisID: 'y1'
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      animation: false, // Better performance for real-time updates
      interaction: {
        mode: 'index',
        intersect: false
      },
      plugins: {
        legend: {
          position: 'top'
        },
        tooltip: {
          mode: 'index',
          intersect: false
        }
      },
      scales: {
        x: {
          type: 'time',
          time: {
            unit: 'minute'
          },
          grid: {
            color: 'rgba(0, 0, 0, 0.1)'
          }
        },
        y: {
          type: 'linear',
          display: true,
          position: 'left',
          grid: {
            color: 'rgba(0, 0, 0, 0.1)'
          }
        },
        y1: {
          type: 'linear',
          display: true,
          position: 'right',
          grid: {
            drawOnChartArea: false
          }
        }
      }
    }
  })
}

const updatePerformanceChart = () => {
  if (!performanceChart) return

  const now = new Date()
  const timeLabels = Array.from({ length: 20 }, (_, i) => 
    new Date(now.getTime() - (19 - i) * 60000)
  )

  performanceChart.data.labels = timeLabels
  performanceChart.data.datasets[0].data = impressionChartData.value
  performanceChart.data.datasets[1].data = clickChartData.value
  performanceChart.data.datasets[2].data = conversionChartData.value

  performanceChart.update('none') // No animation for better performance
}

// Helper functions
const formatNumber = (value: number): string => {
  if (value >= 1000000) {
    return (value / 1000000).toFixed(1) + 'M'
  } else if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'K'
  }
  return value.toString()
}

const formatPercentage = (value: number): string => {
  return value.toFixed(2) + '%'
}

const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(value)
}

const formatTime = (timestamp: Date): string => {
  const now = new Date()
  const diffMs = now.getTime() - timestamp.getTime()
  const diffSecs = Math.floor(diffMs / 1000)
  const diffMins = Math.floor(diffSecs / 60)
  const diffHours = Math.floor(diffMins / 60)

  if (diffSecs < 60) {
    return `${diffSecs}s ago`
  } else if (diffMins < 60) {
    return `${diffMins}m ago`
  } else if (diffHours < 24) {
    return `${diffHours}h ago`
  } else {
    return timestamp.toLocaleDateString()
  }
}

const getEventIcon = (eventType: string): string => {
  const icons = {
    impression: 'mdi:eye',
    click: 'mdi:cursor-pointer',
    conversion: 'mdi:target',
    view: 'mdi:play'
  }
  return icons[eventType] || 'mdi:circle'
}

const getAlertIcon = (alertType: string): string => {
  const icons = {
    performance_drop: 'mdi:trending-down',
    budget_exceeded: 'mdi:wallet',
    anomaly_detected: 'mdi:alert',
    target_achieved: 'mdi:trophy'
  }
  return icons[alertType] || 'mdi:information'
}

// Watchers
watch([impressionChart, clickChart, conversionChart], () => {
  updatePerformanceChart()
}, { deep: true })

// Lifecycle
onMounted(() => {
  if (props.autoConnect) {
    initWebSocket()
  }
  
  if (selectedCampaign.value) {
    subscribeToCampaign(selectedCampaign.value)
  }
  
  initPerformanceChart()
})

onUnmounted(() => {
  if (performanceChart) {
    performanceChart.destroy()
  }
  disconnect()
})
</script>

<style scoped>
.campaign-analytics-dashboard {
  @apply min-h-screen bg-gray-50 dark:bg-gray-900 p-6;
}

.metric-card {
  @apply transition-all duration-200 hover:shadow-md;
}

.chart-wrapper {
  @apply relative;
}

.event-item {
  @apply transition-all duration-300;
}

.event-enter-active,
.event-leave-active {
  transition: all 0.3s ease;
}

.event-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.event-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

.alert-item {
  @apply transition-all duration-200 hover:shadow-sm;
}

/* Custom scrollbar for event stream */
.event-list::-webkit-scrollbar {
  width: 4px;
}

.event-list::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

.event-list::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded;
}

.event-list::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}
</style>