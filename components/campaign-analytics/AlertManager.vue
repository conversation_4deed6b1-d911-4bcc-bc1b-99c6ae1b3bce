<template>
  <div class="alert-manager">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
          Performance Alerts
        </h2>
        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Create and manage automated performance alerts for your campaigns
        </p>
      </div>
      
      <button
        @click="showCreateAlert = true"
        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
      >
        <Icon name="mdi:plus" class="w-4 h-4 mr-2" />
        Create Alert
      </button>
    </div>

    <!-- Alert Rules List -->
    <div class="space-y-4 mb-8">
      <div 
        v-for="rule in alertRules"
        :key="rule.id"
        class="alert-rule-card bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6"
      >
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <div class="flex items-center space-x-3">
              <div 
                class="w-3 h-3 rounded-full"
                :class="rule.isActive ? 'bg-green-500' : 'bg-gray-400'"
              ></div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {{ rule.name }}
              </h3>
              <span 
                class="px-2 py-1 text-xs rounded-full"
                :class="{
                  'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300': getSeverityColor(rule) === 'red',
                  'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300': getSeverityColor(rule) === 'yellow',
                  'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300': getSeverityColor(rule) === 'blue',
                  'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-300': getSeverityColor(rule) === 'gray'
                }"
              >
                {{ rule.condition.metric }} {{ getComparisonText(rule.comparison) }} {{ formatThreshold(rule) }}
              </span>
            </div>
            
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">
              {{ rule.description }}
            </p>
            
            <div class="flex items-center space-x-4 mt-3 text-sm text-gray-500 dark:text-gray-400">
              <div class="flex items-center space-x-1">
                <Icon name="mdi:clock" class="w-4 h-4" />
                <span>{{ rule.timeframe }}min window</span>
              </div>
              
              <div class="flex items-center space-x-1">
                <Icon name="mdi:bell" class="w-4 h-4" />
                <span>{{ rule.notificationChannels.length }} channel{{ rule.notificationChannels.length !== 1 ? 's' : '' }}</span>
              </div>
              
              <div v-if="rule.lastTriggered" class="flex items-center space-x-1">
                <Icon name="mdi:alert" class="w-4 h-4" />
                <span>Last triggered {{ formatRelativeTime(rule.lastTriggered) }}</span>
              </div>
            </div>
          </div>
          
          <!-- Alert Actions -->
          <div class="flex items-center space-x-2">
            <button
              @click="toggleAlert(rule.id)"
              class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              :title="rule.isActive ? 'Disable Alert' : 'Enable Alert'"
            >
              <Icon :name="rule.isActive ? 'mdi:pause' : 'mdi:play'" class="w-4 h-4" />
            </button>
            
            <button
              @click="editAlert(rule)"
              class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              title="Edit Alert"
            >
              <Icon name="mdi:pencil" class="w-4 h-4" />
            </button>
            
            <button
              @click="deleteAlert(rule.id)"
              class="p-2 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
              title="Delete Alert"
            >
              <Icon name="mdi:delete" class="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
      
      <!-- Empty State -->
      <div v-if="alertRules.length === 0" class="text-center py-12 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <Icon name="mdi:bell-off" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">No Alert Rules</h3>
        <p class="mt-2 text-gray-500 dark:text-gray-400">
          Create your first alert rule to get notified about campaign performance changes.
        </p>
        <button
          @click="showCreateAlert = true"
          class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          Create Your First Alert
        </button>
      </div>
    </div>

    <!-- Recent Triggered Alerts -->
    <div v-if="triggeredAlerts.length > 0" class="recent-alerts">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
        Recent Triggered Alerts
      </h3>
      
      <div class="space-y-3">
        <div 
          v-for="alert in triggeredAlerts.slice(0, 10)"
          :key="alert.id"
          class="triggered-alert p-4 rounded-lg border-l-4"
          :class="{
            'bg-red-50 dark:bg-red-900/20 border-red-500': alert.severity === 'critical',
            'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-500': alert.severity === 'high',
            'bg-blue-50 dark:bg-blue-900/20 border-blue-500': alert.severity === 'medium',
            'bg-gray-50 dark:bg-gray-900/20 border-gray-500': alert.severity === 'low'
          }"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-2">
                <Icon 
                  :name="getAlertIcon(alert.type)" 
                  class="w-4 h-4"
                  :class="{
                    'text-red-600': alert.severity === 'critical',
                    'text-yellow-600': alert.severity === 'high',
                    'text-blue-600': alert.severity === 'medium',
                    'text-gray-600': alert.severity === 'low'
                  }"
                />
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {{ alert.type.replace('_', ' ').toUpperCase() }}
                </span>
                <span class="text-xs text-gray-500 dark:text-gray-400">
                  {{ formatRelativeTime(alert.timestamp) }}
                </span>
              </div>
              
              <p class="text-sm text-gray-700 dark:text-gray-300 mt-1">
                {{ alert.message }}
              </p>
              
              <div v-if="alert.recommendedActions && alert.recommendedActions.length > 0" class="mt-2">
                <div class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                  Recommended Actions:
                </div>
                <ul class="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                  <li v-for="action in alert.recommendedActions" :key="action" class="flex items-start">
                    <Icon name="mdi:chevron-right" class="w-3 h-3 mt-0.5 mr-1 flex-shrink-0" />
                    {{ action }}
                  </li>
                </ul>
              </div>
            </div>
            
            <div class="flex items-center space-x-2">
              <button
                v-if="!alert.isRead"
                @click="markAlertAsRead(alert.id)"
                class="px-2 py-1 text-xs text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded"
              >
                Mark Read
              </button>
              
              <button
                @click="viewAlertDetails(alert)"
                class="px-2 py-1 text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 border border-blue-300 dark:border-blue-600 rounded"
              >
                View Details
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Create/Edit Alert Modal -->
    <div 
      v-if="showCreateAlert || editingAlert"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    >
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {{ editingAlert ? 'Edit Alert Rule' : 'Create Alert Rule' }}
          </h3>
        </div>
        
        <form @submit.prevent="saveAlert" class="p-6 space-y-6">
          <!-- Alert Name -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Alert Name
            </label>
            <input
              v-model="alertForm.name"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              placeholder="e.g., CTR Drop Alert"
            />
          </div>
          
          <!-- Description -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Description
            </label>
            <textarea
              v-model="alertForm.description"
              rows="2"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              placeholder="Brief description of what this alert monitors"
            ></textarea>
          </div>
          
          <!-- Metric Selection -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Metric
              </label>
              <select
                v-model="alertForm.condition.metric"
                required
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700"
              >
                <option value="">Select Metric</option>
                <option value="ctr">Click-Through Rate (CTR)</option>
                <option value="cpm">Cost Per Mille (CPM)</option>
                <option value="cpc">Cost Per Click (CPC)</option>
                <option value="cpa">Cost Per Acquisition (CPA)</option>
                <option value="roas">Return on Ad Spend (ROAS)</option>
                <option value="impressions">Impressions</option>
                <option value="clicks">Clicks</option>
                <option value="conversions">Conversions</option>
                <option value="spend">Spend</option>
                <option value="quality_score">Quality Score</option>
              </select>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Comparison
              </label>
              <select
                v-model="alertForm.comparison"
                required
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700"
              >
                <option value="greater_than">Greater Than</option>
                <option value="less_than">Less Than</option>
                <option value="equals">Equals</option>
                <option value="percentage_change">Percentage Change</option>
              </select>
            </div>
          </div>
          
          <!-- Threshold and Timeframe -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Threshold Value
              </label>
              <input
                v-model.number="alertForm.threshold"
                type="number"
                step="0.01"
                required
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                placeholder="0.00"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Time Window (minutes)
              </label>
              <select
                v-model.number="alertForm.timeframe"
                required
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700"
              >
                <option :value="5">5 minutes</option>
                <option :value="15">15 minutes</option>
                <option :value="30">30 minutes</option>
                <option :value="60">1 hour</option>
                <option :value="180">3 hours</option>
                <option :value="360">6 hours</option>
              </select>
            </div>
          </div>
          
          <!-- Campaign Selection -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Apply to Campaigns
            </label>
            <select
              v-model="alertForm.campaignId"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700"
            >
              <option value="">All Campaigns</option>
              <option v-for="campaign in availableCampaigns" :key="campaign.id" :value="campaign.id">
                {{ campaign.name }}
              </option>
            </select>
          </div>
          
          <!-- Notification Channels -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Notification Channels
            </label>
            <div class="space-y-2">
              <label class="flex items-center">
                <input
                  v-model="alertForm.notificationChannels"
                  type="checkbox"
                  value="email"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Email</span>
              </label>
              
              <label class="flex items-center">
                <input
                  v-model="alertForm.notificationChannels"
                  type="checkbox"
                  value="push"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Push Notification</span>
              </label>
              
              <label class="flex items-center">
                <input
                  v-model="alertForm.notificationChannels"
                  type="checkbox"
                  value="in_app"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">In-App Notification</span>
              </label>
              
              <label class="flex items-center">
                <input
                  v-model="alertForm.notificationChannels"
                  type="checkbox"
                  value="slack"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Slack</span>
              </label>
            </div>
          </div>
          
          <!-- Active Toggle -->
          <div class="flex items-center">
            <input
              v-model="alertForm.isActive"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
              Enable this alert rule immediately
            </span>
          </div>
          
          <!-- Form Actions -->
          <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              @click="cancelAlert"
              class="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              Cancel
            </button>
            
            <button
              type="submit"
              :disabled="isSubmitting"
              class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="isSubmitting" class="flex items-center">
                <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                Saving...
              </span>
              <span v-else>
                {{ editingAlert ? 'Update Alert' : 'Create Alert' }}
              </span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRealTimeAnalytics } from '~/composables/useRealTimeAnalytics'

// Interface definitions
interface AlertRule {
  id: string
  userId: string
  campaignId?: string
  name: string
  description: string
  condition: {
    metric: string
  }
  threshold: number
  comparison: 'greater_than' | 'less_than' | 'equals' | 'percentage_change'
  timeframe: number
  isActive: boolean
  notificationChannels: string[]
  lastTriggered?: Date
  createdAt: Date
}

interface TriggeredAlert {
  id: string
  type: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  campaignId: string
  timestamp: Date
  isRead: boolean
  recommendedActions?: string[]
}

const { alerts: triggeredAlerts, markAlertAsRead } = useRealTimeAnalytics()

// Component state
const alertRules = ref<AlertRule[]>([])
const showCreateAlert = ref(false)
const editingAlert = ref<AlertRule | null>(null)
const isSubmitting = ref(false)

// Mock campaigns data
const availableCampaigns = ref([
  { id: 'campaign-1', name: 'Holiday Sale 2024' },
  { id: 'campaign-2', name: 'Brand Awareness Q1' },
  { id: 'campaign-3', name: 'Product Launch' }
])

// Alert form
const alertForm = reactive({
  name: '',
  description: '',
  condition: {
    metric: ''
  },
  threshold: 0,
  comparison: 'less_than' as const,
  timeframe: 30,
  campaignId: '',
  notificationChannels: ['email', 'in_app'] as string[],
  isActive: true
})

// Methods
const resetAlertForm = () => {
  Object.assign(alertForm, {
    name: '',
    description: '',
    condition: { metric: '' },
    threshold: 0,
    comparison: 'less_than',
    timeframe: 30,
    campaignId: '',
    notificationChannels: ['email', 'in_app'],
    isActive: true
  })
}

const editAlert = (rule: AlertRule) => {
  editingAlert.value = rule
  Object.assign(alertForm, {
    name: rule.name,
    description: rule.description,
    condition: { ...rule.condition },
    threshold: rule.threshold,
    comparison: rule.comparison,
    timeframe: rule.timeframe,
    campaignId: rule.campaignId || '',
    notificationChannels: [...rule.notificationChannels],
    isActive: rule.isActive
  })
}

const saveAlert = async () => {
  isSubmitting.value = true
  
  try {
    const alertData = {
      ...alertForm,
      id: editingAlert.value?.id || generateId(),
      userId: 'current-user-id', // Would come from auth
      createdAt: editingAlert.value?.createdAt || new Date()
    }

    if (editingAlert.value) {
      // Update existing alert
      const index = alertRules.value.findIndex(rule => rule.id === editingAlert.value?.id)
      if (index !== -1) {
        alertRules.value[index] = alertData as AlertRule
      }
    } else {
      // Create new alert
      alertRules.value.push(alertData as AlertRule)
    }

    // Here you would normally save to the backend
    // await saveAlertRule(alertData)

    cancelAlert()
  } catch (error) {
    console.error('Error saving alert:', error)
  } finally {
    isSubmitting.value = false
  }
}

const cancelAlert = () => {
  showCreateAlert.value = false
  editingAlert.value = null
  resetAlertForm()
}

const toggleAlert = async (alertId: string) => {
  const rule = alertRules.value.find(r => r.id === alertId)
  if (rule) {
    rule.isActive = !rule.isActive
    // Here you would normally update the backend
    // await updateAlertRule(rule)
  }
}

const deleteAlert = async (alertId: string) => {
  if (confirm('Are you sure you want to delete this alert rule?')) {
    alertRules.value = alertRules.value.filter(rule => rule.id !== alertId)
    // Here you would normally delete from the backend
    // await deleteAlertRule(alertId)
  }
}

const viewAlertDetails = (alert: TriggeredAlert) => {
  // Navigate to alert details or show modal
  console.log('View alert details:', alert)
}

// Helper functions
const generateId = () => {
  return Math.random().toString(36).substr(2, 9)
}

const getSeverityColor = (rule: AlertRule): string => {
  // Determine severity based on metric and threshold
  switch (rule.condition.metric) {
    case 'ctr':
    case 'roas':
    case 'quality_score':
      return rule.comparison === 'less_than' ? 'red' : 'yellow'
    case 'cpm':
    case 'cpc':
    case 'cpa':
    case 'spend':
      return rule.comparison === 'greater_than' ? 'red' : 'blue'
    default:
      return 'gray'
  }
}

const getComparisonText = (comparison: string): string => {
  const map = {
    greater_than: '>',
    less_than: '<',
    equals: '=',
    percentage_change: '±'
  }
  return map[comparison] || comparison
}

const formatThreshold = (rule: AlertRule): string => {
  const value = rule.threshold
  const metric = rule.condition.metric
  
  if (['ctr', 'roas'].includes(metric)) {
    return `${value}%`
  } else if (['cpm', 'cpc', 'cpa', 'spend'].includes(metric)) {
    return `$${value}`
  } else if (rule.comparison === 'percentage_change') {
    return `${value}%`
  }
  
  return value.toString()
}

const formatRelativeTime = (date: Date): string => {
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffHours / 24)
  
  if (diffDays > 0) {
    return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`
  } else if (diffHours > 0) {
    return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`
  } else {
    const diffMins = Math.floor(diffMs / (1000 * 60))
    return `${Math.max(1, diffMins)} minute${diffMins !== 1 ? 's' : ''} ago`
  }
}

const getAlertIcon = (alertType: string): string => {
  const icons = {
    performance_drop: 'mdi:trending-down',
    budget_exceeded: 'mdi:wallet',
    anomaly_detected: 'mdi:alert',
    target_achieved: 'mdi:trophy'
  }
  return icons[alertType] || 'mdi:information'
}

// Initialize with mock data
onMounted(() => {
  // Mock alert rules
  alertRules.value = [
    {
      id: '1',
      userId: 'current-user-id',
      campaignId: 'campaign-1',
      name: 'CTR Drop Alert',
      description: 'Alert when CTR drops below 2% for Holiday Sale campaign',
      condition: { metric: 'ctr' },
      threshold: 2,
      comparison: 'less_than',
      timeframe: 30,
      isActive: true,
      notificationChannels: ['email', 'push'],
      lastTriggered: new Date(Date.now() - 2 * 60 * 60 * 1000),
      createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    },
    {
      id: '2',
      userId: 'current-user-id',
      name: 'High CPA Alert',
      description: 'Alert when Cost Per Acquisition exceeds $50',
      condition: { metric: 'cpa' },
      threshold: 50,
      comparison: 'greater_than',
      timeframe: 60,
      isActive: true,
      notificationChannels: ['email', 'slack'],
      createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
    }
  ]
})
</script>

<style scoped>
.alert-manager {
  @apply p-6 bg-gray-50 dark:bg-gray-900 min-h-screen;
}

.alert-rule-card {
  @apply transition-all duration-200 hover:shadow-md;
}

.triggered-alert {
  @apply transition-all duration-200 hover:shadow-sm;
}
</style>