<template>
  <div class="mini-chart-container">
    <canvas ref="chartRef" class="w-full h-full"></canvas>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import Chart from 'chart.js/auto'

interface ChartDataPoint {
  x: Date | number
  y: number
}

interface Props {
  data: ChartDataPoint[]
  color?: string
  type?: 'line' | 'area' | 'bar'
  showPoints?: boolean
  animate?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  color: '#3B82F6',
  type: 'line',
  showPoints: false,
  animate: false
})

const chartRef = ref<HTMLCanvasElement | null>(null)
let chartInstance: Chart | null = null

const initChart = () => {
  if (!chartRef.value) return

  const ctx = chartRef.value.getContext('2d')
  if (!ctx) return

  // Destroy existing chart if it exists
  if (chartInstance) {
    chartInstance.destroy()
  }

  const gradient = ctx.createLinearGradient(0, 0, 0, 48)
  gradient.addColorStop(0, props.color + '40') // 25% opacity
  gradient.addColorStop(1, props.color + '00') // 0% opacity

  chartInstance = new Chart(ctx, {
    type: props.type === 'bar' ? 'bar' : 'line',
    data: {
      labels: props.data.map(point => point.x),
      datasets: [{
        data: props.data.map(point => point.y),
        borderColor: props.color,
        backgroundColor: props.type === 'area' ? gradient : props.color,
        borderWidth: 2,
        fill: props.type === 'area',
        tension: 0.4,
        pointRadius: props.showPoints ? 2 : 0,
        pointHoverRadius: props.showPoints ? 4 : 0,
        pointBackgroundColor: props.color,
        pointBorderColor: '#ffffff',
        pointBorderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      animation: props.animate ? {
        duration: 300,
        easing: 'easeOutQuart'
      } : false,
      interaction: {
        intersect: false,
        mode: 'index'
      },
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          enabled: false
        }
      },
      scales: {
        x: {
          display: false,
          grid: {
            display: false
          }
        },
        y: {
          display: false,
          grid: {
            display: false
          },
          beginAtZero: true,
          // Add some padding to prevent clipping
          grace: '5%'
        }
      },
      elements: {
        point: {
          radius: props.showPoints ? 2 : 0,
          hoverRadius: props.showPoints ? 4 : 0
        },
        line: {
          borderJoinStyle: 'round',
          borderCapStyle: 'round'
        }
      },
      // Optimize for performance
      normalized: true,
      parsing: false,
      spanGaps: true
    }
  })
}

const updateChart = () => {
  if (!chartInstance) return

  const ctx = chartRef.value?.getContext('2d')
  if (!ctx) return

  // Update gradient color if needed
  const gradient = ctx.createLinearGradient(0, 0, 0, 48)
  gradient.addColorStop(0, props.color + '40')
  gradient.addColorStop(1, props.color + '00')

  chartInstance.data.labels = props.data.map(point => point.x)
  chartInstance.data.datasets[0].data = props.data.map(point => point.y)
  chartInstance.data.datasets[0].borderColor = props.color
  chartInstance.data.datasets[0].backgroundColor = props.type === 'area' ? gradient : props.color
  chartInstance.data.datasets[0].pointBackgroundColor = props.color

  chartInstance.update('none') // No animation for performance
}

// Watch for data changes
watch(() => props.data, () => {
  updateChart()
}, { deep: true })

// Watch for color changes
watch(() => props.color, () => {
  updateChart()
})

// Watch for type changes
watch(() => props.type, () => {
  initChart()
})

onMounted(() => {
  // Small delay to ensure canvas is ready
  setTimeout(() => {
    initChart()
  }, 100)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.destroy()
    chartInstance = null
  }
})
</script>

<style scoped>
.mini-chart-container {
  @apply w-full h-full relative;
}

canvas {
  @apply block;
}
</style>