<template>
  <div class="firebase-test p-4 border rounded-lg bg-white shadow-sm">
    <h2 class="text-xl font-bold mb-4">Firebase Initialization Test</h2>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <!-- Firebase Status -->
      <div class="border rounded p-4">
        <h3 class="font-semibold mb-2">Firebase Status</h3>
        <div class="space-y-2">
          <div class="flex items-center">
            <span class="w-32">Firebase App:</span>
            <span :class="firebaseApp ? 'text-green-600' : 'text-red-600'">
              {{ firebaseApp ? 'Initialized ✓' : 'Not Initialized ✗' }}
            </span>
          </div>
          <div class="flex items-center">
            <span class="w-32">Firestore:</span>
            <span :class="firestore ? 'text-green-600' : 'text-red-600'">
              {{ firestore ? 'Initialized ✓' : 'Not Initialized ✗' }}
            </span>
          </div>
          <div class="flex items-center">
            <span class="w-32">Auth:</span>
            <span :class="auth ? 'text-green-600' : 'text-red-600'">
              {{ auth ? 'Initialized ✓' : 'Not Initialized ✗' }}
            </span>
          </div>
          <div class="flex items-center">
            <span class="w-32">Storage:</span>
            <span :class="storage ? 'text-green-600' : 'text-red-600'">
              {{ storage ? 'Initialized ✓' : 'Not Initialized ✗' }}
            </span>
          </div>
          <div class="flex items-center">
            <span class="w-32">Messaging:</span>
            <span :class="messaging ? 'text-green-600' : 'text-red-600'">
              {{ messaging ? 'Initialized ✓' : 'Not Initialized ✗' }}
            </span>
          </div>
          <div class="flex items-center">
            <span class="w-32">VertexAI:</span>
            <span :class="vertexAI ? 'text-green-600' : 'text-red-600'">
              {{ vertexAI ? 'Initialized ✓' : 'Not Initialized ✗' }}
            </span>
          </div>
        </div>
      </div>

      <!-- Firestore Test -->
      <div class="border rounded p-4">
        <h3 class="font-semibold mb-2">Firestore Test</h3>
        <div class="space-y-4">
          <div>
            <button
              @click="testFirestoreRead"
              class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 mr-2"
              :disabled="!firestore"
            >
              Test Read
            </button>
            <button
              @click="testFirestoreWrite"
              class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              :disabled="!firestore"
            >
              Test Write
            </button>
          </div>
          <div v-if="firestoreResult" class="mt-2">
            <h4 class="font-medium">Result:</h4>
            <pre class="bg-gray-100 p-2 rounded text-sm overflow-auto max-h-40">{{ firestoreResult }}</pre>
          </div>
          <div v-if="firestoreError" class="mt-2 text-red-600">
            <h4 class="font-medium">Error:</h4>
            <pre class="bg-red-50 p-2 rounded text-sm overflow-auto max-h-40">{{ firestoreError }}</pre>
          </div>
        </div>
      </div>

      <!-- Auth Test -->
      <div class="border rounded p-4">
        <h3 class="font-semibold mb-2">Auth Test</h3>
        <div class="space-y-2">
          <div class="flex items-center">
            <span class="w-32">Auth State:</span>
            <span :class="authState ? 'text-green-600' : 'text-gray-600'">
              {{ authState ? 'Signed In' : 'Signed Out' }}
            </span>
          </div>
          <div v-if="authState" class="mt-2">
            <h4 class="font-medium">User Info:</h4>
            <pre class="bg-gray-100 p-2 rounded text-sm overflow-auto max-h-40">{{ JSON.stringify(authState, null, 2) }}</pre>
          </div>
        </div>
      </div>

      <!-- Server API Test -->
      <div class="border rounded p-4">
        <h3 class="font-semibold mb-2">Server API Test</h3>
        <div class="space-y-4">
          <div>
            <button
              @click="testServerApi"
              class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Test Server API
            </button>
          </div>
          <div v-if="serverResult" class="mt-2">
            <h4 class="font-medium">Result:</h4>
            <pre class="bg-gray-100 p-2 rounded text-sm overflow-auto max-h-40">{{ serverResult }}</pre>
          </div>
          <div v-if="serverError" class="mt-2 text-red-600">
            <h4 class="font-medium">Error:</h4>
            <pre class="bg-red-50 p-2 rounded text-sm overflow-auto max-h-40">{{ serverError }}</pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { collection, getDocs, addDoc, Timestamp, query, limit } from 'firebase/firestore'

// Use the new Firebase initialization
const {
  firebaseApp,
  firestore,
  auth,
  storage,
  messaging,
  vertexAI,
  onAuthStateChanged
} = useFirebase()

// State variables
const authState = ref(null)
const firestoreResult = ref('')
const firestoreError = ref('')
const serverResult = ref('')
const serverError = ref('')

// Auth state listener
let unsubscribe: any = null

onMounted(() => {
  // Set up auth state listener
  if (auth) {
    unsubscribe = onAuthStateChanged(auth, (user) => {
      authState.value = user
    })
  }
})

onUnmounted(() => {
  // Clean up auth state listener
  if (unsubscribe) {
    unsubscribe()
  }
})

// Test Firestore read
const testFirestoreRead = async () => {
  if (!firestore) {
    firestoreError.value = 'Firestore not initialized'
    return
  }

  try {
    firestoreResult.value = 'Reading from Firestore...'
    firestoreError.value = ''

    // Try to read from a collection that should be accessible
    // First try businesscards collection which allows read for authenticated users
    let q = query(collection(firestore, 'businesscards'), limit(5))
    let querySnapshot = await getDocs(q)

    // If no documents found, try uploads collection as fallback
    if (querySnapshot.empty) {
      q = query(collection(firestore, 'uploads'), limit(5))
      querySnapshot = await getDocs(q)
    }

    // If still no documents, create a test document in firebase_test collection
    if (querySnapshot.empty) {
      // Create a test document first
      const testDocRef = await addDoc(collection(firestore, 'firebase_test'), {
        message: 'Test document for read test',
        timestamp: Timestamp.now(),
        testId: `read-test-${Date.now()}`
      })

      // Then read it back
      q = query(collection(firestore, 'firebase_test'), limit(5))
      querySnapshot = await getDocs(q)
    }

    const results = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }))

    firestoreResult.value = JSON.stringify(results, null, 2)
  } catch (error: any) {
    console.error('Error reading from Firestore:', error)
    firestoreError.value = error.message || 'Unknown error'
    firestoreResult.value = ''
  }
}

// Test Firestore write
const testFirestoreWrite = async () => {
  if (!firestore) {
    firestoreError.value = 'Firestore not initialized'
    return
  }

  try {
    firestoreResult.value = 'Writing to Firestore...'
    firestoreError.value = ''

    const docRef = await addDoc(collection(firestore, 'firebase_test'), {
      message: 'Test write from Firebase migration test',
      timestamp: Timestamp.now(),
      testId: `test-${Date.now()}`
    })

    firestoreResult.value = `Document written with ID: ${docRef.id}`
  } catch (error: any) {
    console.error('Error writing to Firestore:', error)
    firestoreError.value = error.message || 'Unknown error'
    firestoreResult.value = ''
  }
}

// Test server API
const testServerApi = async () => {
  try {
    serverResult.value = 'Calling server API...'
    serverError.value = ''

    const response = await fetch('/api/firebase-test')
    const data = await response.json()

    serverResult.value = JSON.stringify(data, null, 2)
  } catch (error: any) {
    console.error('Error calling server API:', error)
    serverError.value = error.message || 'Unknown error'
    serverResult.value = ''
  }
}
</script>
