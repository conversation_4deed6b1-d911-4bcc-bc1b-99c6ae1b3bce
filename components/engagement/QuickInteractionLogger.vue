<template>
  <div class="quick-logger">
    <!-- Mobile-optimized header -->
    <div class="logger-header">
      <h3 class="logger-title">Log Interaction</h3>
      <button @click="$emit('close')" class="close-button">
        <Icon name="mdi:close" class="w-6 h-6" />
      </button>
    </div>

    <!-- Quick Action Buttons -->
    <div class="quick-actions">
      <button
        v-for="quickAction in quickActions"
        :key="quickAction.type"
        @click="selectQuickAction(quickAction)"
        class="quick-action-btn"
        :class="{ active: selectedAction === quickAction.type }"
      >
        <Icon :name="quickAction.icon" class="action-icon" />
        <span class="action-label">{{ quickAction.label }}</span>
        <span class="action-duration">{{ quickAction.duration }}min</span>
      </button>
    </div>

    <!-- Entity Selection -->
    <div class="form-section">
      <label class="form-label">Who?</label>
      
      <!-- Recent Contacts Quick Select -->
      <div v-if="recentContacts.length > 0 && !showEntitySearch" class="recent-contacts">
        <button
          v-for="contact in recentContacts"
          :key="contact.id"
          @click="selectRecentContact(contact)"
          class="recent-contact-btn"
          :class="{ selected: formData.entityId === contact.id }"
        >
          <div class="contact-avatar">
            {{ getInitials(contact.name) }}
          </div>
          <span class="contact-name">{{ contact.name }}</span>
        </button>
        <button @click="showEntitySearch = true" class="search-more-btn">
          <Icon name="mdi:magnify" class="w-5 h-5" />
          <span>Search more</span>
        </button>
      </div>

      <!-- Entity Search -->
      <div v-else class="entity-search">
        <div class="search-input-wrapper">
          <Icon name="mdi:magnify" class="search-icon" />
          <input
            ref="searchInput"
            v-model="searchQuery"
            @input="performSearch"
            type="text"
            placeholder="Search contacts or companies..."
            class="search-input"
          />
          <button 
            v-if="searchQuery" 
            @click="clearSearch"
            class="clear-search-btn"
          >
            <Icon name="mdi:close-circle" class="w-5 h-5" />
          </button>
        </div>

        <!-- Search Results -->
        <div v-if="searchResults.length > 0" class="search-results">
          <button
            v-for="result in searchResults"
            :key="result.id"
            @click="selectSearchResult(result)"
            class="search-result-item"
          >
            <Icon 
              :name="result.type === 'contact' ? 'mdi:account' : 'mdi:domain'"
              class="result-icon"
            />
            <div class="result-info">
              <p class="result-name">{{ result.name }}</p>
              <p class="result-type">{{ result.type }}</p>
            </div>
          </button>
        </div>
      </div>

      <!-- Selected Entity Display -->
      <div v-if="selectedEntity" class="selected-entity">
        <Icon 
          :name="selectedEntity.type === 'contact' ? 'mdi:account' : 'mdi:domain'"
          class="entity-type-icon"
        />
        <span class="entity-name">{{ selectedEntity.name }}</span>
        <button @click="clearSelection" class="remove-btn">
          <Icon name="mdi:close" class="w-4 h-4" />
        </button>
      </div>
    </div>

    <!-- Details Section -->
    <div class="form-section">
      <label class="form-label">Details</label>
      
      <!-- Outcome Pills -->
      <div class="outcome-pills">
        <button
          v-for="outcome in outcomes"
          :key="outcome.value"
          @click="formData.outcome = outcome.value"
          class="outcome-pill"
          :class="[
            `outcome-${outcome.value}`,
            { active: formData.outcome === outcome.value }
          ]"
        >
          <Icon :name="outcome.icon" class="w-4 h-4" />
          <span>{{ outcome.label }}</span>
        </button>
      </div>

      <!-- Quick Tags -->
      <div class="quick-tags">
        <button
          v-for="tag in quickTags"
          :key="tag"
          @click="toggleTag(tag)"
          class="tag-btn"
          :class="{ active: formData.tags.includes(tag) }"
        >
          {{ tag }}
        </button>
        <button @click="showCustomTag = true" class="tag-btn add-tag">
          <Icon name="mdi:plus" class="w-4 h-4" />
        </button>
      </div>

      <!-- Voice Note Option -->
      <div class="voice-note-section">
        <button
          v-if="!isRecording && !audioBlob"
          @click="startRecording"
          class="voice-btn"
        >
          <Icon name="mdi:microphone" class="w-5 h-5" />
          <span>Add voice note</span>
        </button>

        <div v-else-if="isRecording" class="recording-indicator">
          <div class="recording-dot"></div>
          <span>Recording... {{ recordingDuration }}s</span>
          <button @click="stopRecording" class="stop-btn">
            <Icon name="mdi:stop" class="w-5 h-5" />
          </button>
        </div>

        <div v-else-if="audioBlob" class="audio-preview">
          <audio ref="audioPlayer" controls class="audio-controls"></audio>
          <button @click="removeAudio" class="remove-audio-btn">
            <Icon name="mdi:delete" class="w-5 h-5" />
          </button>
        </div>
      </div>

      <!-- Text Notes -->
      <textarea
        v-model="formData.notes"
        placeholder="Add notes... (optional)"
        class="notes-textarea"
        rows="3"
      ></textarea>

      <!-- Follow-up Toggle -->
      <div class="follow-up-section">
        <label class="toggle-label">
          <input
            type="checkbox"
            v-model="formData.followUpRequired"
            class="toggle-input"
          />
          <span class="toggle-switch"></span>
          <span class="toggle-text">Follow-up needed</span>
        </label>

        <div v-if="formData.followUpRequired" class="follow-up-date">
          <input
            type="datetime-local"
            v-model="formData.followUpDate"
            class="date-input"
            :min="minFollowUpDate"
          />
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
      <button @click="$emit('close')" class="btn-cancel">
        Cancel
      </button>
      <button 
        @click="submitInteraction"
        :disabled="!canSubmit"
        class="btn-submit"
      >
        <Icon name="mdi:check" class="w-5 h-5" />
        <span>Log Interaction</span>
      </button>
    </div>

    <!-- Custom Tag Modal -->
    <Teleport to="body">
      <div v-if="showCustomTag" class="modal-overlay" @click="showCustomTag = false">
        <div class="modal-content" @click.stop>
          <h4 class="modal-title">Add Custom Tag</h4>
          <input
            v-model="customTagInput"
            @keyup.enter="addCustomTag"
            type="text"
            placeholder="Enter tag name..."
            class="tag-input"
            autofocus
          />
          <div class="modal-actions">
            <button @click="showCustomTag = false" class="btn-secondary">
              Cancel
            </button>
            <button @click="addCustomTag" class="btn-primary">
              Add Tag
            </button>
          </div>
        </div>
      </div>
    </Teleport>

    <!-- Success Toast -->
    <Transition name="toast">
      <div v-if="showSuccess" class="success-toast">
        <Icon name="mdi:check-circle" class="w-5 h-5 text-green-600" />
        <span>Interaction logged successfully!</span>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useEngagementTracking } from '~/composables/useEngagementTracking'
import { useContacts } from '~/composables/useContacts'
import { useBusinessEntities } from '~/composables/useBusinessEntities'
import { debounce } from 'lodash-es'
import type { EngagementType, EngagementOutcome } from '~/composables/useEngagementTracking'

// Props & Emits
const props = defineProps<{
  preselectedEntity?: {
    id: string
    type: 'contact' | 'company'
    name: string
  }
}>()

const emit = defineEmits<{
  close: []
  success: [eventId: string]
}>()

// Composables
const { createEngagementEvent } = useEngagementTracking()
const { contacts, searchContacts } = useContacts()
const { businessEntities, searchBusinessEntities } = useBusinessEntities()

// State
const selectedAction = ref<EngagementType>('call')
const formData = ref({
  type: 'call' as EngagementType,
  entityId: '',
  entityType: 'contact' as 'contact' | 'company',
  title: '',
  notes: '',
  duration: 15,
  outcome: null as EngagementOutcome | null,
  tags: [] as string[],
  followUpRequired: false,
  followUpDate: ''
})

const showEntitySearch = ref(false)
const searchQuery = ref('')
const searchResults = ref<Array<{
  id: string
  name: string
  type: 'contact' | 'company'
}>>([])

const selectedEntity = ref<typeof props.preselectedEntity | null>(null)
const showCustomTag = ref(false)
const customTagInput = ref('')
const showSuccess = ref(false)

// Voice Recording State
const isRecording = ref(false)
const audioBlob = ref<Blob | null>(null)
const recordingDuration = ref(0)
const mediaRecorder = ref<MediaRecorder | null>(null)
const recordingTimer = ref<number | null>(null)
const audioPlayer = ref<HTMLAudioElement>()
const searchInput = ref<HTMLInputElement>()

// Quick Actions
const quickActions = [
  { type: 'call', label: 'Call', icon: 'mdi:phone', duration: 15 },
  { type: 'email', label: 'Email', icon: 'mdi:email', duration: 5 },
  { type: 'meeting', label: 'Meeting', icon: 'mdi:calendar', duration: 30 },
  { type: 'social', label: 'Message', icon: 'mdi:message', duration: 5 },
  { type: 'note', label: 'Note', icon: 'mdi:note', duration: 2 }
] as const

// Outcomes
const outcomes = [
  { value: 'positive', label: 'Positive', icon: 'mdi:emoticon-happy' },
  { value: 'neutral', label: 'Neutral', icon: 'mdi:emoticon-neutral' },
  { value: 'negative', label: 'Negative', icon: 'mdi:emoticon-sad' }
] as const

// Quick Tags
const quickTags = [
  'Follow-up needed',
  'Decision maker',
  'Budget discussed',
  'Demo requested',
  'Proposal sent',
  'Next steps defined'
]

// Computed
const recentContacts = computed(() => {
  // Get last 5 interacted contacts
  return contacts.value
    .filter(c => c.lastInteractionDate)
    .sort((a, b) => {
      const dateA = a.lastInteractionDate?.getTime() || 0
      const dateB = b.lastInteractionDate?.getTime() || 0
      return dateB - dateA
    })
    .slice(0, 5)
    .map(c => ({
      id: c.id,
      name: `${c.firstName} ${c.lastName}`,
      type: 'contact' as const
    }))
})

const canSubmit = computed(() => {
  return formData.value.entityId && formData.value.type
})

const minFollowUpDate = computed(() => {
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  return tomorrow.toISOString().slice(0, 16)
})

// Methods
function selectQuickAction(action: typeof quickActions[number]) {
  selectedAction.value = action.type
  formData.value.type = action.type
  formData.value.duration = action.duration
  formData.value.title = `Quick ${action.label}`
}

function selectRecentContact(contact: typeof recentContacts.value[number]) {
  selectedEntity.value = contact
  formData.value.entityId = contact.id
  formData.value.entityType = 'contact'
}

const performSearch = debounce(async () => {
  if (!searchQuery.value || searchQuery.value.length < 2) {
    searchResults.value = []
    return
  }

  const [contactResults, companyResults] = await Promise.all([
    searchContacts(searchQuery.value),
    searchBusinessEntities(searchQuery.value)
  ])

  searchResults.value = [
    ...contactResults.slice(0, 3).map(c => ({
      id: c.id,
      name: `${c.firstName} ${c.lastName}`,
      type: 'contact' as const
    })),
    ...companyResults.slice(0, 3).map(c => ({
      id: c.id,
      name: c.name,
      type: 'company' as const
    }))
  ]
}, 300)

function selectSearchResult(result: typeof searchResults.value[number]) {
  selectedEntity.value = result
  formData.value.entityId = result.id
  formData.value.entityType = result.type
  searchQuery.value = ''
  searchResults.value = []
  showEntitySearch.value = false
}

function clearSearch() {
  searchQuery.value = ''
  searchResults.value = []
}

function clearSelection() {
  selectedEntity.value = null
  formData.value.entityId = ''
  showEntitySearch.value = false
}

function toggleTag(tag: string) {
  const index = formData.value.tags.indexOf(tag)
  if (index > -1) {
    formData.value.tags.splice(index, 1)
  } else {
    formData.value.tags.push(tag)
  }
}

function addCustomTag() {
  if (customTagInput.value && !formData.value.tags.includes(customTagInput.value)) {
    formData.value.tags.push(customTagInput.value)
  }
  customTagInput.value = ''
  showCustomTag.value = false
}

function getInitials(name: string): string {
  return name
    .split(' ')
    .map(part => part[0])
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

// Voice Recording Methods
async function startRecording() {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
    mediaRecorder.value = new MediaRecorder(stream)
    const chunks: Blob[] = []

    mediaRecorder.value.ondataavailable = (e) => {
      chunks.push(e.data)
    }

    mediaRecorder.value.onstop = () => {
      audioBlob.value = new Blob(chunks, { type: 'audio/webm' })
      if (audioPlayer.value && audioBlob.value) {
        audioPlayer.value.src = URL.createObjectURL(audioBlob.value)
      }
      stream.getTracks().forEach(track => track.stop())
    }

    mediaRecorder.value.start()
    isRecording.value = true
    recordingDuration.value = 0

    recordingTimer.value = window.setInterval(() => {
      recordingDuration.value++
    }, 1000)
  } catch (error) {
    console.error('Failed to start recording:', error)
  }
}

function stopRecording() {
  if (mediaRecorder.value && isRecording.value) {
    mediaRecorder.value.stop()
    isRecording.value = false
    
    if (recordingTimer.value) {
      clearInterval(recordingTimer.value)
      recordingTimer.value = null
    }
  }
}

function removeAudio() {
  audioBlob.value = null
  if (audioPlayer.value) {
    audioPlayer.value.src = ''
  }
}

async function submitInteraction() {
  if (!canSubmit.value) return

  try {
    const eventData = {
      type: formData.value.type,
      [formData.value.entityType === 'contact' ? 'contactId' : 'companyId']: formData.value.entityId,
      title: formData.value.title || `Quick ${formData.value.type}`,
      description: formData.value.notes,
      duration: formData.value.duration,
      outcome: formData.value.outcome,
      tags: formData.value.tags,
      followUpRequired: formData.value.followUpRequired,
      followUpDate: formData.value.followUpDate ? new Date(formData.value.followUpDate) : undefined,
      status: 'completed' as const,
      completedAt: new Date(),
      metadata: {
        source: 'manual' as const,
        quickLog: true
      }
    }

    // Handle audio attachment if present
    if (audioBlob.value) {
      // In a real app, upload audio to storage and attach URL
      console.log('Audio attachment would be uploaded here')
    }

    const eventId = await createEngagementEvent(eventData)
    
    showSuccess.value = true
    setTimeout(() => {
      showSuccess.value = false
      emit('success', eventId)
      emit('close')
    }, 1500)
  } catch (error) {
    console.error('Failed to log interaction:', error)
  }
}

// Initialize with preselected entity
onMounted(() => {
  if (props.preselectedEntity) {
    selectedEntity.value = props.preselectedEntity
    formData.value.entityId = props.preselectedEntity.id
    formData.value.entityType = props.preselectedEntity.type
  }
  
  // Focus search input if shown
  if (showEntitySearch.value && searchInput.value) {
    searchInput.value.focus()
  }
})

// Cleanup
onUnmounted(() => {
  if (isRecording.value) {
    stopRecording()
  }
  if (audioPlayer.value?.src) {
    URL.revokeObjectURL(audioPlayer.value.src)
  }
})

// Watch for search visibility changes
watch(showEntitySearch, (show) => {
  if (show && searchInput.value) {
    setTimeout(() => searchInput.value?.focus(), 100)
  }
})
</script>

<style scoped>
.quick-logger {
  @apply bg-white rounded-t-2xl md:rounded-lg shadow-xl max-w-lg mx-auto;
}

.logger-header {
  @apply flex items-center justify-between p-4 border-b;
}

.logger-title {
  @apply text-lg font-semibold text-gray-900;
}

.close-button {
  @apply p-1 text-gray-400 hover:text-gray-600 transition-colors;
}

.quick-actions {
  @apply grid grid-cols-3 gap-2 p-4;
}

.quick-action-btn {
  @apply flex flex-col items-center p-3 rounded-lg border-2 transition-all;
}

.quick-action-btn.active {
  @apply border-blue-500 bg-blue-50;
}

.quick-action-btn:not(.active) {
  @apply border-gray-200 hover:border-gray-300;
}

.action-icon {
  @apply w-6 h-6 mb-1;
}

.action-label {
  @apply text-sm font-medium;
}

.action-duration {
  @apply text-xs text-gray-500;
}

.form-section {
  @apply px-4 py-3 border-b;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.recent-contacts {
  @apply flex flex-wrap gap-2;
}

.recent-contact-btn {
  @apply flex items-center space-x-2 px-3 py-2 rounded-full border transition-all;
}

.recent-contact-btn.selected {
  @apply border-blue-500 bg-blue-50;
}

.recent-contact-btn:not(.selected) {
  @apply border-gray-200 hover:border-gray-300;
}

.contact-avatar {
  @apply w-8 h-8 rounded-full bg-gray-300 text-white text-xs font-medium flex items-center justify-center;
}

.contact-name {
  @apply text-sm;
}

.search-more-btn {
  @apply flex items-center space-x-1 px-3 py-2 text-blue-600 hover:bg-blue-50 rounded-full transition-colors;
}

.entity-search {
  @apply space-y-2;
}

.search-input-wrapper {
  @apply relative flex items-center;
}

.search-icon {
  @apply absolute left-3 w-5 h-5 text-gray-400;
}

.search-input {
  @apply w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500;
}

.clear-search-btn {
  @apply absolute right-2 p-1 text-gray-400 hover:text-gray-600;
}

.search-results {
  @apply bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto;
}

.search-result-item {
  @apply flex items-center space-x-3 px-3 py-2 hover:bg-gray-50 w-full text-left;
}

.result-icon {
  @apply w-5 h-5 text-gray-400;
}

.result-info {
  @apply flex-1;
}

.result-name {
  @apply text-sm font-medium text-gray-900;
}

.result-type {
  @apply text-xs text-gray-500;
}

.selected-entity {
  @apply flex items-center space-x-2 bg-blue-50 border border-blue-200 rounded-lg px-3 py-2 mt-2;
}

.entity-type-icon {
  @apply w-5 h-5 text-blue-600;
}

.entity-name {
  @apply flex-1 text-sm font-medium text-blue-900;
}

.remove-btn {
  @apply p-1 text-blue-600 hover:text-blue-800;
}

.outcome-pills {
  @apply flex space-x-2 mb-3;
}

.outcome-pill {
  @apply flex items-center space-x-1 px-3 py-1.5 rounded-full border-2 transition-all;
}

.outcome-pill.active {
  @apply border-current;
}

.outcome-pill.outcome-positive {
  @apply text-green-600 border-gray-200;
}

.outcome-pill.outcome-positive.active {
  @apply bg-green-50 border-green-500;
}

.outcome-pill.outcome-neutral {
  @apply text-gray-600 border-gray-200;
}

.outcome-pill.outcome-neutral.active {
  @apply bg-gray-100 border-gray-500;
}

.outcome-pill.outcome-negative {
  @apply text-red-600 border-gray-200;
}

.outcome-pill.outcome-negative.active {
  @apply bg-red-50 border-red-500;
}

.quick-tags {
  @apply flex flex-wrap gap-2 mb-3;
}

.tag-btn {
  @apply px-3 py-1 text-sm rounded-full border transition-all;
}

.tag-btn.active {
  @apply border-blue-500 bg-blue-50 text-blue-700;
}

.tag-btn:not(.active) {
  @apply border-gray-200 text-gray-600 hover:border-gray-300;
}

.tag-btn.add-tag {
  @apply border-dashed border-gray-300 text-gray-500 hover:border-gray-400;
}

.voice-note-section {
  @apply mb-3;
}

.voice-btn {
  @apply flex items-center space-x-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors;
}

.recording-indicator {
  @apply flex items-center space-x-3 px-4 py-2 bg-red-50 text-red-600 rounded-lg;
}

.recording-dot {
  @apply w-3 h-3 bg-red-600 rounded-full animate-pulse;
}

.stop-btn {
  @apply ml-auto p-1 hover:bg-red-100 rounded;
}

.audio-preview {
  @apply flex items-center space-x-2;
}

.audio-controls {
  @apply flex-1;
}

.remove-audio-btn {
  @apply p-2 text-red-600 hover:bg-red-50 rounded;
}

.notes-textarea {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500;
}

.follow-up-section {
  @apply space-y-2 mt-3;
}

.toggle-label {
  @apply flex items-center space-x-3 cursor-pointer;
}

.toggle-input {
  @apply sr-only;
}

.toggle-switch {
  @apply relative inline-block w-11 h-6 bg-gray-200 rounded-full transition-colors;
}

.toggle-input:checked + .toggle-switch {
  @apply bg-blue-600;
}

.toggle-switch::after {
  @apply content-[''] absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full transition-transform;
}

.toggle-input:checked + .toggle-switch::after {
  @apply translate-x-5;
}

.toggle-text {
  @apply text-sm font-medium text-gray-700;
}

.follow-up-date {
  @apply mt-2;
}

.date-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500;
}

.action-buttons {
  @apply flex space-x-3 p-4;
}

.btn-cancel {
  @apply flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors;
}

.btn-submit {
  @apply flex-1 flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed;
}

/* Modal Styles */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4;
}

.modal-content {
  @apply bg-white rounded-lg p-4 max-w-sm w-full;
}

.modal-title {
  @apply text-lg font-semibold text-gray-900 mb-3;
}

.tag-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500;
}

.modal-actions {
  @apply flex space-x-3 mt-4;
}

.btn-secondary {
  @apply flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50;
}

.btn-primary {
  @apply flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700;
}

/* Success Toast */
.success-toast {
  @apply fixed bottom-4 left-1/2 transform -translate-x-1/2 flex items-center space-x-2 px-4 py-3 bg-white rounded-lg shadow-lg;
}

/* Transitions */
.toast-enter-active,
.toast-leave-active {
  @apply transition-all duration-300;
}

.toast-enter-from {
  @apply opacity-0 translate-y-4;
}

.toast-leave-to {
  @apply opacity-0 translate-y-4;
}
</style>