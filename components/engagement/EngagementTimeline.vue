<template>
  <div class="engagement-timeline">
    <!-- Timeline Header -->
    <div class="timeline-header">
      <h3 class="timeline-title">Engagement History</h3>
      <div class="timeline-controls">
        <button
          v-for="period in periods"
          :key="period.value"
          @click="selectedPeriod = period.value"
          class="period-btn"
          :class="{ active: selectedPeriod === period.value }"
        >
          {{ period.label }}
        </button>
      </div>
    </div>

    <!-- Timeline Content -->
    <div class="timeline-content">
      <!-- Date Groups -->
      <div v-for="group in groupedEvents" :key="group.date" class="date-group">
        <div class="date-header">
          <div class="date-line"></div>
          <div class="date-label">{{ formatDateHeader(group.date) }}</div>
          <div class="date-line"></div>
        </div>

        <!-- Events for this date -->
        <div class="events-list">
          <div
            v-for="event in group.events"
            :key="event.id"
            class="event-item"
            :class="`type-${event.type}`"
            @click="selectEvent(event)"
          >
            <!-- Event Time -->
            <div class="event-time">
              {{ formatTime(event.completedAt || event.scheduledAt) }}
            </div>

            <!-- Event Connector -->
            <div class="event-connector">
              <div class="connector-line"></div>
              <div class="event-icon">
                <Icon :name="getEventIcon(event.type)" class="w-4 h-4" />
              </div>
            </div>

            <!-- Event Content -->
            <div class="event-content">
              <div class="event-header">
                <h4 class="event-title">{{ event.title }}</h4>
                <div class="event-badges">
                  <span v-if="event.duration" class="duration-badge">
                    <Icon name="mdi:clock-outline" class="w-3 h-3" />
                    {{ event.duration }}min
                  </span>
                  <span v-if="event.outcome" class="outcome-badge" :class="`outcome-${event.outcome}`">
                    {{ event.outcome }}
                  </span>
                </div>
              </div>

              <div class="event-details">
                <div v-if="event.contactId || event.companyId" class="event-entity">
                  <Icon 
                    :name="event.contactId ? 'mdi:account' : 'mdi:domain'" 
                    class="w-4 h-4 text-gray-400"
                  />
                  <span>{{ getEntityName(event) }}</span>
                </div>

                <p v-if="event.description" class="event-description">
                  {{ truncateText(event.description, 100) }}
                </p>

                <div v-if="event.tags.length > 0" class="event-tags">
                  <span v-for="tag in event.tags.slice(0, 3)" :key="tag" class="event-tag">
                    {{ tag }}
                  </span>
                  <span v-if="event.tags.length > 3" class="more-tags">
                    +{{ event.tags.length - 3 }} more
                  </span>
                </div>

                <div v-if="event.participants.length > 1" class="event-participants">
                  <Icon name="mdi:account-multiple" class="w-4 h-4 text-gray-400" />
                  <span>{{ event.participants.length }} participants</span>
                </div>

                <div v-if="event.followUpRequired" class="follow-up-indicator">
                  <Icon name="mdi:bell" class="w-4 h-4 text-orange-500" />
                  <span>Follow-up required</span>
                  <span v-if="event.followUpDate">
                    by {{ formatDate(event.followUpDate) }}
                  </span>
                </div>
              </div>

              <!-- Sentiment Indicator -->
              <div 
                v-if="event.sentiment !== undefined" 
                class="sentiment-indicator"
                :style="{ '--sentiment': event.sentiment }"
              >
                <div class="sentiment-bar"></div>
                <span class="sentiment-label">
                  {{ getSentimentLabel(event.sentiment) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="groupedEvents.length === 0" class="empty-state">
        <Icon name="mdi:calendar-blank" class="w-12 h-12 text-gray-300 mb-2" />
        <p class="text-gray-500">No engagement events in this period</p>
      </div>

      <!-- Load More -->
      <div v-if="hasMore" class="load-more">
        <button @click="loadMore" class="load-more-btn">
          <Icon name="mdi:chevron-down" class="w-5 h-5" />
          <span>Load more events</span>
        </button>
      </div>
    </div>

    <!-- Event Detail Modal -->
    <Teleport to="body">
      <Transition name="modal">
        <div v-if="selectedEvent" class="modal-overlay" @click="selectedEvent = null">
          <div class="modal-content" @click.stop>
            <div class="modal-header">
              <div class="modal-icon" :class="`type-${selectedEvent.type}`">
                <Icon :name="getEventIcon(selectedEvent.type)" class="w-6 h-6" />
              </div>
              <div class="modal-title-section">
                <h3 class="modal-title">{{ selectedEvent.title }}</h3>
                <p class="modal-subtitle">
                  {{ formatFullDate(selectedEvent.completedAt || selectedEvent.scheduledAt) }}
                </p>
              </div>
              <button @click="selectedEvent = null" class="modal-close">
                <Icon name="mdi:close" class="w-6 h-6" />
              </button>
            </div>

            <div class="modal-body">
              <!-- Event Details -->
              <div class="detail-section">
                <h4 class="detail-title">Details</h4>
                <div class="detail-grid">
                  <div class="detail-item">
                    <span class="detail-label">Type:</span>
                    <span class="detail-value">{{ formatEventType(selectedEvent.type) }}</span>
                  </div>
                  <div v-if="selectedEvent.duration" class="detail-item">
                    <span class="detail-label">Duration:</span>
                    <span class="detail-value">{{ selectedEvent.duration }} minutes</span>
                  </div>
                  <div v-if="selectedEvent.outcome" class="detail-item">
                    <span class="detail-label">Outcome:</span>
                    <span class="detail-value outcome-badge" :class="`outcome-${selectedEvent.outcome}`">
                      {{ selectedEvent.outcome }}
                    </span>
                  </div>
                  <div v-if="selectedEvent.sentiment !== undefined" class="detail-item">
                    <span class="detail-label">Sentiment:</span>
                    <span class="detail-value">{{ getSentimentLabel(selectedEvent.sentiment) }}</span>
                  </div>
                </div>
              </div>

              <!-- Description -->
              <div v-if="selectedEvent.description" class="detail-section">
                <h4 class="detail-title">Notes</h4>
                <p class="event-notes">{{ selectedEvent.description }}</p>
              </div>

              <!-- Participants -->
              <div v-if="selectedEvent.participants.length > 0" class="detail-section">
                <h4 class="detail-title">Participants</h4>
                <div class="participants-list">
                  <div
                    v-for="participant in selectedEvent.participants"
                    :key="participant.id"
                    class="participant-item"
                  >
                    <div class="participant-avatar">
                      <Icon :name="getParticipantIcon(participant.type)" class="w-4 h-4" />
                    </div>
                    <div class="participant-info">
                      <p class="participant-name">{{ participant.name }}</p>
                      <p v-if="participant.role" class="participant-role">{{ participant.role }}</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Tags -->
              <div v-if="selectedEvent.tags.length > 0" class="detail-section">
                <h4 class="detail-title">Tags</h4>
                <div class="tags-list">
                  <span v-for="tag in selectedEvent.tags" :key="tag" class="detail-tag">
                    {{ tag }}
                  </span>
                </div>
              </div>

              <!-- Follow-up -->
              <div v-if="selectedEvent.followUpRequired" class="detail-section follow-up-section">
                <h4 class="detail-title">
                  <Icon name="mdi:bell" class="w-5 h-5 text-orange-500" />
                  Follow-up Required
                </h4>
                <p v-if="selectedEvent.followUpDate" class="follow-up-date">
                  Due by {{ formatFullDate(selectedEvent.followUpDate) }}
                </p>
              </div>
            </div>

            <div class="modal-footer">
              <button @click="editEvent(selectedEvent)" class="btn-secondary">
                <Icon name="mdi:pencil" class="w-5 h-5" />
                <span>Edit</span>
              </button>
              <button @click="deleteEvent(selectedEvent)" class="btn-danger">
                <Icon name="mdi:delete" class="w-5 h-5" />
                <span>Delete</span>
              </button>
            </div>
          </div>
        </div>
      </Transition>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useEngagementTracking } from '~/composables/useEngagementTracking'
import { useContacts } from '~/composables/useContacts'
import { useBusinessEntities } from '~/composables/useBusinessEntities'
import { format, formatRelative, isToday, isYesterday, isThisWeek, startOfDay } from 'date-fns'
import type { EngagementEvent } from '~/composables/useEngagementTracking'

// Props
const props = defineProps<{
  entityId?: string
  entityType?: 'contact' | 'company'
}>()

// Composables
const { engagementEvents } = useEngagementTracking()
const { contacts } = useContacts()
const { businessEntities } = useBusinessEntities()

// State
const selectedPeriod = ref<'week' | 'month' | 'quarter' | 'all'>('month')
const selectedEvent = ref<EngagementEvent | null>(null)
const displayLimit = ref(20)

// Period options
const periods = [
  { value: 'week', label: 'This Week' },
  { value: 'month', label: 'This Month' },
  { value: 'quarter', label: 'This Quarter' },
  { value: 'all', label: 'All Time' }
] as const

// Computed
const filteredEvents = computed(() => {
  let events = engagementEvents.value

  // Filter by entity if provided
  if (props.entityId) {
    if (props.entityType === 'contact') {
      events = events.filter(e => e.contactId === props.entityId)
    } else {
      events = events.filter(e => e.companyId === props.entityId)
    }
  }

  // Filter by period
  const now = new Date()
  const startDate = new Date()

  switch (selectedPeriod.value) {
    case 'week':
      startDate.setDate(now.getDate() - 7)
      break
    case 'month':
      startDate.setMonth(now.getMonth() - 1)
      break
    case 'quarter':
      startDate.setMonth(now.getMonth() - 3)
      break
    case 'all':
      startDate.setFullYear(2000) // Effectively all time
      break
  }

  events = events.filter(e => {
    const eventDate = e.completedAt || e.scheduledAt
    return eventDate && eventDate >= startDate
  })

  // Sort by date descending
  return events.sort((a, b) => {
    const dateA = a.completedAt || a.scheduledAt || new Date(0)
    const dateB = b.completedAt || b.scheduledAt || new Date(0)
    return dateB.getTime() - dateA.getTime()
  })
})

const displayedEvents = computed(() => 
  filteredEvents.value.slice(0, displayLimit.value)
)

const hasMore = computed(() => 
  filteredEvents.value.length > displayLimit.value
)

const groupedEvents = computed(() => {
  const groups: Array<{
    date: string
    events: EngagementEvent[]
  }> = []

  displayedEvents.value.forEach(event => {
    const eventDate = event.completedAt || event.scheduledAt
    if (!eventDate) return

    const dateKey = startOfDay(eventDate).toISOString()
    let group = groups.find(g => g.date === dateKey)
    
    if (!group) {
      group = { date: dateKey, events: [] }
      groups.push(group)
    }
    
    group.events.push(event)
  })

  return groups
})

// Methods
function loadMore() {
  displayLimit.value += 20
}

function selectEvent(event: EngagementEvent) {
  selectedEvent.value = event
}

function editEvent(event: EngagementEvent) {
  // Emit edit event or navigate to edit view
  console.log('Edit event:', event.id)
}

function deleteEvent(event: EngagementEvent) {
  // Confirm and delete
  if (confirm('Are you sure you want to delete this event?')) {
    console.log('Delete event:', event.id)
  }
}

function getEventIcon(type: string): string {
  const icons: Record<string, string> = {
    email: 'mdi:email',
    call: 'mdi:phone',
    meeting: 'mdi:calendar',
    social: 'mdi:message',
    note: 'mdi:note',
    task: 'mdi:checkbox-marked',
    custom: 'mdi:dots-horizontal'
  }
  return icons[type] || 'mdi:circle'
}

function getEntityName(event: EngagementEvent): string {
  if (event.contactId) {
    const contact = contacts.value.find(c => c.id === event.contactId)
    return contact ? `${contact.firstName} ${contact.lastName}` : 'Unknown Contact'
  } else if (event.companyId) {
    const company = businessEntities.value.find(b => b.id === event.companyId)
    return company?.name || 'Unknown Company'
  }
  return 'Unknown'
}

function getParticipantIcon(type: string): string {
  const icons: Record<string, string> = {
    contact: 'mdi:account',
    user: 'mdi:account-tie',
    external: 'mdi:account-outline'
  }
  return icons[type] || 'mdi:account'
}

function formatDateHeader(dateStr: string): string {
  const date = new Date(dateStr)
  
  if (isToday(date)) {
    return 'Today'
  } else if (isYesterday(date)) {
    return 'Yesterday'
  } else if (isThisWeek(date)) {
    return format(date, 'EEEE')
  } else {
    return format(date, 'MMM d, yyyy')
  }
}

function formatTime(date: Date | undefined): string {
  if (!date) return ''
  return format(date, 'h:mm a')
}

function formatDate(date: Date): string {
  return format(date, 'MMM d')
}

function formatFullDate(date: Date | undefined): string {
  if (!date) return ''
  return format(date, 'PPpp')
}

function formatEventType(type: string): string {
  return type.charAt(0).toUpperCase() + type.slice(1)
}

function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength) + '...'
}

function getSentimentLabel(sentiment: number): string {
  if (sentiment >= 0.5) return 'Very Positive'
  if (sentiment >= 0.2) return 'Positive'
  if (sentiment >= -0.2) return 'Neutral'
  if (sentiment >= -0.5) return 'Negative'
  return 'Very Negative'
}

// Reset display limit when period changes
watch(selectedPeriod, () => {
  displayLimit.value = 20
})
</script>

<style scoped>
.engagement-timeline {
  @apply bg-white rounded-lg shadow-sm border border-gray-200;
}

.timeline-header {
  @apply flex items-center justify-between p-6 border-b;
}

.timeline-title {
  @apply text-lg font-semibold text-gray-900;
}

.timeline-controls {
  @apply flex space-x-1 bg-gray-100 rounded-lg p-1;
}

.period-btn {
  @apply px-3 py-1.5 text-sm font-medium text-gray-600 rounded-md transition-colors;
}

.period-btn.active {
  @apply bg-white text-gray-900 shadow-sm;
}

.timeline-content {
  @apply p-6;
}

.date-group {
  @apply mb-8 last:mb-0;
}

.date-header {
  @apply flex items-center mb-4;
}

.date-line {
  @apply flex-1 h-px bg-gray-200;
}

.date-label {
  @apply px-4 text-sm font-medium text-gray-500 uppercase tracking-wider;
}

.events-list {
  @apply space-y-4;
}

.event-item {
  @apply flex items-start space-x-4 cursor-pointer hover:bg-gray-50 p-2 -mx-2 rounded-lg transition-colors;
}

.event-time {
  @apply text-sm text-gray-500 w-20 text-right flex-shrink-0 pt-1;
}

.event-connector {
  @apply relative flex flex-col items-center flex-shrink-0;
}

.connector-line {
  @apply absolute top-8 bottom-0 w-px bg-gray-200;
}

.event-item:last-child .connector-line {
  @apply hidden;
}

.event-icon {
  @apply w-8 h-8 rounded-full flex items-center justify-center text-white z-10 bg-white;
}

.event-item.type-email .event-icon {
  @apply bg-blue-500;
}

.event-item.type-call .event-icon {
  @apply bg-green-500;
}

.event-item.type-meeting .event-icon {
  @apply bg-purple-500;
}

.event-item.type-social .event-icon {
  @apply bg-pink-500;
}

.event-item.type-note .event-icon {
  @apply bg-gray-500;
}

.event-item.type-task .event-icon {
  @apply bg-yellow-500;
}

.event-content {
  @apply flex-1 pb-2;
}

.event-header {
  @apply flex items-start justify-between mb-1;
}

.event-title {
  @apply font-medium text-gray-900;
}

.event-badges {
  @apply flex items-center space-x-2;
}

.duration-badge {
  @apply text-xs text-gray-500 flex items-center space-x-1;
}

.outcome-badge {
  @apply text-xs px-2 py-0.5 rounded-full;
}

.outcome-badge.outcome-positive {
  @apply bg-green-100 text-green-700;
}

.outcome-badge.outcome-neutral {
  @apply bg-gray-100 text-gray-700;
}

.outcome-badge.outcome-negative {
  @apply bg-red-100 text-red-700;
}

.event-details {
  @apply space-y-2;
}

.event-entity {
  @apply flex items-center space-x-1 text-sm text-gray-600;
}

.event-description {
  @apply text-sm text-gray-600 line-clamp-2;
}

.event-tags {
  @apply flex flex-wrap gap-1;
}

.event-tag {
  @apply text-xs bg-gray-100 text-gray-600 px-2 py-0.5 rounded-full;
}

.more-tags {
  @apply text-xs text-gray-500;
}

.event-participants {
  @apply flex items-center space-x-1 text-sm text-gray-500;
}

.follow-up-indicator {
  @apply flex items-center space-x-1 text-sm text-orange-600;
}

.sentiment-indicator {
  @apply mt-2;
}

.sentiment-bar {
  @apply w-full h-1.5 bg-gray-200 rounded-full overflow-hidden relative;
}

.sentiment-bar::after {
  content: '';
  @apply absolute inset-y-0 left-0 bg-gradient-to-r from-red-500 via-yellow-500 to-green-500;
  width: calc((var(--sentiment) + 1) * 50%);
}

.sentiment-label {
  @apply text-xs text-gray-500 mt-1;
}

.empty-state {
  @apply text-center py-12;
}

.load-more {
  @apply text-center mt-6;
}

.load-more-btn {
  @apply inline-flex items-center space-x-2 px-4 py-2 text-sm text-gray-600 hover:text-gray-900 transition-colors;
}

/* Modal Styles */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4;
}

.modal-content {
  @apply bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col;
}

.modal-header {
  @apply flex items-center space-x-4 p-6 border-b;
}

.modal-icon {
  @apply w-12 h-12 rounded-full flex items-center justify-center text-white;
}

.modal-icon.type-email {
  @apply bg-blue-500;
}

.modal-icon.type-call {
  @apply bg-green-500;
}

.modal-icon.type-meeting {
  @apply bg-purple-500;
}

.modal-icon.type-social {
  @apply bg-pink-500;
}

.modal-icon.type-note {
  @apply bg-gray-500;
}

.modal-title-section {
  @apply flex-1;
}

.modal-title {
  @apply text-xl font-semibold text-gray-900;
}

.modal-subtitle {
  @apply text-sm text-gray-500;
}

.modal-close {
  @apply p-1 text-gray-400 hover:text-gray-600 transition-colors;
}

.modal-body {
  @apply p-6 space-y-6 overflow-y-auto;
}

.detail-section {
  @apply space-y-3;
}

.detail-title {
  @apply font-medium text-gray-900 flex items-center space-x-2;
}

.detail-grid {
  @apply grid grid-cols-2 gap-4;
}

.detail-item {
  @apply space-y-1;
}

.detail-label {
  @apply text-sm text-gray-500;
}

.detail-value {
  @apply text-sm font-medium text-gray-900;
}

.event-notes {
  @apply text-gray-700 whitespace-pre-wrap;
}

.participants-list {
  @apply space-y-2;
}

.participant-item {
  @apply flex items-center space-x-3;
}

.participant-avatar {
  @apply w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-gray-600;
}

.participant-info {
  @apply flex-1;
}

.participant-name {
  @apply text-sm font-medium text-gray-900;
}

.participant-role {
  @apply text-xs text-gray-500;
}

.tags-list {
  @apply flex flex-wrap gap-2;
}

.detail-tag {
  @apply px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full;
}

.follow-up-section {
  @apply bg-orange-50 p-4 rounded-lg;
}

.follow-up-date {
  @apply text-sm text-orange-700;
}

.modal-footer {
  @apply flex justify-end space-x-3 p-6 border-t;
}

.btn-secondary {
  @apply flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors;
}

.btn-danger {
  @apply flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors;
}

/* Transitions */
.modal-enter-active,
.modal-leave-active {
  @apply transition-opacity duration-300;
}

.modal-enter-from,
.modal-leave-to {
  @apply opacity-0;
}

.modal-enter-active .modal-content,
.modal-leave-active .modal-content {
  @apply transition-transform duration-300;
}

.modal-enter-from .modal-content {
  @apply transform scale-95;
}

.modal-leave-to .modal-content {
  @apply transform scale-95;
}
</style>