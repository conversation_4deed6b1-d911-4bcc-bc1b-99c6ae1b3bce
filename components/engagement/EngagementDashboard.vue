<template>
  <div class="engagement-dashboard">
    <!-- Header with Quick Actions -->
    <div class="dashboard-header">
      <h2 class="text-2xl font-bold text-gray-900">Engagement Analytics</h2>
      <div class="header-actions">
        <select v-model="selectedPeriod" @change="refreshAnalytics" class="period-select">
          <option value="day">Today</option>
          <option value="week">This Week</option>
          <option value="month">This Month</option>
          <option value="quarter">This Quarter</option>
          <option value="year">This Year</option>
        </select>
        <button @click="showQuickLog = true" class="btn-primary">
          <Icon name="mdi:plus" class="w-5 h-5 mr-2" />
          Quick Log
        </button>
      </div>
    </div>

    <!-- Key Metrics Cards -->
    <div class="metrics-grid">
      <div class="metric-card">
        <div class="metric-icon bg-blue-100">
          <Icon name="mdi:account-multiple" class="w-6 h-6 text-blue-600" />
        </div>
        <div class="metric-content">
          <p class="metric-label">Total Interactions</p>
          <p class="metric-value">{{ analytics?.metrics.totalInteractions || 0 }}</p>
          <p class="metric-change text-green-600">
            <Icon name="mdi:trending-up" class="w-4 h-4" />
            +12% from last period
          </p>
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-icon bg-green-100">
          <Icon name="mdi:chart-line" class="w-6 h-6 text-green-600" />
        </div>
        <div class="metric-content">
          <p class="metric-label">Avg Engagement Score</p>
          <p class="metric-value">{{ analytics?.metrics.averageEngagementScore || 0 }}</p>
          <div class="score-bar">
            <div 
              class="score-fill"
              :style="{ width: `${analytics?.metrics.averageEngagementScore || 0}%` }"
              :class="getScoreClass(analytics?.metrics.averageEngagementScore || 0)"
            />
          </div>
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-icon bg-purple-100">
          <Icon name="mdi:account-check" class="w-6 h-6 text-purple-600" />
        </div>
        <div class="metric-content">
          <p class="metric-label">Active Contacts</p>
          <p class="metric-value">{{ analytics?.metrics.uniqueContacts || 0 }}</p>
          <p class="metric-subtext">{{ analytics?.metrics.uniqueCompanies || 0 }} companies</p>
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-icon bg-orange-100">
          <Icon name="mdi:bell-alert" class="w-6 h-6 text-orange-600" />
        </div>
        <div class="metric-content">
          <p class="metric-label">Pending Actions</p>
          <p class="metric-value">{{ pendingRecommendations.length }}</p>
          <p class="metric-subtext">{{ highPriorityCount }} high priority</p>
        </div>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="dashboard-content">
      <!-- Left Column: Charts and Analytics -->
      <div class="content-left">
        <!-- Engagement Timeline Chart -->
        <div class="chart-container">
          <h3 class="chart-title">Engagement Timeline</h3>
          <canvas ref="timelineChart" height="300"></canvas>
        </div>

        <!-- Interaction Breakdown -->
        <div class="chart-container">
          <h3 class="chart-title">Interaction Types</h3>
          <canvas ref="breakdownChart" height="250"></canvas>
        </div>

        <!-- Top Engaged Entities -->
        <div class="top-entities-container">
          <h3 class="section-title">Top Engaged</h3>
          <div class="entity-list">
            <div 
              v-for="entity in topEntities" 
              :key="entity.entityId"
              class="entity-item"
              @click="viewEntityDetails(entity)"
            >
              <div class="entity-avatar">
                <Icon 
                  :name="entity.entityType === 'contact' ? 'mdi:account' : 'mdi:domain'" 
                  class="w-5 h-5"
                />
              </div>
              <div class="entity-info">
                <p class="entity-name">{{ entity.name }}</p>
                <p class="entity-meta">
                  Score: {{ entity.score }} • Last: {{ formatRelativeTime(entity.lastInteraction) }}
                </p>
              </div>
              <div class="entity-score">
                <div class="mini-score-bar">
                  <div 
                    class="mini-score-fill"
                    :style="{ width: `${entity.score}%` }"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Column: Recommendations and Actions -->
      <div class="content-right">
        <!-- ML Recommendations -->
        <div class="recommendations-container">
          <h3 class="section-title">
            AI Recommendations
            <span class="ml-badge">ML Powered</span>
          </h3>
          
          <div v-if="pendingRecommendations.length === 0" class="empty-state">
            <Icon name="mdi:robot-happy" class="w-12 h-12 text-gray-400 mb-2" />
            <p class="text-gray-500">No recommendations at this time</p>
          </div>

          <div v-else class="recommendation-list">
            <div 
              v-for="rec in pendingRecommendations.slice(0, 5)" 
              :key="rec.id"
              class="recommendation-card"
              :class="`priority-${rec.priority}`"
            >
              <div class="rec-header">
                <Icon :name="getRecommendationIcon(rec.type)" class="rec-icon" />
                <span class="rec-type">{{ formatRecommendationType(rec.type) }}</span>
                <span class="rec-confidence">{{ Math.round(rec.confidence * 100) }}% confident</span>
              </div>
              <p class="rec-action">{{ rec.action }}</p>
              <p class="rec-reason">{{ rec.reason }}</p>
              <div class="rec-actions">
                <button 
                  @click="acceptRecommendation(rec.id)"
                  class="btn-sm btn-accept"
                >
                  Accept
                </button>
                <button 
                  @click="dismissRecommendation(rec.id)"
                  class="btn-sm btn-dismiss"
                >
                  Dismiss
                </button>
                <span v-if="rec.suggestedDate" class="rec-date">
                  <Icon name="mdi:calendar" class="w-4 h-4" />
                  {{ formatDate(rec.suggestedDate) }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Activities -->
        <div class="activities-container">
          <h3 class="section-title">Recent Activities</h3>
          <div class="activity-timeline">
            <div 
              v-for="event in recentEvents.slice(0, 5)" 
              :key="event.id"
              class="activity-item"
            >
              <div class="activity-icon" :class="`type-${event.type}`">
                <Icon :name="getActivityIcon(event.type)" class="w-4 h-4" />
              </div>
              <div class="activity-content">
                <p class="activity-title">{{ event.title }}</p>
                <p class="activity-meta">
                  <span v-if="event.contactId">Contact</span>
                  <span v-else-if="event.companyId">Company</span>
                  • {{ formatRelativeTime(event.completedAt) }}
                  <span v-if="event.duration"> • {{ event.duration }}min</span>
                </p>
                <div v-if="event.outcome" class="activity-outcome" :class="`outcome-${event.outcome}`">
                  {{ event.outcome }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Upcoming Events -->
        <div class="upcoming-container">
          <h3 class="section-title">Upcoming</h3>
          <div v-if="upcomingEvents.length === 0" class="empty-state">
            <Icon name="mdi:calendar-blank" class="w-8 h-8 text-gray-400" />
            <p class="text-gray-500 text-sm">No upcoming events</p>
          </div>
          <div v-else class="upcoming-list">
            <div 
              v-for="event in upcomingEvents.slice(0, 3)" 
              :key="event.id"
              class="upcoming-item"
            >
              <div class="upcoming-date">
                <span class="date-day">{{ formatDay(event.scheduledAt) }}</span>
                <span class="date-month">{{ formatMonth(event.scheduledAt) }}</span>
              </div>
              <div class="upcoming-info">
                <p class="upcoming-title">{{ event.title }}</p>
                <p class="upcoming-time">
                  <Icon name="mdi:clock-outline" class="w-4 h-4" />
                  {{ formatTime(event.scheduledAt) }}
                  <span v-if="event.duration"> • {{ event.duration }}min</span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Log Modal -->
    <Teleport to="body">
      <div v-if="showQuickLog" class="modal-overlay" @click="showQuickLog = false">
        <div class="modal-content" @click.stop>
          <h3 class="modal-title">Quick Log Interaction</h3>
          
          <form @submit.prevent="submitQuickLog" class="quick-log-form">
            <div class="form-group">
              <label>Type</label>
              <div class="interaction-types">
                <button
                  v-for="type in interactionTypes"
                  :key="type.value"
                  type="button"
                  @click="quickLogData.type = type.value"
                  class="type-button"
                  :class="{ active: quickLogData.type === type.value }"
                >
                  <Icon :name="type.icon" class="w-5 h-5" />
                  <span>{{ type.label }}</span>
                </button>
              </div>
            </div>

            <div class="form-group">
              <label>Contact or Company</label>
              <div class="entity-select">
                <input
                  v-model="quickLogData.entitySearch"
                  @input="searchEntities"
                  placeholder="Search contacts or companies..."
                  class="form-input"
                />
                <div v-if="entitySearchResults.length > 0" class="search-results">
                  <div
                    v-for="result in entitySearchResults"
                    :key="result.id"
                    @click="selectEntity(result)"
                    class="search-result"
                  >
                    <Icon 
                      :name="result.type === 'contact' ? 'mdi:account' : 'mdi:domain'" 
                      class="w-4 h-4"
                    />
                    {{ result.name }}
                  </div>
                </div>
              </div>
            </div>

            <div class="form-group">
              <label>Notes (optional)</label>
              <textarea
                v-model="quickLogData.notes"
                rows="3"
                class="form-textarea"
                placeholder="Add any notes about this interaction..."
              />
            </div>

            <div class="modal-actions">
              <button type="button" @click="showQuickLog = false" class="btn-secondary">
                Cancel
              </button>
              <button type="submit" class="btn-primary" :disabled="!canSubmitQuickLog">
                <Icon name="mdi:check" class="w-5 h-5 mr-2" />
                Log Interaction
              </button>
            </div>
          </form>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { Chart, registerables } from 'chart.js'
import { useEngagementTracking } from '~/composables/useEngagementTracking'
import { useContacts } from '~/composables/useContacts'
import { useBusinessEntities } from '~/composables/useBusinessEntities'
import { format, formatDistance, formatRelative } from 'date-fns'
import type { EngagementType, EngagementAnalytics } from '~/composables/useEngagementTracking'

// Register Chart.js components
Chart.register(...registerables)

// Composables
const { 
  analytics, 
  pendingRecommendations, 
  recentEvents, 
  upcomingEvents,
  generateAnalytics,
  quickLogInteraction,
  acceptRecommendation: acceptRec,
  dismissRecommendation: dismissRec
} = useEngagementTracking()

const { contacts, searchContacts } = useContacts()
const { businessEntities, searchBusinessEntities } = useBusinessEntities()

// State
const selectedPeriod = ref<EngagementAnalytics['period']>('month')
const showQuickLog = ref(false)
const timelineChart = ref<HTMLCanvasElement>()
const breakdownChart = ref<HTMLCanvasElement>()
let timelineChartInstance: Chart | null = null
let breakdownChartInstance: Chart | null = null

// Quick Log State
const quickLogData = ref({
  type: 'call' as EngagementType,
  entityId: '',
  entityType: 'contact' as 'contact' | 'company',
  entitySearch: '',
  notes: ''
})

const entitySearchResults = ref<Array<{
  id: string
  name: string
  type: 'contact' | 'company'
}>>([])

const interactionTypes = [
  { value: 'email', label: 'Email', icon: 'mdi:email' },
  { value: 'call', label: 'Call', icon: 'mdi:phone' },
  { value: 'meeting', label: 'Meeting', icon: 'mdi:calendar' },
  { value: 'social', label: 'Social', icon: 'mdi:message' },
  { value: 'note', label: 'Note', icon: 'mdi:note' }
]

// Computed
const highPriorityCount = computed(() => 
  pendingRecommendations.value.filter(r => r.priority === 'high').length
)

const topEntities = computed(() => 
  analytics.value?.metrics.topEngagedEntities || []
)

const canSubmitQuickLog = computed(() => 
  quickLogData.value.entityId && quickLogData.value.type
)

// Methods
async function refreshAnalytics() {
  await generateAnalytics(selectedPeriod.value)
  updateCharts()
}

function updateCharts() {
  if (!analytics.value) return

  // Update Timeline Chart
  if (timelineChart.value && timelineChartInstance) {
    const ctx = timelineChart.value.getContext('2d')
    if (!ctx) return

    // Generate sample timeline data (would use real trend data)
    const labels = []
    const data = []
    const now = new Date()
    
    for (let i = 29; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000)
      labels.push(format(date, 'MMM d'))
      data.push(Math.floor(Math.random() * 20) + 5) // Sample data
    }

    timelineChartInstance.data = {
      labels,
      datasets: [{
        label: 'Daily Interactions',
        data,
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        fill: true
      }]
    }
    timelineChartInstance.update()
  }

  // Update Breakdown Chart
  if (breakdownChart.value && breakdownChartInstance) {
    const breakdown = analytics.value.metrics.interactionBreakdown
    
    breakdownChartInstance.data = {
      labels: Object.keys(breakdown),
      datasets: [{
        data: Object.values(breakdown),
        backgroundColor: [
          'rgba(59, 130, 246, 0.8)',
          'rgba(16, 185, 129, 0.8)',
          'rgba(139, 92, 246, 0.8)',
          'rgba(251, 146, 60, 0.8)',
          'rgba(244, 63, 94, 0.8)'
        ]
      }]
    }
    breakdownChartInstance.update()
  }
}

function initCharts() {
  // Timeline Chart
  if (timelineChart.value) {
    const ctx = timelineChart.value.getContext('2d')
    if (ctx) {
      timelineChartInstance = new Chart(ctx, {
        type: 'line',
        data: {
          labels: [],
          datasets: []
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true
            }
          }
        }
      })
    }
  }

  // Breakdown Chart
  if (breakdownChart.value) {
    const ctx = breakdownChart.value.getContext('2d')
    if (ctx) {
      breakdownChartInstance = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: [],
          datasets: []
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'right'
            }
          }
        }
      })
    }
  }
}

async function searchEntities(event: Event) {
  const search = (event.target as HTMLInputElement).value
  if (!search || search.length < 2) {
    entitySearchResults.value = []
    return
  }

  const contactResults = await searchContacts(search)
  const companyResults = await searchBusinessEntities(search)

  entitySearchResults.value = [
    ...contactResults.slice(0, 3).map(c => ({
      id: c.id,
      name: `${c.firstName} ${c.lastName}`,
      type: 'contact' as const
    })),
    ...companyResults.slice(0, 3).map(c => ({
      id: c.id,
      name: c.name,
      type: 'company' as const
    }))
  ]
}

function selectEntity(entity: typeof entitySearchResults.value[0]) {
  quickLogData.value.entityId = entity.id
  quickLogData.value.entityType = entity.type
  quickLogData.value.entitySearch = entity.name
  entitySearchResults.value = []
}

async function submitQuickLog() {
  if (!canSubmitQuickLog.value) return

  try {
    await quickLogInteraction(
      quickLogData.value.type,
      quickLogData.value.entityId,
      quickLogData.value.entityType,
      quickLogData.value.notes
    )

    // Reset form
    quickLogData.value = {
      type: 'call',
      entityId: '',
      entityType: 'contact',
      entitySearch: '',
      notes: ''
    }
    showQuickLog.value = false

    // Refresh analytics
    await refreshAnalytics()
  } catch (error) {
    console.error('Failed to log interaction:', error)
  }
}

async function acceptRecommendation(id: string) {
  await acceptRec(id)
}

async function dismissRecommendation(id: string) {
  await dismissRec(id)
}

function viewEntityDetails(entity: any) {
  // Navigate to entity details
  navigateTo(`/${entity.entityType}s/${entity.entityId}`)
}

// Formatting helpers
function getScoreClass(score: number): string {
  if (score >= 80) return 'bg-green-500'
  if (score >= 60) return 'bg-blue-500'
  if (score >= 40) return 'bg-yellow-500'
  return 'bg-red-500'
}

function getRecommendationIcon(type: string): string {
  const icons: Record<string, string> = {
    follow_up: 'mdi:phone-forward',
    reconnect: 'mdi:account-reactivate',
    escalate: 'mdi:arrow-up-bold',
    maintain: 'mdi:shield-check',
    opportunity: 'mdi:lightbulb'
  }
  return icons[type] || 'mdi:information'
}

function formatRecommendationType(type: string): string {
  return type.split('_').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ')
}

function getActivityIcon(type: string): string {
  const icons: Record<string, string> = {
    email: 'mdi:email',
    call: 'mdi:phone',
    meeting: 'mdi:calendar',
    social: 'mdi:message',
    note: 'mdi:note',
    task: 'mdi:checkbox-marked'
  }
  return icons[type] || 'mdi:dots-horizontal'
}

function formatRelativeTime(date: Date | undefined): string {
  if (!date) return 'Never'
  return formatDistance(date, new Date(), { addSuffix: true })
}

function formatDate(date: Date): string {
  return format(date, 'MMM d')
}

function formatDay(date: Date | undefined): string {
  if (!date) return ''
  return format(date, 'd')
}

function formatMonth(date: Date | undefined): string {
  if (!date) return ''
  return format(date, 'MMM')
}

function formatTime(date: Date | undefined): string {
  if (!date) return ''
  return format(date, 'h:mm a')
}

// Lifecycle
onMounted(async () => {
  await refreshAnalytics()
  initCharts()
})

onUnmounted(() => {
  if (timelineChartInstance) {
    timelineChartInstance.destroy()
  }
  if (breakdownChartInstance) {
    breakdownChartInstance.destroy()
  }
})

// Watch for analytics updates
watch(analytics, () => {
  updateCharts()
})
</script>

<style scoped>
.engagement-dashboard {
  @apply p-6 max-w-7xl mx-auto;
}

.dashboard-header {
  @apply flex justify-between items-center mb-6;
}

.header-actions {
  @apply flex items-center space-x-4;
}

.period-select {
  @apply px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500;
}

.btn-primary {
  @apply flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors;
}

.btn-secondary {
  @apply px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors;
}

.metrics-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8;
}

.metric-card {
  @apply bg-white p-6 rounded-lg shadow-sm border border-gray-200 flex items-start space-x-4;
}

.metric-icon {
  @apply p-3 rounded-lg flex-shrink-0;
}

.metric-content {
  @apply flex-1;
}

.metric-label {
  @apply text-sm text-gray-600 mb-1;
}

.metric-value {
  @apply text-2xl font-bold text-gray-900;
}

.metric-change {
  @apply text-sm flex items-center mt-2;
}

.metric-subtext {
  @apply text-sm text-gray-500 mt-1;
}

.score-bar {
  @apply w-full h-2 bg-gray-200 rounded-full mt-2 overflow-hidden;
}

.score-fill {
  @apply h-full transition-all duration-500;
}

.dashboard-content {
  @apply grid grid-cols-1 lg:grid-cols-3 gap-6;
}

.content-left {
  @apply lg:col-span-2 space-y-6;
}

.content-right {
  @apply space-y-6;
}

.chart-container {
  @apply bg-white p-6 rounded-lg shadow-sm border border-gray-200;
}

.chart-title {
  @apply text-lg font-semibold text-gray-900 mb-4;
}

.section-title {
  @apply text-lg font-semibold text-gray-900 mb-4 flex items-center justify-between;
}

.ml-badge {
  @apply text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full font-normal;
}

.top-entities-container,
.recommendations-container,
.activities-container,
.upcoming-container {
  @apply bg-white p-6 rounded-lg shadow-sm border border-gray-200;
}

.entity-list {
  @apply space-y-3;
}

.entity-item {
  @apply flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors;
}

.entity-avatar {
  @apply w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center text-gray-600;
}

.entity-info {
  @apply flex-1;
}

.entity-name {
  @apply font-medium text-gray-900;
}

.entity-meta {
  @apply text-sm text-gray-500;
}

.entity-score {
  @apply w-24;
}

.mini-score-bar {
  @apply w-full h-1.5 bg-gray-200 rounded-full overflow-hidden;
}

.mini-score-fill {
  @apply h-full bg-blue-500;
}

.empty-state {
  @apply text-center py-8 flex flex-col items-center;
}

.recommendation-list {
  @apply space-y-3;
}

.recommendation-card {
  @apply p-4 rounded-lg border-l-4 bg-gray-50;
}

.recommendation-card.priority-high {
  @apply border-red-500 bg-red-50;
}

.recommendation-card.priority-medium {
  @apply border-yellow-500 bg-yellow-50;
}

.recommendation-card.priority-low {
  @apply border-green-500 bg-green-50;
}

.rec-header {
  @apply flex items-center space-x-2 mb-2;
}

.rec-icon {
  @apply w-5 h-5;
}

.rec-type {
  @apply font-medium text-sm;
}

.rec-confidence {
  @apply text-xs text-gray-500 ml-auto;
}

.rec-action {
  @apply font-medium text-gray-900 mb-1;
}

.rec-reason {
  @apply text-sm text-gray-600 mb-3;
}

.rec-actions {
  @apply flex items-center space-x-2;
}

.btn-sm {
  @apply px-3 py-1 text-sm rounded-md transition-colors;
}

.btn-accept {
  @apply bg-green-600 text-white hover:bg-green-700;
}

.btn-dismiss {
  @apply bg-gray-300 text-gray-700 hover:bg-gray-400;
}

.rec-date {
  @apply text-xs text-gray-500 ml-auto flex items-center;
}

.activity-timeline {
  @apply space-y-3;
}

.activity-item {
  @apply flex items-start space-x-3;
}

.activity-icon {
  @apply w-8 h-8 rounded-full flex items-center justify-center text-white flex-shrink-0;
}

.activity-icon.type-email {
  @apply bg-blue-500;
}

.activity-icon.type-call {
  @apply bg-green-500;
}

.activity-icon.type-meeting {
  @apply bg-purple-500;
}

.activity-icon.type-social {
  @apply bg-pink-500;
}

.activity-icon.type-note {
  @apply bg-gray-500;
}

.activity-content {
  @apply flex-1;
}

.activity-title {
  @apply font-medium text-gray-900;
}

.activity-meta {
  @apply text-sm text-gray-500;
}

.activity-outcome {
  @apply inline-block text-xs px-2 py-1 rounded-full mt-1;
}

.activity-outcome.outcome-positive {
  @apply bg-green-100 text-green-700;
}

.activity-outcome.outcome-neutral {
  @apply bg-gray-100 text-gray-700;
}

.activity-outcome.outcome-negative {
  @apply bg-red-100 text-red-700;
}

.upcoming-list {
  @apply space-y-3;
}

.upcoming-item {
  @apply flex items-start space-x-3;
}

.upcoming-date {
  @apply text-center flex-shrink-0;
}

.date-day {
  @apply block text-2xl font-bold text-gray-900;
}

.date-month {
  @apply block text-sm text-gray-500 uppercase;
}

.upcoming-info {
  @apply flex-1;
}

.upcoming-title {
  @apply font-medium text-gray-900;
}

.upcoming-time {
  @apply text-sm text-gray-500 flex items-center mt-1;
}

/* Modal Styles */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.modal-content {
  @apply bg-white rounded-lg p-6 max-w-md w-full mx-4;
}

.modal-title {
  @apply text-xl font-semibold text-gray-900 mb-4;
}

.quick-log-form {
  @apply space-y-4;
}

.form-group {
  @apply space-y-2;
}

.form-group label {
  @apply block text-sm font-medium text-gray-700;
}

.interaction-types {
  @apply grid grid-cols-3 gap-2;
}

.type-button {
  @apply flex flex-col items-center p-3 border rounded-lg transition-colors;
}

.type-button.active {
  @apply border-blue-500 bg-blue-50 text-blue-600;
}

.type-button:not(.active) {
  @apply border-gray-300 hover:border-gray-400 text-gray-600;
}

.entity-select {
  @apply relative;
}

.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500;
}

.form-textarea {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none;
}

.search-results {
  @apply absolute top-full mt-1 w-full bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-48 overflow-y-auto;
}

.search-result {
  @apply px-3 py-2 hover:bg-gray-50 cursor-pointer flex items-center space-x-2;
}

.modal-actions {
  @apply flex justify-end space-x-3 mt-6;
}
</style>