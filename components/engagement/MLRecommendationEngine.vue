<template>
  <div class="ml-engine">
    <!-- ML Engine Status -->
    <div class="engine-status">
      <div class="status-header">
        <h3 class="status-title">
          <Icon name="mdi:robot" class="w-5 h-5" />
          ML Recommendation Engine
        </h3>
        <div class="status-indicator" :class="statusClass">
          <div class="status-dot"></div>
          <span>{{ status }}</span>
        </div>
      </div>

      <!-- Model Metrics -->
      <div v-if="modelLoaded" class="model-metrics">
        <div class="metric">
          <span class="metric-label">Model Version:</span>
          <span class="metric-value">{{ modelVersion }}</span>
        </div>
        <div class="metric">
          <span class="metric-label">Accuracy:</span>
          <span class="metric-value">{{ (modelAccuracy * 100).toFixed(1) }}%</span>
        </div>
        <div class="metric">
          <span class="metric-label">Training Samples:</span>
          <span class="metric-value">{{ trainingSamples.toLocaleString() }}</span>
        </div>
        <div class="metric">
          <span class="metric-label">Last Updated:</span>
          <span class="metric-value">{{ formatRelativeTime(lastUpdated) }}</span>
        </div>
      </div>

      <!-- Training Progress -->
      <div v-if="isTraining" class="training-progress">
        <p class="progress-label">Training in progress...</p>
        <div class="progress-bar">
          <div 
            class="progress-fill"
            :style="{ width: `${trainingProgress}%` }"
          />
        </div>
        <p class="progress-stats">
          Epoch {{ currentEpoch }} / {{ totalEpochs }} • Loss: {{ currentLoss.toFixed(4) }}
        </p>
      </div>
    </div>

    <!-- Recommendation Controls -->
    <div class="recommendation-controls">
      <h4 class="controls-title">Recommendation Settings</h4>
      
      <div class="control-group">
        <label class="control-label">Confidence Threshold</label>
        <div class="slider-container">
          <input
            v-model.number="confidenceThreshold"
            type="range"
            min="0.5"
            max="0.95"
            step="0.05"
            class="confidence-slider"
          />
          <span class="slider-value">{{ (confidenceThreshold * 100).toFixed(0) }}%</span>
        </div>
      </div>

      <div class="control-group">
        <label class="control-label">Recommendation Types</label>
        <div class="type-toggles">
          <label v-for="type in recommendationTypes" :key="type.value" class="type-toggle">
            <input
              v-model="enabledTypes"
              :value="type.value"
              type="checkbox"
              class="toggle-checkbox"
            />
            <span class="toggle-label">{{ type.label }}</span>
          </label>
        </div>
      </div>

      <div class="control-group">
        <label class="control-label">Max Recommendations</label>
        <select v-model.number="maxRecommendations" class="control-select">
          <option :value="5">5 per entity</option>
          <option :value="10">10 per entity</option>
          <option :value="20">20 per entity</option>
        </select>
      </div>
    </div>

    <!-- Model Actions -->
    <div class="model-actions">
      <button
        @click="retrainModel"
        :disabled="isTraining"
        class="action-btn retrain-btn"
      >
        <Icon name="mdi:refresh" class="w-5 h-5" />
        <span>Retrain Model</span>
      </button>

      <button
        @click="exportModel"
        :disabled="!modelLoaded"
        class="action-btn export-btn"
      >
        <Icon name="mdi:download" class="w-5 h-5" />
        <span>Export Model</span>
      </button>

      <button
        @click="showInsights = true"
        class="action-btn insights-btn"
      >
        <Icon name="mdi:chart-line" class="w-5 h-5" />
        <span>View Insights</span>
      </button>
    </div>

    <!-- Feature Importance Visualization -->
    <div v-if="modelLoaded" class="feature-importance">
      <h4 class="section-title">Feature Importance</h4>
      <div class="importance-chart">
        <div
          v-for="feature in topFeatures"
          :key="feature.name"
          class="feature-bar"
        >
          <div class="feature-name">{{ feature.label }}</div>
          <div class="feature-bar-container">
            <div
              class="feature-bar-fill"
              :style="{ width: `${feature.importance * 100}%` }"
            />
            <span class="feature-value">{{ (feature.importance * 100).toFixed(1) }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Model Insights Modal -->
    <Teleport to="body">
      <div v-if="showInsights" class="modal-overlay" @click="showInsights = false">
        <div class="modal-content" @click.stop>
          <div class="modal-header">
            <h3 class="modal-title">ML Model Insights</h3>
            <button @click="showInsights = false" class="modal-close">
              <Icon name="mdi:close" class="w-6 h-6" />
            </button>
          </div>

          <div class="insights-content">
            <!-- Prediction Distribution -->
            <div class="insight-section">
              <h4 class="insight-title">Recommendation Distribution</h4>
              <canvas ref="distributionChart" height="200"></canvas>
            </div>

            <!-- Model Performance Over Time -->
            <div class="insight-section">
              <h4 class="insight-title">Model Performance</h4>
              <canvas ref="performanceChart" height="200"></canvas>
            </div>

            <!-- Key Patterns -->
            <div class="insight-section">
              <h4 class="insight-title">Discovered Patterns</h4>
              <div class="pattern-list">
                <div v-for="pattern in discoveredPatterns" :key="pattern.id" class="pattern-item">
                  <Icon :name="pattern.icon" class="pattern-icon" />
                  <div class="pattern-content">
                    <p class="pattern-title">{{ pattern.title }}</p>
                    <p class="pattern-description">{{ pattern.description }}</p>
                    <div class="pattern-stats">
                      <span class="stat">
                        <Icon name="mdi:account-multiple" class="w-4 h-4" />
                        {{ pattern.affectedEntities }} entities
                      </span>
                      <span class="stat">
                        <Icon name="mdi:check-circle" class="w-4 h-4" />
                        {{ pattern.accuracy }}% accurate
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import * as tf from '@tensorflow/tfjs'
import { Chart, registerables } from 'chart.js'
import { useEngagementTracking } from '~/composables/useEngagementTracking'
import { formatDistance } from 'date-fns'

// Register Chart.js
Chart.register(...registerables)

// Composables
const { engagementEvents, engagementScores } = useEngagementTracking()

// State
const status = ref<'initializing' | 'ready' | 'training' | 'predicting'>('initializing')
const modelLoaded = ref(false)
const modelVersion = ref('2.0-tfjs')
const modelAccuracy = ref(0.867)
const trainingSamples = ref(15420)
const lastUpdated = ref(new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)) // 2 days ago

const isTraining = ref(false)
const trainingProgress = ref(0)
const currentEpoch = ref(0)
const totalEpochs = ref(50)
const currentLoss = ref(0.0)

const confidenceThreshold = ref(0.75)
const enabledTypes = ref(['follow_up', 'reconnect', 'opportunity'])
const maxRecommendations = ref(10)
const showInsights = ref(false)

// TensorFlow.js Model
let model: tf.LayersModel | null = null
let distributionChart: Chart | null = null
let performanceChart: Chart | null = null
const distributionChartRef = ref<HTMLCanvasElement>()
const performanceChartRef = ref<HTMLCanvasElement>()

// Recommendation Types
const recommendationTypes = [
  { value: 'follow_up', label: 'Follow-up Timing' },
  { value: 'reconnect', label: 'Re-engagement' },
  { value: 'escalate', label: 'Escalation' },
  { value: 'maintain', label: 'Maintenance' },
  { value: 'opportunity', label: 'Opportunities' }
]

// Feature Importance (simulated for demo)
const topFeatures = ref([
  { name: 'recency', label: 'Days Since Last Contact', importance: 0.342 },
  { name: 'frequency', label: 'Interaction Frequency', importance: 0.289 },
  { name: 'sentiment', label: 'Average Sentiment', importance: 0.178 },
  { name: 'duration', label: 'Engagement Duration', importance: 0.134 },
  { name: 'diversity', label: 'Channel Diversity', importance: 0.057 }
])

// Discovered Patterns (simulated for demo)
const discoveredPatterns = ref([
  {
    id: '1',
    icon: 'mdi:calendar-clock',
    title: 'Optimal Follow-up Window',
    description: 'Contacts are 3x more likely to respond when followed up within 3-5 days after initial meeting',
    affectedEntities: 342,
    accuracy: 89
  },
  {
    id: '2',
    icon: 'mdi:email-fast',
    title: 'Email Engagement Pattern',
    description: 'Tuesday mornings (9-11 AM) show 45% higher email open rates',
    affectedEntities: 567,
    accuracy: 76
  },
  {
    id: '3',
    icon: 'mdi:trending-up',
    title: 'Opportunity Indicator',
    description: 'Rapid increase in engagement frequency often precedes deal closure by 2-3 weeks',
    affectedEntities: 128,
    accuracy: 82
  }
])

// Computed
const statusClass = computed(() => {
  switch (status.value) {
    case 'ready':
      return 'status-ready'
    case 'training':
      return 'status-training'
    case 'predicting':
      return 'status-predicting'
    default:
      return 'status-initializing'
  }
})

// Methods
async function initializeModel() {
  try {
    status.value = 'initializing'
    
    // Create a simple neural network for recommendation scoring
    model = tf.sequential({
      layers: [
        tf.layers.dense({
          inputShape: [10], // 10 input features
          units: 32,
          activation: 'relu',
          kernelInitializer: 'glorotUniform'
        }),
        tf.layers.dropout({ rate: 0.2 }),
        tf.layers.dense({
          units: 16,
          activation: 'relu'
        }),
        tf.layers.dropout({ rate: 0.1 }),
        tf.layers.dense({
          units: 5, // 5 recommendation types
          activation: 'softmax'
        })
      ]
    })

    // Compile the model
    model.compile({
      optimizer: tf.train.adam(0.001),
      loss: 'categoricalCrossentropy',
      metrics: ['accuracy']
    })

    modelLoaded.value = true
    status.value = 'ready'
  } catch (error) {
    console.error('Failed to initialize ML model:', error)
    status.value = 'ready' // Fallback to rule-based
  }
}

async function prepareTrainingData() {
  // In a real implementation, this would prepare actual training data
  // from historical engagement events and outcomes
  
  const numSamples = 1000
  const features = tf.randomNormal([numSamples, 10])
  const labels = tf.randomUniform([numSamples, 5])
  
  return { features, labels }
}

async function retrainModel() {
  if (!model || isTraining.value) return

  try {
    isTraining.value = true
    status.value = 'training'
    trainingProgress.value = 0
    currentEpoch.value = 0

    const { features, labels } = await prepareTrainingData()

    await model.fit(features, labels, {
      epochs: totalEpochs.value,
      batchSize: 32,
      validationSplit: 0.2,
      callbacks: {
        onEpochEnd: (epoch, logs) => {
          currentEpoch.value = epoch + 1
          trainingProgress.value = ((epoch + 1) / totalEpochs.value) * 100
          currentLoss.value = logs?.loss || 0
          
          if (logs?.acc) {
            modelAccuracy.value = logs.acc
          }
        }
      }
    })

    // Cleanup tensors
    features.dispose()
    labels.dispose()

    trainingSamples.value += 1000
    lastUpdated.value = new Date()
    status.value = 'ready'
  } catch (error) {
    console.error('Failed to retrain model:', error)
    status.value = 'ready'
  } finally {
    isTraining.value = false
  }
}

async function generatePredictions(entityId: string, entityType: 'contact' | 'company') {
  if (!model || !modelLoaded.value) {
    // Fallback to rule-based recommendations
    return null
  }

  try {
    status.value = 'predicting'

    // Extract features for the entity
    const features = extractEntityFeatures(entityId, entityType)
    
    // Make prediction
    const input = tf.tensor2d([features], [1, 10])
    const prediction = model.predict(input) as tf.Tensor
    const scores = await prediction.array() as number[][]
    
    // Cleanup
    input.dispose()
    prediction.dispose()

    // Convert to recommendations
    const recommendations = scores[0]
      .map((score, index) => ({
        type: recommendationTypes[index].value,
        confidence: score
      }))
      .filter(rec => rec.confidence >= confidenceThreshold.value)
      .filter(rec => enabledTypes.value.includes(rec.type))
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, maxRecommendations.value)

    status.value = 'ready'
    return recommendations
  } catch (error) {
    console.error('Failed to generate predictions:', error)
    status.value = 'ready'
    return null
  }
}

function extractEntityFeatures(entityId: string, entityType: 'contact' | 'company'): number[] {
  // Extract relevant features from engagement data
  const score = engagementScores.value.get(entityId)
  if (!score) {
    return new Array(10).fill(0)
  }

  // Normalize features to 0-1 range
  return [
    score.components.recency / 100,
    score.components.frequency / 100,
    score.components.depth / 100,
    score.components.diversity / 100,
    score.components.consistency / 100,
    score.components.sentiment / 100,
    score.trend === 'increasing' ? 1 : score.trend === 'decreasing' ? 0 : 0.5,
    Math.min(score.trendData.length / 30, 1), // Data history
    entityType === 'contact' ? 1 : 0,
    Math.random() // Random feature for demo
  ]
}

async function exportModel() {
  if (!model) return

  try {
    await model.save('downloads://engagement-ml-model')
  } catch (error) {
    console.error('Failed to export model:', error)
  }
}

function initCharts() {
  // Distribution Chart
  if (distributionChartRef.value) {
    const ctx = distributionChartRef.value.getContext('2d')
    if (ctx) {
      distributionChart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: recommendationTypes.map(t => t.label),
          datasets: [{
            label: 'Recommendations',
            data: [245, 189, 156, 98, 67],
            backgroundColor: [
              'rgba(59, 130, 246, 0.8)',
              'rgba(16, 185, 129, 0.8)',
              'rgba(251, 146, 60, 0.8)',
              'rgba(139, 92, 246, 0.8)',
              'rgba(244, 63, 94, 0.8)'
            ]
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          }
        }
      })
    }
  }

  // Performance Chart
  if (performanceChartRef.value) {
    const ctx = performanceChartRef.value.getContext('2d')
    if (ctx) {
      const labels = []
      const accuracyData = []
      const lossData = []
      
      // Generate sample data
      for (let i = 6; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        labels.push(date.toLocaleDateString('en', { month: 'short', day: 'numeric' }))
        accuracyData.push(0.8 + Math.random() * 0.1)
        lossData.push(0.3 - Math.random() * 0.1)
      }

      performanceChart = new Chart(ctx, {
        type: 'line',
        data: {
          labels,
          datasets: [
            {
              label: 'Accuracy',
              data: accuracyData,
              borderColor: 'rgb(16, 185, 129)',
              backgroundColor: 'rgba(16, 185, 129, 0.1)',
              tension: 0.4,
              yAxisID: 'y'
            },
            {
              label: 'Loss',
              data: lossData,
              borderColor: 'rgb(244, 63, 94)',
              backgroundColor: 'rgba(244, 63, 94, 0.1)',
              tension: 0.4,
              yAxisID: 'y1'
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          interaction: {
            mode: 'index',
            intersect: false
          },
          scales: {
            y: {
              type: 'linear',
              display: true,
              position: 'left',
              min: 0,
              max: 1
            },
            y1: {
              type: 'linear',
              display: true,
              position: 'right',
              min: 0,
              max: 0.5,
              grid: {
                drawOnChartArea: false
              }
            }
          }
        }
      })
    }
  }
}

function formatRelativeTime(date: Date): string {
  return formatDistance(date, new Date(), { addSuffix: true })
}

// Lifecycle
onMounted(async () => {
  await initializeModel()
})

onUnmounted(() => {
  if (model) {
    model.dispose()
  }
  if (distributionChart) {
    distributionChart.destroy()
  }
  if (performanceChart) {
    performanceChart.destroy()
  }
})

// Watch for insights modal
watch(showInsights, (show) => {
  if (show) {
    setTimeout(() => {
      initCharts()
    }, 100)
  }
})

// Expose methods for parent components
defineExpose({
  generatePredictions,
  retrainModel
})
</script>

<style scoped>
.ml-engine {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6 space-y-6;
}

.engine-status {
  @apply space-y-4;
}

.status-header {
  @apply flex items-center justify-between;
}

.status-title {
  @apply text-lg font-semibold text-gray-900 flex items-center space-x-2;
}

.status-indicator {
  @apply flex items-center space-x-2 text-sm;
}

.status-dot {
  @apply w-2 h-2 rounded-full;
}

.status-ready .status-dot {
  @apply bg-green-500 animate-pulse;
}

.status-training .status-dot {
  @apply bg-yellow-500 animate-pulse;
}

.status-predicting .status-dot {
  @apply bg-blue-500 animate-pulse;
}

.status-initializing .status-dot {
  @apply bg-gray-500;
}

.model-metrics {
  @apply grid grid-cols-2 gap-4;
}

.metric {
  @apply flex flex-col;
}

.metric-label {
  @apply text-sm text-gray-500;
}

.metric-value {
  @apply text-base font-medium text-gray-900;
}

.training-progress {
  @apply space-y-2;
}

.progress-label {
  @apply text-sm text-gray-600;
}

.progress-bar {
  @apply w-full h-2 bg-gray-200 rounded-full overflow-hidden;
}

.progress-fill {
  @apply h-full bg-blue-600 transition-all duration-300;
}

.progress-stats {
  @apply text-xs text-gray-500;
}

.recommendation-controls {
  @apply space-y-4 border-t pt-4;
}

.controls-title {
  @apply font-medium text-gray-900 mb-3;
}

.control-group {
  @apply space-y-2;
}

.control-label {
  @apply block text-sm font-medium text-gray-700;
}

.slider-container {
  @apply flex items-center space-x-3;
}

.confidence-slider {
  @apply flex-1;
}

.slider-value {
  @apply text-sm font-medium text-gray-900 w-12 text-right;
}

.type-toggles {
  @apply space-y-2;
}

.type-toggle {
  @apply flex items-center space-x-2 cursor-pointer;
}

.toggle-checkbox {
  @apply rounded border-gray-300 text-blue-600 focus:ring-blue-500;
}

.toggle-label {
  @apply text-sm text-gray-700;
}

.control-select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500;
}

.model-actions {
  @apply flex space-x-3 border-t pt-4;
}

.action-btn {
  @apply flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors;
}

.retrain-btn {
  @apply bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed;
}

.export-btn {
  @apply bg-gray-600 text-white hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed;
}

.insights-btn {
  @apply bg-purple-600 text-white hover:bg-purple-700;
}

.feature-importance {
  @apply border-t pt-4;
}

.section-title {
  @apply font-medium text-gray-900 mb-3;
}

.importance-chart {
  @apply space-y-2;
}

.feature-bar {
  @apply space-y-1;
}

.feature-name {
  @apply text-sm text-gray-700;
}

.feature-bar-container {
  @apply relative w-full h-6 bg-gray-200 rounded overflow-hidden;
}

.feature-bar-fill {
  @apply absolute inset-y-0 left-0 bg-gradient-to-r from-blue-500 to-purple-500;
}

.feature-value {
  @apply absolute inset-y-0 right-2 flex items-center text-xs font-medium text-gray-700;
}

/* Modal Styles */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4;
}

.modal-content {
  @apply bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col;
}

.modal-header {
  @apply flex items-center justify-between p-6 border-b;
}

.modal-title {
  @apply text-xl font-semibold text-gray-900;
}

.modal-close {
  @apply p-1 text-gray-400 hover:text-gray-600 transition-colors;
}

.insights-content {
  @apply p-6 space-y-6 overflow-y-auto;
}

.insight-section {
  @apply space-y-3;
}

.insight-title {
  @apply font-medium text-gray-900;
}

.pattern-list {
  @apply space-y-3;
}

.pattern-item {
  @apply flex items-start space-x-3 p-4 bg-gray-50 rounded-lg;
}

.pattern-icon {
  @apply w-10 h-10 p-2 bg-white rounded-lg text-blue-600 flex-shrink-0;
}

.pattern-content {
  @apply flex-1;
}

.pattern-title {
  @apply font-medium text-gray-900;
}

.pattern-description {
  @apply text-sm text-gray-600 mt-1;
}

.pattern-stats {
  @apply flex items-center space-x-4 mt-2;
}

.stat {
  @apply flex items-center space-x-1 text-xs text-gray-500;
}
</style>