<script setup lang="ts">
defineProps({
  item: {
    type: Object,
    required: true,
  },
})
</script>

<template>
    <div class="flex">
        <div class="relative flex-shrink-0 w-12 text-center">
            <span
              class="absolute border-gray-200 border-dashed ltr:left-1/2 ltr:-ml-1 rtl:right-1/2 rtl:-mr-1 top-10 -bottom-4 ltr:border-l-2 rtl:border-r-2 dark:border-gray-700"></span>
            <div class="ltr:mr-auto rtl:ml-auto">
              <div
                class="flex items-center justify-center w-10 h-10 bg-gray-100 border border-gray-200 rounded-full text-primary_focus dark:border-gray-700 dark:bg-gray-900 dark:bg-opacity-40">
                <Icon :name="item.icon" size="1.5rem" />
              </div>
            </div>
          </div>
          <div class="mb-4 ltr:ml-4 rtl:mr-4">
            <div class="flex flex-row justify-between pb-2">
              <div class="self-center mr-3 text-xs text-gray-500">
                {{ item.date }}
              </div>
            </div>
            <div v-html="item.text" />
            <div class="self-center mr-3 text-xs text-gray-500">
                {{ item.customer_name }}
              </div>
          </div>
    </div>
</template>