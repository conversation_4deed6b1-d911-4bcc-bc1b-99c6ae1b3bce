<script setup lang="ts">
import { collection, query, where, getDocs } from "firebase/firestore";

// Use the new Firebase initialization
const { firestore } = useFirebase();
const { currentSpace } = space()
const { meUser } = useMe()

const historyProp = defineProps({
  title: {
    type: String,
    default: 'History',
  },
  type: {
    type: String,
    default: 'user',
  },
  id: {
        type: String,
        required: true,
    },
    filter: {
    type: Object,
    default: () => {},
  },
})

const history: any = ref([
])



onMounted(async () => {

  const q = query(collection(firestore, 'history'), where('linked_ids', 'array-contains', historyProp.id))
  const querySnapshot = await getDocs(q);
  querySnapshot.forEach((doc: any) => {
    history.value.push(doc.data())
  });
})
</script>

<template>
  <div class="h-full p-6 rounded-lg shadow-lg theme_100">
    <div class="flex flex-col pb-6">
      <h3 class="text-base font-bold">{{ title }}</h3>
    </div>
    <div class="relative">
      <ol class="mb-6 overflow-y-auto h-72 scrollbars show scrollbar-hide">
        <li class="flex mb-2" v-for="(item, index) in history" :key="index">
          <history-card :item="item" />
        </li>
      </ol>
    </div>
  </div>
</template>