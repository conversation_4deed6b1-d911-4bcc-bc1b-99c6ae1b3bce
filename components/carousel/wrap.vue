<script setup lang='ts'>
// import { Carousel, Navigation, Pagination, Slide } from 'vue3-carousel'
import 'vue3-carousel/dist/carousel.css'
const myPropsP = defineProps({
  schema: {
    type: Object,
    default: () => {},
  },
  index: { type: Number, default: null },
  mainIndex: { type: Number, default: null },
  num: { type: Number, default: null },
  len: { type: Number, default: null },
  numMain: { type: Number, default: null },
  lenMain: { type: Number, default: null },

})
onMounted(() => {
  // const element = document.getElementById(myPropsP.schema.id)
  // if (element) {
  //   const attrs1 = myPropsP.schema.attrs
  //   if (attrs1) {
  //     for (const key in attrs1) {
  //       if (attrs1.hasOwnProperty(key))
  //         element.setAttribute(key, attrs1[key])
  //     }
  //   }
  // }
})

</script>

<template>
  <div
    :id="schema.id"
    class="relative"
    :class="schema.class"
    :style="schema.style"
  >
    
    
    <Carousel :items-to-show="schema.options.items_to_show.value" :wrap-around="schema.wrapAround">
      <Slide v-for="(comp, ind) in schema.children" :key="ind">
        <component
          :is="comp.title"
          :key="ind"
          :feature_id="comp.feature_id ? comp.feature_id : comp.id"
          :num="ind"
          :len="schema.children.length"
          :schema="comp"
        />
      </Slide>

      <template #addons>
        <Navigation />
        <Pagination />
      </template>
    </Carousel>
  </div>
</template>

      <style>
      .carousel__item {
        min-height: 200px;
        width: 100%;
        background-color: var(--vc-clr-primary);
        color:  var(--vc-clr-white);
        font-size: 20px;
        border-radius: 8px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .carousel__slide {
        padding: 10px;
      }

      .carousel__prev,
      .carousel__next {
        box-sizing: content-box;
        border: 5px solid white;
      }
      </style>

