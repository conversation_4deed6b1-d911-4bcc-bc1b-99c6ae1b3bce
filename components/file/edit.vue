<script setup lang="ts">
const { addToCollection: addToCollectionSpaceRegister, updateToId } = database()
const currentClient: object = useState('currentClient', () => {
        return {}
    })
const { $swal } = useNuxtApp()
const { emailContent } = useContent()
const { UploadsFolders, selectedFolder } = useUploads()
const { currentSpace } = space()

const open = ref(false)
const spaceInstance: any = getCurrentInstance()
const formSpaceRegister: any = ref({
  title: '',
  about: '',
  type: '',
  tags: [],
  members: [currentClient.value.id],
  invitees: [],
  other: '',
  approval_needed: false,
  approvals: [],
  status: 'Pending',
  emails: []

})

const selectedFolderEdit = useState('selectedFolderEdit', () => {
  return {}
})

onMounted(()=> {
  formSpaceRegister.value = selectedFolderEdit.value
})
async function submitNetcashRegistration() {

  try {
    console.log(selectedFolder.value)
    if(selectedFolder.value.title)
    formSpaceRegister.value.folders = selectedFolder.value.id
    const res = await updateToId(formSpaceRegister.value, 'folders', formSpaceRegister.value)
    const payload = { ...formSpaceRegister.value, ...res }
    UploadsFolders.value.push(payload)

    await addToCollectionSpaceRegister('notifications', {
      space_id: currentSpace.value.id,
      type: 'invite',
      title: 'Filemanage Folder Invite',
      message: `<p><b>Folder</b> invite ${formSpaceRegister.value.title}</p>`,
      notification_read: 'user',
      notification_action: 'accept',
      status: 'unread',
      col: 'folders',
      component: 'notifications-invite-accept',
      icon: 'mdi:check-circle',
      colour: 'green',
    })

    $swal.fire({
      title: 'Success!',
      text: `Yeah, enjoy`,
      icon: 'success',
      confirmButtonText: 'Cool'
    })
  }
  catch (e) {
    console.error(e)
  }

  formSpaceRegister.value = {
    title: '',
    about: '',
    type: 'Folder',
    tags: [],
    members: [currentClient.value.id],
    invitees: [],
    other: '',
    approval_needed: false,
    approvals: [],
    status: 'Pending',
    market: false,
    parent: [],
    size: 0,


  }
  spaceInstance.emit('close')
}

const emailTypes = ref([
  'Transactional',
  'Newsletter',
  'Announcement',
  'Event invitation',
  'Sales',
  'Follow-up',
  'Onboarding',
  'Survey',
  'Marketing',
  'Customer service',
  'Other'
])

const schema: any = ref({
  options: {
    input_class: { value: 'o_input' },
    search_button_class: { value: 'o_btn_icon_square' },
    icon: { value: 'mdi:magnify' },
  }
})

const userinvite = (us: { id: any; }) => {
  console.log(us)
  //see if in array, if in array then delete, if not in array then add
  if (formSpaceRegister.value.invitees.includes(us.id)) {
    formSpaceRegister.value.invitees = formSpaceRegister.value.invitees.filter((item: any) => item !== us.id)
  } else {
    formSpaceRegister.value.invitees.push(us.id)
  }
  formSpaceRegister.value.invitees = formSpaceRegister.value.invitees.filter(function(x) {
     return x !== undefined})
}

const userapproval = (us: { id: any; }) => {
  console.log(us)
  //see if in array, if in array then delete, if not in array then add
  if (formSpaceRegister.value.approvals.includes(us.id)) {
    formSpaceRegister.value.approvals = formSpaceRegister.value.approvals.filter((item: any) => item !== us.id)
  } else {
    formSpaceRegister.value.approvals.push(us.id)
  }
  // formSpaceRegister.value.invitees.push(us.id)
}

const input = (data) => {
  if (data && data.path === undefined && data.bubbles === undefined)
    formSpaceRegister.value.about = data
}
const removeUser = (us: { id: any }) => {
  console.log(us);
  //see if in array, if in array then delete, if not in array then add
  formSpaceRegister.value.invitees = formSpaceRegister.value.invitees.filter(
      (item: any) => item !== us.id
    );
    formSpaceRegister.value.invitees = formSpaceRegister.value.invitees.filter(function(x) {
     return x !== undefined})
};
const uploadAdd = (data: any) => {
  console.log(data)
  formSpaceRegister.value.images = data
}
</script>
<template>
  <div class="font-sans text-base font-normal theme_300">
    <div class="overflow-x-hidden theme_300">
      <div x-bind:aria-expanded="open"
        :class="{ 'ltr:ml-64 ltr:-mr-64 md:ltr:ml-0 md:ltr:mr-0 rtl:mr-64 rtl:-ml-64 md:rtl:mr-0 md:rtl:ml-0': open, 'ltr:ml-0 ltr:mr-0 md:ltr:ml-64 rtl:mr-0 rtl:ml-0 md:rtl:mr-64': !(open) }"
        class="flex flex-col min-h-screen transition-all duration-500 ease-in-out ltr:ml-64 rtl:mr-64">
        <div class="-mt-2 ">
          <div class="p-2 mx-auto">
            <!-- row -->
            <div class="flex flex-row flex-wrap">
              <!-- <div class="flex-shrink w-full max-w-full px-4">
                <p class="mt-3 mb-5 text-xl font-bold">Create new folder</p>
              </div> -->
              <div class="flex-shrink w-full max-w-full px-4 mb-6">
                <div class="h-full p-6 rounded-lg shadow-lg theme_100">
                  <div class="flex flex-row flex-wrap -mx-4">
                    <div class="flex-shrink w-full max-w-full px-4 mb-4">
                      <label for="inputtitle" class="o_label">Title</label>
                      <input v-model="formSpaceRegister.title" type="text"
                        class="o_input"
                        id="inputtitle">
                    </div>
                    <div class="flex-shrink w-full max-w-full px-4 mb-4">
                      <label for="inputbudget" class="o_label">About</label>
                      <forms-inputs-quill @input="input" :placeholder="formSpaceRegister.about" />
                    </div>
           
                    <div class="flex-shrink w-full max-w-full px-4">
                      <client-only>
                        <TagsSelect type="emailcontent" :show-add="true"
                          @selected-tag="formSpaceRegister.tags = $event" />

                      </client-only>

                    </div>
                
                    <div class="flex-shrink w-full max-w-full px-4 mb-4">
                      <label for="inputtitle" class="o_label">User</label>
                      <user-search :schema="schema" @input="userinvite" />
                      <user-groups :users="formSpaceRegister.invitees" @input="removeUser" />
                    </div>
                 

                    <div class="w-full p-4">
                      <FileButton @upload-add="uploadAdd" />
                      <FileView :images="formSpaceRegister.images"
                        @clicked="formSpaceRegister.images.splice($event, 1)" />
                    </div>


                    <div class="flex-shrink w-full max-w-full px-4">
                      <button @click="submitNetcashRegistration"
                        class="o_btn_primarys">Add
                        new folder <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor"
                          class="inline-block ltr:ml-1 rtl:mr-1 bi bi-plus-lg" viewBox="0 0 16 16">
                          <path
                            d="M8 0a1 1 0 0 1 1 1v6h6a1 1 0 1 1 0 2H9v6a1 1 0 1 1-2 0V9H1a1 1 0 0 1 0-2h6V1a1 1 0 0 1 1-1z">
                          </path>
                        </svg></button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>