<script setup lang="ts">
import {
  getDownloadURL,
  getStorage,
  ref,
  uploadBytesResumable,
} from "firebase/storage";

const propsAddImage = defineProps({
  accept: {
    type: String,
    default: "image/*",
  },
  label: {
    type: String,
    default: "",
  },
  multiple: {
    type: Boolean,
    default: true,
  },
  idUpload: {
    type: String,
    default: "image-upload",
  },
  idEdit: {
    type: String,
    default: "image-edit",
  },
  selectedTags: {
    type: Array,
    default: () => {
      return [];
    },
  },
  linked_id: {
    type: String,
    default: "",
  },
});

const { UploadsItems: filesSave } = useUploads();
const location: any = useBrowserLocation();

// const { UploadsItems } = useUploads()

const currentUser: any = useState("currentUser", () => {
  return {};
});
const { currentSpace } = space();
const { addToCollection } = database();
const curnt: any = getCurrentInstance();
const images = useState("files", () => []);
const uploadingNowFile = useState("uploadingNowFile", () => false);
const { selectedFolder } = useUploads();

function uploadFieldChange(e: {
  target: { files: any };
  dataTransfer: { files: any };
}) {
  console.log("e", e);

  const files = e.target.files || e.dataTransfer.files;
  console.log("FILES", files);
  if (!files.length) return false;

  createImage(files);
}
function onDrop(e) {
  this.isDragover = false;
  e.stopPropagation();
  e.preventDefault();
  const files = e.dataTransfer.files;
  if (!files.length) return false;

  createImage(files);
}

function createImage(files: any) {
  console.log("files create", files);
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    console.log("file", file);
    const reader = new FileReader();
    console.log("RED", reader);
    reader.onload = (e) => {
      console.log("reader", file.name);
      const image: any = {
        name: file.name,
        src: e.target.result,
        file,
        type: file.type,
        size: file.size,
      };
      console.log("image", image);
      images.value.push(image);
    };
    console.log("last file", file);
    reader.readAsDataURL(file);
  }
}

async function saveImage() {
  await fileUpload(images.value);
}

async function fileUpload(imgs: any[]) {
  uploadingNowFile.value = true;
  // TODO server side upload
  //  let result = await uploadToCollection(images.value, currentSpace.value.id, currentUser.value.id, propsAddImage.selectedTags)
  const storage = getStorage();
  // if (location.host === 'localhost:3000')
  //   connectStorageEmulator(storage, 'localhost', 9199)

  async function uploadImageToFire(file: {
    type: string;
    size: string;
    name: any;
    file: Blob | Uint8Array | ArrayBuffer;
  }) {
    // Create the file metadata
    /** @type {any} */
    const metadata = {
      contentType: file.type,
      name: file.name,
      space: currentSpace.value.id,
      uid: currentUser.value.id,
      tags: propsAddImage.selectedTags,
      size: file.size,
    };

    // Upload file and metadata to the object 'images/mountains.jpg'
    const uploadUrl = `${currentSpace.value.id}/${file.type}/${file.name}`;
    const storageRef = ref(storage, uploadUrl);
    const uploadTask = uploadBytesResumable(storageRef, file.file, metadata);
    console.log("storageRef", storageRef);
    console.log("uploadUrl", uploadUrl);
    // Listen for state changes, errors, and completion of the upload.
    uploadTask.on(
      "state_changed",
      (snapshot) => {
        // Get task progress, including the number of bytes uploaded and the total number of bytes to be uploaded
        const progress =
          (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
        console.log(`Upload is ${progress}% done`);
        switch (snapshot.state) {
          case "paused":
            console.log("Upload is paused");
            break;
          case "running":
            console.log("Upload is running");
            break;
        }
      },
      (error) => {
        // A full list of error codes is available at
        // https://firebase.google.com/docs/storage/web/handle-errors
        switch (error.code) {
          case "storage/unauthorized":
            // User doesn't have permission to access the object
            break;
          case "storage/canceled":
            // User canceled the upload
            break;

          // ...

          case "storage/unknown":
            // Unknown error occurred, inspect error.serverResponse
            // Upload completed successfully, now we can get the download URL
            getDownloadURL(uploadTask.snapshot.ref).then(
              async (downloadURL) => {
                // const ref = doc(db, "downloads", user.value.uid);
                console.log("metadata", metadata);
                const document = {
                  type: file.type,
                  src: downloadURL,
                  bUid: currentSpace.value.id ? currentSpace.value.id : null,
                  ...metadata,
                };

                try {
                  // addDoc(collection(firestoreDb, "uploads"), document).then(
                  //   () => {
                  //     images.value = [];
                  //   }
                  // );
                  const uploadResult = await addToCollection(
                    "uploads",
                    document
                  );
                  uploadingNowFile.value = false;
                  return uploadResult;
                } catch (e) {
                  alert("Error!");
                }
              }
            );
            break;
        }
      },
      () => {
        // Upload completed successfully, now we can get the download URL
        getDownloadURL(uploadTask.snapshot.ref).then(async (downloadURL) => {
          // const ref = doc(db, "downloads", user.value.uid);
          console.log("metadata", metadata);
          console.log("downloadURL", downloadURL);
          console.log("storageRef", storageRef);
          console.log("uploadUrl", uploadUrl);
          const shortType = file.type.split("/")[1];
          const document = {
            type: file.type,
            src: `${downloadURL}.${shortType}`,
            bUid: currentSpace.value.id ? currentSpace.value.id : null,
            origin: "upload",
            ...metadata,
            linked: [],
          };
          if (propsAddImage.linked_id) {
            document.linked.push(propsAddImage.linked_id);
          }
          if (selectedFolder.value.title)
            document.folders = selectedFolder.value.id;
          console.log("document", document);

          try {
            // addDoc(collection(firestoreDb, "uploads"), document).then(() => {
            //   images.value = [];
            // });

            const uploadResult = await addToCollection("uploads", document);
            curnt.emit("uploadAdd", uploadResult);
            console.log("uploadResult", uploadResult);
            filesSave.value.push(uploadResult);
            console.log("filesSave", filesSave.value);
            images.value = [];
            uploadingNowFile.value = false;
            return uploadResult;
            //  UploadsItems.value.push(addedContent)
          } catch (e) {
            alert("Error!");
          }
        });
      }
    );
  }
  for (let i = 0; i < imgs.length; i++) await uploadImageToFire(imgs[i]);
}

function deleteItem(index) {
  images.value.splice(index, 1);
}
</script>

<template>
  <div>
    <div class="">
      <div>
        <div class="mb-6 dropzone multiple-dropzone">
          <div class="">
            <label
              class="flex flex-col p-2 bg-base_100 rounded-xl hover:bg-gray-100 hover:border-gray-300"
            >
              <div class="flex flex-col items-center justify-center pt-7">
                <div class="flex items-center space-x-2">
                  <p>Drag and drop your file or</p>
                  <Icon name="mdi:upload" />
                  <p
                    class="pt-1 text-sm tracking-wider text-gray-400 group-hover:text-gray-600"
                  >
                    Select files
                  </p>
                </div>

                <p class="mt-2 text-xs opacity-50">
                  Max 2MB. Supported file formats: JPG, PNG, GIF, and SVG.
                </p>
              </div>
              <input
                :id="idUpload"
                label="Image upload"
                class="opacity-0"
                name="images"
                :multiple="multiple"
                :accept="accept"
                type="file"
                @change="uploadFieldChange"
              />
            </label>
          </div>
        </div>
      </div>

      <div class="flex flex-wrap">
        <div v-for="(img, ind) in images" :key="ind" class="relative mt-2">
          <Icon
            name="mdi:delete"
            class="float-right mr-2"
            @click="deleteItem(ind)"
          />
          <div
            class="flex items-center content-center justify-center h-64 mt-2 mr-2 overflow-hidden bg-gray-100 border rounded border-om_tertiary"
          >
            <document-type :doc="img" classes="w-48" />
            <!-- <input class="o_input_small" v-model="" /> -->
          </div>
        </div>
      </div>
      <div v-if="images.length > 0" class="p-2">
        <div v-if="uploadingNowFile" class="flex justify-center">
          <loading-colorrockets />
        </div>
        <button
          class="w-full rounded-lg shadow-xl o_gradient hover:bg-gray-100 hover:border-gray-300 text-base_100"
          @click="saveImage"
          v-if="!uploadingNowFile"
        >
          Upload
        </button>
      </div>
    </div>
  </div>
</template>
