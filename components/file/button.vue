<script setup lang="ts">
import { rand } from "@vueuse/core";
import {
  getDownloadURL,
  getStorage,
  ref,
  uploadBytesResumable,
} from "firebase/storage";

const propsAddImage = defineProps({
  accept: {
    type: String,
    default: "image/*",
  },
  label: {
    type: String,
    default: "",
  },
  multiple: {
    type: Boolean,
    default: true,
  },
  idUpload: {
    type: String,
    default: "image-upload",
  },
  idEdit: {
    type: String,
    default: "image-edit",
  },
  selectedTags: {
    type: Array,
    default: () => {
      return [];
    },
  },
  linked_id: {
    type: String,
    default: "",
  },
});

const { UploadsItems: filesSave } = useUploads();
const location: any = useBrowserLocation();

// const { UploadsItems } = useUploads()
const currentUser: any = useState("currentUser", () => {
  return {};
});
const { currentSpace } = space();

const curnt: any = getCurrentInstance();
const images = useState("files", () => []);
const imagesNow: any = useState("filesNow", () => []);

const uploadingNowFile = useState("uploadingNowFile", () => false);
const { selectedFolder } = useUploads();
onBeforeUnmount(() => {
  imagesNow.value = [];
});
function uploadFieldChange(e: {
  target: { files: any };
  dataTransfer: { files: any };
}) {
  const files = e.target.files || e.dataTransfer.files;
  if (!files.length) return false;

  createImage(files);
}
function onDrop(e: {
  stopPropagation: () => void;
  preventDefault: () => void;
  dataTransfer: { files: any };
}) {
  this.isDragover = false;
  e.stopPropagation();
  e.preventDefault();
  const files = e.dataTransfer.files;
  if (!files.length) return false;

  createImage(files);
}
const curp = ref([]);

function createImage(files: any) {
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    const reader = new FileReader();
    reader.onload = (e) => {
      const image: any = {
        name: file.name,
        src: e.target.result,
        file,
        type: file.type,
        size: file.size,
      };
      images.value.push(image);
    };
    reader.readAsDataURL(file);
  }
}

async function saveImage() {
  await fileUpload(images.value);
}

async function fileUpload(imgs: any[]) {
  imagesNow.value = [];
  uploadingNowFile.value = true;
  // TODO server side upload
  //  let result = await uploadToCollection(images.value, currentSpace.value.id, currentUser.value.id, propsAddImage.selectedTags)
  const storage = getStorage();
  // if (location.host === 'localhost:3000')
  //   connectStorageEmulator(storage, 'localhost', 9199)

  async function uploadImageToFire(file: {
    type: string;
    size: string;
    name: any;
    file: Blob | Uint8Array | ArrayBuffer;
  }) {
    // Create the file metadata
    /** @type {any} */
    const metadata = {
      contentType: file.type,
      name: file.name,
      space: currentSpace.value.id,
      uid: currentUser.value.id,
      tags: propsAddImage.selectedTags,
      size: file.size,
    };

    console.log("metadata", file.type, file.name, file.size, file);

    // Upload file and metadata to the object 'images/mountains.jpg'
    const uploadUrl = `${currentSpace.value.id}/${file.type}/${file.name}-${
      Math.random() * 200
    }`;
    const storageRef = ref(storage, uploadUrl);
    const uploadTask = uploadBytesResumable(storageRef, file.file, metadata);
    // Listen for state changes, errors, and completion of the upload.
    uploadTask.on(
      "state_changed",
      (snapshot) => {
        // Get task progress, including the number of bytes uploaded and the total number of bytes to be uploaded
        const progress =
          (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
        console.log(`Upload is ${progress}% done`);
        switch (snapshot.state) {
          case "paused":
            console.log("Upload is paused");
            break;
          case "running":
            console.log("Upload is running");
            break;
        }
      },
      (error) => {
        // A full list of error codes is available at
        // https://firebase.google.com/docs/storage/web/handle-errors
        switch (error.code) {
          case "storage/unauthorized":
            // User doesn't have permission to access the object
            break;
          case "storage/canceled":
            // User canceled the upload
            break;

          // ...

          case "storage/unknown":
            // Unknown error occurred, inspect error.serverResponse
            // Upload completed successfully, now we can get the download URL
            getDownloadURL(uploadTask.snapshot.ref).then(
              async (downloadURL) => {
                // const ref = doc(db, "downloads", user.value.uid);
                let document = {
                  type: file.type,
                  src: downloadURL,
                  bUid: currentSpace.value.id ? currentSpace.value.id : null,
                  ...metadata,
                };

                try {
                  // addDoc(collection(firestoreDb, "uploads"), document).then(
                  //   () => {
                  //     images.value = [];
                  //   }
                  // );
                  document = removeUndefined(document);
                  console.log("document", document);
                  const uploadResult = await add("uploads", document);
                  uploadingNowFile.value = false;
                  return uploadResult;
                } catch (e) {
                  alert("Error!");
                }
              }
            );
            break;
        }
      },
      () => {
        // Upload completed successfully, now we can get the download URL
        getDownloadURL(uploadTask.snapshot.ref).then(async (downloadURL) => {
          // const ref = doc(db, "downloads", user.value.uid);
          const shortType = file.type.split("/")[1];
          console.log("metadata", metadata);
          console.log("downloadURL", downloadURL);
          console.log("shortType", shortType);
          let document: any = {
            type: file.type,
            src: `${downloadURL}.${shortType}`,
            bUid: currentSpace.value.id ? currentSpace.value.id : null,
            origin: "upload",
            ...metadata,
            linked: [],
          };
          if (propsAddImage.linked_id) {
            document.linked.push(propsAddImage.linked_id);
          }
          if (selectedFolder.value.title)
            document.folders = selectedFolder.value.id;

          try {
            document = removeUndefined(document);

            const uploadResult: any = await add("uploads", document);
            let doc = await queryById("uploads", uploadResult);
            console.log("doc", doc);
            getExtacted(
              "gs://covalonic-959ba.appspot.com/" + uploadUrl,
              doc.id,
              doc
            );

            return uploadResult;
            //  UploadsItems.value.push(addedContent)
          } catch (e) {
            console.log("error", e);
            alert("Error!");
          }
        });
      }
    );
  }
  for (let i = 0; i < imgs.length; i++) await uploadImageToFire(imgs[i]);
}

const getExtacted = async (file: string, id: string, data: any) => {
  setTimeout(async () => {
    try {
      // Query the extractedText collection for the uploaded file
      let doc = await queryByWhere("extractedText", "file", "==", file);
      console.log("Extracted text document:", doc);

      if (!doc || doc.length === 0) {
        console.error("No extracted text found for file:", file);
        // If no extracted text is found, use a fallback or empty string
        let uploadResult = {
          ...data,
          text: "No text could be extracted. Please manually enter the information.",
        };
        curnt.emit("uploadAdd", uploadResult);
        filesSave.value.push(uploadResult);
        imagesNow.value.push(uploadResult);
        images.value = [];
        uploadingNowFile.value = false;
        return;
      }

      console.log("File ID:", id);
      const text = doc[0].text;
      console.log("Extracted text:", text);

      // Update the upload record with the extracted text
      let setD = await update("uploads", id, {
        text: text,
      });

      let uploadResult = {
        ...data,
        text: text,
      };

      console.log("Update result:", setD);
      curnt.emit("uploadAdd", uploadResult);
      filesSave.value.push(uploadResult);
      imagesNow.value.push(uploadResult);
      images.value = [];
      uploadingNowFile.value = false;
    } catch (error) {
      console.error("Error in text extraction process:", error);
      // Handle the error gracefully
      let uploadResult = {
        ...data,
        text: "Error extracting text. Please manually enter the information.",
      };
      curnt.emit("uploadAdd", uploadResult);
      filesSave.value.push(uploadResult);
      imagesNow.value.push(uploadResult);
      images.value = [];
      uploadingNowFile.value = false;
    }
  }, 2000);
};

function deleteItem(index: number) {
  images.value.splice(index, 1);
}

function cropImage(img: any) {
  console.log("img", img);
  console.log("images.value[0].file", images.value[0].file);

  images.value[0].src = img;
  images.value[0].file.src = img;
}
</script>

<template>
  <div>
    <div>
      <div>
        <div class="mb-4 dropzone multiple-dropzone">
          <div>
            <div class="flex flex-col p-4 bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border border-blue-200 dark:border-blue-800 rounded-lg shadow-sm">
              <div class="flex flex-col items-center justify-center py-4">
                <Icon name="mdi:cloud-upload" class="text-blue-600 dark:text-blue-400 w-10 h-10 mb-2" />
                <div class="flex items-center space-x-2">
                  <p class="text-gray-700 dark:text-gray-300 font-medium">Drag and drop your file or</p>
                  <label class="upload-button text-blue-600 dark:text-blue-400 font-medium underline cursor-pointer hover:text-blue-800 dark:hover:text-blue-300">
                    browse
                    <input
                      :id="idUpload"
                      label="Image upload"
                      class="hidden"
                      name="images"
                      :multiple="multiple"
                      :accept="accept"
                      type="file"
                      @change="uploadFieldChange"
                    />
                  </label>
                </div>

                <p class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                  Max 2MB. Supported file formats: JPG, PNG, GIF, and SVG.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="flex flex-wrap gap-3">
        <div v-for="(img, ind) in images" :key="ind" class="relative">
          <div class="bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-sm border border-gray-200 dark:border-gray-700">
            <div class="absolute top-2 right-2 z-10">
              <button
                @click="deleteItem(ind)"
                class="bg-red-100 dark:bg-red-900/50 text-red-600 dark:text-red-400 p-1 rounded-full hover:bg-red-200 dark:hover:bg-red-800 transition-colors"
              >
                <Icon name="mdi:delete" class="w-5 h-5" />
              </button>
            </div>

            <upload-cropper :src="img.src" @cropImage="cropImage" />

            <div class="p-2 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
              <p class="text-xs truncate text-gray-700 dark:text-gray-300">{{ img.name || 'Selected file' }}</p>
            </div>
          </div>
        </div>
      </div>

      <div v-if="images.length > 0" class="mt-4">
        <div v-if="uploadingNowFile" class="flex justify-center p-4">
          <loading-colorrockets />
        </div>
        <button
          v-else
          class="w-full py-2 rounded-md shadow-sm bg-blue-600 hover:bg-blue-700 text-white font-medium transition-colors duration-200 flex items-center justify-center"
          @click="saveImage"
        >
          <Icon name="mdi:cloud-upload" class="mr-2" />
          Upload File
        </button>
      </div>
    </div>
  </div>
</template>
