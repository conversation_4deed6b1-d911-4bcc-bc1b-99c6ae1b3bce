<template>
  <div class="modal-overlay fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="modal-content bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
      <!-- Modal Header -->
      <div class="modal-header flex items-center justify-between p-6 border-b border-gray-200">
        <h2 class="text-xl font-semibold text-gray-900">Create A/B Test</h2>
        <button 
          @click="$emit('close')"
          class="close-btn p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
        >
          <Icon name="x" class="w-5 h-5" />
        </button>
      </div>

      <!-- Modal Body -->
      <div class="modal-body p-6">
        <form @submit.prevent="createTest" class="space-y-6">
          <!-- Basic Information -->
          <div class="section">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Test Name *
                </label>
                <input
                  v-model="form.name"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="e.g., Header Color Test"
                />
              </div>
              
              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Base Business Card *
                </label>
                <select
                  v-model="form.baseCardId"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select a business card</option>
                  <option 
                    v-for="card in businessCards"
                    :key="card.id"
                    :value="card.id"
                  >
                    {{ card.name }}
                  </option>
                </select>
              </div>
            </div>
            
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                v-model="form.description"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Describe what you're testing and why..."
              ></textarea>
            </div>
          </div>

          <!-- Test Variants -->
          <div class="section">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-medium text-gray-900">Test Variants</h3>
              <button
                type="button"
                @click="addVariant"
                class="add-variant-btn bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm flex items-center gap-2 transition-colors"
              >
                <Icon name="plus" class="w-4 h-4" />
                Add Variant
              </button>
            </div>
            
            <div class="variants-list space-y-4">
              <div 
                v-for="(variant, index) in form.variants"
                :key="index"
                class="variant-card border border-gray-200 rounded-lg p-4"
              >
                <div class="variant-header flex items-center justify-between mb-4">
                  <h4 class="font-medium text-gray-900">
                    Variant {{ index + 1 }}
                    <span v-if="variant.isControl" class="control-badge ml-2 text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                      Control
                    </span>
                  </h4>
                  <button
                    v-if="!variant.isControl && form.variants.length > 2"
                    type="button"
                    @click="removeVariant(index)"
                    class="remove-variant-btn p-1 text-red-400 hover:text-red-600 rounded transition-colors"
                  >
                    <Icon name="trash" class="w-4 h-4" />
                  </button>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Name *
                    </label>
                    <input
                      v-model="variant.name"
                      type="text"
                      required
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      :placeholder="variant.isControl ? 'Control' : `Variant ${index + 1}`"
                    />
                  </div>
                  
                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Traffic % *
                    </label>
                    <input
                      v-model.number="variant.trafficPercentage"
                      type="number"
                      min="1"
                      max="100"
                      required
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  
                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Control Variant
                    </label>
                    <label class="flex items-center">
                      <input
                        v-model="variant.isControl"
                        type="checkbox"
                        @change="handleControlChange(index)"
                        class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span class="ml-2 text-sm text-gray-700">Set as control</span>
                    </label>
                  </div>
                </div>
                
                <div class="form-group mt-4">
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    v-model="variant.description"
                    rows="2"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Describe the changes in this variant..."
                  ></textarea>
                </div>
                
                <!-- Modifications -->
                <div class="modifications mt-4">
                  <div class="flex items-center justify-between mb-3">
                    <label class="text-sm font-medium text-gray-700">
                      Modifications
                    </label>
                    <button
                      type="button"
                      @click="addModification(index)"
                      class="add-modification-btn text-blue-600 hover:text-blue-700 text-sm flex items-center gap-1"
                    >
                      <Icon name="plus" class="w-3 h-3" />
                      Add Modification
                    </button>
                  </div>
                  
                  <div class="modifications-list space-y-2">
                    <div 
                      v-for="(modification, modIndex) in variant.modifications"
                      :key="modIndex"
                      class="modification-item flex items-center gap-2"
                    >
                      <select
                        v-model="modification.element"
                        class="flex-1 px-2 py-1 border border-gray-300 rounded text-sm"
                      >
                        <option value="">Select element</option>
                        <option value="background">Background</option>
                        <option value="text">Text</option>
                        <option value="button">Button</option>
                        <option value="layout">Layout</option>
                        <option value="image">Image</option>
                      </select>
                      
                      <select
                        v-model="modification.property"
                        class="flex-1 px-2 py-1 border border-gray-300 rounded text-sm"
                      >
                        <option value="">Select property</option>
                        <option value="color">Color</option>
                        <option value="size">Size</option>
                        <option value="position">Position</option>
                        <option value="content">Content</option>
                        <option value="style">Style</option>
                      </select>
                      
                      <input
                        v-model="modification.testValue"
                        type="text"
                        placeholder="New value"
                        class="flex-1 px-2 py-1 border border-gray-300 rounded text-sm"
                      />
                      
                      <button
                        type="button"
                        @click="removeModification(index, modIndex)"
                        class="p-1 text-red-400 hover:text-red-600 rounded transition-colors"
                      >
                        <Icon name="x" class="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Traffic Allocation Summary -->
            <div class="traffic-summary mt-4 p-4 bg-gray-50 rounded-lg">
              <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-700">Total Traffic Allocation:</span>
                <span 
                  :class="[
                    'text-sm font-semibold',
                    totalTrafficPercentage === 100 ? 'text-green-600' : 'text-red-600'
                  ]"
                >
                  {{ totalTrafficPercentage }}%
                </span>
              </div>
              <p v-if="totalTrafficPercentage !== 100" class="text-xs text-red-600 mt-1">
                Traffic allocation must equal 100%
              </p>
            </div>
          </div>

          <!-- Success Metrics -->
          <div class="section">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Success Metrics</h3>
            
            <div class="metrics-list space-y-3">
              <div 
                v-for="(metric, index) in form.successMetrics"
                :key="index"
                class="metric-item flex items-center gap-4 p-3 border border-gray-200 rounded-lg"
              >
                <div class="flex-1">
                  <select
                    v-model="metric.type"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="conversion">Conversion Rate</option>
                    <option value="engagement">Engagement Rate</option>
                    <option value="revenue">Revenue</option>
                    <option value="custom">Custom Metric</option>
                  </select>
                </div>
                
                <div class="flex items-center">
                  <label class="flex items-center">
                    <input
                      v-model="metric.isPrimary"
                      type="checkbox"
                      @change="handlePrimaryMetricChange(index)"
                      class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span class="ml-2 text-sm text-gray-700">Primary</span>
                  </label>
                </div>
                
                <button
                  v-if="form.successMetrics.length > 1"
                  type="button"
                  @click="removeSuccessMetric(index)"
                  class="p-1 text-red-400 hover:text-red-600 rounded transition-colors"
                >
                  <Icon name="trash" class="w-4 h-4" />
                </button>
              </div>
            </div>
            
            <button
              type="button"
              @click="addSuccessMetric"
              class="add-metric-btn mt-3 text-blue-600 hover:text-blue-700 text-sm flex items-center gap-1"
            >
              <Icon name="plus" class="w-3 h-3" />
              Add Success Metric
            </button>
          </div>

          <!-- Test Configuration -->
          <div class="section">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Test Configuration</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Minimum Sample Size
                </label>
                <input
                  v-model.number="form.minimumSampleSize"
                  type="number"
                  min="100"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Significance Level
                </label>
                <select
                  v-model="form.significanceLevel"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="0.05">95% (α = 0.05)</option>
                  <option value="0.01">99% (α = 0.01)</option>
                  <option value="0.10">90% (α = 0.10)</option>
                </select>
              </div>
              
              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Max Duration (days)
                </label>
                <input
                  v-model.number="form.maxDuration"
                  type="number"
                  min="1"
                  max="90"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            
            <div class="form-group mt-4">
              <label class="flex items-center">
                <input
                  v-model="form.autoStopEnabled"
                  type="checkbox"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span class="ml-2 text-sm text-gray-700">
                  Auto-stop test when statistically significant result is achieved
                </span>
              </label>
            </div>
          </div>
        </form>
      </div>

      <!-- Modal Footer -->
      <div class="modal-footer flex items-center justify-end gap-3 p-6 border-t border-gray-200">
        <button
          type="button"
          @click="$emit('close')"
          class="cancel-btn px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
        <button
          @click="createTest"
          :disabled="!isFormValid || isCreating"
          class="create-btn bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white px-6 py-2 rounded-lg transition-colors flex items-center gap-2"
        >
          <Icon 
            v-if="isCreating" 
            name="loader" 
            class="w-4 h-4 animate-spin" 
          />
          {{ isCreating ? 'Creating...' : 'Create Test' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Icon } from '#components'
import type { ABTestConfig, ABTestVariantConfig, SuccessMetric, CardModification } from '~/types/abTesting'

// Emits
const emit = defineEmits<{
  close: []
  created: [test: any]
}>()

// Dependencies
const { createABTest, isCreating } = useABTesting()

// Local state
const businessCards = ref<any[]>([])

// Form data
const form = ref<ABTestConfig>({
  name: '',
  description: '',
  baseCardId: '',
  variants: [
    {
      name: 'Control',
      description: 'Original version',
      trafficPercentage: 50,
      isControl: true,
      modifications: []
    },
    {
      name: 'Variant A',
      description: 'Test version',
      trafficPercentage: 50,
      isControl: false,
      modifications: []
    }
  ],
  successMetrics: [
    {
      id: 'conversion',
      name: 'Conversion Rate',
      type: 'conversion',
      description: 'Primary conversion metric',
      isPrimary: true,
      improvementThreshold: 5
    }
  ],
  minimumSampleSize: 1000,
  significanceLevel: 0.05,
  maxDuration: 30,
  autoStopEnabled: true
})

// Computed properties
const totalTrafficPercentage = computed(() => {
  return form.value.variants.reduce((sum, variant) => sum + variant.trafficPercentage, 0)
})

const isFormValid = computed(() => {
  return form.value.name.trim() !== '' &&
         form.value.baseCardId !== '' &&
         form.value.variants.length >= 2 &&
         totalTrafficPercentage.value === 100 &&
         form.value.successMetrics.some(m => m.isPrimary)
})

// Methods
const addVariant = () => {
  const variantNumber = form.value.variants.length + 1
  form.value.variants.push({
    name: `Variant ${String.fromCharCode(64 + variantNumber)}`,
    description: '',
    trafficPercentage: 0,
    isControl: false,
    modifications: []
  })
}

const removeVariant = (index: number) => {
  form.value.variants.splice(index, 1)
}

const handleControlChange = (index: number) => {
  if (form.value.variants[index].isControl) {
    // Ensure only one control variant
    form.value.variants.forEach((variant, i) => {
      if (i !== index) {
        variant.isControl = false
      }
    })
  }
}

const addModification = (variantIndex: number) => {
  form.value.variants[variantIndex].modifications.push({
    id: Date.now().toString(),
    element: '',
    property: '',
    originalValue: '',
    testValue: '',
    description: '',
    category: 'design'
  })
}

const removeModification = (variantIndex: number, modIndex: number) => {
  form.value.variants[variantIndex].modifications.splice(modIndex, 1)
}

const addSuccessMetric = () => {
  form.value.successMetrics.push({
    id: Date.now().toString(),
    name: 'New Metric',
    type: 'conversion',
    description: '',
    isPrimary: false,
    improvementThreshold: 5
  })
}

const removeSuccessMetric = (index: number) => {
  form.value.successMetrics.splice(index, 1)
}

const handlePrimaryMetricChange = (index: number) => {
  if (form.value.successMetrics[index].isPrimary) {
    // Ensure only one primary metric
    form.value.successMetrics.forEach((metric, i) => {
      if (i !== index) {
        metric.isPrimary = false
      }
    })
  }
}

const createTest = async () => {
  if (!isFormValid.value) return

  try {
    const test = await createABTest(form.value)
    emit('created', test)
  } catch (error) {
    console.error('Failed to create test:', error)
  }
}

const loadBusinessCards = async () => {
  // Load user's business cards
  // This would be implemented with actual API call
  businessCards.value = [
    { id: '1', name: 'Personal Card' },
    { id: '2', name: 'Business Card' },
    { id: '3', name: 'Creative Card' }
  ]
}

// Lifecycle
onMounted(() => {
  loadBusinessCards()
})
</script>

<style scoped>
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.modal-content {
  @apply bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto;
}

.modal-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200;
}

.modal-body {
  @apply p-6;
}

.modal-footer {
  @apply flex items-center justify-end gap-3 p-6 border-t border-gray-200;
}

.section {
  @apply space-y-4;
}

.form-group {
  @apply space-y-2;
}

.variant-card {
  @apply border border-gray-200 rounded-lg p-4;
}

.control-badge {
  @apply ml-2 text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded;
}

.traffic-summary {
  @apply mt-4 p-4 bg-gray-50 rounded-lg;
}

.metric-item {
  @apply flex items-center gap-4 p-3 border border-gray-200 rounded-lg;
}

.modifications {
  @apply mt-4;
}

.modification-item {
  @apply flex items-center gap-2;
}

/* Button styles */
.close-btn {
  @apply p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors;
}

.add-variant-btn {
  @apply bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm flex items-center gap-2 transition-colors;
}

.remove-variant-btn {
  @apply p-1 text-red-400 hover:text-red-600 rounded transition-colors;
}

.add-modification-btn {
  @apply text-blue-600 hover:text-blue-700 text-sm flex items-center gap-1;
}

.add-metric-btn {
  @apply mt-3 text-blue-600 hover:text-blue-700 text-sm flex items-center gap-1;
}

.cancel-btn {
  @apply px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors;
}

.create-btn {
  @apply bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white px-6 py-2 rounded-lg transition-colors flex items-center gap-2;
}

.create-btn:disabled {
  @apply cursor-not-allowed;
}
</style>
