<template>
  <div v-if="isVisible" class="modal-overlay" @click="$emit('close')">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h3>Alert Details</h3>
        <button @click="$emit('close')" class="close-btn">
          <Icon name="x" />
        </button>
      </div>
      
      <div class="modal-body" v-if="alert">
        <div class="alert-overview">
          <div class="alert-status" :class="getStatusClass(alert.severity)">
            <Icon :name="getStatusIcon(alert.severity)" />
            <span>{{ alert.severity }} Priority</span>
          </div>
          <div class="alert-timestamp">
            {{ formatDate(alert.createdAt) }}
          </div>
        </div>
        
        <div class="alert-content">
          <h4>{{ alert.title }}</h4>
          <p class="alert-description">{{ alert.description }}</p>
        </div>
        
        <div class="alert-metrics" v-if="alert.metrics">
          <h5>Related Metrics</h5>
          <div class="metrics-grid">
            <div v-for="metric in alert.metrics" :key="metric.name" class="metric-item">
              <div class="metric-label">{{ metric.name }}</div>
              <div class="metric-value" :class="getMetricClass(metric.trend)">
                {{ formatMetricValue(metric.value, metric.type) }}
                <Icon :name="getTrendIcon(metric.trend)" size="12" />
              </div>
            </div>
          </div>
        </div>
        
        <div class="alert-recommendations" v-if="alert.recommendations">
          <h5>Recommended Actions</h5>
          <ul class="recommendations-list">
            <li v-for="recommendation in alert.recommendations" :key="recommendation">
              {{ recommendation }}
            </li>
          </ul>
        </div>
        
        <div class="alert-history" v-if="alert.history && alert.history.length > 0">
          <h5>Alert History</h5>
          <div class="history-timeline">
            <div v-for="event in alert.history" :key="event.id" class="history-item">
              <div class="history-timestamp">{{ formatDate(event.timestamp) }}</div>
              <div class="history-event">{{ event.description }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="modal-footer">
        <button @click="markAsResolved" class="btn-primary" :disabled="alert?.isResolved">
          {{ alert?.isResolved ? 'Resolved' : 'Mark as Resolved' }}
        </button>
        <button @click="snoozeAlert" class="btn-secondary">Snooze</button>
        <button @click="$emit('close')" class="btn-secondary">Close</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface AlertMetric {
  name: string
  value: number
  type: 'percentage' | 'number' | 'currency'
  trend: 'up' | 'down' | 'neutral'
}

interface AlertHistoryEvent {
  id: string
  timestamp: Date
  description: string
}

interface Alert {
  id: string
  title: string
  description: string
  severity: 'high' | 'medium' | 'low'
  createdAt: Date
  isResolved?: boolean
  metrics?: AlertMetric[]
  recommendations?: string[]
  history?: AlertHistoryEvent[]
}

interface Props {
  isVisible: boolean
  alert: Alert | null
}

interface Emits {
  (e: 'close'): void
  (e: 'resolve', alertId: string): void
  (e: 'snooze', alertId: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const getStatusClass = (severity: string): string => {
  return `status-${severity}`
}

const getStatusIcon = (severity: string): string => {
  const iconMap = {
    'high': 'alert-triangle',
    'medium': 'alert-circle',
    'low': 'info'
  }
  return iconMap[severity] || 'info'
}

const getMetricClass = (trend: string): string => {
  return `trend-${trend}`
}

const getTrendIcon = (trend: string): string => {
  const iconMap = {
    'up': 'trending-up',
    'down': 'trending-down',
    'neutral': 'minus'
  }
  return iconMap[trend] || 'minus'
}

const formatDate = (date: Date): string => {
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatMetricValue = (value: number, type: string): string => {
  switch (type) {
    case 'percentage':
      return `${Math.round(value * 100)}%`
    case 'currency':
      return `$${value.toLocaleString()}`
    default:
      return value.toLocaleString()
  }
}

const markAsResolved = () => {
  if (props.alert) {
    emit('resolve', props.alert.id)
  }
}

const snoozeAlert = () => {
  if (props.alert) {
    emit('snooze', props.alert.id)
  }
}
</script>

<style scoped>
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.modal-content {
  @apply bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-screen overflow-y-auto;
}

.modal-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200;
}

.modal-header h3 {
  @apply text-lg font-semibold text-gray-900;
}

.close-btn {
  @apply text-gray-400 hover:text-gray-600 transition-colors;
}

.modal-body {
  @apply p-6 space-y-6;
}

.alert-overview {
  @apply flex items-center justify-between;
}

.alert-status {
  @apply flex items-center space-x-2 px-3 py-1 rounded-full text-sm font-medium;
}

.alert-status.status-high {
  @apply bg-red-100 text-red-800;
}

.alert-status.status-medium {
  @apply bg-yellow-100 text-yellow-800;
}

.alert-status.status-low {
  @apply bg-blue-100 text-blue-800;
}

.alert-timestamp {
  @apply text-sm text-gray-500;
}

.alert-content h4 {
  @apply font-semibold text-gray-900 mb-2;
}

.alert-description {
  @apply text-gray-700;
}

.alert-metrics h5,
.alert-recommendations h5,
.alert-history h5 {
  @apply font-medium text-gray-900 mb-3;
}

.metrics-grid {
  @apply grid grid-cols-2 gap-4;
}

.metric-item {
  @apply bg-gray-50 p-3 rounded-lg;
}

.metric-label {
  @apply text-sm text-gray-600 mb-1;
}

.metric-value {
  @apply flex items-center space-x-1 font-semibold;
}

.metric-value.trend-up {
  @apply text-green-600;
}

.metric-value.trend-down {
  @apply text-red-600;
}

.metric-value.trend-neutral {
  @apply text-gray-600;
}

.recommendations-list {
  @apply list-disc pl-5 space-y-1;
}

.recommendations-list li {
  @apply text-gray-700;
}

.history-timeline {
  @apply space-y-3;
}

.history-item {
  @apply flex items-start space-x-3 p-3 bg-gray-50 rounded-lg;
}

.history-timestamp {
  @apply text-xs text-gray-500 whitespace-nowrap;
}

.history-event {
  @apply text-sm text-gray-700;
}

.modal-footer {
  @apply flex items-center justify-end space-x-3 p-6 border-t border-gray-200;
}

.btn-secondary {
  @apply px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors;
}

.btn-primary {
  @apply px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors;
}
</style>