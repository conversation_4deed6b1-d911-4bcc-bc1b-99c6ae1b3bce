<template>
  <div v-if="isVisible" class="modal-overlay" @click="$emit('close')">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h3>Dashboard Settings</h3>
        <button @click="$emit('close')" class="close-btn">
          <Icon name="x" />
        </button>
      </div>
      
      <div class="modal-body">
        <div class="settings-section">
          <h4>Display Options</h4>
          <div class="setting-item">
            <label class="setting-label">
              <input 
                type="checkbox" 
                :checked="settings.showRealtimeUpdates"
                @change="updateSetting('showRealtimeUpdates', $event.target.checked)"
              />
              Enable realtime updates
            </label>
          </div>
          
          <div class="setting-item">
            <label class="setting-label">
              <input 
                type="checkbox" 
                :checked="settings.showTrendIndicators"
                @change="updateSetting('showTrendIndicators', $event.target.checked)"
              />
              Show trend indicators
            </label>
          </div>
          
          <div class="setting-item">
            <label class="setting-label">
              Auto-refresh interval (seconds)
            </label>
            <select 
              :value="settings.refreshInterval"
              @change="updateSetting('refreshInterval', parseInt($event.target.value))"
              class="setting-select"
            >
              <option value="30">30 seconds</option>
              <option value="60">1 minute</option>
              <option value="300">5 minutes</option>
              <option value="600">10 minutes</option>
            </select>
          </div>
        </div>
        
        <div class="settings-section">
          <h4>Notifications</h4>
          <div class="setting-item">
            <label class="setting-label">
              <input 
                type="checkbox" 
                :checked="settings.enableAlerts"
                @change="updateSetting('enableAlerts', $event.target.checked)"
              />
              Enable engagement alerts
            </label>
          </div>
          
          <div class="setting-item">
            <label class="setting-label">
              <input 
                type="checkbox" 
                :checked="settings.enableChurnAlerts"
                @change="updateSetting('enableChurnAlerts', $event.target.checked)"
              />
              Enable churn risk alerts
            </label>
          </div>
        </div>
      </div>
      
      <div class="modal-footer">
        <button @click="$emit('close')" class="btn-secondary">Cancel</button>
        <button @click="saveSettings" class="btn-primary">Save Changes</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

interface DashboardSettings {
  showRealtimeUpdates: boolean
  showTrendIndicators: boolean
  refreshInterval: number
  enableAlerts: boolean
  enableChurnAlerts: boolean
}

interface Props {
  isVisible: boolean
  initialSettings: DashboardSettings
}

interface Emits {
  (e: 'close'): void
  (e: 'save', settings: DashboardSettings): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const settings = ref<DashboardSettings>({ ...props.initialSettings })

watch(() => props.initialSettings, (newSettings) => {
  settings.value = { ...newSettings }
}, { deep: true })

const updateSetting = (key: keyof DashboardSettings, value: any) => {
  settings.value[key] = value
}

const saveSettings = () => {
  emit('save', settings.value)
  emit('close')
}
</script>

<style scoped>
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.modal-content {
  @apply bg-white rounded-lg shadow-xl max-w-md w-full mx-4;
}

.modal-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200;
}

.modal-header h3 {
  @apply text-lg font-semibold text-gray-900;
}

.close-btn {
  @apply text-gray-400 hover:text-gray-600 transition-colors;
}

.modal-body {
  @apply p-6 space-y-6;
}

.settings-section {
  @apply space-y-4;
}

.settings-section h4 {
  @apply font-medium text-gray-900 border-b border-gray-200 pb-2;
}

.setting-item {
  @apply space-y-2;
}

.setting-label {
  @apply flex items-center space-x-2 text-sm text-gray-700;
}

.setting-select {
  @apply mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500;
}

.modal-footer {
  @apply flex items-center justify-end space-x-3 p-6 border-t border-gray-200;
}

.btn-secondary {
  @apply px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors;
}

.btn-primary {
  @apply px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 transition-colors;
}
</style>