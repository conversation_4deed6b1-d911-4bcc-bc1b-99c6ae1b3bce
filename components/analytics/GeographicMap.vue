<template>
  <div class="geographic-map">
    <div class="map-container" ref="mapContainer"></div>
    
    <div class="map-controls">
      <div class="flex space-x-2 mb-4">
        <button 
          v-for="type in eventTypes" 
          :key="type.value"
          @click="setEventType(type.value)"
          class="px-3 py-1 text-sm rounded-md" 
          :class="activeEventType === type.value ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'"
        >
          {{ type.label }}
        </button>
      </div>
      
      <div class="flex space-x-2">
        <button 
          v-for="view in viewTypes" 
          :key="view.value"
          @click="setViewType(view.value)"
          class="px-3 py-1 text-sm rounded-md" 
          :class="activeViewType === view.value ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'"
        >
          {{ view.label }}
        </button>
      </div>
    </div>
    
    <div v-if="topLocations.length > 0" class="top-locations mt-4">
      <h3 class="text-sm font-medium text-gray-700 mb-2">Top Locations</h3>
      <ul class="bg-white rounded-md shadow divide-y divide-gray-200">
        <li v-for="(location, index) in topLocations" :key="index" class="px-4 py-3">
          <div class="flex justify-between">
            <span class="text-sm font-medium text-gray-900">{{ location.country }}</span>
            <span class="text-sm text-gray-500">{{ location.count }} interactions</span>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import { useGeographicAnalytics } from '~/composables/useGeographicAnalytics'

// Props
const props = defineProps({
  cardId: {
    type: String,
    default: undefined
  }
})

// State
const mapContainer = ref<HTMLElement | null>(null)
const map = ref<any>(null)
const heatmapLayer = ref<any>(null)
const markerLayer = ref<any>(null)
const activeEventType = ref<string | null>(null)
const activeViewType = ref<string>('heatmap')

// Event types for filtering
const eventTypes = [
  { label: 'All', value: null },
  { label: 'Views', value: 'view' },
  { label: 'Downloads', value: 'download' },
  { label: 'QR Scans', value: 'qr_scan' },
  { label: 'Contact Actions', value: 'contact_action' }
]

// View types for display
const viewTypes = [
  { label: 'Heatmap', value: 'heatmap' },
  { label: 'Markers', value: 'markers' }
]

// Geographic analytics composable
const { 
  isLoading, 
  error, 
  geoPoints, 
  locationData,
  fetchGeoData, 
  getHeatmapData, 
  getMarkerData,
  getTopLocations
} = useGeographicAnalytics()

// Computed properties
const topLocations = computed(() => getTopLocations(5))

// Methods
const initMap = async () => {
  if (!process.client || !mapContainer.value) return
  
  try {
    // Dynamically import Leaflet (client-side only)
    const L = await import('leaflet')
    
    // Initialize map
    map.value = L.map(mapContainer.value).setView([20, 0], 2)
    
    // Add tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map.value)
    
    // Initialize layers (will be populated later)
    initLayers()
  } catch (err) {
    console.error('Error initializing map:', err)
  }
}

const initLayers = async () => {
  if (!map.value) return
  
  try {
    // Dynamically import Leaflet.heat and Leaflet.markercluster
    const L = await import('leaflet')
    const { HeatLayer } = await import('leaflet.heat')
    const { MarkerClusterGroup } = await import('leaflet.markercluster')
    
    // Initialize heatmap layer
    heatmapLayer.value = new HeatLayer([], {
      radius: 25,
      blur: 15,
      maxZoom: 10
    }).addTo(map.value)
    
    // Initialize marker cluster layer
    markerLayer.value = new MarkerClusterGroup().addTo(map.value)
    
    // Update layers with data
    updateLayers()
  } catch (err) {
    console.error('Error initializing layers:', err)
  }
}

const updateLayers = async () => {
  if (!map.value || !heatmapLayer.value || !markerLayer.value) return
  
  try {
    const L = await import('leaflet')
    
    // Get data based on active event type
    const heatmapData = getHeatmapData(activeEventType.value || undefined)
    const markerData = getMarkerData(activeEventType.value || undefined)
    
    // Update heatmap layer
    heatmapLayer.value.setLatLngs(heatmapData)
    
    // Clear and update marker layer
    markerLayer.value.clearLayers()
    
    // Add markers
    markerData.forEach((point: any) => {
      const marker = L.marker([point.lat, point.lng])
      
      // Add popup with information
      marker.bindPopup(`
        <div>
          <strong>Event Type:</strong> ${point.type}<br>
          <strong>Date:</strong> ${point.timestamp ? new Date(point.timestamp).toLocaleString() : 'Unknown'}<br>
          ${point.cardId ? `<strong>Card ID:</strong> ${point.cardId}<br>` : ''}
        </div>
      `)
      
      markerLayer.value.addLayer(marker)
    })
    
    // Show/hide layers based on active view type
    if (activeViewType.value === 'heatmap') {
      map.value.addLayer(heatmapLayer.value)
      map.value.removeLayer(markerLayer.value)
    } else {
      map.value.removeLayer(heatmapLayer.value)
      map.value.addLayer(markerLayer.value)
    }
  } catch (err) {
    console.error('Error updating layers:', err)
  }
}

const setEventType = (type: string | null) => {
  activeEventType.value = type
  updateLayers()
}

const setViewType = (type: string) => {
  activeViewType.value = type
  updateLayers()
}

// Watch for changes in geoPoints
watch(geoPoints, () => {
  updateLayers()
})

// Lifecycle hooks
onMounted(async () => {
  // Fetch geographic data
  await fetchGeoData(props.cardId)
  
  // Initialize map
  initMap()
})

onUnmounted(() => {
  // Clean up map
  if (map.value) {
    map.value.remove()
    map.value = null
  }
})
</script>

<style>
/* Import Leaflet CSS */
@import 'leaflet/dist/leaflet.css';
@import 'leaflet.markercluster/dist/MarkerCluster.css';
@import 'leaflet.markercluster/dist/MarkerCluster.Default.css';

.geographic-map {
  @apply bg-white rounded-lg shadow p-4;
}

.map-container {
  height: 400px;
  width: 100%;
  @apply rounded-lg overflow-hidden mb-4;
}

.map-controls {
  @apply flex flex-col;
}

@media (min-width: 768px) {
  .map-controls {
    @apply flex-row justify-between items-center;
  }
}
</style>
