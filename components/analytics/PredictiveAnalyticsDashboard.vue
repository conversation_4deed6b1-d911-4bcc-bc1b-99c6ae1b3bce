<template>
  <div class="predictive-analytics-dashboard">
    <!-- Dashboard Header -->
    <div class="dashboard-header mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-2xl font-bold text-gray-900">Predictive Analytics</h2>
          <p class="text-gray-600 mt-1">AI-powered insights and forecasting for your business cards</p>
        </div>
        <div class="refresh-controls">
          <button 
            @click="refreshPredictions" 
            :disabled="isRefreshing"
            class="refresh-btn bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
          >
            <Icon 
              name="refresh" 
              :class="{ 'animate-spin': isRefreshing }" 
              class="w-4 h-4"
            />
            {{ isRefreshing ? 'Refreshing...' : 'Refresh Predictions' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex items-center justify-center py-12">
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
        <p class="text-gray-600 mt-4">Loading predictive analytics...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
      <div class="flex items-center">
        <Icon name="alert-circle" class="w-5 h-5 text-red-500 mr-2" />
        <span class="text-red-700">{{ error }}</span>
      </div>
    </div>

    <!-- Analytics Grid -->
    <div v-else class="analytics-grid grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Engagement Forecast -->
      <div class="analytics-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">Engagement Forecast</h3>
          <select 
            v-model="selectedTimeframe" 
            @change="updateTimeframe(selectedTimeframe)"
            class="text-sm border border-gray-300 rounded px-3 py-1"
          >
            <option value="30d">30 Days</option>
            <option value="60d">60 Days</option>
            <option value="90d">90 Days</option>
          </select>
        </div>
        
        <EngagementForecastChart 
          :predictions="engagementPredictions"
          :timeframe="selectedTimeframe"
          class="mb-4"
        />
        
        <div class="forecast-summary bg-gray-50 rounded-lg p-4">
          <div class="metric flex items-center justify-between">
            <span class="label text-gray-600">Next {{ selectedTimeframe }}</span>
            <div class="text-right">
              <span class="value text-xl font-bold text-blue-600">
                {{ formatPrediction(next30DaysPrediction) }}
              </span>
              <div class="confidence text-sm text-gray-500">
                {{ next30DaysConfidence }}% confidence
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Optimal Sharing Times -->
      <div class="analytics-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Optimal Sharing Times</h3>
        
        <SharingTimeHeatmap 
          :recommendations="sharingRecommendations" 
          class="mb-4"
        />
        
        <div class="recommendations-list space-y-2">
          <div 
            v-for="rec in topRecommendations" 
            :key="rec.id"
            class="recommendation-item flex items-center justify-between p-3 bg-gray-50 rounded-lg"
          >
            <div class="time-slot font-medium text-gray-900">
              {{ formatTimeSlot(rec) }}
            </div>
            <div class="predicted-lift text-green-600 font-semibold">
              +{{ rec.predictedLift }}% engagement
            </div>
          </div>
        </div>
      </div>

      <!-- Trend Analysis -->
      <div class="analytics-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Performance Trends</h3>
        
        <TrendAnalysisChart 
          :trends="performanceTrends"
          :showSignificance="true"
          class="mb-4"
        />
        
        <div class="trend-insights space-y-2">
          <div 
            v-for="insight in trendInsights" 
            :key="insight.metric"
            class="insight-item flex items-center gap-3 p-2"
          >
            <Icon 
              :name="insight.trendIcon" 
              :class="insight.trendClass" 
              class="w-5 h-5"
            />
            <span class="insight-text text-gray-700">{{ insight.description }}</span>
          </div>
        </div>
      </div>

      <!-- Industry Benchmarks -->
      <div class="analytics-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Industry Benchmarks</h3>
        
        <BenchmarkComparisonChart 
          :userMetrics="userMetrics"
          :industryBenchmarks="industryBenchmarks"
          :industry="userIndustry"
          class="mb-4"
        />
        
        <div class="benchmark-summary space-y-2">
          <div 
            v-for="comparison in benchmarkComparisons"
            :key="comparison.metric"
            class="comparison-item flex items-center justify-between p-2"
          >
            <span class="metric-name text-gray-700 capitalize">
              {{ comparison.metric.replace('_', ' ') }}
            </span>
            <span 
              :class="['performance-indicator font-semibold', comparison.performanceClass]"
            >
              {{ comparison.performanceText }}
            </span>
          </div>
        </div>
      </div>

      <!-- Growth Projections -->
      <div class="analytics-card full-width lg:col-span-2 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Growth Projections</h3>
        
        <GrowthProjectionChart 
          :scenarios="growthScenarios"
          :selectedScenario="selectedGrowthScenario"
          class="mb-4"
        />
        
        <div class="projection-controls">
          <div class="scenario-selector flex gap-2">
            <button 
              v-for="scenario in growthScenarios"
              :key="scenario.name"
              @click="updateGrowthScenario(scenario.name)"
              :class="[
                'scenario-btn px-4 py-2 rounded-lg text-sm font-medium transition-colors',
                selectedGrowthScenario === scenario.name 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              ]"
            >
              {{ scenario.name.charAt(0).toUpperCase() + scenario.name.slice(1) }}
            </button>
          </div>
        </div>
      </div>

      <!-- Anomaly Alerts -->
      <div 
        v-if="anomalies.length" 
        class="analytics-card anomaly-alerts lg:col-span-2 bg-white rounded-lg shadow-sm border border-gray-200 p-6"
      >
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <Icon name="alert-triangle" class="w-5 h-5 text-orange-500" />
          Recent Anomalies
        </h3>
        
        <div class="anomaly-list space-y-4">
          <div 
            v-for="anomaly in anomalies"
            :key="anomaly.id"
            class="anomaly-item border border-orange-200 rounded-lg p-4 bg-orange-50"
          >
            <div class="anomaly-header flex items-center justify-between mb-2">
              <div class="flex items-center gap-2">
                <Icon 
                  name="alert-triangle" 
                  :class="[
                    'anomaly-icon w-4 h-4',
                    anomaly.severity === 'critical' ? 'text-red-500' :
                    anomaly.severity === 'high' ? 'text-orange-500' :
                    'text-yellow-500'
                  ]"
                />
                <span class="anomaly-title font-semibold text-gray-900">
                  {{ anomaly.title }}
                </span>
              </div>
              <span class="anomaly-time text-sm text-gray-500">
                {{ formatRelativeTime(anomaly.detectedAt) }}
              </span>
            </div>
            
            <p class="anomaly-description text-gray-700 mb-3">
              {{ anomaly.description }}
            </p>
            
            <div class="anomaly-actions flex gap-2">
              <button 
                @click="investigateAnomaly(anomaly)" 
                class="investigate-btn bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors"
              >
                Investigate
              </button>
              <button 
                @click="dismissAnomaly(anomaly)" 
                class="dismiss-btn bg-gray-300 hover:bg-gray-400 text-gray-700 px-3 py-1 rounded text-sm transition-colors"
              >
                Dismiss
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Predictive Insights -->
      <div 
        v-if="predictiveInsights.length"
        class="analytics-card lg:col-span-2 bg-white rounded-lg shadow-sm border border-gray-200 p-6"
      >
        <h3 class="text-lg font-semibold text-gray-900 mb-4">AI Insights</h3>
        
        <div class="insights-list space-y-3">
          <div 
            v-for="insight in predictiveInsights.slice(0, 5)"
            :key="insight.id"
            class="insight-item p-4 border border-gray-200 rounded-lg"
          >
            <div class="flex items-start justify-between mb-2">
              <h4 class="font-semibold text-gray-900">{{ insight.title }}</h4>
              <span 
                :class="[
                  'impact-badge px-2 py-1 rounded text-xs font-medium',
                  insight.impact === 'high' ? 'bg-red-100 text-red-700' :
                  insight.impact === 'medium' ? 'bg-yellow-100 text-yellow-700' :
                  'bg-green-100 text-green-700'
                ]"
              >
                {{ insight.impact }} impact
              </span>
            </div>
            <p class="text-gray-700 text-sm">{{ insight.description }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Icon } from '#components'

// Import chart components (these would need to be created)
import EngagementForecastChart from './charts/EngagementForecastChart.vue'
import SharingTimeHeatmap from './charts/SharingTimeHeatmap.vue'
import TrendAnalysisChart from './charts/TrendAnalysisChart.vue'
import BenchmarkComparisonChart from './charts/BenchmarkComparisonChart.vue'
import GrowthProjectionChart from './charts/GrowthProjectionChart.vue'

// Use the predictive analytics composable
const {
  // State
  isLoading,
  error,
  isRefreshing,
  
  // Data
  engagementPredictions,
  sharingRecommendations,
  performanceTrends,
  anomalies,
  growthScenarios,
  industryBenchmarks,
  benchmarkComparisons,
  predictiveInsights,
  
  // Configuration
  selectedTimeframe,
  selectedGrowthScenario,
  userIndustry,
  
  // Computed
  next30DaysPrediction,
  next30DaysConfidence,
  topRecommendations,
  trendInsights,
  userMetrics,
  
  // Methods
  refreshAnalyticsPredictions: refreshPredictions,
  updateTimeframe,
  updateGrowthScenario,
  dismissAnomaly,
  investigateAnomaly,
  formatPrediction,
  formatTimeSlot,
  formatRelativeTime
} = usePredictiveAnalytics()
</script>

<style scoped>
.predictive-analytics-dashboard {
  @apply max-w-7xl mx-auto;
}

.analytics-grid {
  @apply grid-cols-1 lg:grid-cols-2 gap-6;
}

.analytics-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.full-width {
  @apply lg:col-span-2;
}

.refresh-btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.scenario-btn.active {
  @apply bg-blue-600 text-white;
}

.anomaly-alerts {
  @apply border-orange-200 bg-orange-50;
}

.insight-item {
  @apply transition-all duration-200 hover:shadow-sm;
}
</style>
