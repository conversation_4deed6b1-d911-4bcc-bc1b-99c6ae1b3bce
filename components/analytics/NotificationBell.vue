<template>
  <div class="notification-bell relative">
    <button 
      @click="toggleDropdown" 
      class="relative p-2 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
    >
      <svg 
        xmlns="http://www.w3.org/2000/svg" 
        class="h-6 w-6 text-gray-600" 
        fill="none" 
        viewBox="0 0 24 24" 
        stroke="currentColor"
      >
        <path 
          stroke-linecap="round" 
          stroke-linejoin="round" 
          stroke-width="2" 
          d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" 
        />
      </svg>
      
      <!-- Unread Badge -->
      <span 
        v-if="unreadCount > 0" 
        class="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full"
      >
        {{ unreadCount > 9 ? '9+' : unreadCount }}
      </span>
    </button>
    
    <!-- Notification Dropdown -->
    <div 
      v-if="isOpen" 
      class="notification-dropdown absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg z-50 overflow-hidden"
    >
      <div class="p-3 border-b border-gray-200 flex justify-between items-center">
        <h3 class="text-sm font-medium text-gray-700">Notifications</h3>
        <button 
          v-if="unreadCount > 0"
          @click="markAllAsRead" 
          class="text-xs text-blue-600 hover:text-blue-800"
        >
          Mark all as read
        </button>
      </div>
      
      <!-- Loading State -->
      <div v-if="isLoading" class="flex justify-center items-center py-6">
        <div class="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-600"></div>
      </div>
      
      <!-- Error State -->
      <div v-else-if="error" class="p-3 text-sm text-red-600">
        {{ error }}
      </div>
      
      <!-- Empty State -->
      <div v-else-if="notifications.length === 0" class="p-6 text-center">
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          class="h-10 w-10 mx-auto text-gray-400 mb-2" 
          fill="none" 
          viewBox="0 0 24 24" 
          stroke="currentColor"
        >
          <path 
            stroke-linecap="round" 
            stroke-linejoin="round" 
            stroke-width="2" 
            d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" 
          />
        </svg>
        <p class="text-sm text-gray-500">No notifications yet</p>
      </div>
      
      <!-- Notification List -->
      <div v-else class="max-h-80 overflow-y-auto">
        <div 
          v-for="notification in notifications.slice(0, 5)" 
          :key="notification.id" 
          class="notification-item p-3 border-b border-gray-200 hover:bg-gray-50 cursor-pointer"
          :class="{ 'bg-blue-50': !notification.read }"
          @click="viewNotification(notification)"
        >
          <div class="flex items-start">
            <!-- Notification Icon -->
            <div class="flex-shrink-0 mr-3">
              <div 
                class="w-8 h-8 rounded-full flex items-center justify-center"
                :class="getNotificationIconClass(notification.type)"
              >
                <svg 
                  xmlns="http://www.w3.org/2000/svg" 
                  class="h-4 w-4" 
                  fill="none" 
                  viewBox="0 0 24 24" 
                  stroke="currentColor"
                >
                  <path 
                    stroke-linecap="round" 
                    stroke-linejoin="round" 
                    stroke-width="2" 
                    :d="getNotificationIconPath(notification.type)" 
                  />
                </svg>
              </div>
            </div>
            
            <!-- Notification Content -->
            <div class="flex-1">
              <h4 class="text-sm font-medium text-gray-900">{{ notification.title }}</h4>
              <p class="text-xs text-gray-600 mt-1">{{ notification.message }}</p>
              <p class="text-xs text-gray-500 mt-1">{{ formatDate(notification.created_at) }}</p>
            </div>
            
            <!-- Unread Indicator -->
            <div v-if="!notification.read" class="ml-2 w-2 h-2 bg-blue-600 rounded-full"></div>
          </div>
        </div>
      </div>
      
      <!-- View All Link -->
      <div v-if="notifications.length > 5" class="p-3 border-t border-gray-200 text-center">
        <button 
          @click="viewAllNotifications" 
          class="text-sm text-blue-600 hover:text-blue-800"
        >
          View all notifications
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useAnalyticsNotifications, Notification, NotificationType } from '~/composables/useAnalyticsNotifications'
import moment from 'moment'

// Analytics notifications composable
const { 
  isLoading, 
  error, 
  notifications,
  unreadCount,
  initNotifications,
  markAsRead,
  markAllAsRead,
  cleanup
} = useAnalyticsNotifications()

// Dropdown state
const isOpen = ref(false)

// Toggle dropdown
const toggleDropdown = () => {
  isOpen.value = !isOpen.value
}

// View notification
const viewNotification = async (notification: Notification) => {
  // Mark as read
  if (!notification.read) {
    await markAsRead(notification.id)
  }
  
  // Handle notification action based on type
  if (notification.type === 'ab_test_complete' && notification.data.test_id) {
    // Navigate to A/B test results
    // This would be implemented based on the app's routing
    console.log(`Navigate to A/B test: ${notification.data.test_id}`)
  } else if (notification.data.card_id) {
    // Navigate to business card analytics
    // This would be implemented based on the app's routing
    console.log(`Navigate to card analytics: ${notification.data.card_id}`)
  }
}

// View all notifications
const viewAllNotifications = () => {
  // Navigate to notifications page
  // This would be implemented based on the app's routing
  console.log('Navigate to all notifications')
}

// Format date
const formatDate = (date: Date) => {
  return moment(date).fromNow()
}

// Get notification icon class
const getNotificationIconClass = (type: NotificationType) => {
  switch (type) {
    case 'view_increase':
      return 'bg-green-100 text-green-600'
    case 'engagement_increase':
      return 'bg-blue-100 text-blue-600'
    case 'ab_test_complete':
      return 'bg-purple-100 text-purple-600'
    case 'unusual_activity':
      return 'bg-red-100 text-red-600'
    case 'milestone':
      return 'bg-yellow-100 text-yellow-600'
    case 'summary':
      return 'bg-gray-100 text-gray-600'
    default:
      return 'bg-gray-100 text-gray-600'
  }
}

// Get notification icon path
const getNotificationIconPath = (type: NotificationType) => {
  switch (type) {
    case 'view_increase':
      return 'M13 7h8m0 0v8m0-8l-8 8-4-4-6 6'
    case 'engagement_increase':
      return 'M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z'
    case 'ab_test_complete':
      return 'M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z'
    case 'unusual_activity':
      return 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z'
    case 'milestone':
      return 'M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z'
    case 'summary':
      return 'M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
    default:
      return 'M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9'
  }
}

// Close dropdown when clicking outside
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement
  if (!target.closest('.notification-bell')) {
    isOpen.value = false
  }
}

// Lifecycle hooks
onMounted(() => {
  // Initialize notifications
  initNotifications()
  
  // Add click outside listener
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  // Clean up
  cleanup()
  
  // Remove click outside listener
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.notification-dropdown {
  max-height: 400px;
  overflow-y: auto;
}
</style>
