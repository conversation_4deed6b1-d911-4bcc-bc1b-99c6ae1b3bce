<template>
  <div class="user-engagement-dashboard">
    <div class="dashboard-header">
      <h2 class="dashboard-title">User Engagement Dashboard</h2>
      <div class="header-controls">
        <div class="realtime-indicator" :class="{ active: isRealtimeActive }">
          <div class="indicator-dot"></div>
          <span>{{ isRealtimeActive ? 'Live' : 'Offline' }}</span>
        </div>
        <button @click="refreshDashboard" :disabled="isLoading" class="refresh-btn">
          <Icon name="refresh" :class="{ spinning: isLoading }" />
          Refresh
        </button>
      </div>
    </div>

    <!-- Key Metrics Overview -->
    <div class="metrics-grid">
      <div class="metric-card">
        <div class="metric-header">
          <h3>Engagement Score</h3>
          <div class="score-badge" :class="engagementLevel">
            {{ Math.round(engagementMetrics.currentScore) }}
          </div>
        </div>
        <div class="metric-body">
          <div class="score-progress">
            <div 
              class="progress-bar" 
              :style="{ width: `${engagementMetrics.currentScore}%` }"
              :class="engagementLevel"
            ></div>
          </div>
          <div class="metric-trend" :class="engagementTrend">
            <Icon :name="getTrendIcon(engagementTrend)" />
            <span>{{ formatTrend(engagementTrend) }}</span>
          </div>
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-header">
          <h3>Active Users</h3>
          <div class="metric-value">{{ formatNumber(realtimeMetrics.activeUsers) }}</div>
        </div>
        <div class="metric-body">
          <div class="metric-details">
            <div class="detail-item">
              <span class="label">Current Session:</span>
              <span class="value">{{ formatDuration(currentSessionDuration) }}</span>
            </div>
            <div class="detail-item">
              <span class="label">Avg. Session:</span>
              <span class="value">{{ formatDuration(engagementMetrics.avgSessionDuration) }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-header">
          <h3>User Segments</h3>
          <div class="segment-indicator">
            <div class="segment-dot power-users"></div>
            <div class="segment-dot regular-users"></div>
            <div class="segment-dot casual-users"></div>
            <div class="segment-dot at-risk-users"></div>
          </div>
        </div>
        <div class="metric-body">
          <div class="segment-breakdown">
            <div v-for="segment in userSegments" :key="segment.id" class="segment-item">
              <div class="segment-bar">
                <div 
                  class="segment-fill" 
                  :class="segment.id"
                  :style="{ width: `${segment.percentage}%` }"
                ></div>
              </div>
              <span class="segment-label">{{ segment.name }}: {{ segment.count }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-header">
          <h3>Interactions</h3>
          <div class="metric-value">{{ formatNumber(realtimeMetrics.totalInteractions) }}</div>
        </div>
        <div class="metric-body">
          <div class="metric-details">
            <div class="detail-item">
              <span class="label">Per User:</span>
              <span class="value">{{ formatNumber(realtimeMetrics.avgInteractionsPerUser) }}</span>
            </div>
            <div class="detail-item">
              <span class="label">Per Session:</span>
              <span class="value">{{ formatNumber(realtimeMetrics.avgInteractionsPerSession) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Engagement Charts -->
    <div class="charts-section">
      <div class="chart-row">
        <div class="chart-container large">
          <h3 class="chart-title">Engagement Score Over Time</h3>
          <EngagementScoreChart 
            :data="engagementChartData"
            :loading="isLoading"
            @period-change="onPeriodChange"
          />
        </div>
        <div class="chart-container medium">
          <h3 class="chart-title">User Segment Distribution</h3>
          <!-- UserSegmentChart component not implemented yet
          <UserSegmentChart 
            :segments="userSegments"
            :loading="isLoading"
            @segment-click="onSegmentClick"
          />
          -->
        </div>
      </div>

      <div class="chart-row">
        <div class="chart-container medium">
          <h3 class="chart-title">Feature Usage Heatmap</h3>
          <FeatureUsageHeatmap 
            :data="featureUsageData"
            :loading="isLoading"
            @feature-click="onFeatureClick"
          />
        </div>
        <div class="chart-container medium">
          <h3 class="chart-title">Session Duration Distribution</h3>
          <SessionDurationChart 
            :data="sessionDurationData"
            :loading="isLoading"
          />
        </div>
      </div>
    </div>

    <!-- User Journey and Behavior Analysis -->
    <div class="analysis-section">
      <div class="section-header">
        <h3>Behavioral Analysis</h3>
        <div class="section-controls">
          <select v-model="selectedAnalysisPeriod" @change="refreshAnalysis">
            <option value="7">Last 7 days</option>
            <option value="30">Last 30 days</option>
            <option value="90">Last 90 days</option>
          </select>
        </div>
      </div>

      <div class="analysis-grid">
        <div class="analysis-card">
          <h4>User Journey Flow</h4>
          <UserJourneyVisualization 
            :data="userJourneyData"
            :loading="isLoading"
            @node-click="onJourneyNodeClick"
          />
        </div>

        <div class="analysis-card">
          <h4>Cohort Retention</h4>
          <CohortRetentionHeatmap 
            :data="cohortData"
            :loading="isLoading"
            @cohort-click="onCohortClick"
          />
        </div>

        <div class="analysis-card">
          <h4>Churn Risk Alert</h4>
          <ChurnRiskWidget 
            :predictions="churnPredictions"
            :loading="isLoading"
            @user-click="onChurnUserClick"
          />
        </div>

        <div class="analysis-card">
          <h4>Personalized Recommendations</h4>
          <RecommendationsWidget 
            :recommendations="recommendations"
            :metrics="recommendationMetrics"
            :loading="isLoading"
            @recommendation-click="onRecommendationClick"
          />
        </div>
      </div>
    </div>

    <!-- Alerts and Notifications -->
    <div v-if="alerts.length > 0" class="alerts-section">
      <h3>Engagement Alerts</h3>
      <div class="alerts-list">
        <div 
          v-for="alert in alerts" 
          :key="alert.id"
          class="alert-item"
          :class="alert.severity"
        >
          <div class="alert-icon">
            <Icon :name="getAlertIcon(alert.type)" />
          </div>
          <div class="alert-content">
            <div class="alert-title">{{ alert.title }}</div>
            <div class="alert-message">{{ alert.message }}</div>
            <div class="alert-time">{{ formatTime(alert.timestamp) }}</div>
          </div>
          <div class="alert-actions">
            <button @click="acknowledgeAlert(alert.id)" class="alert-btn">
              Acknowledge
            </button>
            <button @click="viewAlertDetails(alert)" class="alert-btn">
              Details
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Export and Configuration -->
    <div class="dashboard-footer">
      <div class="export-section">
        <button @click="exportDashboardData" class="export-btn">
          <Icon name="download" />
          Export Data
        </button>
        <button @click="scheduleReport" class="export-btn">
          <Icon name="calendar" />
          Schedule Report
        </button>
      </div>

      <div class="config-section">
        <button @click="showDashboardSettings = true" class="config-btn">
          <Icon name="settings" />
          Configure Dashboard
        </button>
      </div>
    </div>

    <!-- Dashboard Settings Modal -->
    <DashboardSettingsModal 
      v-if="showDashboardSettings"
      :settings="dashboardSettings"
      @close="showDashboardSettings = false"
      @save="saveDashboardSettings"
    />

    <!-- Alert Details Modal -->
    <AlertDetailsModal 
      v-if="selectedAlert"
      :alert="selectedAlert"
      @close="selectedAlert = null"
      @action="handleAlertAction"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useUserEngagement } from '~/composables/useUserEngagement'
import { useCohortAnalysis } from '~/composables/useCohortAnalysis'
import { useChurnPrediction } from '~/composables/useChurnPrediction'
import { useRecommendations } from '~/composables/useRecommendations'

// Component imports
import EngagementScoreChart from './charts/EngagementScoreChart.vue'
// import UserSegmentChart from './charts/UserSegmentChart.vue' // Component not implemented yet
import FeatureUsageHeatmap from './charts/FeatureUsageHeatmap.vue'
import SessionDurationChart from './charts/SessionDurationChart.vue'
import UserJourneyVisualization from './UserJourneyVisualization.vue'
import CohortRetentionHeatmap from './charts/CohortRetentionHeatmap.vue'
import ChurnRiskWidget from './widgets/ChurnRiskWidget.vue'
import RecommendationsWidget from './widgets/RecommendationsWidget.vue'
import DashboardSettingsModal from './modals/DashboardSettingsModal.vue'
import AlertDetailsModal from './modals/AlertDetailsModal.vue'

// Composables
const {
  state: engagementState,
  isLoading: engagementLoading,
  currentEngagementLevel,
  sessionDurationMinutes,
  engagementTrend,
  userSegments: engagementSegments,
  startEngagementTracking,
  getUserEngagementHistory,
  analyzeUserJourney
} = useUserEngagement()

const {
  state: cohortState,
  performCohortAnalysis,
  retentionHeatmapData
} = useCohortAnalysis()

const {
  state: churnState,
  runChurnPrediction,
  highRiskUsers
} = useChurnPrediction()

const {
  state: recommendationState,
  generateRecommendations,
  personalizedRecommendations,
  engagementImpact,
  calculateRecommendationMetrics
} = useRecommendations()

// Reactive data
const isLoading = ref(false)
const isRealtimeActive = ref(true)
const selectedAnalysisPeriod = ref('30')
const showDashboardSettings = ref(false)
const selectedAlert = ref(null)

// Dashboard settings
const dashboardSettings = ref({
  refreshInterval: 30000, // 30 seconds
  showRealtimeMetrics: true,
  enableAlerts: true,
  autoRefresh: true,
  defaultPeriod: '30'
})

// Real-time update interval
let updateInterval: NodeJS.Timeout | null = null

// Computed properties
const engagementLevel = computed(() => {
  const score = engagementMetrics.value.currentScore
  if (score >= 80) return 'excellent'
  if (score >= 60) return 'good'
  if (score >= 40) return 'average'
  return 'poor'
})

const engagementMetrics = computed(() => ({
  currentScore: engagementState.engagementScore || 0,
  avgSessionDuration: engagementState.userMetrics.sessionDuration || 0,
  totalInteractions: engagementState.userMetrics.interactions || 0,
  conversionRate: engagementState.userMetrics.conversionEvents || 0
}))

const realtimeMetrics = computed(() => ({
  activeUsers: 1, // Current user - would be real count in production
  totalInteractions: engagementState.userMetrics.interactions || 0,
  avgInteractionsPerUser: engagementState.userMetrics.interactions || 0,
  avgInteractionsPerSession: Math.round(engagementState.userMetrics.interactions / Math.max(1, engagementState.userMetrics.pageViews))
}))

const currentSessionDuration = computed(() => sessionDurationMinutes.value * 60)

const userSegments = computed(() => {
  const segments = engagementSegments || []
  const total = segments.reduce((sum, s) => sum + s.users.length, 0)
  
  return segments.map(segment => ({
    id: segment.id,
    name: segment.name,
    count: segment.users.length,
    percentage: total > 0 ? (segment.users.length / total) * 100 : 0,
    color: segment.color
  }))
})

const engagementChartData = computed(() => {
  // Generate sample data - would come from real analytics
  const days = 30
  const data = []
  const baseScore = 65
  
  for (let i = days; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    
    // Add some realistic variation
    const variation = Math.sin(i * 0.2) * 10 + Math.random() * 5
    const score = Math.max(0, Math.min(100, baseScore + variation))
    
    data.push({
      date: date.toISOString().split('T')[0],
      score: Math.round(score),
      users: Math.floor(Math.random() * 50) + 20
    })
  }
  
  return data
})

const featureUsageData = computed(() => {
  const features = ['Dashboard', 'Analytics', 'Reports', 'Settings', 'Export', 'Share']
  const hours = 24
  const data = []
  
  for (let hour = 0; hour < hours; hour++) {
    for (const feature of features) {
      // Generate realistic usage patterns
      let usage = 0
      if (hour >= 9 && hour <= 17) { // Business hours
        usage = Math.floor(Math.random() * 30) + 10
      } else {
        usage = Math.floor(Math.random() * 10)
      }
      
      data.push({
        hour,
        feature,
        usage,
        intensity: usage / 40 // Normalize for heatmap
      })
    }
  }
  
  return data
})

const sessionDurationData = computed(() => {
  // Generate session duration distribution
  const buckets = [
    { range: '0-1 min', count: 15, percentage: 12 },
    { range: '1-5 min', count: 35, percentage: 28 },
    { range: '5-15 min', count: 45, percentage: 36 },
    { range: '15-30 min', count: 20, percentage: 16 },
    { range: '30+ min', count: 10, percentage: 8 }
  ]
  
  return buckets
})

const userJourneyData = computed(() => {
  return analyzeUserJourney()
})

const cohortData = computed(() => {
  return retentionHeatmapData.value || []
})

const churnPredictions = computed(() => {
  return highRiskUsers.value.slice(0, 5) // Top 5 at-risk users
})

const recommendations = computed(() => {
  return personalizedRecommendations.value.slice(0, 3) // Top 3 recommendations
})

const recommendationMetrics = computed(() => {
  return engagementImpact.value
})

const alerts = computed(() => [
  ...churnState.alerts.filter(a => !a.acknowledged).slice(0, 3),
  // Add other alert types as needed
])

// Methods
async function refreshDashboard() {
  isLoading.value = true
  
  try {
    // Parallel refresh of all data
    await Promise.all([
      getUserEngagementHistory(),
      performCohortAnalysis(),
      runChurnPrediction(),
      generateRecommendations(),
      calculateRecommendationMetrics()
    ])
  } catch (error) {
    console.error('Error refreshing dashboard:', error)
  } finally {
    isLoading.value = false
  }
}

function startRealtimeUpdates() {
  if (!dashboardSettings.value.autoRefresh) return
  
  updateInterval = setInterval(async () => {
    if (isRealtimeActive.value && !isLoading.value) {
      await refreshDashboard()
    }
  }, dashboardSettings.value.refreshInterval)
}

function stopRealtimeUpdates() {
  if (updateInterval) {
    clearInterval(updateInterval)
    updateInterval = null
  }
}

function onPeriodChange(period: string) {
  selectedAnalysisPeriod.value = period
  refreshAnalysis()
}

function onSegmentClick(segment: any) {
  // Handle segment click - could open detailed view
  console.log('Segment clicked:', segment)
}

function onFeatureClick(feature: any) {
  // Handle feature click - could show feature analytics
  console.log('Feature clicked:', feature)
}

function onJourneyNodeClick(node: any) {
  // Handle journey node click - could show path details
  console.log('Journey node clicked:', node)
}

function onCohortClick(cohort: any) {
  // Handle cohort click - could show cohort details
  console.log('Cohort clicked:', cohort)
}

function onChurnUserClick(user: any) {
  // Handle churn user click - could show user details
  console.log('Churn user clicked:', user)
}

function onRecommendationClick(recommendation: any) {
  // Handle recommendation click - could show recommendation details
  console.log('Recommendation clicked:', recommendation)
}

async function refreshAnalysis() {
  isLoading.value = true
  
  try {
    const days = parseInt(selectedAnalysisPeriod.value)
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)
    
    await Promise.all([
      performCohortAnalysis(startDate),
      runChurnPrediction()
    ])
  } catch (error) {
    console.error('Error refreshing analysis:', error)
  } finally {
    isLoading.value = false
  }
}

function acknowledgeAlert(alertId: string) {
  const alert = alerts.value.find(a => a.id === alertId)
  if (alert) {
    alert.acknowledged = true
  }
}

function viewAlertDetails(alert: any) {
  selectedAlert.value = alert
}

function handleAlertAction(action: string, alert: any) {
  console.log('Alert action:', action, alert)
  selectedAlert.value = null
}

async function exportDashboardData() {
  const data = {
    timestamp: new Date().toISOString(),
    engagementMetrics: engagementMetrics.value,
    userSegments: userSegments.value,
    churnPredictions: churnPredictions.value.map(p => ({
      userId: p.userId,
      probability: p.churnProbability,
      riskLevel: p.riskLevel
    })),
    recommendations: recommendations.value.map(r => ({
      itemId: r.itemId,
      score: r.score,
      algorithm: r.algorithm
    }))
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `engagement-dashboard-${new Date().toISOString().split('T')[0]}.json`
  a.click()
  URL.revokeObjectURL(url)
}

function scheduleReport() {
  // Handle report scheduling
  console.log('Schedule report clicked')
}

function saveDashboardSettings(settings: any) {
  dashboardSettings.value = { ...dashboardSettings.value, ...settings }
  showDashboardSettings.value = false
  
  // Restart real-time updates with new settings
  stopRealtimeUpdates()
  startRealtimeUpdates()
}

// Utility functions
function formatNumber(value: number): string {
  if (value >= 1000000) return `${(value / 1000000).toFixed(1)}M`
  if (value >= 1000) return `${(value / 1000).toFixed(1)}K`
  return value.toString()
}

function formatDuration(seconds: number): string {
  if (seconds < 60) return `${seconds}s`
  const minutes = Math.floor(seconds / 60)
  if (minutes < 60) return `${minutes}m`
  const hours = Math.floor(minutes / 60)
  return `${hours}h ${minutes % 60}m`
}

function formatTrend(trend: string): string {
  const trendMap = {
    increasing: '+5.2%',
    decreasing: '-2.1%',
    stable: '0.0%'
  }
  return trendMap[trend as keyof typeof trendMap] || '0.0%'
}

function getTrendIcon(trend: string): string {
  const iconMap = {
    increasing: 'trending-up',
    decreasing: 'trending-down',
    stable: 'trending-flat'
  }
  return iconMap[trend as keyof typeof iconMap] || 'trending-flat'
}

function getAlertIcon(type: string): string {
  const iconMap = {
    churn_risk: 'warning',
    behavior_anomaly: 'info',
    engagement_drop: 'alert'
  }
  return iconMap[type as keyof typeof iconMap] || 'info'
}

function formatTime(timestamp: Date): string {
  return new Intl.RelativeTimeFormat('en', { numeric: 'auto' }).format(
    Math.floor((timestamp.getTime() - Date.now()) / (1000 * 60)),
    'minute'
  )
}

// Lifecycle
onMounted(async () => {
  // Start engagement tracking
  startEngagementTracking()
  
  // Initial data load
  await refreshDashboard()
  
  // Start real-time updates
  startRealtimeUpdates()
})

onUnmounted(() => {
  stopRealtimeUpdates()
})

// Watch for visibility changes to pause/resume updates
watch(() => document.visibilityState, (state) => {
  isRealtimeActive.value = state === 'visible'
})
</script>

<style scoped>
.user-engagement-dashboard {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.dashboard-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.realtime-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  background: var(--color-background-soft);
  color: var(--color-text-secondary);
  transition: all 0.3s ease;
}

.realtime-indicator.active {
  background: var(--color-success-background);
  color: var(--color-success);
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--color-text-secondary);
  transition: all 0.3s ease;
}

.realtime-indicator.active .indicator-dot {
  background: var(--color-success);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border: 1px solid var(--color-border);
  border-radius: 8px;
  background: var(--color-background);
  color: var(--color-text-primary);
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  background: var(--color-background-soft);
  border-color: var(--color-primary);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.metric-card {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 24px;
  transition: all 0.3s ease;
}

.metric-card:hover {
  border-color: var(--color-primary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.metric-header h3 {
  font-size: 16px;
  font-weight: 500;
  color: var(--color-text-secondary);
  margin: 0;
}

.metric-value {
  font-size: 32px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.score-badge {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 20px;
  font-weight: 600;
  color: white;
}

.score-badge.excellent { background: var(--color-success); }
.score-badge.good { background: var(--color-info); }
.score-badge.average { background: var(--color-warning); }
.score-badge.poor { background: var(--color-error); }

.score-progress {
  width: 100%;
  height: 8px;
  background: var(--color-background-soft);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 12px;
}

.progress-bar {
  height: 100%;
  transition: width 0.5s ease;
}

.progress-bar.excellent { background: var(--color-success); }
.progress-bar.good { background: var(--color-info); }
.progress-bar.average { background: var(--color-warning); }
.progress-bar.poor { background: var(--color-error); }

.metric-trend {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 500;
}

.metric-trend.increasing { color: var(--color-success); }
.metric-trend.decreasing { color: var(--color-error); }
.metric-trend.stable { color: var(--color-text-secondary); }

/* Segment indicators and breakdown */
.segment-indicator {
  display: flex;
  gap: 4px;
}

.segment-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.segment-dot.power-users { background: var(--color-success); }
.segment-dot.regular-users { background: var(--color-info); }
.segment-dot.casual-users { background: var(--color-warning); }
.segment-dot.at-risk-users { background: var(--color-error); }

.segment-breakdown {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.segment-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.segment-bar {
  flex: 1;
  height: 6px;
  background: var(--color-background-soft);
  border-radius: 3px;
  overflow: hidden;
}

.segment-fill {
  height: 100%;
  transition: width 0.5s ease;
}

.segment-fill.power_users { background: var(--color-success); }
.segment-fill.regular_users { background: var(--color-info); }
.segment-fill.casual_users { background: var(--color-warning); }
.segment-fill.at_risk_users { background: var(--color-error); }

.segment-label {
  font-size: 12px;
  color: var(--color-text-secondary);
  min-width: 120px;
}

.metric-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-item .label {
  font-size: 14px;
  color: var(--color-text-secondary);
}

.detail-item .value {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-primary);
}

/* Charts Section */
.charts-section {
  margin-bottom: 32px;
}

.chart-row {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.chart-row:last-child {
  grid-template-columns: 1fr 1fr;
}

.chart-container {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 24px;
}

.chart-title {
  font-size: 18px;
  font-weight: 500;
  color: var(--color-text-primary);
  margin: 0 0 20px 0;
}

/* Analysis Section */
.analysis-section {
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-header h3 {
  font-size: 20px;
  font-weight: 500;
  color: var(--color-text-primary);
  margin: 0;
}

.section-controls select {
  padding: 8px 16px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  background: var(--color-background);
  color: var(--color-text-primary);
}

.analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.analysis-card {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 24px;
}

.analysis-card h4 {
  font-size: 16px;
  font-weight: 500;
  color: var(--color-text-primary);
  margin: 0 0 16px 0;
}

/* Alerts Section */
.alerts-section {
  margin-bottom: 32px;
}

.alerts-section h3 {
  font-size: 20px;
  font-weight: 500;
  color: var(--color-text-primary);
  margin: 0 0 16px 0;
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.alert-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid;
}

.alert-item.low {
  background: var(--color-info-background);
  border-left-color: var(--color-info);
}

.alert-item.medium {
  background: var(--color-warning-background);
  border-left-color: var(--color-warning);
}

.alert-item.high {
  background: var(--color-error-background);
  border-left-color: var(--color-error);
}

.alert-icon {
  color: currentColor;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 500;
  color: var(--color-text-primary);
  margin-bottom: 4px;
}

.alert-message {
  font-size: 14px;
  color: var(--color-text-secondary);
  margin-bottom: 4px;
}

.alert-time {
  font-size: 12px;
  color: var(--color-text-tertiary);
}

.alert-actions {
  display: flex;
  gap: 8px;
}

.alert-btn {
  padding: 6px 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: var(--color-background);
  color: var(--color-text-primary);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.alert-btn:hover {
  background: var(--color-background-soft);
}

/* Dashboard Footer */
.dashboard-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 24px;
  border-top: 1px solid var(--color-border);
}

.export-section, .config-section {
  display: flex;
  gap: 12px;
}

.export-btn, .config-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  background: var(--color-background);
  color: var(--color-text-primary);
  cursor: pointer;
  transition: all 0.3s ease;
}

.export-btn:hover, .config-btn:hover {
  background: var(--color-background-soft);
  border-color: var(--color-primary);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .chart-row {
    grid-template-columns: 1fr;
  }
  
  .analysis-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .user-engagement-dashboard {
    padding: 16px;
  }
  
  .dashboard-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-controls {
    justify-content: space-between;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .analysis-grid {
    grid-template-columns: 1fr;
  }
  
  .dashboard-footer {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
}
</style>