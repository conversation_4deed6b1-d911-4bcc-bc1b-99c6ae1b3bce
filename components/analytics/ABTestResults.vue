<template>
  <div class="ab-test-results">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
    </div>
    
    <!-- Error State -->
    <div v-else-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
      <p>{{ error }}</p>
    </div>
    
    <!-- No Tests State -->
    <div v-else-if="!hasTests" class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-6">
      <p>No A/B tests available for this business card.</p>
      <button 
        @click="createNewTest" 
        class="mt-2 bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        Create New Test
      </button>
    </div>
    
    <!-- Test Results -->
    <div v-else>
      <!-- Test Selector -->
      <div v-if="tests.length > 1" class="mb-6">
        <label for="test-selector" class="block text-sm font-medium text-gray-700 mb-1">Select Test</label>
        <select 
          id="test-selector" 
          v-model="selectedTestId" 
          class="block w-full border border-gray-300 rounded-md px-3 py-2"
          @change="selectTest"
        >
          <option v-for="test in tests" :key="test.id" :value="test.id">
            {{ test.name }} ({{ test.status }})
          </option>
        </select>
      </div>
      
      <!-- Selected Test Details -->
      <div v-if="selectedTest" class="mb-6">
        <div class="flex justify-between items-center mb-4">
          <div>
            <h3 class="text-lg font-medium text-gray-800">{{ selectedTest.name }}</h3>
            <p class="text-sm text-gray-500">{{ selectedTest.description }}</p>
          </div>
          <div class="flex items-center">
            <span 
              class="px-2 py-1 text-xs rounded-full" 
              :class="getStatusClass(selectedTest.status)"
            >
              {{ selectedTest.status }}
            </span>
            <div class="ml-4 flex space-x-2">
              <button 
                v-if="selectedTest.status === 'draft'" 
                @click="startTest(selectedTest.id)" 
                class="bg-green-600 text-white px-3 py-1 rounded-md text-sm font-medium hover:bg-green-700"
              >
                Start
              </button>
              <button 
                v-if="selectedTest.status === 'running'" 
                @click="pauseTest(selectedTest.id)" 
                class="bg-yellow-600 text-white px-3 py-1 rounded-md text-sm font-medium hover:bg-yellow-700"
              >
                Pause
              </button>
              <button 
                v-if="selectedTest.status === 'paused'" 
                @click="startTest(selectedTest.id)" 
                class="bg-green-600 text-white px-3 py-1 rounded-md text-sm font-medium hover:bg-green-700"
              >
                Resume
              </button>
              <button 
                v-if="['running', 'paused'].includes(selectedTest.status)" 
                @click="completeTest(selectedTest.id)" 
                class="bg-blue-600 text-white px-3 py-1 rounded-md text-sm font-medium hover:bg-blue-700"
              >
                Complete
              </button>
            </div>
          </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div class="bg-white rounded-lg shadow p-4">
            <h4 class="text-sm font-medium text-gray-500 mb-2">Test Period</h4>
            <p class="text-sm">
              {{ formatDate(selectedTest.start_date) }} - 
              {{ selectedTest.status === 'completed' ? formatDate(selectedTest.end_date) : 'Ongoing' }}
            </p>
          </div>
          <div class="bg-white rounded-lg shadow p-4">
            <h4 class="text-sm font-medium text-gray-500 mb-2">Primary Metric</h4>
            <p class="text-sm capitalize">{{ selectedTest.metrics.primary.replace('_', ' ') }}</p>
          </div>
        </div>
        
        <!-- Results Section -->
        <div v-if="hasResults" class="mb-6">
          <h3 class="text-lg font-medium text-gray-800 mb-4">Results</h3>
          
          <!-- Winner Banner -->
          <div 
            v-if="selectedTest.results.winner" 
            class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6"
          >
            <div class="flex justify-between items-center">
              <p class="font-medium">
                Variant {{ selectedTest.results.winner.toUpperCase() }} is the winner!
              </p>
              <p class="text-sm">
                Confidence: {{ selectedTest.results.confidence.toFixed(1) }}%
              </p>
            </div>
          </div>
          
          <!-- Comparison Chart -->
          <div class="bg-white rounded-lg shadow p-4 mb-6">
            <h4 class="text-sm font-medium text-gray-800 mb-4">Conversion Rates</h4>
            <div class="h-64">
              <canvas ref="conversionChartRef"></canvas>
            </div>
          </div>
          
          <!-- Metrics Table -->
          <div class="bg-white rounded-lg shadow p-4">
            <h4 class="text-sm font-medium text-gray-800 mb-4">Detailed Metrics</h4>
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Metric</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Variant A</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Variant B</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Difference</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="(metric, index) in metrics" :key="index">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">{{ metric.label }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">{{ formatMetricValue(metric.key, selectedTest.results.a[metric.key]) }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">{{ formatMetricValue(metric.key, selectedTest.results.b[metric.key]) }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div 
                        class="text-sm" 
                        :class="getDifferenceClass(selectedTest.results.a[metric.key], selectedTest.results.b[metric.key])"
                      >
                        {{ formatDifference(selectedTest.results.a[metric.key], selectedTest.results.b[metric.key]) }}
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        
        <!-- No Results Section -->
        <div v-else class="bg-gray-100 border border-gray-300 text-gray-700 px-4 py-3 rounded mb-6">
          <p>No results available yet. The test needs more data to generate results.</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useABTesting } from '~/composables/useABTesting'
import Chart from 'chart.js/auto'
import moment from 'moment'

// Props
const props = defineProps({
  cardId: {
    type: String,
    default: undefined
  }
})

// A/B testing composable
const { 
  isLoading, 
  error, 
  tests,
  fetchTests,
  startTest,
  pauseTest,
  completeTest,
  calculateTestResults
} = useABTesting()

// Chart reference
const conversionChartRef = ref<HTMLCanvasElement | null>(null)

// Chart instance
let conversionChart: Chart | null = null

// Selected test
const selectedTestId = ref<string>('')
const selectedTest = ref<any>(null)

// Metrics
const metrics = [
  { key: 'views', label: 'Views' },
  { key: 'downloads', label: 'Downloads' },
  { key: 'qr_scans', label: 'QR Scans' },
  { key: 'contact_actions', label: 'Contact Actions' },
  { key: 'conversion_rate', label: 'Conversion Rate (%)' }
]

// Computed properties
const hasTests = computed(() => tests.value.length > 0)

const hasResults = computed(() => {
  return selectedTest.value && 
         selectedTest.value.results && 
         (selectedTest.value.results.a.views > 0 || selectedTest.value.results.b.views > 0)
})

// Methods
const formatDate = (date: Date) => {
  return date ? moment(date).format('MMM D, YYYY') : 'N/A'
}

const getStatusClass = (status: string) => {
  switch (status) {
    case 'draft':
      return 'bg-gray-200 text-gray-800'
    case 'running':
      return 'bg-green-200 text-green-800'
    case 'paused':
      return 'bg-yellow-200 text-yellow-800'
    case 'completed':
      return 'bg-blue-200 text-blue-800'
    default:
      return 'bg-gray-200 text-gray-800'
  }
}

const formatMetricValue = (key: string, value: number) => {
  if (key === 'conversion_rate') {
    return `${value.toFixed(1)}%`
  }
  return value
}

const formatDifference = (valueA: number, valueB: number) => {
  const difference = valueA - valueB
  const percentDifference = valueB !== 0 ? (difference / valueB) * 100 : 0
  
  if (difference === 0) {
    return 'No difference'
  }
  
  const sign = difference > 0 ? '+' : ''
  return `${sign}${difference} (${sign}${percentDifference.toFixed(1)}%)`
}

const getDifferenceClass = (valueA: number, valueB: number) => {
  const difference = valueA - valueB
  
  if (difference > 0) {
    return 'text-green-600'
  } else if (difference < 0) {
    return 'text-red-600'
  }
  
  return 'text-gray-600'
}

const selectTest = () => {
  selectedTest.value = tests.value.find(test => test.id === selectedTestId.value) || null
  
  if (selectedTest.value) {
    // Calculate results if not already calculated
    if (!selectedTest.value.results || !selectedTest.value.results.a) {
      calculateTestResults(selectedTest.value.id)
    }
    
    // Initialize chart
    initChart()
  }
}

const createNewTest = () => {
  // This would open a modal or navigate to a create test page
  console.log('Create new test')
}

const initChart = () => {
  if (!conversionChartRef.value || !selectedTest.value || !selectedTest.value.results) {
    return
  }
  
  // Destroy existing chart
  if (conversionChart) {
    conversionChart.destroy()
  }
  
  // Create new chart
  conversionChart = new Chart(conversionChartRef.value, {
    type: 'bar',
    data: {
      labels: ['Variant A', 'Variant B'],
      datasets: [
        {
          label: 'Conversion Rate (%)',
          data: [
            selectedTest.value.results.a.conversion_rate,
            selectedTest.value.results.b.conversion_rate
          ],
          backgroundColor: [
            selectedTest.value.results.winner === 'a' ? '#10B981' : '#3B82F6',
            selectedTest.value.results.winner === 'b' ? '#10B981' : '#3B82F6'
          ]
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: 'Conversion Rate (%)'
          }
        }
      }
    }
  })
}

// Lifecycle hooks
onMounted(async () => {
  // Fetch tests
  await fetchTests(props.cardId)
  
  // Select first test
  if (tests.value.length > 0) {
    selectedTestId.value = tests.value[0].id
    selectTest()
  }
})

// Watch for changes in tests
watch(tests, () => {
  if (tests.value.length > 0 && !selectedTestId.value) {
    selectedTestId.value = tests.value[0].id
    selectTest()
  }
})
</script>

<style scoped>
.ab-test-results {
  @apply bg-white rounded-lg shadow p-4;
}
</style>
