<template>
  <div class="benchmark-comparison-chart">
    <div class="chart-container" ref="chartContainer">
      <canvas ref="chartCanvas"></canvas>
    </div>
    
    <!-- Performance Summary -->
    <div class="performance-summary mt-4">
      <div class="summary-header mb-3">
        <h4 class="text-sm font-semibold text-gray-700">
          Performance vs {{ industry || 'Industry' }} Average
        </h4>
      </div>
      
      <div class="metrics-grid grid grid-cols-2 gap-3">
        <div 
          v-for="metric in displayMetrics" 
          :key="metric.name"
          class="metric-item p-3 border border-gray-200 rounded-lg"
        >
          <div class="flex items-center justify-between mb-1">
            <span class="text-sm text-gray-600 capitalize">
              {{ metric.name.replace('_', ' ') }}
            </span>
            <Icon 
              :name="metric.performanceIcon" 
              :class="metric.performanceColor"
              class="w-4 h-4"
            />
          </div>
          
          <div class="flex items-baseline gap-2">
            <span class="text-lg font-bold text-gray-900">
              {{ formatMetricValue(metric.userValue, metric.name) }}
            </span>
            <span class="text-sm text-gray-500">
              vs {{ formatMetricValue(metric.benchmarkValue, metric.name) }}
            </span>
          </div>
          
          <div class="progress-bar mt-2">
            <div class="progress-track bg-gray-200 rounded-full h-2">
              <div 
                :class="[
                  'progress-fill h-2 rounded-full transition-all duration-500',
                  metric.performanceColor.replace('text-', 'bg-')
                ]"
                :style="{ width: `${metric.progressPercent}%` }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { Chart, registerables } from 'chart.js'
import type { IndustryBenchmark } from '~/types/analytics'

// Register Chart.js components
Chart.register(...registerables)

interface Props {
  userMetrics: Record<string, number>
  industryBenchmarks: IndustryBenchmark[]
  industry?: string
}

const props = defineProps<Props>()

// Template refs
const chartContainer = ref<HTMLDivElement>()
const chartCanvas = ref<HTMLCanvasElement>()

// Chart instance
let chartInstance: Chart | null = null

/**
 * Compute display metrics with performance indicators
 */
const displayMetrics = computed(() => {
  if (!props.industryBenchmarks.length) return []
  
  return props.industryBenchmarks.map(benchmark => {
    const userValue = props.userMetrics[benchmark.metric] || 0
    const benchmarkValue = benchmark.benchmarkValue
    const ratio = benchmarkValue > 0 ? userValue / benchmarkValue : 1
    
    let performance: 'above' | 'below' | 'average'
    let performanceIcon: string
    let performanceColor: string
    
    if (ratio >= 1.1) {
      performance = 'above'
      performanceIcon = 'trending-up'
      performanceColor = 'text-green-500'
    } else if (ratio <= 0.9) {
      performance = 'below'
      performanceIcon = 'trending-down'
      performanceColor = 'text-red-500'
    } else {
      performance = 'average'
      performanceIcon = 'minus'
      performanceColor = 'text-yellow-500'
    }
    
    return {
      name: benchmark.metric,
      userValue,
      benchmarkValue,
      ratio,
      performance,
      performanceIcon,
      performanceColor,
      progressPercent: Math.min(100, ratio * 100)
    }
  })
})

/**
 * Initialize the chart
 */
const initChart = async () => {
  await nextTick()
  
  if (!chartCanvas.value || !displayMetrics.value.length) return

  const ctx = chartCanvas.value.getContext('2d')
  if (!ctx) return

  // Destroy existing chart
  if (chartInstance) {
    chartInstance.destroy()
  }

  // Prepare chart data
  const chartData = prepareChartData()

  // Create new chart
  chartInstance = new Chart(ctx, {
    type: 'radar',
    data: chartData,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
          labels: {
            usePointStyle: true,
            padding: 20
          }
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: 'white',
          bodyColor: 'white',
          borderColor: 'rgba(59, 130, 246, 0.5)',
          borderWidth: 1,
          callbacks: {
            label: (context) => {
              const metric = displayMetrics.value[context.dataIndex]
              const isUser = context.datasetIndex === 0
              const value = isUser ? metric.userValue : metric.benchmarkValue
              const label = isUser ? 'Your Performance' : 'Industry Average'
              
              return `${label}: ${formatMetricValue(value, metric.name)}`
            }
          }
        }
      },
      scales: {
        r: {
          beginAtZero: true,
          grid: {
            color: 'rgba(107, 114, 128, 0.2)'
          },
          pointLabels: {
            color: '#6B7280',
            font: {
              size: 12
            }
          },
          ticks: {
            color: '#6B7280',
            backdropColor: 'transparent'
          }
        }
      },
      elements: {
        line: {
          borderWidth: 2
        },
        point: {
          radius: 4,
          hoverRadius: 6
        }
      }
    }
  })
}

/**
 * Prepare chart data for radar chart
 */
const prepareChartData = () => {
  if (!displayMetrics.value.length) {
    return {
      labels: [],
      datasets: []
    }
  }

  const labels = displayMetrics.value.map(metric => 
    metric.name.replace('_', ' ').toUpperCase()
  )
  
  // Normalize values for radar chart (0-100 scale)
  const userValues = displayMetrics.value.map(metric => {
    const maxValue = Math.max(metric.userValue, metric.benchmarkValue) * 1.2
    return (metric.userValue / maxValue) * 100
  })
  
  const benchmarkValues = displayMetrics.value.map(metric => {
    const maxValue = Math.max(metric.userValue, metric.benchmarkValue) * 1.2
    return (metric.benchmarkValue / maxValue) * 100
  })

  return {
    labels,
    datasets: [
      {
        label: 'Your Performance',
        data: userValues,
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.2)',
        borderWidth: 2,
        pointBackgroundColor: 'rgb(59, 130, 246)',
        pointBorderColor: 'white',
        pointBorderWidth: 2
      },
      {
        label: `${props.industry || 'Industry'} Average`,
        data: benchmarkValues,
        borderColor: 'rgb(107, 114, 128)',
        backgroundColor: 'rgba(107, 114, 128, 0.1)',
        borderWidth: 2,
        pointBackgroundColor: 'rgb(107, 114, 128)',
        pointBorderColor: 'white',
        pointBorderWidth: 2,
        borderDash: [5, 5]
      }
    ]
  }
}

/**
 * Format metric value based on type
 */
const formatMetricValue = (value: number, metric: string): string => {
  if (metric.includes('rate') || metric.includes('percent')) {
    return `${(value * 100).toFixed(1)}%`
  }
  
  if (metric.includes('duration')) {
    return `${Math.round(value)}s`
  }
  
  if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}k`
  }
  
  return Math.round(value).toString()
}

/**
 * Update chart when data changes
 */
const updateChart = () => {
  if (chartInstance && displayMetrics.value.length) {
    const newData = prepareChartData()
    chartInstance.data = newData
    chartInstance.update('active')
  }
}

// Watch for changes in data
watch([() => props.userMetrics, () => props.industryBenchmarks], () => {
  updateChart()
}, { deep: true })

// Lifecycle
onMounted(() => {
  initChart()
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.destroy()
  }
})
</script>

<style scoped>
.benchmark-comparison-chart {
  @apply w-full;
}

.chart-container {
  @apply relative w-full;
  height: 300px;
}

.performance-summary {
  @apply mt-4;
}

.summary-header {
  @apply mb-3;
}

.metrics-grid {
  @apply grid grid-cols-2 gap-3;
}

.metric-item {
  @apply p-3 border border-gray-200 rounded-lg;
}

.progress-bar {
  @apply mt-2;
}

.progress-track {
  @apply bg-gray-200 rounded-full h-2;
}

.progress-fill {
  @apply h-2 rounded-full transition-all duration-500;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .metrics-grid {
    @apply grid-cols-1;
  }
  
  .chart-container {
    height: 250px;
  }
}
</style>
