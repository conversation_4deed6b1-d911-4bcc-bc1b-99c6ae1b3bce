<template>
  <div class="trend-analysis-chart">
    <div class="chart-container" ref="chartContainer">
      <canvas ref="chartCanvas"></canvas>
    </div>
    
    <!-- Trend Summary -->
    <div class="trend-summary mt-4 grid grid-cols-1 sm:grid-cols-3 gap-4">
      <div 
        v-for="trend in trends" 
        :key="trend.metric"
        class="trend-item p-3 bg-gray-50 rounded-lg"
      >
        <div class="flex items-center justify-between mb-1">
          <span class="text-sm font-medium text-gray-700 capitalize">
            {{ trend.metric.replace('_', ' ') }}
          </span>
          <Icon 
            :name="getTrendIcon(trend.trend)" 
            :class="getTrendColor(trend.trend)"
            class="w-4 h-4"
          />
        </div>
        <div class="text-lg font-bold text-gray-900">
          {{ formatTrendValue(trend.currentValue, trend.metric) }}
        </div>
        <div class="flex items-center gap-2 text-sm">
          <span :class="getTrendColor(trend.trend)">
            {{ trend.changePercent > 0 ? '+' : '' }}{{ trend.changePercent.toFixed(1) }}%
          </span>
          <span v-if="showSignificance" class="text-gray-500">
            ({{ (trend.significance * 100).toFixed(0) }}% confidence)
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { Chart, registerables } from 'chart.js'
import type { TrendAnalysis } from '~/types/analytics'

// Register Chart.js components
Chart.register(...registerables)

interface Props {
  trends: TrendAnalysis[]
  showSignificance?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showSignificance: false
})

// Template refs
const chartContainer = ref<HTMLDivElement>()
const chartCanvas = ref<HTMLCanvasElement>()

// Chart instance
let chartInstance: Chart | null = null

/**
 * Initialize the chart
 */
const initChart = async () => {
  await nextTick()
  
  if (!chartCanvas.value || !props.trends.length) return

  const ctx = chartCanvas.value.getContext('2d')
  if (!ctx) return

  // Destroy existing chart
  if (chartInstance) {
    chartInstance.destroy()
  }

  // Prepare chart data
  const chartData = prepareChartData()

  // Create new chart
  chartInstance = new Chart(ctx, {
    type: 'line',
    data: chartData,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      interaction: {
        intersect: false,
        mode: 'index'
      },
      plugins: {
        legend: {
          position: 'top',
          labels: {
            usePointStyle: true,
            padding: 20
          }
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: 'white',
          bodyColor: 'white',
          borderColor: 'rgba(59, 130, 246, 0.5)',
          borderWidth: 1,
          callbacks: {
            title: (context) => {
              return `Day ${context[0].dataIndex + 1}`
            },
            label: (context) => {
              const trend = props.trends[context.datasetIndex]
              const value = context.parsed.y
              return `${trend.metric}: ${formatTrendValue(value, trend.metric)}`
            }
          }
        }
      },
      scales: {
        x: {
          title: {
            display: true,
            text: 'Time Period (Days)',
            color: '#6B7280'
          },
          grid: {
            color: 'rgba(107, 114, 128, 0.1)'
          },
          ticks: {
            color: '#6B7280'
          }
        },
        y: {
          title: {
            display: true,
            text: 'Value',
            color: '#6B7280'
          },
          grid: {
            color: 'rgba(107, 114, 128, 0.1)'
          },
          ticks: {
            color: '#6B7280'
          }
        }
      },
      elements: {
        line: {
          tension: 0.4
        },
        point: {
          radius: 3,
          hoverRadius: 5
        }
      }
    }
  })
}

/**
 * Prepare chart data from trends
 */
const prepareChartData = () => {
  if (!props.trends.length) {
    return {
      labels: [],
      datasets: []
    }
  }

  // Generate 30 days of sample data for each trend
  const days = 30
  const labels = Array.from({ length: days }, (_, i) => `Day ${i + 1}`)
  
  // Color palette for different metrics
  const colors = [
    'rgb(59, 130, 246)',   // Blue
    'rgb(16, 185, 129)',   // Green
    'rgb(245, 158, 11)',   // Yellow
    'rgb(239, 68, 68)',    // Red
    'rgb(139, 92, 246)',   // Purple
    'rgb(236, 72, 153)'    // Pink
  ]

  const datasets = props.trends.map((trend, index) => {
    const color = colors[index % colors.length]
    const data = generateTrendData(trend, days)
    
    return {
      label: trend.metric.replace('_', ' ').toUpperCase(),
      data,
      borderColor: color,
      backgroundColor: color.replace('rgb', 'rgba').replace(')', ', 0.1)'),
      borderWidth: 2,
      fill: false,
      pointBackgroundColor: color,
      pointBorderColor: 'white',
      pointBorderWidth: 2
    }
  })

  return {
    labels,
    datasets
  }
}

/**
 * Generate sample trend data based on trend analysis
 */
const generateTrendData = (trend: TrendAnalysis, days: number): number[] => {
  const data: number[] = []
  const currentValue = trend.currentValue
  const changePercent = trend.changePercent / 100
  
  // Calculate daily change rate
  const dailyChangeRate = changePercent / days
  
  for (let i = 0; i < days; i++) {
    let value: number
    
    if (trend.trend === 'increasing') {
      // Exponential growth
      value = currentValue * Math.pow(1 + dailyChangeRate, i - days + 1)
    } else if (trend.trend === 'decreasing') {
      // Exponential decay
      value = currentValue * Math.pow(1 + dailyChangeRate, i - days + 1)
    } else {
      // Stable with minor fluctuations
      const noise = (Math.random() - 0.5) * 0.1 * currentValue
      value = currentValue + noise
    }
    
    // Add seasonality if present
    if (trend.seasonality && trend.seasonality.length > 0) {
      const seasonal = trend.seasonality[0]
      const seasonalEffect = seasonal.amplitude * Math.sin(
        (2 * Math.PI * i / (seasonal.period === 'weekly' ? 7 : 30)) + seasonal.phase
      )
      value += seasonalEffect * currentValue
    }
    
    data.push(Math.max(0, value))
  }
  
  return data
}

/**
 * Get trend icon based on trend direction
 */
const getTrendIcon = (trend: 'increasing' | 'decreasing' | 'stable'): string => {
  switch (trend) {
    case 'increasing': return 'trending-up'
    case 'decreasing': return 'trending-down'
    default: return 'minus'
  }
}

/**
 * Get trend color based on trend direction
 */
const getTrendColor = (trend: 'increasing' | 'decreasing' | 'stable'): string => {
  switch (trend) {
    case 'increasing': return 'text-green-500'
    case 'decreasing': return 'text-red-500'
    default: return 'text-gray-500'
  }
}

/**
 * Format trend value based on metric type
 */
const formatTrendValue = (value: number, metric: string): string => {
  if (metric.includes('rate') || metric.includes('percent')) {
    return `${(value * 100).toFixed(1)}%`
  }
  
  if (metric.includes('duration')) {
    return `${Math.round(value)}s`
  }
  
  if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}k`
  }
  
  return Math.round(value).toString()
}

/**
 * Update chart when trends change
 */
const updateChart = () => {
  if (chartInstance && props.trends.length) {
    const newData = prepareChartData()
    chartInstance.data = newData
    chartInstance.update('active')
  }
}

// Watch for changes in trends
watch(() => props.trends, () => {
  updateChart()
}, { deep: true })

// Lifecycle
onMounted(() => {
  initChart()
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.destroy()
  }
})
</script>

<style scoped>
.trend-analysis-chart {
  @apply w-full;
}

.chart-container {
  @apply relative w-full;
  height: 300px;
}

.trend-summary {
  @apply mt-4 grid grid-cols-1 sm:grid-cols-3 gap-4;
}

.trend-item {
  @apply p-3 bg-gray-50 rounded-lg;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .trend-summary {
    @apply grid-cols-1;
  }
  
  .chart-container {
    height: 250px;
  }
}
</style>
