<template>
  <div class="growth-projection-chart">
    <div class="chart-container" ref="chartContainer">
      <canvas ref="chartCanvas"></canvas>
    </div>
    
    <!-- Scenario Details -->
    <div class="scenario-details mt-4">
      <div 
        v-if="selectedScenarioData"
        class="scenario-info p-4 bg-gray-50 rounded-lg"
      >
        <div class="flex items-center justify-between mb-2">
          <h4 class="text-sm font-semibold text-gray-700 capitalize">
            {{ selectedScenarioData.name }} Scenario
          </h4>
          <span class="text-sm text-gray-500">
            {{ (selectedScenarioData.confidence * 100).toFixed(0) }}% confidence
          </span>
        </div>
        
        <p class="text-sm text-gray-600 mb-3">
          {{ selectedScenarioData.description }}
        </p>
        
        <div class="projection-stats grid grid-cols-3 gap-4">
          <div class="stat-item text-center">
            <div class="stat-value text-lg font-bold text-blue-600">
              {{ formatGrowthRate(selectedScenarioData.growthRate) }}
            </div>
            <div class="stat-label text-xs text-gray-500">Monthly Growth</div>
          </div>
          
          <div class="stat-item text-center">
            <div class="stat-value text-lg font-bold text-green-600">
              {{ formatProjectedValue(getProjectedValue(30)) }}
            </div>
            <div class="stat-label text-xs text-gray-500">30-Day Projection</div>
          </div>
          
          <div class="stat-item text-center">
            <div class="stat-value text-lg font-bold text-purple-600">
              {{ formatProjectedValue(getProjectedValue(90)) }}
            </div>
            <div class="stat-label text-xs text-gray-500">90-Day Projection</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { Chart, registerables } from 'chart.js'
import type { GrowthScenario } from '~/types/analytics'

// Register Chart.js components
Chart.register(...registerables)

interface Props {
  scenarios: GrowthScenario[]
  selectedScenario: string
}

const props = defineProps<Props>()

// Template refs
const chartContainer = ref<HTMLDivElement>()
const chartCanvas = ref<HTMLCanvasElement>()

// Chart instance
let chartInstance: Chart | null = null

/**
 * Get the currently selected scenario data
 */
const selectedScenarioData = computed(() => {
  return props.scenarios.find(s => s.name === props.selectedScenario)
})

/**
 * Initialize the chart
 */
const initChart = async () => {
  await nextTick()
  
  if (!chartCanvas.value || !props.scenarios.length) return

  const ctx = chartCanvas.value.getContext('2d')
  if (!ctx) return

  // Destroy existing chart
  if (chartInstance) {
    chartInstance.destroy()
  }

  // Prepare chart data
  const chartData = prepareChartData()

  // Create new chart
  chartInstance = new Chart(ctx, {
    type: 'line',
    data: chartData,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      interaction: {
        intersect: false,
        mode: 'index'
      },
      plugins: {
        legend: {
          position: 'top',
          labels: {
            usePointStyle: true,
            padding: 20,
            filter: (legendItem) => {
              // Only show the selected scenario and historical data
              return legendItem.text === 'Historical' || 
                     legendItem.text?.toLowerCase().includes(props.selectedScenario.toLowerCase())
            }
          }
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: 'white',
          bodyColor: 'white',
          borderColor: 'rgba(59, 130, 246, 0.5)',
          borderWidth: 1,
          callbacks: {
            title: (context) => {
              const dataIndex = context[0].dataIndex
              if (dataIndex < 30) {
                return `Day -${30 - dataIndex} (Historical)`
              } else {
                return `Day +${dataIndex - 29} (Projected)`
              }
            },
            label: (context) => {
              const value = context.parsed.y
              const label = context.dataset.label
              return `${label}: ${formatProjectedValue(value)}`
            }
          }
        }
      },
      scales: {
        x: {
          title: {
            display: true,
            text: 'Time Period',
            color: '#6B7280'
          },
          grid: {
            color: 'rgba(107, 114, 128, 0.1)'
          },
          ticks: {
            color: '#6B7280',
            callback: function(value, index) {
              if (index < 30) {
                return `${30 - index}d ago`
              } else if (index === 30) {
                return 'Today'
              } else {
                return `+${index - 30}d`
              }
            }
          }
        },
        y: {
          title: {
            display: true,
            text: 'Engagement Value',
            color: '#6B7280'
          },
          grid: {
            color: 'rgba(107, 114, 128, 0.1)'
          },
          ticks: {
            color: '#6B7280',
            callback: function(value) {
              return formatProjectedValue(Number(value))
            }
          }
        }
      },
      elements: {
        line: {
          tension: 0.4
        },
        point: {
          radius: 2,
          hoverRadius: 4
        }
      }
    }
  })
}

/**
 * Prepare chart data for growth projections
 */
const prepareChartData = () => {
  if (!props.scenarios.length) {
    return {
      labels: [],
      datasets: []
    }
  }

  // Generate 60 days total: 30 historical + 30 projected
  const totalDays = 60
  const historicalDays = 30
  const projectedDays = 30
  
  const labels = Array.from({ length: totalDays }, (_, i) => {
    if (i < historicalDays) {
      return `${historicalDays - i}d ago`
    } else if (i === historicalDays) {
      return 'Today'
    } else {
      return `+${i - historicalDays}d`
    }
  })

  // Generate historical data (declining trend leading to current)
  const currentValue = 100 // Base current engagement value
  const historicalData = generateHistoricalData(currentValue, historicalDays)
  
  const datasets = []

  // Historical data line
  datasets.push({
    label: 'Historical',
    data: [...historicalData, ...Array(projectedDays).fill(null)],
    borderColor: 'rgb(107, 114, 128)',
    backgroundColor: 'rgba(107, 114, 128, 0.1)',
    borderWidth: 2,
    fill: false,
    pointBackgroundColor: 'rgb(107, 114, 128)',
    pointBorderColor: 'white',
    pointBorderWidth: 1,
    spanGaps: false
  })

  // Scenario projection lines
  const colors = {
    conservative: 'rgb(34, 197, 94)',   // Green
    moderate: 'rgb(59, 130, 246)',      // Blue  
    optimistic: 'rgb(168, 85, 247)'     // Purple
  }

  props.scenarios.forEach(scenario => {
    const projectionData = generateProjectionData(currentValue, scenario, projectedDays)
    const color = colors[scenario.name as keyof typeof colors] || 'rgb(107, 114, 128)'
    
    // Only show the selected scenario prominently
    const isSelected = scenario.name === props.selectedScenario
    
    datasets.push({
      label: `${scenario.name.charAt(0).toUpperCase() + scenario.name.slice(1)} Projection`,
      data: [...Array(historicalDays).fill(null), currentValue, ...projectionData],
      borderColor: color,
      backgroundColor: color.replace('rgb', 'rgba').replace(')', ', 0.1)'),
      borderWidth: isSelected ? 3 : 1,
      fill: isSelected ? '+1' : false,
      pointBackgroundColor: color,
      pointBorderColor: 'white',
      pointBorderWidth: 1,
      spanGaps: false,
      hidden: !isSelected // Hide non-selected scenarios
    })
    
    // Add confidence interval for selected scenario
    if (isSelected) {
      const upperBound = projectionData.map(value => value * (1 + (1 - scenario.confidence) * 0.5))
      const lowerBound = projectionData.map(value => value * (1 - (1 - scenario.confidence) * 0.5))
      
      datasets.push({
        label: 'Upper Confidence',
        data: [...Array(historicalDays + 1).fill(null), ...upperBound],
        borderColor: color.replace('rgb', 'rgba').replace(')', ', 0.3)'),
        backgroundColor: 'transparent',
        borderWidth: 1,
        fill: false,
        pointRadius: 0,
        pointHoverRadius: 0,
        borderDash: [2, 2],
        spanGaps: false
      })
      
      datasets.push({
        label: 'Lower Confidence',
        data: [...Array(historicalDays + 1).fill(null), ...lowerBound],
        borderColor: color.replace('rgb', 'rgba').replace(')', ', 0.3)'),
        backgroundColor: 'transparent',
        borderWidth: 1,
        fill: false,
        pointRadius: 0,
        pointHoverRadius: 0,
        borderDash: [2, 2],
        spanGaps: false
      })
    }
  })

  return {
    labels,
    datasets
  }
}

/**
 * Generate historical data with realistic patterns
 */
const generateHistoricalData = (currentValue: number, days: number): number[] => {
  const data: number[] = []
  
  for (let i = 0; i < days; i++) {
    // Start from a lower value and gradually increase to current
    const progress = i / (days - 1)
    const baseValue = currentValue * (0.7 + 0.3 * progress)
    
    // Add some realistic fluctuation
    const noise = (Math.random() - 0.5) * 0.1 * baseValue
    const weeklyPattern = Math.sin((i / 7) * 2 * Math.PI) * 0.05 * baseValue
    
    data.push(Math.max(0, baseValue + noise + weeklyPattern))
  }
  
  return data.reverse() // Reverse to show oldest first
}

/**
 * Generate projection data based on scenario
 */
const generateProjectionData = (startValue: number, scenario: GrowthScenario, days: number): number[] => {
  const data: number[] = []
  const dailyGrowthRate = scenario.growthRate / 30 // Convert monthly to daily
  
  for (let i = 0; i < days; i++) {
    // Compound growth
    const growthValue = startValue * Math.pow(1 + dailyGrowthRate, i + 1)
    
    // Add some uncertainty based on confidence
    const uncertainty = (1 - scenario.confidence) * 0.2
    const noise = (Math.random() - 0.5) * uncertainty * growthValue
    
    data.push(Math.max(0, growthValue + noise))
  }
  
  return data
}

/**
 * Get projected value for a specific day
 */
const getProjectedValue = (days: number): number => {
  if (!selectedScenarioData.value) return 0
  
  const currentValue = 100
  const dailyGrowthRate = selectedScenarioData.value.growthRate / 30
  
  return currentValue * Math.pow(1 + dailyGrowthRate, days)
}

/**
 * Format growth rate for display
 */
const formatGrowthRate = (rate: number): string => {
  return `${(rate * 100).toFixed(1)}%`
}

/**
 * Format projected value for display
 */
const formatProjectedValue = (value: number): string => {
  if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}k`
  }
  return Math.round(value).toString()
}

/**
 * Update chart when data changes
 */
const updateChart = () => {
  if (chartInstance && props.scenarios.length) {
    const newData = prepareChartData()
    chartInstance.data = newData
    chartInstance.update('active')
  }
}

// Watch for changes in scenarios or selected scenario
watch([() => props.scenarios, () => props.selectedScenario], () => {
  updateChart()
}, { deep: true })

// Lifecycle
onMounted(() => {
  initChart()
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.destroy()
  }
})
</script>

<style scoped>
.growth-projection-chart {
  @apply w-full;
}

.chart-container {
  @apply relative w-full;
  height: 400px;
}

.scenario-details {
  @apply mt-4;
}

.scenario-info {
  @apply p-4 bg-gray-50 rounded-lg;
}

.projection-stats {
  @apply grid grid-cols-3 gap-4;
}

.stat-item {
  @apply text-center;
}

.stat-value {
  @apply text-lg font-bold;
}

.stat-label {
  @apply text-xs text-gray-500;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .chart-container {
    height: 300px;
  }
  
  .projection-stats {
    @apply grid-cols-1 gap-2;
  }
  
  .stat-value {
    @apply text-base;
  }
}
</style>
