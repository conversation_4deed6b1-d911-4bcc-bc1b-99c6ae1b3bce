<template>
  <div class="engagement-forecast-chart">
    <div class="chart-container" ref="chartContainer">
      <canvas ref="chartCanvas"></canvas>
    </div>
    
    <!-- Chart Legend -->
    <div class="chart-legend mt-4 flex items-center justify-center gap-6">
      <div class="legend-item flex items-center gap-2">
        <div class="legend-color w-3 h-3 bg-blue-500 rounded"></div>
        <span class="text-sm text-gray-600">Predicted Engagement</span>
      </div>
      <div class="legend-item flex items-center gap-2">
        <div class="legend-color w-3 h-3 bg-blue-200 rounded"></div>
        <span class="text-sm text-gray-600">Confidence Interval</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { Chart, registerables } from 'chart.js'
import type { EngagementPrediction } from '~/types/analytics'

// Register Chart.js components
Chart.register(...registerables)

interface Props {
  predictions: EngagementPrediction[]
  timeframe: '30d' | '60d' | '90d'
}

const props = defineProps<Props>()

// Template refs
const chartContainer = ref<HTMLDivElement>()
const chartCanvas = ref<HTMLCanvasElement>()

// Chart instance
let chartInstance: Chart | null = null

/**
 * Initialize the chart
 */
const initChart = async () => {
  await nextTick()
  
  if (!chartCanvas.value) return

  const ctx = chartCanvas.value.getContext('2d')
  if (!ctx) return

  // Destroy existing chart
  if (chartInstance) {
    chartInstance.destroy()
  }

  // Prepare chart data
  const chartData = prepareChartData()

  // Create new chart
  chartInstance = new Chart(ctx, {
    type: 'line',
    data: chartData,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      interaction: {
        intersect: false,
        mode: 'index'
      },
      plugins: {
        legend: {
          display: false // We use custom legend
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: 'white',
          bodyColor: 'white',
          borderColor: 'rgba(59, 130, 246, 0.5)',
          borderWidth: 1,
          callbacks: {
            title: (context) => {
              return `Day ${context[0].dataIndex + 1}`
            },
            label: (context) => {
              const value = context.parsed.y
              const confidence = getConfidenceForDataPoint(context.dataIndex)
              return [
                `Predicted Engagement: ${(value * 100).toFixed(1)}%`,
                `Confidence: ${(confidence * 100).toFixed(0)}%`
              ]
            }
          }
        }
      },
      scales: {
        x: {
          title: {
            display: true,
            text: 'Days',
            color: '#6B7280'
          },
          grid: {
            color: 'rgba(107, 114, 128, 0.1)'
          },
          ticks: {
            color: '#6B7280'
          }
        },
        y: {
          title: {
            display: true,
            text: 'Engagement Rate',
            color: '#6B7280'
          },
          grid: {
            color: 'rgba(107, 114, 128, 0.1)'
          },
          ticks: {
            color: '#6B7280',
            callback: function(value) {
              return `${(Number(value) * 100).toFixed(0)}%`
            }
          },
          min: 0,
          max: 1
        }
      },
      elements: {
        line: {
          tension: 0.4
        },
        point: {
          radius: 4,
          hoverRadius: 6
        }
      }
    }
  })
}

/**
 * Prepare chart data from predictions
 */
const prepareChartData = () => {
  if (!props.predictions.length) {
    return {
      labels: [],
      datasets: []
    }
  }

  // Generate time series data based on timeframe
  const days = parseInt(props.timeframe.replace('d', ''))
  const labels = Array.from({ length: days }, (_, i) => `Day ${i + 1}`)
  
  // For demo purposes, generate sample prediction data
  // In real implementation, this would come from the ML model
  const predictionData = generateSamplePredictions(days)
  const confidenceData = generateConfidenceInterval(predictionData)

  return {
    labels,
    datasets: [
      {
        label: 'Predicted Engagement',
        data: predictionData,
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        borderWidth: 2,
        fill: false,
        pointBackgroundColor: 'rgb(59, 130, 246)',
        pointBorderColor: 'white',
        pointBorderWidth: 2
      },
      {
        label: 'Upper Confidence',
        data: confidenceData.upper,
        borderColor: 'rgba(59, 130, 246, 0.3)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        borderWidth: 1,
        fill: '+1',
        pointRadius: 0,
        pointHoverRadius: 0
      },
      {
        label: 'Lower Confidence',
        data: confidenceData.lower,
        borderColor: 'rgba(59, 130, 246, 0.3)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        borderWidth: 1,
        fill: false,
        pointRadius: 0,
        pointHoverRadius: 0
      }
    ]
  }
}

/**
 * Generate sample prediction data
 * In real implementation, this would use actual ML predictions
 */
const generateSamplePredictions = (days: number): number[] => {
  const baseEngagement = 0.15 // 15% base engagement rate
  const predictions: number[] = []
  
  for (let i = 0; i < days; i++) {
    // Add some trend and seasonality
    const trend = 0.001 * i // Slight upward trend
    const seasonality = 0.02 * Math.sin((2 * Math.PI * i) / 7) // Weekly pattern
    const noise = (Math.random() - 0.5) * 0.01 // Small random variation
    
    const prediction = Math.max(0, Math.min(1, baseEngagement + trend + seasonality + noise))
    predictions.push(prediction)
  }
  
  return predictions
}

/**
 * Generate confidence interval data
 */
const generateConfidenceInterval = (predictions: number[]): { upper: number[], lower: number[] } => {
  const confidenceWidth = 0.05 // ±5% confidence interval
  
  return {
    upper: predictions.map(p => Math.min(1, p + confidenceWidth)),
    lower: predictions.map(p => Math.max(0, p - confidenceWidth))
  }
}

/**
 * Get confidence value for a specific data point
 */
const getConfidenceForDataPoint = (index: number): number => {
  // In real implementation, this would come from the ML model
  // For now, return a decreasing confidence over time
  return Math.max(0.5, 0.9 - (index * 0.01))
}

/**
 * Update chart when predictions change
 */
const updateChart = () => {
  if (chartInstance) {
    const newData = prepareChartData()
    chartInstance.data = newData
    chartInstance.update('active')
  }
}

// Watch for changes in predictions or timeframe
watch([() => props.predictions, () => props.timeframe], () => {
  updateChart()
}, { deep: true })

// Lifecycle
onMounted(() => {
  initChart()
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.destroy()
  }
})
</script>

<style scoped>
.engagement-forecast-chart {
  @apply w-full;
}

.chart-container {
  @apply relative w-full;
  height: 300px;
}

.chart-legend {
  @apply flex items-center justify-center gap-6 mt-4;
}

.legend-item {
  @apply flex items-center gap-2;
}

.legend-color {
  @apply w-3 h-3 rounded;
}
</style>
