<template>
  <div class="engagement-score-chart">
    <div class="chart-controls">
      <div class="period-selector">
        <button 
          v-for="period in periods" 
          :key="period.value"
          @click="changePeriod(period.value)"
          :class="{ active: selectedPeriod === period.value }"
          class="period-btn"
        >
          {{ period.label }}
        </button>
      </div>
      
      <div class="chart-options">
        <label class="checkbox-label">
          <input type="checkbox" v-model="showTrendLine" @change="updateChart">
          <span>Show Trend</span>
        </label>
        <label class="checkbox-label">
          <input type="checkbox" v-model="showAverage" @change="updateChart">
          <span>Show Average</span>
        </label>
      </div>
    </div>

    <div class="chart-metrics">
      <div class="metric-item">
        <span class="metric-label">Current Score:</span>
        <span class="metric-value" :class="currentScoreLevel">{{ currentScore }}</span>
      </div>
      <div class="metric-item">
        <span class="metric-label">Trend:</span>
        <span class="metric-value" :class="trendDirection">
          <Icon :name="trendIcon" />
          {{ trendPercentage }}
        </span>
      </div>
      <div class="metric-item">
        <span class="metric-label">Average:</span>
        <span class="metric-value">{{ averageScore.toFixed(1) }}</span>
      </div>
    </div>

    <div ref="chartContainer" class="chart-container">
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>Loading engagement data...</p>
      </div>
    </div>

    <div class="chart-legend">
      <div class="legend-item">
        <div class="legend-color primary"></div>
        <span>Engagement Score</span>
      </div>
      <div v-if="showTrendLine" class="legend-item">
        <div class="legend-color trend"></div>
        <span>Trend Line</span>
      </div>
      <div v-if="showAverage" class="legend-item">
        <div class="legend-color average"></div>
        <span>Average ({{ averageScore.toFixed(1) }})</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as d3 from 'd3'

// Props
interface Props {
  data?: Array<{
    date: string
    score: number
    users?: number
  }>
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  loading: false
})

// Emits
const emit = defineEmits<{
  'period-change': [period: string]
  'data-point-click': [dataPoint: any]
}>()

// Reactive data
const chartContainer = ref<HTMLElement>()
const selectedPeriod = ref('30')
const showTrendLine = ref(true)
const showAverage = ref(true)

// Chart dimensions and D3 elements
let svg: d3.Selection<SVGSVGElement, unknown, null, undefined>
let width = 0
let height = 300
const margin = { top: 20, right: 30, bottom: 40, left: 50 }

// Period options
const periods = [
  { value: '7', label: '7D' },
  { value: '30', label: '30D' },
  { value: '90', label: '90D' },
  { value: '365', label: '1Y' }
]

// Computed properties
const chartData = computed(() => {
  if (!props.data || props.data.length === 0) return []
  
  return props.data.map(d => ({
    date: new Date(d.date),
    score: d.score,
    users: d.users || 0
  })).sort((a, b) => a.date.getTime() - b.date.getTime())
})

const currentScore = computed(() => {
  if (chartData.value.length === 0) return 0
  return chartData.value[chartData.value.length - 1].score
})

const currentScoreLevel = computed(() => {
  const score = currentScore.value
  if (score >= 80) return 'excellent'
  if (score >= 60) return 'good'
  if (score >= 40) return 'average'
  return 'poor'
})

const averageScore = computed(() => {
  if (chartData.value.length === 0) return 0
  return chartData.value.reduce((sum, d) => sum + d.score, 0) / chartData.value.length
})

const trendDirection = computed(() => {
  if (chartData.value.length < 2) return 'stable'
  
  const recent = chartData.value.slice(-7) // Last 7 points
  const older = chartData.value.slice(-14, -7) // Previous 7 points
  
  if (recent.length === 0 || older.length === 0) return 'stable'
  
  const recentAvg = recent.reduce((sum, d) => sum + d.score, 0) / recent.length
  const olderAvg = older.reduce((sum, d) => sum + d.score, 0) / older.length
  
  const difference = ((recentAvg - olderAvg) / olderAvg) * 100
  
  if (difference > 2) return 'increasing'
  if (difference < -2) return 'decreasing'
  return 'stable'
})

const trendPercentage = computed(() => {
  if (chartData.value.length < 2) return '0.0%'
  
  const recent = chartData.value.slice(-7)
  const older = chartData.value.slice(-14, -7)
  
  if (recent.length === 0 || older.length === 0) return '0.0%'
  
  const recentAvg = recent.reduce((sum, d) => sum + d.score, 0) / recent.length
  const olderAvg = older.reduce((sum, d) => sum + d.score, 0) / older.length
  
  const difference = ((recentAvg - olderAvg) / olderAvg) * 100
  return `${difference > 0 ? '+' : ''}${difference.toFixed(1)}%`
})

const trendIcon = computed(() => {
  const direction = trendDirection.value
  if (direction === 'increasing') return 'trending-up'
  if (direction === 'decreasing') return 'trending-down'
  return 'trending-flat'
})

// Methods
function initializeChart() {
  if (!chartContainer.value) return

  // Clear previous chart
  d3.select(chartContainer.value).selectAll('*').remove()

  // Set dimensions
  width = chartContainer.value.clientWidth
  height = 300

  // Create SVG
  svg = d3.select(chartContainer.value)
    .append('svg')
    .attr('width', width)
    .attr('height', height)

  // Create main group
  const g = svg.append('g')
    .attr('transform', `translate(${margin.left},${margin.top})`)

  updateChart()
}

function updateChart() {
  if (!svg || chartData.value.length === 0) return

  const innerWidth = width - margin.left - margin.right
  const innerHeight = height - margin.top - margin.bottom

  // Clear previous content
  svg.select('g').selectAll('*').remove()
  const g = svg.select('g')

  // Create scales
  const xScale = d3.scaleTime()
    .domain(d3.extent(chartData.value, d => d.date) as [Date, Date])
    .range([0, innerWidth])

  const yScale = d3.scaleLinear()
    .domain([0, 100]) // Engagement score is always 0-100
    .range([innerHeight, 0])

  // Create line generator
  const line = d3.line<any>()
    .x(d => xScale(d.date))
    .y(d => yScale(d.score))
    .curve(d3.curveMonotoneX)

  // Add gradient definition
  const gradient = svg.append('defs')
    .append('linearGradient')
    .attr('id', 'engagement-gradient')
    .attr('gradientUnits', 'userSpaceOnUse')
    .attr('x1', 0).attr('y1', 0)
    .attr('x2', 0).attr('y2', innerHeight)

  gradient.append('stop')
    .attr('offset', '0%')
    .attr('stop-color', '#3b82f6')
    .attr('stop-opacity', 0.3)

  gradient.append('stop')
    .attr('offset', '100%')
    .attr('stop-color', '#3b82f6')
    .attr('stop-opacity', 0)

  // Create area generator for fill
  const area = d3.area<any>()
    .x(d => xScale(d.date))
    .y0(innerHeight)
    .y1(d => yScale(d.score))
    .curve(d3.curveMonotoneX)

  // Add area fill
  g.append('path')
    .attr('class', 'area')
    .attr('d', area(chartData.value))
    .style('fill', 'url(#engagement-gradient)')

  // Add main line
  g.append('path')
    .attr('class', 'line')
    .attr('d', line(chartData.value))
    .style('fill', 'none')
    .style('stroke', '#3b82f6')
    .style('stroke-width', 3)
    .style('stroke-linecap', 'round')

  // Add trend line if enabled
  if (showTrendLine.value) {
    const trendData = calculateTrendLine(chartData.value)
    const trendLine = d3.line<any>()
      .x(d => xScale(d.date))
      .y(d => yScale(d.score))

    g.append('path')
      .attr('class', 'trend-line')
      .attr('d', trendLine(trendData))
      .style('fill', 'none')
      .style('stroke', '#ef4444')
      .style('stroke-width', 2)
      .style('stroke-dasharray', '5,5')
  }

  // Add average line if enabled
  if (showAverage.value) {
    g.append('line')
      .attr('class', 'average-line')
      .attr('x1', 0)
      .attr('x2', innerWidth)
      .attr('y1', yScale(averageScore.value))
      .attr('y2', yScale(averageScore.value))
      .style('stroke', '#10b981')
      .style('stroke-width', 2)
      .style('stroke-dasharray', '3,3')
  }

  // Add data points
  const dots = g.selectAll('.dot')
    .data(chartData.value)
    .enter()
    .append('circle')
    .attr('class', 'dot')
    .attr('cx', d => xScale(d.date))
    .attr('cy', d => yScale(d.score))
    .attr('r', 4)
    .style('fill', '#3b82f6')
    .style('stroke', '#ffffff')
    .style('stroke-width', 2)
    .style('cursor', 'pointer')

  // Add hover effects
  dots
    .on('mouseover', function(event, d) {
      d3.select(this)
        .transition()
        .duration(200)
        .attr('r', 6)

      // Show tooltip
      showTooltip(event, d)
    })
    .on('mouseout', function() {
      d3.select(this)
        .transition()
        .duration(200)
        .attr('r', 4)

      hideTooltip()
    })
    .on('click', (event, d) => {
      emit('data-point-click', d)
    })

  // Add axes
  const xAxis = d3.axisBottom(xScale)
    .tickFormat(d3.timeFormat('%m/%d'))
    .ticks(6)

  const yAxis = d3.axisLeft(yScale)
    .tickFormat(d => `${d}`)
    .ticks(5)

  g.append('g')
    .attr('class', 'x-axis')
    .attr('transform', `translate(0,${innerHeight})`)
    .call(xAxis)
    .style('color', '#6b7280')

  g.append('g')
    .attr('class', 'y-axis')
    .call(yAxis)
    .style('color', '#6b7280')

  // Add axis labels
  g.append('text')
    .attr('class', 'x-label')
    .attr('text-anchor', 'middle')
    .attr('x', innerWidth / 2)
    .attr('y', innerHeight + 35)
    .style('font-size', '12px')
    .style('fill', '#6b7280')
    .text('Date')

  g.append('text')
    .attr('class', 'y-label')
    .attr('text-anchor', 'middle')
    .attr('transform', 'rotate(-90)')
    .attr('x', -innerHeight / 2)
    .attr('y', -35)
    .style('font-size', '12px')
    .style('fill', '#6b7280')
    .text('Engagement Score')
}

function calculateTrendLine(data: any[]): any[] {
  if (data.length < 2) return data

  // Simple linear regression
  const n = data.length
  const sumX = data.reduce((sum, d, i) => sum + i, 0)
  const sumY = data.reduce((sum, d) => sum + d.score, 0)
  const sumXY = data.reduce((sum, d, i) => sum + i * d.score, 0)
  const sumXX = data.reduce((sum, d, i) => sum + i * i, 0)

  const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX)
  const intercept = (sumY - slope * sumX) / n

  return data.map((d, i) => ({
    date: d.date,
    score: slope * i + intercept
  }))
}

function showTooltip(event: MouseEvent, data: any) {
  const tooltip = d3.select('body')
    .append('div')
    .attr('class', 'engagement-tooltip')
    .style('position', 'absolute')
    .style('background', 'rgba(0, 0, 0, 0.8)')
    .style('color', 'white')
    .style('padding', '8px 12px')
    .style('border-radius', '4px')
    .style('font-size', '12px')
    .style('pointer-events', 'none')
    .style('z-index', '1000')

  tooltip.html(`
    <div>Date: ${data.date.toLocaleDateString()}</div>
    <div>Score: ${data.score}</div>
    ${data.users ? `<div>Users: ${data.users}</div>` : ''}
  `)

  tooltip
    .style('left', (event.pageX + 10) + 'px')
    .style('top', (event.pageY - 10) + 'px')
    .style('opacity', 0)
    .transition()
    .duration(200)
    .style('opacity', 1)
}

function hideTooltip() {
  d3.selectAll('.engagement-tooltip')
    .transition()
    .duration(200)
    .style('opacity', 0)
    .remove()
}

function changePeriod(period: string) {
  selectedPeriod.value = period
  emit('period-change', period)
}

// Resize handler
let resizeTimeout: NodeJS.Timeout
function handleResize() {
  clearTimeout(resizeTimeout)
  resizeTimeout = setTimeout(() => {
    if (chartContainer.value && svg) {
      initializeChart()
    }
  }, 250)
}

// Lifecycle
onMounted(() => {
  nextTick(() => {
    initializeChart()
  })
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (resizeTimeout) clearTimeout(resizeTimeout)
  d3.selectAll('.engagement-tooltip').remove()
})

// Watch for data changes
watch(() => props.data, () => {
  if (svg) {
    updateChart()
  }
}, { deep: true })

watch(() => props.loading, (newLoading) => {
  if (!newLoading && svg) {
    updateChart()
  }
})
</script>

<style scoped>
.engagement-score-chart {
  width: 100%;
  height: 100%;
}

.chart-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 16px;
}

.period-selector {
  display: flex;
  gap: 4px;
}

.period-btn {
  padding: 6px 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: var(--color-background);
  color: var(--color-text-secondary);
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.period-btn:hover {
  background: var(--color-background-soft);
  border-color: var(--color-primary);
}

.period-btn.active {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.chart-options {
  display: flex;
  gap: 16px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: var(--color-text-secondary);
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
}

.chart-metrics {
  display: flex;
  gap: 24px;
  margin-bottom: 16px;
  padding: 12px;
  background: var(--color-background-soft);
  border-radius: 6px;
  flex-wrap: wrap;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.metric-label {
  font-size: 12px;
  color: var(--color-text-secondary);
  font-weight: 500;
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
}

.metric-value.excellent { color: var(--color-success); }
.metric-value.good { color: var(--color-info); }
.metric-value.average { color: var(--color-warning); }
.metric-value.poor { color: var(--color-error); }

.metric-value.increasing { color: var(--color-success); }
.metric-value.decreasing { color: var(--color-error); }
.metric-value.stable { color: var(--color-text-secondary); }

.chart-container {
  position: relative;
  width: 100%;
  height: 300px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  background: var(--color-background);
}

.loading-state {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  z-index: 10;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-border);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.chart-legend {
  display: flex;
  gap: 20px;
  margin-top: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: var(--color-text-secondary);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.primary { background: #3b82f6; }
.legend-color.trend { background: #ef4444; }
.legend-color.average { background: #10b981; }

/* Responsive Design */
@media (max-width: 768px) {
  .chart-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .period-selector {
    justify-content: center;
  }

  .chart-options {
    justify-content: center;
  }

  .chart-metrics {
    flex-direction: column;
    gap: 8px;
  }

  .metric-item {
    justify-content: space-between;
  }

  .chart-container {
    height: 250px;
  }

  .chart-legend {
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }
}
</style>