<template>
  <div class="sharing-time-heatmap">
    <div class="heatmap-container">
      <!-- Day labels -->
      <div class="day-labels">
        <div class="day-label"></div> <!-- Empty corner -->
        <div 
          v-for="day in dayLabels" 
          :key="day"
          class="day-label"
        >
          {{ day }}
        </div>
      </div>
      
      <!-- Hour rows -->
      <div 
        v-for="hour in hours" 
        :key="hour"
        class="hour-row"
      >
        <!-- Hour label -->
        <div class="hour-label">
          {{ formatHour(hour) }}
        </div>
        
        <!-- Day cells -->
        <div 
          v-for="day in 7" 
          :key="`${hour}-${day}`"
          :class="[
            'time-cell',
            getIntensityClass(getRecommendationScore(day - 1, hour))
          ]"
          :title="getCellTooltip(day - 1, hour)"
          @click="selectTimeSlot(day - 1, hour)"
        >
          <div class="cell-content">
            <span class="lift-value">
              +{{ getRecommendationScore(day - 1, hour) }}%
            </span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Legend -->
    <div class="heatmap-legend mt-4">
      <div class="legend-title text-sm font-medium text-gray-700 mb-2">
        Predicted Engagement Lift
      </div>
      <div class="legend-scale flex items-center gap-1">
        <span class="text-xs text-gray-500">Low</span>
        <div class="scale-colors flex">
          <div 
            v-for="(color, index) in legendColors" 
            :key="index"
            :class="color"
            class="scale-color w-4 h-4"
          ></div>
        </div>
        <span class="text-xs text-gray-500">High</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { SharingRecommendation } from '~/types/analytics'

interface Props {
  recommendations: SharingRecommendation[]
}

const props = defineProps<Props>()

// Day and hour configurations
const dayLabels = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
const hours = Array.from({ length: 24 }, (_, i) => i)

// Legend colors for intensity scale
const legendColors = [
  'bg-gray-100',
  'bg-blue-100',
  'bg-blue-200',
  'bg-blue-300',
  'bg-blue-400',
  'bg-blue-500',
  'bg-blue-600'
]

/**
 * Create a lookup map for recommendations
 */
const recommendationMap = computed(() => {
  const map = new Map<string, SharingRecommendation>()
  
  props.recommendations.forEach(rec => {
    const key = `${rec.dayOfWeek}-${rec.hour}`
    map.set(key, rec)
  })
  
  return map
})

/**
 * Get recommendation score for a specific day and hour
 */
const getRecommendationScore = (dayOfWeek: number, hour: number): number => {
  const key = `${dayOfWeek}-${hour}`
  const recommendation = recommendationMap.value.get(key)
  
  if (recommendation) {
    return Math.round(recommendation.predictedLift)
  }
  
  // Generate sample data if no recommendation exists
  return generateSampleScore(dayOfWeek, hour)
}

/**
 * Generate sample engagement lift score
 * In real implementation, this would come from the ML model
 */
const generateSampleScore = (dayOfWeek: number, hour: number): number => {
  // Business hours (9-17) on weekdays tend to have higher engagement
  const isWeekday = dayOfWeek >= 1 && dayOfWeek <= 5
  const isBusinessHour = hour >= 9 && hour <= 17
  const isLunchTime = hour >= 12 && hour <= 13
  const isEveningPeak = hour >= 18 && hour <= 20
  
  let baseScore = 5 // Base 5% lift
  
  if (isWeekday) {
    baseScore += 10
    
    if (isBusinessHour) {
      baseScore += 15
    }
    
    if (isLunchTime) {
      baseScore += 10
    }
  } else {
    // Weekends
    if (isEveningPeak) {
      baseScore += 20
    }
  }
  
  // Add some randomness
  const randomVariation = (Math.random() - 0.5) * 10
  
  return Math.max(0, Math.round(baseScore + randomVariation))
}

/**
 * Get CSS class for cell intensity based on score
 */
const getIntensityClass = (score: number): string => {
  if (score >= 40) return 'intensity-6'
  if (score >= 30) return 'intensity-5'
  if (score >= 20) return 'intensity-4'
  if (score >= 15) return 'intensity-3'
  if (score >= 10) return 'intensity-2'
  if (score >= 5) return 'intensity-1'
  return 'intensity-0'
}

/**
 * Format hour for display
 */
const formatHour = (hour: number): string => {
  if (hour === 0) return '12 AM'
  if (hour < 12) return `${hour} AM`
  if (hour === 12) return '12 PM'
  return `${hour - 12} PM`
}

/**
 * Get tooltip text for a cell
 */
const getCellTooltip = (dayOfWeek: number, hour: number): string => {
  const day = dayLabels[dayOfWeek]
  const time = formatHour(hour)
  const score = getRecommendationScore(dayOfWeek, hour)
  
  return `${day} at ${time}: +${score}% predicted engagement lift`
}

/**
 * Handle time slot selection
 */
const selectTimeSlot = (dayOfWeek: number, hour: number) => {
  const recommendation = recommendationMap.value.get(`${dayOfWeek}-${hour}`)
  
  if (recommendation) {
    // Emit event or show details
    console.log('Selected time slot:', recommendation)
  }
}
</script>

<style scoped>
.sharing-time-heatmap {
  @apply w-full;
}

.heatmap-container {
  @apply grid gap-1;
  grid-template-columns: auto repeat(7, 1fr);
}

.day-labels {
  @apply contents;
}

.day-label {
  @apply text-xs font-medium text-gray-600 text-center py-2;
}

.hour-row {
  @apply contents;
}

.hour-label {
  @apply text-xs text-gray-600 text-right pr-2 py-1 flex items-center justify-end;
  min-width: 50px;
}

.time-cell {
  @apply relative border border-gray-200 rounded cursor-pointer transition-all duration-200 hover:scale-105 hover:shadow-sm;
  aspect-ratio: 1;
  min-height: 32px;
}

.cell-content {
  @apply absolute inset-0 flex items-center justify-center;
}

.lift-value {
  @apply text-xs font-medium;
}

/* Intensity classes */
.intensity-0 {
  @apply bg-gray-100 text-gray-600;
}

.intensity-1 {
  @apply bg-blue-100 text-blue-700;
}

.intensity-2 {
  @apply bg-blue-200 text-blue-800;
}

.intensity-3 {
  @apply bg-blue-300 text-blue-900;
}

.intensity-4 {
  @apply bg-blue-400 text-white;
}

.intensity-5 {
  @apply bg-blue-500 text-white;
}

.intensity-6 {
  @apply bg-blue-600 text-white;
}

.heatmap-legend {
  @apply mt-4;
}

.legend-title {
  @apply text-sm font-medium text-gray-700 mb-2;
}

.legend-scale {
  @apply flex items-center gap-2;
}

.scale-colors {
  @apply flex gap-0;
}

.scale-color {
  @apply w-4 h-4 first:rounded-l last:rounded-r;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .hour-label {
    @apply text-xs;
    min-width: 40px;
  }
  
  .time-cell {
    min-height: 28px;
  }
  
  .lift-value {
    @apply text-xs;
  }
  
  .day-label {
    @apply text-xs py-1;
  }
}
</style>
