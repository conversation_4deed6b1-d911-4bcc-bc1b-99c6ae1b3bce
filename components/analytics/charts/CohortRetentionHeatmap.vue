<template>
  <div class="cohort-retention-heatmap">
    <div class="chart-header">
      <h3>Cohort Retention Heatmap</h3>
    </div>
    <div class="chart-container">
      <canvas ref="chartCanvas" class="chart-canvas"></canvas>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'

const chartCanvas = ref<HTMLCanvasElement | null>(null)

const props = defineProps<{
  data?: any[]
  loading?: boolean
}>()

onMounted(async () => {
  await nextTick()
  if (chartCanvas.value) {
    // Simple placeholder chart implementation
    const ctx = chartCanvas.value.getContext('2d')
    if (ctx) {
      ctx.fillStyle = '#e5e7eb'
      ctx.fillRect(0, 0, chartCanvas.value.width, chartCanvas.value.height)
      ctx.fillStyle = '#374151'
      ctx.font = '14px system-ui'
      ctx.textAlign = 'center'
      ctx.fillText('Cohort Retention Heatmap', chartCanvas.value.width / 2, chartCanvas.value.height / 2)
    }
  }
})
</script>

<style scoped>
.cohort-retention-heatmap {
  @apply bg-white rounded-lg shadow p-4;
}

.chart-header h3 {
  @apply text-lg font-semibold text-gray-900 mb-4;
}

.chart-container {
  @apply relative h-64;
}

.chart-canvas {
  @apply w-full h-full;
}
</style>