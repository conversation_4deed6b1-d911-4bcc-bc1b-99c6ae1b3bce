<template>
  <div class="churn-risk-widget">
    <div v-if="loading" class="loading-state">
      <div class="spinner"></div>
      <span>Loading churn predictions...</span>
    </div>
    
    <div v-else-if="!predictions || predictions.length === 0" class="empty-state">
      <Icon name="users" />
      <span>No churn predictions available</span>
    </div>
    
    <div v-else class="predictions-list">
      <div 
        v-for="prediction in topRiskUsers" 
        :key="prediction.userId"
        class="prediction-item"
        :class="getRiskClass(prediction.riskScore)"
        @click="$emit('user-click', prediction)"
      >
        <div class="user-info">
          <div class="user-avatar">
            <span>{{ getUserInitials(prediction.userName) }}</span>
          </div>
          <div class="user-details">
            <div class="user-name">{{ prediction.userName }}</div>
            <div class="user-meta">{{ prediction.userEmail }}</div>
          </div>
        </div>
        
        <div class="risk-indicator">
          <div class="risk-score">{{ Math.round(prediction.riskScore * 100) }}%</div>
          <div class="risk-label">{{ getRiskLabel(prediction.riskScore) }}</div>
        </div>
        
        <div class="risk-factors">
          <div class="factor" v-for="factor in prediction.topFactors" :key="factor">
            <Icon name="warning" size="12" />
            <span>{{ factor }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <div class="widget-footer">
      <button class="view-all-btn" @click="$emit('view-all')">
        View All Predictions
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface ChurnPrediction {
  userId: string
  userName: string
  userEmail: string
  riskScore: number
  topFactors: string[]
  lastActivity?: Date
  accountValue?: number
}

interface Props {
  predictions?: ChurnPrediction[]
  loading?: boolean
  maxDisplayed?: number
}

interface Emits {
  (e: 'user-click', prediction: ChurnPrediction): void
  (e: 'view-all'): void
}

const props = withDefaults(defineProps<Props>(), {
  predictions: () => [],
  loading: false,
  maxDisplayed: 5
})

const emit = defineEmits<Emits>()

const topRiskUsers = computed(() => {
  if (!props.predictions) return []
  return props.predictions
    .sort((a, b) => b.riskScore - a.riskScore)
    .slice(0, props.maxDisplayed)
})

const getRiskClass = (riskScore: number): string => {
  if (riskScore >= 0.8) return 'high-risk'
  if (riskScore >= 0.6) return 'medium-risk'
  return 'low-risk'
}

const getRiskLabel = (riskScore: number): string => {
  if (riskScore >= 0.8) return 'High Risk'
  if (riskScore >= 0.6) return 'Medium Risk'
  return 'Low Risk'
}

const getUserInitials = (name: string): string => {
  if (!name) return '?'
  return name.split(' ').map(n => n.charAt(0)).join('').toUpperCase().slice(0, 2)
}
</script>

<style scoped>
.churn-risk-widget {
  @apply bg-white rounded-lg border border-gray-200 p-4;
}

.loading-state, .empty-state {
  @apply flex flex-col items-center justify-center py-8 text-gray-500;
}

.spinner {
  @apply w-6 h-6 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin mb-2;
}

.predictions-list {
  @apply space-y-3;
}

.prediction-item {
  @apply flex items-center justify-between p-3 rounded-lg border cursor-pointer transition-all;
}

.prediction-item:hover {
  @apply shadow-md;
}

.prediction-item.high-risk {
  @apply bg-red-50 border-red-200 hover:bg-red-100;
}

.prediction-item.medium-risk {
  @apply bg-yellow-50 border-yellow-200 hover:bg-yellow-100;
}

.prediction-item.low-risk {
  @apply bg-green-50 border-green-200 hover:bg-green-100;
}

.user-info {
  @apply flex items-center space-x-3;
}

.user-avatar {
  @apply w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-medium;
}

.user-details {
  @apply flex flex-col;
}

.user-name {
  @apply font-medium text-gray-900;
}

.user-meta {
  @apply text-xs text-gray-500;
}

.risk-indicator {
  @apply text-center;
}

.risk-score {
  @apply font-bold text-lg;
}

.high-risk .risk-score {
  @apply text-red-600;
}

.medium-risk .risk-score {
  @apply text-yellow-600;
}

.low-risk .risk-score {
  @apply text-green-600;
}

.risk-label {
  @apply text-xs text-gray-500;
}

.risk-factors {
  @apply flex flex-col space-y-1;
}

.factor {
  @apply flex items-center space-x-1 text-xs text-gray-600;
}

.widget-footer {
  @apply mt-4 pt-3 border-t border-gray-200;
}

.view-all-btn {
  @apply w-full py-2 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors;
}
</style>