<template>
  <div class="recommendations-widget">
    <div v-if="loading" class="loading-state">
      <div class="spinner"></div>
      <span>Loading recommendations...</span>
    </div>
    
    <div v-else-if="!recommendations || recommendations.length === 0" class="empty-state">
      <Icon name="lightbulb" />
      <span>No recommendations available</span>
    </div>
    
    <div v-else class="recommendations-list">
      <div 
        v-for="recommendation in recommendations" 
        :key="recommendation.id"
        class="recommendation-item"
        :class="getPriorityClass(recommendation.priority)"
      >
        <div class="recommendation-header">
          <div class="recommendation-icon">
            <Icon :name="getRecommendationIcon(recommendation.type)" />
          </div>
          <div class="recommendation-meta">
            <div class="recommendation-title">{{ recommendation.title }}</div>
            <div class="recommendation-category">{{ recommendation.category }}</div>
          </div>
          <div class="priority-badge" :class="getPriorityClass(recommendation.priority)">
            {{ recommendation.priority }}
          </div>
        </div>
        
        <div class="recommendation-content">
          <p class="recommendation-description">{{ recommendation.description }}</p>
          
          <div class="recommendation-impact" v-if="recommendation.expectedImpact">
            <div class="impact-metric">
              <Icon name="trending-up" size="14" />
              <span>Expected Impact: {{ recommendation.expectedImpact }}</span>
            </div>
          </div>
          
          <div class="recommendation-actions">
            <button 
              class="action-btn primary"
              @click="$emit('apply-recommendation', recommendation)"
              :disabled="recommendation.isApplied"
            >
              {{ recommendation.isApplied ? 'Applied' : 'Apply' }}
            </button>
            <button 
              class="action-btn secondary"
              @click="$emit('view-details', recommendation)"
            >
              View Details
            </button>
            <button 
              class="action-btn dismiss"
              @click="$emit('dismiss-recommendation', recommendation)"
            >
              Dismiss
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <div class="widget-footer">
      <button class="view-all-btn" @click="$emit('view-all')">
        View All Recommendations
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Recommendation {
  id: string
  title: string
  description: string
  category: string
  type: string
  priority: 'high' | 'medium' | 'low'
  expectedImpact?: string
  isApplied?: boolean
  createdAt?: Date
}

interface Props {
  recommendations?: Recommendation[]
  loading?: boolean
}

interface Emits {
  (e: 'apply-recommendation', recommendation: Recommendation): void
  (e: 'view-details', recommendation: Recommendation): void
  (e: 'dismiss-recommendation', recommendation: Recommendation): void
  (e: 'view-all'): void
}

const props = withDefaults(defineProps<Props>(), {
  recommendations: () => [],
  loading: false
})

const emit = defineEmits<Emits>()

const getPriorityClass = (priority: string): string => {
  return `priority-${priority}`
}

const getRecommendationIcon = (type: string): string => {
  const iconMap: Record<string, string> = {
    'engagement': 'users',
    'performance': 'activity',
    'feature': 'star',
    'retention': 'heart',
    'conversion': 'trending-up',
    'default': 'lightbulb'
  }
  return iconMap[type] || iconMap.default
}
</script>

<style scoped>
.recommendations-widget {
  @apply bg-white rounded-lg border border-gray-200 p-4;
}

.loading-state, .empty-state {
  @apply flex flex-col items-center justify-center py-8 text-gray-500;
}

.spinner {
  @apply w-6 h-6 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin mb-2;
}

.recommendations-list {
  @apply space-y-4;
}

.recommendation-item {
  @apply border rounded-lg p-4;
}

.recommendation-item.priority-high {
  @apply border-red-200 bg-red-50;
}

.recommendation-item.priority-medium {
  @apply border-yellow-200 bg-yellow-50;
}

.recommendation-item.priority-low {
  @apply border-green-200 bg-green-50;
}

.recommendation-header {
  @apply flex items-start justify-between mb-3;
}

.recommendation-icon {
  @apply w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600;
}

.recommendation-meta {
  @apply flex-1 mx-3;
}

.recommendation-title {
  @apply font-medium text-gray-900;
}

.recommendation-category {
  @apply text-xs text-gray-500 uppercase tracking-wide;
}

.priority-badge {
  @apply px-2 py-1 rounded-full text-xs font-medium;
}

.priority-badge.priority-high {
  @apply bg-red-100 text-red-800;
}

.priority-badge.priority-medium {
  @apply bg-yellow-100 text-yellow-800;
}

.priority-badge.priority-low {
  @apply bg-green-100 text-green-800;
}

.recommendation-content {
  @apply space-y-3;
}

.recommendation-description {
  @apply text-sm text-gray-700;
}

.impact-metric {
  @apply flex items-center space-x-1 text-sm text-gray-600;
}

.recommendation-actions {
  @apply flex space-x-2;
}

.action-btn {
  @apply px-3 py-1 rounded text-xs font-medium transition-colors;
}

.action-btn.primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed;
}

.action-btn.secondary {
  @apply bg-gray-100 text-gray-700 hover:bg-gray-200;
}

.action-btn.dismiss {
  @apply bg-red-100 text-red-700 hover:bg-red-200;
}

.widget-footer {
  @apply mt-4 pt-3 border-t border-gray-200;
}

.view-all-btn {
  @apply w-full py-2 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors;
}
</style>