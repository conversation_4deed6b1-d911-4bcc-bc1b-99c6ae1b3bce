<template>
  <div class="analytics-dashboard">
    <div class="analytics-header">
      <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-2">
        <h1 class="text-xl sm:text-2xl font-bold text-gray-800">Business Card Analytics</h1>
        <NotificationBell />
      </div>

      <!-- Date Range Selector -->
      <div class="date-range-selector mb-6">
        <div class="flex items-center space-x-4">
          <div class="flex items-center">
            <label for="start-date" class="mr-2 text-sm font-medium text-gray-700">From:</label>
            <input
              type="date"
              id="start-date"
              class="border border-gray-300 rounded-md px-3 py-2 text-sm"
              :value="formatDate(startDate)"
              @change="updateStartDate"
            />
          </div>
          <div class="flex items-center">
            <label for="end-date" class="mr-2 text-sm font-medium text-gray-700">To:</label>
            <input
              type="date"
              id="end-date"
              class="border border-gray-300 rounded-md px-3 py-2 text-sm"
              :value="formatDate(endDate)"
              @change="updateEndDate"
            />
          </div>
          <div class="flex space-x-2">
            <button
              @click="setPresetRange('7d')"
              class="px-3 py-1 text-sm rounded-md"
              :class="activeRange === '7d' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'"
            >
              Last 7 days
            </button>
            <button
              @click="setPresetRange('30d')"
              class="px-3 py-1 text-sm rounded-md"
              :class="activeRange === '30d' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'"
            >
              Last 30 days
            </button>
            <button
              @click="setPresetRange('90d')"
              class="px-3 py-1 text-sm rounded-md"
              :class="activeRange === '90d' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'"
            >
              Last 90 days
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
      <p>{{ error }}</p>
    </div>

    <!-- Analytics Content -->
    <div v-else>
      <!-- Summary Cards -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Views</h3>
          <p class="text-2xl font-bold text-gray-800 dark:text-gray-200">{{ summaryMetrics.views }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">{{ summaryMetrics.unique_views }} unique viewers</p>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Downloads</h3>
          <p class="text-2xl font-bold text-gray-800 dark:text-gray-200">{{ summaryMetrics.downloads }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ ((summaryMetrics.downloads / summaryMetrics.views) * 100).toFixed(1) }}% conversion rate
          </p>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Contact Actions</h3>
          <p class="text-2xl font-bold text-gray-800 dark:text-gray-200">
            {{
              summaryMetrics.email_clicks +
              summaryMetrics.phone_calls +
              summaryMetrics.mobile_calls +
              summaryMetrics.website_visits
            }}
          </p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{
              (((
                summaryMetrics.email_clicks +
                summaryMetrics.phone_calls +
                summaryMetrics.mobile_calls +
                summaryMetrics.website_visits
              ) / summaryMetrics.views) * 100).toFixed(1)
            }}% engagement rate
          </p>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Avg. View Time</h3>
          <p class="text-2xl font-bold text-gray-800 dark:text-gray-200">{{ formatDuration(summaryMetrics.avg_duration) }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">{{ formatDuration(summaryMetrics.total_duration) }} total</p>
        </div>
      </div>

      <!-- Engagement Chart -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4 mb-8">
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-2">
          <h2 class="text-lg font-medium text-gray-800 dark:text-gray-200">Engagement Over Time</h2>
          <div class="flex flex-wrap gap-2">
            <button
              @click="chartInterval = 'daily'"
              class="px-3 py-1 text-sm rounded-md"
              :class="chartInterval === 'daily' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300'"
            >
              Daily
            </button>
            <button
              @click="chartInterval = 'weekly'"
              class="px-3 py-1 text-sm rounded-md"
              :class="chartInterval === 'weekly' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300'"
            >
              Weekly
            </button>
            <button
              @click="chartInterval = 'monthly'"
              class="px-3 py-1 text-sm rounded-md"
              :class="chartInterval === 'monthly' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300'"
            >
              Monthly
            </button>
          </div>
        </div>
        <div class="h-64 chart-container">
          <canvas ref="engagementChartRef"></canvas>
        </div>
      </div>

      <!-- Bottom Charts Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Engagement Breakdown -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <h2 class="text-lg font-medium text-gray-800 dark:text-gray-200 mb-4">Engagement Breakdown</h2>
          <div class="h-64 chart-container">
            <canvas ref="engagementBreakdownRef"></canvas>
          </div>
        </div>

        <!-- Contact Actions -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <h2 class="text-lg font-medium text-gray-800 dark:text-gray-200 mb-4">Contact Actions</h2>
          <div class="h-64 chart-container">
            <canvas ref="contactActionsRef"></canvas>
          </div>
        </div>
      </div>

      <!-- Geographic Visualization -->
      <div class="mb-8">
        <h2 class="text-lg font-medium text-gray-800 mb-4">Geographic Distribution</h2>
        <GeographicMap :card-id="cardId" />
      </div>

      <!-- Engagement Funnel -->
      <div class="mb-8">
        <h2 class="text-lg font-medium text-gray-800 mb-4">Engagement Funnel</h2>
        <EngagementFunnel :card-id="cardId" />
      </div>

      <!-- A/B Testing -->
      <div class="mb-8">
        <h2 class="text-lg font-medium text-gray-800 mb-4">A/B Testing</h2>
        <ABTestResults :card-id="cardId" />
      </div>

      <!-- Export Controls -->
      <div class="flex justify-end mb-8">
        <button
          @click="exportData"
          class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Export Data
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useAnalytics } from '~/composables/useAnalytics'
import Chart from 'chart.js/auto'
import moment from 'moment'
import GeographicMap from '~/components/analytics/GeographicMap.vue'
import EngagementFunnel from '~/components/analytics/EngagementFunnel.vue'
import ABTestResults from '~/components/analytics/ABTestResults.vue'
import NotificationBell from '~/components/analytics/NotificationBell.vue'

// Props
const props = defineProps({
  cardId: {
    type: String,
    required: true
  }
})

// Analytics composable
const {
  isLoading,
  error,
  startDate,
  endDate,
  summaryMetrics,
  fetchCardAnalytics,
  getTimeSeriesData,
  getEngagementBreakdown,
  getContactActionsBreakdown,
  setDateRange,
  exportAsCSV
} = useAnalytics()

// Chart references
const engagementChartRef = ref<HTMLCanvasElement | null>(null)
const engagementBreakdownRef = ref<HTMLCanvasElement | null>(null)
const contactActionsRef = ref<HTMLCanvasElement | null>(null)

// Chart instances
let engagementChart: Chart | null = null
let engagementBreakdownChart: Chart | null = null
let contactActionsChart: Chart | null = null

// Chart interval
const chartInterval = ref<'daily' | 'weekly' | 'monthly'>('daily')

// Active date range
const activeRange = ref<string>('30d')

// Format date for input
const formatDate = (date: Date) => {
  return moment(date).format('YYYY-MM-DD')
}

// Format duration in milliseconds to readable format
const formatDuration = (duration: number) => {
  if (duration < 1000) return `${duration}ms`
  if (duration < 60000) return `${(duration / 1000).toFixed(1)}s`
  return `${(duration / 60000).toFixed(1)}m`
}

// Update start date
const updateStartDate = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.value) {
    activeRange.value = 'custom'
    setDateRange(new Date(target.value), endDate.value, props.cardId)
  }
}

// Update end date
const updateEndDate = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.value) {
    activeRange.value = 'custom'
    setDateRange(startDate.value, new Date(target.value), props.cardId)
  }
}

// Set preset date range
const setPresetRange = (range: string) => {
  activeRange.value = range
  const end = new Date()
  let start: Date

  switch (range) {
    case '7d':
      start = moment().subtract(7, 'days').toDate()
      break
    case '30d':
      start = moment().subtract(30, 'days').toDate()
      break
    case '90d':
      start = moment().subtract(90, 'days').toDate()
      break
    default:
      start = moment().subtract(30, 'days').toDate()
  }

  setDateRange(start, end, props.cardId)
}

// Initialize charts
const initCharts = () => {
  // Engagement Chart
  if (engagementChartRef.value) {
    const viewsData = getTimeSeriesData('views', chartInterval.value)
    const downloadsData = getTimeSeriesData('downloads', chartInterval.value)

    engagementChart = new Chart(engagementChartRef.value, {
      type: 'line',
      data: {
        labels: viewsData.map(item => item.date),
        datasets: [
          {
            label: 'Views',
            data: viewsData.map(item => item.value),
            borderColor: '#3B82F6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.1
          },
          {
            label: 'Downloads',
            data: downloadsData.map(item => item.value),
            borderColor: '#10B981',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            tension: 0.1
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    })
  }

  // Engagement Breakdown Chart
  if (engagementBreakdownRef.value) {
    const breakdownData = getEngagementBreakdown()

    engagementBreakdownChart = new Chart(engagementBreakdownRef.value, {
      type: 'doughnut',
      data: {
        labels: breakdownData.map(item => item.label),
        datasets: [
          {
            data: breakdownData.map(item => item.value),
            backgroundColor: [
              '#3B82F6', // Blue
              '#10B981', // Green
              '#F59E0B', // Yellow
              '#EF4444', // Red
              '#8B5CF6', // Purple
              '#EC4899'  // Pink
            ]
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'right'
          }
        }
      }
    })
  }

  // Contact Actions Chart
  if (contactActionsRef.value) {
    const actionsData = getContactActionsBreakdown()

    contactActionsChart = new Chart(contactActionsRef.value, {
      type: 'bar',
      data: {
        labels: actionsData.map(item => item.label),
        datasets: [
          {
            label: 'Actions',
            data: actionsData.map(item => item.value),
            backgroundColor: [
              '#3B82F6', // Blue
              '#10B981', // Green
              '#F59E0B', // Yellow
              '#EF4444', // Red
              '#8B5CF6'  // Purple
            ]
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    })
  }
}

// Update charts
const updateCharts = () => {
  // Destroy existing charts
  if (engagementChart) engagementChart.destroy()
  if (engagementBreakdownChart) engagementBreakdownChart.destroy()
  if (contactActionsChart) contactActionsChart.destroy()

  // Initialize new charts
  initCharts()
}

// Export data
const exportData = () => {
  const csvContent = exportAsCSV()
  if (!csvContent) return

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.setAttribute('href', url)
  link.setAttribute('download', `card_analytics_${props.cardId}_${formatDate(startDate.value)}_to_${formatDate(endDate.value)}.csv`)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// Watch for changes in chart interval
watch(chartInterval, () => {
  updateCharts()
})

// Watch for changes in summary metrics
watch(summaryMetrics, () => {
  updateCharts()
})

// Fetch data and initialize charts on mount
onMounted(async () => {
  await fetchCardAnalytics(props.cardId)
  initCharts()
})
</script>

<style scoped>
.analytics-dashboard {
  @apply p-6 bg-gray-50 rounded-lg;
}
</style>
