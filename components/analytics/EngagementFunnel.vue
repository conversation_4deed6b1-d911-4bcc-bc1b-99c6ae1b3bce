<template>
  <div class="engagement-funnel">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
    </div>
    
    <!-- Error State -->
    <div v-else-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
      <p>{{ error }}</p>
    </div>
    
    <!-- No Data State -->
    <div v-else-if="!hasFunnelData" class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-6">
      <p>No engagement data available for the selected date range.</p>
    </div>
    
    <!-- Funnel Visualization -->
    <div v-else class="funnel-container">
      <!-- Overall Conversion Rate -->
      <div class="overall-conversion mb-6">
        <h3 class="text-sm font-medium text-gray-500">Overall Conversion Rate</h3>
        <p class="text-2xl font-bold text-gray-800">{{ overallConversionRate.toFixed(1) }}%</p>
        <p class="text-sm text-gray-500">From view to contact</p>
      </div>
      
      <!-- SVG Funnel -->
      <div class="funnel-svg-container">
        <svg :width="svgWidth" :height="svgHeight" class="funnel-svg">
          <!-- Funnel Stages -->
          <g v-for="(stage, index) in funnelStages" :key="stage.id" :transform="`translate(0, ${index * stageHeight})`">
            <!-- Stage Trapezoid -->
            <path 
              :d="getStagePath(stage, index)" 
              :fill="getStageColor(index)"
              class="stage-path"
              @mouseenter="activeStage = index"
              @mouseleave="activeStage = null"
            />
            
            <!-- Stage Label -->
            <text 
              :x="svgWidth / 2" 
              :y="stageHeight / 2" 
              text-anchor="middle" 
              dominant-baseline="middle"
              class="stage-label"
              :class="{'text-white': index > 0}"
            >
              {{ stage.name }}
            </text>
            
            <!-- Stage Count -->
            <text 
              :x="svgWidth - 10" 
              :y="stageHeight / 2" 
              text-anchor="end" 
              dominant-baseline="middle"
              class="stage-count"
              :class="{'text-white': index > 0}"
            >
              {{ stage.count }}
            </text>
          </g>
          
          <!-- Conversion Rate Labels -->
          <g v-for="(stage, index) in funnelStages.slice(1)" :key="`conv-${stage.id}`">
            <text 
              :x="svgWidth + 5" 
              :y="index * stageHeight + stageHeight / 2 + stageHeight / 2" 
              text-anchor="start" 
              dominant-baseline="middle"
              class="conversion-label"
            >
              {{ stage.conversionRate.toFixed(1) }}%
            </text>
          </g>
        </svg>
      </div>
      
      <!-- Stage Details -->
      <div class="stage-details mt-6">
        <div v-for="(stage, index) in funnelStages" :key="`detail-${stage.id}`" class="stage-detail-row">
          <div class="flex items-center">
            <div 
              class="w-4 h-4 rounded-full mr-2" 
              :style="`background-color: ${getStageColor(index)}`"
            ></div>
            <span class="font-medium">{{ stage.name }}</span>
          </div>
          <div class="flex items-center space-x-4">
            <span>{{ stage.count }}</span>
            <span>{{ stage.percentage.toFixed(1) }}%</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useEngagementFunnel } from '~/composables/useEngagementFunnel'

// Props
const props = defineProps({
  cardId: {
    type: String,
    default: undefined
  }
})

// Engagement funnel composable
const { 
  isLoading, 
  error, 
  funnelStages,
  fetchFunnelData,
  getOverallConversionRate
} = useEngagementFunnel()

// SVG dimensions
const svgWidth = 400
const svgHeight = 300
const stageHeight = 60
const maxWidth = svgWidth - 40 // Padding on both sides
const minWidth = maxWidth * 0.3 // Minimum width for the bottom of the funnel

// Active stage for hover effects
const activeStage = ref<number | null>(null)

// Computed properties
const hasFunnelData = computed(() => {
  return funnelStages.value.some(stage => stage.count > 0)
})

const overallConversionRate = computed(() => {
  return getOverallConversionRate()
})

// Methods
const getStagePath = (stage: any, index: number) => {
  const topWidth = getStageWidth(index)
  const bottomWidth = getStageWidth(index + 1)
  const topPadding = (svgWidth - topWidth) / 2
  const bottomPadding = (svgWidth - bottomWidth) / 2
  
  return `
    M ${topPadding} 0
    L ${topPadding + topWidth} 0
    L ${bottomPadding + bottomWidth} ${stageHeight}
    L ${bottomPadding} ${stageHeight}
    Z
  `
}

const getStageWidth = (index: number) => {
  if (index >= funnelStages.value.length) {
    return minWidth
  }
  
  const stage = funnelStages.value[index]
  const maxCount = funnelStages.value[0].count || 1
  const percentage = stage.count / maxCount
  
  return Math.max(minWidth, maxWidth * percentage)
}

const getStageColor = (index: number) => {
  const colors = [
    '#3B82F6', // Blue
    '#10B981', // Green
    '#F59E0B', // Yellow
    '#8B5CF6', // Purple
    '#EC4899'  // Pink
  ]
  
  return colors[index % colors.length]
}

// Lifecycle hooks
onMounted(async () => {
  // Fetch funnel data
  await fetchFunnelData(props.cardId)
})

// Watch for changes in props
watch(() => props.cardId, async (newCardId) => {
  await fetchFunnelData(newCardId)
})
</script>

<style scoped>
.engagement-funnel {
  @apply bg-white rounded-lg shadow p-4;
}

.funnel-container {
  @apply flex flex-col;
}

.funnel-svg-container {
  @apply relative overflow-x-auto;
}

.stage-path {
  @apply transition-opacity duration-200;
  opacity: 0.9;
}

.stage-path:hover {
  opacity: 1;
}

.stage-label {
  @apply text-xs font-medium fill-current;
}

.stage-count {
  @apply text-xs font-medium fill-current;
}

.conversion-label {
  @apply text-xs font-medium fill-gray-500;
}

.stage-details {
  @apply grid gap-2;
}

.stage-detail-row {
  @apply flex justify-between items-center py-1 border-b border-gray-200;
}
</style>
