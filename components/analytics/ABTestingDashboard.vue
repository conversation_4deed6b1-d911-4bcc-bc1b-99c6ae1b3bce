<template>
  <div class="ab-testing-dashboard">
    <!-- Dashboard Header -->
    <div class="dashboard-header mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-2xl font-bold text-gray-900">A/B Testing Dashboard</h2>
          <p class="text-gray-600 mt-1">Optimize your business cards with data-driven experiments</p>
        </div>
        <div class="header-actions flex items-center gap-3">
          <button 
            @click="refreshTests" 
            :disabled="isLoading"
            class="refresh-btn bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
          >
            <Icon 
              name="refresh" 
              :class="{ 'animate-spin': isLoading }" 
              class="w-4 h-4"
            />
            Refresh
          </button>
          <button 
            @click="showCreateTestModal = true" 
            class="create-btn bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
          >
            <Icon name="plus" class="w-4 h-4" />
            Create Test
          </button>
        </div>
      </div>
    </div>

    <!-- Stats Overview -->
    <div class="stats-overview grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
      <div class="stat-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="stat-icon bg-blue-100 rounded-lg p-3 mr-4">
            <Icon name="beaker" class="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <p class="text-sm font-medium text-gray-600">Total Tests</p>
            <p class="text-2xl font-bold text-gray-900">{{ tests.length }}</p>
          </div>
        </div>
      </div>

      <div class="stat-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="stat-icon bg-green-100 rounded-lg p-3 mr-4">
            <Icon name="play" class="w-6 h-6 text-green-600" />
          </div>
          <div>
            <p class="text-sm font-medium text-gray-600">Running Tests</p>
            <p class="text-2xl font-bold text-gray-900">{{ runningTestsCount }}</p>
          </div>
        </div>
      </div>

      <div class="stat-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="stat-icon bg-purple-100 rounded-lg p-3 mr-4">
            <Icon name="check-circle" class="w-6 h-6 text-purple-600" />
          </div>
          <div>
            <p class="text-sm font-medium text-gray-600">Completed Tests</p>
            <p class="text-2xl font-bold text-gray-900">{{ completedTestsCount }}</p>
          </div>
        </div>
      </div>

      <div class="stat-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="stat-icon bg-yellow-100 rounded-lg p-3 mr-4">
            <Icon name="trending-up" class="w-6 h-6 text-yellow-600" />
          </div>
          <div>
            <p class="text-sm font-medium text-gray-600">Significant Results</p>
            <p class="text-2xl font-bold text-gray-900">{{ significantTestsCount }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters and Search -->
    <div class="filters-section mb-6">
      <div class="flex flex-col sm:flex-row gap-4">
        <div class="search-box flex-1">
          <div class="relative">
            <Icon name="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search tests..."
              class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
        
        <select 
          v-model="statusFilter" 
          class="status-filter px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">All Statuses</option>
          <option value="draft">Draft</option>
          <option value="running">Running</option>
          <option value="paused">Paused</option>
          <option value="completed">Completed</option>
          <option value="cancelled">Cancelled</option>
        </select>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex items-center justify-center py-12">
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
        <p class="text-gray-600 mt-4">Loading A/B tests...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
      <div class="flex items-center">
        <Icon name="alert-circle" class="w-5 h-5 text-red-500 mr-2" />
        <span class="text-red-700">{{ error }}</span>
      </div>
    </div>

    <!-- Tests List -->
    <div v-else class="tests-list">
      <div v-if="filteredTests.length === 0" class="empty-state text-center py-12">
        <Icon name="beaker" class="w-16 h-16 text-gray-300 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">No A/B tests found</h3>
        <p class="text-gray-600 mb-6">
          {{ searchQuery || statusFilter ? 'Try adjusting your filters' : 'Create your first A/B test to start optimizing your business cards' }}
        </p>
        <button 
          v-if="!searchQuery && !statusFilter"
          @click="showCreateTestModal = true" 
          class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
        >
          Create Your First Test
        </button>
      </div>

      <div v-else class="tests-grid space-y-4">
        <ABTestCard
          v-for="test in filteredTests"
          :key="test.id"
          :test="test"
          :results="testResults.get(test.id)"
          @start="startTest"
          @pause="pauseTest"
          @complete="completeTest"
          @delete="deleteTest"
          @clone="cloneTest"
          @view-details="viewTestDetails"
        />
      </div>
    </div>

    <!-- Create Test Modal -->
    <CreateABTestModal
      v-if="showCreateTestModal"
      @close="showCreateTestModal = false"
      @created="handleTestCreated"
    />

    <!-- TODO: Implement missing modal components
    <ABTestDetailsModal
      v-if="selectedTest"
      :test="selectedTest"
      :results="testResults.get(selectedTest.id)"
      :analytics="testAnalytics.get(selectedTest.id)"
      @close="selectedTest = null"
      @updated="handleTestUpdated"
    />

    <CloneABTestModal
      v-if="testToClone"
      :original-test="testToClone"
      @close="testToClone = null"
      @cloned="handleTestCloned"
    />
    -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Icon } from '#components'
import type { ABTest } from '~/types/abTesting'

// Import child components
import ABTestCard from './ABTestCard.vue'
import CreateABTestModal from './modals/CreateABTestModal.vue'
// import ABTestDetailsModal from './modals/ABTestDetailsModal.vue' // TODO: Create this component
// import CloneABTestModal from './modals/CloneABTestModal.vue' // TODO: Create this component

// Use the A/B testing composable
const {
  // State
  isLoading,
  error,
  isCreating,
  isStarting,
  
  // Data
  tests,
  activeTests,
  testResults,
  testAnalytics,
  
  // Filters
  statusFilter,
  searchQuery,
  
  // Computed
  filteredTests,
  runningTestsCount,
  completedTestsCount,
  
  // Methods
  loadABTests,
  createABTest,
  startABTest,
  pauseABTest,
  completeABTest,
  deleteABTest,
  cloneABTest,
  loadTestAnalytics,
  refreshTests,
  
  // Helpers
  formatDuration,
  getTestProgress,
  getTestConfidence
} = useABTesting()

// Local state
const showCreateTestModal = ref(false)
const selectedTest = ref<ABTest | null>(null)
const testToClone = ref<ABTest | null>(null)

// Computed properties
const significantTestsCount = computed(() => {
  return tests.value.filter(test => {
    const results = testResults.value.get(test.id)
    return results?.some(r => r.statisticalTests.some(t => t.isSignificant))
  }).length
})

// Event handlers
const handleTestCreated = async (test: ABTest) => {
  showCreateTestModal.value = false
  await refreshTests()
}

const handleTestUpdated = async () => {
  await refreshTests()
}

const handleTestCloned = async (test: ABTest) => {
  testToClone.value = null
  await refreshTests()
}

const startTest = async (testId: string) => {
  try {
    await startABTest(testId)
  } catch (error) {
    console.error('Failed to start test:', error)
  }
}

const pauseTest = async (testId: string) => {
  try {
    await pauseABTest(testId)
  } catch (error) {
    console.error('Failed to pause test:', error)
  }
}

const completeTest = async (testId: string) => {
  try {
    await completeABTest(testId)
  } catch (error) {
    console.error('Failed to complete test:', error)
  }
}

const deleteTest = async (testId: string) => {
  if (confirm('Are you sure you want to delete this test? This action cannot be undone.')) {
    try {
      await deleteABTest(testId)
    } catch (error) {
      console.error('Failed to delete test:', error)
    }
  }
}

const cloneTest = (test: ABTest) => {
  testToClone.value = test
}

const viewTestDetails = async (test: ABTest) => {
  selectedTest.value = test
  
  // Load analytics if not already loaded
  if (!testAnalytics.value.has(test.id)) {
    await loadTestAnalytics(test.id)
  }
}
</script>

<style scoped>
.ab-testing-dashboard {
  @apply max-w-7xl mx-auto;
}

.dashboard-header {
  @apply mb-6;
}

.header-actions {
  @apply flex items-center gap-3;
}

.stats-overview {
  @apply grid grid-cols-1 md:grid-cols-4 gap-6 mb-6;
}

.stat-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.stat-icon {
  @apply rounded-lg p-3 mr-4;
}

.filters-section {
  @apply mb-6;
}

.search-box {
  @apply flex-1;
}

.status-filter {
  @apply px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.tests-list {
  @apply space-y-4;
}

.empty-state {
  @apply text-center py-12;
}

.tests-grid {
  @apply space-y-4;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .stats-overview {
    @apply grid-cols-1;
  }
  
  .header-actions {
    @apply flex-col w-full gap-2;
  }
  
  .filters-section .flex {
    @apply flex-col gap-2;
  }
}
</style>
