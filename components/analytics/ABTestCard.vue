<template>
  <div class="ab-test-card bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
    <!-- Test Header -->
    <div class="test-header flex items-start justify-between mb-4">
      <div class="test-info flex-1">
        <div class="flex items-center gap-3 mb-2">
          <h3 class="text-lg font-semibold text-gray-900">{{ test.name }}</h3>
          <span 
            :class="[
              'status-badge px-2 py-1 rounded-full text-xs font-medium',
              getStatusClass(test.status)
            ]"
          >
            {{ formatStatus(test.status) }}
          </span>
        </div>
        <p class="text-gray-600 text-sm">{{ test.description }}</p>
      </div>
      
      <div class="test-actions">
        <div class="relative">
          <button 
            @click="showMenu = !showMenu"
            class="action-menu-btn p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <Icon name="more-vertical" class="w-4 h-4" />
          </button>
          
          <!-- Action Menu -->
          <div 
            v-if="showMenu"
            v-click-outside="() => showMenu = false"
            class="action-menu absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-10 min-w-[150px]"
          >
            <button 
              @click="viewDetails"
              class="menu-item w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
            >
              <Icon name="eye" class="w-4 h-4" />
              View Details
            </button>
            
            <button 
              v-if="test.status === 'draft'"
              @click="startTest"
              class="menu-item w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
            >
              <Icon name="play" class="w-4 h-4" />
              Start Test
            </button>
            
            <button 
              v-if="test.status === 'running'"
              @click="pauseTest"
              class="menu-item w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
            >
              <Icon name="pause" class="w-4 h-4" />
              Pause Test
            </button>
            
            <button 
              v-if="test.status === 'running' || test.status === 'paused'"
              @click="completeTest"
              class="menu-item w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
            >
              <Icon name="check" class="w-4 h-4" />
              Complete Test
            </button>
            
            <button 
              @click="cloneTest"
              class="menu-item w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
            >
              <Icon name="copy" class="w-4 h-4" />
              Clone Test
            </button>
            
            <div class="border-t border-gray-100 my-1"></div>
            
            <button 
              @click="deleteTest"
              class="menu-item w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center gap-2"
            >
              <Icon name="trash" class="w-4 h-4" />
              Delete Test
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Test Progress (for running tests) -->
    <div v-if="test.status === 'running'" class="test-progress mb-4">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm font-medium text-gray-700">Progress</span>
        <span class="text-sm text-gray-600">{{ progressPercentage }}%</span>
      </div>
      <div class="progress-bar bg-gray-200 rounded-full h-2">
        <div 
          class="progress-fill bg-blue-600 h-2 rounded-full transition-all duration-300"
          :style="{ width: `${progressPercentage}%` }"
        ></div>
      </div>
    </div>

    <!-- Test Metrics -->
    <div class="test-metrics grid grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
      <div class="metric">
        <p class="text-xs text-gray-500 uppercase tracking-wide">Variants</p>
        <p class="text-lg font-semibold text-gray-900">{{ test.variants.length }}</p>
      </div>
      
      <div class="metric">
        <p class="text-xs text-gray-500 uppercase tracking-wide">Duration</p>
        <p class="text-lg font-semibold text-gray-900">{{ testDuration }}</p>
      </div>
      
      <div class="metric">
        <p class="text-xs text-gray-500 uppercase tracking-wide">Total Views</p>
        <p class="text-lg font-semibold text-gray-900">{{ totalViews }}</p>
      </div>
      
      <div class="metric">
        <p class="text-xs text-gray-500 uppercase tracking-wide">Confidence</p>
        <p class="text-lg font-semibold text-gray-900">{{ confidenceLevel }}%</p>
      </div>
    </div>

    <!-- Variants Preview -->
    <div class="variants-preview mb-4">
      <h4 class="text-sm font-medium text-gray-700 mb-3">Variants</h4>
      <div class="variants-grid grid grid-cols-1 sm:grid-cols-2 gap-3">
        <div 
          v-for="variant in test.variants"
          :key="variant.id"
          class="variant-card border border-gray-200 rounded-lg p-3"
        >
          <div class="flex items-center justify-between mb-2">
            <span class="variant-name text-sm font-medium text-gray-900">
              {{ variant.name }}
              <span v-if="variant.isControl" class="control-badge ml-1 text-xs bg-gray-100 text-gray-600 px-2 py-0.5 rounded">
                Control
              </span>
            </span>
            <span class="traffic-percentage text-xs text-gray-500">
              {{ variant.trafficPercentage }}%
            </span>
          </div>
          
          <div v-if="results" class="variant-metrics text-xs text-gray-600">
            <div class="metric-row flex justify-between">
              <span>Views:</span>
              <span>{{ getVariantMetric(variant.id, 'views') }}</span>
            </div>
            <div class="metric-row flex justify-between">
              <span>Conversions:</span>
              <span>{{ getVariantMetric(variant.id, 'conversions') }}</span>
            </div>
            <div class="metric-row flex justify-between">
              <span>Rate:</span>
              <span>{{ getVariantConversionRate(variant.id) }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Results Summary (for completed tests) -->
    <div v-if="test.status === 'completed' && results" class="results-summary">
      <div class="flex items-center justify-between">
        <h4 class="text-sm font-medium text-gray-700">Results</h4>
        <div v-if="winningVariant" class="winner-badge flex items-center gap-1">
          <Icon name="trophy" class="w-4 h-4 text-yellow-500" />
          <span class="text-sm font-medium text-gray-900">
            {{ winningVariant.name }} wins
          </span>
        </div>
      </div>
      
      <div v-if="isSignificant" class="significance-indicator mt-2">
        <span class="inline-flex items-center gap-1 text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
          <Icon name="check-circle" class="w-3 h-3" />
          Statistically Significant
        </span>
      </div>
    </div>

    <!-- Test Dates -->
    <div class="test-dates mt-4 pt-4 border-t border-gray-100">
      <div class="dates-grid grid grid-cols-2 gap-4 text-xs text-gray-500">
        <div>
          <span class="label">Created:</span>
          <span class="value ml-1">{{ formatDate(test.createdAt) }}</span>
        </div>
        <div v-if="test.startDate">
          <span class="label">Started:</span>
          <span class="value ml-1">{{ formatDate(test.startDate) }}</span>
        </div>
        <div v-if="test.endDate">
          <span class="label">Ended:</span>
          <span class="value ml-1">{{ formatDate(test.endDate) }}</span>
        </div>
        <div v-if="test.status === 'running'">
          <span class="label">Ends:</span>
          <span class="value ml-1">{{ formatDate(estimatedEndDate) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Icon } from '#components'
import type { ABTest, TestResults } from '~/types/abTesting'

interface Props {
  test: ABTest
  results?: TestResults[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  start: [testId: string]
  pause: [testId: string]
  complete: [testId: string]
  delete: [testId: string]
  clone: [test: ABTest]
  viewDetails: [test: ABTest]
}>()

// Local state
const showMenu = ref(false)

// Computed properties
const progressPercentage = computed(() => {
  if (!props.test.startDate || props.test.status !== 'running') return 0
  
  const elapsed = Date.now() - props.test.startDate.getTime()
  const maxDuration = props.test.maxDuration * 24 * 60 * 60 * 1000 // Convert days to ms
  
  return Math.min(100, Math.round((elapsed / maxDuration) * 100))
})

const testDuration = computed(() => {
  if (!props.test.startDate) return 'Not started'
  
  const endDate = props.test.endDate || new Date()
  const days = Math.ceil((endDate.getTime() - props.test.startDate.getTime()) / (1000 * 60 * 60 * 24))
  
  return `${days} day${days !== 1 ? 's' : ''}`
})

const totalViews = computed(() => {
  if (!props.results) return 0
  return props.results.reduce((sum, result) => sum + result.metrics.views, 0)
})

const confidenceLevel = computed(() => {
  if (!props.results || props.results.length === 0) return 0
  
  const maxConfidence = Math.max(...props.results.map(r => r.confidence))
  return Math.round(maxConfidence * 100)
})

const winningVariant = computed(() => {
  if (!props.results) return null
  
  const nonControlResults = props.results.filter(r => 
    !props.test.variants.find(v => v.id === r.variantId)?.isControl
  )
  
  if (nonControlResults.length === 0) return null
  
  const winner = nonControlResults.reduce((best, current) => 
    current.confidence > best.confidence ? current : best
  )
  
  return props.test.variants.find(v => v.id === winner.variantId)
})

const isSignificant = computed(() => {
  if (!props.results) return false
  return props.results.some(r => r.statisticalTests.some(t => t.isSignificant))
})

const estimatedEndDate = computed(() => {
  if (!props.test.startDate) return new Date()
  
  const endTime = props.test.startDate.getTime() + (props.test.maxDuration * 24 * 60 * 60 * 1000)
  return new Date(endTime)
})

// Methods
const getStatusClass = (status: string): string => {
  const classes = {
    draft: 'bg-gray-100 text-gray-700',
    running: 'bg-green-100 text-green-700',
    paused: 'bg-yellow-100 text-yellow-700',
    completed: 'bg-blue-100 text-blue-700',
    cancelled: 'bg-red-100 text-red-700'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-700'
}

const formatStatus = (status: string): string => {
  return status.charAt(0).toUpperCase() + status.slice(1)
}

const getVariantMetric = (variantId: string, metric: keyof TestResults['metrics']): number => {
  if (!props.results) return 0
  
  const result = props.results.find(r => r.variantId === variantId)
  return result?.metrics[metric] || 0
}

const getVariantConversionRate = (variantId: string): string => {
  if (!props.results) return '0.0'
  
  const result = props.results.find(r => r.variantId === variantId)
  return result?.metrics.conversionRate.toFixed(1) || '0.0'
}

const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  }).format(date)
}

// Event handlers
const viewDetails = () => {
  showMenu.value = false
  emit('viewDetails', props.test)
}

const startTest = () => {
  showMenu.value = false
  emit('start', props.test.id)
}

const pauseTest = () => {
  showMenu.value = false
  emit('pause', props.test.id)
}

const completeTest = () => {
  showMenu.value = false
  emit('complete', props.test.id)
}

const cloneTest = () => {
  showMenu.value = false
  emit('clone', props.test)
}

const deleteTest = () => {
  showMenu.value = false
  emit('delete', props.test.id)
}
</script>

<style scoped>
.ab-test-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow;
}

.test-header {
  @apply flex items-start justify-between mb-4;
}

.status-badge {
  @apply px-2 py-1 rounded-full text-xs font-medium;
}

.action-menu-btn {
  @apply p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors;
}

.action-menu {
  @apply absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-10 min-w-[150px];
}

.menu-item {
  @apply w-full text-left px-4 py-2 text-sm hover:bg-gray-100 flex items-center gap-2 transition-colors;
}

.progress-bar {
  @apply bg-gray-200 rounded-full h-2;
}

.progress-fill {
  @apply bg-blue-600 h-2 rounded-full transition-all duration-300;
}

.test-metrics {
  @apply grid grid-cols-2 lg:grid-cols-4 gap-4 mb-4;
}

.variants-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 gap-3;
}

.variant-card {
  @apply border border-gray-200 rounded-lg p-3;
}

.control-badge {
  @apply ml-1 text-xs bg-gray-100 text-gray-600 px-2 py-0.5 rounded;
}

.winner-badge {
  @apply flex items-center gap-1;
}

.significance-indicator {
  @apply mt-2;
}

.test-dates {
  @apply mt-4 pt-4 border-t border-gray-100;
}

.dates-grid {
  @apply grid grid-cols-2 gap-4 text-xs text-gray-500;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .test-metrics {
    @apply grid-cols-2;
  }
  
  .variants-grid {
    @apply grid-cols-1;
  }
}
</style>
