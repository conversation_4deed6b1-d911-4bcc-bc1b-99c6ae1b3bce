<template>
  <div 
    class="chart-widget bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
    :class="{ 'real-time-active': realTime && isUpdating }"
  >
    <!-- Chart Header -->
    <div class="chart-header flex items-center justify-between p-4 border-b border-gray-100">
      <div class="chart-info flex-1">
        <h3 class="chart-title text-lg font-semibold text-gray-900">{{ chart.title }}</h3>
        <p v-if="chart.description" class="chart-description text-sm text-gray-600 mt-1">
          {{ chart.description }}
        </p>
      </div>
      
      <div class="chart-actions flex items-center gap-2">
        <!-- Real-time Indicator -->
        <div 
          v-if="realTime"
          class="realtime-indicator flex items-center gap-1 text-xs text-green-600"
        >
          <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          Live
        </div>
        
        <!-- Performance Indicator -->
        <div 
          v-if="performance && showPerformance"
          :class="[
            'performance-indicator text-xs px-2 py-1 rounded',
            getPerformanceClass(performance.renderTime)
          ]"
        >
          {{ performance.renderTime }}ms
        </div>
        
        <!-- Insights Badge -->
        <div 
          v-if="insights.length > 0"
          class="insights-badge bg-blue-100 text-blue-700 text-xs px-2 py-1 rounded-full cursor-pointer"
          @click="showInsightsPanel = !showInsightsPanel"
        >
          {{ insights.length }} insight{{ insights.length !== 1 ? 's' : '' }}
        </div>
        
        <!-- Chart Menu -->
        <div class="relative">
          <button
            @click="showMenu = !showMenu"
            class="chart-menu-btn p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <Icon name="more-vertical" class="w-4 h-4" />
          </button>
          
          <!-- Menu Dropdown -->
          <div 
            v-if="showMenu"
            v-click-outside="() => showMenu = false"
            class="chart-menu absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-10 min-w-[160px]"
          >
            <button 
              @click="refreshChart"
              class="menu-item w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
            >
              <Icon name="refresh-cw" class="w-4 h-4" />
              Refresh Data
            </button>
            
            <button 
              @click="editChart"
              class="menu-item w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
            >
              <Icon name="edit" class="w-4 h-4" />
              Edit Chart
            </button>
            
            <button 
              @click="duplicateChart"
              class="menu-item w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
            >
              <Icon name="copy" class="w-4 h-4" />
              Duplicate
            </button>
            
            <div class="border-t border-gray-100 my-1"></div>
            
            <button 
              @click="exportChart('png')"
              class="menu-item w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
            >
              <Icon name="image" class="w-4 h-4" />
              Export as PNG
            </button>
            
            <button 
              @click="exportChart('csv')"
              class="menu-item w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
            >
              <Icon name="download" class="w-4 h-4" />
              Export Data
            </button>
            
            <div class="border-t border-gray-100 my-1"></div>
            
            <button 
              @click="generateChartInsights"
              class="menu-item w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
            >
              <Icon name="lightbulb" class="w-4 h-4" />
              Generate Insights
            </button>
            
            <button 
              @click="deleteChart"
              class="menu-item w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center gap-2"
            >
              <Icon name="trash" class="w-4 h-4" />
              Delete Chart
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Chart Content -->
    <div class="chart-content relative">
      <!-- Loading Overlay -->
      <div 
        v-if="isLoading"
        class="loading-overlay absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10"
      >
        <div class="text-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p class="text-sm text-gray-600 mt-2">Loading chart...</p>
        </div>
      </div>

      <!-- Error State -->
      <div 
        v-if="error"
        class="error-state p-6 text-center"
      >
        <Icon name="alert-triangle" class="w-12 h-12 text-red-400 mx-auto mb-3" />
        <h4 class="text-sm font-medium text-gray-900 mb-1">Chart Error</h4>
        <p class="text-sm text-gray-600">{{ error }}</p>
        <button 
          @click="refreshChart"
          class="mt-3 text-sm text-blue-600 hover:text-blue-800"
        >
          Try Again
        </button>
      </div>

      <!-- Chart Canvas -->
      <div 
        v-else
        class="chart-canvas-container p-4"
        :style="{ height: chartHeight + 'px' }"
      >
        <canvas 
          ref="chartCanvas"
          :id="`chart-${chart.id}`"
          class="w-full h-full"
        ></canvas>
      </div>

      <!-- Insights Panel -->
      <div 
        v-if="showInsightsPanel && insights.length > 0"
        class="insights-panel absolute top-0 right-0 w-80 h-full bg-white border-l border-gray-200 shadow-lg z-20 overflow-y-auto"
      >
        <div class="insights-header flex items-center justify-between p-4 border-b border-gray-100">
          <h4 class="text-sm font-medium text-gray-900">Insights</h4>
          <button
            @click="showInsightsPanel = false"
            class="text-gray-400 hover:text-gray-600"
          >
            <Icon name="x" class="w-4 h-4" />
          </button>
        </div>
        
        <div class="insights-list p-4 space-y-3">
          <div 
            v-for="insight in insights"
            :key="insight.id"
            class="insight-item p-3 bg-gray-50 rounded-lg"
          >
            <div class="flex items-start gap-2">
              <Icon 
                :name="getInsightIcon(insight.type)" 
                :class="getInsightIconClass(insight.type)"
                class="w-4 h-4 mt-0.5 flex-shrink-0"
              />
              <div class="flex-1">
                <h5 class="text-sm font-medium text-gray-900">{{ insight.title }}</h5>
                <p class="text-xs text-gray-600 mt-1">{{ insight.description }}</p>
                <div class="flex items-center justify-between mt-2">
                  <span class="text-xs text-gray-500">
                    {{ (insight.confidence * 100).toFixed(0) }}% confidence
                  </span>
                  <span 
                    v-if="insight.actionable"
                    class="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded"
                  >
                    Actionable
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Chart Footer -->
    <div v-if="data?.metadata" class="chart-footer px-4 py-3 bg-gray-50 border-t border-gray-100">
      <div class="flex items-center justify-between text-xs text-gray-500">
        <span>
          {{ data.metadata.totalRecords }} records
        </span>
        <span>
          Last updated: {{ formatDate(data.metadata.lastUpdated) }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { Chart, registerables } from 'chart.js'
import { Icon } from '#components'
import type { ChartConfig, ChartData, VisualizationInsight, PerformanceMetrics } from '~/types/visualization'

// Register Chart.js components
Chart.register(...registerables)

interface Props {
  chart: ChartConfig
  data?: ChartData
  insights: VisualizationInsight[]
  performance?: PerformanceMetrics
  realTime?: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  update: [chartId: string, updates: Partial<ChartConfig>]
  delete: [chartId: string]
  export: [chartId: string, format: string]
  generateInsights: [chartId: string]
  resize: [chartId: string, size: { width: number; height: number }]
}>()

// Template refs
const chartCanvas = ref<HTMLCanvasElement>()

// Local state
const isLoading = ref(false)
const error = ref<string | null>(null)
const isUpdating = ref(false)
const showMenu = ref(false)
const showInsightsPanel = ref(false)
const showPerformance = ref(false)

// Chart instance
let chartInstance: Chart | null = null

// Computed properties
const chartHeight = computed(() => {
  return props.chart.position?.height || 300
})

// Methods
const initChart = async () => {
  await nextTick()
  
  if (!chartCanvas.value || !props.data) return

  const ctx = chartCanvas.value.getContext('2d')
  if (!ctx) return

  // Destroy existing chart
  if (chartInstance) {
    chartInstance.destroy()
  }

  const startTime = performance.now()

  try {
    // Create chart configuration
    const config = createChartConfig()
    
    // Create new chart
    chartInstance = new Chart(ctx, config)
    
    // Record performance
    const renderTime = performance.now() - startTime
    console.log(`Chart ${props.chart.id} rendered in ${renderTime.toFixed(2)}ms`)
    
  } catch (err: any) {
    console.error('Error creating chart:', err)
    error.value = err.message || 'Failed to create chart'
  }
}

const createChartConfig = () => {
  const config: any = {
    type: props.chart.type,
    data: props.data,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      animation: {
        duration: props.chart.settings.animations ? 750 : 0
      },
      plugins: {
        legend: {
          display: props.chart.settings.showLegend,
          position: 'top',
          labels: {
            usePointStyle: true,
            padding: 20,
            font: {
              size: props.chart.settings.fontSize || 12,
              family: props.chart.settings.fontFamily || 'Inter'
            }
          }
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: 'white',
          bodyColor: 'white',
          borderColor: 'rgba(59, 130, 246, 0.5)',
          borderWidth: 1,
          callbacks: {
            label: (context: any) => {
              const label = context.dataset.label || ''
              const value = context.parsed.y || context.parsed
              return `${label}: ${formatValue(value)}`
            }
          }
        }
      },
      scales: createScalesConfig(),
      onClick: handleChartClick,
      onHover: handleChartHover
    }
  }

  // Chart type specific configurations
  switch (props.chart.type) {
    case 'pie':
    case 'donut':
      delete config.options.scales
      break
    case 'gauge':
      config.options.circumference = 180
      config.options.rotation = 270
      break
  }

  return config
}

const createScalesConfig = () => {
  if (['pie', 'donut', 'gauge'].includes(props.chart.type)) {
    return undefined
  }

  return {
    x: {
      display: true,
      grid: {
        display: props.chart.settings.showGrid,
        color: 'rgba(107, 114, 128, 0.1)'
      },
      ticks: {
        color: '#6B7280',
        font: {
          size: props.chart.settings.fontSize || 11
        }
      }
    },
    y: {
      display: true,
      grid: {
        display: props.chart.settings.showGrid,
        color: 'rgba(107, 114, 128, 0.1)'
      },
      ticks: {
        color: '#6B7280',
        font: {
          size: props.chart.settings.fontSize || 11
        },
        callback: function(value: any) {
          return formatValue(value)
        }
      }
    }
  }
}

const handleChartClick = (event: any, elements: any[]) => {
  if (elements.length > 0) {
    const element = elements[0]
    const dataIndex = element.index
    const datasetIndex = element.datasetIndex
    
    console.log('Chart clicked:', {
      chartId: props.chart.id,
      dataIndex,
      datasetIndex,
      value: props.data?.datasets[datasetIndex]?.data[dataIndex]
    })
  }
}

const handleChartHover = (event: any, elements: any[]) => {
  if (chartCanvas.value) {
    chartCanvas.value.style.cursor = elements.length > 0 ? 'pointer' : 'default'
  }
}

const refreshChart = async () => {
  isLoading.value = true
  error.value = null
  
  try {
    // Emit refresh request to parent
    emit('update', props.chart.id, { updatedAt: new Date() })
  } catch (err: any) {
    error.value = err.message || 'Failed to refresh chart'
  } finally {
    isLoading.value = false
  }
}

const editChart = () => {
  showMenu.value = false
  // Emit edit request to parent
  console.log('Edit chart:', props.chart.id)
}

const duplicateChart = () => {
  showMenu.value = false
  // Emit duplicate request to parent
  console.log('Duplicate chart:', props.chart.id)
}

const exportChart = (format: string) => {
  showMenu.value = false
  emit('export', props.chart.id, format)
}

const generateChartInsights = () => {
  showMenu.value = false
  emit('generateInsights', props.chart.id)
}

const deleteChart = () => {
  showMenu.value = false
  if (confirm('Are you sure you want to delete this chart?')) {
    emit('delete', props.chart.id)
  }
}

const getPerformanceClass = (renderTime: number): string => {
  if (renderTime < 100) return 'bg-green-100 text-green-700'
  if (renderTime < 500) return 'bg-yellow-100 text-yellow-700'
  return 'bg-red-100 text-red-700'
}

const getInsightIcon = (type: string): string => {
  const icons = {
    trend: 'trending-up',
    anomaly: 'alert-triangle',
    correlation: 'link',
    pattern: 'target',
    recommendation: 'lightbulb'
  }
  return icons[type as keyof typeof icons] || 'info'
}

const getInsightIconClass = (type: string): string => {
  const classes = {
    trend: 'text-blue-500',
    anomaly: 'text-red-500',
    correlation: 'text-purple-500',
    pattern: 'text-green-500',
    recommendation: 'text-yellow-500'
  }
  return classes[type as keyof typeof classes] || 'text-gray-500'
}

const formatValue = (value: number): string => {
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`
  } else if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}K`
  }
  return value.toFixed(0)
}

const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

// Watch for data changes
watch(() => props.data, (newData) => {
  if (newData && chartInstance) {
    isUpdating.value = true
    chartInstance.data = newData
    chartInstance.update('active')
    
    setTimeout(() => {
      isUpdating.value = false
    }, 1000)
  }
}, { deep: true })

// Watch for chart config changes
watch(() => props.chart, () => {
  initChart()
}, { deep: true })

// Lifecycle
onMounted(() => {
  if (props.data) {
    initChart()
  }
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.destroy()
  }
})
</script>

<style scoped>
.chart-widget {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
}

.chart-widget.real-time-active {
  @apply ring-2 ring-green-200;
}

.chart-header {
  @apply flex items-center justify-between p-4 border-b border-gray-100;
}

.chart-actions {
  @apply flex items-center gap-2;
}

.realtime-indicator {
  @apply flex items-center gap-1 text-xs text-green-600;
}

.performance-indicator {
  @apply text-xs px-2 py-1 rounded;
}

.insights-badge {
  @apply bg-blue-100 text-blue-700 text-xs px-2 py-1 rounded-full cursor-pointer;
}

.chart-menu {
  @apply absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-10 min-w-[160px];
}

.menu-item {
  @apply w-full text-left px-4 py-2 text-sm hover:bg-gray-100 flex items-center gap-2 transition-colors;
}

.chart-content {
  @apply relative;
}

.loading-overlay {
  @apply absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10;
}

.error-state {
  @apply p-6 text-center;
}

.chart-canvas-container {
  @apply p-4;
}

.insights-panel {
  @apply absolute top-0 right-0 w-80 h-full bg-white border-l border-gray-200 shadow-lg z-20 overflow-y-auto;
}

.insights-header {
  @apply flex items-center justify-between p-4 border-b border-gray-100;
}

.insights-list {
  @apply p-4 space-y-3;
}

.insight-item {
  @apply p-3 bg-gray-50 rounded-lg;
}

.chart-footer {
  @apply px-4 py-3 bg-gray-50 border-t border-gray-100;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .insights-panel {
    @apply w-full;
  }
  
  .chart-header {
    @apply flex-col items-start gap-3;
  }
  
  .chart-actions {
    @apply w-full justify-end;
  }
}
</style>
