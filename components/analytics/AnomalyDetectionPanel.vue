<template>
  <div class="bg-white rounded-lg shadow p-6">
    <div class="flex items-center justify-between mb-6">
      <div>
        <h3 class="text-lg font-semibold text-gray-900">Advanced Anomaly Detection</h3>
        <p class="text-sm text-gray-600">
          Real-time anomaly detection with {{ selectedAlgorithm }} algorithm
        </p>
      </div>
      
      <div class="flex items-center space-x-3">
        <!-- Algorithm Selector -->
        <select 
          v-model="selectedAlgorithm" 
          @change="detectAnomalies"
          class="text-sm border border-gray-300 rounded-md px-3 py-1.5 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="ensemble">Ensemble</option>
          <option value="statistical">Statistical</option>
          <option value="isolation_forest">Isolation Forest</option>
          <option value="lstm_autoencoder">LSTM Autoencoder</option>
        </select>

        <!-- Refresh Button -->
        <button 
          @click="refreshAnomalies"
          :disabled="isLoading"
          class="px-3 py-1.5 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <Icon v-if="isLoading" name="heroicons:arrow-path" class="animate-spin w-4 h-4" />
          <Icon v-else name="heroicons:magnifying-glass" class="w-4 h-4" />
        </button>
      </div>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-4 gap-4 mb-6">
      <div class="bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="text-2xl font-bold text-red-600">
          {{ criticalAnomalies.length }}
        </div>
        <div class="text-sm text-red-700">Critical</div>
        <Icon name="heroicons:exclamation-triangle" class="w-4 h-4 text-red-500 mt-1" />
      </div>
      
      <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div class="text-2xl font-bold text-yellow-600">
          {{ highAnomalies.length }}
        </div>
        <div class="text-sm text-yellow-700">High</div>
        <Icon name="heroicons:exclamation-triangle" class="w-4 h-4 text-yellow-500 mt-1" />
      </div>
      
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="text-2xl font-bold text-blue-600">
          {{ mediumAnomalies.length }}
        </div>
        <div class="text-sm text-blue-700">Medium</div>
        <Icon name="heroicons:information-circle" class="w-4 h-4 text-blue-500 mt-1" />
      </div>
      
      <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <div class="text-2xl font-bold text-gray-600">
          {{ lowAnomalies.length }}
        </div>
        <div class="text-sm text-gray-700">Low</div>
        <Icon name="heroicons:information-circle" class="w-4 h-4 text-gray-500 mt-1" />
      </div>
    </div>

    <!-- Anomaly List -->
    <div class="space-y-4">
      <div 
        v-for="anomaly in sortedAnomalies" 
        :key="anomaly.timestamp.getTime()"
        class="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
        :class="{
          'border-red-200 bg-red-50': anomaly.severity === 'critical',
          'border-yellow-200 bg-yellow-50': anomaly.severity === 'high',
          'border-blue-200 bg-blue-50': anomaly.severity === 'medium',
          'border-gray-200': anomaly.severity === 'low'
        }"
      >
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <div class="flex items-center space-x-2 mb-2">
              <Icon 
                :name="getAnomalyIcon(anomaly.severity)" 
                :class="getAnomalyIconClass(anomaly.severity)"
                class="w-5 h-5"
              />
              <h4 class="font-medium text-gray-900">
                {{ anomaly.metric }} Anomaly
              </h4>
              <span 
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                :class="getSeverityBadgeClass(anomaly.severity)"
              >
                {{ anomaly.severity.toUpperCase() }}
              </span>
              <span class="text-xs text-gray-500">
                {{ anomaly.algorithm }}
              </span>
            </div>
            
            <div class="grid grid-cols-3 gap-4 mb-3">
              <div>
                <div class="text-sm text-gray-600">Detected Value</div>
                <div class="font-semibold text-gray-900">
                  {{ formatNumber(anomaly.value) }}
                </div>
              </div>
              <div>
                <div class="text-sm text-gray-600">Expected Value</div>
                <div class="font-semibold text-gray-700">
                  {{ formatNumber(anomaly.expectedValue) }}
                </div>
              </div>
              <div>
                <div class="text-sm text-gray-600">Anomaly Score</div>
                <div class="font-semibold" :class="getScoreClass(anomaly.anomalyScore)">
                  {{ (anomaly.anomalyScore * 100).toFixed(1) }}%
                </div>
              </div>
            </div>
            
            <div class="flex items-center justify-between text-sm">
              <div class="text-gray-500">
                Detected {{ formatRelativeTime(anomaly.timestamp) }} • 
                Confidence: {{ Math.round(anomaly.confidence * 100) }}%
              </div>
              <div class="flex items-center space-x-2">
                <span 
                  class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium"
                  :class="getTypeClass(anomaly.anomalyType)"
                >
                  {{ anomaly.anomalyType.replace('_', ' ').toUpperCase() }}
                </span>
              </div>
            </div>
          </div>
          
          <div class="flex items-center space-x-2 ml-4">
            <button
              @click="showAnomalyDetails(anomaly)"
              class="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              title="View Details"
            >
              <Icon name="heroicons:eye" class="w-4 h-4" />
            </button>
            <button
              @click="acknowledgeAnomaly(anomaly)"
              class="p-2 text-gray-400 hover:text-green-600 transition-colors"
              title="Acknowledge"
            >
              <Icon name="heroicons:check" class="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div 
      v-if="!isLoading && anomalies.length === 0" 
      class="text-center py-12"
    >
      <Icon name="heroicons:shield-check" class="w-12 h-12 text-green-500 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 mb-2">No Anomalies Detected</h3>
      <p class="text-gray-600">
        All metrics are within expected ranges. The system is operating normally.
      </p>
    </div>

    <!-- Loading State -->
    <div 
      v-if="isLoading" 
      class="flex items-center justify-center py-12"
    >
      <div class="flex items-center space-x-2">
        <Icon name="heroicons:arrow-path" class="animate-spin w-5 h-5 text-blue-600" />
        <span class="text-sm text-gray-600">Detecting anomalies...</span>
      </div>
    </div>

    <!-- Error State -->
    <div v-if="error" class="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
      <div class="flex items-center space-x-2">
        <Icon name="heroicons:exclamation-triangle" class="w-5 h-5 text-red-500" />
        <span class="text-sm text-red-700">{{ error }}</span>
      </div>
    </div>
  </div>

  <!-- Anomaly Details Modal -->
  <div 
    v-if="selectedAnomaly" 
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    @click="closeAnomalyDetails"
  >
    <div 
      class="bg-white rounded-lg p-6 max-w-2xl w-full mx-4"
      @click.stop
    >
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">
          Anomaly Details - {{ selectedAnomaly.metric }}
        </h3>
        <button 
          @click="closeAnomalyDetails"
          class="text-gray-400 hover:text-gray-600"
        >
          <Icon name="heroicons:x-mark" class="w-5 h-5" />
        </button>
      </div>

      <div class="grid grid-cols-2 gap-6 mb-6">
        <div>
          <h4 class="font-medium text-gray-900 mb-2">Detection Details</h4>
          <dl class="space-y-2 text-sm">
            <div class="flex justify-between">
              <dt class="text-gray-600">Algorithm:</dt>
              <dd class="font-medium text-gray-900">{{ selectedAnomaly.algorithm }}</dd>
            </div>
            <div class="flex justify-between">
              <dt class="text-gray-600">Type:</dt>
              <dd class="font-medium text-gray-900">{{ selectedAnomaly.anomalyType }}</dd>
            </div>
            <div class="flex justify-between">
              <dt class="text-gray-600">Severity:</dt>
              <dd :class="getSeverityTextClass(selectedAnomaly.severity)">
                {{ selectedAnomaly.severity.toUpperCase() }}
              </dd>
            </div>
            <div class="flex justify-between">
              <dt class="text-gray-600">Confidence:</dt>
              <dd class="font-medium text-gray-900">
                {{ Math.round(selectedAnomaly.confidence * 100) }}%
              </dd>
            </div>
          </dl>
        </div>

        <div>
          <h4 class="font-medium text-gray-900 mb-2">Value Analysis</h4>
          <dl class="space-y-2 text-sm">
            <div class="flex justify-between">
              <dt class="text-gray-600">Detected Value:</dt>
              <dd class="font-medium text-gray-900">
                {{ formatNumber(selectedAnomaly.value) }}
              </dd>
            </div>
            <div class="flex justify-between">
              <dt class="text-gray-600">Expected Value:</dt>
              <dd class="font-medium text-gray-700">
                {{ formatNumber(selectedAnomaly.expectedValue) }}
              </dd>
            </div>
            <div class="flex justify-between">
              <dt class="text-gray-600">Deviation:</dt>
              <dd class="font-medium text-red-600">
                {{ Math.abs(selectedAnomaly.value - selectedAnomaly.expectedValue).toFixed(2) }}
              </dd>
            </div>
            <div class="flex justify-between">
              <dt class="text-gray-600">Score:</dt>
              <dd :class="getScoreClass(selectedAnomaly.anomalyScore)">
                {{ (selectedAnomaly.anomalyScore * 100).toFixed(1) }}%
              </dd>
            </div>
          </dl>
        </div>
      </div>

      <div v-if="selectedAnomaly.context" class="mb-4">
        <h4 class="font-medium text-gray-900 mb-2">Context Information</h4>
        <pre class="text-xs bg-gray-100 p-3 rounded-md overflow-auto">{{ JSON.stringify(selectedAnomaly.context, null, 2) }}</pre>
      </div>

      <div class="flex justify-end space-x-3">
        <button
          @click="closeAnomalyDetails"
          class="px-4 py-2 text-sm text-gray-600 hover:text-gray-800"
        >
          Close
        </button>
        <button
          @click="acknowledgeAnomaly(selectedAnomaly)"
          class="px-4 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700"
        >
          Acknowledge
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import type { AnomalyDetectionResult } from '~/types/analytics'

// Composables
const { 
  detectAdvancedAnomalies, 
  advancedAnomalies, 
  updateAnomalyAlgorithm,
  isLoading, 
  error 
} = usePredictiveAnalytics()

// Reactive state
const selectedAlgorithm = ref<'statistical' | 'isolation_forest' | 'lstm_autoencoder' | 'ensemble'>('ensemble')
const selectedAnomaly = ref<AnomalyDetectionResult | null>(null)

// Computed properties
const anomalies = computed(() => advancedAnomalies.value)

const criticalAnomalies = computed(() => 
  anomalies.value.filter(a => a.severity === 'critical')
)

const highAnomalies = computed(() => 
  anomalies.value.filter(a => a.severity === 'high')
)

const mediumAnomalies = computed(() => 
  anomalies.value.filter(a => a.severity === 'medium')
)

const lowAnomalies = computed(() => 
  anomalies.value.filter(a => a.severity === 'low')
)

const sortedAnomalies = computed(() => {
  return [...anomalies.value].sort((a, b) => {
    // Sort by severity first, then by anomaly score
    const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 }
    const severityDiff = severityOrder[b.severity] - severityOrder[a.severity]
    if (severityDiff !== 0) return severityDiff
    
    return b.anomalyScore - a.anomalyScore
  })
})

// Methods
const detectAnomalies = async () => {
  updateAnomalyAlgorithm(selectedAlgorithm.value)
  await detectAdvancedAnomalies(['views', 'downloads', 'qr_scans'], selectedAlgorithm.value)
}

const refreshAnomalies = async () => {
  await detectAnomalies()
}

const showAnomalyDetails = (anomaly: AnomalyDetectionResult) => {
  selectedAnomaly.value = anomaly
}

const closeAnomalyDetails = () => {
  selectedAnomaly.value = null
}

const acknowledgeAnomaly = async (anomaly: AnomalyDetectionResult) => {
  // Remove from local list (in real implementation, this would update the database)
  const index = advancedAnomalies.value.findIndex(a => 
    a.timestamp.getTime() === anomaly.timestamp.getTime() && a.metric === anomaly.metric
  )
  if (index !== -1) {
    advancedAnomalies.value.splice(index, 1)
  }
  closeAnomalyDetails()
}

const getAnomalyIcon = (severity: string): string => {
  switch (severity) {
    case 'critical': return 'heroicons:exclamation-triangle'
    case 'high': return 'heroicons:exclamation-triangle'
    case 'medium': return 'heroicons:information-circle'
    case 'low': return 'heroicons:information-circle'
    default: return 'heroicons:information-circle'
  }
}

const getAnomalyIconClass = (severity: string): string => {
  switch (severity) {
    case 'critical': return 'text-red-500'
    case 'high': return 'text-yellow-500'
    case 'medium': return 'text-blue-500'
    case 'low': return 'text-gray-500'
    default: return 'text-gray-500'
  }
}

const getSeverityBadgeClass = (severity: string): string => {
  switch (severity) {
    case 'critical': return 'bg-red-100 text-red-800'
    case 'high': return 'bg-yellow-100 text-yellow-800'
    case 'medium': return 'bg-blue-100 text-blue-800'
    case 'low': return 'bg-gray-100 text-gray-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getSeverityTextClass = (severity: string): string => {
  switch (severity) {
    case 'critical': return 'font-medium text-red-600'
    case 'high': return 'font-medium text-yellow-600'
    case 'medium': return 'font-medium text-blue-600'
    case 'low': return 'font-medium text-gray-600'
    default: return 'font-medium text-gray-600'
  }
}

const getScoreClass = (score: number): string => {
  if (score >= 0.8) return 'text-red-600 font-semibold'
  if (score >= 0.6) return 'text-yellow-600 font-semibold'
  if (score >= 0.4) return 'text-blue-600 font-semibold'
  return 'text-gray-600'
}

const getTypeClass = (type: string): string => {
  switch (type) {
    case 'point': return 'bg-purple-100 text-purple-800'
    case 'contextual': return 'bg-indigo-100 text-indigo-800'
    case 'collective': return 'bg-pink-100 text-pink-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const formatNumber = (value: number): string => {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value)
}

const formatRelativeTime = (date: Date): string => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)

  if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`
  if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`
  return `${minutes} minute${minutes > 1 ? 's' : ''} ago`
}

// Lifecycle hooks
onMounted(() => {
  detectAnomalies()
})
</script>

<style scoped>
/* Custom scrollbar for pre element */
pre::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

pre::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

pre::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

pre::-webkit-scrollbar-thumb:hover {
  background: #555;
}
</style>