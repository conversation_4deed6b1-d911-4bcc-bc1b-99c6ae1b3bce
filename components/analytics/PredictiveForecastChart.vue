<template>
  <div class="bg-white rounded-lg shadow p-6">
    <div class="flex items-center justify-between mb-6">
      <div>
        <h3 class="text-lg font-semibold text-gray-900">{{ title }}</h3>
        <p class="text-sm text-gray-600">{{ subtitle }}</p>
      </div>
      
      <div class="flex items-center space-x-3">
        <!-- Algorithm Selector -->
        <select 
          v-model="selectedAlgorithm" 
          @change="updateForecast"
          class="text-sm border border-gray-300 rounded-md px-3 py-1.5 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="ensemble">Ensemble</option>
          <option value="arima">ARIMA</option>
          <option value="exponential_smoothing">Exp. Smoothing</option>
          <option value="neural_network">Neural Network</option>
        </select>

        <!-- Scenario Selector -->
        <select 
          v-model="selectedScenario" 
          @change="updateForecast"
          class="text-sm border border-gray-300 rounded-md px-3 py-1.5 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="conservative">Conservative</option>
          <option value="moderate">Moderate</option>
          <option value="optimistic">Optimistic</option>
        </select>

        <!-- Refresh Button -->
        <button 
          @click="refreshForecast"
          :disabled="isLoading"
          class="px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <Icon v-if="isLoading" name="heroicons:arrow-path" class="animate-spin w-4 h-4" />
          <Icon v-else name="heroicons:arrow-path" class="w-4 h-4" />
        </button>
      </div>
    </div>

    <!-- Forecast Metrics -->
    <div v-if="forecast" class="grid grid-cols-4 gap-4 mb-6">
      <div class="bg-gray-50 rounded-lg p-4">
        <div class="text-2xl font-bold text-gray-900">
          {{ formatCurrency(forecast.predictedRevenue) }}
        </div>
        <div class="text-sm text-gray-600">Predicted {{ metricLabel }}</div>
        <div class="text-xs text-green-600 mt-1">
          {{ Math.round(forecast.confidence * 100) }}% confidence
        </div>
      </div>
      
      <div class="bg-gray-50 rounded-lg p-4">
        <div class="text-2xl font-bold text-gray-700">
          {{ formatCurrency(forecast.lowerBound) }}
        </div>
        <div class="text-sm text-gray-600">Lower Bound</div>
        <div class="text-xs text-gray-500 mt-1">95% confidence</div>
      </div>
      
      <div class="bg-gray-50 rounded-lg p-4">
        <div class="text-2xl font-bold text-gray-700">
          {{ formatCurrency(forecast.upperBound) }}
        </div>
        <div class="text-sm text-gray-600">Upper Bound</div>
        <div class="text-xs text-gray-500 mt-1">95% confidence</div>
      </div>
      
      <div class="bg-gray-50 rounded-lg p-4">
        <div class="text-2xl font-bold text-blue-600">
          {{ Math.round(forecast.modelAccuracy * 100) }}%
        </div>
        <div class="text-sm text-gray-600">Model Accuracy</div>
        <div class="text-xs text-gray-500 mt-1">{{ forecast.algorithm }}</div>
      </div>
    </div>

    <!-- Chart Container -->
    <div class="relative h-80">
      <canvas 
        ref="chartCanvas" 
        class="absolute inset-0 w-full h-full"
        :class="{ 'opacity-50': isLoading }"
      ></canvas>
      
      <!-- Loading Overlay -->
      <div 
        v-if="isLoading" 
        class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 rounded-lg"
      >
        <div class="flex items-center space-x-2">
          <Icon name="heroicons:arrow-path" class="animate-spin w-5 h-5 text-blue-600" />
          <span class="text-sm text-gray-600">Generating forecast...</span>
        </div>
      </div>
    </div>

    <!-- Key Factors -->
    <div v-if="forecast && forecast.factors.length > 0" class="mt-6">
      <h4 class="text-sm font-medium text-gray-900 mb-3">Key Contributing Factors</h4>
      <div class="grid grid-cols-2 gap-4">
        <div 
          v-for="factor in forecast.factors" 
          :key="factor.factor"
          class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
        >
          <div class="flex items-center space-x-2">
            <div 
              class="w-3 h-3 rounded-full"
              :class="{
                'bg-green-500': factor.trend === 'positive',
                'bg-red-500': factor.trend === 'negative',
                'bg-gray-500': factor.trend === 'neutral'
              }"
            ></div>
            <span class="text-sm font-medium text-gray-900">{{ factor.factor }}</span>
          </div>
          <div class="text-right">
            <div class="text-sm font-semibold text-gray-900">
              {{ Math.round(factor.contribution * 100) }}%
            </div>
            <div class="text-xs text-gray-500">
              Impact: {{ factor.importance.toFixed(1) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Error State -->
    <div v-if="error" class="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
      <div class="flex items-center space-x-2">
        <Icon name="heroicons:exclamation-triangle" class="w-5 h-5 text-red-500" />
        <span class="text-sm text-red-700">{{ error }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed, nextTick } from 'vue'
import { Chart, registerables } from 'chart.js'
import type { RevenueForecast } from '~/types/analytics'

// Register Chart.js components
Chart.register(...registerables)

interface Props {
  title?: string
  subtitle?: string
  metricLabel?: string
  initialScenario?: 'conservative' | 'moderate' | 'optimistic'
  initialAlgorithm?: 'arima' | 'exponential_smoothing' | 'neural_network' | 'ensemble'
  periods?: number
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Revenue Forecast',
  subtitle: 'Predictive analysis with confidence intervals',
  metricLabel: 'Revenue',
  initialScenario: 'moderate',
  initialAlgorithm: 'ensemble',
  periods: 30
})

// Composables
const { generateRevenueForecast, isLoading, error } = usePredictiveAnalytics()

// Reactive state
const chartCanvas = ref<HTMLCanvasElement>()
const chart = ref<Chart | null>(null)
const forecast = ref<RevenueForecast | null>(null)
const selectedScenario = ref(props.initialScenario)
const selectedAlgorithm = ref(props.initialAlgorithm)

// Computed properties
const chartData = computed(() => {
  if (!forecast.value) return null

  // Generate historical data (placeholder)
  const historicalDates = Array.from({ length: 30 }, (_, i) => {
    const date = new Date()
    date.setDate(date.getDate() - (29 - i))
    return date.toLocaleDateString()
  })

  const historicalValues = Array.from({ length: 30 }, () => 
    Math.random() * 2000 + 1000
  )

  // Generate forecast dates
  const forecastDates = Array.from({ length: props.periods }, (_, i) => {
    const date = new Date()
    date.setDate(date.getDate() + i + 1)
    return date.toLocaleDateString()
  })

  // Generate forecast values based on the forecast
  const dailyForecast = forecast.value.predictedRevenue / props.periods
  const forecastValues = Array.from({ length: props.periods }, (_, i) => 
    dailyForecast * (1 + (Math.random() - 0.5) * 0.2)
  )

  const upperBoundValues = forecastValues.map(v => v * 1.2)
  const lowerBoundValues = forecastValues.map(v => v * 0.8)

  return {
    labels: [...historicalDates, ...forecastDates],
    datasets: [
      {
        label: 'Historical',
        data: [...historicalValues, ...Array(props.periods).fill(null)],
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        borderWidth: 2,
        pointRadius: 2,
        pointHoverRadius: 4,
        tension: 0.1
      },
      {
        label: 'Forecast',
        data: [...Array(30).fill(null), ...forecastValues],
        borderColor: 'rgb(16, 185, 129)',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        borderWidth: 2,
        borderDash: [5, 5],
        pointRadius: 2,
        pointHoverRadius: 4,
        tension: 0.1
      },
      {
        label: 'Upper Bound',
        data: [...Array(30).fill(null), ...upperBoundValues],
        borderColor: 'rgba(16, 185, 129, 0.3)',
        backgroundColor: 'rgba(16, 185, 129, 0.05)',
        borderWidth: 1,
        borderDash: [2, 2],
        pointRadius: 0,
        fill: '+1'
      },
      {
        label: 'Lower Bound',
        data: [...Array(30).fill(null), ...lowerBoundValues],
        borderColor: 'rgba(16, 185, 129, 0.3)',
        backgroundColor: 'rgba(16, 185, 129, 0.05)',
        borderWidth: 1,
        borderDash: [2, 2],
        pointRadius: 0,
        fill: false
      }
    ]
  }
})

// Methods
const initializeChart = () => {
  if (!chartCanvas.value || !chartData.value) return

  if (chart.value) {
    chart.value.destroy()
  }

  chart.value = new Chart(chartCanvas.value, {
    type: 'line',
    data: chartData.value,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      interaction: {
        intersect: false,
        mode: 'index'
      },
      scales: {
        x: {
          display: true,
          title: {
            display: true,
            text: 'Time Period'
          },
          grid: {
            display: false
          }
        },
        y: {
          display: true,
          title: {
            display: true,
            text: props.metricLabel
          },
          beginAtZero: false,
          grid: {
            color: 'rgba(0, 0, 0, 0.1)'
          },
          ticks: {
            callback: function(value) {
              return formatCurrency(value as number)
            }
          }
        }
      },
      plugins: {
        legend: {
          display: true,
          position: 'top',
          labels: {
            filter: function(legendItem) {
              // Hide upper and lower bound from legend
              return !legendItem.text?.includes('Bound')
            }
          }
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              const label = context.dataset.label || ''
              const value = formatCurrency(context.parsed.y)
              return `${label}: ${value}`
            }
          }
        }
      },
      elements: {
        point: {
          hoverRadius: 6
        }
      }
    }
  })
}

const updateChart = () => {
  if (chart.value && chartData.value) {
    chart.value.data = chartData.value
    chart.value.update('active')
  }
}

const updateForecast = async () => {
  try {
    const newForecast = await generateRevenueForecast(
      selectedScenario.value,
      props.periods,
      selectedAlgorithm.value
    )
    forecast.value = newForecast
  } catch (err) {
    console.error('Failed to update forecast:', err)
  }
}

const refreshForecast = async () => {
  await updateForecast()
}

const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value)
}

// Lifecycle hooks
onMounted(async () => {
  // Load initial forecast
  await updateForecast()
  
  // Initialize chart after DOM update
  await nextTick()
  initializeChart()
})

// Watchers
watch(chartData, () => {
  updateChart()
}, { deep: true })

watch([selectedScenario, selectedAlgorithm], () => {
  updateForecast()
})
</script>

<style scoped>
.chart-container {
  position: relative;
  height: 320px;
}
</style>