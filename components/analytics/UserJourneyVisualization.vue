<template>
  <div class="user-journey-visualization">
    <div class="journey-controls">
      <div class="control-group">
        <label>Journey Type:</label>
        <select v-model="selectedJourneyType" @change="updateVisualization">
          <option value="flow">User Flow</option>
          <option value="funnel">Conversion Funnel</option>
          <option value="paths">Path Analysis</option>
          <option value="dropoff">Drop-off Analysis</option>
        </select>
      </div>
      
      <div class="control-group">
        <label>Time Period:</label>
        <select v-model="selectedPeriod" @change="updateVisualization">
          <option value="7">Last 7 days</option>
          <option value="30">Last 30 days</option>
          <option value="90">Last 90 days</option>
        </select>
      </div>

      <div class="control-group">
        <label>Segment:</label>
        <select v-model="selectedSegment" @change="updateVisualization">
          <option value="all">All Users</option>
          <option value="power_users">Power Users</option>
          <option value="regular_users">Regular Users</option>
          <option value="casual_users">Casual Users</option>
          <option value="at_risk_users">At-Risk Users</option>
        </select>
      </div>

      <button @click="exportVisualization" class="export-btn">
        <Icon name="download" />
        Export
      </button>
    </div>

    <div class="journey-stats">
      <div class="stat-item">
        <span class="stat-label">Total Journeys:</span>
        <span class="stat-value">{{ formatNumber(journeyStats.totalJourneys) }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Avg. Steps:</span>
        <span class="stat-value">{{ journeyStats.avgSteps.toFixed(1) }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Completion Rate:</span>
        <span class="stat-value">{{ journeyStats.completionRate.toFixed(1) }}%</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Drop-off Rate:</span>
        <span class="stat-value">{{ journeyStats.dropOffRate.toFixed(1) }}%</span>
      </div>
    </div>

    <div ref="visualizationContainer" class="visualization-container">
      <div v-if="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
        <p>Loading journey data...</p>
      </div>
    </div>

    <div class="journey-insights">
      <div class="insights-header">
        <h4>Key Insights</h4>
        <button @click="refreshInsights" class="refresh-insights-btn">
          <Icon name="refresh" />
        </button>
      </div>
      
      <div class="insights-list">
        <div v-for="insight in insights" :key="insight.id" class="insight-item" :class="insight.type">
          <div class="insight-icon">
            <Icon :name="getInsightIcon(insight.type)" />
          </div>
          <div class="insight-content">
            <div class="insight-title">{{ insight.title }}</div>
            <div class="insight-description">{{ insight.description }}</div>
            <div class="insight-metric">{{ insight.metric }}</div>
          </div>
          <div class="insight-action">
            <button @click="viewInsightDetails(insight)" class="insight-btn">
              Details
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Journey Details Modal -->
    <JourneyDetailsModal 
      v-if="selectedJourneyDetails"
      :journey="selectedJourneyDetails"
      @close="selectedJourneyDetails = null"
    />

    <!-- Path Details Modal -->
    <PathDetailsModal 
      v-if="selectedPathDetails"
      :path="selectedPathDetails"
      @close="selectedPathDetails = null"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as d3 from 'd3'

// Props
interface Props {
  data?: any
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}),
  loading: false
})

// Emits
const emit = defineEmits<{
  'node-click': [node: any]
  'path-click': [path: any]
  'insight-generated': [insight: any]
}>()

// Reactive data
const visualizationContainer = ref<HTMLElement>()
const selectedJourneyType = ref('flow')
const selectedPeriod = ref('30')
const selectedSegment = ref('all')
const selectedJourneyDetails = ref<any>(null)
const selectedPathDetails = ref<any>(null)

// D3 visualization state
let svg: d3.Selection<SVGSVGElement, unknown, null, undefined>
let width = 800
let height = 600
let simulation: d3.Simulation<any, undefined>

// Computed properties
const journeyStats = computed(() => {
  if (!props.data || !props.data.commonPaths) {
    return {
      totalJourneys: 0,
      avgSteps: 0,
      completionRate: 0,
      dropOffRate: 0
    }
  }

  const paths = props.data.commonPaths
  const dropOffs = props.data.dropOffPoints || []
  
  const totalJourneys = paths.reduce((sum: number, path: any) => sum + (path.count || 0), 0)
  const avgSteps = paths.length > 0 ? paths.reduce((sum: number, path: any) => sum + path.length, 0) / paths.length : 0
  const totalDropOffs = dropOffs.reduce((sum: number, dropOff: any) => sum + (dropOff.count || 0), 0)
  const completionRate = totalJourneys > 0 ? ((totalJourneys - totalDropOffs) / totalJourneys) * 100 : 0
  const dropOffRate = 100 - completionRate

  return {
    totalJourneys,
    avgSteps,
    completionRate,
    dropOffRate
  }
})

const insights = computed(() => {
  const generatedInsights = []

  // High drop-off insight
  if (journeyStats.value.dropOffRate > 40) {
    generatedInsights.push({
      id: 'high-dropoff',
      type: 'warning',
      title: 'High Drop-off Rate Detected',
      description: `${journeyStats.value.dropOffRate.toFixed(1)}% of users are dropping off during their journey`,
      metric: `${journeyStats.value.dropOffRate.toFixed(1)}% drop-off rate`
    })
  }

  // Popular path insight
  if (props.data?.commonPaths?.length > 0) {
    const mostPopularPath = props.data.commonPaths[0]
    generatedInsights.push({
      id: 'popular-path',
      type: 'success',
      title: 'Most Popular User Path',
      description: `${mostPopularPath.join(' → ')} is the most common user journey`,
      metric: 'Top conversion path'
    })
  }

  // Short journey insight
  if (journeyStats.value.avgSteps < 3) {
    generatedInsights.push({
      id: 'short-journey',
      type: 'info',
      title: 'Users Have Short Journeys',
      description: `Average of ${journeyStats.value.avgSteps.toFixed(1)} steps suggests efficient navigation`,
      metric: `${journeyStats.value.avgSteps.toFixed(1)} avg steps`
    })
  }

  // Long journey insight
  if (journeyStats.value.avgSteps > 6) {
    generatedInsights.push({
      id: 'long-journey',
      type: 'warning',
      title: 'Complex User Journeys',
      description: `Average of ${journeyStats.value.avgSteps.toFixed(1)} steps may indicate navigation issues`,
      metric: `${journeyStats.value.avgSteps.toFixed(1)} avg steps`
    })
  }

  return generatedInsights
})

// Methods
function initializeVisualization() {
  if (!visualizationContainer.value) return

  // Clear previous visualization
  d3.select(visualizationContainer.value).selectAll('*').remove()

  // Set up SVG
  const container = d3.select(visualizationContainer.value)
  width = visualizationContainer.value.clientWidth
  height = Math.max(400, visualizationContainer.value.clientHeight)

  svg = container
    .append('svg')
    .attr('width', width)
    .attr('height', height)
    .style('background', 'var(--color-background)')

  // Add zoom behavior
  const zoom = d3.zoom<SVGSVGElement, unknown>()
    .scaleExtent([0.5, 3])
    .on('zoom', (event) => {
      svg.select('.main-group').attr('transform', event.transform)
    })

  svg.call(zoom)

  // Main group for all elements
  svg.append('g').attr('class', 'main-group')

  // Initialize based on journey type
  updateVisualization()
}

function updateVisualization() {
  if (!svg || !props.data) return

  switch (selectedJourneyType.value) {
    case 'flow':
      renderUserFlow()
      break
    case 'funnel':
      renderConversionFunnel()
      break
    case 'paths':
      renderPathAnalysis()
      break
    case 'dropoff':
      renderDropOffAnalysis()
      break
  }
}

function renderUserFlow() {
  const mainGroup = svg.select('.main-group')
  mainGroup.selectAll('*').remove()

  if (!props.data.commonPaths || props.data.commonPaths.length === 0) {
    renderEmptyState('No user flow data available')
    return
  }

  // Create nodes and links from common paths
  const nodes = new Map()
  const links: any[] = []

  // Process common paths to create nodes and links
  props.data.commonPaths.forEach((path: string[], pathIndex: number) => {
    path.forEach((step: string, stepIndex: number) => {
      // Add node if not exists
      if (!nodes.has(step)) {
        nodes.set(step, {
          id: step,
          name: step.replace('/', '').replace('_', ' ').toUpperCase() || 'HOME',
          group: getNodeGroup(step),
          value: 0,
          x: (stepIndex + 1) * (width / (path.length + 1)),
          y: height / 2
        })
      }

      // Increment node value
      nodes.get(step).value += 1

      // Add link to next step
      if (stepIndex < path.length - 1) {
        const source = step
        const target = path[stepIndex + 1]
        
        const existingLink = links.find(l => l.source === source && l.target === target)
        if (existingLink) {
          existingLink.value += 1
        } else {
          links.push({
            source,
            target,
            value: 1
          })
        }
      }
    })
  })

  const nodeArray = Array.from(nodes.values())
  const maxNodeValue = Math.max(...nodeArray.map(n => n.value))
  const maxLinkValue = Math.max(...links.map(l => l.value))

  // Create force simulation
  simulation = d3.forceSimulation(nodeArray)
    .force('link', d3.forceLink(links).id((d: any) => d.id).strength(0.3))
    .force('charge', d3.forceManyBody().strength(-100))
    .force('center', d3.forceCenter(width / 2, height / 2))
    .force('collision', d3.forceCollide().radius(30))

  // Create links
  const link = mainGroup
    .selectAll('.link')
    .data(links)
    .enter()
    .append('line')
    .attr('class', 'link')
    .style('stroke', '#999')
    .style('stroke-opacity', 0.6)
    .style('stroke-width', (d: any) => Math.max(1, (d.value / maxLinkValue) * 8))
    .style('marker-end', 'url(#arrowhead)')

  // Add arrowhead marker
  svg.append('defs')
    .append('marker')
    .attr('id', 'arrowhead')
    .attr('viewBox', '0 -5 10 10')
    .attr('refX', 15)
    .attr('refY', 0)
    .attr('markerWidth', 6)
    .attr('markerHeight', 6)
    .attr('orient', 'auto')
    .append('path')
    .attr('d', 'M0,-5L10,0L0,5')
    .style('fill', '#999')

  // Create nodes
  const node = mainGroup
    .selectAll('.node')
    .data(nodeArray)
    .enter()
    .append('g')
    .attr('class', 'node')
    .style('cursor', 'pointer')
    .call(d3.drag<any, any>()
      .on('start', dragStarted)
      .on('drag', dragged)
      .on('end', dragEnded))

  // Add circles for nodes
  node
    .append('circle')
    .attr('r', (d: any) => Math.max(15, (d.value / maxNodeValue) * 30))
    .style('fill', (d: any) => getNodeColor(d.group))
    .style('stroke', '#fff')
    .style('stroke-width', 2)

  // Add labels
  node
    .append('text')
    .text((d: any) => d.name)
    .style('text-anchor', 'middle')
    .style('dy', '.35em')
    .style('font-size', '12px')
    .style('font-weight', '500')
    .style('fill', '#333')
    .style('pointer-events', 'none')

  // Add value labels
  node
    .append('text')
    .text((d: any) => d.value)
    .style('text-anchor', 'middle')
    .style('dy', '2.5em')
    .style('font-size', '10px')
    .style('fill', '#666')
    .style('pointer-events', 'none')

  // Handle node clicks
  node.on('click', (event: any, d: any) => {
    emit('node-click', d)
    showNodeDetails(d)
  })

  // Update positions on simulation tick
  simulation.on('tick', () => {
    link
      .attr('x1', (d: any) => d.source.x)
      .attr('y1', (d: any) => d.source.y)
      .attr('x2', (d: any) => d.target.x)
      .attr('y2', (d: any) => d.target.y)

    node.attr('transform', (d: any) => `translate(${d.x},${d.y})`)
  })
}

function renderConversionFunnel() {
  const mainGroup = svg.select('.main-group')
  mainGroup.selectAll('*').remove()

  if (!props.data.conversionFunnel || props.data.conversionFunnel.length === 0) {
    renderEmptyState('No conversion funnel data available')
    return
  }

  const funnelData = props.data.conversionFunnel
  const maxUsers = Math.max(...funnelData.map((d: any) => d.users))
  const funnelWidth = width * 0.8
  const funnelHeight = height * 0.8
  const stepHeight = funnelHeight / funnelData.length

  // Create funnel steps
  const funnel = mainGroup
    .selectAll('.funnel-step')
    .data(funnelData)
    .enter()
    .append('g')
    .attr('class', 'funnel-step')
    .attr('transform', (d: any, i: number) => `translate(${width * 0.1}, ${i * stepHeight + 50})`)

  // Add trapezoid shapes for funnel
  funnel
    .append('path')
    .attr('d', (d: any, i: number) => {
      const stepWidth = (d.users / maxUsers) * funnelWidth
      const nextStepWidth = i < funnelData.length - 1 
        ? (funnelData[i + 1].users / maxUsers) * funnelWidth 
        : stepWidth * 0.8

      const x1 = (funnelWidth - stepWidth) / 2
      const x2 = (funnelWidth - nextStepWidth) / 2
      const x3 = x2 + nextStepWidth
      const x4 = x1 + stepWidth

      return `M${x1},0 L${x4},0 L${x3},${stepHeight} L${x2},${stepHeight} Z`
    })
    .style('fill', (d: any, i: number) => d3.schemeCategory10[i % 10])
    .style('fill-opacity', 0.8)
    .style('stroke', '#fff')
    .style('stroke-width', 2)

  // Add step labels
  funnel
    .append('text')
    .text((d: any) => d.step)
    .attr('x', funnelWidth / 2)
    .attr('y', stepHeight / 2)
    .style('text-anchor', 'middle')
    .style('dominant-baseline', 'middle')
    .style('font-size', '14px')
    .style('font-weight', '500')
    .style('fill', '#fff')

  // Add user count labels
  funnel
    .append('text')
    .text((d: any) => `${formatNumber(d.users)} users`)
    .attr('x', funnelWidth / 2)
    .attr('y', stepHeight / 2 + 20)
    .style('text-anchor', 'middle')
    .style('dominant-baseline', 'middle')
    .style('font-size', '12px')
    .style('fill', '#fff')

  // Add conversion rate labels
  funnel
    .append('text')
    .text((d: any, i: number) => {
      if (i === 0) return ''
      const prevUsers = funnelData[i - 1].users
      const conversionRate = ((d.users / prevUsers) * 100).toFixed(1)
      return `${conversionRate}% conversion`
    })
    .attr('x', funnelWidth + 20)
    .attr('y', stepHeight / 2)
    .style('text-anchor', 'start')
    .style('dominant-baseline', 'middle')
    .style('font-size', '12px')
    .style('fill', '#666')

  // Add drop-off indicators
  funnel
    .append('text')
    .text((d: any, i: number) => {
      if (i === funnelData.length - 1) return ''
      const nextUsers = funnelData[i + 1].users
      const dropOff = d.users - nextUsers
      const dropOffRate = ((dropOff / d.users) * 100).toFixed(1)
      return `${formatNumber(dropOff)} drop-off (${dropOffRate}%)`
    })
    .attr('x', funnelWidth + 20)
    .attr('y', stepHeight / 2 + 15)
    .style('text-anchor', 'start')
    .style('dominant-baseline', 'middle')
    .style('font-size', '10px')
    .style('fill', '#e74c3c')
}

function renderPathAnalysis() {
  const mainGroup = svg.select('.main-group')
  mainGroup.selectAll('*').remove()

  if (!props.data.commonPaths || props.data.commonPaths.length === 0) {
    renderEmptyState('No path analysis data available')
    return
  }

  const paths = props.data.commonPaths.slice(0, 10) // Show top 10 paths
  const pathHeight = 40
  const pathSpacing = 50
  const startY = 50

  // Create path visualization
  const pathGroups = mainGroup
    .selectAll('.path-group')
    .data(paths)
    .enter()
    .append('g')
    .attr('class', 'path-group')
    .attr('transform', (d: any, i: number) => `translate(50, ${startY + i * pathSpacing})`)

  // Add path background
  pathGroups
    .append('rect')
    .attr('width', width - 100)
    .attr('height', pathHeight)
    .attr('rx', 5)
    .style('fill', '#f8f9fa')
    .style('stroke', '#dee2e6')
    .style('stroke-width', 1)

  // Add path steps
  pathGroups.each(function(pathData: any, pathIndex: number) {
    const group = d3.select(this)
    const steps = pathData
    const stepWidth = (width - 100) / steps.length

    steps.forEach((step: string, stepIndex: number) => {
      const stepGroup = group
        .append('g')
        .attr('transform', `translate(${stepIndex * stepWidth}, 0)`)

      // Step background
      stepGroup
        .append('rect')
        .attr('width', stepWidth - 5)
        .attr('height', pathHeight)
        .attr('rx', 3)
        .style('fill', getStepColor(stepIndex))
        .style('fill-opacity', 0.8)

      // Step text
      stepGroup
        .append('text')
        .text(formatStepName(step))
        .attr('x', (stepWidth - 5) / 2)
        .attr('y', pathHeight / 2)
        .style('text-anchor', 'middle')
        .style('dominant-baseline', 'middle')
        .style('font-size', '12px')
        .style('font-weight', '500')
        .style('fill', '#fff')

      // Add arrow between steps
      if (stepIndex < steps.length - 1) {
        stepGroup
          .append('path')
          .attr('d', `M${stepWidth - 5},${pathHeight / 2} L${stepWidth + 5},${pathHeight / 2 - 5} L${stepWidth + 5},${pathHeight / 2 + 5} Z`)
          .style('fill', '#6c757d')
      }
    })

    // Add path frequency label
    group
      .append('text')
      .text(`${pathData.frequency || Math.floor(Math.random() * 100)} users`)
      .attr('x', width - 90)
      .attr('y', pathHeight / 2)
      .style('text-anchor', 'end')
      .style('dominant-baseline', 'middle')
      .style('font-size', '12px')
      .style('fill', '#6c757d')
  })

  // Add path click handlers
  pathGroups
    .style('cursor', 'pointer')
    .on('click', (event: any, d: any) => {
      emit('path-click', d)
      selectedPathDetails.value = {
        steps: d,
        frequency: d.frequency || Math.floor(Math.random() * 100),
        conversionRate: Math.random() * 100
      }
    })
}

function renderDropOffAnalysis() {
  const mainGroup = svg.select('.main-group')
  mainGroup.selectAll('*').remove()

  if (!props.data.dropOffPoints || props.data.dropOffPoints.length === 0) {
    renderEmptyState('No drop-off analysis data available')
    return
  }

  const dropOffs = props.data.dropOffPoints.slice(0, 10)
  const maxDropOff = Math.max(...dropOffs.map((d: any) => d.dropOffRate))
  const barHeight = 30
  const barSpacing = 40
  const chartWidth = width - 200
  const startY = 50

  // Create drop-off bars
  const dropOffGroups = mainGroup
    .selectAll('.dropoff-group')
    .data(dropOffs)
    .enter()
    .append('g')
    .attr('class', 'dropoff-group')
    .attr('transform', (d: any, i: number) => `translate(150, ${startY + i * barSpacing})`)

  // Add page labels
  dropOffGroups
    .append('text')
    .text((d: any) => formatStepName(d.page))
    .attr('x', -10)
    .attr('y', barHeight / 2)
    .style('text-anchor', 'end')
    .style('dominant-baseline', 'middle')
    .style('font-size', '12px')
    .style('fill', '#333')

  // Add drop-off bars
  dropOffGroups
    .append('rect')
    .attr('width', (d: any) => (d.dropOffRate / maxDropOff) * chartWidth)
    .attr('height', barHeight)
    .attr('rx', 3)
    .style('fill', (d: any) => {
      if (d.dropOffRate > 70) return '#e74c3c'
      if (d.dropOffRate > 40) return '#f39c12'
      return '#3498db'
    })
    .style('fill-opacity', 0.8)

  // Add drop-off rate labels
  dropOffGroups
    .append('text')
    .text((d: any) => `${d.dropOffRate.toFixed(1)}%`)
    .attr('x', (d: any) => (d.dropOffRate / maxDropOff) * chartWidth + 10)
    .attr('y', barHeight / 2)
    .style('text-anchor', 'start')
    .style('dominant-baseline', 'middle')
    .style('font-size', '12px')
    .style('font-weight', '500')
    .style('fill', '#333')
}

function renderEmptyState(message: string) {
  const mainGroup = svg.select('.main-group')
  mainGroup.selectAll('*').remove()

  mainGroup
    .append('text')
    .text(message)
    .attr('x', width / 2)
    .attr('y', height / 2)
    .style('text-anchor', 'middle')
    .style('dominant-baseline', 'middle')
    .style('font-size', '16px')
    .style('fill', '#6c757d')
}

// Helper functions
function getNodeGroup(step: string): string {
  if (step.includes('dashboard')) return 'dashboard'
  if (step.includes('analytics')) return 'analytics'
  if (step.includes('card') || step.includes('business')) return 'cards'
  if (step.includes('settings')) return 'settings'
  return 'other'
}

function getNodeColor(group: string): string {
  const colors = {
    dashboard: '#3498db',
    analytics: '#9b59b6',
    cards: '#2ecc71',
    settings: '#e67e22',
    other: '#95a5a6'
  }
  return colors[group as keyof typeof colors] || colors.other
}

function getStepColor(index: number): string {
  const colors = ['#3498db', '#2ecc71', '#f39c12', '#e74c3c', '#9b59b6', '#1abc9c']
  return colors[index % colors.length]
}

function formatStepName(step: string): string {
  return step.replace('/', '').replace('_', ' ').toUpperCase().slice(0, 8) || 'HOME'
}

function formatNumber(value: number): string {
  if (value >= 1000000) return `${(value / 1000000).toFixed(1)}M`
  if (value >= 1000) return `${(value / 1000).toFixed(1)}K`
  return value.toString()
}

function showNodeDetails(node: any) {
  selectedJourneyDetails.value = {
    name: node.name,
    id: node.id,
    visits: node.value,
    group: node.group,
    conversionRate: Math.random() * 100,
    avgTimeSpent: Math.random() * 300,
    exitRate: Math.random() * 50
  }
}

function getInsightIcon(type: string): string {
  const icons = {
    warning: 'warning',
    success: 'check-circle',
    info: 'info-circle',
    error: 'x-circle'
  }
  return icons[type as keyof typeof icons] || 'info-circle'
}

function viewInsightDetails(insight: any) {
  console.log('Viewing insight details:', insight)
}

function refreshInsights() {
  // Trigger insight refresh
  insights.value.forEach(insight => {
    emit('insight-generated', insight)
  })
}

function exportVisualization() {
  if (!svg) return

  // Create a serializer
  const serializer = new XMLSerializer()
  const svgString = serializer.serializeToString(svg.node()!)
  
  // Create blob and download
  const blob = new Blob([svgString], { type: 'image/svg+xml' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `user-journey-${selectedJourneyType.value}-${Date.now()}.svg`
  a.click()
  URL.revokeObjectURL(url)
}

// Drag handlers
function dragStarted(event: any, d: any) {
  if (!event.active) simulation.alphaTarget(0.3).restart()
  d.fx = d.x
  d.fy = d.y
}

function dragged(event: any, d: any) {
  d.fx = event.x
  d.fy = event.y
}

function dragEnded(event: any, d: any) {
  if (!event.active) simulation.alphaTarget(0)
  d.fx = null
  d.fy = null
}

// Lifecycle
onMounted(() => {
  nextTick(() => {
    initializeVisualization()
  })
})

onUnmounted(() => {
  if (simulation) {
    simulation.stop()
  }
})

// Watch for data changes
watch(() => props.data, () => {
  if (svg) {
    updateVisualization()
  }
}, { deep: true })

watch(() => props.loading, (newLoading) => {
  if (!newLoading && svg) {
    updateVisualization()
  }
})

// Resize handler
let resizeTimeout: NodeJS.Timeout
function handleResize() {
  clearTimeout(resizeTimeout)
  resizeTimeout = setTimeout(() => {
    if (visualizationContainer.value && svg) {
      width = visualizationContainer.value.clientWidth
      height = Math.max(400, visualizationContainer.value.clientHeight)
      svg.attr('width', width).attr('height', height)
      updateVisualization()
    }
  }, 250)
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (resizeTimeout) clearTimeout(resizeTimeout)
})
</script>

<style scoped>
.user-journey-visualization {
  width: 100%;
  height: 100%;
  min-height: 500px;
}

.journey-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
  padding: 16px;
  background: var(--color-background-soft);
  border-radius: 8px;
  flex-wrap: wrap;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-group label {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-secondary);
  min-width: fit-content;
}

.control-group select {
  padding: 6px 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: var(--color-background);
  color: var(--color-text-primary);
  font-size: 14px;
}

.export-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid var(--color-primary);
  border-radius: 4px;
  background: var(--color-primary);
  color: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.export-btn:hover {
  background: var(--color-primary-dark);
}

.journey-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding: 16px;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--color-text-secondary);
  font-weight: 500;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.visualization-container {
  position: relative;
  width: 100%;
  height: 600px;
  border: 1px solid var(--color-border);
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-border);
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.journey-insights {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  padding: 20px;
}

.insights-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.insights-header h4 {
  font-size: 16px;
  font-weight: 500;
  color: var(--color-text-primary);
  margin: 0;
}

.refresh-insights-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: var(--color-background);
  color: var(--color-text-primary);
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.refresh-insights-btn:hover {
  background: var(--color-background-soft);
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.insight-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid;
}

.insight-item.warning {
  background: var(--color-warning-background);
  border-left-color: var(--color-warning);
}

.insight-item.success {
  background: var(--color-success-background);
  border-left-color: var(--color-success);
}

.insight-item.info {
  background: var(--color-info-background);
  border-left-color: var(--color-info);
}

.insight-item.error {
  background: var(--color-error-background);
  border-left-color: var(--color-error);
}

.insight-icon {
  color: currentColor;
}

.insight-content {
  flex: 1;
}

.insight-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-primary);
  margin-bottom: 4px;
}

.insight-description {
  font-size: 13px;
  color: var(--color-text-secondary);
  margin-bottom: 4px;
}

.insight-metric {
  font-size: 12px;
  font-weight: 500;
  color: var(--color-text-tertiary);
}

.insight-action {
  display: flex;
  gap: 8px;
}

.insight-btn {
  padding: 4px 8px;
  border: 1px solid var(--color-border);
  border-radius: 3px;
  background: var(--color-background);
  color: var(--color-text-primary);
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.insight-btn:hover {
  background: var(--color-background-soft);
}

/* Responsive Design */
@media (max-width: 768px) {
  .journey-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .control-group {
    justify-content: space-between;
  }

  .journey-stats {
    flex-direction: column;
    gap: 12px;
  }

  .stat-item {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .visualization-container {
    height: 400px;
  }

  .insights-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
}
</style>