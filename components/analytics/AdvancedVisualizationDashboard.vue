<template>
  <div class="advanced-visualization-dashboard">
    <!-- Dashboard Header -->
    <div class="dashboard-header bg-white border-b border-gray-200 px-6 py-4">
      <div class="flex items-center justify-between">
        <div class="header-info">
          <h1 class="text-2xl font-bold text-gray-900">Advanced Analytics Dashboard</h1>
          <p class="text-gray-600 mt-1">Interactive data visualization and insights</p>
        </div>
        
        <div class="header-actions flex items-center gap-3">
          <!-- Dashboard Selector -->
          <select 
            v-model="selectedDashboardId"
            @change="loadSelectedDashboard"
            class="dashboard-selector px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Select Dashboard</option>
            <option 
              v-for="dashboard in dashboards"
              :key="dashboard.id"
              :value="dashboard.id"
            >
              {{ dashboard.name }}
            </option>
          </select>
          
          <!-- Global Filters Toggle -->
          <button
            @click="showGlobalFilters = !showGlobalFilters"
            :class="[
              'filter-toggle px-4 py-2 rounded-lg flex items-center gap-2 transition-colors',
              showGlobalFilters 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            ]"
          >
            <Icon name="filter" class="w-4 h-4" />
            Filters
            <span v-if="activeFiltersCount > 0" class="filter-count bg-white text-blue-600 text-xs px-2 py-0.5 rounded-full">
              {{ activeFiltersCount }}
            </span>
          </button>
          
          <!-- Export Options -->
          <div class="relative">
            <button
              @click="showExportMenu = !showExportMenu"
              class="export-btn bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
            >
              <Icon name="download" class="w-4 h-4" />
              Export
            </button>
            
            <!-- Export Menu -->
            <div 
              v-if="showExportMenu"
              v-click-outside="() => showExportMenu = false"
              class="export-menu absolute right-0 top-full mt-2 bg-white border border-gray-200 rounded-lg shadow-lg py-2 z-20 min-w-[200px]"
            >
              <button 
                @click="exportDashboard('png')"
                class="menu-item w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
              >
                <Icon name="image" class="w-4 h-4" />
                Export as PNG
              </button>
              <button 
                @click="exportDashboard('pdf')"
                class="menu-item w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
              >
                <Icon name="file-text" class="w-4 h-4" />
                Export as PDF
              </button>
              <button 
                @click="generateDashboardReport"
                class="menu-item w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
              >
                <Icon name="file-plus" class="w-4 h-4" />
                Generate Report
              </button>
              <div class="border-t border-gray-100 my-1"></div>
              <button 
                @click="exportAllData"
                class="menu-item w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
              >
                <Icon name="database" class="w-4 h-4" />
                Export All Data
              </button>
            </div>
          </div>
          
          <!-- Add Chart Button -->
          <button
            @click="showChartCreator = true"
            class="add-chart-btn bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
          >
            <Icon name="plus" class="w-4 h-4" />
            Add Chart
          </button>
          
          <!-- Dashboard Settings -->
          <button
            @click="showDashboardSettings = true"
            class="settings-btn p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <Icon name="settings" class="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>

    <!-- Global Filters Panel -->
    <div 
      v-if="showGlobalFilters"
      class="global-filters-panel bg-gray-50 border-b border-gray-200 px-6 py-4"
    >
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">Global Filters</h3>
        <div class="filter-actions flex items-center gap-2">
          <button
            @click="clearAllFilters"
            class="clear-filters-btn text-sm text-gray-600 hover:text-gray-800"
          >
            Clear All
          </button>
          <button
            @click="saveFilterPreset"
            class="save-preset-btn text-sm text-blue-600 hover:text-blue-800"
          >
            Save Preset
          </button>
        </div>
      </div>
      
      <div class="filters-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- TODO: Implement GlobalFilterComponent
        <GlobalFilterComponent
          v-for="filter in globalFilters"
          :key="filter.id"
          :filter="filter"
          @update="updateGlobalFilter"
        />
        -->
        <div class="text-center text-gray-500 col-span-full py-4">
          Global filters coming soon...
        </div>
      </div>
    </div>

    <!-- Dashboard Content -->
    <div class="dashboard-content flex-1 p-6">
      <!-- Loading State -->
      <div v-if="isLoading" class="loading-state flex items-center justify-center py-12">
        <div class="text-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p class="text-gray-600 mt-4">Loading dashboard...</p>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="error-state bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <div class="flex items-center">
          <Icon name="alert-circle" class="w-5 h-5 text-red-500 mr-2" />
          <span class="text-red-700">{{ error }}</span>
        </div>
      </div>

      <!-- Empty Dashboard -->
      <div v-else-if="!currentDashboard || dashboardCharts.length === 0" class="empty-dashboard text-center py-12">
        <Icon name="bar-chart" class="w-16 h-16 text-gray-300 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">No Charts Available</h3>
        <p class="text-gray-600 mb-6">
          {{ currentDashboard ? 'Add charts to start visualizing your data' : 'Select or create a dashboard to get started' }}
        </p>
        <div class="empty-actions flex items-center justify-center gap-4">
          <button 
            @click="showChartCreator = true"
            class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            Create Your First Chart
          </button>
          <button 
            @click="showDashboardCreator = true"
            class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            Create Dashboard
          </button>
        </div>
      </div>

      <!-- Charts Grid -->
      <div v-else class="charts-grid">
        <div 
          class="grid gap-6"
          :class="getGridClass()"
        >
          <ChartWidget
            v-for="chart in dashboardCharts"
            :key="chart.id"
            :chart="chart"
            :data="chart.data"
            :insights="chart.insights"
            :performance="chart.performance"
            :real-time="isRealTimeEnabled"
            @update="updateChart"
            @delete="deleteChart"
            @export="exportChart"
            @generate-insights="generateInsights"
            @resize="handleChartResize"
          />
        </div>
      </div>
    </div>

    <!-- Performance Monitor -->
    <div 
      v-if="showPerformanceMonitor"
      class="performance-monitor fixed bottom-4 right-4 bg-white border border-gray-200 rounded-lg shadow-lg p-4 z-30"
    >
      <div class="flex items-center justify-between mb-2">
        <h4 class="text-sm font-medium text-gray-900">Performance</h4>
        <button
          @click="showPerformanceMonitor = false"
          class="text-gray-400 hover:text-gray-600"
        >
          <Icon name="x" class="w-4 h-4" />
        </button>
      </div>
      <div class="performance-metrics space-y-2">
        <div class="metric flex justify-between text-xs">
          <span class="text-gray-600">Avg Render Time:</span>
          <span class="font-medium">{{ averageRenderTime }}ms</span>
        </div>
        <div class="metric flex justify-between text-xs">
          <span class="text-gray-600">Memory Usage:</span>
          <span class="font-medium">{{ memoryUsage }}MB</span>
        </div>
        <div class="metric flex justify-between text-xs">
          <span class="text-gray-600">Active Charts:</span>
          <span class="font-medium">{{ dashboardCharts.length }}</span>
        </div>
      </div>
    </div>

    <!-- Modals -->
    <!-- TODO: Implement ChartCreatorModal
    <ChartCreatorModal
      v-if="showChartCreator"
      :templates="chartTemplates"
      @close="showChartCreator = false"
      @created="handleChartCreated"
    />
    -->

    <!-- TODO: Implement missing modal components
    <DashboardCreatorModal
      v-if="showDashboardCreator"
      @close="showDashboardCreator = false"
      @created="handleDashboardCreated"
    />

    <DashboardSettingsModal
      v-if="showDashboardSettings"
      :dashboard="currentDashboard"
      @close="showDashboardSettings = false"
      @updated="handleDashboardUpdated"
    />

    <ReportGeneratorModal
      v-if="showReportGenerator"
      :dashboard="currentDashboard"
      :charts="dashboardCharts"
      @close="showReportGenerator = false"
      @generated="handleReportGenerated"
    />
    -->

    <!-- Real-time Status Indicator -->
    <div 
      v-if="isRealTimeEnabled"
      class="realtime-indicator fixed top-4 right-4 bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium z-40 flex items-center gap-2"
    >
      <div class="w-2 h-2 bg-white rounded-full animate-pulse"></div>
      Live Updates
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { Icon } from '#components'
import type { ChartConfig, DashboardLayout, GlobalFilter, ExportOptions, ReportOptions } from '~/types/visualization'

// Import child components
import ChartWidget from './ChartWidget.vue'
// TODO: Create missing components
// import GlobalFilterComponent from './GlobalFilterComponent.vue'
// import ChartCreatorModal from './modals/ChartCreatorModal.vue'
// import DashboardCreatorModal from './modals/DashboardCreatorModal.vue'
// import DashboardSettingsModal from './modals/DashboardSettingsModal.vue'
// import ReportGeneratorModal from './modals/ReportGeneratorModal.vue'

// Use the visualization composable
const {
  // State
  isLoading,
  error,
  isExporting,
  isGeneratingReport,
  
  // Data
  charts,
  chartData,
  dashboards,
  currentDashboard,
  globalFilters,
  chartTemplates,
  insights,
  performanceMetrics,
  
  // Computed
  dashboardCharts,
  
  // Methods
  createChart,
  loadChartData,
  updateChart,
  deleteChart,
  createDashboard,
  loadDashboard,
  applyGlobalFilters,
  exportChart,
  generateReport,
  subscribeToRealTimeUpdates,
  loadChartTemplates,
  generateInsights
} = useVisualization()

// Local state
const selectedDashboardId = ref<string>('')
const showGlobalFilters = ref(false)
const showExportMenu = ref(false)
const showChartCreator = ref(false)
const showDashboardCreator = ref(false)
const showDashboardSettings = ref(false)
const showReportGenerator = ref(false)
const showPerformanceMonitor = ref(false)
const isRealTimeEnabled = ref(true)

// Computed properties
const activeFiltersCount = computed(() => {
  return globalFilters.value.filter(f => f.active && f.value !== null && f.value !== '').length
})

const averageRenderTime = computed(() => {
  const metrics = Array.from(performanceMetrics.value.values())
  if (metrics.length === 0) return 0
  
  const total = metrics.reduce((sum, metric) => sum + metric.renderTime, 0)
  return Math.round(total / metrics.length)
})

const memoryUsage = computed(() => {
  const metrics = Array.from(performanceMetrics.value.values())
  if (metrics.length === 0) return 0
  
  const total = metrics.reduce((sum, metric) => sum + metric.memoryUsage, 0)
  return Math.round(total / 1024 / 1024) // Convert to MB
})

// Methods
const loadSelectedDashboard = async () => {
  if (selectedDashboardId.value) {
    await loadDashboard(selectedDashboardId.value)
    
    // Subscribe to real-time updates for all charts
    if (isRealTimeEnabled.value) {
      dashboardCharts.value.forEach(chart => {
        subscribeToRealTimeUpdates(chart.id)
      })
    }
  }
}

const getGridClass = () => {
  const chartCount = dashboardCharts.value.length
  
  if (chartCount === 1) return 'grid-cols-1'
  if (chartCount === 2) return 'grid-cols-1 lg:grid-cols-2'
  if (chartCount <= 4) return 'grid-cols-1 lg:grid-cols-2'
  return 'grid-cols-1 lg:grid-cols-2 xl:grid-cols-3'
}

const updateGlobalFilter = (filterId: string, value: any) => {
  const filter = globalFilters.value.find(f => f.id === filterId)
  if (filter) {
    filter.value = value
    filter.active = value !== null && value !== ''
  }
}

const clearAllFilters = () => {
  globalFilters.value.forEach(filter => {
    filter.value = null
    filter.active = false
  })
}

const saveFilterPreset = () => {
  // Implementation would save current filter state as preset
  console.log('Saving filter preset...')
}

const exportDashboard = async (format: 'png' | 'pdf') => {
  if (!currentDashboard.value) return
  
  const options: ExportOptions = {
    format,
    includeData: true,
    includeFilters: true,
    title: currentDashboard.value.name,
    width: 1920,
    height: 1080
  }
  
  try {
    const exportUrl = await exportChart(currentDashboard.value.id, options)
    // Download the exported file
    const link = document.createElement('a')
    link.href = exportUrl
    link.download = `${currentDashboard.value.name}.${format}`
    link.click()
  } catch (error) {
    console.error('Export failed:', error)
  }
}

const generateDashboardReport = () => {
  showReportGenerator.value = true
}

const exportAllData = async () => {
  if (!currentDashboard.value) return
  
  const options: ExportOptions = {
    format: 'csv',
    includeData: true,
    includeFilters: true
  }
  
  try {
    const exportUrl = await exportChart(currentDashboard.value.id, options)
    const link = document.createElement('a')
    link.href = exportUrl
    link.download = `${currentDashboard.value.name}_data.csv`
    link.click()
  } catch (error) {
    console.error('Data export failed:', error)
  }
}

const handleChartCreated = async (chart: ChartConfig) => {
  showChartCreator.value = false
  
  if (currentDashboard.value) {
    currentDashboard.value.charts.push(chart)
    await loadChartData(chart.id)
    
    if (isRealTimeEnabled.value) {
      subscribeToRealTimeUpdates(chart.id)
    }
  }
}

const handleDashboardCreated = async (dashboard: DashboardLayout) => {
  showDashboardCreator.value = false
  dashboards.value.push(dashboard)
  selectedDashboardId.value = dashboard.id
  await loadSelectedDashboard()
}

const handleDashboardUpdated = (dashboard: DashboardLayout) => {
  showDashboardSettings.value = false
  const index = dashboards.value.findIndex(d => d.id === dashboard.id)
  if (index !== -1) {
    dashboards.value[index] = dashboard
  }
}

const handleReportGenerated = (reportUrl: string) => {
  showReportGenerator.value = false
  
  // Download the report
  const link = document.createElement('a')
  link.href = reportUrl
  link.download = `${currentDashboard.value?.name || 'dashboard'}_report.pdf`
  link.click()
}

const handleChartResize = (chartId: string, newSize: { width: number; height: number }) => {
  const chart = charts.value.get(chartId)
  if (chart) {
    chart.position.width = newSize.width
    chart.position.height = newSize.height
    updateChart(chartId, { position: chart.position })
  }
}

// Lifecycle
onMounted(() => {
  loadChartTemplates()
  
  // Load default dashboard if available
  if (dashboards.value.length > 0) {
    const defaultDashboard = dashboards.value.find(d => d.isDefault) || dashboards.value[0]
    selectedDashboardId.value = defaultDashboard.id
    loadSelectedDashboard()
  }
})

// Watch for global filter changes
watch(globalFilters, (newFilters) => {
  if (newFilters.some(f => f.active)) {
    applyGlobalFilters(newFilters)
  }
}, { deep: true })
</script>

<style scoped>
.advanced-visualization-dashboard {
  @apply flex flex-col h-full bg-gray-50;
}

.dashboard-header {
  @apply bg-white border-b border-gray-200 px-6 py-4;
}

.header-actions {
  @apply flex items-center gap-3;
}

.dashboard-selector {
  @apply px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.filter-toggle {
  @apply px-4 py-2 rounded-lg flex items-center gap-2 transition-colors;
}

.filter-count {
  @apply bg-white text-blue-600 text-xs px-2 py-0.5 rounded-full;
}

.export-menu {
  @apply absolute right-0 top-full mt-2 bg-white border border-gray-200 rounded-lg shadow-lg py-2 z-20 min-w-[200px];
}

.menu-item {
  @apply w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2 transition-colors;
}

.global-filters-panel {
  @apply bg-gray-50 border-b border-gray-200 px-6 py-4;
}

.filters-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4;
}

.dashboard-content {
  @apply flex-1 p-6;
}

.charts-grid {
  @apply w-full;
}

.performance-monitor {
  @apply fixed bottom-4 right-4 bg-white border border-gray-200 rounded-lg shadow-lg p-4 z-30;
}

.realtime-indicator {
  @apply fixed top-4 right-4 bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium z-40 flex items-center gap-2;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .header-actions {
    @apply flex-wrap gap-2;
  }
  
  .filters-grid {
    @apply grid-cols-1;
  }
  
  .performance-monitor {
    @apply bottom-2 right-2 text-xs;
  }
}
</style>
