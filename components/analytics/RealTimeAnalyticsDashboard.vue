<template>
  <div class="real-time-analytics-dashboard">
    <!-- Header with Live Status -->
    <div class="dashboard-header">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-2xl font-bold text-gray-800 dark:text-white">
            Real-Time Analytics
          </h2>
          <p class="text-sm text-gray-600 dark:text-gray-400">
            Live data updates every 5 seconds
          </p>
        </div>
        
        <div class="flex items-center space-x-4">
          <!-- Live Status Indicator -->
          <div class="flex items-center">
            <div 
              class="w-3 h-3 rounded-full mr-2"
              :class="isLive ? 'bg-green-500 animate-pulse' : 'bg-red-500'"
            ></div>
            <span class="text-sm font-medium">
              {{ isLive ? 'LIVE' : 'OFFLINE' }}
            </span>
          </div>
          
          <!-- Last Updated -->
          <div class="text-sm text-gray-500">
            Updated: {{ formatTime(lastUpdated) }}
          </div>
          
          <!-- Settings Button -->
          <button
            @click="showSettings = !showSettings"
            class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Key Metrics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Active Users -->
      <div class="metric-card">
        <div class="metric-header">
          <h3>Active Users</h3>
          <div class="metric-trend" :class="getTrendClass(usersTrend)">
            {{ formatTrend(usersTrend) }}
          </div>
        </div>
        <div class="metric-value">{{ formatNumber(realTimeData.activeUsers) }}</div>
        <div class="metric-sparkline">
          <canvas ref="usersSparkline" width="120" height="30"></canvas>
        </div>
      </div>

      <!-- Page Views -->
      <div class="metric-card">
        <div class="metric-header">
          <h3>Page Views</h3>
          <div class="metric-trend" :class="getTrendClass(pageViewsTrend)">
            {{ formatTrend(pageViewsTrend) }}
          </div>
        </div>
        <div class="metric-value">{{ formatNumber(realTimeData.pageViews) }}</div>
        <div class="metric-sparkline">
          <canvas ref="pageViewsSparkline" width="120" height="30"></canvas>
        </div>
      </div>

      <!-- Events -->
      <div class="metric-card">
        <div class="metric-header">
          <h3>Events</h3>
          <div class="metric-trend" :class="getTrendClass(eventsTrend)">
            {{ formatTrend(eventsTrend) }}
          </div>
        </div>
        <div class="metric-value">{{ formatNumber(realTimeData.events) }}</div>
        <div class="metric-sparkline">
          <canvas ref="eventsSparkline" width="120" height="30"></canvas>
        </div>
      </div>

      <!-- Conversions -->
      <div class="metric-card">
        <div class="metric-header">
          <h3>Conversions</h3>
          <div class="metric-trend" :class="getTrendClass(conversionsTrend)">
            {{ formatTrend(conversionsTrend) }}
          </div>
        </div>
        <div class="metric-value">{{ formatNumber(realTimeData.conversions) }}</div>
        <div class="metric-sparkline">
          <canvas ref="conversionsSparkline" width="120" height="30"></canvas>
        </div>
      </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Real-Time Activity Chart -->
      <div class="chart-container">
        <div class="chart-header">
          <h3>Real-Time Activity</h3>
          <div class="chart-controls">
            <select v-model="activityTimeRange" class="control-select">
              <option value="1h">Last Hour</option>
              <option value="6h">Last 6 Hours</option>
              <option value="24h">Last 24 Hours</option>
            </select>
          </div>
        </div>
        <div class="chart-content">
          <canvas ref="activityChart" width="400" height="200"></canvas>
        </div>
      </div>

      <!-- Top Events Chart -->
      <div class="chart-container">
        <div class="chart-header">
          <h3>Top Events</h3>
          <div class="chart-controls">
            <button
              v-for="type in ['pie', 'bar']"
              :key="type"
              @click="topEventsChartType = type"
              class="control-button"
              :class="{ active: topEventsChartType === type }"
            >
              {{ type.toUpperCase() }}
            </button>
          </div>
        </div>
        <div class="chart-content">
          <canvas ref="topEventsChart" width="400" height="200"></canvas>
        </div>
      </div>
    </div>

    <!-- Data Tables Row -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
      <!-- Top Pages -->
      <div class="data-table-container">
        <h3>Top Pages</h3>
        <div class="data-table">
          <div 
            v-for="(page, index) in realTimeData.topPages" 
            :key="page.page"
            class="data-row"
          >
            <div class="rank">{{ index + 1 }}</div>
            <div class="item-name">{{ formatPageName(page.page) }}</div>
            <div class="item-value">{{ formatNumber(page.views) }}</div>
          </div>
        </div>
      </div>

      <!-- Device Breakdown -->
      <div class="data-table-container">
        <h3>Device Types</h3>
        <div class="data-table">
          <div 
            v-for="(count, device) in realTimeData.deviceBreakdown" 
            :key="device"
            class="data-row"
          >
            <div class="device-icon">
              <svg v-if="device === 'mobile'" class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M7 2h10a2 2 0 012 2v16a2 2 0 01-2 2H7a2 2 0 01-2-2V4a2 2 0 012-2zm0 2v16h10V4H7zm8 14a1 1 0 11-2 0 1 1 0 012 0z"/>
              </svg>
              <svg v-else-if="device === 'tablet'" class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M4 4h16a2 2 0 012 2v12a2 2 0 01-2 2H4a2 2 0 01-2-2V6a2 2 0 012-2zm0 2v12h16V6H4z"/>
              </svg>
              <svg v-else class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M4 6h16v10H4V6zm-2 0a2 2 0 012-2h16a2 2 0 012 2v10a2 2 0 01-2 2h-5v2h2a1 1 0 110 2H7a1 1 0 110-2h2v-2H4a2 2 0 01-2-2V6z"/>
              </svg>
            </div>
            <div class="item-name capitalize">{{ device }}</div>
            <div class="item-value">{{ formatNumber(count) }}</div>
            <div class="item-percentage">
              {{ calculatePercentage(count, getTotalDeviceCount()) }}%
            </div>
          </div>
        </div>
      </div>

      <!-- Geographic Breakdown -->
      <div class="data-table-container">
        <h3>Top Locations</h3>
        <div class="data-table">
          <div 
            v-for="(count, location) in topLocations" 
            :key="location"
            class="data-row"
          >
            <div class="country-flag">
              {{ getCountryFlag(location) }}
            </div>
            <div class="item-name">{{ location }}</div>
            <div class="item-value">{{ formatNumber(count) }}</div>
            <div class="item-percentage">
              {{ calculatePercentage(count, getTotalLocationCount()) }}%
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Settings Panel -->
    <div v-if="showSettings" class="settings-panel">
      <div class="settings-header">
        <h3>Dashboard Settings</h3>
        <button @click="showSettings = false" class="close-button">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      
      <div class="settings-content">
        <div class="setting-group">
          <label>Refresh Interval</label>
          <select v-model="refreshInterval" class="setting-select">
            <option :value="5000">5 seconds</option>
            <option :value="10000">10 seconds</option>
            <option :value="30000">30 seconds</option>
            <option :value="60000">1 minute</option>
          </select>
        </div>

        <div class="setting-group">
          <label>Privacy Mode</label>
          <div class="setting-toggle">
            <input 
              id="privacy-mode"
              v-model="privacyMode" 
              type="checkbox"
              class="toggle-input"
            >
            <label for="privacy-mode" class="toggle-label">
              Hide sensitive data
            </label>
          </div>
        </div>

        <div class="setting-group">
          <label>Auto-refresh</label>
          <div class="setting-toggle">
            <input 
              id="auto-refresh"
              v-model="autoRefresh" 
              type="checkbox"
              class="toggle-input"
            >
            <label for="auto-refresh" class="toggle-label">
              Enable auto-refresh
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading Overlay -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p>Loading real-time data...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { Chart, ChartConfiguration } from 'chart.js/auto'
import { useFirebaseAnalytics } from '../../composables/useFirebaseAnalytics'
import { usePrivacyCompliance } from '../../composables/usePrivacyCompliance'

// Props
interface Props {
  height?: string
  refreshInterval?: number
  showControls?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  height: '100vh',
  refreshInterval: 5000,
  showControls: true
})

// Composables
const { 
  realTimeData, 
  isRealTimeEnabled, 
  trackPageView,
  isLoading: analyticsLoading
} = useFirebaseAnalytics()

const { hasAnalyticsConsent } = usePrivacyCompliance()

// Reactive state
const isLoading = ref(true)
const isLive = ref(false)
const lastUpdated = ref(new Date())
const showSettings = ref(false)
const refreshInterval = ref(props.refreshInterval)
const privacyMode = ref(false)
const autoRefresh = ref(true)

// Chart settings
const activityTimeRange = ref('1h')
const topEventsChartType = ref<'pie' | 'bar'>('pie')

// Chart instances
const charts = ref<{ [key: string]: Chart }>({})

// Refs for canvas elements
const usersSparkline = ref<HTMLCanvasElement>()
const pageViewsSparkline = ref<HTMLCanvasElement>()
const eventsSparkline = ref<HTMLCanvasElement>()
const conversionsSparkline = ref<HTMLCanvasElement>()
const activityChart = ref<HTMLCanvasElement>()
const topEventsChart = ref<HTMLCanvasElement>()

// Historical data for trends
const historicalData = ref({
  users: [] as number[],
  pageViews: [] as number[],
  events: [] as number[],
  conversions: [] as number[]
})

// Computed properties
const usersTrend = computed(() => calculateTrend(historicalData.value.users))
const pageViewsTrend = computed(() => calculateTrend(historicalData.value.pageViews))
const eventsTrend = computed(() => calculateTrend(historicalData.value.events))
const conversionsTrend = computed(() => calculateTrend(historicalData.value.conversions))

const topLocations = computed(() => {
  const locations = realTimeData.value.locationBreakdown
  return Object.fromEntries(
    Object.entries(locations)
      .sort(([,a], [,b]) => (b as number) - (a as number))
      .slice(0, 5)
  )
})

// Auto-refresh timer
let refreshTimer: NodeJS.Timeout | null = null

/**
 * Initialize dashboard
 */
onMounted(async () => {
  if (!hasAnalyticsConsent()) {
    isLoading.value = false
    return
  }

  try {
    // Track dashboard view
    await trackPageView('real-time-analytics-dashboard')

    // Initialize charts
    await nextTick()
    initializeCharts()

    // Start real-time updates
    startRealTimeUpdates()

    isLive.value = true
    isLoading.value = false

  } catch (error) {
    console.error('Error initializing dashboard:', error)
    isLoading.value = false
  }
})

onUnmounted(() => {
  stopRealTimeUpdates()
  destroyCharts()
})

/**
 * Initialize all charts
 */
function initializeCharts() {
  // Initialize sparklines
  initializeSparklines()
  
  // Initialize main charts
  initializeActivityChart()
  initializeTopEventsChart()
}

/**
 * Initialize sparkline charts
 */
function initializeSparklines() {
  const sparklineOptions = {
    responsive: false,
    maintainAspectRatio: false,
    plugins: { legend: { display: false } },
    scales: {
      x: { display: false },
      y: { display: false }
    },
    elements: {
      point: { radius: 0 },
      line: { borderWidth: 2 }
    }
  }

  // Users sparkline
  if (usersSparkline.value) {
    charts.value.usersSparkline = new Chart(usersSparkline.value, {
      type: 'line',
      data: {
        labels: Array(20).fill(''),
        datasets: [{
          data: Array(20).fill(0),
          borderColor: '#3B82F6',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          fill: true
        }]
      },
      options: sparklineOptions
    })
  }

  // Page views sparkline
  if (pageViewsSparkline.value) {
    charts.value.pageViewsSparkline = new Chart(pageViewsSparkline.value, {
      type: 'line',
      data: {
        labels: Array(20).fill(''),
        datasets: [{
          data: Array(20).fill(0),
          borderColor: '#10B981',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          fill: true
        }]
      },
      options: sparklineOptions
    })
  }

  // Events sparkline
  if (eventsSparkline.value) {
    charts.value.eventsSparkline = new Chart(eventsSparkline.value, {
      type: 'line',
      data: {
        labels: Array(20).fill(''),
        datasets: [{
          data: Array(20).fill(0),
          borderColor: '#F59E0B',
          backgroundColor: 'rgba(245, 158, 11, 0.1)',
          fill: true
        }]
      },
      options: sparklineOptions
    })
  }

  // Conversions sparkline
  if (conversionsSparkline.value) {
    charts.value.conversionsSparkline = new Chart(conversionsSparkline.value, {
      type: 'line',
      data: {
        labels: Array(20).fill(''),
        datasets: [{
          data: Array(20).fill(0),
          borderColor: '#EF4444',
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          fill: true
        }]
      },
      options: sparklineOptions
    })
  }
}

/**
 * Initialize activity chart
 */
function initializeActivityChart() {
  if (!activityChart.value) return

  charts.value.activityChart = new Chart(activityChart.value, {
    type: 'line',
    data: {
      labels: [],
      datasets: [
        {
          label: 'Page Views',
          data: [],
          borderColor: '#3B82F6',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          fill: true
        },
        {
          label: 'Events',
          data: [],
          borderColor: '#10B981',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          fill: true
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      interaction: {
        intersect: false,
        mode: 'index'
      },
      plugins: {
        tooltip: {
          position: 'nearest'
        }
      },
      scales: {
        x: {
          display: true,
          title: {
            display: true,
            text: 'Time'
          }
        },
        y: {
          display: true,
          title: {
            display: true,
            text: 'Count'
          },
          beginAtZero: true
        }
      }
    }
  })
}

/**
 * Initialize top events chart
 */
function initializeTopEventsChart() {
  if (!topEventsChart.value) return

  const config: ChartConfiguration = {
    type: topEventsChartType.value,
    data: {
      labels: [],
      datasets: [{
        label: 'Events',
        data: [],
        backgroundColor: [
          '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
          '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6B7280'
        ]
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: topEventsChartType.value === 'pie',
          position: 'right'
        }
      }
    }
  }

  charts.value.topEventsChart = new Chart(topEventsChart.value, config)
}

/**
 * Update sparkline data
 */
function updateSparklines() {
  const data = realTimeData.value

  // Update historical data
  historicalData.value.users.push(data.activeUsers)
  historicalData.value.pageViews.push(data.pageViews)
  historicalData.value.events.push(data.events)
  historicalData.value.conversions.push(data.conversions)

  // Keep only last 20 data points
  const maxPoints = 20
  Object.keys(historicalData.value).forEach(key => {
    const array = historicalData.value[key as keyof typeof historicalData.value]
    if (array.length > maxPoints) {
      array.splice(0, array.length - maxPoints)
    }
  })

  // Update sparkline charts
  updateSparklineChart('usersSparkline', historicalData.value.users)
  updateSparklineChart('pageViewsSparkline', historicalData.value.pageViews)
  updateSparklineChart('eventsSparkline', historicalData.value.events)
  updateSparklineChart('conversionsSparkline', historicalData.value.conversions)
}

/**
 * Update individual sparkline chart
 */
function updateSparklineChart(chartKey: string, data: number[]) {
  const chart = charts.value[chartKey]
  if (chart) {
    chart.data.datasets[0].data = [...data]
    chart.update('none')
  }
}

/**
 * Update main charts
 */
function updateMainCharts() {
  updateActivityChart()
  updateTopEventsChart()
}

/**
 * Update activity chart
 */
function updateActivityChart() {
  const chart = charts.value.activityChart
  if (!chart) return

  // Generate time labels based on range
  const labels = generateTimeLabels(activityTimeRange.value)
  
  // Update chart data
  chart.data.labels = labels
  chart.data.datasets[0].data = generateActivityData(labels, 'pageViews')
  chart.data.datasets[1].data = generateActivityData(labels, 'events')
  
  chart.update()
}

/**
 * Update top events chart
 */
function updateTopEventsChart() {
  const chart = charts.value.topEventsChart
  if (!chart) return

  const topEvents = realTimeData.value.topEvents.slice(0, 8)
  
  chart.data.labels = topEvents.map(event => formatEventName(event.event))
  chart.data.datasets[0].data = topEvents.map(event => event.count)
  
  chart.update()
}

/**
 * Start real-time updates
 */
function startRealTimeUpdates() {
  if (!autoRefresh.value || !hasAnalyticsConsent()) return

  refreshTimer = setInterval(() => {
    updateDashboard()
  }, refreshInterval.value)
}

/**
 * Stop real-time updates
 */
function stopRealTimeUpdates() {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

/**
 * Update dashboard data
 */
function updateDashboard() {
  lastUpdated.value = new Date()
  updateSparklines()
  updateMainCharts()
}

/**
 * Destroy all charts
 */
function destroyCharts() {
  Object.values(charts.value).forEach(chart => {
    if (chart) {
      chart.destroy()
    }
  })
  charts.value = {}
}

// Utility functions

function formatNumber(num: number): string {
  if (privacyMode.value) return '***'
  
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

function formatTime(date: Date): string {
  return date.toLocaleTimeString()
}

function formatTrend(trend: number): string {
  if (Math.abs(trend) < 0.1) return '0.0%'
  const sign = trend > 0 ? '+' : ''
  return `${sign}${trend.toFixed(1)}%`
}

function getTrendClass(trend: number): string {
  if (trend > 0) return 'trend-up'
  if (trend < 0) return 'trend-down'
  return 'trend-neutral'
}

function calculateTrend(data: number[]): number {
  if (data.length < 2) return 0
  
  const current = data[data.length - 1]
  const previous = data[data.length - 2]
  
  if (previous === 0) return current > 0 ? 100 : 0
  return ((current - previous) / previous) * 100
}

function formatPageName(path: string): string {
  if (path === '/') return 'Home'
  return path.split('/').filter(Boolean).join(' > ').replace(/[-_]/g, ' ')
}

function formatEventName(event: string): string {
  return event.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

function calculatePercentage(value: number, total: number): string {
  if (total === 0) return '0'
  return ((value / total) * 100).toFixed(1)
}

function getTotalDeviceCount(): number {
  return Object.values(realTimeData.value.deviceBreakdown).reduce((sum, count) => sum + (count as number), 0)
}

function getTotalLocationCount(): number {
  return Object.values(realTimeData.value.locationBreakdown).reduce((sum, count) => sum + (count as number), 0)
}

function getCountryFlag(countryCode: string): string {
  // Simple country code to flag emoji mapping
  const flags: { [key: string]: string } = {
    'US': '🇺🇸', 'GB': '🇬🇧', 'CA': '🇨🇦', 'DE': '🇩🇪', 'FR': '🇫🇷',
    'JP': '🇯🇵', 'AU': '🇦🇺', 'BR': '🇧🇷', 'IN': '🇮🇳', 'IT': '🇮🇹'
  }
  return flags[countryCode] || '🌍'
}

function generateTimeLabels(range: string): string[] {
  const now = new Date()
  const labels: string[] = []
  
  let interval: number
  let count: number
  
  switch (range) {
    case '1h':
      interval = 5 * 60 * 1000 // 5 minutes
      count = 12
      break
    case '6h':
      interval = 30 * 60 * 1000 // 30 minutes
      count = 12
      break
    case '24h':
      interval = 2 * 60 * 60 * 1000 // 2 hours
      count = 12
      break
    default:
      interval = 5 * 60 * 1000
      count = 12
  }
  
  for (let i = count - 1; i >= 0; i--) {
    const time = new Date(now.getTime() - (i * interval))
    labels.push(time.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }))
  }
  
  return labels
}

function generateActivityData(labels: string[], type: string): number[] {
  // Generate sample data - in real implementation, this would come from the analytics service
  return labels.map(() => Math.floor(Math.random() * 100) + 50)
}

// Watchers
watch(refreshInterval, (newInterval) => {
  stopRealTimeUpdates()
  if (autoRefresh.value) {
    startRealTimeUpdates()
  }
})

watch(autoRefresh, (enabled) => {
  if (enabled) {
    startRealTimeUpdates()
  } else {
    stopRealTimeUpdates()
  }
})

watch(topEventsChartType, () => {
  if (charts.value.topEventsChart) {
    charts.value.topEventsChart.destroy()
    nextTick(() => {
      initializeTopEventsChart()
      updateTopEventsChart()
    })
  }
})

watch(() => realTimeData.value, () => {
  if (!isLoading.value) {
    updateDashboard()
  }
}, { deep: true })
</script>

<style scoped>
.real-time-analytics-dashboard {
  @apply p-6 bg-gray-50 dark:bg-gray-900 min-h-screen;
}

.dashboard-header {
  @apply mb-8;
}

.metric-card {
  @apply bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700;
}

.metric-header {
  @apply flex items-center justify-between mb-2;
}

.metric-header h3 {
  @apply text-sm font-medium text-gray-600 dark:text-gray-400;
}

.metric-trend {
  @apply text-xs font-semibold px-2 py-1 rounded;
}

.trend-up {
  @apply text-green-700 bg-green-100 dark:text-green-400 dark:bg-green-900;
}

.trend-down {
  @apply text-red-700 bg-red-100 dark:text-red-400 dark:bg-red-900;
}

.trend-neutral {
  @apply text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-700;
}

.metric-value {
  @apply text-3xl font-bold text-gray-900 dark:text-white mb-4;
}

.metric-sparkline {
  @apply h-8;
}

.chart-container {
  @apply bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700;
}

.chart-header {
  @apply flex items-center justify-between mb-4;
}

.chart-header h3 {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.chart-controls {
  @apply flex items-center space-x-2;
}

.control-select {
  @apply px-3 py-1 text-sm border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white;
}

.control-button {
  @apply px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-700 dark:text-white;
}

.control-button.active {
  @apply bg-blue-50 border-blue-300 text-blue-700 dark:bg-blue-900 dark:border-blue-600 dark:text-blue-300;
}

.chart-content {
  @apply h-64;
}

.data-table-container {
  @apply bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700;
}

.data-table-container h3 {
  @apply text-lg font-semibold text-gray-900 dark:text-white mb-4;
}

.data-table {
  @apply space-y-3;
}

.data-row {
  @apply flex items-center justify-between p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700;
}

.rank {
  @apply w-6 h-6 flex items-center justify-center bg-gray-100 dark:bg-gray-700 rounded-full text-xs font-semibold;
}

.device-icon,
.country-flag {
  @apply w-6 h-6 flex items-center justify-center;
}

.item-name {
  @apply flex-1 text-sm font-medium text-gray-900 dark:text-white ml-3;
}

.item-value {
  @apply text-sm font-semibold text-gray-900 dark:text-white;
}

.item-percentage {
  @apply text-xs text-gray-500 dark:text-gray-400 ml-2;
}

.settings-panel {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.settings-panel > div {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4;
}

.settings-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700;
}

.settings-header h3 {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.close-button {
  @apply p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg;
}

.settings-content {
  @apply p-6 space-y-6;
}

.setting-group {
  @apply space-y-2;
}

.setting-group label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.setting-select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white;
}

.setting-toggle {
  @apply flex items-center;
}

.toggle-input {
  @apply sr-only;
}

.toggle-label {
  @apply ml-0 flex items-center cursor-pointer;
}

.toggle-label::before {
  content: '';
  @apply w-10 h-6 bg-gray-300 dark:bg-gray-600 rounded-full mr-3 relative;
  transition: background-color 0.2s;
}

.toggle-label::after {
  content: '';
  @apply absolute w-4 h-4 bg-white rounded-full;
  left: 1px;
  top: 1px;
  transition: transform 0.2s;
}

.toggle-input:checked + .toggle-label::before {
  @apply bg-blue-600;
}

.toggle-input:checked + .toggle-label::after {
  transform: translateX(16px);
}

.loading-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex flex-col items-center justify-center z-50;
}

.loading-spinner {
  @apply w-12 h-12 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mb-4;
}

.loading-overlay p {
  @apply text-white text-lg;
}
</style>