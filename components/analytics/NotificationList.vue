<template>
  <div class="notification-list">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-lg font-medium text-gray-800">Notifications</h2>
      <div class="flex space-x-2">
        <button 
          v-if="unreadCount > 0"
          @click="markAllAsRead" 
          class="text-sm text-blue-600 hover:text-blue-800"
        >
          Mark all as read
        </button>
        <button 
          @click="openSettings" 
          class="text-sm text-gray-600 hover:text-gray-800 flex items-center"
        >
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            class="h-4 w-4 mr-1" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path 
              stroke-linecap="round" 
              stroke-linejoin="round" 
              stroke-width="2" 
              d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" 
            />
            <path 
              stroke-linecap="round" 
              stroke-linejoin="round" 
              stroke-width="2" 
              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" 
            />
          </svg>
          Settings
        </button>
      </div>
    </div>
    
    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
    </div>
    
    <!-- Error State -->
    <div v-else-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
      <p>{{ error }}</p>
    </div>
    
    <!-- Empty State -->
    <div v-else-if="notifications.length === 0" class="bg-white rounded-lg shadow p-12 text-center">
      <svg 
        xmlns="http://www.w3.org/2000/svg" 
        class="h-16 w-16 mx-auto text-gray-400 mb-4" 
        fill="none" 
        viewBox="0 0 24 24" 
        stroke="currentColor"
      >
        <path 
          stroke-linecap="round" 
          stroke-linejoin="round" 
          stroke-width="2" 
          d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" 
        />
      </svg>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No notifications</h3>
      <p class="text-sm text-gray-500">You don't have any notifications yet. They will appear here when you receive them.</p>
    </div>
    
    <!-- Notification List -->
    <div v-else>
      <!-- Filter Tabs -->
      <div class="flex border-b border-gray-200 mb-6">
        <button 
          @click="activeFilter = 'all'" 
          class="py-2 px-4 text-sm font-medium"
          :class="activeFilter === 'all' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'"
        >
          All
        </button>
        <button 
          @click="activeFilter = 'unread'" 
          class="py-2 px-4 text-sm font-medium"
          :class="activeFilter === 'unread' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'"
        >
          Unread
        </button>
      </div>
      
      <!-- Notification Items -->
      <div class="space-y-4">
        <div 
          v-for="notification in filteredNotifications" 
          :key="notification.id" 
          class="notification-item bg-white rounded-lg shadow overflow-hidden"
          :class="{ 'border-l-4 border-blue-600': !notification.read }"
        >
          <div class="p-4">
            <div class="flex items-start">
              <!-- Notification Icon -->
              <div class="flex-shrink-0 mr-4">
                <div 
                  class="w-10 h-10 rounded-full flex items-center justify-center"
                  :class="getNotificationIconClass(notification.type)"
                >
                  <svg 
                    xmlns="http://www.w3.org/2000/svg" 
                    class="h-5 w-5" 
                    fill="none" 
                    viewBox="0 0 24 24" 
                    stroke="currentColor"
                  >
                    <path 
                      stroke-linecap="round" 
                      stroke-linejoin="round" 
                      stroke-width="2" 
                      :d="getNotificationIconPath(notification.type)" 
                    />
                  </svg>
                </div>
              </div>
              
              <!-- Notification Content -->
              <div class="flex-1">
                <div class="flex justify-between items-start">
                  <h4 class="text-sm font-medium text-gray-900">{{ notification.title }}</h4>
                  <p class="text-xs text-gray-500">{{ formatDate(notification.created_at) }}</p>
                </div>
                <p class="text-sm text-gray-600 mt-1">{{ notification.message }}</p>
                
                <!-- Notification Details -->
                <div v-if="notification.data" class="mt-2">
                  <div v-if="notification.data.percentage_change" class="text-xs text-gray-600">
                    <span class="font-medium">Change:</span> 
                    <span 
                      :class="notification.data.percentage_change > 0 ? 'text-green-600' : 'text-red-600'"
                    >
                      {{ notification.data.percentage_change > 0 ? '+' : '' }}{{ notification.data.percentage_change.toFixed(1) }}%
                    </span>
                  </div>
                  <div v-if="notification.data.value" class="text-xs text-gray-600">
                    <span class="font-medium">Current value:</span> {{ notification.data.value }}
                  </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="mt-3 flex space-x-2">
                  <button 
                    @click="viewNotification(notification)" 
                    class="text-xs text-blue-600 hover:text-blue-800"
                  >
                    View details
                  </button>
                  <button 
                    v-if="!notification.read"
                    @click="markAsRead(notification.id)" 
                    class="text-xs text-gray-600 hover:text-gray-800"
                  >
                    Mark as read
                  </button>
                  <button 
                    @click="deleteNotification(notification.id)" 
                    class="text-xs text-red-600 hover:text-red-800"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Settings Modal -->
    <div v-if="showSettings" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-lg w-full max-w-md mx-4">
        <div class="p-4 border-b border-gray-200 flex justify-between items-center">
          <h3 class="text-lg font-medium text-gray-900">Notification Settings</h3>
          <button @click="showSettings = false" class="text-gray-400 hover:text-gray-500">
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              class="h-6 w-6" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                stroke-linecap="round" 
                stroke-linejoin="round" 
                stroke-width="2" 
                d="M6 18L18 6M6 6l12 12" 
              />
            </svg>
          </button>
        </div>
        
        <div class="p-4">
          <h4 class="text-sm font-medium text-gray-700 mb-2">Notification Types</h4>
          <div class="space-y-2 mb-4">
            <div v-for="(enabled, type) in notificationPreferences" :key="type" class="flex items-center">
              <input 
                :id="`pref-${type}`" 
                type="checkbox" 
                v-model="notificationPreferences[type]" 
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label :for="`pref-${type}`" class="ml-2 text-sm text-gray-700">
                {{ formatNotificationType(type) }}
              </label>
            </div>
          </div>
          
          <h4 class="text-sm font-medium text-gray-700 mb-2">Thresholds</h4>
          <div class="space-y-4 mb-4">
            <div>
              <label for="view-threshold" class="block text-sm text-gray-700 mb-1">
                View increase threshold (%)
              </label>
              <input 
                id="view-threshold" 
                type="number" 
                v-model="thresholds.view_increase_threshold" 
                min="1" 
                max="100" 
                class="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              />
            </div>
            <div>
              <label for="engagement-threshold" class="block text-sm text-gray-700 mb-1">
                Engagement increase threshold (%)
              </label>
              <input 
                id="engagement-threshold" 
                type="number" 
                v-model="thresholds.engagement_increase_threshold" 
                min="1" 
                max="100" 
                class="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              />
            </div>
          </div>
          
          <div class="flex justify-end mt-6">
            <button 
              @click="saveSettings" 
              class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Save Settings
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useAnalyticsNotifications, Notification, NotificationType } from '~/composables/useAnalyticsNotifications'
import moment from 'moment'

// Analytics notifications composable
const { 
  isLoading, 
  error, 
  notifications,
  unreadCount,
  preferences,
  initNotifications,
  updatePreferences,
  markAsRead,
  markAllAsRead,
  deleteNotification,
  cleanup
} = useAnalyticsNotifications()

// Filter state
const activeFilter = ref<'all' | 'unread'>('all')

// Settings state
const showSettings = ref(false)
const notificationPreferences = ref<Record<string, boolean>>({
  view_increase: true,
  engagement_increase: true,
  ab_test_complete: true,
  unusual_activity: true,
  milestone: true,
  summary: true
})
const thresholds = ref({
  view_increase_threshold: 20,
  engagement_increase_threshold: 20,
  unusual_activity_threshold: 2
})

// Computed properties
const filteredNotifications = computed(() => {
  if (activeFilter.value === 'unread') {
    return notifications.value.filter(n => !n.read)
  }
  return notifications.value
})

// Methods
const formatDate = (date: Date) => {
  return moment(date).format('MMM D, YYYY [at] h:mm A')
}

const formatNotificationType = (type: string) => {
  switch (type) {
    case 'view_increase':
      return 'View increases'
    case 'engagement_increase':
      return 'Engagement increases'
    case 'ab_test_complete':
      return 'A/B test completions'
    case 'unusual_activity':
      return 'Unusual activity'
    case 'milestone':
      return 'Milestone achievements'
    case 'summary':
      return 'Weekly/monthly summaries'
    default:
      return type.replace('_', ' ')
  }
}

const getNotificationIconClass = (type: NotificationType) => {
  switch (type) {
    case 'view_increase':
      return 'bg-green-100 text-green-600'
    case 'engagement_increase':
      return 'bg-blue-100 text-blue-600'
    case 'ab_test_complete':
      return 'bg-purple-100 text-purple-600'
    case 'unusual_activity':
      return 'bg-red-100 text-red-600'
    case 'milestone':
      return 'bg-yellow-100 text-yellow-600'
    case 'summary':
      return 'bg-gray-100 text-gray-600'
    default:
      return 'bg-gray-100 text-gray-600'
  }
}

const getNotificationIconPath = (type: NotificationType) => {
  switch (type) {
    case 'view_increase':
      return 'M13 7h8m0 0v8m0-8l-8 8-4-4-6 6'
    case 'engagement_increase':
      return 'M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z'
    case 'ab_test_complete':
      return 'M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z'
    case 'unusual_activity':
      return 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z'
    case 'milestone':
      return 'M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z'
    case 'summary':
      return 'M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
    default:
      return 'M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9'
  }
}

const viewNotification = async (notification: Notification) => {
  // Mark as read
  if (!notification.read) {
    await markAsRead(notification.id)
  }
  
  // Handle notification action based on type
  if (notification.type === 'ab_test_complete' && notification.data.test_id) {
    // Navigate to A/B test results
    // This would be implemented based on the app's routing
    console.log(`Navigate to A/B test: ${notification.data.test_id}`)
  } else if (notification.data.card_id) {
    // Navigate to business card analytics
    // This would be implemented based on the app's routing
    console.log(`Navigate to card analytics: ${notification.data.card_id}`)
  }
}

const openSettings = () => {
  // Load current preferences
  if (preferences.value) {
    notificationPreferences.value = { ...preferences.value.preferences }
    thresholds.value = { ...preferences.value.thresholds }
  }
  
  showSettings.value = true
}

const saveSettings = async () => {
  // Update preferences
  await updatePreferences({
    preferences: notificationPreferences.value,
    thresholds: thresholds.value
  })
  
  showSettings.value = false
}

// Lifecycle hooks
onMounted(() => {
  // Initialize notifications
  initNotifications()
})

onUnmounted(() => {
  // Clean up
  cleanup()
})
</script>

<style scoped>
.notification-list {
  @apply bg-gray-50 rounded-lg p-4;
}
</style>
