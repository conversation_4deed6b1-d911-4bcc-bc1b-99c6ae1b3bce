<!--
  Content Flagging Modal Component
  Part of Story 1.4 content moderation system
  Allows users to report inappropriate content
-->

<template>
  <TransitionRoot as="template" :show="show">
    <Dialog as="div" class="relative z-50" @close="close">
      <TransitionChild
        as="template"
        enter="ease-out duration-300"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="ease-in duration-200"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
      </TransitionChild>

      <div class="fixed inset-0 z-10 overflow-y-auto">
        <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <TransitionChild
            as="template"
            enter="ease-out duration-300"
            enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            enter-to="opacity-100 translate-y-0 sm:scale-100"
            leave="ease-in duration-200"
            leave-from="opacity-100 translate-y-0 sm:scale-100"
            leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          >
            <DialogPanel class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
              <div>
                <div class="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
                  <Icon name="mdi:flag" class="h-6 w-6 text-red-600" />
                </div>
                <div class="mt-3 text-center sm:mt-5">
                  <DialogTitle as="h3" class="text-base font-semibold leading-6 text-gray-900">
                    Report Content
                  </DialogTitle>
                  <div class="mt-2">
                    <p class="text-sm text-gray-500">
                      Help us maintain a safe community by reporting content that violates our guidelines.
                    </p>
                  </div>
                </div>
              </div>

              <!-- Form -->
              <div class="mt-6 space-y-4">
                <!-- Reason Selection -->
                <div>
                  <label for="reason" class="block text-sm font-medium text-gray-700">
                    Reason for reporting *
                  </label>
                  <select
                    id="reason"
                    v-model="reportData.reason"
                    required
                    class="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-red-500 focus:outline-none focus:ring-red-500 sm:text-sm"
                  >
                    <option value="">Select a reason...</option>
                    <option value="inappropriate">Inappropriate Content</option>
                    <option value="spam">Spam or Unwanted Commercial Content</option>
                    <option value="harassment">Harassment or Abuse</option>
                    <option value="violence">Violence or Dangerous Organizations</option>
                    <option value="hate_speech">Hate Speech</option>
                    <option value="false_information">False Information</option>
                    <option value="intellectual_property">Intellectual Property Violation</option>
                    <option value="privacy">Privacy Violation</option>
                    <option value="adult_content">Adult Content</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <!-- Additional Details -->
                <div>
                  <label for="details" class="block text-sm font-medium text-gray-700">
                    Additional details (optional)
                  </label>
                  <textarea
                    id="details"
                    v-model="reportData.details"
                    rows="3"
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500 sm:text-sm"
                    placeholder="Please provide any additional information that might help us understand the issue..."
                  />
                </div>

                <!-- Anonymous Reporting Option -->
                <div class="flex items-start">
                  <div class="flex h-5 items-center">
                    <input
                      id="anonymous"
                      v-model="reportData.anonymous"
                      type="checkbox"
                      class="h-4 w-4 rounded border-gray-300 text-red-600 focus:ring-red-500"
                    />
                  </div>
                  <div class="ml-3 text-sm">
                    <label for="anonymous" class="font-medium text-gray-700">Report anonymously</label>
                    <p class="text-gray-500">
                      Your identity will not be shared with the content creator.
                    </p>
                  </div>
                </div>

                <!-- Content Preview -->
                <div v-if="contentPreview" class="rounded-md bg-gray-50 p-3">
                  <h4 class="text-sm font-medium text-gray-900">Content being reported:</h4>
                  <div class="mt-2 text-sm text-gray-600">
                    <div class="flex items-start space-x-3">
                      <img
                        v-if="contentPreview.imageUrl"
                        :src="contentPreview.imageUrl"
                        :alt="contentPreview.title || 'Content preview'"
                        class="h-12 w-12 rounded object-cover"
                      />
                      <div class="flex-1 min-w-0">
                        <p class="font-medium text-gray-900 truncate">
                          {{ contentPreview.title || contentPreview.name || 'Untitled' }}
                        </p>
                        <p class="text-gray-500 text-xs">
                          {{ contentPreview.type }} • {{ formatDate(contentPreview.createdAt) }}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Community Guidelines Link -->
                <div class="rounded-md bg-blue-50 p-3">
                  <div class="flex">
                    <div class="flex-shrink-0">
                      <Icon name="mdi:information" class="h-5 w-5 text-blue-400" />
                    </div>
                    <div class="ml-3">
                      <h3 class="text-sm font-medium text-blue-800">
                        Community Guidelines
                      </h3>
                      <div class="mt-2 text-sm text-blue-700">
                        <p>
                          Before reporting, please review our 
                          <NuxtLink to="/community-guidelines" class="font-medium underline">
                            Community Guidelines
                          </NuxtLink>
                          to understand what content is allowed.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Error Message -->
              <div v-if="error" class="mt-4 rounded-md bg-red-50 p-4">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <Icon name="mdi:alert-circle" class="h-5 w-5 text-red-400" />
                  </div>
                  <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">
                      Error submitting report
                    </h3>
                    <div class="mt-2 text-sm text-red-700">
                      <p>{{ error }}</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Success Message -->
              <div v-if="submitted" class="mt-4 rounded-md bg-green-50 p-4">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <Icon name="mdi:check-circle" class="h-5 w-5 text-green-400" />
                  </div>
                  <div class="ml-3">
                    <h3 class="text-sm font-medium text-green-800">
                      Report submitted successfully
                    </h3>
                    <div class="mt-2 text-sm text-green-700">
                      <p>Thank you for helping keep our community safe. We'll review your report within 24 hours.</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="mt-6 flex flex-col-reverse space-y-3 space-y-reverse sm:flex-row sm:space-x-3 sm:space-y-0">
                <button
                  type="button"
                  class="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto"
                  @click="close"
                  :disabled="isSubmitting"
                >
                  {{ submitted ? 'Close' : 'Cancel' }}
                </button>
                <button
                  v-if="!submitted"
                  type="button"
                  class="inline-flex w-full justify-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600 sm:w-auto"
                  @click="submitReport"
                  :disabled="!canSubmit || isSubmitting"
                >
                  <Icon v-if="isSubmitting" name="mdi:loading" class="mr-2 h-4 w-4 animate-spin" />
                  {{ isSubmitting ? 'Submitting...' : 'Submit Report' }}
                </button>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'
import { useContentModeration } from '~/composables/content-moderation'
import moment from 'moment'

interface Props {
  show: boolean
  contentId: string
  contentType: string
  contentPreview?: {
    title?: string
    name?: string
    type: string
    imageUrl?: string
    createdAt: Date
  }
}

interface Emits {
  (e: 'close'): void
  (e: 'reported', reportId: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const { flagContent } = useContentModeration()

// State
const reportData = ref({
  reason: '',
  details: '',
  anonymous: false
})

const isSubmitting = ref(false)
const error = ref('')
const submitted = ref(false)

// Computed
const canSubmit = computed(() => {
  return reportData.value.reason.length > 0 && !isSubmitting.value
})

// Methods
const close = () => {
  if (!isSubmitting.value) {
    emit('close')
  }
}

const submitReport = async () => {
  if (!canSubmit.value) return

  isSubmitting.value = true
  error.value = ''

  try {
    const result = await flagContent(
      props.contentId,
      props.contentType,
      reportData.value.reason,
      reportData.value.details
    )

    if (result.success) {
      submitted.value = true
      emit('reported', 'report-id') // In real implementation, return actual report ID
      
      // Auto-close after 3 seconds
      setTimeout(() => {
        close()
      }, 3000)
    } else {
      error.value = result.error || 'Failed to submit report. Please try again.'
    }
  } catch (err) {
    error.value = 'An unexpected error occurred. Please try again later.'
    console.error('Error submitting content report:', err)
  } finally {
    isSubmitting.value = false
  }
}

const formatDate = (date: Date) => {
  return moment(date).format('MMM D, YYYY')
}

// Reset form when modal opens/closes
watch(() => props.show, (newShow) => {
  if (newShow) {
    // Reset form when opening
    reportData.value = {
      reason: '',
      details: '',
      anonymous: false
    }
    error.value = ''
    submitted.value = false
    isSubmitting.value = false
  }
})
</script>