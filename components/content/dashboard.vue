<script setup lang="ts">
const tabs = ref(1)
const open = ref(false)
import { Chart, registerables, CategoryScale } from "chart.js";
Chart.register(...registerables, CategoryScale);
const text_primaryss_500 = '#6366F1';
const text_secondary_500 = '#EC4899';
const text_yellow_500 = '#F59E0B';
const text_green_500 = '#22C55E';
const text_gray_500 = '#84848f';
onMounted(() => {
    if (process.client) {
        Chart.defaults.color = text_gray_500;

        // ECOMMERCE DOUGHNUT CHART
        const chart_dougnut = document.getElementById("DoughnutChart");
        if (chart_dougnut != null) {
            const ctd = chart_dougnut.getContext('2d');
            const DoughnutChart = new Chart(ctd, {
                type: 'doughnut',
                data: {
                    labels: ['Search Engine', 'Social Post', 'Paid Ads', 'Refferal Link', 'Direct Link', 'Other Source'],
                    datasets: [{
                        label: 'Traffic Source',
                        data: [925, 430, 252, 135, 78, 53],
                        backgroundColor: [
                            text_green_500,
                            text_primaryss_500,
                            hexToRGBA(text_primaryss_500, 0.6),
                            text_yellow_500,
                            hexToRGBA(text_yellow_500, 0.6),
                            text_secondary_500,
                        ],
                        hoverOffset: 4
                    }]
                },
                options: {
                    animation: {
                        delay: 2000
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: "bottom",
                        }
                    }
                }
            })
        }
        // ECOMMERCE BAR CHART
        const chart_bar = document.getElementById("BarChart");
        if (chart_bar != null) {
            const ctb = chart_bar.getContext('2d');
            const BarChart = new Chart(ctb, {
                type: 'bar',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug'],
                    datasets: [{
                        label: '# Visitors',
                        data: [1170, 1321, 1835, 1834, 2183, 1504, 2175, 2521],
                        backgroundColor: [
                            hexToRGBA(text_primaryss_500, 0.6)
                        ],
                        borderColor: [
                            hexToRGBA(text_primaryss_500, 0.6)
                        ],
                        borderWidth: 1
                    },
                    {
                        label: '# Sales',
                        data: [670, 721, 835, 734, 683, 724, 875, 1021],
                        backgroundColor: [
                            text_primaryss_500,
                        ],
                        borderColor: [
                            text_primaryss_500,
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    animation: {
                        y: {
                            duration: 4000,
                            from: 500
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            display: true,
                            grid: {
                                borderDash: [4, 4]
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: "bottom",
                        }
                    }
                }
            })
        }
        // ECOMMERCE LINE CHART
        const chart_line = document.getElementById("LineChart");
        if (chart_line != null) {
            const ctl = chart_line.getContext('2d');
            const LineChart = new Chart(ctl, {
                type: 'line',
                data: {
                    labels: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
                    datasets: [{
                        label: 'Previous Week',
                        data: [70, 121, 235, 334, 483, 304, 475],
                        fill: false,
                        borderColor: text_primaryss_500,
                        cubicInterpolationMode: 'monotone',
                        tension: 0.1
                    },
                    {
                        label: 'Current Week',
                        data: [13, 204, 175, 421, 331, 532, 683],
                        fill: false,
                        borderColor: text_green_500,
                        cubicInterpolationMode: 'monotone',
                        tension: 0.1
                    }]
                },
                options: {
                    animation: {
                        y: {
                            duration: 4000,
                            from: 500
                        }
                    },
                    responsive: true,
                    plugins: {
                        legend: {
                            display: true,
                            position: "bottom",
                        }
                    },
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true
                            },
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: 'Daily Sales'
                            },
                            grid: {
                                borderDash: [4, 4]
                            },
                            Min: -10,
                            Max: 200
                        }
                    }
                }
            })
        }
    }
})
const selectedCard = ref('')
const router = useRouter()
const cardreceived = (data: string) => {
    console.log(data)
   router.push(data)
}

const contentTypes = ref([
    {
        title: 'Emails',
        icon: 'mdi:mail',
        subtitle: '5',
        sidetitle: '2',
        route: '/o/content-emails',
        class: 'bg-access text-white',
        bottomtext: 'View more...'
    },
    {
        title: 'Twitter',
        icon: 'ph:twitter-logo-duotone',
        subtitle: '5',
        sidetitle: '2',
        component: '',
        class: 'bg-blue-600 text-white',
        bottomtext: 'View more...'
    },
    {
        title: 'Facebook',
        icon: 'ph:facebook-logo-duotone',
        subtitle: '5',
        sidetitle: '2',
        component: '',
        class: 'bg-blue-700 text-white',
        bottomtext: 'View more...'
    },
    {
        title: 'Instagram',
        icon: 'ph:instagram-logo-duotone',
        subtitle: '5',
        sidetitle: '2',
        component: '',
        class: 'bg-pink-500 text-white',
        bottomtext: 'View more...'
    },
    {
        title: 'Short Content',
        icon: 'ic:twotone-content-paste',
        subtitle: '5',
        sidetitle: '2',
        component: '',
        class: 'bg-red-800 text-white',
        bottomtext: 'View more...'
    },
    {
        title: 'Blog',
        icon: 'fluent:content-view-gallery-20-filled',
        subtitle: '5',
        sidetitle: '2',
        component: '',
        class: 'bg-purple-500 text-white',
        bottomtext: 'View more...'
    },
    {
        title: 'Site',
        icon: 'gg:website',
        subtitle: '5',
        sidetitle: '2',
        component: '',
        class: 'bg-yellow-500 text-white',
        bottomtext: 'View more...'
    },
  
])
</script>
<template>
                    <main class="pt-2 -mt-2">
                        <div class="p-2 mx-auto">
                            <!-- row -->
                            <div class="flex flex-row flex-wrap">
                                <div class="flex-shrink w-full max-w-full px-4">
                                    <p class="mt-3 mb-5 text-xl font-bold">Content</p>
                                </div>
                                <div class="flex flex-wrap w-full">
                                    <div v-for="(it, index) in contentTypes" :key="index"  class="flex-shrink w-full max-w-full px-4 mb-6 sm:w-1/2 lg:w-1/4">
                                        <CardIcon-big :route="it.route" :bottomtext="it.bottomtext" :title="it.title" :subtitle="it.subtitle"
                                            :sidetitle="it.sidetitle" @card-emit="cardreceived" :class="{'border border-gray-800 dark:border-white': selectedCard === it.title}">
                                            <template #icon>
                                                <div
                                                 :class="it.class"   class="relative flex items-center self-center justify-center w-16 h-16 text-center rounded-full ">
                                                    <Icon :name="it.icon" class="w-10 h-10" />
                                                </div>
                                            </template>
                                        </CardIcon-big>
                                    </div>
                                </div>
                            </div>

                            <!-- row -->
                            <!-- <div class="flex flex-row flex-wrap">
                                <div class="flex-shrink w-full max-w-full px-4 mb-6 lg:w-1/2">
                                    <div class="p-6 mb-6 rounded-lg shadow-lg theme_100">
                                        <div class="flex flex-row justify-between pb-6">
                                            <div class="flex flex-col">
                                                <h3 class="text-base font-bold">Monthly Sales</h3>
                                                <span class="text-sm text-gray-500">Monthly Traffic and Sales</span>
                                            </div>
                                            <div class="relative">
                                                <button @click="open = !open"
                                                    class="text-gray-500 transition-colors duration-200 hover:text-gray-600 dark:hover:text-gray-400 focus:outline-none hover:outline-none">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                                        class="w-6 h-6 bi bi-three-dots" viewBox="0 0 16 16">
                                                        <path
                                                            d="M3 9.5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3z" />
                                                    </svg>
                                                </button>
                                                <div v-show="open" @click.away="open = false"
                                                    class="absolute z-10 origin-top-right bg-white border border-gray-200 rounded ltr:right-0 rtl:left-0 rounded-t-non dark:bg-gray-800 dark:border-gray-700"
                                                    style="min-width:12rem">
                                                    <a class="block px-3 py-2 hover:bg-gray-100 focus:bg-gray-100 dark:hover:bg-gray-900 dark:hover:bg-opacity-20 dark:focus:bg-gray-900"
                                                        href="#">Daily</a>
                                                    <a class="block px-3 py-2 hover:bg-gray-100 focus:bg-gray-100 dark:hover:bg-gray-900 dark:hover:bg-opacity-20 dark:focus:bg-gray-900"
                                                        href="#">Weekly</a>
                                                    <a class="block px-3 py-2 hover:bg-gray-100 focus:bg-gray-100 dark:hover:bg-gray-900 dark:hover:bg-opacity-20 dark:focus:bg-gray-900"
                                                        href="#">Yearly</a>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="relative">
                                            <canvas class="max-w-100" id="BarChart"></canvas>
                                        </div>
                                    </div>

                                    <div class="p-6 rounded-lg shadow-lg theme_100">
                                        <div class="relative">
                                            <table class="w-full text-sm table-sm ltr:text-left rtl:text-right">
                                                <thead>
                                                    <tr class="border-b dark:border-gray-700">
                                                        <th>
                                                            Platform
                                                        </th>
                                                        <th>
                                                            Visitors
                                                        </th>
                                                        <th>
                                                            Ads budget
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>
                                                            Facebook Ads
                                                        </td>
                                                        <td>
                                                            1,520
                                                        </td>
                                                        <td>
                                                            <div class="flex items-center">
                                                                <span class="ltr:mr-2 rtl:ml-2">78%</span>
                                                                <div class="relative w-full">
                                                                    <div
                                                                        class="flex h-2 overflow-hidden text-xs bg-indigo-300 rounded">
                                                                        <div style="width:78%"
                                                                            class="flex flex-col justify-center text-center text-white bg-indigo-500 shadow-none whitespace-nowrap">
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            Google Ads
                                                        </td>
                                                        <td>
                                                            980
                                                        </td>
                                                        <td>
                                                            <div class="flex items-center">
                                                                <span class="ltr:mr-2 rtl:ml-2">65%</span>
                                                                <div class="relative w-full">
                                                                    <div
                                                                        class="flex h-2 overflow-hidden text-xs bg-pink-300 rounded">
                                                                        <div style="width:65%"
                                                                            class="flex flex-col justify-center text-center text-white bg-pink-500 shadow-none whitespace-nowrap">
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            Microsoft Ads
                                                        </td>
                                                        <td>
                                                            540
                                                        </td>
                                                        <td>
                                                            <div class="flex items-center">
                                                                <span class="ltr:mr-2 rtl:ml-2">55%</span>
                                                                <div class="relative w-full">
                                                                    <div
                                                                        class="flex h-2 overflow-hidden text-xs bg-yellow-300 rounded">
                                                                        <div style="width:55%"
                                                                            class="flex flex-col justify-center text-center text-white bg-yellow-500 shadow-none whitespace-nowrap">
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            Tiktok Ads
                                                        </td>
                                                        <td>
                                                            350
                                                        </td>
                                                        <td>
                                                            <div class="flex items-center">
                                                                <span class="ltr:mr-2 rtl:ml-2">40%</span>
                                                                <div class="relative w-full">
                                                                    <div
                                                                        class="flex h-2 overflow-hidden text-xs bg-gray-400 rounded">
                                                                        <div style="width:40%"
                                                                            class="flex flex-col justify-center text-center text-white bg-gray-700 shadow-none whitespace-nowrap">
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex-shrink w-full max-w-full px-4 mb-6 lg:w-1/2">
                                    <div class="h-full p-6 rounded-lg shadow-lg theme_100">
                                        <div class="flex flex-row justify-between pb-6">
                                            <div class="flex flex-col">
                                                <h3 class="text-base font-bold">Traffic Source</h3>
                                                <span class="text-sm text-gray-500">Monthly traffic source</span>
                                            </div>
                                            <div class="relative">
                                                <button @click="open = !open"
                                                    class="text-gray-500 transition-colors duration-200 hover:text-gray-600 dark:hover:text-gray-400 focus:outline-none hover:outline-none">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                                        class="w-6 h-6 bi bi-three-dots" viewBox="0 0 16 16">
                                                        <path
                                                            d="M3 9.5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3z" />
                                                    </svg>
                                                </button>
                                                <div v-show="open" @click.away="open = false"
                                                    class="absolute z-10 origin-top-right bg-white border border-gray-200 rounded ltr:right-0 rtl:left-0 rounded-t-non dark:bg-gray-800 dark:border-gray-700"
                                                    style="min-width:12rem">
                                                    <a class="block px-3 py-2 hover:bg-gray-100 focus:bg-gray-100 dark:hover:bg-gray-900 dark:hover:bg-opacity-20 dark:focus:bg-gray-900"
                                                        href="#">Daily</a>
                                                    <a class="block px-3 py-2 hover:bg-gray-100 focus:bg-gray-100 dark:hover:bg-gray-900 dark:hover:bg-opacity-20 dark:focus:bg-gray-900"
                                                        href="#">Weekly</a>
                                                    <a class="block px-3 py-2 hover:bg-gray-100 focus:bg-gray-100 dark:hover:bg-gray-900 dark:hover:bg-opacity-20 dark:focus:bg-gray-900"
                                                        href="#">Yearly</a>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="relative w-full mx-auto text-center sm:w-2/3 lg:w-full">
                                            <canvas class="max-w-100" id="DoughnutChart"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div> -->

                            <!-- row -->
                            <!-- <div class="flex flex-row flex-wrap">
                                <div class="flex-shrink w-full max-w-full px-4 mb-6 lg:w-2/3">
                                    <div class="h-full p-6 rounded-lg shadow-lg theme_100">
                                        <div class="flex flex-row justify-between pb-6">
                                            <div class="flex flex-col">
                                                <h3 class="text-base font-bold">Revenue</h3>
                                                <span class="text-sm font-semibold text-gray-500">Today's Earning: <span
                                                        class="text-green-500">$1,570.30</span></span>
                                            </div>
                                        </div>
                                        <div class="relative">
                                            <canvas class="max-w-100" id="LineChart"></canvas>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex-shrink w-full max-w-full px-4 mb-6 lg:w-1/3">
                                    <div class="h-full p-6 rounded-lg shadow-lg theme_100">
                                        <div class="mb-2">
                                            <table class="w-full text-sm table-sm ltr:text-left rtl:text-right">
                                                <thead>
                                                    <tr class="border-b dark:border-gray-700">
                                                        <th>
                                                            Best Seller
                                                        </th>
                                                        <th>
                                                            Sales
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>
                                                            <a href="#" class="hover:text-indigo-500">
                                                                <div class="flex items-center">
                                                                    <div class="flex-shrink-0 w-10 h-10">
                                                                        <img class="w-10 h-10 rounded-full"
                                                                            src=""
                                                                            alt="product images">
                                                                    </div>
                                                                    <div class="ltr:ml-4 rtl:mr-4">
                                                                        <div class="leading-5">
                                                                            Nike Women's Race Running Shoe
                                                                        </div>
                                                                        <div class="text-xs leading-5 text-gray-500">
                                                                            Women shoes
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </a>
                                                        </td>
                                                        <td>
                                                            <div class="leading-5 text-green-700">$4,345</div>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <a href="#" class="hover:text-indigo-500">
                                                                <div class="flex items-center">
                                                                    <div class="flex-shrink-0 w-10 h-10">
                                                                        <img class="w-10 h-10 rounded-full"
                                                                            src=""
                                                                            alt="product images">
                                                                    </div>
                                                                    <div class="ltr:ml-4 rtl:mr-4">
                                                                        <div class="leading-5">
                                                                            Nike Womens Free RN Flyknit 2018
                                                                        </div>
                                                                        <div class="text-xs leading-5 text-gray-500">
                                                                            Women shoes
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </a>
                                                        </td>
                                                        <td>
                                                            <div class="leading-5 text-green-700">$3,235</div>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <a href="#" class="hover:text-indigo-500">
                                                                <div class="flex items-center">
                                                                    <div class="flex-shrink-0 w-10 h-10">
                                                                        <img class="w-10 h-10 rounded-full"
                                                                            src=""
                                                                            alt="product images">
                                                                    </div>
                                                                    <div class="ltr:ml-4 rtl:mr-4">
                                                                        <div class="leading-5">
                                                                            Nike Women's Sneaker Running Shoes
                                                                        </div>
                                                                        <div class="text-xs leading-5 text-gray-500">
                                                                            Women shoes
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </a>
                                                        </td>
                                                        <td>
                                                            <div class="leading-5 text-green-700">$1,545</div>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <a href="#" class="hover:text-indigo-500">
                                                                <div class="flex items-center">
                                                                    <div class="flex-shrink-0 w-10 h-10">
                                                                        <img class="w-10 h-10 rounded-full"
                                                                            src=""
                                                                            alt="product images">
                                                                    </div>
                                                                    <div class="ltr:ml-4 rtl:mr-4">
                                                                        <div class="leading-5">
                                                                            Nike Women's Gymnastics Tennis Shoes
                                                                        </div>
                                                                        <div class="text-xs leading-5 text-gray-500">
                                                                            Women shoes
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </a>
                                                        </td>
                                                        <td>
                                                            <div class="leading-5 text-green-700">$1,045</div>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div> -->

                            <!-- row -->
                            <div class="flex flex-row flex-wrap">
                                <div class="flex-shrink w-full max-w-full px-4 mb-6">
                                    <div class="h-full p-6 rounded-lg shadow-lg theme_100">
                                        <div class="flex flex-row justify-between pb-2">
                                            <div class="flex flex-col">
                                                <h3 class="text-base font-bold">Latest Orders</h3>
                                            </div>
                                        </div>
                                        <div class="overflow-x-auto">
                                            <table
                                                class="w-full text-gray-500 table-sorter table-bordered-bottom dark:text-gray-400">
                                                <thead>
                                                    <tr class="bg-gray-200 dark:bg-gray-900 dark:bg-opacity-40">
                                                        <th>
                                                            Snippet ID
                                                        </th>
                                                        <th class="hidden lg:table-cell">
                                                            Title
                                                        </th>
                                                        <th>
                                                            Type
                                                        </th>
                                                        <th class="hidden lg:table-cell">
                                                            Date Added
                                                        </th>
                                                        <th>
                                                            Tags
                                                        </th>
                                                        <th data-sortable="false">
                                                            Action
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody class="text-sm">
                                                    <tr>
                                                        <td>
                                                            <div class="leading-5 uppercase">#inv12637</div>
                                                        </td>
                                                        <td class="hidden lg:table-cell">
                                                            <div class="flex flex-row flex-wrap items-center">
                                                                <div
                                                                    class="flex-shrink w-full max-w-full mb-1 font-bold leading-5 text-gray-900 dark:text-gray-300">
                                                                    John Thomas
                                                                </div>
                                                                <div
                                                                    class="flex-shrink w-full max-w-full italic leading-5 text-gray-500">
                                                                    California, USA
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div
                                                                class="inline-block px-3 py-1 font-semibold leading-tight text-center text-pink-700 bg-pink-100 rounded-full dark:bg-opacity-80">
                                                                Paid</div>
                                                        </td>
                                                        <td class="hidden lg:table-cell">
                                                            <div class="leading-5">May 09, 2025</div>
                                                        </td>
                                                        <td>
                                                            <div class="font-bold leading-5 text-green-700">$79</div>
                                                        </td>
                                                        <td class="text-center">
                                                            <a href="#"
                                                                class="px-3 py-2 mb-3 leading-5 text-center text-gray-100 bg-indigo-500 border border-indigo-500 rounded hover:text-white hover:bg-indigo-600 hover:ring-0 hover:border-indigo-600 focus:bg-indigo-600 focus:border-indigo-600 focus:outline-none focus:ring-0">
                                                                <svg xmlns="http://www.w3.org/2000/svg"
                                                                    fill="currentColor"
                                                                    class="inline w-4 h-4 bi bi-pencil-square"
                                                                    viewBox="0 0 16 16">
                                                                    <path
                                                                        d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z" />
                                                                    <path fill-rule="evenodd"
                                                                        d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5v11z" />
                                                                </svg>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <div class="leading-5 uppercase">#inv12636</div>
                                                        </td>
                                                        <td class="hidden lg:table-cell">
                                                            <div class="flex flex-row flex-wrap items-center">
                                                                <div
                                                                    class="flex-shrink w-full max-w-full mb-1 font-bold leading-5 text-gray-900 dark:text-gray-300">
                                                                    Daniel
                                                                </div>
                                                                <div
                                                                    class="flex-shrink w-full max-w-full italic leading-5 text-gray-500">
                                                                    San Francisco, USA
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div
                                                                class="inline-block px-3 py-1 font-semibold leading-tight text-center text-purple-700 bg-purple-100 rounded-full dark:bg-opacity-80">
                                                                Processing</div>
                                                        </td>
                                                        <td class="hidden lg:table-cell">
                                                            <div class="leading-5">May 09, 2025</div>
                                                        </td>
                                                        <td>
                                                            <div class="font-bold leading-5 text-green-700">$119</div>
                                                        </td>
                                                        <td class="text-center">
                                                            <a href="#"
                                                                class="px-3 py-2 mb-3 leading-5 text-center text-gray-100 bg-indigo-500 border border-indigo-500 rounded hover:text-white hover:bg-indigo-600 hover:ring-0 hover:border-indigo-600 focus:bg-indigo-600 focus:border-indigo-600 focus:outline-none focus:ring-0">
                                                                <svg xmlns="http://www.w3.org/2000/svg"
                                                                    fill="currentColor"
                                                                    class="inline w-4 h-4 bi bi-pencil-square"
                                                                    viewBox="0 0 16 16">
                                                                    <path
                                                                        d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z" />
                                                                    <path fill-rule="evenodd"
                                                                        d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5v11z" />
                                                                </svg>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <div class="leading-5 uppercase">#inv12635</div>
                                                        </td>
                                                        <td class="hidden lg:table-cell">
                                                            <div class="flex flex-row flex-wrap items-center">
                                                                <div
                                                                    class="flex-shrink w-full max-w-full mb-1 font-bold leading-5 text-gray-900 dark:text-gray-300">
                                                                    Vinjay Khan
                                                                </div>
                                                                <div
                                                                    class="flex-shrink w-full max-w-full italic leading-5 text-gray-500">
                                                                    New Delhi, India
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div
                                                                class="inline-block px-3 py-1 font-semibold leading-tight text-center text-yellow-700 bg-yellow-100 rounded-full dark:bg-opacity-80">
                                                                Packing</div>
                                                        </td>
                                                        <td class="hidden lg:table-cell">
                                                            <div class="leading-5">May 09, 2025</div>
                                                        </td>
                                                        <td>
                                                            <div class="font-bold leading-5 text-green-700">$58</div>
                                                        </td>
                                                        <td class="text-center">
                                                            <a href="#"
                                                                class="px-3 py-2 mb-3 leading-5 text-center text-gray-100 bg-indigo-500 border border-indigo-500 rounded hover:text-white hover:bg-indigo-600 hover:ring-0 hover:border-indigo-600 focus:bg-indigo-600 focus:border-indigo-600 focus:outline-none focus:ring-0">
                                                                <svg xmlns="http://www.w3.org/2000/svg"
                                                                    fill="currentColor"
                                                                    class="inline w-4 h-4 bi bi-pencil-square"
                                                                    viewBox="0 0 16 16">
                                                                    <path
                                                                        d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z" />
                                                                    <path fill-rule="evenodd"
                                                                        d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5v11z" />
                                                                </svg>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <div class="leading-5 uppercase">#inv12634</div>
                                                        </td>
                                                        <td class="hidden lg:table-cell">
                                                            <div class="flex flex-row flex-wrap items-center">
                                                                <div
                                                                    class="flex-shrink w-full max-w-full mb-1 font-bold leading-5 text-gray-900 dark:text-gray-300">
                                                                    David Arya
                                                                </div>
                                                                <div
                                                                    class="flex-shrink w-full max-w-full italic leading-5 text-gray-500">
                                                                    Jakarta, Indonesia
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div
                                                                class="inline-block px-3 py-1 font-semibold leading-tight text-center text-green-700 bg-green-100 rounded-full dark:bg-opacity-80">
                                                                Shipped</div>
                                                        </td>
                                                        <td class="hidden lg:table-cell">
                                                            <div class="leading-5">May 09, 2025</div>
                                                        </td>
                                                        <td>
                                                            <div class="font-bold leading-5 text-green-700">$79</div>
                                                        </td>
                                                        <td class="text-center">
                                                            <a href="#"
                                                                class="px-3 py-2 mb-3 leading-5 text-center text-gray-100 bg-indigo-500 border border-indigo-500 rounded hover:text-white hover:bg-indigo-600 hover:ring-0 hover:border-indigo-600 focus:bg-indigo-600 focus:border-indigo-600 focus:outline-none focus:ring-0">
                                                                <svg xmlns="http://www.w3.org/2000/svg"
                                                                    fill="currentColor"
                                                                    class="inline w-4 h-4 bi bi-pencil-square"
                                                                    viewBox="0 0 16 16">
                                                                    <path
                                                                        d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z" />
                                                                    <path fill-rule="evenodd"
                                                                        d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5v11z" />
                                                                </svg>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <div class="leading-5 uppercase">#inv12633</div>
                                                        </td>
                                                        <td class="hidden lg:table-cell">
                                                            <div class="flex flex-row flex-wrap items-center">
                                                                <div
                                                                    class="flex-shrink w-full max-w-full mb-1 font-bold leading-5 text-gray-900 dark:text-gray-300">
                                                                    William Stone
                                                                </div>
                                                                <div
                                                                    class="flex-shrink w-full max-w-full italic leading-5 text-gray-500">
                                                                    London, UK
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div
                                                                class="inline-block px-3 py-1 font-semibold leading-tight text-center text-gray-700 bg-gray-100 rounded-full dark:bg-opacity-80">
                                                                Complete</div>
                                                        </td>
                                                        <td class="hidden lg:table-cell">
                                                            <div class="leading-5">May 09, 2025</div>
                                                        </td>
                                                        <td>
                                                            <div class="font-bold leading-5 text-green-700">$158</div>
                                                        </td>
                                                        <td class="text-center">
                                                            <a href="#"
                                                                class="px-3 py-2 mb-3 leading-5 text-center text-gray-100 bg-indigo-500 border border-indigo-500 rounded hover:text-white hover:bg-indigo-600 hover:ring-0 hover:border-indigo-600 focus:bg-indigo-600 focus:border-indigo-600 focus:outline-none focus:ring-0">
                                                                <svg xmlns="http://www.w3.org/2000/svg"
                                                                    fill="currentColor"
                                                                    class="inline w-4 h-4 bi bi-pencil-square"
                                                                    viewBox="0 0 16 16">
                                                                    <path
                                                                        d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z" />
                                                                    <path fill-rule="evenodd"
                                                                        d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5v11z" />
                                                                </svg>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <div class="leading-5 uppercase">#inv12632</div>
                                                        </td>
                                                        <td class="hidden lg:table-cell">
                                                            <div class="flex flex-row flex-wrap items-center">
                                                                <div
                                                                    class="flex-shrink w-full max-w-full mb-1 font-bold leading-5 text-gray-900 dark:text-gray-300">
                                                                    Danile
                                                                </div>
                                                                <div
                                                                    class="flex-shrink w-full max-w-full italic leading-5 text-gray-500">
                                                                    California, US
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div
                                                                class="inline-block px-3 py-1 font-semibold leading-tight text-center text-gray-700 bg-gray-100 rounded-full dark:bg-opacity-80">
                                                                Complete</div>
                                                        </td>
                                                        <td class="hidden lg:table-cell">
                                                            <div class="leading-5">May 08, 2025</div>
                                                        </td>
                                                        <td>
                                                            <div class="font-bold leading-5 text-green-700">$128</div>
                                                        </td>
                                                        <td class="text-center">
                                                            <a href="#"
                                                                class="px-3 py-2 mb-3 leading-5 text-center text-gray-100 bg-indigo-500 border border-indigo-500 rounded hover:text-white hover:bg-indigo-600 hover:ring-0 hover:border-indigo-600 focus:bg-indigo-600 focus:border-indigo-600 focus:outline-none focus:ring-0">
                                                                <svg xmlns="http://www.w3.org/2000/svg"
                                                                    fill="currentColor"
                                                                    class="inline w-4 h-4 bi bi-pencil-square"
                                                                    viewBox="0 0 16 16">
                                                                    <path
                                                                        d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z" />
                                                                    <path fill-rule="evenodd"
                                                                        d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5v11z" />
                                                                </svg>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <div class="leading-5 uppercase">#inv12631</div>
                                                        </td>
                                                        <td class="hidden lg:table-cell">
                                                            <div class="flex flex-row flex-wrap items-center">
                                                                <div
                                                                    class="flex-shrink w-full max-w-full mb-1 font-bold leading-5 text-gray-900 dark:text-gray-300">
                                                                    Romano
                                                                </div>
                                                                <div
                                                                    class="flex-shrink w-full max-w-full italic leading-5 text-gray-500">
                                                                    California, US
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div
                                                                class="inline-block px-3 py-1 font-semibold leading-tight text-center text-gray-700 bg-gray-100 rounded-full dark:bg-opacity-80">
                                                                Complete</div>
                                                        </td>
                                                        <td class="hidden lg:table-cell">
                                                            <div class="leading-5">May 08, 2025</div>
                                                        </td>
                                                        <td>
                                                            <div class="font-bold leading-5 text-green-700">$98</div>
                                                        </td>
                                                        <td class="text-center">
                                                            <a href="#"
                                                                class="px-3 py-2 mb-3 leading-5 text-center text-gray-100 bg-indigo-500 border border-indigo-500 rounded hover:text-white hover:bg-indigo-600 hover:ring-0 hover:border-indigo-600 focus:bg-indigo-600 focus:border-indigo-600 focus:outline-none focus:ring-0">
                                                                <svg xmlns="http://www.w3.org/2000/svg"
                                                                    fill="currentColor"
                                                                    class="inline w-4 h-4 bi bi-pencil-square"
                                                                    viewBox="0 0 16 16">
                                                                    <path
                                                                        d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z" />
                                                                    <path fill-rule="evenodd"
                                                                        d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5v11z" />
                                                                </svg>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <div class="leading-5 uppercase">#inv12630</div>
                                                        </td>
                                                        <td class="hidden lg:table-cell">
                                                            <div class="flex flex-row flex-wrap items-center">
                                                                <div
                                                                    class="flex-shrink w-full max-w-full mb-1 font-bold leading-5 text-gray-900 dark:text-gray-300">
                                                                    Yonanda
                                                                </div>
                                                                <div
                                                                    class="flex-shrink w-full max-w-full italic leading-5 text-gray-500">
                                                                    California, US
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div
                                                                class="inline-block px-3 py-1 font-semibold leading-tight text-center text-gray-700 bg-gray-100 rounded-full dark:bg-opacity-80">
                                                                Complete</div>
                                                        </td>
                                                        <td class="hidden lg:table-cell">
                                                            <div class="leading-5">May 08, 2025</div>
                                                        </td>
                                                        <td>
                                                            <div class="font-bold leading-5 text-green-700">$138</div>
                                                        </td>
                                                        <td class="text-center">
                                                            <a href="#"
                                                                class="px-3 py-2 mb-3 leading-5 text-center text-gray-100 bg-indigo-500 border border-indigo-500 rounded hover:text-white hover:bg-indigo-600 hover:ring-0 hover:border-indigo-600 focus:bg-indigo-600 focus:border-indigo-600 focus:outline-none focus:ring-0">
                                                                <svg xmlns="http://www.w3.org/2000/svg"
                                                                    fill="currentColor"
                                                                    class="inline w-4 h-4 bi bi-pencil-square"
                                                                    viewBox="0 0 16 16">
                                                                    <path
                                                                        d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z" />
                                                                    <path fill-rule="evenodd"
                                                                        d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5v11z" />
                                                                </svg>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <div class="leading-5 uppercase">#inv12629</div>
                                                        </td>
                                                        <td class="hidden lg:table-cell">
                                                            <div class="flex flex-row flex-wrap items-center">
                                                                <div
                                                                    class="flex-shrink w-full max-w-full mb-1 font-bold leading-5 text-gray-900 dark:text-gray-300">
                                                                    Danile
                                                                </div>
                                                                <div
                                                                    class="flex-shrink w-full max-w-full italic leading-5 text-gray-500">
                                                                    California, US
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div
                                                                class="inline-block px-3 py-1 font-semibold leading-tight text-center text-gray-700 bg-gray-100 rounded-full dark:bg-opacity-80">
                                                                Complete</div>
                                                        </td>
                                                        <td class="hidden lg:table-cell">
                                                            <div class="leading-5">May 08, 2025</div>
                                                        </td>
                                                        <td>
                                                            <div class="font-bold leading-5 text-green-700">$128</div>
                                                        </td>
                                                        <td class="text-center">
                                                            <a href="#"
                                                                class="px-3 py-2 mb-3 leading-5 text-center text-gray-100 bg-indigo-500 border border-indigo-500 rounded hover:text-white hover:bg-indigo-600 hover:ring-0 hover:border-indigo-600 focus:bg-indigo-600 focus:border-indigo-600 focus:outline-none focus:ring-0">
                                                                <svg xmlns="http://www.w3.org/2000/svg"
                                                                    fill="currentColor"
                                                                    class="inline w-4 h-4 bi bi-pencil-square"
                                                                    viewBox="0 0 16 16">
                                                                    <path
                                                                        d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z" />
                                                                    <path fill-rule="evenodd"
                                                                        d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5v11z" />
                                                                </svg>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <div class="leading-5 uppercase">#inv12628</div>
                                                        </td>
                                                        <td class="hidden lg:table-cell">
                                                            <div class="flex flex-row flex-wrap items-center">
                                                                <div
                                                                    class="flex-shrink w-full max-w-full mb-1 font-bold leading-5 text-gray-900 dark:text-gray-300">
                                                                    Romano
                                                                </div>
                                                                <div
                                                                    class="flex-shrink w-full max-w-full italic leading-5 text-gray-500">
                                                                    California, US
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div
                                                                class="inline-block px-3 py-1 font-semibold leading-tight text-center text-gray-700 bg-gray-100 rounded-full dark:bg-opacity-80">
                                                                Complete</div>
                                                        </td>
                                                        <td class="hidden lg:table-cell">
                                                            <div class="leading-5">May 08, 2025</div>
                                                        </td>
                                                        <td>
                                                            <div class="font-bold leading-5 text-green-700">$98</div>
                                                        </td>
                                                        <td class="text-center">
                                                            <a href="#"
                                                                class="px-3 py-2 mb-3 leading-5 text-center text-gray-100 bg-indigo-500 border border-indigo-500 rounded hover:text-white hover:bg-indigo-600 hover:ring-0 hover:border-indigo-600 focus:bg-indigo-600 focus:border-indigo-600 focus:outline-none focus:ring-0">
                                                                <svg xmlns="http://www.w3.org/2000/svg"
                                                                    fill="currentColor"
                                                                    class="inline w-4 h-4 bi bi-pencil-square"
                                                                    viewBox="0 0 16 16">
                                                                    <path
                                                                        d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z" />
                                                                    <path fill-rule="evenodd"
                                                                        d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5v11z" />
                                                                </svg>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <div class="leading-5 uppercase">#inv12627</div>
                                                        </td>
                                                        <td class="hidden lg:table-cell">
                                                            <div class="flex flex-row flex-wrap items-center">
                                                                <div
                                                                    class="flex-shrink w-full max-w-full mb-1 font-bold leading-5 text-gray-900 dark:text-gray-300">
                                                                    Yonanda
                                                                </div>
                                                                <div
                                                                    class="flex-shrink w-full max-w-full italic leading-5 text-gray-500">
                                                                    California, US
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div
                                                                class="inline-block px-3 py-1 font-semibold leading-tight text-center text-gray-700 bg-gray-100 rounded-full dark:bg-opacity-80">
                                                                Complete</div>
                                                        </td>
                                                        <td class="hidden lg:table-cell">
                                                            <div class="leading-5">May 08, 2025</div>
                                                        </td>
                                                        <td>
                                                            <div class="font-bold leading-5 text-green-700">$138</div>
                                                        </td>
                                                        <td class="text-center">
                                                            <a href="#"
                                                                class="px-3 py-2 mb-3 leading-5 text-center text-gray-100 bg-indigo-500 border border-indigo-500 rounded hover:text-white hover:bg-indigo-600 hover:ring-0 hover:border-indigo-600 focus:bg-indigo-600 focus:border-indigo-600 focus:outline-none focus:ring-0">
                                                                <svg xmlns="http://www.w3.org/2000/svg"
                                                                    fill="currentColor"
                                                                    class="inline w-4 h-4 bi bi-pencil-square"
                                                                    viewBox="0 0 16 16">
                                                                    <path
                                                                        d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z" />
                                                                    <path fill-rule="evenodd"
                                                                        d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5v11z" />
                                                                </svg>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <div class="leading-5 uppercase">#inv12626</div>
                                                        </td>
                                                        <td class="hidden lg:table-cell">
                                                            <div class="flex flex-row flex-wrap items-center">
                                                                <div
                                                                    class="flex-shrink w-full max-w-full mb-1 font-bold leading-5 text-gray-900 dark:text-gray-300">
                                                                    Danile
                                                                </div>
                                                                <div
                                                                    class="flex-shrink w-full max-w-full italic leading-5 text-gray-500">
                                                                    California, US
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div
                                                                class="inline-block px-3 py-1 font-semibold leading-tight text-center text-gray-700 bg-gray-100 rounded-full dark:bg-opacity-80">
                                                                Complete</div>
                                                        </td>
                                                        <td class="hidden lg:table-cell">
                                                            <div class="leading-5">May 08, 2025</div>
                                                        </td>
                                                        <td>
                                                            <div class="font-bold leading-5 text-green-700">$128</div>
                                                        </td>
                                                        <td class="text-center">
                                                            <a href="#"
                                                                class="px-3 py-2 mb-3 leading-5 text-center text-gray-100 bg-indigo-500 border border-indigo-500 rounded hover:text-white hover:bg-indigo-600 hover:ring-0 hover:border-indigo-600 focus:bg-indigo-600 focus:border-indigo-600 focus:outline-none focus:ring-0">
                                                                <svg xmlns="http://www.w3.org/2000/svg"
                                                                    fill="currentColor"
                                                                    class="inline w-4 h-4 bi bi-pencil-square"
                                                                    viewBox="0 0 16 16">
                                                                    <path
                                                                        d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z" />
                                                                    <path fill-rule="evenodd"
                                                                        d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5v11z" />
                                                                </svg>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <div class="leading-5 uppercase">#inv12625</div>
                                                        </td>
                                                        <td class="hidden lg:table-cell">
                                                            <div class="flex flex-row flex-wrap items-center">
                                                                <div
                                                                    class="flex-shrink w-full max-w-full mb-1 font-bold leading-5 text-gray-900 dark:text-gray-300">
                                                                    Romano
                                                                </div>
                                                                <div
                                                                    class="flex-shrink w-full max-w-full italic leading-5 text-gray-500">
                                                                    California, US
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div
                                                                class="inline-block px-3 py-1 font-semibold leading-tight text-center text-gray-700 bg-gray-100 rounded-full dark:bg-opacity-80">
                                                                Complete</div>
                                                        </td>
                                                        <td class="hidden lg:table-cell">
                                                            <div class="leading-5">May 08, 2025</div>
                                                        </td>
                                                        <td>
                                                            <div class="font-bold leading-5 text-green-700">$98</div>
                                                        </td>
                                                        <td class="text-center">
                                                            <a href="#"
                                                                class="px-3 py-2 mb-3 leading-5 text-center text-gray-100 bg-indigo-500 border border-indigo-500 rounded hover:text-white hover:bg-indigo-600 hover:ring-0 hover:border-indigo-600 focus:bg-indigo-600 focus:border-indigo-600 focus:outline-none focus:ring-0">
                                                                <svg xmlns="http://www.w3.org/2000/svg"
                                                                    fill="currentColor"
                                                                    class="inline w-4 h-4 bi bi-pencil-square"
                                                                    viewBox="0 0 16 16">
                                                                    <path
                                                                        d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z" />
                                                                    <path fill-rule="evenodd"
                                                                        d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5v11z" />
                                                                </svg>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <div class="leading-5 uppercase">#inv12624</div>
                                                        </td>
                                                        <td class="hidden lg:table-cell">
                                                            <div class="flex flex-row flex-wrap items-center">
                                                                <div
                                                                    class="flex-shrink w-full max-w-full mb-1 font-bold leading-5 text-gray-900 dark:text-gray-300">
                                                                    Yonanda
                                                                </div>
                                                                <div
                                                                    class="flex-shrink w-full max-w-full italic leading-5 text-gray-500">
                                                                    California, US
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div
                                                                class="inline-block px-3 py-1 font-semibold leading-tight text-center text-gray-700 bg-gray-100 rounded-full dark:bg-opacity-80">
                                                                Complete</div>
                                                        </td>
                                                        <td class="hidden lg:table-cell">
                                                            <div class="leading-5">May 08, 2025</div>
                                                        </td>
                                                        <td>
                                                            <div class="font-bold leading-5 text-green-700">$138</div>
                                                        </td>
                                                        <td class="text-center">
                                                            <a href="#"
                                                                class="px-3 py-2 mb-3 leading-5 text-center text-gray-100 bg-indigo-500 border border-indigo-500 rounded hover:text-white hover:bg-indigo-600 hover:ring-0 hover:border-indigo-600 focus:bg-indigo-600 focus:border-indigo-600 focus:outline-none focus:ring-0">
                                                                <svg xmlns="http://www.w3.org/2000/svg"
                                                                    fill="currentColor"
                                                                    class="inline w-4 h-4 bi bi-pencil-square"
                                                                    viewBox="0 0 16 16">
                                                                    <path
                                                                        d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z" />
                                                                    <path fill-rule="evenodd"
                                                                        d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5v11z" />
                                                                </svg>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
              </main>
</template>
