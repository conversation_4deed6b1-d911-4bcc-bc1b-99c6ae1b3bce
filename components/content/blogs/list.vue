<script setup lang="ts">
const { blogsContent, blogsContentSelected } = useContent()

const router = useRouter()
const selectedblogs = (blogs: any) => {
    blogsContentSelected.value = blogs
    router.push(`/o/content-blogs-single?id=${blogs.id}`)
}

const deleteblog = (blog: any, index: any) => {
    deleteItems('blogs-content', blog.id)
    blogsContent.value.splice(index, 1)
}
</script>

<template>
    <div>
        <div class="flex flex-row flex-wrap">
            <div class="flex-shrink w-full max-w-full px-4 mb-6">
                <div class="h-full p-6 o_card">
                    <div class="flex flex-row justify-between pb-2">
                        <div class="flex flex-col">
                            <h3 class="text-base font-bold">Latest blogs</h3>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full text-gray-500 table-sorter table-bordered-bottom dark:text-gray-400">
                            <thead>
                                <tr class="bg-gray-200 dark:bg-gray-900 dark:bg-opacity-40">
                                    <!-- <th>
                                        Order ID
                                    </th> -->
                                    <th class="text-left lg:table-cell">
                                        Title
                                    </th>
                                    <th class="text-left ">
                                        Status
                                    </th>
                                    <th class="hidden text-left lg:table-cell">
                                        Date Added
                                    </th>
                                
                                    <th class="text-left ">
                                        Author
                                    </th>
                                    <th class="text-left ">
                                        Action
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="text-sm">
                                <tr v-for="(cont, index) in blogsContent" :key="index">
                                    <td class="table-cell">
                                        <div class="flex flex-row flex-wrap items-center">
                                            <div
                                                class="flex-shrink w-full max-w-full mb-1 font-bold leading-5 text-gray-900 dark:text-gray-300">
                                               {{cont.title}}
                                            </div>
                                            <div class="flex-shrink w-full max-w-full italic leading-5 text-gray-500">
                                              {{cont.type}}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div
                                            class="inline-block px-3 py-1 font-semibold leading-tight text-center text-pink-700 bg-pink-100 rounded-full dark:bg-opacity-80">
                                            {{cont.status}}
                                        </div>
                                    </td>
                                    <td class="hidden lg:table-cell">
                                        <div class="leading-5">  {{cont.created_date}}</div>
                                    </td>
                                    <td>
                                            <user-groups :users="cont.members" />
                                    </td>
                                    <td>
                                        <Tooltip text="Use" side="left">
                                        <template #body>
                                            <button class="o_btn_icon_square" @click="selectedblogs(cont)">
                                                <Icon name="material-symbols:arrow-selector-tool" />
                                            </button>
                                        </template>
                                    </Tooltip>
                                    <Tooltip text="Delete" side="right">
                                        <template #body>
                                            <button class="o_btn_icon_square" @click="deleteblog(cont, index)">
                                                <Icon name="mdi:trash" />
                                            </button>
                                        </template>
                                    </Tooltip>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>


</template>