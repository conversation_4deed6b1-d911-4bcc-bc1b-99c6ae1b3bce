<script setup lang="ts">
const { snippetsSelected, blogsContentSelected } = useContent();

const script = ref("");
const formSpaceRegister: any = useState("blogsave", () => {
  return {
    title: "",
    about: "",
    tags: [],
    blogs: "",
  };
});

watch(
  () => formSpaceRegister.value.blogs,
  () => {
    console.log("here", formSpaceRegister.value.blogs);
    script.value = formSpaceRegister.value.blogs;
    
  }
);

const input = (data: any) => {
  if (data && data.path === undefined && data.bubbles === undefined){
    script.value = data;
    formSpaceRegister.value.blogs = data
  }
    
};

const messages = (data: any) => {
  if (data && data.path === undefined && data.bubbles === undefined)
    blogsContentSelected.value.messages = data;
};
const snippetsshow = useState("snippetsshow", () => "list");
const snippetSingle: any = useState("snippets-single", () => {});

onMounted(() => {
  snippetsshow.value = "snippets";
  snippetsSelected.value = {};
  snippetSingle.value = {};
});

const extratext = ref("");
const edit = ref(false);
const usegpt = ref(false);
</script>

<template>
  <div>
    <div
      v-if="!edit"
      class="relative flex justify-between o_card scrollbar-hide"
    >
      <div>
        <div>
          {{ blogsContentSelected.title }}
          <small> - {{ blogsContentSelected.type }}</small>
        </div>
        <div v-html="blogsContentSelected.about"></div>
      </div>
      <user-groups :users="blogsContentSelected.members" />

      <div class="flex items-center space-x-2">
        <div v-for="(tag, index) in blogsContentSelected.tags" :key="index">
          <div class="o_pill">
            {{ tag }}
          </div>
        </div>
        <Tooltip text="Snippets" side="bottom">
          <template #body>
            <button class="o_btn_icon_square" @click="edit = true">
              <Icon name="mdi:pencil" />
            </button>
          </template>
        </Tooltip>
        <Tooltip text="AI" side="bottom">
          <template #body>
            <button
              class="o_btn_icon_square"
              :class="{ 'border-y border-primarys': usegpt }"
              @click="usegpt = !usegpt"
            >
              <open-ai-icon color="text-blue-600" />
            </button>
          </template>
        </Tooltip>
      </div>
    </div>
    <div
      v-if="edit"
      class="relative flex justify-between px-2 py-4 mt-2 mb-2 overflow-auto rounded-lg shadow-lg theme_300 scrollbar-hide"
    >
      <content-blogs-edit @close="edit = false" />
    </div>

    <div class="grid grid-cols-3 gap-4 mt-4" v-if="usegpt">
      <div class="col-span-2">
        <open-ai-chat-gpt-single-emit
          @input="input"
          @messages="messages"
          :showModel="false"
          :showfirstline="false"
          usemodel="text-davinci-003"
          :extratext="extratext"
        />
      </div> 
      <div>
        <div class="o_card h-100 scrollbar-hide">
          <div class="flex justify-end w-full space-x-2">
            <Tooltip text="Snippets" side="bottom">
              <template #body>
                <button
                  class="o_btn_icon_square"
                  :class="{
                    'border-b border-info': snippetsshow == 'snippets',
                  }"
                  @click="snippetsshow = 'snippets'"
                >
                  <Icon name="material-symbols:snippet-folder-outline-sharp" />
                </button>
              </template>
            </Tooltip>
            <div v-if="snippetsSelected">
              <Tooltip
                text="Lists"
                side="bottom"
                v-if="snippetsSelected.last_action_date"
              >
                <template #body>
                  <button
                    class="o_btn_icon_square"
                    :class="{ 'border-b border-info': snippetsshow == 'list' }"
                    @click="snippetsshow = 'list'"
                  >
                    <Icon name="ic:round-format-list-numbered" />
                  </button>
                </template>
              </Tooltip>
            </div>
            <div v-if="snippetSingle">
              <Tooltip
                text="Single"
                side="bottom"
                v-if="snippetSingle.last_action_date"
              >
                <template #body>
                  <button
                    class="o_btn_icon_square"
                    :class="{
                      'border-b border-info': snippetsshow == 'snippet',
                    }"
                    @click="snippetsshow = 'snippet'"
                  >
                    <Icon name="entypo:dot-single" />
                  </button>
                </template>
              </Tooltip>
            </div>
          </div>
          <content-blogs-snippets v-if="snippetsshow == 'snippets'" />
          <content-blogs-single-list v-if="snippetsshow == 'list'" />
          <content-blogs-snippet
            v-if="snippetsshow == 'snippet'"
            @usetext="extratext = $event"
          />
        </div>
      </div>
    </div>
    <div class="grid grid-cols-3 gap-4 mt-4">
      <div class="col-span-2" :class="{ 'col-span-3': !usegpt }">
        <div class="o_card h-100 scrollbar-hide">
          <content-blogs-save :script="script" />
        </div>
      </div>
      <div class="o_card h-100 scrollbar-hide" v-if="usegpt">
        <content-blogs-blog-list />
      </div>
    </div>
  </div>
</template>
