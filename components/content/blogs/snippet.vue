<script setup lang="ts">
const snippetSingle: any = useState('snippets-single', () => { })
const snippet = ref({
    inputs: [],
    text: '',
    example: '',
    textreal: '',
})

const snipshow = ref(true)
const snippetsshow = useState('snippetsshow', ()=> 'list')

const changesnippet = (item: { inputs: never[]; text: string; example: string; textreal: string; })=> {
    snippet.value = item
    snipshow.value = false
    snippetsshow.value = 'snippet'

    setTimeout(() => {
        snipshow.value = true
    }, 100);
}
const snipC: any = getCurrentInstance()
const settext = (data: any)=> {
    console.log('data', data)
    if(data){
        snipC.emit('usetext', data)
    }
}
</script>
<template>
    <div>
        <div class="p-2 rounded o_card mb-1">
            <label class="o_label_small">Select Snippet to use</label>
            <div>
                <div v-for="(snip, index) in snippetSingle.snippets" :key="index" class="flex justify-between p-2 o_card_soft"
                    :class="{ 'table_base_100': index % 2 }">
                    <div v-html="snip.text"></div>
                    <tooltip text="Use" side="left">
                        <template #body>
                            <button class="o_btn_icon_square" @click="changesnippet(snip)">
                                <Icon name="material-symbols:arrow-selector-tool" />
                            </button>
                        </template>
                    </tooltip>
                </div>
            </div>
        </div>
        <div class="p-2 rounded o_card">
            <h1 class="mt-8 o_label_small">Fill in Snippet</h1>
            <div v-if="snipshow">
                <content-snippets-input :item="snippet" @usetext="settext" />
            </div>
        </div>
    </div>
</template>