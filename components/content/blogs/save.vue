<script setup lang="ts">
import moment from "moment"
const { addToCollection: addToCollectionSpaceRegister, setToId } = database()
const {  blogsContentSelected } = useContent()
const {currentSpace} = space()
const props = defineProps(
    {
        item: { type: Object, default: () => { } },
        script: { type: String, default: () => '' },
    },
)
const currentClient: object = useState('currentClient', () => {
        return {}
    })
const { $swal } = useNuxtApp()
const open = ref(false)
const spaceInstance: any = getCurrentInstance()
const date = ref('')

const formSpaceRegister: any = useState('blogsSave', () =>{return{
    title: '',
    about: '',
    tags: [],
    blogs: '',
    logo: {},
    background_image: {},
    publish_date: moment().format('DD-MM-YYYY'),
    author_id: [currentClient.value.id],
    author: {name: currentClient.value.first_name + ' ' + currentClient.value.last_name, email: currentClient.value.email, phone: currentClient.value.phone, avatar: currentClient.value.avatar},
}})

watch(()=> props.script, (val)=>{
    formSpaceRegister.value.blogs = val
})


onMounted(()=> {
    date.value = moment().format('YYYY-MM-DD')
    formSpaceRegister.value =  {
    title: '',
    about: '',
    tags: formSpaceRegister.value.tags,
    blogs: '',
    logo: {},
    background_image: {},
    publish_date: moment().format('DD-MM-YYYY'),
    author_id: [currentClient.value.id],
    author: {name: currentClient.value.first_name + ' ' + currentClient.value.last_name, email: currentClient.value.email, phone: currentClient.value.phone, avatar: currentClient.value.avatar},
}
})
const blogsSingle: any = useState('blogs-singles', () => [])
async function submitBlogPost() {

    try {
        formSpaceRegister.value.blogs_id = blogsContentSelected.value.id
        // formSpaceRegister.value.blogs = props.script

        const res = await addToCollectionSpaceRegister('blogs-singles', formSpaceRegister.value)
        const payload = { ...formSpaceRegister.value, ...res }
        blogsSingle.value.push(payload)

        $swal.fire({
            title: 'Success!',
            text: `Yeah, enjoy`,
            icon: 'success',
            confirmButtonText: 'Cool'
        })
    }
    catch (e) {
        console.error(e)
    }

    formSpaceRegister.value = {
        title: '',
    about: '',
    tags: [],
    blogs: '',
    logo: {},
    background_image: {},
    publish_date: moment().format('DD-MM-YYYY'),
    author_id: [currentClient.value.id],
    author: {name: currentClient.value.first_name + ' ' + currentClient.value.last_name, email: currentClient.value.email, phone: currentClient.value.phone, avatar: currentClient.value.avatar},

    }
    spaceInstance.emit('close')
}

const updatehRegistration = async (e: any) => {
    // formSpaceRegister.value.blogs = props.script

    await setToId(formSpaceRegister.value.id, 'blogs-singles', formSpaceRegister.value)
    $swal.fire({
            title: 'Updated!',
            text: `Yeah, enjoy`,
            icon: 'success',
            confirmButtonText: 'Cool'
        })

    formSpaceRegister.value = {
        title: '',
    about: '',
    tags: [],
    blogs: '',
    logo: {},
    background_image: {},
    publish_date: moment().format('DD-MM-YYYY'),
    author_id: [currentClient.value.id],
    author: {name: currentClient.value.first_name + ' ' + currentClient.value.last_name, email: currentClient.value.email, phone: currentClient.value.phone, avatar: currentClient.value.avatar},
    }
    spaceInstance.emit('close')
}

// watch(() => props.item.text, () => {
//     if (formSpaceRegister.value.snippets.length == 0)
//         formSpaceRegister.value.title = props.item.text.slice(0, 30);
// })
const cr: any = useState('presets-create', () => {
    return {
        inputs: [],
        text: '',
        example: '',
        textreal: '',
    }

})
const saveinput = () => {
    const payload = JSON.parse(JSON.stringify(props.item))
    formSpaceRegister.value.snippets.push(payload)
    cr.value = {
        inputs: [],
        text: '',
        example: '',
        textreal: '',
    }
}

const input = (data: { path: undefined; bubbles: undefined; })=> {
if(data && data.path === undefined && data.bubbles === undefined){
    formSpaceRegister.value.blogs = data

}
}

function setIcon(e: any) {
  formSpaceRegister.value.logo = {
    src: e[0].src,
    name: e[0].name,
    type: e[0].type,
  };
}

function setBackground(e: any) {
    if(e.length == 0){
        formSpaceRegister.value.background_image = {}
        return
    }
  formSpaceRegister.value.background_image = {
    src: e[0].src,
    name: e[0].name,
    type: e[0].type,
  };
}


watch(() => date.value, () => {
    formSpaceRegister.value.publish_date  = moment(date.value).format('DD-MM-YYYY')
})

const searchSchema = ref({
  index: "omni",
  collection: "contacts",
  display: "first_name",
  options: {
    input_class: { type: "text", value: "o_input", title: "Button Branding" },
    search_button_class: {
      type: "text",
      value: "o_btn_primarys",
      title: "Button Branding",
    },
    icon: { type: "text", value: "mdi:arrow-right", title: "Button Branding" },
  },
});

const userinvite = (us: { id: any }) => {
  console.log(us);

    formSpaceRegister.value.author_id = [us.id]
    formSpaceRegister.value.author = {name: us.first_name + ' ' + us.last_name, email: us.email, phone: us.phone, avatar: currentClient.value.avatar}

};

const filter = computed(()=> {
  if(isAdmin())  {
    return {space_own: currentSpace.value.id}

  }else{
    return {
      space_own: currentSpace.value.id,

    }
  }
})

const TagsSelected = (data: any)=> {
  if (data && data.path === undefined && data.bubbles === undefined)
  formSpaceRegister.value.tags = data
}
const view = ref(false)
</script>
<template>
    <div class="flex flex-row flex-wrap">
  
<div class="flex justify-between w-full">
    <h1 class="mb-6 mt- o_label">Blogs Saver</h1>
    <Icon name="fluent:text-clear-formatting-16-filled" @click="formSpaceRegister = 
         {
        title: '',
    about: '',
    tags: [],
    blogs: '',
    logo: {},
    background_image: {},
    publish_date: moment().format('DD-MM-YYYY'),
    author_id: [currentClient.value.id],
    author: {name: currentClient.value.first_name + ' ' + currentClient.value.last_name, email: currentClient.value.email, phone: currentClient.value.phone, avatar: currentClient.value.avatar},
    }
    "/>
</div>
        <div class="flex-shrink w-full max-w-full mb-6">
            <div class="h-full rounded-lg theme_100">
                <div class="w-full">
                    <div class="flex space-x-2">
                        <div class="flex-shrink w-1/2">
                            <label for="inputtitle" class="o_label">Title</label>
                            <input v-model="formSpaceRegister.title" type="text"
                                class="o_input"
                                id="inputtitle">
                        </div>
                        <div class="flex-shrink w-1/2 max-w-full">
                            <label for="inputtitle" class="o_label">Tags</label>

                            <TagsSelect type="snippets" :show-add="true"
                                @input="TagsSelected" />
                        </div>
                    </div>

                    <div class="flex space-x-2">
                        <div class="flex-shrink w-1/2">
                            <label for="inputtitle" class="o_label">Publish Date</label>
                            <input v-model="date" type="date" 
                                class="o_input"
                                id="inputtitle">
                        </div>
                        <div class="flex-shrink w-1/2 max-w-full">
                            <label for="inputtitle" class="o_label"
                        >Author</label
                      >
                      <search :schema="searchSchema" @input="userinvite" :filter="filter"/>
                      <user-groups :users="formSpaceRegister.author_id" />
                        </div>
                    </div>

                    <div class="flex-shrink w-full max-w-full">
                        <label for="inputbudget" class="o_label">Short Description</label>
                        <textarea v-model="formSpaceRegister.about" type="text"
                            class="o_input"
                            id="inputbudget" />
                    </div>
                  
                    <div class="grid grid-cols-2 gap-4">
                        <div class="flex-shrink w-full max-w-full mb-4">
                      <label for="inputbudget" class="o_label"
                        >Logo</label
                      >
                      <file-button @uploadAdd="setIcon" />
                      <file-view :images="[formSpaceRegister.logo]" v-if="formSpaceRegister.logo"/>
                    </div>
                    <div class="flex-shrink w-full max-w-full mb-4">
                      <label for="inputbudget" class="o_label"
                        >Background Image</label
                      >
                      <file-button @uploadAdd="setBackground" />
                      <file-view :images="[formSpaceRegister.background_image]" v-if="formSpaceRegister.background_image"/>
                    </div>
                    </div>
                    <div class="flex justify-end">
                        <div>
                            <Icon name="mdi:pencil" @click="view = !view" v-if="view"/>
                            <Icon name="mdi:eye" @click="view = !view" v-if="!view"/>


                        </div>
                    </div>
                    <div v-show="view">
                        <div v-html="formSpaceRegister.blogs"></div>
                    </div>
                    <div v-show="!view">
                        <label for="inputbudget" class="o_label"
                        >Blog Post</label
                      >
                        <forms-inputs-quill :placeholder="formSpaceRegister.blogs" @input="input"/>

                    </div>
                    <div class="flex-shrink w-full max-w-full mt-2">
                        <button @click="updatehRegistration" v-if="formSpaceRegister.id"
                            class="block px-4 py-2 leading-5 text-center text-gray-100 border rounded bg-primaryss_focus border-primaryss_focus lg:inline-block hover:text-white hover:bg-primaryss hover:ring-0 hover:border-primaryss focus:bg-primaryss focus:border-primaryss focus:outline-none focus:ring-0">Update
                            to {{ blogsContentSelected.title }} <svg xmlns="http://www.w3.org/2000/svg" width="12"
                                height="12" fill="currentColor" class="inline-block ltr:ml-1 rtl:mr-1 bi bi-plus-lg"
                                viewBox="0 0 16 16">
                                <path
                                    d="M8 0a1 1 0 0 1 1 1v6h6a1 1 0 1 1 0 2H9v6a1 1 0 1 1-2 0V9H1a1 1 0 0 1 0-2h6V1a1 1 0 0 1 1-1z">
                                </path>
                            </svg>
                        </button>
                        <button @click="submitBlogPost" v-else
                            class="block px-4 py-2 leading-5 text-center text-gray-100 border rounded bg-primaryss_focus border-primaryss_focus lg:inline-block hover:text-white hover:bg-primaryss hover:ring-0 hover:border-primaryss focus:bg-primaryss focus:border-primaryss focus:outline-none focus:ring-0">Add
                            to {{ blogsContentSelected.title }} <svg xmlns="http://www.w3.org/2000/svg" width="12"
                                height="12" fill="currentColor" class="inline-block ltr:ml-1 rtl:mr-1 bi bi-plus-lg"
                                viewBox="0 0 16 16">
                                <path
                                    d="M8 0a1 1 0 0 1 1 1v6h6a1 1 0 1 1 0 2H9v6a1 1 0 1 1-2 0V9H1a1 1 0 0 1 0-2h6V1a1 1 0 0 1 1-1z">
                                </path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

</template>