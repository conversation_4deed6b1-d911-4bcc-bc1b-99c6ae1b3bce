<script setup lang="ts">
const { snippets, snippetsSelected } = useContent()
const { getCollectionWhere, deleteDoc } = database()
const snippetsSingle: any = useState('snippets-singles', () => [])
const snippetSingle: any = useState('snippets-single', () => {return {} })

onMounted(async () => {
    getlist()
})

const getlist = async () => {
    snippetsSingle.value = []
    try {
        if (snippetsSelected.value) {
            let res = await queryByWhere2('snippets-singles', 'snippet_id', snippetsSelected.value.id, '==')
            
            snippetsSingle.value = res.result
        }

    }
    catch (e) {
        console.error(e)
    }
}
const snippetsshow = useState('snippetsshow', () => 'list')

const deleteSnippet = async (snip: any) => {
    try {
        snippetSingle.value = snip
        snippetsshow.value = 'snippet'

    }
    catch (e) {
        console.error(e)
    }
}

if (snippetsSelected.value) {
    watch(snippetsSelected.value.id, async (val) => {
        await getlist()
    })
}

</script>

<template>
    <div class="flex flex-row flex-wrap">
        <div class="flex-shrink w-full max-w-full mb-6">
            <div class="h-full rounded-lg shadow-lg theme_100">
                <div class="flex flex-row justify-between pb-2">
                    <div class="flex flex-col">
                        <h3 class="text-base font-bold">Snippets for {{
                            snippetsSelected? snippetsSelected.title : ''
                        }}</h3>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="w-full text-left text-gray-500 table-sorter table-bordered-bottom dark:text-gray-400">
                        <thead>
                            <tr class="bg-gray-200 dark:bg-gray-900 dark:bg-opacity-40">

                                <th class="hidden lg:table-cell">
                                    Title
                                </th>


                                <th>
                                    Tags
                                </th>
                                <th data-sortable="false">
                                    Action
                                </th>
                            </tr>
                        </thead>
                        <tbody class="text-sm">
                            <tr v-for="(snip, index) in snippetsSingle" :key="index"
                                :class="{ 'border-l-4 border-success': snippetSingle.title === snip.title }">

                                <td class="hidden lg:table-cell">
                                    <div class="flex flex-row flex-wrap items-center">
                                        <div
                                            class="flex-shrink w-full max-w-full mb-1 font-bold leading-5 text-gray-900 dark:text-gray-300">
                                            {{ snip.title }}
                                        </div>
                                        <div class="flex-shrink w-full max-w-full italic leading-5 text-gray-500">
                                            {{ snip.about }} 
                                        </div>
                                    </div>
                                </td>


                                <td>
                                    <div class="font-bold leading-5 text-green-700">
                                        <div v-for="(tg, ind) in snip.tags" :key="ind">{{ tg }}</div>
                                    </div>
                                </td>
                                <td class="flex space-x-2 text-center">
                                    <Tooltip text="Use" side="bottom">
                                        <template #body>
                                            <button class="o_btn_icon_square" @click="deleteSnippet(snip)">
                                                <Icon name="material-symbols:arrow-selector-tool" />
                                            </button>
                                        </template>
                                    </Tooltip>

                                </td>
                            </tr>

                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</template>