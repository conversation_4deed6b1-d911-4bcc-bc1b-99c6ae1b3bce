<script setup lang="ts">
const { addToCollection: addToCollectionSpaceRegister } = database()
const currentClient: object = useState('currentClient', () => {
        return {}
    })
const { $swal } = useNuxtApp()
const { blogsContent } = useContent()
const open = ref(false)
const spaceInstance: any = getCurrentInstance()
const formSpaceRegister: any = ref({
  title: '',
  about: '',
  type: '',
  tags: [],
  members: [currentClient.value.id],
  invitees: [],
  other: '',
  approval_needed: false,
  approvals: [],
  status: 'Pending',
  emails: [],
  logo: {},
    background_image: {},

})
async function submitNetcashRegistration() {

  try {
  let payload = createData(formSpaceRegister.value);

    const res = await addToCollectionSpaceRegister('blogs-content', payload)
    const payloads = { ...payload, ...res }
    blogsContent.value.push(payloads)

    $swal.fire({
      title: 'Success!',
      text: `Yeah, enjoy`,
      icon: 'success',
      confirmButtonText: 'Cool'
    })
  }
  catch (e) {
    console.error(e)
  }

  formSpaceRegister.value = {
    title: '',
    about: '',
    type: '',
    tags: [],
    members: [currentClient.value.id],
    invitees: [],
    other: '',
    approval_needed: false,
    approvals: [],
    status: 'Pending',
    emails: [],
    market: false,
    logo: {},
    background_image: {},

  }
  spaceInstance.emit('close')
}

const emailTypes = ref([
'Travel Blog',
'Fashion Blog', 
'Lifestyle Blog', 
'Food Blog', 
'Parenting Blog', 
'Health & Fitness Blog', 
'Music Blog', 
'Outdoor/ Adventure Blog', 
'DIY Blog', 
'Personal Blog', 
'Product/ Gadget Review Blog', 
'News/ Politics Blog', 
'Education/ Learning Blog', 
'Business/ Entrepreneurship Blog', 
'Pet Blog', 
'Sports Blog', 
'Art/ Design Blog',
'Comedy Blog',
'Photo Blog', 
'Video Blog',
  'Other'
])

const schema: any = ref({
  options: {
    input_class: { value: 'o_input' },
    search_button_class: { value: 'o_btn_icon_square' },
    icon: { value: 'mdi:magnify' },
  }
})

const userinvite = (us: { id: any; }) => {
  console.log(us)
  //see if in array, if in array then delete, if not in array then add
  if (formSpaceRegister.value.invitees.includes(us.id)) {
    formSpaceRegister.value.invitees = formSpaceRegister.value.invitees.filter((item: any) => item !== us.id)
  } else {
    formSpaceRegister.value.invitees.push(us.id)
  }
  // formSpaceRegister.value.invitees.push(us.id)
}

const userapproval = (us: { id: any; }) => {
  console.log(us)
  //see if in array, if in array then delete, if not in array then add
  if (formSpaceRegister.value.approvals.includes(us.id)) {
    formSpaceRegister.value.approvals = formSpaceRegister.value.approvals.filter((item: any) => item !== us.id)
  } else {
    formSpaceRegister.value.approvals.push(us.id)
  }
  // formSpaceRegister.value.invitees.push(us.id)
}

const input = (data) => {
  if (data && data.path === undefined && data.bubbles === undefined)
    formSpaceRegister.value.about = data
}

const uploadAdd = (data: any) => {
  console.log(data)
  formSpaceRegister.value.images = data
}

function setIcon(e: any) {
  formSpaceRegister.value.logo = {
    src: e[0].src,
    name: e[0].name,
    type: e[0].type,
  };
}

function setBackground(e: any) {
  formSpaceRegister.value.background_image = {
    src: e[0].src,
    name: e[0].name,
    type: e[0].type,
  };
}

const TagsSelected = (data: any)=> {
  if (data && data.path === undefined && data.bubbles === undefined)
  formSpaceRegister.value.tags = data
}
</script>
<template>
   
                  <div class="flex flex-row flex-wrap -mx-4">
                    <div class="flex-shrink w-full max-w-full px-4 mb-4">
                      <label for="inputtitle" class="o_label">Title</label>
                      <input v-model="formSpaceRegister.title" type="text"
                        class="o_input"
                        id="inputtitle">
                    </div>
                    <div class="flex-shrink w-full max-w-full px-4 mb-4">
                      <label for="inputbudget" class="o_label">About</label>
                      <!-- <textarea  v-model="formSpaceRegister.about" type="text" class="o_input" id="inputbudget" /> -->
                      <forms-inputs-quill @input="input" :placeholder="formSpaceRegister.about" />
                    </div>
                    <div class="flex-shrink w-full max-w-full px-4 mb-4 md:w-1/2">
                      <label for="inputtask" class="o_label">Type</label>
                      <select v-model="formSpaceRegister.type" type="text"
                        class="o_input"
                        id="inputtask">
                        <option v-for="(type, index) in emailTypes" :key="type" :value="type">{{ type }}</option>
                      </select>
                    </div>
                    <div class="flex-shrink w-full max-w-full px-4 mb-4 md:w-1/2">
                      <client-only>
                        <label for="inputtitle" class="o_label">Tags <small>(comma seperated)</small></label>
                        <TagsSelect type="snippets" :show-add="true"
                          @input="TagsSelected" />

                      </client-only>

                    </div>
                    <!-- <div class="flex-shrink w-full max-w-full px-4 mb-4">
                      <input v-model="formSpaceRegister.approval_needed" type="checkbox">
                      <label for="inputtitle" class="inline-block mb-2 ml-2">Approval Needed?</label>

                    </div> -->
                    <div class="flex-shrink w-full max-w-full px-4 mb-4" v-if="formSpaceRegister.type == 'Other'">
                      <label for="inputtitle" class="o_label">Other</label>
                      <input v-model="formSpaceRegister.other" type="text"
                        class="o_input"
                        id="inputtitle">
                    </div>
                    <!-- <div class="flex-shrink w-full max-w-full px-4 mb-4">
                      <label for="inputtitle" class="o_label">User</label>
                      <user-search :schema="schema" @input="userinvite" />
                      <user-groups :users="formSpaceRegister.invitees" />
                    </div>
                    <div class="flex-shrink w-full max-w-full px-4 mb-4" v-if="formSpaceRegister.approval_needed">
                      <label for="inputtitle" class="o_label">Approval</label>
                      <user-search :schema="schema" @input="userapproval" />
                      <user-groups :users="formSpaceRegister.approvals" />
                    </div> -->

                
                    <div class="grid md:grid-cols-2 gap-2">
                      <div class="flex-shrink w-full max-w-full px-4 mb-4">
                      <label for="inputbudget" class="o_label"
                        >Logo</label
                      >
                      <file-button @uploadAdd="setIcon" />
                      <file-view :images="[formSpaceRegister.logo]" />
                    </div>
                    <div class="flex-shrink w-full max-w-full px-4 mb-4">
                      <label for="inputbudget" class="o_label"
                        >Background Image</label
                      >
                      <file-button @uploadAdd="setBackground" />
                      <file-view :images="[formSpaceRegister.background_image]" />
                    </div>
                    </div>
                   


                    <div class="flex-shrink w-full max-w-full px-4">
                      <button @click="submitNetcashRegistration"
                        class="block px-4 py-2 leading-5 text-center text-gray-100 border rounded bg-primarys_focus border-primarys_focus lg:inline-block hover:text-white hover:bg-primarys hover:ring-0 hover:border-primarys focus:bg-primarys focus:border-primarys focus:outline-none focus:ring-0">Add
                        new blog <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor"
                          class="inline-block ltr:ml-1 rtl:mr-1 bi bi-plus-lg" viewBox="0 0 16 16">
                          <path
                            d="M8 0a1 1 0 0 1 1 1v6h6a1 1 0 1 1 0 2H9v6a1 1 0 1 1-2 0V9H1a1 1 0 0 1 0-2h6V1a1 1 0 0 1 1-1z">
                          </path>
                        </svg></button>
                    </div>
                  </div>
              
</template>