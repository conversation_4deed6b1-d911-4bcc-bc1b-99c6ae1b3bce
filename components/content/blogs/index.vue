<script setup lang="ts">
const { blogsContent } = useContent()
const { getCollectionWhere } = database()
const { currentSpace } = space()

onMounted(async () => {
    const result = await queryByWhere2('blogs-content', 'spaces', currentSpace.value.id, 'array-contains')
    blogsContent.value = result.result
})

const addCard = ref(false)
</script>

<template>
    <div>
        <modal-small :open="addCard" :close="() => addCard = false" title="Create new blog">
            <template #body>
                <content-blogs-create @close="addCard = false" />
            </template>
        </modal-small>
        <div class="flex w-full max-w-full px-4 justify-between">
            <p class="mt-3 mb-5 text-xl font-bold">Blogs</p>
            <div class="ml-2">
                <button class="o_btn_icon_square" @click="addCard = !addCard">
                    <Icon name="mdi:plus" />
                </button>

            </div>
        </div>
        <content-blogs-list />
    </div>


</template>