<script setup lang="ts">
const {  blogsContentSelected } = useContent()
const { getCollectionWhere, deleteDoc} = database()
const blogsSingle: any = useState('blogs-singles', ()=> [])

onMounted(async ()=> {
   let res = await queryByWhere2('blogs-singles', 'blogs_id', blogsContentSelected.value.id, '==')
   
    blogsSingle.value = res.result
})

const deleteblogs = async (snip: any) => {
    try {
        await deleteDoc('blogs-singles', snip.id)
        const index = blogsSingle.value.findIndex((item: any) => item.id === snip.id)
        blogsSingle.value.splice(index, 1)
    }
    catch (e) {
        console.error(e)
    }
}
const formSpaceRegister: any = useState('blogsSave', () =>{return{
    title: '',
    about: '',
    type: '',
    tags: [],
    blogs: []

}})
const useblogs = (snip: any) => {
    formSpaceRegister.value = snip
  
}
</script>

<template>
    <div class="flex flex-row flex-wrap w-full">
        <div class="flex-shrink w-full max-w-full mb-6">
            <div class="h-full p-2 rounded-lg theme_100 scrollbar-hide">
                <div class="flex flex-row justify-between pb-2">
                    <div class="flex flex-col">
                        <h3 class="text-base font-bold">blog posts for {{ blogsContentSelected.title }}</h3>
                    </div>
                </div>
                <div class="w-full scrollbar-hide">
                    <table class="w-full text-left text-gray-500 table-sorter table-bordered-bottom dark:text-gray-400 scrollbar-hide">
                        <thead>
                            <tr class="bg-gray-200 dark:bg-gray-900 dark:bg-opacity-40">
                                <!-- <th>
                                    blogs ID
                                </th> -->
                                <th class="hidden lg:table-cell">
                                    Title
                                </th>
                               
                                <!-- <th class="hidden lg:table-cell">
                                    Date Added
                                </th> -->
                                <th>
                                    Tags
                                </th>
                                <th data-sortable="false">
                                    Action
                                </th>
                            </tr>
                        </thead>
                        <tbody class="text-sm">
                            <tr v-for="(snip, index) in blogsSingle" :key="index">
                                <!-- <td>
                                    <div class="leading-5 uppercase">#{{ snip.id }}</div>
                                </td> -->
                                <td class="hidden lg:table-cell">
                                    <div class="flex flex-row flex-wrap items-center">
                                        <div
                                            class="flex-shrink w-full max-w-full mb-1 font-bold leading-5 text-gray-900 dark:text-gray-300">
                                            {{ snip.title }}
                                        </div>
                                        <div class="flex-shrink w-full max-w-full italic leading-5 text-gray-500">
                                            {{ snip.about }}
                                        </div>
                                    </div>
                                </td>
                              
                                <!-- <td class="hidden lg:table-cell">
                                    <div class="leading-5"> {{ snip.created_date }}</div>
                                </td> -->
                                <td>
                                    <div class="font-bold leading-5 text-green-700">
                                        <div v-for="(tg, ind) in snip.tags" :key="ind">{{ tg }}</div>
                                    </div>
                                </td>
                                <td class="flex space-x-2 text-center">
                                   
                         
                                    <Tooltip text="Delete" side="bottom">
                                        <template #body>
                                            <button class="o_btn_icon_square" @click="deleteblogs(snip)">
                                                <Icon name="mdi:trash" />
                                            </button>
                                        </template>
                                    </Tooltip>
                                    <Tooltip text="Use" side="bottom">
                                        <template #body>
                                            <button class="o_btn_icon_square" @click="useblogs(snip)">
                                                <Icon name="material-symbols:arrow-selector-tool" />
                                            </button>
                                        </template>
                                    </Tooltip>
                                </td>
                            </tr>

                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</template>