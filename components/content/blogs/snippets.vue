<script setup lang="ts">
const open = ref(false)
const { deleteDoc: deleteDocItems, getCollectionWhereDoubleLimit } = database()
const { currentSpace: currentSpaceSnippets } = space()

const { snippets, snippetsSelected } = useContent()

const addCard = ref(false)
const selectedCard = ref('')
const router = useRouter()
const cardreceived = (data: string) => {
    console.log(data)
    //    router.push(data)
    selectedCard.value = data
}
async function getItemsData() {
    console.log('HERE', currentSpaceSnippets.value.id)
    const snips = await getCollectionWhereDoubleLimit('snippets', 'space_own', currentSpaceSnippets.value.id, '==', 'type', 'Blog', '==', 10)
    console.log('snips', snips)
    snippets.value = snips.result
} 
onMounted(async () => {
    getItemsData()

})
const snippetsshow = useState('snippetsshow', ()=> 'list')

const updateSnippet = async (snippet: any) => {
    console.log('updateSnippet', snippet)
    snippetsSelected.value = snippet
    snippetsshow.value = 'list'
    // router.push('/o/content-snippets-single')
}

</script>
<template>
    <main>
        <div class="mx-auto">

            <div class="flex flex-row flex-wrap">
                <div class="flex-shrink w-full max-w-full mb-6">
                    <div class="h-full rounded-lg theme_100">
                        <div class="flex flex-row justify-between pb-2">
                            <div class="flex flex-col">
                                <h3 class="text-base font-bold">Blogs Snippets</h3>
                            </div>
                        </div>
                        <div class="overflow-x-auto">
                            <table
                                class="w-full text-left text-gray-500 table-sorter table-bordered-bottom dark:text-gray-400">
                                <thead>
                                    <tr class="bg-gray-200 dark:bg-gray-900 dark:bg-opacity-40">

                                        <th class="hidden lg:table-cell">
                                            Title
                                        </th>

                                        <th>
                                            Tags
                                        </th>
                                        <th data-sortable="false">
                                            Action
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="text-sm">
                                    <tr v-for="(snip, index) in snippets" :key="index"
                                        :class="{ 'border-l-4 border-success': snippetsSelected.title === snip.title }">

                                        <td class="hidden lg:table-cell">
                                            <div class="flex flex-row flex-wrap items-center">
                                                <div
                                                    class="flex-shrink w-full max-w-full mb-1 font-bold leading-5 text-gray-900 dark:text-gray-300">
                                                    {{ snip.title }}
                                                </div>
                                                <div
                                                    class="flex-shrink w-full max-w-full italic leading-5 text-gray-500">
                                                    {{ snip.about }}
                                                </div>
                                            </div>
                                        </td>

                                        <td>
                                            <div class="font-bold leading-5 text-green-700">
                                                <div v-for="(tg, ind) in snip.tags" :key="ind">{{ tg }}</div>
                                            </div>
                                        </td>
                                        <td class="text-center">

                                            <Tooltip text="Use" side="left">
                                                <template #body>
                                                    <button class="o_btn_icon_square" @click="updateSnippet(snip)">
                                                        <Icon name="material-symbols:arrow-selector-tool" />
                                                    </button>
                                                </template>
                                            </Tooltip>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

</template>
