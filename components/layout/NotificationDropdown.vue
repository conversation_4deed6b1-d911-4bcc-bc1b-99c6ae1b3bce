<script setup lang="ts">
import type { INotification } from '~/types/crm';

interface Props {
  notification: INotification;
}

interface Emits {
  (e: 'click', notification: INotification): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Methods
const handleClick = () => {
  emit('click', props.notification);
};

const formatDate = (date: any): string => {
  if (!date) return '';
  
  const dateObj = date.toDate ? date.toDate() : new Date(date);
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - dateObj.getTime()) / (1000 * 60));
  
  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
  if (diffInMinutes < 43200) return `${Math.floor(diffInMinutes / 1440)}d ago`;
  
  return dateObj.toLocaleDateString();
};

const getNotificationIcon = (type: string): string => {
  switch (type) {
    case 'task_reminder':
      return 'M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4';
    case 'client_update':
      return 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z';
    case 'business_card_approved':
      return 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z';
    case 'business_card_rejected':
      return 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z';
    default:
      return 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z';
  }
};

const getNotificationColor = (type: string): string => {
  switch (type) {
    case 'task_reminder':
      return 'text-blue-500';
    case 'client_update':
      return 'text-green-500';
    case 'business_card_approved':
      return 'text-green-500';
    case 'business_card_rejected':
      return 'text-red-500';
    default:
      return 'text-gray-500';
  }
};

const getPriorityColor = (priority: string): string => {
  switch (priority) {
    case 'high':
      return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20';
    case 'medium':
      return 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/20';
    case 'low':
      return 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800';
    default:
      return 'border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800';
  }
};
</script>

<template>
  <div
    @click="handleClick"
    :class="[
      'p-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors',
      !notification.read ? 'bg-blue-50 dark:bg-blue-900/20' : '',
      notification.priority ? getPriorityColor(notification.priority) : ''
    ]"
  >
    <div class="flex items-start space-x-3">
      <!-- Icon -->
      <div class="flex-shrink-0 pt-1">
        <div :class="[
          'w-8 h-8 rounded-full flex items-center justify-center',
          notification.priority === 'high' ? 'bg-red-100 dark:bg-red-900/30' :
          notification.priority === 'medium' ? 'bg-yellow-100 dark:bg-yellow-900/30' :
          'bg-gray-100 dark:bg-gray-700'
        ]">
          <svg 
            :class="[
              'w-4 h-4',
              getNotificationColor(notification.type)
            ]" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path 
              stroke-linecap="round" 
              stroke-linejoin="round" 
              stroke-width="2" 
              :d="getNotificationIcon(notification.type)" 
            />
          </svg>
        </div>
      </div>
      
      <!-- Content -->
      <div class="flex-1 min-w-0">
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <p :class="[
              'text-sm font-medium',
              !notification.read ? 'text-gray-900 dark:text-white' : 'text-gray-700 dark:text-gray-300'
            ]">
              {{ notification.title }}
            </p>
            
            <p :class="[
              'text-sm mt-1',
              !notification.read ? 'text-gray-600 dark:text-gray-400' : 'text-gray-500 dark:text-gray-500'
            ]">
              {{ notification.message }}
            </p>
            
            <!-- Additional context for task reminders -->
            <div v-if="notification.type === 'task_reminder' && notification.metadata" class="mt-2">
              <div class="flex items-center text-xs text-gray-500 dark:text-gray-400 space-x-2">
                <span v-if="notification.metadata.client_name">
                  👤 {{ notification.metadata.client_name }}
                </span>
                <span v-if="notification.metadata.due_date">
                  📅 Due {{ formatDate(notification.metadata.due_date) }}
                </span>
                <span v-if="notification.metadata.priority" :class="[
                  'px-2 py-1 rounded-full text-xs font-medium',
                  notification.metadata.priority === 'high' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
                  notification.metadata.priority === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                  'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                ]">
                  {{ notification.metadata.priority }}
                </span>
              </div>
            </div>
          </div>
          
          <!-- Timestamp -->
          <div class="flex-shrink-0 ml-2">
            <span class="text-xs text-gray-400 dark:text-gray-500">
              {{ formatDate(notification.created_at) }}
            </span>
          </div>
        </div>
        
        <!-- Action buttons for task reminders -->
        <div v-if="notification.type === 'task_reminder' && notification.actions" class="mt-3 flex space-x-2">
          <button
            v-for="action in notification.actions"
            :key="action.type"
            @click.stop="$emit('action', { notification, action })"
            :class="[
              'px-3 py-1 text-xs font-medium rounded-md transition-colors',
              action.type === 'complete' 
                ? 'bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900/20 dark:text-green-400 dark:hover:bg-green-900/30'
                : action.type === 'snooze'
                ? 'bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                : 'bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:hover:bg-blue-900/30'
            ]"
          >
            {{ action.label }}
          </button>
        </div>
      </div>
      
      <!-- Unread indicator -->
      <div v-if="!notification.read" class="flex-shrink-0">
        <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
      </div>
    </div>
  </div>
</template>