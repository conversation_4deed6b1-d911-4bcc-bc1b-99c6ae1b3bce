<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useNotifications } from '~/composables/useNotifications';
import { useAuth } from '~/composables/useAuth';
import NotificationDropdown from './NotificationDropdown.vue';
import type { INotification } from '~/types/crm';

// Composables
const { showError } = useNotifications();
const { user } = useAuth();

// Component state
const showDropdown = ref(false);
const notifications = ref<INotification[]>([]);
const loading = ref(false);
const unsubscribe = ref<(() => void) | null>(null);

// Computed properties
const unreadCount = computed(() => 
  notifications.value.filter(n => !n.read).length
);

// Methods
onMounted(async () => {
  if (user.value) {
    await loadNotifications();
    setupRealtimeListener();
  }
});

onUnmounted(() => {
  if (unsubscribe.value) {
    unsubscribe.value();
  }
});

const loadNotifications = async () => {
  if (!user.value) return;
  
  loading.value = true;
  try {
    const { db } = await import('~/composables/useFirebase');
    const { collection, query, where, orderBy, limit, getDocs } = await import('firebase/firestore');
    
    const q = query(
      collection(db, 'notifications'),
      where('user_id', '==', user.value.uid),
      orderBy('created_at', 'desc'),
      limit(10)
    );
    
    const snapshot = await getDocs(q);
    notifications.value = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as INotification));
  } catch (error: any) {
    showError(`Failed to load notifications: ${error.message}`);
  } finally {
    loading.value = false;
  }
};

const setupRealtimeListener = async () => {
  if (!user.value) return;
  
  try {
    const { db } = await import('~/composables/useFirebase');
    const { collection, query, where, orderBy, limit, onSnapshot } = await import('firebase/firestore');
    
    const q = query(
      collection(db, 'notifications'),
      where('user_id', '==', user.value.uid),
      orderBy('created_at', 'desc'),
      limit(10)
    );
    
    unsubscribe.value = onSnapshot(q, (snapshot) => {
      notifications.value = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as INotification));
    });
  } catch (error: any) {
    console.error('Failed to setup notification listener:', error);
  }
};

const handleNotificationClick = async (notification: INotification) => {
  // Mark as read if unread
  if (!notification.read) {
    await markAsRead(notification.id);
  }
  
  // Handle action based on notification type
  if (notification.type === 'task_reminder' && notification.action_url) {
    await navigateTo(notification.action_url);
  }
  
  showDropdown.value = false;
};

const markAsRead = async (notificationId: string) => {
  try {
    const { db } = await import('~/composables/useFirebase');
    const { doc, updateDoc } = await import('firebase/firestore');
    
    await updateDoc(doc(db, 'notifications', notificationId), {
      read: true,
      read_at: new Date()
    });
  } catch (error: any) {
    showError(`Failed to mark notification as read: ${error.message}`);
  }
};

const markAllAsRead = async () => {
  if (!user.value) return;
  
  try {
    const { db } = await import('~/composables/useFirebase');
    const { collection, query, where, getDocs, writeBatch } = await import('firebase/firestore');
    
    const q = query(
      collection(db, 'notifications'),
      where('user_id', '==', user.value.uid),
      where('read', '==', false)
    );
    
    const snapshot = await getDocs(q);
    const batch = writeBatch(db);
    
    snapshot.docs.forEach(doc => {
      batch.update(doc.ref, {
        read: true,
        read_at: new Date()
      });
    });
    
    await batch.commit();
  } catch (error: any) {
    showError(`Failed to mark all notifications as read: ${error.message}`);
  }
};

// Close dropdown when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as Element;
  if (!target.closest('.notification-bell')) {
    showDropdown.value = false;
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<template>
  <div class="relative notification-bell">
    <button
      @click="showDropdown = !showDropdown"
      class="relative p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
      :class="{ 'bg-gray-100 dark:bg-gray-700': showDropdown }"
    >
      <!-- Bell Icon -->
      <svg class="w-6 h-6 text-gray-600 dark:text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
      </svg>
      
      <!-- Unread Count Badge -->
      <span
        v-if="unreadCount > 0"
        class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium"
      >
        {{ unreadCount > 9 ? '9+' : unreadCount }}
      </span>
    </button>
    
    <!-- Dropdown -->
    <Transition
      enter-active-class="transition ease-out duration-200"
      enter-from-class="opacity-0 scale-95"
      enter-to-class="opacity-100 scale-100"
      leave-active-class="transition ease-in duration-150"
      leave-from-class="opacity-100 scale-100"
      leave-to-class="opacity-0 scale-95"
    >
      <div
        v-if="showDropdown"
        class="absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50"
      >
        <!-- Header -->
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold text-gray-900 dark:text-white">Notifications</h3>
            <button
              v-if="unreadCount > 0"
              @click="markAllAsRead"
              class="text-sm text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
            >
              Mark all read
            </button>
          </div>
        </div>
        
        <!-- Notifications List -->
        <div class="max-h-96 overflow-y-auto">
          <div v-if="loading" class="p-4 text-center">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
          </div>
          
          <div v-else-if="notifications.length === 0" class="p-8 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
            </svg>
            <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">No notifications yet</p>
          </div>
          
          <div v-else class="divide-y divide-gray-200 dark:divide-gray-700">
            <NotificationDropdown
              v-for="notification in notifications"
              :key="notification.id"
              :notification="notification"
              @click="handleNotificationClick"
            />
          </div>
        </div>
        
        <!-- Footer -->
        <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-b-lg">
          <NuxtLink
            to="/c/notifications"
            class="block text-center text-sm text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
            @click="showDropdown = false"
          >
            View all notifications
          </NuxtLink>
        </div>
      </div>
    </Transition>
  </div>
</template>