<script setup lang="ts">
defineProps({
  textSpin: {
    type: String,
    default: 'Processing...',
  },
  icon: {
    type: String,
    default: '',
  },
})
</script>

<template>
  <div class="flex justify-center items-center space-x-2 text-white">
    <button type="button">
      <div class="flex">
        <div class="animate-spin h-15 w-15 mr-3 i-mdi-loading text-accent" />
        <div class="animate-spin h-15 w-15 mr-3 i-mdi-loading text-secondary" />
        <div class="animate-spin h-15 w-15 mr-3 i-mdi-loading text-secondary_focus" />
      </div>
      <div class="text-lg">
        {{ textSpin }}
      </div>
    </button>
  </div>
</template>
