<script setup lang="ts">
defineProps({
  textSpin: {
    type: String,
    default: 'Processing...',
  },
  icon: {
    type: String,
    default: '',
  },
})
</script>

<template>
  <div class="flex items-center justify-center space-x-2 text-white">
    <button type="button">
      <div class="flex">
        <div class="mr-3 animate-bounce h-15 w-15 i-mdi-rocket text-primary"><Icon name="mdi:rocket"/></div>
        <div class="mr-3 animate-bounce h-15 w-15 i-mdi-rocket text-secondary"><Icon name="mdi:rocket"/></div>
        <div class="mr-3 animate-bounce h-15 w-15 i-mdi-rocket text-tertiary"><Icon name="mdi:rocket"/></div>
      </div>
      <div class="text-xl animate-pulse">
        {{ textSpin }}
      </div>
    </button>
  </div>
</template>
