<script setup lang="ts">
 const loadingItems = useState<any>('loadingItems', () => {
    return {
      loading: false,
      error: false,
      message: '',
      number: 0,
      baseNumber: 0,
      topNumber: 0,
    }
  })
</script>

<template>
  <div>
    <div v-if="loadingItems.loading" class="w-full bg-gray-200 rounded-full dark:bg-gray-700">
      <div class="bg-blue-600 text-xs font-medium text-blue-100 text-center p-0.5 leading-none rounded-full" :style="`width: ${loadingItems.number}%`">
        {{ loadingItems.message }}  {{ loadingItems.number }}%
      </div>
    </div>
  </div>
</template>
