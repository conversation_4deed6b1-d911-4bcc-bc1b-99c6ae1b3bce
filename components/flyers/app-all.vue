<script setup lang="ts">
import * as geofire from "geofire-common";
import { useGeolocation } from "@vueuse/core";

// Use the new Firebase initialization
const { firestore } = useFirebase();

import {
  collection,
  query,
  startAt,
  getDocs,
  orderBy,
  endAt,
  where,
} from "firebase/firestore";
const { coords, locatedAt, error, resume, pause } = useGeolocation();
const { currentSpace } = space();
// Find cities within 50km of London
const center = useState("center", () => [-25.8800624, 28.1530337]);
const radiusInM = useState("radiusInM", () => {
  return 100 * 1000;
});
// const promises = useState("nearbyFlyers", () => {
//   return [];
// });
const promises: any = ref([]);
const shuffle = (array: any) => {
  //remove duplicates
  array = array.filter(
    (v: any, i: any, a: any) => a.findIndex((t: any) => t.id === v.id) === i
  );
  array.sort(() => Math.random() - 0.5);
};

const promisesRemoveDuplicates = computed(() => {
  //remove duplicates
  let array = promises.value.filter(
    (v: any, i: any, a: any) => a.findIndex((t: any) => t.id === v.id) === i
  );
  return array;
});

// Each item in 'bounds' represents a startAt/endAt pair. We have to issue
// a separate query for each pair. There can be up to 9 pairs of bounds
// depending on overlap, but in most cases there are 4.
const gettingPromises = async () => {
  promises.value = [];

  const bounds = geofire.geohashQueryBounds(center.value, radiusInM.value);
  for (const b of bounds) {
    console.log("b", b);
    // const q = query(
    //   collection(firestore, "flyers"),
    //   orderBy("hash"),
    //   startAt(b[0]),
    //   endAt(b[1])
    // );
    const q = query(
      collection(firestore, "flyers"),
      where("status", "==", "Approved")
    );

    const querySnapshot = await getDocs(q);
    querySnapshot.forEach((doc) => {
      let space: any = { id: doc.id, ...doc.data() };
      const lat = space.lat;
      const lng = space.lng;

      if (!lat || !lng) {
        promises.value.push(space);
        return;
      }
      const distanceInKm = geofire.distanceBetween([lat, lng], center.value);
      space.distanceInKm = Number(distanceInKm);
      const distanceInM = distanceInKm * 1000;
      space.distanceInM = Number(distanceInM);
      if (distanceInM <= radiusInM.value) {
        promises.value.push(space);
      }
    });
    shuffle(promises.value);
  }
};

watch(
  () => center.value,
  (val) => {
    promises.value = [];
    gettingPromises();
  }
);

watch(
  () => radiusInM.value,
  (val) => {
    promises.value = [];
    gettingPromises();
  }
);
onMounted(() => {
  promises.value = [];
  setTimeout(() => {
    // console.log('mounted2 coords', [coords.value.coords.latitude, coords.value.coords.longitude])

    // center.value = [coords.value.coords.latitude, coords.value.coords.longitude]
    gettingPromises();
  }, 1000);
  // gettingPromises()
});
</script>

<template>
  <div class="flex space-x-2 overflow-hidden">
    <div
      v-for="(rest, index) in promisesRemoveDuplicates"
      :key="index"
      class="relative h-96 rounded cursor-pointer"
    >
      <flyers-slide-up :schema="rest" />
    </div>
  </div>
</template>
