<script setup lang="ts">

const selectedFlyers: any = useState("selectedFlyers", () => {
  return "";
});

const schema = computed(()=> {
  return {
    name: 'test'
  }
})
</script>

<template>
  <div>
    <layouts-main
      :title="selectedFlyers.name"
    >
      <template #buttons>
        <tooltip-icon
          text="List"
          side="left"
          icon="material-symbols:list-alt-sharp"
          @submit="$router.push('/c/flyers')"
        />
        <tooltip-icon
          text="Edit"
          side="bottom"
          icon="mdi:pencil"
          @submit="$router.push('/c/flyers-edit')"
        />
       
      </template>
      <template #content>
        <div class="w-full">
          <flyers-slide-up :schema="selectedFlyers" />
        </div>
      </template>
    </layouts-main>
  </div>
</template>
