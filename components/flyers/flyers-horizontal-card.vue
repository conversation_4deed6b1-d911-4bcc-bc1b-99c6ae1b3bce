<script setup lang="ts">
const props = defineProps({
  schema: {
    type: Object,
    default: () => ({})
  }
});

// Handle website click with conditional logic
const handleWebsiteClick = (event: Event) => {
  event.preventDefault();
  event.stopPropagation();
  
  if (props.schema.website && props.schema.website.trim()) {
    // If website is provided, redirect to it
    const website = props.schema.website;
    // Ensure the URL has a protocol
    const url = website.startsWith('http://') || website.startsWith('https://') 
      ? website 
      : `https://${website}`;
    window.open(url, '_blank');
  } else {
    // If no website, show a message
    alert('No website provided for this flyer.');
  }
};
</script>

<template>
  <div class="relative overflow-hidden bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-xl border border-white/10 hover:border-white/20 transition-all duration-300 hover:shadow-2xl group transform hover:scale-[1.02]">
    <!-- Horizontal Layout Container -->
    <div class="flex items-stretch">
      <!-- Image Section - Left Side -->
      <div class="w-48 flex-shrink-0 relative overflow-hidden">
        <img 
          v-if="schema.image" 
          :src="schema.image" 
          :alt="schema.name" 
          class="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
        >
        <div v-else class="flex items-center justify-center w-full h-full bg-gradient-to-br from-gray-700 to-gray-800">
          <Icon name="mdi:flare" class="w-12 h-12 text-gray-400" />
        </div>
        
        <!-- Status Badge - Positioned over image -->
        <div class="absolute top-3 left-3">
          <span 
            class="px-2 py-1 text-xs font-medium rounded-full backdrop-blur-sm"
            :class="{
              'bg-green-500/80 text-white border border-green-400/30': schema.status === 'Approved',
              'bg-yellow-500/80 text-white border border-yellow-400/30': schema.status === 'Pending',
              'bg-red-500/80 text-white border border-red-400/30': schema.status === 'Declined',
              'bg-blue-500/80 text-white border border-blue-400/30': !schema.status
            }"
          >
            {{ schema.status || 'Active' }}
          </span>
        </div>
      </div>
      
      <!-- Content Section - Right Side -->
      <div class="flex-1 p-6 flex flex-col justify-between relative">
        <!-- Main Content -->
        <div class="flex-1">
          <h3 class="text-xl font-bold text-white mb-2 leading-tight group-hover:text-blue-300 transition-colors">
            {{ schema.name || 'Untitled Flyer' }}
          </h3>
          
          <p class="text-gray-300 text-sm mb-4 line-clamp-2 leading-relaxed">
            {{ schema.description || 'No description available' }}
          </p>
          
          <!-- Company/Business Info -->
          <div v-if="schema.company" class="flex items-center text-gray-400 text-xs mb-2">
            <Icon name="mdi:office-building" class="w-4 h-4 mr-2 text-blue-400" />
            <span>{{ schema.company }}</span>
          </div>
          
          <!-- Location if available -->
          <div v-if="schema.location || schema.address" class="flex items-center text-gray-400 text-xs mb-3">
            <Icon name="mdi:map-marker" class="w-4 h-4 mr-2 text-green-400" />
            <span>{{ schema.location || schema.address }}</span>
          </div>
        </div>
        
        <!-- Bottom Actions & Info -->
        <div class="flex items-center justify-between pt-4 border-t border-white/10">
          <div class="flex items-center space-x-3">
            <!-- Website Button -->
            <button 
              v-if="schema.website"
              @click="handleWebsiteClick" 
              class="inline-flex items-center px-4 py-2 text-xs font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 transform hover:scale-[1.02] shadow-lg hover:shadow-xl"
            >
              <Icon name="mdi:web" class="w-4 h-4 mr-2" />
              Visit Website
            </button>
            
            <!-- Contact Info -->
            <div v-if="schema.phone || schema.email" class="flex items-center space-x-2 text-xs text-gray-400">
              <Icon v-if="schema.phone" name="mdi:phone" class="w-4 h-4 text-green-400" />
              <Icon v-if="schema.email" name="mdi:email" class="w-4 h-4 text-blue-400" />
            </div>
          </div>
          
          <!-- Date -->
          <div class="text-xs text-gray-500">
            {{ schema.created_at ? new Date(schema.created_at).toLocaleDateString() : 'Recent' }}
          </div>
        </div>
        
        <!-- Decorative gradient elements -->
        <div class="absolute top-4 right-4 w-8 h-8 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-sm"></div>
        <div class="absolute bottom-4 right-8 w-6 h-6 bg-gradient-to-br from-green-400/20 to-blue-400/20 rounded-full blur-sm"></div>
      </div>
    </div>
    
    <!-- Hover Overlay with Call to Action -->
    <div class="absolute inset-0 flex items-center justify-center transition-all duration-300 bg-black/60 backdrop-blur-sm opacity-0 group-hover:opacity-100 rounded-xl">
      <div class="text-center">
        <button class="px-6 py-3 font-medium text-white bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-[1.05] shadow-lg hover:shadow-xl">
          <Icon name="mdi:eye" class="w-5 h-5 mr-2 inline" />
          View Full Details
        </button>
        <p class="text-xs text-gray-300 mt-2 opacity-90">Click to learn more</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Enhanced hover animations */
.group:hover .transform {
  transform: translateY(-2px);
}

/* Subtle shimmer effect */
.group::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s ease;
  z-index: 10;
  pointer-events: none;
}

.group:hover::before {
  left: 100%;
}
</style>