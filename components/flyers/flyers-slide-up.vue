<script setup lang="ts">
const props = defineProps({
  schema: {
    type: Object,
    default: () => ({})
  }
});

// Handle website click with conditional logic
const handleWebsiteClick = (event: Event) => {
  event.preventDefault();
  event.stopPropagation();
  
  if (props.schema.website && props.schema.website.trim()) {
    // If website is provided, redirect to it
    const website = props.schema.website;
    // Ensure the URL has a protocol
    const url = website.startsWith('http://') || website.startsWith('https://') 
      ? website 
      : `https://${website}`;
    window.open(url, '_blank');
  } else {
    // If no website, show a message
    alert('No website provided for this flyer.');
  }
};
</script>

<template>
  <div class="relative h-full overflow-hidden group">
    <!-- Image Section -->
    <div class="h-48 overflow-hidden bg-gray-200">
      <img 
        v-if="schema.image" 
        :src="schema.image" 
        :alt="schema.name" 
        class="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
      >
      <div v-else class="flex items-center justify-center h-full bg-gray-200">
        <svg xmlns="http://www.w3.org/2000/svg" class="w-16 h-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      </div>
    </div>
    
    <!-- Content Section -->
    <div class="p-4">
      <h3 class="mb-2 text-lg font-semibold text-gray-800 dark:text-white">{{ schema.name || 'Untitled Flyer' }}</h3>
      <p class="mb-4 text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
        {{ schema.description || 'No description available' }}
      </p>
      
      <!-- Website Button -->
      <div v-if="schema.website" class="mb-2">
        <button 
          @click="handleWebsiteClick" 
          class="px-3 py-1 text-xs font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
        >
          Visit Website
        </button>
      </div>
      
      <!-- Status Badge -->
      <div class="absolute top-2 right-2">
        <span 
          class="px-2 py-1 text-xs font-medium rounded-full"
          :class="{
            'bg-green-100 text-green-800': schema.status === 'Approved',
            'bg-yellow-100 text-yellow-800': schema.status === 'Pending',
            'bg-red-100 text-red-800': schema.status === 'Declined',
            'bg-gray-100 text-gray-800': !schema.status
          }"
        >
          {{ schema.status || 'Unknown' }}
        </span>
      </div>
      
      <!-- Date -->
      <div class="absolute bottom-2 right-2 text-xs text-gray-500 dark:text-gray-400">
        {{ schema.created_at ? new Date(schema.created_at).toLocaleDateString() : 'No date' }}
      </div>
    </div>
    
    <!-- Hover Overlay -->
    <div class="absolute inset-0 flex items-center justify-center transition-opacity duration-300 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100">
      <button class="px-4 py-2 font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
        View Details
      </button>
    </div>
  </div>
</template>
