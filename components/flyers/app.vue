<script setup lang="ts">
import * as geofire from "geofire-common";
import { useGeolocation } from '@vueuse/core'

import {
  collection,
  query,
  startAt,
  getDocs,
  orderBy,
  endAt,
  where,
} from "firebase/firestore";
const { coords, locatedAt, error, resume, pause } = useGeolocation();
const { currentSpace } = space();
const { firestore } = useFirebase();
// Find cities within 50km of London
const center = useState("center", () => [-25.8800624, 28.1530337]);
const radiusInM = useState('radiusInM', ()=> {
  return 100 * 1000
});
// const promises = useState("nearbyFlyers", () => {
//   return [];
// });
const promises = ref([])

// Each item in 'bounds' represents a startAt/endAt pair. We have to issue
// a separate query for each pair. There can be up to 9 pairs of bounds
// depending on overlap, but in most cases there are 4.
const gettingPromises = async () => {
  promises.value = [];

  const bounds = geofire.geohashQueryBounds(center.value, radiusInM.value);
  for (const b of bounds) {
    console.log('b', b)
    const q = query(
      collection(firestore, "flyers"),
      orderBy("hash"),
      startAt(b[0]),
      endAt(b[1])
    );
    // const q = query(collection(firestoreDb, "spaces"), where("active", "==", true));

    const querySnapshot = await getDocs(q);
    querySnapshot.forEach((doc) => {
      let space = { id: doc.id, ...doc.data() };
      const lat = space.lat;
      const lng = space.lng;

      const distanceInKm = geofire.distanceBetween([lat, lng], center.value);
      space.distanceInKm = Number(distanceInKm);
      const distanceInM = distanceInKm * 1000;
      space.distanceInM = Number(distanceInM);
      if (distanceInM <= radiusInM.value) {
        promises.value.push(space);
      }
    });
  }
};

watch(
  () => center.value,
  (val) => {
    promises.value = [];
    gettingPromises();
  }
);


watch(
  () => radiusInM.value,
  (val) => {
    promises.value = [];
    gettingPromises();
  }
);
onMounted(() => {
  promises.value = [];
  setTimeout(() => {
    // console.log('mounted2 coords', [coords.value.coords.latitude, coords.value.coords.longitude])

    // center.value = [coords.value.coords.latitude, coords.value.coords.longitude]
    gettingPromises();
  }, 1000);
  // gettingPromises()
});

</script>

<template>
    <!-- <div>
        <Carousel :items-to-show="4" :wrap-around="true">
      <Slide v-for="(comp, ind) in promises" :key="ind">
        <card-slide-up :schema="comp" />
      </Slide>

      <template #addons>
        <Navigation />
        <Pagination />
      </template>
    </Carousel>
    </div> -->
  <div class="flex space-x-2 overflow-hidden">
    <div
      v-for="(rest, index) in promises"
      :key="index"
      class="relative h-96 rounded cursor-pointer"
    >
    <flyers-slide-up :schema="rest" />

    </div>
  </div>

</template>
