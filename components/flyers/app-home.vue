<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import {
  collection,
  query,
  getDocs,
  limit,
  orderBy,
} from "firebase/firestore";

// Use the new Firebase initialization
const { firestore } = useFirebase();

// Use ad spot content composable
const {
  adSpotContent,
  isLoading: isLoadingAds,
  fetchAdSpotContent,
  getMixedContent
} = useAdSpotContent();

const flyers = ref([]);
const isLoading = ref(false); // Set to false initially to prevent loading spinner
const error = ref(null);
const maxFlyers = 6; // Limit to 6 flyers for the homepage

// Get mixed content (flyers + ad spots)
const mixedContent = computed(() => {
  return getMixedContent(flyers.value, maxFlyers);
});

// Check if we have enough content to fill the grid
const needsPlaceholders = computed(() => {
  return mixedContent.value.length < maxFlyers;
});

// Generate placeholder count
const placeholderCount = computed(() => {
  return maxFlyers - mixedContent.value.length;
});

// Check if we're loading either flyers or ads
const isLoadingContent = computed(() => {
  return isLoading.value || isLoadingAds.value;
});

// Check if we're on the home page
const route = useRoute();
const isFromHomePage = computed(() => {
  return route.path === '/';
});

const fetchFlyers = async () => {
  isLoading.value = true;
  error.value = null;
  flyers.value = [];

  try {
    // Check if Firestore is initialized
    if (!firestore) {
      console.warn('Firestore is not initialized, skipping data fetch');
      flyers.value = [];
      isLoading.value = false;
      return;
    }

    // Fetch both flyers and ad content in parallel with timeout
    const timeout = new Promise((_, reject) => 
      setTimeout(() => reject(new Error('Request timeout')), 10000)
    );

    const [flyersResult, adContentResult] = await Promise.allSettled([
      Promise.race([fetchFlyersData(), timeout]),
      Promise.race([fetchAdSpotContent(), timeout])
    ]);

    // Handle flyers result
    if (flyersResult.status === 'fulfilled') {
      flyers.value = flyersResult.value || [];
      console.log('Flyers loaded:', flyers.value.length);
    } else {
      console.error('Failed to fetch flyers:', flyersResult.reason);
      flyers.value = [];
    }

    // Handle ad content result
    if (adContentResult.status === 'fulfilled') {
      console.log('Ad content loaded:', adSpotContent.value.length);
      console.log('Ad content data:', adSpotContent.value);
    } else {
      console.error('Failed to fetch ad content:', adContentResult.reason);
    }

  } catch (err) {
    console.error("Error fetching content:", err);
    error.value = err.message;
    flyers.value = [];
  } finally {
    isLoading.value = false;
  }
};

// Separate function to fetch flyers data
const fetchFlyersData = async () => {
  // First try to fetch flyers from the API endpoint
  try {
    const response = await fetch('/api/flyers');
    if (response.ok) {
      const data = await response.json();
      const limitedData = data.slice(0, maxFlyers);
      // Shuffle the flyers for variety
      shuffle(limitedData);
      return limitedData;
    } else {
      console.log('API response not ok:', response.status, response.statusText);
    }
  } catch (apiError) {
    console.log('API fetch failed, falling back to Firestore:', apiError);
  }

  // Fallback to direct Firestore query
  try {
    if (!firestore) {
      console.warn('Firestore not available for direct query');
      return [];
    }

    const q = query(
      collection(firestore, "flyers"),
      orderBy("created_at", "desc"),
      limit(maxFlyers)
    );

    const querySnapshot = await getDocs(q);
    const flyersData = [];

    if (!querySnapshot.empty) {
      querySnapshot.forEach((doc) => {
        let flyer = { id: doc.id, ...doc.data() };
        flyersData.push(flyer);
      });
      // Shuffle the flyers for variety
      shuffle(flyersData);

      // Cache the results for offline use
      try {
        const cache = await caches.open('firestore-cache');
        await cache.put('/api/flyers', new Response(JSON.stringify(flyersData)));
      } catch (cacheError) {
        console.error('Error caching flyers:', cacheError);
      }
    } else {
      console.log('No flyers found in Firestore');
    }

    return flyersData;
  } catch (firestoreError) {
    console.error('Firestore query failed:', firestoreError);
    return [];
  }
};

function shuffle(array) {
  let currentIndex = array.length, randomIndex;

  // While there remain elements to shuffle
  while (currentIndex != 0) {
    // Pick a remaining element
    randomIndex = Math.floor(Math.random() * currentIndex);
    currentIndex--;

    // And swap it with the current element
    [array[currentIndex], array[randomIndex]] = [
      array[randomIndex], array[currentIndex]
    ];
  }

  return array;
}

// Handle ad click events
const handleAdClick = (adContent) => {
  console.log('Ad clicked:', adContent);
  // Open the click URL if available
  if (adContent.click_url) {
    window.open(adContent.click_url, '_blank');
  }
  // Additional analytics or tracking can be added here
}

onMounted(() => {
  // Temporarily disable data fetching to prevent JavaScript errors
  // fetchFlyers();
  console.log('FlyersAppHome mounted - data fetching temporarily disabled');
});

// Expose refresh function for external use
defineExpose({
  refresh: fetchFlyers
});
</script>

<template>
  <!-- Loading State -->
  <div v-if="isLoadingContent" class="flex justify-center items-center py-12">
    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
  </div>

  <!-- Error State -->
  <div v-else-if="error" class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg text-center">
    <p class="text-red-600 dark:text-red-400">
      Unable to load flyers. Please try again later.
    </p>
    <button
      @click="fetchFlyers"
      class="mt-2 px-4 py-2 bg-red-100 dark:bg-red-800 text-red-600 dark:text-red-300 rounded-md hover:bg-red-200 dark:hover:bg-red-700"
    >
      Retry
    </button>
  </div>

  <!-- Empty State -->
  <div v-else-if="mixedContent.length === 0" class="bg-blue-50 dark:bg-blue-900/20 p-8 rounded-lg text-center">
    <div class="flex justify-center mb-4">
      <Icon name="mdi:flare" class="text-blue-500" size="48" />
    </div>
    <h3 class="text-xl font-semibold text-blue-700 dark:text-blue-300 mb-2">No Content Available</h3>
    <p class="text-blue-600 dark:text-blue-400">
      Be the first to advertise your business here!
    </p>
    <button
      v-if="!isFromHomePage"
      @click="$router.push('/upload')"
      class="mt-4 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
    >
      Create Flyer
    </button>
  </div>

  <!-- 2x3 Grid Layout for Horizontal Cards -->
  <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <!-- Mixed Content (Flyers + Ad Spots) -->
    <div
      v-for="(item, index) in mixedContent"
      :key="item.isAd ? `ad-${item.subscription.id}` : `flyer-${item.id}`"
      class="cursor-pointer"
      @click="item.isAd ? handleAdClick(item) : $router.push(`/flyers/${item.id}`)"
    >
      <!-- Ad Spot Content (Horizontal Layout) -->
      <div
        v-if="item.isAd"
        class="relative overflow-hidden bg-gradient-to-br from-purple-900/70 via-purple-800/70 to-purple-900/70 backdrop-blur-sm rounded-xl border border-purple-400/20 hover:border-purple-400/40 transition-all duration-300 hover:shadow-2xl group transform hover:scale-[1.02] h-full"
      >
        <div class="flex flex-col h-full">
          <!-- Ad Image Section -->
          <div class="h-32 relative overflow-hidden flex-shrink-0">
            <div class="flex items-center justify-center w-full h-full bg-gradient-to-br from-purple-600 to-purple-800">
              <Icon name="mdi:star" class="w-8 h-8 text-yellow-300" />
            </div>
            <div class="absolute top-2 left-2">
              <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-500/80 text-white border border-yellow-400/30">
                Sponsored
              </span>
            </div>
          </div>
          <!-- Ad Content -->
          <div class="flex-1 p-4 flex flex-col justify-between">
            <div>
              <h3 class="text-lg font-bold text-white mb-2 leading-tight">{{ item.title || 'Featured Advertisement' }}</h3>
              <p class="text-purple-200 text-sm mb-3 line-clamp-2">{{ item.description || 'Premium advertising space available' }}</p>
            </div>
            <div class="flex items-center justify-between pt-3 border-t border-purple-400/20">
              <button class="inline-flex items-center px-3 py-1 text-xs font-medium text-white bg-gradient-to-r from-purple-600 to-purple-700 rounded-lg hover:from-purple-700 hover:to-purple-800">
                <Icon name="mdi:open-in-new" class="w-3 h-3 mr-1" />
                Learn More
              </button>
              <div class="text-xs text-purple-300">Featured</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Regular Flyer Content (Grid Layout) -->
      <div v-else class="h-full">
        <FlyersGridCard :schema="item" />
      </div>
    </div>

    <!-- Grid Placeholder Cards if needed -->
    <div
      v-for="i in placeholderCount"
      :key="`placeholder-${i}`"
      class="relative overflow-hidden bg-gradient-to-br from-gray-800/50 via-gray-700/50 to-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-600/30 hover:border-gray-500/50 transition-all duration-300 group h-full"
    >
      <div class="flex flex-col h-full">
        <!-- Placeholder Image Section -->
        <div class="h-32 relative overflow-hidden flex-shrink-0">
          <div class="flex items-center justify-center w-full h-full bg-gradient-to-br from-blue-600/20 to-purple-600/20">
            <Icon name="mdi:flare" class="w-8 h-8 text-blue-400/60" />
          </div>
        </div>
        
        <!-- Placeholder Content -->
        <div class="flex-1 p-4 flex flex-col justify-center">
          <div class="text-center">
            <h3 class="text-sm font-semibold text-gray-300 mb-2">Advertise Your Business Here</h3>
            <p class="text-xs text-gray-400 mb-3">Reach thousands of potential customers</p>
            
            <div class="flex flex-col items-center space-y-2">
              <button
                v-if="!isFromHomePage"
                @click.stop="$router.push('/upload')"
                class="inline-flex items-center px-4 py-1 text-xs font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200"
              >
                <Icon name="mdi:plus" class="w-3 h-3 mr-1" />
                Create Ad
              </button>
              
              <div class="text-xs text-gray-500">
                Premium placement
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
