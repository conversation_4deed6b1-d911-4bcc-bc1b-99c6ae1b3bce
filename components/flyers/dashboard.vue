<script setup lang="ts">
const selectedFlyers = useState('selectedFlyers', ()=> { return {}})
onMounted(()=> {
  selectedFlyers.value = {}
})
  </script>
  
  <template>
        <layouts-main title="Flyers">
          <template #buttons><tooltip-icon text="Dashboard" side="left" icon="line-md:grid-3-filled" @submit="$router.push('/c/dashboard')"/></template>
        <template #content> 
        <div class="w-full overflow-hidden">
          <flyers-lists />
        </div>
        </template>
      </layouts-main>
  </template>