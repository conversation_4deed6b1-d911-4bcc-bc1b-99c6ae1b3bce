# Business Card Processing with Vertex AI

This guide explains how to use the business card processing feature that leverages your existing Vertex AI/Gemini integration for OCR and automatic form filling.

## Overview

The business card processing system provides:
- **AI-powered OCR** using Google's Gemini 1.5 Flash model
- **Automatic form filling** with extracted contact information
- **High accuracy** text recognition and data extraction
- **Error handling** for failed processing or incomplete data
- **Multiple integration options** for different use cases

## Quick Start

### 1. Basic Usage with Composable

```typescript
import { useBusinessCardProcessor } from '~/composables/useBusinessCardProcessor'

const {
  processBusinessCard,
  isProcessing,
  extractedData,
  error
} = useBusinessCardProcessor()

// Process a business card image
const handleImageUpload = async (file: File) => {
  try {
    const result = await processBusinessCard(file)
    if (result.success) {
      console.log('Extracted data:', result.data)
    }
  } catch (err) {
    console.error('Processing failed:', err.message)
  }
}
```

### 2. Using the Complete Form Component

```vue
<template>
  <BusinessCardForm
    @submit="handleFormSubmit"
    @auto-fill="handleAutoFill"
    @error="handleError"
  />
</template>

<script setup>
const handleFormSubmit = (data) => {
  // Handle form submission with extracted + user data
  console.log('Form data:', data)
}

const handleAutoFill = (data) => {
  // Handle successful auto-fill
  console.log('Auto-filled:', data)
}

const handleError = (error) => {
  // Handle processing errors
  console.error('Error:', error)
}
</script>
```

### 3. Simple Drop-in Component

```vue
<template>
  <SimpleBusinessCardProcessor
    @data-extracted="handleDataExtracted"
    @error="handleError"
  />
</template>

<script setup>
const handleDataExtracted = (data) => {
  // Use extracted data in your application
  console.log('Extracted:', data)
}
</script>
```

## API Reference

### useBusinessCardProcessor()

The main composable for business card processing.

#### State

- `isProcessing: Ref<boolean>` - Whether processing is in progress
- `progress: Ref<number>` - Processing progress (0-100)
- `error: Ref<string | null>` - Current error message
- `lastResult: Ref<ProcessingResult | null>` - Last processing result
- `extractedData: ComputedRef<BusinessCardData | null>` - Extracted data

#### Methods

##### processBusinessCard(file, options?)

Process a business card image file.

```typescript
const result = await processBusinessCard(file, {
  returnRawText: false,
  enhancementLevel: 'standard', // 'basic' | 'standard' | 'advanced'
  timeout: 30000
})
```

**Parameters:**
- `file: File` - Image file to process
- `options?: ProcessingOptions` - Processing options

**Returns:** `Promise<ProcessingResult>`

##### autoFillForm(formData, mapping?)

Auto-fill a form object with extracted data.

```typescript
const formData = reactive({
  firstName: '',
  lastName: '',
  email: '',
  // ... other fields
})

autoFillForm(formData, {
  firstName: 'firstName',
  lastName: 'lastName',
  companyName: 'company', // Custom mapping
  emailAddress: 'email'
})
```

##### Other Methods

- `reset()` - Clear all state and results
- `clearError()` - Clear error state only
- `validateImageFile(file)` - Validate image file format and size
- `createReactiveForm(initialData?)` - Create a reactive form object
- `getProcessingStats()` - Get processing statistics
- `exportAsVCard()` - Export data as vCard string
- `downloadVCard(filename?)` - Download vCard file

## Data Structures

### BusinessCardData

```typescript
interface BusinessCardData {
  firstName: string
  lastName: string
  fullName: string
  company: string
  jobTitle: string
  email: string
  phone: string
  website: string
  address: {
    street: string
    city: string
    state: string
    postalCode: string
    country: string
    full: string
  }
  linkedin: string
  twitter: string
  additionalInfo: string
}
```

### ProcessingResult

```typescript
interface ProcessingResult {
  success: boolean
  data: BusinessCardData
  confidence: number
  processingTime: number
  engine: string
  error?: string
}
```

## Advanced Usage

### Custom Field Mapping

You can customize how extracted data maps to your form fields:

```typescript
const customMapping = {
  // formField: extractedDataPath
  fullName: 'fullName',
  companyName: 'company',
  position: 'jobTitle',
  emailAddress: 'email',
  phoneNumber: 'phone',
  websiteUrl: 'website',
  streetAddress: 'address.street',
  city: 'address.city',
  state: 'address.state',
  zipCode: 'address.postalCode'
}

autoFillForm(formData, customMapping)
```

### Error Handling

```typescript
const processCard = async (file: File) => {
  try {
    const result = await processBusinessCard(file)
    
    if (!result.success) {
      throw new Error(result.error || 'Processing failed')
    }
    
    // Handle successful processing
    console.log('Confidence:', result.confidence)
    console.log('Processing time:', result.processingTime)
    
  } catch (error) {
    if (error.message.includes('Unsupported file type')) {
      // Handle file type error
    } else if (error.message.includes('File too large')) {
      // Handle file size error
    } else if (error.message.includes('API quota exceeded')) {
      // Handle API quota error
    } else {
      // Handle general processing error
    }
  }
}
```

### Processing Statistics

```typescript
const stats = getProcessingStats()
if (stats) {
  console.log('Fields extracted:', stats.fieldsExtracted)
  console.log('Confidence:', stats.confidence)
  console.log('Processing time:', stats.processingTime)
  console.log('Engine used:', stats.engine)
}
```

## Components

### BusinessCardForm

A complete form component with business card upload and auto-fill functionality.

**Props:**
- `initialData?: Record<string, any>` - Initial form data
- `customFieldMapping?: Record<string, string>` - Custom field mapping

**Events:**
- `@submit` - Form submission with data
- `@auto-fill` - Successful auto-fill with extracted data
- `@error` - Processing error

### SimpleBusinessCardProcessor

A minimal component for quick business card processing.

**Events:**
- `@data-extracted` - Data successfully extracted
- `@error` - Processing error

## Configuration

### Environment Variables

Make sure these environment variables are set:

```bash
# Required for Gemini Vision API
GEMINI_API_KEY=your_gemini_api_key
# OR
GOOGLE_AI_API_KEY=your_google_ai_api_key
```

### Supported File Formats

- **JPEG** (.jpg, .jpeg)
- **PNG** (.png)
- **WebP** (.webp)
- **Maximum file size:** 10MB

## Best Practices

1. **File Validation:** Always validate files before processing
2. **Error Handling:** Implement comprehensive error handling
3. **User Feedback:** Show processing progress and results
4. **Form Validation:** Validate auto-filled data before submission
5. **Confidence Checking:** Check confidence scores for data quality

## Troubleshooting

### Common Issues

1. **"Gemini API authentication failed"**
   - Check that `GEMINI_API_KEY` is set correctly
   - Verify API key has proper permissions

2. **"API quota exceeded"**
   - Check your Gemini API usage limits
   - Implement rate limiting if needed

3. **"Unsupported file type"**
   - Ensure file is JPEG, PNG, or WebP format
   - Check file extension and MIME type

4. **Low confidence scores**
   - Try higher quality images
   - Ensure good lighting and contrast
   - Use `enhancementLevel: 'advanced'` option

5. **Processing timeout**
   - Increase timeout in processing options
   - Check network connectivity
   - Try smaller image files

## Demo

Visit `/business-card-demo` to see a live demonstration of the business card processing functionality.
