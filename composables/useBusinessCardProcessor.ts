/**
 * Business Card Processor Composable
 * Streamlined solution for processing business card images and auto-filling forms
 * Leverages existing Vertex AI/Gemini integration
 */

import { ref, computed } from 'vue'

// Business card data structure for form auto-fill
export interface BusinessCardData {
  firstName: string
  lastName: string
  fullName: string
  company: string
  jobTitle: string
  email: string
  phone: string
  website: string
  address: {
    street: string
    city: string
    state: string
    postalCode: string
    country: string
    full: string
  }
  linkedin: string
  twitter: string
  additionalInfo: string
}

// Processing result
export interface ProcessingResult {
  success: boolean
  data: BusinessCardData
  confidence: number
  processingTime: number
  engine: string
  error?: string
}

// Processing options
export interface ProcessingOptions {
  returnRawText?: boolean
  enhancementLevel?: 'basic' | 'standard' | 'advanced'
  timeout?: number
}

export const useBusinessCardProcessor = () => {
  // State
  const isProcessing = ref(false)
  const progress = ref(0)
  const error = ref<string | null>(null)
  const lastResult = ref<ProcessingResult | null>(null)

  // Computed
  const canProcess = computed(() => !isProcessing.value)
  const hasResult = computed(() => !!lastResult.value?.success)
  const extractedData = computed(() => lastResult.value?.data || null)

  /**
   * Convert file to base64 data URL
   */
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = () => reject(new Error('Failed to read file'))
      reader.readAsDataURL(file)
    })
  }

  /**
   * Validate image file
   */
  const validateImageFile = (file: File): boolean => {
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    const maxSize = 10 * 1024 * 1024 // 10MB

    if (!validTypes.includes(file.type)) {
      error.value = `Unsupported file type: ${file.type}. Please use JPEG, PNG, or WebP.`
      return false
    }

    if (file.size > maxSize) {
      error.value = `File too large: ${(file.size / 1024 / 1024).toFixed(1)}MB. Maximum size is 10MB.`
      return false
    }

    return true
  }

  /**
   * Process business card image using Gemini Vision API
   */
  const processBusinessCard = async (
    file: File,
    options: ProcessingOptions = {}
  ): Promise<ProcessingResult> => {
    if (!canProcess.value) {
      throw new Error('Processing already in progress')
    }

    // Validate file
    if (!validateImageFile(file)) {
      throw new Error(error.value || 'Invalid file')
    }

    isProcessing.value = true
    progress.value = 0
    error.value = null

    try {
      // Convert to base64
      progress.value = 20
      const imageData = await fileToBase64(file)

      // Call Gemini Vision API
      progress.value = 40
      const response = await $fetch('/api/ocr/gemini-vision', {
        method: 'POST',
        body: {
          image: imageData,
          returnRawText: options.returnRawText || false,
          enhancementLevel: options.enhancementLevel || 'standard'
        },
        timeout: options.timeout || 30000
      })

      progress.value = 80

      if (!response.success) {
        throw new Error(response.error || 'Processing failed')
      }

      // Transform the response to our format
      const transformedData = transformGeminiResponse(response.data)
      
      const result: ProcessingResult = {
        success: true,
        data: transformedData,
        confidence: response.confidence || 0,
        processingTime: response.processingTime || 0,
        engine: response.engine || 'gemini-flash-1.5'
      }

      progress.value = 100
      lastResult.value = result
      
      return result

    } catch (err: any) {
      console.error('Business card processing failed:', err)
      
      const errorMessage = err.message || 'Failed to process business card'
      error.value = errorMessage
      
      const result: ProcessingResult = {
        success: false,
        data: getEmptyBusinessCardData(),
        confidence: 0,
        processingTime: 0,
        engine: 'none',
        error: errorMessage
      }
      
      lastResult.value = result
      throw new Error(errorMessage)
      
    } finally {
      isProcessing.value = false
    }
  }

  /**
   * Transform Gemini API response to our BusinessCardData format
   */
  const transformGeminiResponse = (geminiData: any): BusinessCardData => {
    const fullName = [geminiData.firstName, geminiData.lastName]
      .filter(Boolean)
      .join(' ')

    const fullAddress = [
      geminiData.address?.street,
      geminiData.address?.city,
      geminiData.address?.state,
      geminiData.address?.postalCode,
      geminiData.address?.country
    ].filter(Boolean).join(', ')

    return {
      firstName: geminiData.firstName || '',
      lastName: geminiData.lastName || '',
      fullName,
      company: geminiData.company || '',
      jobTitle: geminiData.jobTitle || '',
      email: geminiData.email || '',
      phone: geminiData.phone || '',
      website: geminiData.website || '',
      address: {
        street: geminiData.address?.street || '',
        city: geminiData.address?.city || '',
        state: geminiData.address?.state || '',
        postalCode: geminiData.address?.postalCode || '',
        country: geminiData.address?.country || '',
        full: fullAddress
      },
      linkedin: geminiData.linkedin || '',
      twitter: geminiData.twitter || '',
      additionalInfo: geminiData.additionalInfo || ''
    }
  }

  /**
   * Get empty business card data structure
   */
  const getEmptyBusinessCardData = (): BusinessCardData => ({
    firstName: '',
    lastName: '',
    fullName: '',
    company: '',
    jobTitle: '',
    email: '',
    phone: '',
    website: '',
    address: {
      street: '',
      city: '',
      state: '',
      postalCode: '',
      country: '',
      full: ''
    },
    linkedin: '',
    twitter: '',
    additionalInfo: ''
  })

  /**
   * Auto-fill form with extracted data
   */
  const autoFillForm = (formData: Record<string, any>, mapping?: Record<string, string>) => {
    if (!extractedData.value) {
      console.warn('No extracted data available for auto-fill')
      return formData
    }

    const data = extractedData.value
    const fieldMapping = mapping || {
      // Default field mappings
      firstName: 'firstName',
      lastName: 'lastName',
      name: 'fullName',
      fullName: 'fullName',
      company: 'company',
      organization: 'company',
      jobTitle: 'jobTitle',
      title: 'jobTitle',
      position: 'jobTitle',
      email: 'email',
      phone: 'phone',
      telephone: 'phone',
      website: 'website',
      url: 'website',
      address: 'address.full',
      street: 'address.street',
      city: 'address.city',
      state: 'address.state',
      postalCode: 'address.postalCode',
      zipCode: 'address.postalCode',
      country: 'address.country',
      linkedin: 'linkedin',
      twitter: 'twitter'
    }

    // Apply mappings
    Object.keys(fieldMapping).forEach(formField => {
      const dataPath = fieldMapping[formField]
      const value = getNestedValue(data, dataPath)
      
      if (value && formField in formData) {
        formData[formField] = value
      }
    })

    return formData
  }

  /**
   * Get nested object value by path (e.g., 'address.street')
   */
  const getNestedValue = (obj: any, path: string): any => {
    return path.split('.').reduce((current, key) => current?.[key], obj)
  }

  /**
   * Clear processing state and results
   */
  const reset = () => {
    isProcessing.value = false
    progress.value = 0
    error.value = null
    lastResult.value = null
  }

  /**
   * Clear only the error state
   */
  const clearError = () => {
    error.value = null
  }

  /**
   * Create a reactive form object that can be auto-filled
   */
  const createReactiveForm = (initialData: Record<string, any> = {}) => {
    return reactive({
      firstName: '',
      lastName: '',
      fullName: '',
      company: '',
      jobTitle: '',
      email: '',
      phone: '',
      website: '',
      address: '',
      street: '',
      city: '',
      state: '',
      postalCode: '',
      country: '',
      linkedin: '',
      twitter: '',
      ...initialData
    })
  }

  /**
   * Get processing statistics
   */
  const getProcessingStats = () => {
    if (!lastResult.value) return null

    const data = lastResult.value.data
    const fieldsExtracted = Object.values(data).filter(value => {
      if (typeof value === 'object' && value !== null) {
        return Object.values(value).some(v => v && v.toString().trim())
      }
      return value && value.toString().trim()
    }).length

    return {
      fieldsExtracted,
      confidence: lastResult.value.confidence,
      processingTime: lastResult.value.processingTime,
      engine: lastResult.value.engine,
      success: lastResult.value.success
    }
  }

  /**
   * Export extracted data as vCard format
   */
  const exportAsVCard = (): string | null => {
    if (!extractedData.value) return null

    const data = extractedData.value
    const vcard = [
      'BEGIN:VCARD',
      'VERSION:3.0',
      `FN:${data.fullName}`,
      data.firstName ? `N:${data.lastName};${data.firstName};;;` : '',
      data.company ? `ORG:${data.company}` : '',
      data.jobTitle ? `TITLE:${data.jobTitle}` : '',
      data.email ? `EMAIL:${data.email}` : '',
      data.phone ? `TEL:${data.phone}` : '',
      data.website ? `URL:${data.website}` : '',
      data.address.full ? `ADR:;;${data.address.street};${data.address.city};${data.address.state};${data.address.postalCode};${data.address.country}` : '',
      data.linkedin ? `URL:${data.linkedin}` : '',
      'END:VCARD'
    ].filter(Boolean).join('\n')

    return vcard
  }

  /**
   * Download extracted data as vCard file
   */
  const downloadVCard = (filename?: string) => {
    const vcard = exportAsVCard()
    if (!vcard || !extractedData.value) return

    const blob = new Blob([vcard], { type: 'text/vcard' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename || `${extractedData.value.fullName || 'contact'}.vcf`
    link.click()
    URL.revokeObjectURL(url)
  }

  return {
    // State
    isProcessing: readonly(isProcessing),
    progress: readonly(progress),
    error: readonly(error),
    lastResult: readonly(lastResult),

    // Computed
    canProcess,
    hasResult,
    extractedData,

    // Methods
    processBusinessCard,
    autoFillForm,
    reset,
    clearError,
    getEmptyBusinessCardData,
    validateImageFile,
    createReactiveForm,
    getProcessingStats,
    exportAsVCard,
    downloadVCard
  }
}
