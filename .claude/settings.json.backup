{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(uv:*)", "Bash(find:*)", "<PERSON><PERSON>(mv:*)", "Bash(grep:*)", "Bash(npm:*)", "Bash(ls:*)", "Bash(cp:*)", "Write", "Edit", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(touch:*)"], "deny": []}, "hooks": {"PreToolUse": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run .claude/hooks/pre_tool_use.py"}]}], "PostToolUse": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run .claude/hooks/post_tool_use.py"}]}], "Notification": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run .claude/hooks/notification.py --notify"}]}], "Stop": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run .claude/hooks/stop.py --chat"}]}, {"matcher": "", "hooks": [{"type": "command", "command": "bash -c 'DIR=$(pwd); while [ \"$DIR\" != \"/\" ] && [ ! -d \"$DIR/.claude\" ]; do DIR=$(dirname \"$DIR\"); done; [ -d \"$DIR/.claude\" ] && cat | \"$DIR/.claude/hooks/universal-hook-runner.sh\" notification-hook.sh || exit 2'"}]}], "SubagentStop": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run .claude/hooks/subagent_stop.py"}]}, {"matcher": "", "hooks": [{"type": "command", "command": "bash -c 'DIR=$(pwd); while [ \"$DIR\" != \"/\" ] && [ ! -d \"$DIR/.claude\" ]; do DIR=$(dirname \"$DIR\"); done; [ -d \"$DIR/.claude\" ] && cat | \"$DIR/.claude/hooks/universal-hook-runner.sh\" notification-hook.sh || exit 2'"}]}], "UserPromptSubmit": [{"hooks": [{"type": "command", "command": "uv run .claude/hooks/user_prompt_submit.py --log-only"}]}], "PreCompact": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run .claude/hooks/pre_compact.py"}]}], "SessionStart": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run .claude/hooks/session_start.py"}]}]}}