# Sub-Agent Context Package: Testing
Generated: Tue  8 Jul 2025 11:25:40 SAST
Sub-Agent Type: testing
Complexity: medium
Tasks: test tasks

## Domain-Specific Context

### Testing Architecture Context


### Test Strategy & Coverage
# PIB-METHOD Persistent Planning System

## Overview

This directory contains the enhanced PIB-METHOD persistent planning system that addresses the critical gaps in EPIC/story workflows and cross-session context continuity.

## System Enhancement Summary

### ✅ What We've Built
# PIB-METHOD Persistent Planning System

## Overview

This directory contains the enhanced PIB-METHOD persistent planning system that addresses the critical gaps in EPIC/story workflows and cross-session context continuity.

## System Enhancement Summary

### ✅ What We've Built

### Testing Standards
- Test coverage requirements (>90%)
- Testing pyramid strategy
- Performance testing requirements
- Security testing standards


## Inter-Sub-Agent Coordination

### Dependencies
- Frontend component and integration testing
- Backend API and performance testing
- DevOps deployment and infrastructure testing

### Communication Protocols
- Report completion to <PERSON> (PM) for orchestration updates
- Coordinate with dependent sub-agents before proceeding
- Escalate blockers immediately to appropriate resolver
- Maintain context bridge updates for cross-session continuity


## Recommended Tools (Domain-Optimized)
zen-chat
context7
playwright
zen-testgen
zen-analyze
