# Sub-Agent Context Package: Backend
Generated: Tue  8 Jul 2025 11:25:11 SAST
Sub-Agent Type: backend
Complexity: complex
Tasks: implement user preference API,create notification service,build analytics collection

## Domain-Specific Context

### Backend Architecture Context


### Database & Data Models


### Backend Performance Standards
- API response time requirements
- Database query optimization
- Security standards (authentication, authorization)
- Error handling and logging


## Planning Context (Domain-Filtered)
- Implement real-time data updates

**Backend Sub-Agent Dependencies**:
- User preference API endpoints
- Real-time notification service
- Analytics data aggregation
- Widget configuration persistence

**LEVER Framework Application**:
- **Leverage**: Existing authentication system and design components
- **Extend**: Current notification service for real-time updates
- **Verify**: Performance through Core Web Vitals compliance
- **Eliminate**: Duplicate widget configuration logic
- **Reduce**: Complexity through modular architecture

## Inter-Sub-Agent Coordination

### Dependencies
- Frontend API requirements and data needs
- DevOps database and infrastructure setup
- Testing API validation and performance requirements

### Communication Protocols
- Report completion to <PERSON> (PM) for orchestration updates
- Coordinate with dependent sub-agents before proceeding
- Escalate blockers immediately to appropriate resolver
- Maintain context bridge updates for cross-session continuity


## Recommended Tools (Domain-Optimized)
zen-chat
context7
zen-analyze
zen-debug
zen-secaudit
zen-thinkdeep
zen-consensus
