# PIB-METHOD Agent Mapping

## Consolidated Agent Locations

All agents are now in `pib-core/agents/`:

### Core Agents (from BMAD)
- `.claude/commands/BMad/agents/analyst.md` → `pib-core/agents/core/analyst.md`
- `.claude/commands/BMad/agents/pm.md` → `pib-core/agents/core/pm.md`
- `.claude/commands/BMad/agents/architect.md` → `pib-core/agents/core/architect.md`
- `.claude/commands/BMad/agents/dev.md` → `pib-core/agents/core/dev.md`

### Specialized Agents (from PIB)
- `.claude/agents/code-reviewer.md` → `pib-core/agents/specialized/code-reviewer.md`
- `.claude/agents/qa-tester.md` → `pib-core/agents/specialized/qa-tester.md`
- `.claude/agents/change-implementer.md` → `pib-core/agents/specialized/change-implementer.md`
- `.claude/agents/platform-engineer.md` → `pib-core/agents/specialized/platform-engineer.md`
- `.claude/agents/task-executor.md` → `pib-core/agents/specialized/task-executor.md`
- `.claude/agents/orchestrator.md` → `pib-core/agents/specialized/orchestrator.md`

### Additional Agents in .claude/agents/
The following remain in `.claude/agents/` as they are Claude Code specific:
- meta-agent.md - For creating new agents
- work-completion-summary.md - For audio summaries
- Other specialized category agents (design/, engineering/, marketing/, etc.)

## Usage
All core PIB-METHOD agents are accessed the same way:
```
@agent *task
```

The consolidated agents in pib-core have:
- Enhanced MCP tool integration
- LEVER framework compliance
- Unified methodology approach
