# Quick Guide: When to Create Agents vs Use Documentation

## 🎯 Simple Decision Tree

```
Is it used frequently? (daily/weekly)
├─ YES → Create a specialist agent
└─ NO → Is it complex or critical?
    ├─ YES → Consider creating agent
    └─ NO → Just fetch docs when needed
```

## ✅ Create a Specialist Agent For:

### Core Technologies (Always Create)

- **Main framework**: React, Vue, Angular
- **Backend framework**: Express, Django, Rails
- **Primary database**: PostgreSQL, MongoDB
- **Main language**: Beyond basic syntax

### High-Usage Libraries

- Used in **10+ files**
- Modified **weekly or more**
- **Core to application** functionality
- Team needs to **master it**

### Complex Patterns

- **State management**: Redux, MobX
- **Authentication**: Auth0, Firebase Auth
- **Real-time**: Socket.io, WebSockets
- **Testing frameworks**: Jest, Cypress

## 📄 Use Documentation Only For:

### One-Off Libraries

- **Data parsing**: CSV, XML parsers
- **File generation**: PDFs, Excel
- **Utility libraries**: date-fns, lodash
- **Migration tools**: Used once

### External APIs

- **Payment**: Stripe, PayPal (always changing)
- **Email**: SendGrid, Mailgun
- **SMS**: Twilio
- **Cloud services**: AWS, Google APIs

### Simple Libraries

- **< 5 files** use it
- **Straightforward API**
- **Well-documented**
- **Stable interface**

## 🔢 Quick Scoring System

Rate each aspect 1-3:

- **Frequency**: (1=Rare, 2=Sometimes, 3=Daily)
- **Complexity**: (1=Simple, 2=Moderate, 3=Complex)
- **Criticality**: (1=Nice-to-have, 2=Important, 3=Critical)

**Total Score**:

- **7-9**: Definitely create specialist
- **4-6**: Consider creating specialist
- **1-3**: Use documentation only

## 💡 Practical Examples

### ✅ Create Specialist

```bash
# React - Score: 9 (3+3+3)
/workflows:create-tech-agent "React" --research

# PostgreSQL - Score: 8 (3+3+2)
/workflows:create-tech-agent "PostgreSQL" --research
```

### 📄 Documentation Only

```bash
# PDF Generator - Score: 3 (1+1+1)
# Just fetch PDFKit docs when needed

# Stripe API - Score: 5 (2+2+3)
# Fetch latest API docs each time
```

## 🚀 How It Works in Practice

### When universal-dev-agent encounters a library:

1. **Checks for specialist** → Uses if exists
2. **Checks for plugin** → Loads if exists
3. **Analyzes usage** → Decides approach
4. **If low usage** → Fetches docs via Context7
5. **If high usage** → Suggests creating specialist

### Example Flow:

```
Task: "Add CSV export feature"
→ Detects: csv-writer library
→ Checks: Used in 1 file only
→ Decision: Fetch docs
→ Action: Gets latest csv-writer docs
→ Result: Implements with current API
```

## 📝 Document in CLAUDE.md

Add to your project's CLAUDE.md:

```markdown
## Technology Usage Classification

### High-Frequency (Specialists Created)

- React (150+ components)
- Express (all APIs)
- PostgreSQL (primary DB)

### Medium-Frequency (Plugins Used)

- Jest (testing)
- Redis (caching)

### Low-Frequency (Docs On-Demand)

- PDFKit (2 files)
- csv-parser (1 file)
- stripe (payment module only)
```

## 🎯 Bottom Line

**Create agents for what you use daily.** **Fetch docs for what you use
rarely.**

This keeps your project lean while ensuring expertise where it matters!
