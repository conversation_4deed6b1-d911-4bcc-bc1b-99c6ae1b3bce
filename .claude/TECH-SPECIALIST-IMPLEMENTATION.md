# Technology Specialist Implementation Plan

**IMPORTANT**: Technology specialists are created per-project, not globally synced. Use `/workflows:create-tech-agent` to create specialists for your specific project needs. The tech-specialists directory is excluded from sync to avoid conflicts between projects with different technology stacks.

## Overview
This document outlines the complete implementation of technology-specific agents in PIB-METHOD, inspired by the awesome-claude-agents approach but enhanced with online research capabilities and deep integration with our existing LEVER framework.

## Project-Specific Design

Tech specialists are intentionally project-specific because:
- Different projects use different technology versions
- Custom patterns and conventions vary by project
- Avoiding conflicts when syncing between projects
- Specialists can be tailored to project needs

## Key Innovations

### 1. Research-Based Agent Creation
Unlike static agent definitions, our specialists are created through:
- **Online Research**: Using Perplexity MCP to gather latest best practices
- **Documentation Analysis**: Context7 integration for official docs
- **Pattern Recognition**: Identifying common patterns and anti-patterns
- **Community Standards**: Incorporating real-world usage patterns

### 2. Technology Stack Auto-Detection
- **Automatic Analysis**: Scan project files to detect technologies
- **Version Awareness**: Identify specific versions for precise expertise
- **Dependency Mapping**: Understand the full technology ecosystem
- **Dynamic Configuration**: Generate project-specific routing rules

### 3. Intelligent Orchestration
- **Tech Lead Coordinator**: Routes tasks based on technology expertise
- **Parallel Execution**: Multiple specialists work simultaneously
- **Smart Handoffs**: Seamless transitions between technologies
- **Integration Points**: Ensure smooth cross-technology collaboration

## Implementation Components

### New Agent Types

#### 1. Technology Specialists
Located in `.claude/agents/tech-specialists/`:
- **Frontend**: React, Vue, Angular, Svelte specialists
- **Backend**: Express, Django, Laravel, Rails, FastAPI specialists
- **Database**: PostgreSQL, MongoDB, Redis experts
- **DevOps**: Docker, Kubernetes, CI/CD specialists

#### 2. Orchestrators
Located in `.claude/agents/orchestrators/`:
- **tech-lead**: Main technology coordinator
- **project-analyst**: Stack detection and analysis

### New Commands

#### `/workflows:project-init-tech`
- Detects technology stack automatically
- Assigns appropriate specialists
- Generates routing configuration
- Integrates with existing project-init

#### `/workflows:create-tech-agent`
- Creates new specialists with online research
- Validates against official documentation
- Tests with real-world examples
- Ensures LEVER compliance

### Enhanced CLAUDE.md Format
```markdown
## Technology Stack
- Frontend: React 18, TypeScript
- Backend: Express.js, Node.js
- Database: PostgreSQL, Redis

## Agent Assignments
- /src/components/* → @react-specialist
- /src/api/* → @express-specialist
- /src/db/* → @postgresql-expert

## Workflow Rules
- "create component" → @react-specialist → @code-reviewer
- "add API endpoint" → @express-specialist → @qa-tester
```

## Integration with PIB-METHOD

### 1. Preserves LEVER Framework
All technology specialists still follow:
- **L**: Leverage framework features
- **E**: Extend existing patterns
- **V**: Verify with framework tools
- **E**: Eliminate anti-patterns
- **R**: Reduce to idiomatic code

### 2. Enhances Existing Workflow
- **Phase 0**: New technology detection phase in project-init
- **Story Creation**: Include tech specialist assignments
- **Orchestration**: Tech-aware task routing
- **Quality Gates**: Framework-specific reviews

### 3. Maintains Quality Standards
- Minimum LEVER score: 4/5
- Framework best practices enforced
- Security reviews by specialists
- Performance optimization built-in

## Rollout Plan

### Phase 1: Core Implementation (Week 1)
- [ ] Create directory structure
- [ ] Implement tech-lead orchestrator
- [ ] Build stack detection script
- [ ] Create agent templates

### Phase 2: Initial Specialists (Week 2)
- [ ] React specialist with research
- [ ] Express specialist with research
- [ ] PostgreSQL expert with research
- [ ] Create routing system

### Phase 3: Command Integration (Week 3)
- [ ] Implement project-init-tech
- [ ] Create create-tech-agent command
- [ ] Update existing workflows
- [ ] Test end-to-end flow

### Phase 4: Extended Specialists (Week 4+)
- [ ] Add Vue, Angular specialists
- [ ] Create Django, Laravel experts
- [ ] Build MongoDB, Redis specialists
- [ ] Implement DevOps specialists

## Usage Examples

### New Project with Stack Detection
```bash
> init -> /workflows:project-init

[Phase 0: Technology Detection]
Detected: React 18, Express.js, PostgreSQL
Creating specialist assignments...
✓ Assigned @react-specialist for frontend
✓ Assigned @express-specialist for backend
✓ Assigned @postgresql-expert for database

[Continuing with standard initialization...]
```

### Creating New Specialist
```bash
> create-agent -> /workflows:create-tech-agent "Svelte" --research

[Researching Svelte best practices...]
✓ Found official documentation
✓ Analyzed 15 best practice guides
✓ Identified common patterns

[Creating specialist...]
✓ Generated svelte-specialist.md
✓ Added routing patterns
✓ Validated with examples

Specialist ready for use!
```

### Task Execution with Specialists
```bash
> help -> /workflows:dev-command "create user profile component"

[Tech Lead Analysis]
Task requires: React component with TypeScript
Routing to: @react-specialist

[React Specialist Execution]
✓ Created ProfileComponent.tsx
✓ Added custom hooks
✓ Implemented error boundaries
✓ Added accessibility features

[Automatic Review]
@code-reviewer: LEVER score 5/5 ✓
```

## Benefits

### 1. Higher Code Quality
- Framework-specific best practices
- Idiomatic code for each technology
- Reduced anti-patterns

### 2. Faster Development
- Specialists know optimal approaches
- Parallel execution of tasks
- Less refactoring needed

### 3. Better Maintainability
- Consistent patterns per technology
- Clear separation of concerns
- Well-documented decisions

### 4. Continuous Learning
- Research-based updates
- Adapts to new versions
- Incorporates community feedback

## Success Metrics
- 50% reduction in framework-specific bugs
- 30% faster feature implementation
- 90% LEVER compliance on first pass
- 40% less refactoring required

## Next Steps
1. Review and approve implementation plan
2. Create initial specialist agents
3. Test with pilot project
4. Gather feedback and iterate
5. Roll out to all projects

This implementation brings the best of awesome-claude-agents' specialized approach while maintaining PIB-METHOD's quality standards and adding research-based continuous improvement.