# Project Initialization Quick Reference

## 🚀 Quick Start Commands

### Brand New Project

```bash
/workflows:project-init
```

Creates everything from scratch including tech specialists.

### Existing Project Takeover

```bash
/workflows:project-init --existing-project
```

Analyzes existing code and creates missing pieces.

### Just Tech Detection

```bash
/workflows:project-init-tech --research --create-missing
```

Only creates tech specialists and routing.

## 📄 What Gets Created

### Complete Documentation Set

- ✅ `docs/project-brief.md` - Project overview
- ✅ `docs/prd.md` - Product requirements
- ✅ `docs/architecture.md` - System design
- ✅ `docs/frontend-architecture.md` - UI architecture
- ✅ `docs/uxui-spec.md` - UX/UI specifications
- ✅ `docs/tech-stack.md` - Technology decisions
- ✅ `docs/stories/*.story.md` - Implementation stories
- ✅ `docs/orchestration-guide.md` - Development coordination

### Technology Specialists (Project-Specific)

- ✅ React specialist (if React detected)
- ✅ Express specialist (if Express detected)
- ✅ Database specialists (PostgreSQL, MongoDB, etc.)
- ✅ Testing specialists (Je<PERSON>, <PERSON>yte<PERSON>, etc.)
- ✅ Any other detected technologies

### Configuration

- ✅ Enhanced CLAUDE.md with routing rules
- ✅ Agent assignments by file pattern
- ✅ Technology version tracking
- ✅ Quality gate configuration

## 🔄 Existing Project Workflow

1. **First Time Setup**

   ```bash
   # In your existing project directory
   cd my-existing-project

   # Run PIB sync (if not done)
   # Then initialize
   /workflows:project-init --existing-project
   ```

2. **What Happens**
   - Scans all code files
   - Detects technologies and versions
   - Creates specialized agents
   - Generates missing documentation
   - Preserves existing work

3. **Results**
   - Tech specialists ready to use
   - Complete documentation
   - Agent routing configured
   - Ready for enhancements

## 🎯 Common Scenarios

### React + Node.js Project

```bash
/workflows:project-init --existing-project
# Creates: react-specialist, express-specialist, jest-specialist
```

### Python Django Project

```bash
/workflows:project-init --existing-project
# Creates: django-specialist, python-specialist, pytest-specialist
```

### Full Stack with Multiple DBs

```bash
/workflows:project-init --existing-project
# Creates: All needed specialists based on detection
```

## 💡 Tips

1. **Always run project-init first** - It sets up everything
2. **Tech specialists are project-specific** - Not shared between projects
3. **Documentation is comprehensive** - All architectural decisions included
4. **Existing code is analyzed** - PRD reverse-engineered from code
5. **Nothing is overwritten** - Only missing pieces are created

## 🚨 Important Notes

- Tech specialists stay in YOUR project (not synced)
- Each project gets its own specialists
- Different versions = different patterns
- Run this AFTER syncing PIB-METHOD to your project

## 📊 Time Estimates

- **New Project**: ~30-45 minutes for complete setup
- **Existing Project**: ~20-30 minutes (depending on size)
- **Tech Detection Only**: ~5-10 minutes

## 🆘 Troubleshooting

**Q: Command not found?** A: Make sure you've synced PIB-METHOD first

**Q: No specialists created?** A: Add `--create-missing` flag

**Q: Wrong version detected?** A: Manually specify in
`/workflows:create-tech-agent "React" --version 18`

**Q: Documentation seems wrong?** A: For takeovers, some manual review is
expected
