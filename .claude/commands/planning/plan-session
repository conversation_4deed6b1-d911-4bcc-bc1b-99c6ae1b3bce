#!/bin/bash

# Interactive Planning Session
# Start an interactive planning session that follows the Q-LEVER framework

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m' # No Color

# Print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Find project root
find_project_root() {
    local dir="$(pwd)"
    while [ "$dir" != "/" ]; do
        if [ -d "$dir/.claude" ]; then
            echo "$dir"
            return 0
        fi
        dir=$(dirname "$dir")
    done
    return 1
}

# Parse command line arguments
parse_arguments() {
    # Check for help first
    if [ $# -eq 1 ] && [ "$1" = "--help" ]; then
        show_help
        exit 0
    fi
    
    if [ $# -lt 1 ]; then
        print_status $RED "Error: Task description is required"
        echo
        echo "Usage: /plan-session \"<task_description>\" [--ai copilot|claude] [--scope small|medium|large]"
        echo
        echo "Examples:"
        echo "  /plan-session \"implement user authentication system\""
        echo "  /plan-session \"create analytics dashboard\" --ai claude --scope medium"
        exit 1
    fi
    
    TASK_DESCRIPTION="$1"
    AI_ASSISTANT="claude"  # Default to Claude for planning
    SCOPE="medium"
    
    shift
    while [[ $# -gt 0 ]]; do
        case $1 in
            --ai)
                AI_ASSISTANT="$2"
                shift 2
                ;;
            --scope)
                SCOPE="$2"
                shift 2
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_status $RED "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # Validate AI assistant
    if [ "$AI_ASSISTANT" != "copilot" ] && [ "$AI_ASSISTANT" != "claude" ]; then
        print_status $RED "Error: AI assistant must be 'copilot' or 'claude'"
        exit 1
    fi
    
    # Validate scope
    if [ "$SCOPE" != "small" ] && [ "$SCOPE" != "medium" ] && [ "$SCOPE" != "large" ]; then
        print_status $RED "Error: Scope must be 'small', 'medium', or 'large'"
        exit 1
    fi
}

show_help() {
    echo "Interactive Planning Session - Q-LEVER Framework"
    echo
    echo "Usage: /plan-session \"<task_description>\" [options]"
    echo
    echo "Options:"
    echo "  --ai <copilot|claude>    Choose AI assistant (default: claude)"
    echo "  --scope <small|medium|large>  Task complexity hint (default: medium)"
    echo "  --help                   Show this help"
    echo
    echo "Examples:"
    echo "  /plan-session \"implement user auth\""
    echo "  /plan-session \"refactor payment system\" --ai claude --scope large"
}

# Create planning session
create_session() {
    local project_root
    project_root=$(find_project_root)
    if [ -z "$project_root" ]; then
        print_status $RED "Error: Could not find project root with .claude directory"
        exit 1
    fi
    
    # Create planning state directory
    local planning_dir="$project_root/.claude/state/planning"
    mkdir -p "$planning_dir"
    
    # Generate session ID
    local session_id="plan-$(date +%Y%m%d-%H%M%S)"
    local session_file="$planning_dir/$session_id.json"
    
    # Create session metadata
    cat > "$session_file" << EOF
{
  "session_id": "$session_id",
  "task_description": "$TASK_DESCRIPTION",
  "ai_assistant": "$AI_ASSISTANT",
  "scope": "$SCOPE",
  "created_at": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "status": "questioning",
  "questions_asked": [],
  "answers_provided": [],
  "assumptions": [],
  "plan": null,
  "approved": false
}
EOF
    
    echo "$session_id"
}

# Generate planning prompt
generate_planning_prompt() {
    local session_id="$1"
    
    cat << EOF
# Q-LEVER Interactive Planning Session

## Task Description
$TASK_DESCRIPTION

## Session Instructions
This is an interactive planning session following the Q-LEVER framework. You must:

1. **START WITH QUESTIONS** - Before any planning or implementation suggestions
2. **Ask clarifying questions** to understand requirements, constraints, and context
3. **Validate assumptions** - Don't assume anything about technology choices, scope, or approach
4. **One question at a time** - Ask focused, specific questions
5. **Wait for answers** - Don't proceed until user provides answers

## Q-LEVER Framework Compliance
- **Q**: Question ALL assumptions and clarify requirements FIRST
- **L**: Leverage existing patterns (ask about current architecture)
- **E**: Extend before creating (ask about existing systems)
- **V**: Verify through testing (ask about testing preferences)
- **E**: Eliminate duplication (ask about existing similar functionality)
- **R**: Reduce complexity (ask about constraints and preferences)

## Scope Context
Task scope: $SCOPE
- Small: Simple feature/fix, 1-2 days
- Medium: Feature with multiple components, 1-2 weeks  
- Large: Complex system/refactor, multiple weeks

## Your Role
AI Assistant: $AI_ASSISTANT
Session ID: $session_id

## First Step
Start by asking your first clarifying question about the task. Do NOT provide any implementation suggestions yet. Focus on understanding the requirements first.

Example questions you might ask:
- What specific functionality is needed?
- Are there existing systems this should integrate with?
- What are the technical constraints or preferences?
- Who are the target users and how will they use this?
- What does success look like for this task?

Ask your first question now:
EOF
}

# Start interactive session
start_session() {
    local session_id="$1"
    
    print_status $GREEN "PIB-METHOD Interactive Planning Session"
    print_status $GREEN "======================================"
    echo
    print_status $BLUE "Session ID: $session_id"
    print_status $BLUE "Task: $TASK_DESCRIPTION"
    print_status $BLUE "AI Assistant: $AI_ASSISTANT"
    print_status $BLUE "Scope: $SCOPE"
    echo
    
    print_status $CYAN "🤔 Starting Q-LEVER planning process..."
    print_status $YELLOW "The AI will ask clarifying questions before creating any implementation plan."
    echo
    
    # Generate the planning prompt
    local prompt
    prompt=$(generate_planning_prompt "$session_id")
    
    # Display instructions for user
    print_status $MAGENTA "Instructions:"
    echo "1. The AI will ask you clarifying questions"
    echo "2. Answer each question thoroughly"
    echo "3. Ask for clarification if questions are unclear"
    echo "4. Once all questions are answered, AI will create a detailed plan"
    echo "5. Review and approve the plan before implementation"
    echo
    
    print_status $BLUE "Planning prompt generated. Copy and paste the following into your AI assistant:"
    echo
    print_status $CYAN "=== COPY FROM HERE ==="
    echo "$prompt"
    print_status $CYAN "=== COPY TO HERE ==="
    echo
    
    print_status $YELLOW "After the AI responds with questions, use these commands:"
    echo "• Continue the conversation naturally"
    echo "• Use '/clarify-requirements' if you need to add more context"
    echo "• Use '/confirm-approach' when you're ready to approve the final plan"
    echo
    
    print_status $GREEN "Session started! The AI will guide you through the Q-LEVER planning process."
}

# Main execution
main() {
    parse_arguments "$@"
    
    print_status $BLUE "Creating new planning session..."
    local session_id
    session_id=$(create_session)
    
    start_session "$session_id"
}

# Run main function with all arguments
main "$@"