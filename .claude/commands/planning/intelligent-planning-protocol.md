# Intelligent Planning Protocol for Claude Code

## MANDATORY: Search Codebase Before Asking Questions

When handling ANY planning request, follow this protocol:

### 1. ALWAYS Start with Codebase Analysis

Use these tools in order:

```bash
# 1. Search for technology stack
> Use Grep tool to find package.json, requirements.txt, Cargo.toml, etc.
> Use Read tool to analyze main config files

# 2. Search for existing patterns
> Use Glob tool to find similar components/modules
> Use Grep tool to search for authentication, API patterns, etc.

# 3. Analyze current architecture
> Use Read tool on main entry points (app.js, main.py, etc.)
> Use Grep tool to find database connections, auth setup
```

### 2. Codebase Analysis Template

```markdown
Let me analyze your codebase first to understand the current setup...

🔍 **Codebase Analysis:**

**Technology Stack:**
- Frontend: [Found in package.json/config]
- Backend: [Found in server files]  
- Database: [Found in connection configs]
- Authentication: [Found in auth modules]
- UI Framework: [Found in dependencies]
- Testing: [Found in test configs]

**Existing Patterns:**
- [List reusable patterns found]
- [Similar implementations to leverage]
- [Architecture patterns in use]

**Integration Points:**
- [How new feature connects]
- [Dependencies to consider]
- [Potential conflicts]
```

### 3. What to Search vs What to Ask

#### SEARCH THE CODEBASE FOR:
- Framework/technology choices
- Database type and configuration
- Authentication implementation
- API structure (REST/GraphQL)
- UI component patterns
- State management approach
- Testing setup
- Build configuration
- Existing similar features
- Code organization patterns

#### ASK QUESTIONS ABOUT:
- Business requirements and scope
- User experience expectations
- Priority and constraints
- External integrations not in code
- Performance/security requirements
- User roles and permissions
- Success criteria and metrics

### 4. Smart Planning Questions

After codebase analysis, ask targeted questions:

#### ✅ Good Questions (after analysis):
```
I found your Express.js API with JWT auth and React frontend. 
For this new user dashboard feature:

1. Which user roles should have access to the dashboard?
2. What specific metrics/data should be displayed?
3. Should it integrate with your existing auth middleware in /middleware/auth.js?
4. Any performance requirements for data loading?
```

#### ❌ Bad Questions (ask codebase first):
```
1. What database are you using?
2. Do you have authentication set up?
3. What frontend framework should we use?
4. Should we use REST or GraphQL?
```

### 5. LEVER-Enhanced Analysis

Apply LEVER principles during analysis:

```markdown
**LEVER Analysis:**
- **L**everage: Found AuthService in /src/services/ - can reuse
- **E**xtend: Can extend BaseController pattern from /src/controllers/
- **V**erify: Jest tests in /src/__tests__/ - follow same pattern
- **E**liminate: Found duplicate validation in forms - opportunity to consolidate  
- **R**educe: Functional components preferred - keep simple

**Preliminary LEVER Score:** 4.3/5 (strong foundation to build on)
```

### 6. Planning Agent Behavior

When user requests planning (planning session, requirements clarification, etc.):

1. **Immediately start codebase analysis** using tools
2. **Present findings** in structured format
3. **Only then ask** business/requirement questions
4. **Create plan** that leverages existing patterns
5. **Apply LEVER scoring** throughout

### 7. Tool Usage Priority

For codebase analysis:
1. **Glob** - Find relevant files by pattern
2. **Grep** - Search for specific implementations
3. **Read** - Analyze key configuration/entry files
4. **TodoWrite** - Track analysis progress if complex

### 8. Example Planning Session

```
User: "I need to implement user notifications"

Agent: Let me analyze your codebase first to understand the current setup...

[Uses Glob to find existing notification patterns]
[Uses Grep to search for email/messaging services]
[Uses Read to check package.json and main config files]

🔍 **Analysis Results:**
- Found Express.js backend with Nodemailer setup
- React frontend with existing notification components in /src/components/Notification/
- Socket.io already configured for real-time features
- User model has notification preferences field

**Existing Patterns to Leverage:**
- NotificationService.js in /src/services/
- EmailTemplate components in /src/templates/
- Real-time socket connection established

Now I have some questions about the business requirements:

1. What types of notifications do you want to add? (Email, push, in-app, SMS?)
2. Should users be able to customize notification preferences?
3. What events should trigger notifications?
4. Any compliance requirements for email notifications?

[Continues with planning based on both codebase analysis AND user requirements]
```

### 9. Quality Checklist

Before presenting any plan:
- [ ] Performed comprehensive codebase analysis using tools
- [ ] Identified technology stack from code, not assumptions
- [ ] Found existing patterns to leverage
- [ ] Asked only about business requirements not in code
- [ ] Applied LEVER principles to analysis
- [ ] Provided specific file/line references where relevant

## Remember: Be a detective first, consultant second. The code tells the technical story - your job is to understand the business story.