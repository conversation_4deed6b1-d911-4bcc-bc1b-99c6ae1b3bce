# Confirm Approach

Approve and finalize implementation plans after the Q-LEVER planning process, enabling transition from planning to development.

## Usage
```bash
approach -> /planning:confirm-approach-approach [--session session_id] [--modifications "<changes>"] [--create-branch] [--ai copilot|claude]
```

## Examples
```bash
# Approve current planning session
approach -> /planning:confirm-approach-approach

# Approve with minor modifications
approach -> /planning:confirm-approach-approach --modifications "Use PostgreSQL instead of MySQL for better JSON support"

# Approve and create feature branch
approach -> /planning:confirm-approach-approach --session plan-20250125-143022 --create-branch

# Approve specific session
approach -> /planning:confirm-approach-approach --session plan-20250125-143022
```

## What This Command Does

### 1. Plan Validation
- **Reviews final plan** against Q-LEVER framework
- **Validates completeness** of requirements and assumptions
- **Confirms technical approach** and architecture decisions
- **Verifies scope alignment** with original requirements

### 2. Approval Process
- **Documents approval** with timestamp and user confirmation
- **Saves final plan** as approved implementation guide
- **Creates implementation checklist** based on plan
- **Sets up tracking** for plan execution

### 3. Development Transition
- **Generates implementation tasks** from approved plan
- **Creates branch** for feature development (optional)
- **Sets up monitoring** for plan adherence during development
- **Enables plan-guided development** workflows

## Approval Workflow

### Plan Review Phase
```
Current Plan Summary:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

## Task: Implement User Authentication System

### Q-LEVER Compliance ✅
✓ Q: All assumptions documented and validated
✓ L: Leverages existing User model and Express middleware  
✓ E: Extends current API structure with new auth endpoints
✓ V: Comprehensive test suite (unit, integration, e2e)
✓ E: Eliminates duplicate auth logic across services
✓ R: Minimal implementation using JWT + refresh tokens

### Architecture Decisions
- JWT-based authentication with 15min access tokens
- Redis for refresh token storage (7-day expiry)
- bcrypt for password hashing (12 rounds)
- Rate limiting: 5 attempts per 15min per IP
- Integration with existing User model (extend schema)

### Implementation Steps
1. Database migrations (add auth fields to users table)
2. Authentication middleware (JWT verification)
3. Auth API endpoints (/login, /register, /refresh, /logout)
4. Password validation and security utilities
5. Frontend integration points
6. Comprehensive test suite
7. Documentation updates

### Risk Assessment
- Low risk: Using proven patterns and libraries
- Database migration requires coordination
- Rate limiting needs Redis dependency
- Security review required before production

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

Do you approve this plan? (y/n):
```

### Modification Handling
If modifications are requested:
```bash
approach -> /planning:confirm-approach-approach --modifications "Use session-based auth instead of JWT for simplicity"
```

The AI will:
1. **Analyze impact** of requested changes
2. **Update plan** with modifications
3. **Re-validate Q-LEVER compliance**
4. **Present updated plan** for final approval

### Branch Creation
With `--create-branch` flag:
- Creates feature branch: `feature/user-auth-YYYYMMDD`
- Applies approved plan as branch description
- Sets up plan tracking in branch metadata
- Links plan to development workflow

## Parameters
- `--session`: Specific planning session to approve
- `--modifications`: Last-minute changes to incorporate
- `--create-branch`: Automatically create feature branch for implementation
- `--ai`: AI assistant that created the plan

## Session State Management

### Before Approval
```json
{
  "session_id": "plan-20250125-143022",
  "status": "planning",
  "plan": { ... },
  "approved": false
}
```

### After Approval
```json
{
  "session_id": "plan-20250125-143022", 
  "status": "approved",
  "plan": { ... },
  "approved": true,
  "approved_at": "2025-01-25T14:32:15Z",
  "approved_by": "user",
  "modifications": "Use PostgreSQL instead of MySQL",
  "implementation_branch": "feature/user-auth-20250125"
}
```

## Integration with Development Workflow

### Immediate Next Steps
After approval, you can:
```bash
# Start implementation using approved plan
> /workflows:dev-command --use-plan plan-20250125-143022

# Create GitHub issue with plan context
issue -> /github:ai-issue-issue claude "Implement user authentication" --plan plan-20250125-143022

# Start feature development with plan
start -> /workflows:feature-start-start user-auth --plan plan-20250125-143022
```

### Plan-Guided Development
- **Implementation checklist** auto-generated from plan
- **Progress tracking** against approved architecture
- **Quality gates** based on Q-LEVER compliance
- **Automatic plan adherence** validation

## Quality Gates

### Pre-Approval Validation
- [ ] All Q-LEVER principles addressed
- [ ] Technical decisions documented with rationale
- [ ] Implementation steps clearly defined
- [ ] Risk assessment completed
- [ ] Testing strategy specified
- [ ] Integration points identified

### Post-Approval Setup
- [ ] Plan saved as implementation guide
- [ ] Development tasks generated
- [ ] Branch created (if requested)
- [ ] Plan tracking enabled
- [ ] Next steps communicated

## Plan Modifications

### Minor Modifications (In-Session)
```bash
# Technology substitutions
approach -> /planning:confirm-approach-approach --modifications "Use Redis instead of in-memory sessions"

# Scope adjustments
approach -> /planning:confirm-approach-approach --modifications "Phase 1: Basic auth only, Phase 2: OAuth integration"

# Implementation details
approach -> /planning:confirm-approach-approach --modifications "Add API rate limiting with express-rate-limit"
```

### Major Modifications (New Session)
For significant changes, start a new planning session:
```bash
session -> /planning:plan-session-session "implement user authentication with OAuth and 2FA support"
```

## Error Handling

### Common Issues
- **No active session**: Prompts to start planning session
- **Incomplete plan**: Requests missing Q-LEVER elements
- **Conflicting requirements**: Suggests clarification steps
- **Technical inconsistencies**: Highlights issues for resolution

### Recovery Actions
```bash
# If plan is incomplete
requirements -> /planning:clarify-requirements-requirements "Please complete the testing strategy section"

# If modifications needed
approach -> /planning:confirm-approach-approach --modifications "Add monitoring and alerting requirements"

# If starting over
session -> /planning:plan-session-session "revised requirements with additional context"
```

## Best Practices

### Before Approval
1. **Review thoroughly** - Check all Q-LEVER elements
2. **Validate scope** - Ensure plan matches requirements
3. **Check feasibility** - Confirm technical approach is sound
4. **Consider timeline** - Verify implementation estimates
5. **Review risks** - Understand potential issues

### After Approval
1. **Stick to plan** - Avoid scope creep during implementation
2. **Track progress** - Monitor adherence to approved approach
3. **Document deviations** - Record any necessary changes
4. **Update stakeholders** - Communicate plan and progress
5. **Review outcomes** - Learn from plan vs actual implementation

## Success Metrics
- **Plan completeness**: All Q-LEVER elements addressed
- **Implementation accuracy**: Code matches approved architecture
- **Timeline adherence**: Development follows estimated schedule
- **Quality achievement**: Testing and quality goals met
- **Stakeholder satisfaction**: Requirements fulfilled as planned

## Related Commands
- `session -> /planning:plan-session-session` - Start interactive planning process
- `requirements -> /planning:clarify-requirements-requirements` - Ask additional questions before approval
- `> /workflows:dev-command --use-plan` - Implement using approved plan
- `start -> /workflows:feature-start-start --plan` - Create feature branch with plan context
- `issue -> /github:ai-issue-issue --plan` - Create GitHub issue with plan details