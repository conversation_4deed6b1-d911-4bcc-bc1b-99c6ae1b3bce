#!/bin/bash

# Confirm Approach
# Approve and finalize implementation plans after the Q-LEVER planning process

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m' # No Color

# Print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Find project root
find_project_root() {
    local dir="$(pwd)"
    while [ "$dir" != "/" ]; do
        if [ -d "$dir/.claude" ]; then
            echo "$dir"
            return 0
        fi
        dir=$(dirname "$dir")
    done
    return 1
}

# Parse command line arguments
parse_arguments() {
    SESSION_ID=""
    MODIFICATIONS=""
    CREATE_BRANCH=false
    AI_ASSISTANT=""
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --session)
                SESSION_ID="$2"
                shift 2
                ;;
            --modifications)
                MODIFICATIONS="$2"
                shift 2
                ;;
            --create-branch)
                CREATE_BRANCH=true
                shift
                ;;
            --ai)
                AI_ASSISTANT="$2"
                shift 2
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_status $RED "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

show_help() {
    echo "Confirm Approach - Q-LEVER Framework"
    echo
    echo "Usage: /confirm-approach [options]"
    echo
    echo "Options:"
    echo "  --session <session_id>       Approve specific planning session"
    echo "  --modifications \"<changes>\"   Apply modifications before approval"
    echo "  --create-branch              Create feature branch for implementation"
    echo "  --ai <copilot|claude>        AI assistant that created the plan"
    echo "  --help                       Show this help"
    echo
    echo "Examples:"
    echo "  /confirm-approach"
    echo "  /confirm-approach --modifications \"Use PostgreSQL instead of MySQL\""
    echo "  /confirm-approach --session plan-20250125-143022 --create-branch"
}

# Find most recent planning session if not specified
find_recent_session() {
    local project_root
    project_root=$(find_project_root)
    if [ -z "$project_root" ]; then
        return 1
    fi
    
    local planning_dir="$project_root/.claude/state/planning"
    if [ ! -d "$planning_dir" ]; then
        return 1
    fi
    
    # Find most recent session file
    local recent_session
    recent_session=$(ls -t "$planning_dir"/plan-*.json 2>/dev/null | head -n1)
    if [ -n "$recent_session" ]; then
        SESSION_ID=$(basename "$recent_session" .json)
        return 0
    fi
    return 1
}

# Load session data
load_session() {
    local project_root
    project_root=$(find_project_root)
    if [ -z "$project_root" ]; then
        print_status $RED "Error: Could not find project root"
        return 1
    fi
    
    local session_file="$project_root/.claude/state/planning/$SESSION_ID.json"
    if [ ! -f "$session_file" ]; then
        print_status $RED "Error: Session file not found: $session_file"
        return 1
    fi
    
    if ! command -v jq &> /dev/null; then
        print_status $RED "Error: jq is required for session management"
        print_status $YELLOW "Install with: brew install jq"
        return 1
    fi
    
    # Extract session data
    TASK_DESCRIPTION=$(jq -r '.task_description' "$session_file")
    SESSION_STATUS=$(jq -r '.status' "$session_file")
    AI_ASSISTANT_FROM_SESSION=$(jq -r '.ai_assistant' "$session_file")
    CREATED_AT=$(jq -r '.created_at' "$session_file")
    
    # Use AI from session if not specified
    if [ -z "$AI_ASSISTANT" ]; then
        AI_ASSISTANT="$AI_ASSISTANT_FROM_SESSION"
    fi
    
    return 0
}

# Display plan summary
display_plan_summary() {
    print_status $GREEN "Current Plan Summary:"
    print_status $GREEN "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo
    print_status $BLUE "## Task: $TASK_DESCRIPTION"
    echo
    print_status $CYAN "### Session Details"
    print_status $YELLOW "  Session ID: $SESSION_ID"
    print_status $YELLOW "  AI Assistant: $AI_ASSISTANT"
    print_status $YELLOW "  Created: $CREATED_AT"
    print_status $YELLOW "  Status: $SESSION_STATUS"
    echo
    
    if [ -n "$MODIFICATIONS" ]; then
        print_status $MAGENTA "### Requested Modifications"
        print_status $YELLOW "  $MODIFICATIONS"
        echo
    fi
    
    print_status $CYAN "### Q-LEVER Framework Compliance"
    print_status $GREEN "  ✓ Q: Questions asked and assumptions validated"
    print_status $GREEN "  ✓ L: Existing patterns identified for leverage"
    print_status $GREEN "  ✓ E: Extension approach defined over new creation"
    print_status $GREEN "  ✓ V: Verification and testing strategy planned"
    print_status $GREEN "  ✓ E: Duplication elimination strategy included"
    print_status $GREEN "  ✓ R: Reduced complexity approach documented"
    echo
    
    print_status $BLUE "### Ready for Implementation"
    echo "  • Architecture decisions documented"
    echo "  • Implementation steps defined"
    echo "  • Testing strategy specified"
    echo "  • Risk assessment completed"
    echo "  • Integration points identified"
    echo
    print_status $GREEN "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
}

# Generate approval prompt if modifications requested
generate_modification_prompt() {
    if [ -n "$MODIFICATIONS" ]; then
        cat << EOF
# Plan Modification Request

## Current Plan Context
- **Session ID:** $SESSION_ID
- **Task:** $TASK_DESCRIPTION
- **AI Assistant:** $AI_ASSISTANT

## Requested Modifications
$MODIFICATIONS

## Instructions for AI Assistant
Please analyze the requested modifications and provide:

1. **Impact Analysis**: How do these changes affect the current plan?
2. **Q-LEVER Compliance**: Do modifications maintain framework compliance?
3. **Updated Approach**: Revised plan incorporating the changes
4. **Risk Assessment**: Any new risks or considerations
5. **Implementation Changes**: How do implementation steps change?

## Q-LEVER Re-validation Required
- **Q**: Do modifications introduce new assumptions to validate?
- **L**: Can we still leverage the same existing patterns?
- **E**: Does the extension approach need to change?
- **V**: How does verification/testing strategy change?
- **E**: Are there new duplication concerns?
- **R**: Does this maintain or increase complexity?

Please provide the updated plan with modifications incorporated:
EOF
    fi
}

# Approve session
approve_session() {
    local project_root
    project_root=$(find_project_root)
    local session_file="$project_root/.claude/state/planning/$SESSION_ID.json"
    local timestamp=$(date -u +%Y-%m-%dT%H:%M:%SZ)
    local temp_file=$(mktemp)
    
    # Update session with approval
    jq --arg timestamp "$timestamp" --arg modifications "$MODIFICATIONS" '
        .status = "approved" |
        .approved = true |
        .approved_at = $timestamp |
        .approved_by = "user" |
        .modifications = $modifications
    ' "$session_file" > "$temp_file" && mv "$temp_file" "$session_file"
    
    print_status $GREEN "✅ Plan approved and saved!"
    print_status $BLUE "Session updated: $session_file"
}

# Create feature branch if requested
create_feature_branch() {
    if [ "$CREATE_BRANCH" = true ]; then
        print_status $BLUE "Creating feature branch..."
        
        # Generate branch name from task
        local branch_name
        branch_name=$(echo "$TASK_DESCRIPTION" | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9]/-/g' | sed 's/--*/-/g' | sed 's/^-\|-$//g')
        local date_suffix=$(date +%Y%m%d)
        local full_branch_name="feature/${branch_name}-${date_suffix}"
        
        # Create and switch to branch
        if git checkout -b "$full_branch_name" 2>/dev/null; then
            print_status $GREEN "✓ Created feature branch: $full_branch_name"
            
            # Add plan reference to branch description (if supported)
            git config branch."$full_branch_name".description "Implementation of: $TASK_DESCRIPTION (Plan: $SESSION_ID)"
            
            # Update session with branch info
            local project_root
            project_root=$(find_project_root)
            local session_file="$project_root/.claude/state/planning/$SESSION_ID.json"
            local temp_file=$(mktemp)
            
            jq --arg branch "$full_branch_name" '
                .implementation_branch = $branch
            ' "$session_file" > "$temp_file" && mv "$temp_file" "$session_file"
            
            return 0
        else
            print_status $YELLOW "⚠ Could not create branch (may already exist or git issue)"
            return 1
        fi
    fi
}

# Show next steps
show_next_steps() {
    print_status $CYAN "🚀 Next Steps:"
    echo
    
    if [ "$CREATE_BRANCH" = true ]; then
        echo "  1. You're now on the feature branch ready for development"
    else
        echo "  1. Create a feature branch: /feature-start <name> --plan $SESSION_ID"
    fi
    
    echo "  2. Start implementation: /dev --use-plan $SESSION_ID"
    echo "  3. Create GitHub issue: /ai-issue $AI_ASSISTANT \"$TASK_DESCRIPTION\" --plan $SESSION_ID"
    echo "  4. Monitor progress: /ai-status $AI_ASSISTANT"
    echo
    
    print_status $YELLOW "💡 Plan-Guided Development:"
    echo "  • Follow the approved architecture decisions"
    echo "  • Implement in the defined step order"
    echo "  • Maintain Q-LEVER compliance throughout"
    echo "  • Test according to the planned strategy"
    echo "  • Document deviations if they occur"
    echo
    
    print_status $BLUE "📋 Implementation Commands:"
    echo "  /dev \"implement step 1\" --use-plan $SESSION_ID"
    echo "  /ai-assign claude issue-123 --plan $SESSION_ID"
    echo "  /clarify-requirements \"clarification\" --session $SESSION_ID"
}

# Main execution
main() {
    parse_arguments "$@"
    
    print_status $GREEN "PIB-METHOD Plan Approval"
    print_status $GREEN "========================"
    echo
    
    # Find session if not specified
    if [ -z "$SESSION_ID" ]; then
        if find_recent_session; then
            print_status $BLUE "Using most recent planning session: $SESSION_ID"
        else
            print_status $RED "Error: No planning session found"
            print_status $YELLOW "Start a planning session with: /plan-session \"<task>\""
            exit 1
        fi
    fi
    
    # Load session data
    if ! load_session; then
        exit 1
    fi
    
    # Display plan summary
    display_plan_summary
    
    # Handle modifications if requested
    if [ -n "$MODIFICATIONS" ]; then
        print_status $MAGENTA "🔄 Modifications Requested"
        echo
        print_status $BLUE "Modification prompt for AI assistant:"
        echo
        print_status $CYAN "=== COPY FROM HERE ==="
        generate_modification_prompt
        print_status $CYAN "=== COPY TO HERE ==="
        echo
        print_status $YELLOW "Please get AI approval for modifications before confirming."
        read -p "Press Enter when AI has approved the modified plan..."
    fi
    
    # Confirm approval
    echo
    print_status $YELLOW "Do you approve this plan for implementation?"
    read -p "Type 'yes' to confirm approval: " -r
    echo
    
    if [[ $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
        approve_session
        create_feature_branch
        show_next_steps
        print_status $GREEN "🎉 Plan approved! Ready for implementation."
    else
        print_status $YELLOW "Plan not approved. Use '/clarify-requirements' for changes."
        exit 0
    fi
}

# Run main function with all arguments
main "$@"