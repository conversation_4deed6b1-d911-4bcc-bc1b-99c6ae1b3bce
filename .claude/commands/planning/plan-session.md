# Interactive Planning Session

Start an interactive planning session that follows the Q-LEVER framework, beginning with clarifying questions before any implementation.

## Usage
```bash
session -> session -> /workflows:planning-session:plan-session-session "<task_description>" [--ai copilot|claude] [--scope small|medium|large]
```

## Examples
```bash
# Start planning session for new feature
session -> session -> /workflows:planning-session:plan-session-session "implement user authentication system"

# Planning with specific AI assistant
session -> session -> /workflows:planning-session:plan-session-session "create dashboard for analytics" --ai claude

# Planning with scope hint
session -> session -> /workflows:planning-session:plan-session-session "refactor payment system" --scope large
```

## What This Command Does

### 1. Question Phase (Q-LEVER)
The AI assistant will ask clarifying questions such as:
- **Requirements**: What specific functionality is needed?
- **Scope**: What's included/excluded from this task?
- **Constraints**: Any technical limitations or preferences?
- **Integration**: How does this connect to existing systems?
- **Users**: Who will use this and how?
- **Success Criteria**: How do we know when it's complete?

### 2. Interactive Dialog
- AI asks questions one at a time
- User provides answers and additional context
- AI can ask follow-up questions for clarity
- Session continues until all assumptions are clear

### 3. Plan Generation
After questions are answered, AI creates:
- Detailed implementation plan
- Architecture decisions with rationale
- Testing strategy
- Risk assessment
- Timeline estimation

### 4. Plan Review
- User reviews the generated plan
- Can request modifications or clarifications
- Plan is refined based on feedback
- Final plan approval before implementation

## Planning Session Flow

### Phase 1: Question & Discovery
```
AI: I'll help you plan the user authentication system. Let me ask some clarifying questions:

1. What authentication methods do you want to support? 
   (e.g., email/password, OAuth, SSO, 2FA)

2. Do you have an existing user database/model?

3. What are your security requirements?
   (e.g., password complexity, session management)

4. Should this integrate with any existing systems?

5. What's your target timeline for this implementation?
```

### Phase 2: Plan Creation
```
Based on your answers, here's the implementation plan:

## Authentication System Implementation Plan

### Architecture Decision
- JWT-based authentication with refresh tokens
- Integration with existing User model
- OAuth support for Google/GitHub
- Redis for session management

### Implementation Steps
1. Database schema updates
2. Authentication middleware
3. API endpoints (login, register, refresh)
4. Frontend integration
5. Testing suite

### Q-LEVER Compliance
- **Q**: All assumptions documented above
- **L**: Leverage existing User model and Express middleware
- **E**: Extend current API structure
- **V**: Comprehensive unit and integration tests
- **E**: Eliminate duplicate auth logic
- **R**: Minimal, focused implementation
```

### Phase 3: Plan Approval
```
User reviews plan and can:
- Approve and proceed: approach -> session -> /workflows:planning-session:confirm-approach-approach
- Request changes: "Please modify X to use Y instead"
- Ask questions: "Why did you choose JWT over sessions?"
```

## AI Assistant Behavior

### GitHub Copilot Planning
- Asks practical, implementation-focused questions
- Provides quick, actionable plans
- Shows code examples in planning phase
- Focuses on existing patterns in your codebase

### Claude Code Planning
- Asks comprehensive architectural questions
- Provides detailed analysis and reasoning
- Creates thorough documentation
- Emphasizes Q-LEVER framework compliance

## Parameters
- `<task_description>`: Clear description of what you want to implement
- `--ai`: Choose specific AI assistant (copilot or claude)
- `--scope`: Hint about task complexity (small/medium/large)

## Session State Management
- Planning sessions are saved to `.claude/statesession -> session -> /workflows:planning-session:plan-sessionning/`
- Can resume interrupted sessions
- Plans are versioned and can be referenced later
- Integration with git worktree for feature planning

## Integration with Development Workflow

### After Planning
```bash
# Once plan is approved, start implementation
> /workflows:dev-command --use-plan session-12345

# Or create feature branch with plan
start -> /workflows:feature-start-start auth-system --plan session-12345
```

### Plan Templates
Common planning templates are provided for:
- New feature implementation
- System refactoring
- Bug fixes
- Performance optimization
- Security enhancements

## Best Practices

### For Users
1. **Be specific** in initial task description
2. **Answer questions thoroughly** - more context = better plans
3. **Ask for clarification** if AI assumptions seem wrong
4. **Review plans carefully** before approval
5. **Save approved plans** for future reference

### For AI Assistants
1. **Always start with questions** - never assume requirements
2. **Ask one clear question at a time** - avoid overwhelming users
3. **Explain reasoning** behind architectural decisions
4. **Consider existing codebase** patterns and constraints
5. **Provide realistic estimates** and identify risks

## Quality Gates
- [ ] All ambiguities resolved through questions
- [ ] Assumptions explicitly documented
- [ ] Technical approach validated
- [ ] Integration points identified
- [ ] Testing strategy defined
- [ ] Risk assessment completed
- [ ] Q-LEVER compliance verified

## Related Commands
- `requirements -> session -> /workflows:planning-session:clarify-requirements-requirements` - Ask additional clarifying questions
- `approach -> session -> /workflows:planning-session:confirm-approach-approach` - Approve the generated plan
- `> /workflows:dev-command --use-plan` - Implement using approved plan
- `start -> /workflows:feature-start-start --plan` - Create feature branch with plan