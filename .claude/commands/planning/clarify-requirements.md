# Clarify Requirements

Ask additional clarifying questions during planning or implementation to validate assumptions and gather missing context.

## Usage
```bash
requirements -> /planning:clarify-requirements-requirements "<context_or_question>" [--session session_id] [--ai copilot|claude]
```

## Examples
```bash
# Ask for clarification during active planning
requirements -> /planning:clarify-requirements-requirements "I need more details about the user authentication flow"

# Clarify specific technical requirements
requirements -> /planning:clarify-requirements-requirements "What database should we use for storing user sessions?"

# Add context to existing planning session
requirements -> /planning:clarify-requirements-requirements "The system also needs to support SSO" --session plan-20250125-143022
```

## What This Command Does

### 1. Assumption Validation
- **Identify gaps** in current understanding
- **Challenge assumptions** made during planning
- **Verify technical constraints** and preferences
- **Confirm scope boundaries** and requirements

### 2. Context Enhancement
- **Add missing information** to planning sessions
- **Clarify ambiguous requirements** 
- **Update technical specifications**
- **Refine implementation approach**

### 3. Interactive Dialog
- **Ask follow-up questions** based on new information
- **Validate understanding** of updated requirements
- **Adjust plans** based on clarifications
- **Document assumption changes**

## Question Categories

### Technical Clarifications
```bash
# Database and storage
requirements -> /planning:clarify-requirements-requirements "What's our database choice and why?"

# Architecture decisions  
requirements -> /planning:clarify-requirements-requirements "Should this be a microservice or part of the monolith?"

# Integration points
requirements -> /planning:clarify-requirements-requirements "How does this integrate with the existing auth system?"

# Performance requirements
requirements -> /planning:clarify-requirements-requirements "What are the expected load and performance requirements?"
```

### Business Requirements
```bash
# User experience
requirements -> /planning:clarify-requirements-requirements "What's the expected user flow for this feature?"

# Business logic
requirements -> /planning:clarify-requirements-requirements "What are the business rules for user permissions?"

# Compliance and security
requirements -> /planning:clarify-requirements-requirements "Are there specific security or compliance requirements?"

# Timeline and priorities
requirements -> /planning:clarify-requirements-requirements "What's the deadline and can we deliver in phases?"
```

### Implementation Details
```bash
# Testing strategy
requirements -> /planning:clarify-requirements-requirements "What level of test coverage is expected?"

# Error handling
requirements -> /planning:clarify-requirements-requirements "How should we handle edge cases and errors?"

# Monitoring and logging
requirements -> /planning:clarify-requirements-requirements "What monitoring and alerting do we need?"

# Documentation
requirements -> /planning:clarify-requirements-requirements "What documentation needs to be updated?"
```

## AI Assistant Responses

### GitHub Copilot Clarifications
- **Quick, practical questions** focused on implementation
- **Code examples** to illustrate different approaches
- **Existing pattern references** from your codebase
- **Immediate actionable suggestions**

Example Response:
```
Based on your clarification about SSO, I have a few follow-up questions:

1. Which SSO provider? (SAML, OAuth, OIDC)
2. Do you have existing SSO infrastructure?
3. Should we maintain backward compatibility with current auth?

I can see you're using Passport.js in auth/strategies/. Should we extend that or create a new approach?
```

### Claude Code Clarifications
- **Comprehensive analysis** of implications
- **Architecture considerations** and trade-offs
- **Q-LEVER framework alignment** validation
- **Detailed documentation** of decisions

Example Response:
```
Thank you for clarifying the SSO requirement. This significantly impacts our approach:

## Updated Requirements Analysis
- Original: Simple email/password authentication
- Updated: SSO integration + backward compatibility

## New Questions for Q-LEVER Compliance:
1. **Question**: Which SSO protocols are required? (SAML 2.0, OAuth 2.0, OpenID Connect)
2. **Leverage**: Can we extend your existing Passport.js infrastructure?
3. **Extend**: Should we maintain the current user model or extend it for SSO?
4. **Verify**: What testing is needed for SSO integration?
5. **Eliminate**: Can we consolidate auth flows to avoid duplication?
6. **Reduce**: What's the minimal SSO implementation that meets requirements?

Please clarify these points so I can update the implementation plan.
```

## Session Integration

### Active Planning Sessions
When used during a planning session:
- **Updates session state** with new requirements
- **Adjusts existing plans** based on clarifications
- **Maintains question/answer history**
- **Triggers plan regeneration** if needed

### Implementation Phase
When used during development:
- **Documents requirement changes** for future reference
- **Updates implementation approach** in real-time
- **Maintains traceability** between requirements and code
- **Triggers impact analysis** of changes

## Parameters
- `<context_or_question>`: The clarification needed or additional context
- `--session`: Specific planning session ID to update
- `--ai`: Choose AI assistant for clarification (copilot or claude)

## Integration with Planning Workflow

### During Planning
```bash
# Start planning session
session -> /planning:plan-session-session "implement user dashboard"

# AI asks questions, you answer
# Then you realize you need to clarify something
requirements -> /planning:clarify-requirements-requirements "The dashboard also needs real-time updates via WebSocket"

# AI adjusts questions and plan based on new requirement
```

### During Implementation
```bash
# Start implementation
> /workflows:dev-command "implement auth system" --use-plan plan-20250125-143022

# Discover new requirement during development
requirements -> /planning:clarify-requirements-requirements "Client wants to add 2FA support, how does this impact current implementation?"

# AI provides impact analysis and updated approach
```

## Best Practices

### When to Use
- **Requirements are ambiguous** or incomplete
- **New information** emerges during planning/implementation
- **Assumptions prove incorrect** during development
- **Scope changes** or additional features requested
- **Technical constraints** become apparent

### How to Ask Effective Questions
1. **Be specific** - Provide concrete examples
2. **Give context** - Explain why you need clarification
3. **Include constraints** - Mention limitations or preferences
4. **Show examples** - Reference existing code or systems
5. **Explain impact** - Describe how this affects current plans

### Question Templates
```bash
# Requirement clarification
"I need clarification on [specific requirement]. Currently I understand [current understanding], but [what's unclear]. Can you clarify [specific question]?"

# Technical decision
"For [specific component], should we use [option A] or [option B]? Consider [constraints/preferences]. What are the trade-offs?"

# Integration question  
"How should [new feature] integrate with [existing system]? Are there existing patterns in [relevant codebase section] we should follow?"

# Scope clarification
"The original requirement was [original]. Now we need [additional requirement]. How does this change [impact area]? Should we [suggested approach]?"
```

## Quality Gates
- [ ] Clarification request is specific and actionable
- [ ] Context and constraints are provided
- [ ] Impact on existing plans is considered
- [ ] AI response addresses the core question
- [ ] Updated understanding is documented
- [ ] Plan adjustments are made if needed

## Related Commands
- `session -> /planning:plan-session-session` - Start interactive planning with questions
- `approach -> /planning:confirm-approach-approach` - Approve updated plans after clarification
- `> /workflows:dev-command --use-plan` - Implement with clarified requirements
- `issue -> /github:ai-issue-status` - Monitor AI work on clarified requirements