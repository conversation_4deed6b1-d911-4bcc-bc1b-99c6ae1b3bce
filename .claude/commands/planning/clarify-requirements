#!/bin/bash

# Clarify Requirements
# Ask additional clarifying questions during planning or implementation to validate assumptions

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m' # No Color

# Print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Find project root
find_project_root() {
    local dir="$(pwd)"
    while [ "$dir" != "/" ]; do
        if [ -d "$dir/.claude" ]; then
            echo "$dir"
            return 0
        fi
        dir=$(dirname "$dir")
    done
    return 1
}

# Parse command line arguments
parse_arguments() {
    if [ $# -lt 1 ]; then
        print_status $RED "Error: Context or question is required"
        echo
        echo "Usage: /clarify-requirements \"<context_or_question>\" [--session session_id] [--ai copilot|claude]"
        echo
        echo "Examples:"
        echo "  /clarify-requirements \"I need more details about the user authentication flow\""
        echo "  /clarify-requirements \"What database should we use?\" --ai claude"
        exit 1
    fi
    
    CONTEXT_OR_QUESTION="$1"
    SESSION_ID=""
    AI_ASSISTANT="claude"  # Default to Claude for planning
    
    shift
    while [[ $# -gt 0 ]]; do
        case $1 in
            --session)
                SESSION_ID="$2"
                shift 2
                ;;
            --ai)
                AI_ASSISTANT="$2"
                shift 2
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_status $RED "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # Validate AI assistant
    if [ "$AI_ASSISTANT" != "copilot" ] && [ "$AI_ASSISTANT" != "claude" ]; then
        print_status $RED "Error: AI assistant must be 'copilot' or 'claude'"
        exit 1
    fi
}

show_help() {
    echo "Clarify Requirements - Q-LEVER Framework"
    echo
    echo "Usage: /clarify-requirements \"<context_or_question>\" [options]"
    echo
    echo "Options:"
    echo "  --session <session_id>       Link to specific planning session"
    echo "  --ai <copilot|claude>        Choose AI assistant (default: claude)"
    echo "  --help                       Show this help"
    echo
    echo "Examples:"
    echo "  /clarify-requirements \"Need more details about auth flow\""
    echo "  /clarify-requirements \"What database?\" --session plan-20250125-143022"
}

# Load session context if provided
load_session_context() {
    local project_root
    project_root=$(find_project_root)
    if [ -z "$project_root" ]; then
        return 1
    fi
    
    if [ -n "$SESSION_ID" ]; then
        local session_file="$project_root/.claude/state/planning/$SESSION_ID.json"
        if [ -f "$session_file" ]; then
            # Extract session context for display
            if command -v jq &> /dev/null; then
                TASK_DESCRIPTION=$(jq -r '.task_description' "$session_file" 2>/dev/null || echo "Unknown")
                SESSION_STATUS=$(jq -r '.status' "$session_file" 2>/dev/null || echo "unknown")
                EXISTING_QUESTIONS=$(jq -r '.questions_asked | length' "$session_file" 2>/dev/null || echo "0")
            else
                TASK_DESCRIPTION="Unknown (jq not available)"
                SESSION_STATUS="unknown"
                EXISTING_QUESTIONS="0"
            fi
            return 0
        else
            print_status $YELLOW "Warning: Session file not found: $session_file"
            return 1
        fi
    fi
    return 1
}

# Update session with clarification
update_session() {
    local project_root
    project_root=$(find_project_root)
    if [ -z "$project_root" ] || [ -z "$SESSION_ID" ]; then
        return 1
    fi
    
    local session_file="$project_root/.claude/state/planning/$SESSION_ID.json"
    if [ -f "$session_file" ] && command -v jq &> /dev/null; then
        # Add clarification to session
        local timestamp=$(date -u +%Y-%m-%dT%H:%M:%SZ)
        local temp_file=$(mktemp)
        
        jq --arg clarification "$CONTEXT_OR_QUESTION" --arg timestamp "$timestamp" '
            .clarifications += [{
                "question": $clarification,
                "timestamp": $timestamp,
                "status": "pending"
            }] |
            .updated_at = $timestamp
        ' "$session_file" > "$temp_file" && mv "$temp_file" "$session_file"
        
        return 0
    fi
    return 1
}

# Generate clarification categories
show_clarification_categories() {
    print_status $CYAN "Common clarification categories:"
    echo
    print_status $BLUE "🔧 Technical Clarifications:"
    echo "  • Database and storage choices"
    echo "  • Architecture decisions (microservice vs monolith)"
    echo "  • Integration points with existing systems"
    echo "  • Performance and scalability requirements"
    echo
    print_status $BLUE "📋 Business Requirements:"
    echo "  • User experience and workflow"
    echo "  • Business logic and rules"
    echo "  • Security and compliance needs"
    echo "  • Timeline and delivery phases"
    echo
    print_status $BLUE "⚙️ Implementation Details:"
    echo "  • Testing strategy and coverage"
    echo "  • Error handling approaches"
    echo "  • Monitoring and logging"
    echo "  • Documentation requirements"
}

# Generate clarification prompt
generate_clarification_prompt() {
    cat << EOF
# Q-LEVER Requirement Clarification

## Context
I need clarification on the following:

**Question/Context:** $CONTEXT_OR_QUESTION

EOF
    
    if [ -n "$SESSION_ID" ] && load_session_context; then
        cat << EOF
## Session Context
- **Session ID:** $SESSION_ID
- **Original Task:** $TASK_DESCRIPTION
- **Session Status:** $SESSION_STATUS
- **Questions Asked:** $EXISTING_QUESTIONS

EOF
    fi
    
    cat << EOF
## Q-LEVER Framework Context
Please provide clarification considering these framework principles:

- **Q**: What assumptions need validation based on this new information?
- **L**: How does this affect leveraging existing patterns?
- **E**: Does this change our extension vs creation approach?
- **V**: What verification/testing implications does this have?
- **E**: Are there duplication concerns with this clarification?
- **R**: How can we maintain simplicity with this new requirement?

## Your Response Should Include:
1. **Direct answer** to the clarification request
2. **Follow-up questions** if more details are needed
3. **Impact analysis** on existing plans (if applicable)
4. **Updated approach** or recommendations
5. **Risk assessment** of any changes

## AI Assistant Instructions:
- Ask specific, focused follow-up questions
- Explain implications of different choices
- Reference existing codebase patterns when relevant
- Provide concrete examples or alternatives
- Consider Q-LEVER compliance in your response

Please provide your clarification response:
EOF
}

# Main execution
main() {
    parse_arguments "$@"
    
    print_status $GREEN "PIB-METHOD Requirement Clarification"
    print_status $GREEN "==================================="
    echo
    
    # Load session context if provided
    if load_session_context; then
        print_status $BLUE "📋 Session Context Found:"
        print_status $YELLOW "  Session: $SESSION_ID"
        print_status $YELLOW "  Task: $TASK_DESCRIPTION"
        print_status $YELLOW "  Status: $SESSION_STATUS"
        print_status $YELLOW "  Previous Questions: $EXISTING_QUESTIONS"
        echo
        
        # Update session with clarification
        if update_session; then
            print_status $GREEN "✓ Clarification added to planning session"
        fi
    fi
    
    print_status $BLUE "🤔 Clarification Request:"
    print_status $CYAN "\"$CONTEXT_OR_QUESTION\""
    echo
    
    print_status $BLUE "AI Assistant: $AI_ASSISTANT"
    echo
    
    # Show clarification categories for reference
    show_clarification_categories
    echo
    
    # Generate the clarification prompt
    local prompt
    prompt=$(generate_clarification_prompt)
    
    print_status $BLUE "Clarification prompt generated. Copy and paste into your AI assistant:"
    echo
    print_status $CYAN "=== COPY FROM HERE ==="
    echo "$prompt"
    print_status $CYAN "=== COPY TO HERE ==="
    echo
    
    print_status $YELLOW "💡 Tips for better clarifications:"
    echo "  • Be specific about what you need to know"
    echo "  • Explain why the clarification is important"
    echo "  • Mention any constraints or preferences"
    echo "  • Reference existing code or systems if relevant"
    echo
    
    print_status $GREEN "Next steps after AI responds:"
    echo "  • Continue the clarification dialog as needed"
    echo "  • Use '/clarify-requirements' again for follow-up questions"
    echo "  • Use '/confirm-approach' when ready to proceed with updated plan"
}

# Run main function with all arguments
main "$@"