# Git Feature Workflow

Complete git workflow management integrated with PIB-METHOD worktree system.

## Core Commands

### Feature Development
```bash
/git:feature-start <name>     # Start new feature branch + worktree
/git:feature-list            # List all active features
/git:feature-switch <name>    # Switch between feature branches
/git:feature-status <name>    # Show feature status and progress
/git:feature-merge <name>     # Merge feature with quality gates
/git:feature-cleanup         # Clean up completed features
```

### Quality Integration
```bash
/git:pre-commit-check        # Run LEVER compliance check
/git:branch-review <name>     # Review branch changes
/git:merge-requirements      # Check if ready for merge
```

## Integrated Workflow

### 1. Start New Feature
```bash
/git:feature-start user-authentication
```

**What happens:**
- Creates `feature/user-authentication` branch from main
- Creates worktree in `../pib-user-authentication/`
- Sets up isolated agent state
- Initializes quality gate tracking
- Opens new Claude Code instance in worktree

### 2. Develop with Quality Gates
```bash
# Work normally in the feature worktree
/workflows:dev-command "implement login system"

# Automatic quality checks on commits
git commit -m "Add login validation"
# → Triggers LEVER compliance check
# → Runs pattern validation
# → Updates documentation if needed
```

### 3. Continuous Integration
```bash
# Check feature progress
/git:feature-status user-authentication

# Review changes before merge
/git:branch-review user-authentication
```

### 4. Merge with Quality Assurance
```bash
/git:feature-merge user-authentication
```

**Quality gate requirements:**
- LEVER compliance score 4/5+
- All tests passing
- Documentation updated
- Code review completed
- No merge conflicts

## Branch Strategy

### Branch Naming
```bash
feature/feature-name      # New features
bugfix/issue-description  # Bug fixes
hotfix/critical-fix      # Emergency fixes
research/spike-name      # Research spikes
```

### Protection Rules
- **Main branch**: Protected, requires review
- **Feature branches**: Quality gates enforced
- **Release branches**: Automated testing required

## Worktree Integration

### Automatic Worktree Management
```mermaid
graph TD
    A[/git:feature-start] --> B[Create Git Branch]
    B --> C[Create Worktree Directory]
    C --> D[Initialize Agent State]
    D --> E[Sync PIB-METHOD]
    E --> F[Open Claude Code]
    F --> G[Development Ready]
```

### State Isolation
Each feature worktree has:
- Independent agent conversations
- Isolated analytics and suggestions
- Separate quality tracking
- Individual documentation state

### Workspace Organization
```
Projects/
├── my-project/                 # Main development
├── my-project-auth/           # Feature: authentication
├── my-project-dashboard/      # Feature: user dashboard
└── my-project-api/           # Feature: API refactor
```

## Quality Integration

### Pre-commit Hooks
```bash
# Automatic checks on every commit
- LEVER framework validation (4/5 minimum)
- Pattern compliance check
- Documentation updates
- Security scan for secrets
- Test requirement validation
```

### Merge Requirements
```bash
# All must pass before merge allowed
✓ Quality gates (LEVER 4/5+)
✓ Code review approved
✓ Tests passing
✓ Documentation current
✓ No conflicts with main
```

### Automated Reviews
```bash
# After development complete
/git:branch-review user-authentication
# → Runs code-reviewer agent
# → Generates improvement suggestions
# → Creates merge checklist
```

## PIB-METHOD Integration

### Smart Agent Routing
```bash
# Feature-specific agent context
/workflows:dev-command "add OAuth integration"
# → Loads feature context
# → Uses appropriate specialist
# → Maintains feature isolation
```

### Pattern Compliance
```bash
# Automatic pattern validation
git commit -m "Add auth middleware"
# → Checks against security patterns
# → Validates error handling
# → Ensures LEVER compliance
```

## Advanced Features

### Multi-Feature Development
```bash
# Work on multiple features simultaneously
/git:feature-start user-auth
/git:feature-start admin-panel
/git:feature-start api-v2

# Switch between contexts
/git:feature-switch user-auth
# → Changes worktree
# → Loads feature context
# → Restores agent state
```

### Feature Dependencies
```bash
# Create dependent feature
/git:feature-start payment-flow --depends-on user-auth

# Automatic base branch management
# Merge order enforcement
```

### Release Integration
```bash
# Prepare release branch
/git:release-start v2.1.0

# Merge multiple features
/git:feature-merge user-auth --to-release
/git:feature-merge admin-panel --to-release

# Finalize release
/git:release-finish v2.1.0
```

## Safety and Recovery

### Automatic Backups
```bash
# Before major operations
/git:feature-merge user-auth
# → Creates backup
# → Validates merge safety
# → Proceeds with merge
```

### Rollback Capabilities
```bash
# Undo problematic merge
/git:rollback-merge user-auth

# Restore feature state
/git:restore-feature user-auth --from-backup
```

### Conflict Resolution
```bash
# Interactive conflict resolution
/git:resolve-conflicts --interactive

# PIB-METHOD aware merging
/git:merge-with-pib-awareness
```

## Usage Examples

### Daily Development Flow
```bash
# Start day - sync with latest
/git:sync-upstream

# Work on feature
/git:feature-switch user-dashboard
/workflows:dev-command "implement user profile"

# Check progress
/git:feature-status user-dashboard

# End day - ensure quality
/git:pre-commit-check
git commit -m "Complete user profile implementation"
```

### Weekly Feature Review
```bash
# Review all active features
/git:feature-list --with-status

# Merge completed features
/git:feature-merge user-auth
/git:feature-merge admin-panel

# Clean up completed work
/git:feature-cleanup --older-than 7d
```

### Release Preparation
```bash
# Create release candidate
/git:release-start v2.0.0

# Merge approved features
/git:feature-merge --all-approved --to-release

# Run comprehensive testing
/git:pre-release-validation

# Finalize release
/git:release-finish v2.0.0 --create-tag
```

This workflow provides enterprise-grade git management while leveraging all PIB-METHOD capabilities!