# Sync with PIB-METHOD Upstream

Safely sync your project with the latest PIB-METHOD updates while preserving project-specific customizations.

## Usage

Basic sync:
```bash
/git:sync-upstream
```

With backup (recommended):
```bash
/git:sync-upstream --backup
```

Force sync (skip confirmation):
```bash
/git:sync-upstream --force
```

Dry run (show what would be synced):
```bash
/git:sync-upstream --dry-run
```

## What Gets Updated

### Always Updated (System Files)
- `pib-core/agents/` (core agents - NOT tech-specialists)
- `.claude/commands/` (slash commands)
- `.claude/hooks/` (automation hooks)
- `.claude/scripts/` (utility scripts)
- `.claude/patterns/` (pattern library)
- `.github/` (GitHub Copilot integration)
- `pib-agent/` (core PIB components)

### Intelligently Preserved (Project Files)
- `pib-core/agents/tech-specialists/` (your custom specialists)
- `.claude/summaries/` (auto-generated project docs)
- `.claude/usage-analytics.json` (your project analytics)
- `.claude/smart-suggestions.json` (project suggestions)
- `.claude/state/planning/` (planning sessions)
- Customized agents (marked with PROJECT-SPECIFIC)

### Created If Missing
- `CLAUDE.md` (base template only if not exists)
- `.claude/` directory structure
- VS Code settings for Copilot integration

## Safety Features

### Automatic Backup
```bash
# Creates backup before sync
.claude/backups/pre-sync-2025-01-27-143022/
├── agents/
├── summaries/
├── usage-analytics.json
└── smart-suggestions.json
```

### Conflict Detection
- Detects customized agents vs standard agents
- Preserves project-specific configurations
- Warns about potential conflicts

### Rollback Capability
```bash
/git:restore-backup 2025-01-27-143022
```

## Integration with Git Workflow

### Before Major Features
```bash
# Sync before starting new work
/git:sync-upstream --backup
/git:feature-start user-dashboard
```

### After PIB Updates
```bash
# Stay current with PIB-METHOD
/git:sync-upstream
/git:feature-merge current-work
```

## Smart Conflict Resolution

### Agent Conflicts
```markdown
## Automatic Resolution

- Standard agents (dev-agent, architect, etc.) → Always updated
- Tech specialists → Always preserved
- Customized agents → Detected and preserved

## Manual Resolution Required

- Modified standard agents with PROJECT-SPECIFIC marker
- Configuration conflicts in settings.json
- CLAUDE.md project sections vs PIB sections
```

### Configuration Merging
```bash
# Interactive merge for complex configs
/git:conflict-resolve --interactive
```

## Validation After Sync

### System Health Check
- All agents load correctly
- Hooks are executable
- Pattern library accessible
- GitHub integration working

### Quality Gates
- LEVER framework compliance maintained
- No broken command references
- Agent routing still functional

## Examples

### Regular Update Workflow
```bash
# Weekly PIB update
/git:sync-upstream --backup

# Verify system health
/core:agents
/git:pre-commit-check

# Continue development
/workflows:dev-command "continue feature work"
```

### Emergency Rollback
```bash
# If sync causes issues
/git:restore-backup $(ls -1 .claude/backups | tail -1)

# Or specific backup
/git:restore-backup 2025-01-27-143022
```

## Advanced Usage

### Custom Sync Rules
Create `.claude/sync-rules.json`:
```json
{
  "preserve_patterns": [
    "agents/custom-*",
    "state/*/project-*",
    "config/*-local.*"
  ],
  "force_update": [
    "hooks/security-*",
    "patterns/*"
  ]
}
```

### Selective Sync
```bash
# Only update specific components
/git:sync-upstream --components agents,hooks
/git:sync-upstream --exclude patterns
```

## Troubleshooting

### Common Issues
1. **Permission errors**: Check file permissions
2. **Merge conflicts**: Use conflict resolution
3. **Missing dependencies**: Run system validation
4. **Broken agents**: Restore from backup

### Recovery Commands
```bash
# Full system reset (use carefully)
/git:restore-backup --full

# Repair broken hooks
/git:sync-upstream --components hooks

# Rebuild agent system
/git:sync-upstream --components agents --force
```

This intelligent sync system ensures you stay current with PIB-METHOD while preserving your project-specific customizations!