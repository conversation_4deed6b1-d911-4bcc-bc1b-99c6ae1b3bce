# Git Management System

Comprehensive git workflow management for PIB-METHOD projects.

## Core Commands

### Feature Development
```bash
/git:feature-start <name>     # Start new feature branch + worktree
/git:feature-list            # List all active features
/git:feature-switch <name>    # Switch between feature branches
/git:feature-merge <name>     # Merge feature with automated review
/git:feature-cleanup         # Clean up completed features
```

### PIB Updates
```bash
/git:sync-upstream           # Sync with latest PIB-METHOD
/git:conflict-resolve        # Interactive conflict resolution
/git:backup-project          # Create backup before major updates
/git:restore-backup <id>     # Restore from backup if needed
```

### Quality Gates
```bash
/git:pre-commit-check        # Run quality checks before commit
/git:branch-review          # Review branch changes
/git:merge-requirements     # Check merge requirements
```

## Workflow Integration

### 1. Feature Development Flow
```mermaid
graph TD
    A[/git:feature-start] --> B[Create Branch]
    B --> C[Create Worktree]
    C --> D[Initialize State]
    D --> E[Development Work]
    E --> F[Quality Checks]
    F --> G[/git:feature-merge]
    G --> H[Code Review]
    H --> I[Merge to Main]
    I --> J[/git:feature-cleanup]
```

### 2. PIB Update Flow
```mermaid
graph TD
    A[/git:sync-upstream] --> B[Backup Current State]
    B --> C[Fetch PIB Updates]
    C --> D[Detect Conflicts]
    D --> E{Conflicts?}
    E -->|Yes| F[/git:conflict-resolve]
    E -->|No| G[Apply Updates]
    F --> H[Manual Resolution]
    H --> G
    G --> I[Validate System]
    I --> J[Complete Update]
```

## Smart Sync Strategy

### Files to Always Update (Overwrite)
- `pib-core/agents/` (core agents - not tech-specialists)
- `.claude/hooks/` (system hooks)
- `.claude/scripts/` (utility scripts)
- `.claude/patterns/` (pattern library)
- `.github/` (GitHub integration)
- `sync-pib-complete.sh` (sync script itself)

### Files to Preserve (Never Overwrite)
- `pib-core/agents/tech-specialists/` (project-specific)
- `.claude/summaries/` (auto-generated project docs)
- `.claude/usage-analytics.json` (project analytics)
- `.claude/smart-suggestions.json` (project suggestions)
- `docs/` (project documentation)
- Project-specific configurations

### Files to Merge Intelligently
- `CLAUDE.md` (merge project sections, update PIB sections)
- `.claude/settings.json` (merge configurations)
- Package files (add new dependencies, preserve existing)

## Conflict Resolution

### Automatic Resolution
```bash
# System files - always take upstream
pib-core/agents/*.md (except tech-specialists)
.claude/hooks/*.sh
.claude/patterns/*.md

# Project files - always keep local
docs/*.md
pib-core/agents/tech-specialists/**
.claude/summaries/**
```

### Interactive Resolution
```bash
# Configuration files - manual merge
CLAUDE.md
.claude/settings.json
package.json
```

### Safety Features
- Pre-update backup creation
- Rollback capability  
- Change summary reports
- Validation after updates

## Branch Protection

### Main Branch Rules
- Require quality gate passing (LEVER 4/5+)
- Require code review for merges
- Auto-run tests before merge
- Block direct pushes to main

### Feature Branch Rules
- Auto-create from main
- Include PIB-METHOD integration tests
- Quality checks on each commit
- State isolation per branch

## Integration with Existing System

### Worktree Enhancement
```bash
# Enhanced feature creation
/workflows:feature-start <name> --git
# Creates both worktree AND git branch
# Isolated state + git history
```

### Agent Coordination
```bash
# Git-aware agents
/workflows:dev-command "implement auth" --branch auth-feature
# Creates feature branch if needed
# Commits work to appropriate branch
```

## Quality Integration

### Pre-commit Hooks
- LEVER framework validation
- Pattern compliance check
- Documentation updates
- Test requirements

### Merge Requirements
- All quality gates passed
- Documentation updated
- Tests passing
- Code review approved

## Usage Examples

### Start New Feature
```bash
/git:feature-start user-authentication
# Creates:
# - Branch: feature/user-authentication  
# - Worktree: ../pib-user-authentication/
# - Isolated agent state
# - Quality gate setup
```

### Sync PIB Updates
```bash
/git:backup-project
/git:sync-upstream
# If conflicts:
/git:conflict-resolve --interactive
# Review changes and complete
```

### Merge Feature
```bash
/git:feature-merge user-authentication
# Runs:
# - Quality checks
# - Automated code review
# - Merge conflict resolution
# - Documentation updates
```

## Safety and Recovery

### Backup System
- Automatic backups before PIB updates
- Manual backup on demand
- 30-day retention policy
- Fast restore capability

### Validation System
- Post-update system validation
- Agent functionality testing
- Configuration verification
- Rollback if issues detected

This git management system provides enterprise-grade workflow management while preserving the flexibility and power of PIB-METHOD!