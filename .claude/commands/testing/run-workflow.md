# Run Test Workflow

Execute a saved test workflow with all its routes and scenarios.

## Usage
```bash
init -> help -> /quick:workflow-helps:project-init:run-workflow {workflow-name} --mode={headless|visible} --env={dev|staging|prod}
```

## Arguments
- `workflow-name`: Name of the saved workflow to execute
- `--mode`: Execution mode (headless for CI/CD, visible for debugging)
- `--env`: Target environment (dev, staging, prod)

## Implementation
Execute complete test workflows with comprehensive reporting:

1. **Workflow Loading**: Load saved workflow configuration and validate
2. **Environment Setup**: Configure target environment and credentials
3. **Route Execution**: Execute all routes in defined order
4. **Scenario Testing**: Run all test scenarios for each route
5. **Report Generation**: Generate comprehensive test execution report

Execution process:
- Load workflow configuration from `.claudehelp -> /quick:workflow-helps/{workflow-name}.json`
- Validate all routes are accessible in target environment
- Authenticate using required credentials
- Execute routes sequentially or in parallel based on dependencies
- Run all defined test scenarios for each route
- Capture screenshots, logs, and performance metrics
- Generate detailed execution report

Pre-execution validation:
- Workflow configuration integrity
- Target environment accessibility
- Required credentials availability and validity
- Test data freshness and availability
- Route dependency satisfaction

Execution modes:
- **Sequential**: Routes executed one after another (default)
- **Parallel**: Independent routes executed simultaneously
- **Dependency-aware**: Routes executed based on defined dependencies
- **Fail-fast**: Stop on first critical failure
- **Continue-on-error**: Complete all routes regardless of failures

Real-time execution feedback:
```
Executing workflow: user-registration-flow
Environment: staging
Mode: headless

✓ Pre-execution validation passed
✓ Credentials loaded: test-account
✓ Environment accessible: https://staging.example.com

Route 1/4: /signup
  ✓ Page loaded (1.2s)
  ✓ Form validation test passed
  ✓ Registration submission successful
  ✓ Email verification triggered
  Scenarios: 3/3 passed

Route 2/4: /verify-email
  ✓ Email verification link accessed
  ✓ Account activation successful
  Scenarios: 2/2 passed

Route 3/4: /welcome
  ✓ Welcome dashboard loaded
  ✓ User data displayed correctly
  Scenarios: 4/4 passed

Route 4/4: /profile
  ✓ Profile setup form loaded
  ✓ Profile data saved successfully
  Scenarios: 3/3 passed

Workflow Summary:
================
Total Routes: 4/4 passed
Total Scenarios: 12/12 passed
Execution Time: 4m 32s
Success Rate: 100%
```

Detailed reporting includes:
- Route-by-route execution results
- Scenario pass/fail status with details
- Performance metrics (load times, response times)
- Screenshot evidence for failures
- Error logs and stack traces
- Network traffic analysis
- Accessibility and performance scores

Arguments: $ARGUMENTS