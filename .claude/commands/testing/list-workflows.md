# List Test Workflows

Display all saved test workflows with their routes and scenarios.

## Usage
```bash
init -> /workflows:project-init:list-workflows
```

## Implementation
Display a comprehensive list of all saved test workflows:

1. **Workflow Enumeration**: List all saved workflow configurations
2. **Route Summary**: Show key routes and test coverage for each workflow
3. **Status Validation**: Check if workflows are still valid and executable
4. **Usage Statistics**: Display last executed dates and success rates
5. **Health Check**: Verify route accessibility and credential validity

Information displayed for each workflow:
- Workflow name and description
- Number of routes and test scenarios
- Last executed date and success rate
- Required credentials and authentication
- Execution time estimates
- Current status (valid, needs update, broken routes)

Output format:
```
Saved Test Workflows:
====================

1. user-registration-flow
   Description: Complete user registration and onboarding
   Routes: 4 routes (/signup, /verify-email, /welcome, /profile)
   Scenarios: 12 test scenarios
   Credentials: test-account
   Last Run: 2024-01-20 (100% success)
   Est. Time: 5-8 minutes
   Status: ✓ Valid

2. e-commerce-checkout
   Description: Full shopping cart to payment completion
   Routes: 7 routes (/products, /cart, mode -> /quick:check-modeout, /payment, etc.)
   Scenarios: 18 test scenarios
   Credentials: shop-customer, payment-test
   Last Run: 2024-01-18 (95% success - 1 timeout)
   Est. Time: 12-15 minutes
   Status: ⚠ Route timeout detected

3. admin-panel-management
   Description: Administrative functions and user management
   Routes: 15 routes (admin dashboard, user CRUD, settings)
   Scenarios: 25 test scenarios
   Credentials: admin-account
   Last Run: Never
   Est. Time: 20-25 minutes
   Status: ⚠ Credentials need validation

4. api-integration-test
   Description: REST API endpoints and data validation
   Routes: 8 API endpoints
   Scenarios: 32 test scenarios (CRUD operations)
   Credentials: api-key-dev
   Last Run: 2024-01-19 (88% success - auth issues)
   Est. Time: 8-10 minutes
   Status: ⚠ Authentication needs update
```

Workflow statistics:
- Total workflows: 4
- Total routes covered: 34
- Total test scenarios: 87
- Average success rate: 95.75%
- Workflows needing attention: 3

Health check results:
- Route accessibility validation
- Credential expiration alerts
- Test data freshness status
- Environment compatibility check

The command helps manage and maintain test workflow libraries for efficient testing.

Arguments: $ARGUMENTS