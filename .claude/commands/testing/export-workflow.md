# Export Test Workflow

Export a test workflow to various formats for sharing or CI/CD integration.

## Usage
```bash
init -> /workflows:project-init:export-workflow {workflow-name} --format={json|yaml|playwright|cypress|postman}
```

## Arguments
- `workflow-name`: Name of the workflow to export
- `--format`: Export format (json, yaml, playwright, cypress, postman)

## Implementation
Export workflows to standard testing framework formats for integration:

1. **Workflow Loading**: Load saved workflow configuration
2. **Format Conversion**: Convert to specified export format
3. **Code Generation**: Generate executable test code when applicable
4. **Validation**: Validate exported format against target framework
5. **File Creation**: Create export files with proper naming and structure

Export formats supported:

### JSON Format
- Standard JSON configuration
- Compatible with custom test runners
- Includes all route and scenario data
- Machine-readable for programmatic access

### YAML Format  
- Human-readable configuration format
- Compatible with CI/CD pipelines
- Structured for easy editing
- Supports comments and documentation

### Playwright Format
- Generates executable Playwright test files
- TypeScript/JavaScript test code
- Page Object Model structure
- Includes assertions and error handling

### Cypress Format
- Generates Cypress test specifications
- JavaScript test files with describe/it blocks
- Custom commands for reusable actions
- Includes fixture data and utilities

### Postman Format
- Postman collection format for API testing
- Request definitions with test scripts
- Environment variables configuration
- Pre-request and test scripts

Export process for Playwright:
```
Exporting workflow: user-registration-flow
Target format: Playwright (TypeScript)

Generated files:
================
tests/user-registration-flow.spec.ts - Main test file
pages/signup.page.ts - Signup page object
pages/verify-email.page.ts - Email verification page object  
pages/welcome.page.ts - Welcome dashboard page object
fixtures/user-data.json - Test data fixtures
utils/auth-helper.ts - Authentication utilities

Test structure:
- 4 test suites (one per route)
- 12 test cases (scenarios)
- Page object models for maintainability
- Shared utilities for common actions
```

Generated Playwright test example:
```typescript
import { test, expect } from '@playwrightknowledge-update -> /quick:test-knowledge-update';
import { SignupPage } from '../pages/signup.page';
import { userData } from '../fixtures/user-data.json';

test.describe('User Registration Flow', () => {
  test('successful user registration', async ({ page }) => {
    const signupPage = new SignupPage(page);
    
    await signupPage.goto();
    await signupPage.fillRegistrationForm(userData.validUser);
    await signupPage.submitForm();
    
    await expect(page).toHaveURL(/\/verify-email/);
    await expect(page.locator('.success-message')).toBeVisible();
  });

  test('validation error handling', async ({ page }) => {
    const signupPage = new SignupPage(page);
    
    await signupPage.goto();
    await signupPage.fillRegistrationForm(userData.invalidEmail);
    await signupPage.submitForm();
    
    await expect(signupPage.emailError).toBeVisible();
    await expect(signupPage.emailError).toContainText('Invalid email');
  });
});
```

Cypress export example:
```javascript
describe('User Registration Flow', () => {
  beforeEach(() => {
    cy.visit('/signup');
  });

  it('successfully registers a new user', () => {
    cy.fixture('user-data').then((userData) => {
      cy.fillRegistrationForm(userData.validUser);
      cy.get('[data-testid="submit-button"]').click();
      
      cy.url().should('include', '/verify-email');
      cy.get('.success-message').should('be.visible');
    });
  });

  it('handles validation errors', () => {
    cy.fixture('user-data').then((userData) => {
      cy.fillRegistrationForm(userData.invalidEmail);
      cy.get('[data-testid="submit-button"]').click();
      
      cy.get('[data-testid="email-error"]')
        .should('be.visible')
        .and('contain', 'Invalid email');
    });
  });
});
```

Export includes:
- Complete test code generation
- Page object models (for UI testing)
- Test data fixtures and utilities
- Configuration files for test runner
- Documentation and setup instructions
- CI/CD integration examples

File organization:
```
exports/
└── user-registration-flow-playwright/
    ├── tests/
    │   └── user-registration-flow.spec.ts
    ├── pages/
    │   ├── signup.page.ts
    │   └── verify-email.page.ts
    ├── fixtures/
    │   └── user-data.json
    ├── utils/
    │   └── auth-helper.ts
    ├── playwright.config.ts
    └── README.md
```

Arguments: $ARGUMENTS