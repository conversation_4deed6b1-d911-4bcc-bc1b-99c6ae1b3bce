# Test Visual Regression

Comprehensive visual regression testing to detect UI changes.

## Usage
```bash
init -> /workflows:project-init:test-visual-regression {target}
```

## Arguments
- `target`: Application URL or service to test for visual changes

## Implementation
Execute visual regression testing using screenshot comparison:

1. **Baseline Capture**: Create or update baseline screenshots
2. **Current Capture**: Take screenshots of current application state
3. **Comparison Analysis**: Compare current vs baseline images
4. **Difference Detection**: Identify visual changes and inconsistencies
5. **Report Generation**: Generate detailed visual diff reports

Visual regression testing includes:
- Full page screenshots across multiple viewports
- Component-level screenshot capture
- Cross-browser visual consistency checks
- Mobile and desktop viewport testing
- Before/after comparison with highlighting
- Threshold-based change detection

Test coverage:
- Landing pages and marketing content
- User interface components and layouts
- Form elements and interactive components
- Navigation and menu structures
- Modal dialogs and overlays
- Responsive design breakpoints

The testing process:
1. Navigate to specified target URL
2. Wait for page load and dynamic content
3. Capture screenshots at defined viewports
4. Compare against stored baseline images
5. Generate visual diff reports with annotations
6. Flag significant visual changes for review

Results include:
- Visual diff images with highlighted changes
- Pixel difference calculations
- Change percentage reports
- Mobile/desktop comparison results

Arguments: $ARGUMENTS