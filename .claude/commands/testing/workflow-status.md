# Workflow Status

Display comprehensive status and health information for test workflows.

## Usage
```bash
init -> /workflows:project-init:workflow-status {workflow-name}
```

## Arguments
- `workflow-name`: Name of the workflow to analyze (optional - shows all if omitted)

## Implementation
Provide detailed status analysis and health monitoring for workflows:

1. **Health Assessment**: Analyze workflow health and route accessibility
2. **Performance Metrics**: Show execution times and success rates
3. **Dependency Analysis**: Check credential and environment dependencies
4. **Route Validation**: Verify all routes are accessible and functional
5. **Recommendations**: Suggest optimizations and maintenance actions

Status analysis includes:
- Route accessibility and response times
- Credential validity and expiration status
- Test data freshness and accuracy
- Scenario coverage and effectiveness
- Performance trends and bottlenecks
- Error patterns and failure modes

Detailed workflow status:
```
Workflow Status: user-registration-flow
======================================

Overall Health: ✓ Healthy (95% score)
Last Execution: 2024-01-20 14:30:00 (100% success)
Next Scheduled: 2024-01-21 02:00:00 (nightly regression)

Route Health Matrix:
===================
Route                Status    Response Time   Last Tested
/signup             ✓ Healthy      1.2s        2 hours ago
/verify-email       ✓ Healthy      0.8s        2 hours ago  
/welcome            ⚠ Slow         3.1s        2 hours ago
/profile            ✓ Healthy      1.5s        2 hours ago

Performance Metrics:
===================
Average Execution Time: 4m 32s (target: 5m)
Success Rate (30 days): 98.5% (196/200 runs)
Performance Trend: ↗ Improving (+15% faster vs last month)

Scenario Coverage:
=================
Total Scenarios: 12
Passing: 12 (100%)
Failing: 0
Flaky: 0
Coverage: 95% (code coverage when available)

Dependencies Status:
===================
✓ Credentials: test-account (valid, expires in 45 days)
✓ Environment: staging.example.com (accessible)
✓ Test Data: Fresh (updated 3 days ago)
⚠ Browser: Chrome 118 (119 available)

Recent Issues:
=============
- /welcome route occasionally slow (>3s) - investigate image loading
- No critical issues in last 30 days

Recommendations:
===============
1. Update Chrome browser to latest version (119)
2. Investigate /welcome route performance (image optimization)
3. Consider adding mobile viewport scenarios
4. Credentials expire in 45 days - schedule renewal
```

Multi-workflow overview:
```
All Workflows Status Summary:
============================

✓ user-registration-flow    Healthy (95%)    Last: 2h ago (100%)
⚠ e-commerce-checkout      Warning (78%)    Last: 1d ago (90%)
✗ admin-panel-management   Critical (45%)   Last: 7d ago (Failed)
✓ api-integration-test     Healthy (92%)    Last: 4h ago (95%)

System Overview:
===============
Total Workflows: 4
Healthy: 2 (50%)
Warning: 1 (25%) 
Critical: 1 (25%)
Total Routes: 34
Success Rate: 91.25%

Alerts:
======
🔴 admin-panel-management: Credentials expired
🟡 e-commerce-checkout: Payment API timeout issues
🟡 Browser updates available for 3 workflows
```

Health scoring factors:
- **Route Accessibility** (30%): All routes respond within acceptable time
- **Success Rate** (25%): Historical test execution success percentage
- **Performance** (20%): Execution time within acceptable limits
- **Dependencies** (15%): Valid credentials, accessible environments
- **Freshness** (10%): Recent execution and up-to-date test data

Status categories:
- **Healthy (80-100%)**: All systems operational, minor optimizations possible
- **Warning (60-79%)**: Some issues detected, attention recommended
- **Critical (<60%)**: Significant problems, immediate action required

Performance analysis:
- Execution time trends over 30 days
- Success rate patterns and seasonal variations  
- Route response time analysis and bottlenecks
- Resource utilization and optimization opportunities

Maintenance recommendations:
- Credential renewal scheduling
- Test data refresh requirements
- Browser and dependency updates
- Route optimization opportunities
- Scenario coverage improvements

Arguments: $ARGUMENTS