# Update Test Workflow

Modify an existing test workflow by adding/removing routes or updating scenarios.

## Usage
```bash
init -> /workflows:project-init:update-workflow {workflow-name} --add-routes={route1,route2} --remove-routes={route3} --update-scenarios
```

## Arguments
- `workflow-name`: Name of the workflow to update
- `--add-routes`: Comma-separated list of new routes to add
- `--remove-routes`: Comma-separated list of routes to remove
- `--update-scenarios`: Flag to update test scenarios for existing routes

## Implementation
Modify existing workflows to keep them current with application changes:

1. **Workflow Loading**: Load existing workflow configuration
2. **Route Management**: Add, remove, or modify routes as specified
3. **Scenario Updates**: Update test scenarios for changed routes
4. **Validation**: Validate updated workflow configuration
5. **Backup**: Create backup of previous version before updating

Update operations supported:
- **Add Routes**: Append new routes to existing workflow
- **Remove Routes**: Remove obsolete or deprecated routes
- **Modify Routes**: Update existing route configurations
- **Update Scenarios**: Refresh test scenarios for current application state
- **Update Credentials**: Change authentication requirements
- **Update Test Data**: Refresh test data patterns and validation rules

Interactive update process:
```
Updating workflow: user-registration-flow

Current routes:
1. /signup
2. /verify-email
3. /welcome
4. /profile

Operations:
✓ Adding route: /preferences (user preferences setup)
✓ Adding route: /tutorial (onboarding tutorial)
✓ Removing route: /profile (moved to separate workflow)

Updated route list:
1. /signup
2. /verify-email
3. /welcome
4. /preferences (NEW)
5. /tutorial (NEW)

Configure new routes:
Route: /preferences
- Test data needed: preference categories, settings
- Scenarios: default preferences, custom settings, validation
- Dependencies: requires /welcome completion

Route: /tutorial
- Test data needed: tutorial steps, progress tracking
- Scenarios: tutorial completion, skip tutorial, step navigation
- Dependencies: optional after /welcome
```

Scenario updates include:
- Adding new test scenarios for changed functionality
- Removing obsolete scenarios for deprecated features
- Updating assertions for modified UI/API responses
- Refreshing test data for current validation rules
- Updating error handling for new failure modes

Version management:
- Automatic backup of previous workflow version
- Change tracking with timestamps and descriptions
- Rollback capability to previous workflow versions
- Diff reporting showing changes made
- Version history maintenance

Validation after updates:
- Route accessibility verification
- Credential compatibility check
- Test data validity confirmation
- Scenario logic validation
- Dependency resolution verification

Update confirmation:
```
Workflow Update Summary:
=======================
Workflow: user-registration-flow
Version: 1.2.0 (previous: 1.1.0)

Changes Made:
+ Added route: /preferences (with 3 scenarios)
+ Added route: /tutorial (with 4 scenarios)  
- Removed route: /profile (moved to profile-management workflow)
~ Updated scenarios for /welcome (added tutorial link validation)

Validation Results:
✓ All routes accessible
✓ Credentials valid
✓ Test data patterns updated
✓ Dependencies resolved

Backup created: user-registration-flow.v1.1.0.backup
```

Arguments: $ARGUMENTS