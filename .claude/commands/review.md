# Review Command (Deprecated)

**⚠️ DEPRECATED**: This command has been moved to `/analysis:codereview`

## Usage
Instead of `> /analysis:review`, use:
```bash
/analysis:codereview <files> [focus]
```

## Automatic Redirect
This command automatically redirects to the correct analysis command.

## Implementation
Use the code-reviewer subagent for comprehensive code review:

> Use the code-reviewer subagent to perform a comprehensive code review with LEVER framework compliance assessment.

## Related Commands
- `/analysis:codereview` - Main code review command
- `/analysis:precommit` - Pre-commit review
- `/analysis:secaudit` - Security audit