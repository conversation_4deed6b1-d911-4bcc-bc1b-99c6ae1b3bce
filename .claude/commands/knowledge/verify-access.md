---
description: Verify all agents have proper access to their required documentation
---

# Verify Agent Document Access

**Usage**: `/knowledge:verify-access`

Ensures each PIB agent has access to the documentation they need for their specific roles.

## What This Command Does

### 1. Agent-Document Mapping Verification
Checks that each agent can access their required documents:

#### Analyst Agent
- **Requires**: Project brief, market research, competitive analysis
- **Creates**: Research reports, project briefs
- **Reads from**: `/docs/project-brief.md`, research files

#### PM Agent  
- **Requires**: Project brief, research findings
- **Creates**: PRD, epics
- **Reads from**: `/docs/project-brief.md`
- **Writes to**: `/docs/prd.md`, `/docs/epics/`

#### Architect Agent
- **Requires**: PRD, epics, technical constraints
- **Creates**: Architecture documents
- **Reads from**: `/docs/prd.md`, `/docs/epics/`
- **Writes to**: `/docs/architecture.md`, `/docs/frontend-architecture.md`

#### Dev Agent
- **Requires**: Architecture, stories, technical specs
- **Creates**: Implementation code
- **Reads from**: `/docs/architecture.md`, `/docs/stories/`

#### QA Agent
- **Requires**: Stories, acceptance criteria, architecture
- **Creates**: Test plans, test results
- **Reads from**: `/docs/stories/`, `/docs/architecture.md`

#### Code Reviewer
- **Requires**: Architecture, coding standards, LEVER principles
- **Creates**: Review reports
- **Reads from**: `/docs/architecture.md`, implementation files

### 2. Access Verification Process

#### Document Existence Check
```bash
# Verifies critical documents exist:
- /docs/prd.md
- /docs/architecture.md
- /docs/project-brief.md
- /docs/epics/ (directory)
- /docs/stories/ (directory)
```

#### Permission Verification
- Read access for required documents
- Write access for output locations
- Directory creation permissions

#### Content Validation
- Documents contain expected sections
- No empty required files
- Proper markdown structure

### 3. Knowledge File Synchronization
Ensures `.ai/` directory is synchronized:
```
.ai/
├── project-context.md      # All agents need this
├── tech-stack.md          # Dev, Architect, Reviewer need
├── data-models.md         # Dev, Architect need
├── deployment-info.md     # Platform Engineer needs
└── agent-access-map.md    # Documents who needs what
```

## Usage Examples

### Basic Verification
```bash
# Verify all agent access
/knowledge:verify-access

# Output:
# ✅ Analyst: All required documents accessible
# ✅ PM: All required documents accessible
# ⚠️  Architect: Missing /docs/prd.md
# ✅ Dev: All required documents accessible
# ❌ QA: No stories found in /docs/stories/
```

### With Specific Agent
```bash
# Verify specific agent access
/knowledge:verify-access --agent=architect

# Verify module-specific access
/knowledge:verify-access --module=auth
```

### With Auto-Fix
```bash
# Automatically fix access issues
/knowledge:verify-access --fix

# This will:
# - Create missing directories
# - Generate placeholder files
# - Update .ai/ directory
```

## Access Requirements by Agent

### Core Agents
| Agent | Must Read | Must Write | Optional |
|-------|-----------|------------|----------|
| Analyst | project-brief.md | project-brief.md, research reports | competitive analysis |
| PM | project-brief.md, research | prd.md, epics/ | user feedback |
| Architect | prd.md, epics/ | architecture.md | platform specs |
| Dev | architecture.md, stories/ | implementation | module docs |

### Specialized Agents
| Agent | Must Read | Must Write | Optional |
|-------|-----------|------------|----------|
| QA Tester | stories/, architecture.md | test plans | test results |
| Code Reviewer | architecture.md, LEVER | review reports | style guides |
| Platform Engineer | architecture.md | platform-architecture.md | deployment |
| Change Implementer | review reports | implementation | -|

## Common Issues & Solutions

### Issue: "Agent cannot find PRD"
**Solution**: 
- Check if PRD exists at `/docs/prd.md`
- Run `/knowledge:organize-docs` if misplaced
- Use `/agents:pm-prd` to create if missing

### Issue: "No stories accessible"
**Solution**:
- Verify `/docs/stories/` directory exists
- Run `/workflows:epic-to-stories` to generate
- Check story file naming convention

### Issue: "Knowledge files out of sync"
**Solution**:
- Run `/knowledge:update-knowledge`
- Verify `.ai/` directory structure
- Check file permissions

## Quality Assurance

### Pre-Verification Checklist
- [ ] Project has been initialized
- [ ] Basic documents created
- [ ] Directory structure established

### Post-Verification Actions
- [ ] All agents show green status
- [ ] No missing critical documents
- [ ] Knowledge files are current
- [ ] Cross-references work

## Integration with Workflows

### In Project Initialization
```bash
# After creating documents
/workflows:project-init
/knowledge:verify-access  # Ensure all agents ready
```

### After Reorganization
```bash
# After organizing docs
/knowledge:organize-docs
/knowledge:verify-access  # Verify nothing broke
```

### Before Major Development
```bash
# Before starting development
/knowledge:verify-access --fix
/knowledge:update-knowledge
```

## Access Matrix Output

The command generates an access matrix showing:
```
Agent Access Matrix:
┌─────────────┬──────────────┬──────────────┬────────┐
│ Agent       │ Can Read     │ Can Write    │ Status │
├─────────────┼──────────────┼──────────────┼────────┤
│ Analyst     │ ✓ brief      │ ✓ brief      │ ✅ OK  │
│ PM          │ ✓ brief      │ ✓ prd,epics  │ ✅ OK  │
│ Architect   │ ✓ prd,epics  │ ✓ arch       │ ✅ OK  │
│ Dev         │ ✓ arch,story │ ✓ code       │ ✅ OK  │
└─────────────┴──────────────┴──────────────┴────────┘
```

## Related Commands
- `/knowledge:organize-docs` - Organize documentation structure
- `/knowledge:update-knowledge` - Update agent knowledge files
- `/workflows:project-init` - Initialize with proper access
- `/analysis:docgen` - Generate missing documentation

---
*Part of the PIB Method's knowledge management system*