---
description: Organize and clean up project documentation structure
---

# Organize Documentation

**Usage**: `/knowledge:organize-docs`

Organize and restructure your /docs folder according to PIB-METHOD standards.

## What This Command Does

### 1. Document Organization
- Scans current `/docs` folder structure
- Identifies misplaced documents
- Reorganizes according to PIB standards
- Creates missing directories
- Maintains document integrity

### 2. Standard Structure Enforcement
```
docs/
├── prd.md                    # Product Requirements Document
├── architecture.md           # System Architecture
├── frontend-architecture.md  # UI/UX Architecture
├── platform-architecture.md  # Platform/Infrastructure
├── project-brief.md         # Project Overview
├── uxui-spec.md            # UI/UX Specifications
├── epics/                  # Epic files
│   └── *-epic.md
├── stories/                # Story files
│   └── *.story.md
└── modules/               # Module-specific docs
    └── {module-name}/
        ├── module-prd.md
        └── module-architecture.md
```

### 3. Document Migration
- **PRDs**: Move to `/docs/prd.md` (or module-specific location)
- **Epics**: Move to `/docs/epics/` with proper naming
- **Stories**: Move to `/docs/stories/` with proper naming
- **Architecture**: Consolidate to standard files
- **Module Docs**: Organize under `/docs/modules/{module}/`

### 4. Cleanup Actions
- Remove duplicate documents
- Archive outdated versions
- Fix naming inconsistencies
- Update cross-references
- Clean up temporary files

## Implementation Process

### Phase 1: Analysis
1. Scan current `/docs` structure
2. Identify all document types
3. Check against PIB standards
4. List required changes

### Phase 2: Organization
1. Create missing directories
2. Move documents to correct locations
3. Rename files to follow conventions
4. Update internal references

### Phase 3: Validation
1. Verify all documents are accessible
2. Check cross-references work
3. Ensure no content was lost
4. Update `.ai/` knowledge files

## Usage Examples

### Basic Organization
```bash
# Organize entire docs folder
/knowledge:organize-docs

# This will:
# 1. Analyze current structure
# 2. Show proposed changes
# 3. Execute reorganization
# 4. Report results
```

### With Options
```bash
# Dry run - show what would change
/knowledge:organize-docs --dry-run

# Archive old versions
/knowledge:organize-docs --archive

# Fix references only
/knowledge:organize-docs --fix-refs
```

## Common Scenarios

### Scenario 1: Mixed Document Locations
**Before:**
```
docs/
├── requirements.md
├── prd-draft.md
├── user-stories/
├── epic-payment.md
└── architecture-v2.md
```

**After:**
```
docs/
├── prd.md
├── architecture.md
├── epics/
│   └── payment-epic.md
└── stories/
    └── (moved from user-stories/)
```

### Scenario 2: Module Documentation
**Before:**
```
docs/
├── auth-module-prd.md
├── auth-architecture.md
└── payment-prd.md
```

**After:**
```
docs/
├── modules/
│   ├── auth/
│   │   ├── module-prd.md
│   │   └── module-architecture.md
│   └── payment/
│       └── module-prd.md
```

## Quality Checks

### Pre-Organization
- [ ] Backup important documents
- [ ] Note custom organization needs
- [ ] Check for work-in-progress files

### Post-Organization
- [ ] All documents accessible
- [ ] No content lost
- [ ] References updated
- [ ] Structure follows standards
- [ ] Knowledge files updated

## Integration with Other Commands

### After Organization
```bash
# Update agent knowledge with new structure
/knowledge:update-knowledge

# Verify all agents can access docs
/knowledge:verify-access

# Generate updated index
/knowledge:create-index
```

## Handling Special Cases

### 1. Work-in-Progress Documents
- Creates `docs/wip/` for drafts
- Preserves version suffixes
- Maintains edit history

### 2. Legacy Documents
- Archives to `docs/archive/`
- Preserves timestamps
- Maintains reference trail

### 3. External Documentation
- Links preserved in `docs/external/`
- README references updated
- Integration docs maintained

## Related Commands
- `/knowledge:update-knowledge` - Update agent knowledge after reorganization
- `/knowledge:verify-access` - Verify agents can access reorganized docs
- `/analysis:docgen` - Generate missing documentation
- `/workflows:project-init` - Initialize with proper structure

---
*Part of the PIB Method's knowledge management system*