# Save Credentials

Securely store authentication credentials for automated testing.

## Usage
```bash
init -> /workflows:project-init:save-credentials {site-name}
```

## Arguments
- `site-name`: Name identifier for the site/service credentials

## Implementation
Securely store authentication credentials with encryption:

1. **Interactive Input**: Prompt for username, email, password, and additional fields
2. **Encryption**: Encrypt credentials using local keychain/credential manager
3. **Validation**: Test credentials against the target site
4. **Storage**: Save encrypted credentials with site identifier
5. **Confirmation**: Verify successful storage and accessibility

Credential types supported:
- Username and password combinations
- Email and password authentication
- API keys and tokens
- OAuth refresh tokens
- Multi-factor authentication codes
- Custom authentication fields

Security features:
- Local encryption using system keychain (macOS Keychain, Windows Credential Manager, Linux Secret Service)
- No plain text storage
- Session-based access with timeout
- Credential validation before storage
- Secure deletion capabilities

Interactive prompts collect:
- Site/service identifier name
- Username or email address
- Password (hidden input)
- Additional fields (API keys, tokens, etc.)
- Optional notes or descriptions
- Expiration dates if applicable

The credentials are immediately tested against the target site to ensure validity before storage. This prevents storing incorrect or expired credentials.

Storage location: Encrypted in system credential manager with prefix "claude-test-{site-name}"

Arguments: $ARGUMENTS