# PR Comments Command (Deprecated)

**⚠️ DEPRECATED**: This command has been integrated into the GitHub workflow commands.

## Usage
Instead of `/pr-comments`, use:
```bash
/github:ai-pr <pr-number> "review comment"
```

## Automatic Redirect
For PR review and commenting, use the GitHub AI integration:

> Use the github:ai-pr command to add AI-powered review comments to pull requests.

## Implementation
The modern approach uses GitHub CLI integration for PR interactions.

## Related Commands
- `/github:ai-pr` - Add AI comments to PRs
- `/github:ai-issue` - Create and assign GitHub issues
- `/analysis:codereview` - Local code review