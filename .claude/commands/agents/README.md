# Agent Commands - PIB-METHOD

All agents are now consolidated in `pib-core/agents/`. This directory contains shortcuts for Claude Code.

## Core Agents (from BMAD)
- **analyst** (<PERSON>) - Market research and competitive analysis
- **pm** (<PERSON>) - Product management and requirements
- **architect** (<PERSON>) - System architecture and design  
- **dev** (<PERSON>) - Development with LEVER compliance

## Specialized Agents (from PIB)
- **code-reviewer** (<PERSON>) - Code quality validation
- **qa-tester** (<PERSON>) - Comprehensive testing
- **change-implementer** (Jordan) - Review feedback
- **platform-engineer** (<PERSON>) - Infrastructure
- **task-executor** (<PERSON>) - Sub-task execution
- **orchestrator** (<PERSON>) - Multi-agent coordination

## Usage
```
@agent *task
```

All agent definitions are in: `pib-core/agents/core/` and `pib-core/agents/specialized/`
