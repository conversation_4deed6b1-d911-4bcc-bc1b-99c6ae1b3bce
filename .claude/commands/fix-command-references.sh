#!/bin/bash

# Fix Command References Script
# Corrects all incorrect command references to match available PIB-METHOD commands

set -eo pipefail

echo "🔧 Fixing incorrect command references..."

# Define mapping of incorrect → correct commands
declare -A COMMAND_MAP=(
    # Agent commands
    ["brief -> /agents:analyst-brief-brief"]="/agents:analyst-brief"
    ["brief -> /agents:analyst-brief-module-research"]="/agents:analyst-brief --module"
    ["orchestrate -> /agents:pm-orchestrate-prd"]="/agents:pm-prd"
    ["orchestrate -> /agents:pm-orchestrate-module-prd"]="/agents:pm-prd --module"
    ["orchestrate -> /agents:pm-orchestrate-orchestrate"]="/agents:pm-orchestrate"
    ["design -> /agents:architect-design-design"]="/agents:architect-design"
    ["design -> /agents:architect-design-module-design"]="/agents:architect-design --module"
    ["design -> /agents:architect-design-review"]="/agents:architect-design --review"
    
    # Workflow commands that should use proper namespaces
    ["/sm-create-stories"]="help -> /quick:workflow-helps:epic-to-stories"
    ["/sm-module-stories"]="help -> /quick:workflow-helps:epic-to-stories --module"
    ["> help -> /quick:workflow-helps:dev-command-implement"]="help -> /quick:workflow-helps:dev-command"
    ["> help -> /quick:workflow-helps:dev-command-implement-fixes"]="help -> /quick:workflow-helps:dev-command --fix"
    ["/qa-review"]="/analysis:codereview"
    ["/qa-module-test-plan"]="/analysis:testgen --module"
    ["> help -> /quick:workflow-helps:dev-commandops-deploy-module"]="help -> /quick:workflow-helps:create-deployment-plan"
    
    # Other corrections
    ["knowledge -> /knowledge:update-knowledge-module-knowledge"]="/knowledge:update-knowledge --module"
    ["agent-coordination -> help -> /quick:workflow-helps:sub-agent-coordination-agent-coordination"]="help -> /quick:workflow-helps:spawn-subagents"
)

# Files to process
CLAUDE_DIR="/Users/<USER>/Projects/PIB-METHOD/.claude"

echo "📋 Processing ${#COMMAND_MAP[@]} command mappings..."

# Function to fix commands in a file
fix_commands_in_file() {
    local file="$1"
    local temp_file=$(mktemp)
    local changes_made=false
    
    cp "$file" "$temp_file"
    
    for old_cmd in "${!COMMAND_MAP[@]}"; do
        local new_cmd="${COMMAND_MAP[$old_cmd]}"
        
        # Replace the command if it exists in the file
        if grep -q "$old_cmd" "$file"; then
            sed -i.bak "s|$old_cmd|$new_cmd|g" "$temp_file"
            changes_made=true
            echo "  ✓ $old_cmd → $new_cmd"
        fi
    done
    
    if [ "$changes_made" = true ]; then
        mv "$temp_file" "$file"
        echo "  📝 Updated: $(basename "$file")"
    else
        rm "$temp_file"
    fi
}

# Process all .md files in .claude directory
echo "🔍 Scanning .claude directory for command references..."

find "$CLAUDE_DIR" -name "*.md" -type f | while read -r file; do
    echo "📄 Processing $(basename "$file")..."
    fix_commands_in_file "$file"
done

echo "✅ Command reference fixes completed!"
echo ""
echo "🔍 Summary of corrections made:"
for old_cmd in "${!COMMAND_MAP[@]}"; do
    echo "  $old_cmd → ${COMMAND_MAP[$old_cmd]}"
done