---
description: Create a new epic file using the PIB epic template
---

# Create Epic Workflow

**Usage**: `/workflows:create-epic $ARGUMENTS`

Create a new epic file: $ARGUMENTS

## What This Command Does

### 1. Epic Creation Process
- Uses the epic template from `pib-core/templates/project/epic-tmpl.yaml`
- Guides through structured epic creation
- Ensures all required sections are included
- Saves to `docs/epics/` directory

### 2. Epic Structure
The created epic will include:
- **Overview**: High-level description of the epic
- **Goals**: Primary, secondary, and business goals
- **Success Criteria**: Measurable success indicators
- **Requirements**: Core functionality and technical requirements
- **Constraints & Assumptions**: Limitations and assumptions
- **Dependencies**: External dependencies
- **Risks & Mitigation**: Risk assessment and mitigation strategies
- **LEVER Framework**: Application of LEVER principles

### 3. File Organization
- Epic files are saved to `docs/epics/`
- Naming convention: `{epic-name}-epic.md`
- Example: `user-auth-epic.md`, `payment-system-epic.md`

## Usage Examples

### Basic Epic Creation
```bash
# Create a new epic interactively
/workflows:create-epic "User Authentication System"

# This will:
# 1. Use the epic template
# 2. Guide through each section
# 3. Save to docs/epics/user-authentication-system-epic.md
```

### With Specific Focus
```bash
# Create epic with specific requirements focus
/workflows:create-epic "Payment Processing" --focus=technical

# Create epic with business goals emphasis
/workflows:create-epic "Social Sharing Features" --focus=business
```

## Epic Template Sections

### 1. Header & Overview
- Epic title and description
- Context and background

### 2. Goals
- Primary goal (main objective)
- Secondary goals (supporting objectives)
- Business goals (value proposition)

### 3. Success Criteria
- Measurable success indicators
- Acceptance criteria
- Performance metrics

### 4. Requirements
- Core functionality breakdown
- Technical requirements
- Non-functional requirements

### 5. LEVER Framework
- **Leverage**: Existing functionality to reuse
- **Extend**: Components to enhance
- **Verify**: Testing and validation approach
- **Eliminate**: Duplication to remove
- **Reduce**: Complexity to minimize

## Integration with Story Generation

After creating an epic:
```bash
# Generate stories from the epic
/workflows:epic-to-stories docs/epics/your-epic.md

# Stories will be created in docs/stories/
```

## Best Practices

### 1. Epic Sizing
- Keep epics focused on a single major feature
- Aim for 3-8 stories per epic
- If larger, consider splitting into multiple epics

### 2. Success Criteria
- Make them specific and measurable
- Include performance targets
- Define user satisfaction metrics

### 3. LEVER Application
- Always identify reuse opportunities
- Plan for extensibility
- Define verification strategy upfront

### 4. Risk Management
- Identify technical risks early
- Include mitigation strategies
- Consider dependencies

## Related Commands
- `/workflows:epic-to-stories` - Generate stories from epic
- `/workflows:project-init` - Full project initialization
- `/planning:plan-session` - Planning session for epics
- `@pm create requirements` - Alternative PRD creation

---
*Part of the PIB Method's structured development workflow*