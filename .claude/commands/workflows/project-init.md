# Complete Project Initialization Workflow

Start a new project from scratch through to development readiness.

## Usage

For new projects:
```
/workflows:project-init
```

For existing projects (takeovers):
```
/workflows:project-init --existing-project
```

For complex projects requiring platform infrastructure:
```
/workflows:project-init --complex
```

## For Existing Projects (--existing-project flag)

When taking over an existing project, the workflow adapts:
- **Skips**: Project brief creation (analyzes existing code instead)
- **Adapts**: PRD generation based on current functionality
- **Detects**: Current technology stack and patterns
- **Creates**: Only missing documentation and agents
- **Preserves**: Existing code and documentation

## Workflow Steps
Execute this complete workflow for project initialization:

### Phase 0: Technology Stack Detection
0. **Tech Lead** - Analyze Technology Stack
   - Run `/workflows:project-init-tech --research --create-missing`
   - Detect frameworks, languages, and tools
   - Create/assign specialized agents
   - Generate tech-specific routing rules
   - Update CLAUDE.md with stack configuration

### Phase 1: Requirements & Design
1. **Analyst** - Create Project Brief
   - **New Projects**: Research and brainstorm project concept
   - **Existing Projects**: Analyze current codebase and functionality
   - Document requirements and constraints
   - Save to `docs/project-brief.md`

2. **PM** - Create PRD  
   - **New Projects**: Create PRD from requirements
   - **Existing Projects**: Reverse-engineer PRD from code analysis
   - Document current features and planned enhancements
   - Save to `docs/prd.md`

3. **Architect** - Create Architecture
   - **New Projects**: Design system architecture from PRD
   - **Existing Projects**: Document current architecture
   - Identify improvement opportunities
   - Save to `docs/architecture.md`

4. **Platform Engineer** *(Complex projects only)*
   - Design platform infrastructure for microservices/enterprise
   - Save to `docs/platform-architecture.md`

5. **Design Architect** - Create Frontend Architecture
   - Design UI/UX architecture if applicable
   - Save to `docs/frontend-architecture.md`

6. **Design Architect** - Create UX/UI Spec
   - Create detailed UI/UX specifications
   - Save to `docs/uxui-spec.md`

### Phase 2: Knowledge & Organization
7. **PIB** - Update Agent Knowledge
   - Extract and distribute project knowledge
   - Create `.ai/` directory structure
   - Update all agent contexts

8. **PO** - Organize Documentation
   - Organize and validate all documentation
   - Ensure consistency and completeness

### Phase 3: Implementation Setup  
9. **SM** - Document Sharding
   - Break down large documents into manageable pieces
   - Create epic files in `docs/epics/`

10. **SM** - Create Stories with Sub-Agent Strategy
    - Create implementation stories with sub-agent assignments
    - Include technology-specific specialist assignments
    - Reference tech stack routing from Phase 0
    - Include parallel execution planning in each story
    - Save to `docs/stories/1.1.story.md` (and subsequent stories)

11. **Bill (PM)** - Create Orchestration Guide
    - Analyze all stories for parallel execution opportunities
    - Create task execution matrix with tech-specialist assignments
    - Integrate technology routing rules from CLAUDE.md
    - Save orchestration guide to `docs/stories/{project-name}-orchestration-guide.md`
    - Use `orchestrate -> /agents:pm-orchestrate-orchestrate` command

### Phase 4: Coordinated Development Execution
12. **Tech Lead** - Technology-Aware Coordination
    - Route tasks to technology specialists based on CLAUDE.md
    - Coordinate handoffs between framework experts
    - Ensure integration between different tech stacks

13. **Bill (PM)** - Coordinate Sub-Agent Execution
    - Follow orchestration guide for task assignments
    - Leverage both PIB agents and tech specialists
    - Create sub-agents for parallel work (Frontend, Backend, DevOps, Testing)
    - Use `agent-coordination -> /workflows:sub-agent-coordination-agent-coordination` workflow

14. **James (Dev)** - Execute Through Specialists
    - Implement using technology-specific agents
    - Follow framework best practices from specialists
    - Coordinate across multiple sub-agent contexts
    - Report progress to Bill for orchestration updates

15. **Specialized Reviewers** - Quality Validation
    - Tech specialists review their domain code
    - UI-Reviewer validates frontend work
    - Code-Reviewer validates backend work  
    - Infrastructure-Reviewer validates DevOps work
    - QA-Reviewer validates testing work

16. **Changer Agents** - Implement Feedback
    - Tech specialists implement domain-specific feedback
    - UI-Changer implements frontend feedback
    - Code-Changer implements backend feedback
    - Infrastructure-Changer implements DevOps feedback
    - Test-Changer implements testing feedback

17. **Integration and Deployment**
    - Integrate all sub-agent work
    - Execute comprehensive testing across all components
    - Deploy to appropriate environment with monitoring

## Special Considerations
- **Complex Projects**: Include Platform Engineer for microservices, service mesh, enterprise infrastructure
- **Simple Projects**: Skip Platform Engineer for basic applications
- **UI-Heavy Projects**: Use `/dalle` for mockup generation after UI specs
- **Research-Heavy**: Use `/perplexity` during research phases

## Orchestration Integration
- **After Phase 3**: Use `orchestrate -> /agents:pm-orchestrate-orchestrate` to create coordination strategy
- **During Phase 4**: Use `agent-coordination -> /workflows:sub-agent-coordination-agent-coordination` for parallel execution
- **Throughout**: Bill (PM) manages all agent assignments and progress tracking

## Knowledge Updates
Run `/knowledge:update-knowledge` after each major phase to keep all agents synchronized.

## Related Commands
- `init-tech -> /workflows:project-init-tech-init-tech` - Initialize with technology detection
- `create-agent -> /workflows:create-tech-agent-create-agent` - Create technology-specific agents
- `orchestrate -> /agents:pm-orchestrate-orchestrate` - Create orchestration guide for coordinated execution
- `agent-coordination -> /workflows:sub-agent-coordination-agent-coordination` - Manage parallel sub-agent execution
- `/agents:tech-lead` - Coordinate technology specialists
- `dev -> /workflows:module-dev-dev` - Add modules to existing projects
- `fix -> /workflows:legacy-fix-fix` - Take over existing problematic projects
- `/knowledge:update-knowledge` - Update agent knowledge base