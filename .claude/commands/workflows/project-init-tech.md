# Technology-Aware Project Initialization

Initialize projects with automatic technology stack detection and specialized agent assignment.

## Usage
```bash
/workflows:project-init-tech [--research] [--create-missing]
```

## Workflow Phases

### Phase 1: Stack Detection & Analysis
1. **Scan Project Structure**
   - Detect package.json, requirements.txt, Gemfile, etc.
   - Identify frameworks and libraries
   - Analyze existing code patterns

2. **Research Technology Stack** (if --research)
   - Use Perplexity to research latest best practices
   - Identify common patterns and anti-patterns
   - Gather framework-specific conventions

### Phase 2: Agent Selection & Creation
3. **Match Existing Agents**
   - Find specialized agents for detected technologies
   - Identify gaps in agent coverage

4. **Create Missing Agents** (if --create-missing)
   - Generate new agents for uncovered technologies
   - Research-based agent creation
   - Validate with framework documentation

### Phase 3: Project Configuration
5. **Generate Enhanced CLAUDE.md**
   ```markdown
   # Project Technology Configuration
   
   ## Detected Stack
   - Frontend: React 18.2, TypeScript 5.0
   - Backend: Express.js 4.18, Node.js 18
   - Database: PostgreSQL 15, Redis 7
   - Testing: Je<PERSON>, Playwright
   
   ## Agent Assignments
   ### Routing Rules
   - /src/components/* → @react-specialist
   - /src/hooks/* → @react-hooks-expert
   - /src/api/* → @express-specialist
   - /src/db/* → @postgresql-expert
   - /tests/* → @qa-tester
   
   ### Workflow Patterns
   - "create component" → @react-specialist → @code-reviewer
   - "add API endpoint" → @express-specialist → @api-tester
   - "optimize query" → @postgresql-expert → @performance-optimizer
   
   ## Quality Gates
   - All frontend changes reviewed by @react-specialist
   - API changes require @security-guardian review
   - Database migrations need @postgresql-expert approval
   ```

6. **Setup Agent Collaboration**
   - Define handoff patterns
   - Create consultation rules
   - Establish review requirements

### Phase 4: Integration
7. **Update Project Init**
   - Integrate with existing PIB workflow
   - Maintain LEVER compliance
   - Add to orchestration guide

## Example Execution

```bash
# Initialize with research and agent creation
> /workflows:project-init-tech --research --create-missing

[Scanning project...]
Detected: React 18, Express.js, PostgreSQL

[Researching best practices...]
✓ React 18 concurrent features
✓ Express.js middleware patterns
✓ PostgreSQL optimization techniques

[Creating missing agents...]
✓ Created: react-concurrent-specialist
✓ Created: express-middleware-expert

[Configuring project...]
✓ Updated CLAUDE.md with routing rules
✓ Set up agent collaboration patterns
✓ Configured quality gates

Project initialized with 5 specialized agents!
```

## Parameters
- `--research`: Research best practices for detected technologies
- `--create-missing`: Create new agents for technologies without specialists
- `--update-existing`: Update existing agents with latest practices

## Integration Points
- Runs after Phase 1 of standard project-init
- Before story creation to inform sub-agent assignments
- Updates orchestration guide with tech-specific patterns

## Related Commands
- `/workflows:create-tech-agent` - Create individual technology agents
- `/agents:tech-lead` - Orchestrate technology-specific workflows
- `/workflows:project-init` - Standard project initialization