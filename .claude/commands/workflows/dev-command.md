# Dev Command - Intelligent Development Workflow

Intelligent development command that automatically creates enriched work specifications and orchestrates agent workflows using MCP tools for enhanced development efficiency.

## Usage

Basic development request:
```
> /workflows:dev-command "implement user authentication"
```

Complex development with specific requirements:
```
> /workflows:dev-command "implement user authentication with OAuth support and session management"
```

Specify priority and agent focus:
```
> /workflows:dev-command "implement user authentication" --priority=high --focus=security
```

Status check for active development workflows:
```
> /workflows:dev-command --status
```

## Purpose

Transform development requests into intelligent, context-aware workflows that leverage Claude Code's sub-agent architecture:
- Parse requirements using advanced MCP integration
- Research existing patterns with Context7 MCP 
- Create enriched work specifications with implementation guidance
- Automatically delegate to Claude Code sub-agents
- Maintain state across complex development cycles with seamless sub-agent coordination

## Key Features

### 1. Dynamic Context Engineering
- **Right Information**: Relevance-scored context extraction based on task type and user input
- **Right Format**: Agent-specific context packages with filtered, actionable information
- **Right Time**: Just-in-time context updates during workflow execution
- **Intelligent Analysis**: Uses Zen MCP for multi-model requirement analysis and validation
- **Pattern Research**: Leverages Context7 MCP to identify existing solutions and best practices
- **Work Specification**: Creates detailed specifications with enriched technical context

### 2. Smart Agent Routing (V2 Enhanced)
- **Complexity Detection**: Automatically assesses task complexity (simple/moderate/complex)
- **Direct Execution**: 80% of tasks go straight to universal-dev-agent (no orchestration!)
- **Plugin Loading**: Dynamic loading of tech-specific patterns
- **Intelligent Orchestration**: Only complex tasks trigger full orchestration
- **Performance Optimized**: 50% faster completion for simple tasks

### 3. Enhanced Development Quality
- **LEVER Framework Compliance**: Ensures leverage of existing patterns and minimal code creation
- **MCP Tool Integration**: Uses appropriate tools for analysis, validation, and implementation
- **Quality Gates**: Integrates with existing review and validation processes

## Smart Routing Examples

### Simple Task (Direct to Universal Agent)
```bash
> /workflows:dev-command "add loading state to login button"
[Detected: React, Simple complexity]
→ universal-dev-agent (with react-patterns plugin)
→ Implementation complete in 30 seconds!
```

### Complex Task (Tech Lead Orchestration)
```bash
> /workflows:dev-command "implement OAuth with social providers"
[Detected: React + Express + PostgreSQL + Redis, Complex]
→ tech-lead → coordinates specialist team
→ Proper orchestration for complex feature
```

## Workflow Process

### Phase 1: Intelligent Analysis with LEVER Validation
```
User Request → LEVER Pre-Check → Zen MCP Analysis → Context7 Pattern Research → LEVER Assessment → Work Specification
```

1. **LEVER Pre-Check**: Before any analysis, validate against LEVER principles:
   - **L**everage: Search existing codebase for similar functionality
   - **E**xtend: Identify extension opportunities in current code
   - **V**erify: Check for reactive patterns that could be utilized
   - **E**liminate: Detect potential duplication before it's created
   - **R**educe: Assess if simpler solutions exist

2. **Requirement Parsing**: Zen MCP analyzes the development request for:
   - Core functionality requirements
   - Technical complexity assessment  
   - Potential implementation approaches
   - Quality and security considerations
   - **LEVER Compliance Score**: Rate the approach against LEVER principles

3. **Pattern Research**: Context7 MCP investigates:
   - Existing implementation patterns (LEVER: Leverage)
   - Library and framework options (LEVER: Leverage)
   - Best practices and recommendations
   - Similar functionality in codebase (LEVER: Extend)
   - Extension opportunities in current architecture

4. **LEVER Assessment**: Evaluate research findings:
   - Score reuse opportunities (0-5)
   - Identify extension points in existing code
   - Recommend reactive patterns for verification
   - Flag potential duplications
   - Suggest complexity reduction approaches

5. **Work Specification Creation**: Generate enriched specification containing:
   - Structured requirements and acceptance criteria
   - **LEVER Compliance Plan**: Specific steps to follow LEVER principles
   - Technical implementation guidance with reuse focus
   - Research findings and pattern recommendations
   - Integration points and dependencies
   - **Complexity Reduction Strategy**: Simplification opportunities

### Phase 2: Claude Code Sub-Agent Orchestration with LEVER Context
```
Work Specification → Tech Stack Detection → LEVER Context Injection → Sub-Agent Delegation → Enhanced Context → Development Execution
```

1. **Technology Stack Detection**: Analyze project and file context:
   - Detect frameworks and libraries from CLAUDE.md configuration
   - Identify file-specific technology patterns
   - Route to specialized technology agents when available
   - Fall back to general agents for unknown technologies

2. **LEVER Context Injection**: Embed LEVER principles into all sub-agent contexts:
   - Provide LEVER compliance plan from Phase 1
   - Include reuse opportunities and extension points
   - Specify complexity reduction requirements
   - Set LEVER score targets (minimum 4/5)

3. **Smart Routing V2**: Complexity-based routing:
   - **Simple tasks (80%)**: Direct to universal-dev-agent with auto-loaded plugins
   - **Moderate tasks (15%)**: Universal-dev-agent with multiple plugins
   - **Complex tasks (5%)**: Tech-lead orchestration with specialist team
   - **Override options**: --force-complex or --force-simple flags
   - **Performance**: Bypasses orchestration for most tasks

3. **Sub-Agent Context Enhancement**: Enrich sub-agent context with:
   - Work specification and requirements
   - **LEVER Implementation Guide**: Step-by-step LEVER application
   - Research findings and pattern recommendations
   - Implementation guidance and quality requirements
   - Integration context and dependency information
   - **Reuse Checklist**: Mandatory checks before new code creation

4. **Workflow State Management**: Track and manage:
   - Current development phase and progress
   - **LEVER Compliance Score**: Track throughout development
   - Sub-agent assignments and transitions
   - Context preservation across sub-agent handoffs
   - Quality gate completions with LEVER validation

### Phase 3: Claude Code Sub-Agent Quality & Integration with LEVER Validation
```
Implementation → LEVER Compliance Check → Sub-Agent Review → LEVER Score → Change Implementation → Final Integration
```

1. **LEVER Compliance Check**: Before any review, validate implementation:
   - **L**everage: Verify reuse of existing patterns and libraries
   - **E**xtend: Confirm extension over new creation where possible
   - **V**erify: Validate reactive patterns and self-correction mechanisms
   - **E**liminate: Check for duplication elimination
   - **R**educe: Assess complexity reduction achieved
   - **Generate LEVER Score**: 0-5 rating with improvement recommendations

2. **Claude Code Sub-Agent Review Process**: 
   - **Automatic Delegation**: Claude Code delegates to code-reviewer sub-agent
   - **MCP-Enhanced Analysis**: Sub-agent uses Context7 for validation against best practices
   - **Pattern Validation**: Verify adherence to researched patterns through sub-agent expertise
   - **Quality Standards**: Sub-agent ensures PIB and LEVER framework compliance
   - **LEVER Review**: Dedicated sub-agent review of LEVER principle application
   - **Mandatory LEVER Score**: Minimum 4/5 required for approval

3. **LEVER Score Assessment by Sub-Agent**:
   - **Score Calculation**: Sub-agent provides detailed scoring for each LEVER principle
   - **Improvement Recommendations**: Sub-agent suggests specific LEVER compliance improvements
   - **Refactoring Requirements**: Sub-agent mandates changes if score < 4/5
   - **Best Practice Documentation**: Sub-agent captures successful LEVER applications

4. **Intelligent Change Implementation via Sub-Agent**:
   - **change-implementer Sub-Agent**: Automatic delegation for addressing review feedback
   - **Context-Aware Changes**: Sub-agent uses enriched context for implementing feedback
   - **Pattern-Based Solutions**: Sub-agent leverages researched patterns for improvements
   - **LEVER-Guided Refactoring**: Sub-agent applies LEVER principles to address review feedback
   - **Quality Validation**: Continuous validation against specifications and LEVER compliance

## Command Parameters

### Basic Parameters
- `--priority`: Task priority (low, medium, high, critical) - default: medium
- `--focus`: Development focus area (security, performance, ui, api, data) - default: general
- `--agent`: Preferred agent assignment (auto, james, multi) - default: auto
- `--style`: Development style (minimal, standard, comprehensive) - default: standard

### Advanced Parameters
- `--research-depth`: Context7 research depth (quick, standard, deep) - default: standard
- `--analysis-mode`: Zen analysis complexity (basic, standard, comprehensive) - default: standard
- `--quality-level`: Review intensity (standard, enhanced, strict) - default: standard
- `--pattern-search`: Pattern research scope (local, project, external) - default: project

### LEVER Compliance Parameters
- `--lever-target`: Target LEVER score (3, 4, 5) - default: 4
- `--lever-focus`: Primary LEVER focus (leverage, extend, verify, eliminate, reduce, all) - default: all
- `--skip-lever`: Skip LEVER validation (not recommended) - default: false
- `--lever-strict`: Enforce strict LEVER compliance (fail if target not met) - default: true

### Status and Management
- `--status`: Show active development workflow status
- `--pause`: Pause current development workflow
- `--resume`: Resume paused development workflow
- `--cancel`: Cancel current development workflow

## Integration Points

### MCP Tool Integration
- **Zen MCP**: Multi-model analysis for requirement parsing and validation
- **Context7 MCP**: Pattern research and documentation retrieval
- **Enhanced Orchestration**: Intelligent agent coordination and handoff management

### Hook System Integration
- **Enhanced Orchestration Hook**: Automatic workflow triggering and management
- **Development Workflow Enhancer**: Context enrichment and state management
- **Quality Gate Integration**: Seamless integration with review and validation hooks

### State Management Integration
- **Workflow State**: Persistent state management for complex development cycles
- **Context Preservation**: Rich context maintained across agent transitions
- **Recovery Mechanisms**: Robust error handling and recovery processes

## Work Specification Structure

Generated work specifications include:

### Requirements Section
- **Primary Objective**: Core functionality to implement
- **Acceptance Criteria**: Specific, testable requirements
- **Quality Requirements**: Performance, security, maintainability standards
- **Integration Requirements**: Dependencies and integration points

### Technical Context
- **Research Findings**: Context7 pattern research results
- **Implementation Guidance**: Recommended approaches and frameworks
- **Architecture Considerations**: Design patterns and structural requirements
- **Dependency Analysis**: Required libraries and components

### Quality Assurance
- **Testing Strategy**: Recommended testing approaches and coverage
- **Review Criteria**: Specific review focuses and quality gates
- **Performance Requirements**: Performance benchmarks and constraints
- **Security Considerations**: Security requirements and validation criteria

### Implementation Plan
- **Task Breakdown**: Structured task decomposition
- **Agent Assignment**: Recommended agent allocation and responsibilities
- **Timeline Estimates**: Complexity-based time estimation
- **Risk Assessment**: Potential challenges and mitigation strategies

## Error Handling

### Requirement Analysis Failures
- **Incomplete Requirements**: Prompt for clarification and additional context
- **Technical Complexity**: Escalate to architect or break down into phases
- **Resource Conflicts**: Coordinate with existing workflows and dependencies

### Pattern Research Failures
- **No Patterns Found**: Proceed with minimal implementation approach
- **Conflicting Patterns**: Use Zen consensus tools for decision making
- **Integration Issues**: Research integration patterns and compatibility

### Workflow Orchestration Failures
- **Agent Unavailability**: Reassign to available agents or queue
- **Context Loss**: Restore from state management and re-enrich
- **Quality Gate Failures**: Trigger enhanced review and change implementation

## Quality Assurance Framework

### LEVER Framework Compliance
- **L**everage: Always research existing patterns before creating new code
- **E**xtend: Prioritize extending existing functionality over new implementation
- **V**erify: Use MCP tools for continuous verification and validation
- **E**liminate: Detect and eliminate duplication through pattern research
- **R**educe: Minimize complexity through intelligent analysis and design

### Development Quality Standards
- **Code Quality**: Adherence to PIB coding standards and best practices
- **Testing Coverage**: Comprehensive testing strategy and implementation
- **Security Standards**: Security-first development with validation
- **Performance Standards**: Performance considerations and optimization

### Review Quality Enhancement
- **Multi-Model Validation**: Use Zen tools for enhanced code review
- **Pattern Validation**: Verify adherence to researched best practices
- **Context-Aware Review**: Review with full context of requirements and patterns
- **Continuous Improvement**: Learn from each workflow to enhance future development

## Examples

### Simple Feature Implementation
```bash
> /workflows:dev-command "add password reset functionality"
```
- Zen MCP analyzes security and UX requirements
- Context7 researches password reset patterns and libraries
- Creates work specification with security best practices
- Assigns to James with enhanced context
- Triggers security-focused review process

### Complex Feature Development
```bash
> /workflows:dev-command "implement real-time chat system with message persistence" --priority=high --focus=performance
```
- Comprehensive requirement analysis for real-time systems
- Research WebSocket libraries and message queue patterns
- Multi-agent coordination for frontend, backend, and infrastructure
- Performance-focused quality gates and optimization requirements
- Enhanced review process with performance validation

### API Development
```bash
> /workflows:dev-command "create RESTful API for user management" --style=comprehensive
```
- API design pattern research and best practices
- OpenAPI specification integration and documentation
- Comprehensive testing strategy including API testing
- Security validation and authentication integration
- Enhanced documentation and integration testing

## Related Commands
- `orchestrate -> /agents:pm-orchestrate-orchestrate` - Project management orchestration for larger initiatives
- `agent-coordination -> /workflows:sub-agent-coordination-agent-coordination` - Multi-agent coordination for complex features
- `> /analysis:codereview` - Enhanced code review with MCP tool integration
- `design -> /agents:architect-design-review` - Architectural assessment and validation

---
*Part of the PIB Method's intelligent development workflow system*