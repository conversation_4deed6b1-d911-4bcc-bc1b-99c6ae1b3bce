# Dev Command V2 - Smart Development Workflow

Intelligent development command with automatic complexity detection and smart agent routing.

## Usage

```bash
/workflows:dev-command "implement feature"
```

## Smart Routing Process

### Phase 1: Task Analysis
1. **Parse Request**: Extract core requirements
2. **Detect Technology**: Scan context for tech stack
3. **Assess Complexity**: Simple, moderate, or complex
4. **Route Intelligently**: Direct to appropriate agent

### Phase 2: Execution Paths

#### Simple Tasks (80% of requests)
```mermaid
graph LR
    A[User Request] --> B[Tech Detection]
    B --> C[Universal Agent]
    C --> D[Load Plugin]
    D --> E[Direct Implementation]
```

**Examples**:
- "Add loading spinner to button" → universal-dev-agent + react-plugin
- "Create user API endpoint" → universal-dev-agent + express-plugin
- "Add database index" → universal-dev-agent + postgres-plugin

#### Complex Tasks (20% of requests)
```mermaid
graph LR
    A[User Request] --> B[Complexity Detection]
    B --> C[Tech Lead]
    C --> D[Orchestrate Specialists]
    D --> E[Coordinated Implementation]
```

**Examples**:
- "Implement real-time chat system" → tech-lead orchestration
- "Add payment processing" → multi-agent coordination
- "Refactor authentication system" → specialist team

## Technology Detection

### Automatic Detection Sources
1. **Current File**: Extension and imports
2. **Project Config**: package.json, requirements.txt
3. **CLAUDE.md**: Project-specific stack
4. **Recent Edits**: Context from session
5. **Task Description**: Keywords and patterns

### Detection Examples
```javascript
// Detects: React + TypeScript
import React, { useState } from 'react';

// Detects: Express.js
app.post('/api/users', async (req, res) => {

// Detects: PostgreSQL
await pool.query('SELECT * FROM users WHERE...');
```

## Complexity Assessment

### Simple (Direct to Universal Agent)
- Single file changes
- One technology involved
- Clear implementation path
- No architectural decisions
- < 100 lines of code

### Moderate (Universal Agent + Plugins)
- 2-5 files affected
- 1-2 technologies
- Standard patterns apply
- Minimal integration work
- < 500 lines of code

### Complex (Tech Lead Orchestration)
- Multiple subsystems
- 3+ technologies
- Architectural impacts
- Security implications
- Real-time requirements

## Enhanced LEVER Integration

### Universal Agent LEVER Checks
1. **Before Implementation**:
   - Search existing patterns (Leverage)
   - Find extension points (Extend)
   - Plan verification (Verify)

2. **During Implementation**:
   - Reuse found patterns
   - Extend existing code
   - Add self-checks

3. **After Implementation**:
   - Calculate LEVER score
   - Self-review quality
   - Document patterns used

## Plugin Loading Strategy

### Efficient Plugin Management
```typescript
// Pseudo-code for plugin loading
const loadPlugins = async (technologies: string[]) => {
  const plugins = [];
  
  for (const tech of technologies) {
    // Check cache first
    if (pluginCache.has(tech)) {
      plugins.push(pluginCache.get(tech));
      continue;
    }
    
    // Load and cache new plugin
    const plugin = await loadPlugin(tech);
    pluginCache.set(tech, plugin);
    plugins.push(plugin);
  }
  
  return plugins;
};
```

## Workflow Examples

### Example 1: Simple React Component
```bash
> /workflows:dev-command "add user avatar component"

[Detection]
- Technology: React (from .tsx files)
- Complexity: Simple (single component)
- Routing: universal-dev-agent

[Execution]
- Load react-patterns plugin
- Create component with best practices
- Add tests and documentation
- Complete in one pass!
```

### Example 2: API Endpoint
```bash
> /workflows:dev-command "create endpoint to update user profile"

[Detection]
- Technology: Express + PostgreSQL
- Complexity: Moderate (API + DB)
- Routing: universal-dev-agent

[Execution]
- Load express-patterns + postgres-patterns
- Create route, controller, validation
- Add database query with transaction
- Include error handling
```

### Example 3: Complex Feature
```bash
> /workflows:dev-command "implement real-time notifications"

[Detection]
- Technology: React + Express + Redis + WebSocket
- Complexity: Complex (4+ technologies)
- Routing: tech-lead orchestration

[Execution]
- Tech lead coordinates specialists
- Frontend specialist: React components
- Backend specialist: WebSocket server
- Database specialist: Redis pub/sub
- Integration and testing
```

## Benefits Over V1

### Performance Improvements
- **80% faster** for simple tasks
- **50% fewer** agent handoffs
- **Direct execution** without orchestration
- **Cached plugins** for repeat tasks

### Quality Maintained
- Same LEVER standards (4/5 minimum)
- Automated quality checks
- Pattern reuse enforcement
- Built-in best practices

### Better Developer Experience
- Less waiting for handoffs
- Clearer execution path
- Predictable behavior
- Faster feedback

## Configuration Options

### Override Routing
```bash
# Force specific agent
/workflows:dev-command "task" --agent=tech-lead

# Force simple routing
/workflows:dev-command "task" --simple

# Disable plugins
/workflows:dev-command "task" --no-plugins
```

### Custom Complexity Rules
In CLAUDE.md:
```json
{
  "complexity_overrides": {
    "payment": "always_complex",
    "config_update": "always_simple",
    "auth": "always_complex"
  }
}
```

## Migration from V1

### Backward Compatibility
- All V1 commands still work
- Same quality standards
- Gradual migration path
- No breaking changes

### Gradual Adoption
1. New projects use V2 by default
2. Existing projects opt-in
3. Monitor performance metrics
4. Full migration after validation

## Success Metrics

### Target Improvements
- Simple task completion: < 30 seconds
- Plugin detection accuracy: > 95%
- LEVER compliance: 100% at 4/5+
- Developer satisfaction: Increased

### Monitoring
- Track routing decisions
- Measure completion times
- Monitor quality scores
- Collect feedback

## Related Commands
- `/agents:universal-dev-agent` - Direct agent access
- `/core:tech-plugins` - List available plugins
- `/workflows:create-tech-plugin` - Create new plugin
- `/analysis:complexity` - Analyze task complexity