# List Active Feature Worktrees

Shows all active feature worktrees with their status, branches, and activity information.

## Usage
```
start -> /workflows:feature-start-list [format]
```

## Parameters
- `format`: Display format - 'table' (default), 'detailed', or 'json'

## What This Command Shows

### Basic Information
- **Feature Name**: Clean feature identifier
- **Directory**: Full path to worktree
- **Branch**: Git branch name with timestamp
- **Status**: Current working state (Clean/Modified)

### Activity Details
- **Last Commit**: Time since last commit
- **Uncommitted Changes**: Staged/unstaged modifications
- **State Health**: Isolated state validation status
- **Context Status**: PIB Context Engineering activity

## Display Formats

### Table Format (Default)
```
| Feature | Directory | Branch | Status |
|---------|-----------|--------|--------|
| user-auth | ../pib-user-auth | feature/user-auth-20250704 | Modified |
| payment-sys | ../pib-payment-sys | feature/payment-sys-20250704 | Clean |
```

### Detailed Format
```
🌳 Active PIB Worktrees (2 found)

📁 user-auth
   Directory: /Projects/pib-user-auth
   Branch: feature/user-auth-20250704
   Status: Modified (3 files changed)
   Last Activity: 2 hours ago
   State: Healthy (isolated)
   Context: 5 evolution chains, 12 tool selections

📁 payment-sys
   Directory: /Projects/pib-payment-sys  
   Branch: feature/payment-sys-20250704
   Status: Clean
   Last Activity: 1 day ago
   State: Healthy (isolated)
   Context: 2 evolution chains, 8 tool selections
```

### JSON Format
```json
{
  "active_worktrees": {
    "user-auth": {
      "directory": "/Projects/pib-user-auth",
      "branch": "feature/user-auth-20250704", 
      "status": "modified",
      "last_commit": "2 hours ago",
      "state_health": "healthy",
      "context_chains": 5
    }
  }
}
```

## Status Indicators

### Working Status
- **Clean**: No uncommitted changes
- **Modified**: Has uncommitted changes (staged or unstaged)
- **Conflict**: Merge conflicts present
- **Detached**: Not on expected branch

### State Health
- **Healthy**: State isolation working properly
- **Warning**: Minor state issues detected
- **Error**: State corruption or missing directories

### Activity Levels
- **Active**: Recent commits (< 24 hours)
- **Idle**: No recent activity (1-7 days)
- **Stale**: Long inactivity (> 7 days)

## Examples

### Basic Listing
```
start -> /workflows:feature-start-list
```
Shows table view of all active worktrees

### Detailed Information
```
start -> /workflows:feature-start-list detailed
```
Shows comprehensive information about each worktree

### Machine-Readable Output
```
start -> /workflows:feature-start-list json
```
Outputs JSON for scripting or integration

## Worktree Information Details

### Git Information
- **Branch tracking**: Remote branch relationship
- **Commit status**: Ahead/behind remote
- **Working tree**: Clean or modified state
- **Stash status**: Stashed changes present

### PIB State Information
- **Context isolation**: State directory health
- **Evolution chains**: Active context evolution
- **Tool selections**: JIT tool selection cache
- **MCP conversations**: Active conversation states

### File System Information
- **Directory existence**: Worktree path validation
- **Disk usage**: Worktree size information
- **Last access**: File system activity
- **Permissions**: Access and execution rights

## Integration with Other Commands

### Starting New Features
```bash
start -> /workflows:feature-start-list                    # See current worktrees
start -> /workflows:feature-start-start new-feature      # Start additional feature
start -> /workflows:feature-start-list                   # Verify new worktree created
```

### Managing Active Features
```bash
start -> /workflows:feature-start-list detailed          # Check status of all features
cd ../pib-feature-name         # Switch to specific feature
start -> /workflows:feature-start-merge                  # Merge when ready
start -> /workflows:feature-start-list                   # Verify merge completed
```

### Cleanup Workflow
```bash
start -> /workflows:feature-start-list                   # Identify completed features
start -> /workflows:feature-start-cleanup old-feature    # Remove completed worktree
start -> /workflows:feature-start-list                   # Confirm cleanup
```

## Troubleshooting

### "No active worktrees found"
- This is normal if no features are in development
- Use `start -> /workflows:feature-start-start <name>` to create your first worktree
- Check if you're in the correct main repository

### Missing Worktrees in List
- Worktree directory may have been manually deleted
- Use `start -> /workflows:feature-start-cleanup` to clean orphaned references
- Check Git worktree list: `git worktree list`

### State Health Warnings
- Run state validation: `/.claude/hooks/state-isolator.sh validate`
- Check state backup availability
- Consider re-initializing state if corrupted

## Advanced Usage

### Filtering and Sorting
While not built-in, you can combine with shell tools:
```bash
# Show only modified worktrees
start -> /workflows:feature-start-list | grep Modified

# Count active worktrees  
start -> /workflows:feature-start-list json | jq '.active_worktrees | length'

# List worktrees by activity
start -> /workflows:feature-start-list detailed | grep "Last Activity"
```

### Integration with Scripts
```bash
#!/bin/bash
# Example: Check for stale worktrees

worktrees=$(start -> /workflows:feature-start-list json)
stale_count=$(echo "$worktrees" | jq '[.active_worktrees[] | select(.last_activity | contains("day"))] | length')

if [ "$stale_count" -gt 0 ]; then
    echo "Found $stale_count stale worktree(s)"
    echo "Consider cleaning up inactive features"
fi
```

### Monitoring Workflow
```bash
# Check status before starting work
start -> /workflows:feature-start-list detailed

# Quick status check
start -> /workflows:feature-start-list | grep -E "Modified|Conflict"

# Daily cleanup check
start -> /workflows:feature-start-list detailed | grep -E "day|week"
```

## Performance Considerations

### Large Numbers of Worktrees
- Command remains fast with dozens of worktrees
- JSON format is most efficient for many worktrees
- Consider cleanup of completed features regularly

### Network Operations
- No network calls required for listing
- Local Git and file system operations only
- Works offline and with private repositories

### Resource Usage
- Minimal memory footprint
- Fast execution (< 1 second typically)
- Scales well with project size

## Related Commands
- `start -> /workflows:feature-start-start` - Create new feature worktree
- `start -> /workflows:feature-start-merge` - Integrate feature changes
- `start -> /workflows:feature-start-cleanup` - Remove completed worktrees

## Implementation Notes

This command uses:
- Git worktree list for active worktree detection
- File system checks for directory validation
- PIB state inspection for health reporting
- JSON processing for structured output

---
**Note**: This command provides comprehensive visibility into your parallel development workflow and integrates with the PIB Context Engineering system for enhanced project management.