# Switch PIB Mode

Switch between development mode (fast) and full mode (all hooks).

## Usage

```bash
# Check current mode
.claude/hooksagent -> /core:switch-agent-mode.sh

# Switch to development mode (fast, no auto-lint)
.claude/hooksagent -> /core:switch-agent-mode.sh dev

# Switch to full mode (all hooks, workflow tracking)
.claude/hooksagent -> /core:switch-agent-mode.sh full
```

## Modes

### Development Mode
- ⚡ Fast coding experience
- ✅ LEVER principles still shown
- ✅ Basic notifications
- ❌ No automatic lint (use manual: `.claude/hooks/manual-lint.sh`)
- ❌ No workflow status display

### Full Mode  
- 🔧 Complete workflow tracking
- ✅ All hooks active
- ✅ Automatic lint after edits
- ✅ Workflow status display
- ⚠️ Slower due to lint operations

## Manual Lint
When in dev mode, run lint manually:
```bash
.claude/hooks/manual-lint.sh
```

**Note:** Restart Claude Code after switching modes.