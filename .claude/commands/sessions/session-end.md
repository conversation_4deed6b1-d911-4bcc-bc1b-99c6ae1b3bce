End the current development session by:

1. Check `.claudestart -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-starts/.current-session` for the active session
2. If no active session, inform user there's nothing to end
3. If session exists, append a comprehensive summary including:
   - Session duration
   - Git summary:
     * Total files changed (added/modified/deleted)
     * List all changed files with change type
     * Number of commits made (if any)
     * Final git status
   - Todo summary:
     * Total tasks completed/remaining
     * List all completed tasks
     * List any incomplete tasks with status
   - Key accomplishments
   - All features implemented
   - Problems encountered and solutions
   - Breaking changes or important findings
   - Dependencies added/removed
   - Configuration changes
   - Deployment steps taken
   - Lessons learned
   - What wasn't completed
   - Tips for future developers

4. **AUTO-COMMIT SESSION WORK** (if user has uncommitted changes):
   - Run `git add .` to stage all changes
   - Create commit with message: "feat: [session-name] - [key accomplishments summary]"
   - Include session documentation in commit body
   - Append "🤖 Generated with [<PERSON>](https://claude.ai/code)" signature
   - **Ask user if they want to push to remote** - don't auto-push without permission

5. **TRIGGER FINAL KNOWLEDGE UPDATE**:
   - Automatically run knowledge update to capture session learnings
   - Ensure all agents have access to session insights for future work

6. Empty the `.claudestart -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-starts/.current-session` file (don't remove it, just clear its contents)
7. Inform user the session has been documented and committed

The summary should be thorough enough that another developer (or AI) can understand everything that happened without reading the entire session.