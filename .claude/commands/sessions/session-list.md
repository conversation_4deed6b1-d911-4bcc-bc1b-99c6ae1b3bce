List all development sessions by:

1. Check if `.claudestart -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-starts/` directory exists
2. List all `.md` files (excluding hidden files and `.current-session`)
3. For each session file:
   - Show the filename
   - Extract and show the session title
   - Show the date/time
   - Show first few lines of the overview if available
4. If `.claudestart -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-starts/.current-session` exists, highlight which session is currently active
5. Sort by most recent first

Present in a clean, readable format.