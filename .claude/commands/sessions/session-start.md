Start a new development session by creating a session file in `.claudestart -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-starts/` with the format `YYYY-MM-DD-HHMM-$ARGUMENTS.md` (or just `YYYY-MM-DD-HHMM.md` if no name provided).

The session file should begin with:
1. Session name and timestamp as the title
2. Session overview section with start time
3. Goals section (ask user for goals if not clear)
4. Empty progress section ready for updates

After creating the file, create or update `.claudestart -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-starts/.current-session` to track the active session filename.

Confirm the session has started and remind the user they can:
- Update it with `init -> /workflows:project-init:session-update`
- End it with `init -> /workflows:project-init:session-end`

MUST read (project-root)/pib-agent/ide-pib-orchestrator.md on startup
When done reading, respond with "I am ready to work" and nothing else.