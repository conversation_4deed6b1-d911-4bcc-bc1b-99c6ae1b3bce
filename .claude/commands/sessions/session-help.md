Show help for the session management system:

## Session Management Commands

The session system helps document development work for future reference.

### Available Commands:

- `init -> /workflows:project-init:session-start [name]` - Start a new session with optional name
- `init -> /workflows:project-init:session-update [notes]` - Add notes to current session  
- `init -> /workflows:project-init:session-end` - End session with comprehensive summary
- `init -> /workflows:project-init:session-list` - List all session files
- `init -> /workflows:project-init:session-current` - Show current session status
- `init -> /workflows:project-init:session-help` - Show this help

### How It Works:

1. Sessions are markdown files in `.claudestart -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-starts/`
2. Files use `YYYY-MM-DD-HHMM-name.md` format
3. Only one session can be active at a time
4. Sessions track progress, issues, solutions, and learnings

### Best Practices:

- Start a session when beginning significant work
- Update regularly with important changes or findings
- End with thorough summary for future reference
- Review past sessions before starting similar work

### Example Workflow:

```
init -> /workflows:project-init:session-start refactor-auth
init -> /workflows:project-init:session-update Added Google OAuth restriction
init -> /workflows:project-init:session-update Fixed Next.js 15 params Promise issue  
init -> /workflows:project-init:session-end
```