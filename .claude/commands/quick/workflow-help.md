# Quick Workflow Help

## Description
Provides instant workflow guidance based on what you want to accomplish.

## Usage
```
help -> /quick:workflow-help-help
```

## Execution
Display workflow decision tree and commands for common scenarios:

```
🎯 PIB-METHOD Quick Workflow Guide

What do you want to do?

🆕 NEW PROJECT
Command: init -> help -> /quick:workflow-helps:project-init-init
What: Complete guided workflow from conception to deployment

🔧 ADD FEATURE
Command: dev -> help -> /quick:workflow-helps:module-dev-dev
What: Add new functionality to existing project

🚨 FIX SOMETHING BROKEN
Command: fix -> help -> /quick:workflow-helps:legacy-fix-fix
What: Systematic approach to fixing issues

🔍 UNDERSTAND CODEBASE
Command: > /analysis:analyze
What: Comprehensive code analysis

🐛 DEBUG A PROBLEM
Command: > /analysis:debug
What: Systematic debugging workflow

👥 PARALLEL DEVELOPMENT
Command: start -> help -> /quick:workflow-helps:feature-start-start <name>
What: Multiple people working on different features

🔒 SECURITY REVIEW
Command: > /analysis:secaudit
What: Comprehensive security assessment

📝 CODE REVIEW
Command: > /analysis:codereview
What: Quality and compliance review

🧪 GENERATE TESTS
Command: > /analysis:testgen
What: Create comprehensive test suites

📋 FULL COMMAND LIST
Command: > /core:help
What: See all available commands

🤖 SWITCH PERSONAS
Command: > /core:agents (list) or agent -> /core:switch-agent-agent <name>
What: Change to specialized agent for specific tasks

💡 LEVER CHECK
Command: check -> /core:lever-check-check
What: Validate you're following LEVER principles

❓ WHEN STUCK
1. > /core:help - Find the right command
2. > /core:agents - See available specialists
3. agent -> /core:switch-agent-agent pib-orchestrator - Get guidance
```

## Parameters
None - displays the quick reference guide.

## Output
Interactive decision tree showing the most appropriate workflow and commands for different scenarios.