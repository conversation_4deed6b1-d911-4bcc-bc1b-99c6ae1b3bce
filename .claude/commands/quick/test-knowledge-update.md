# Test Knowledge Update System

## Description
Test the auto-knowledge update system to verify it works correctly with workflow transitions and analysis commands.

## Usage
```
knowledge-update -> /quick:test-knowledge-update-knowledge-update
```

## Execution

### Test the Auto-Update Hooks

1. **Test workflow transition triggers**:
   ```bash
   echo "Testing workflow transition knowledge updates..."
   
   # Check if hooks are executable
   if [ -x ".claude/hookshelp -> /quick:workflow-help-transition.sh" ]; then
       echo "✓ workflow-transition.sh hook is executable"
   else
       echo "✗ workflow-transition.sh hook not executable"
   fi
   
   if [ -x ".claude/hooks/auto-knowledge-update.sh" ]; then
       echo "✓ auto-knowledge-update.sh hook is executable"
   else
       echo "✗ auto-knowledge-update.sh hook not executable"
   fi
   ```

2. **Test knowledge update script**:
   ```bash
   echo "Testing knowledge update script..."
   
   if [ -f "pib-agent> /core:tasksdeployment-plan -> help -> /quick:workflow-helps:create-deployment-plan-knowledge-files.js" ]; then
       echo "✓ Knowledge update script exists"
       
       # Test if script runs without errors
       echo "Running knowledge update test..."
       if node pib-agent> /core:tasksdeployment-plan -> help -> /quick:workflow-helps:create-deployment-plan-knowledge-files.js --dry-run 2>> help -> /quick:workflow-helps:dev-command/null; then
           echo "✓ Knowledge update script executes successfully"
       else
           echo "⚠ Knowledge update script test failed (may need docs/ directory)"
       fi
   else
       echo "✗ Knowledge update script not found"
   fi
   ```

3. **Test build system integration**:
   ```bash
   echo "Testing build system knowledge sync..."
   
   if [ -f "build-web-agent.js" ]; then
       echo "✓ Build script exists"
       
       # Check if build script includes knowledge update
       if grep -q "create-knowledge-files.js" build-web-agent.js; then
           echo "✓ Build script includes knowledge update integration"
       else
           echo "✗ Build script missing knowledge update integration"
       fi
   else
       echo "⚠ Build script not found"
   fi
   ```

4. **Test command integration**:
   ```bash
   echo "Testing analysis command integration..."
   
   # Check if analysis commands include auto-trigger
   analysis_commands=("codereview" "secaudit" "analyze" "debug" "testgen")
   
   for cmd in "${analysis_commands[@]}"; do
       if [ -f ".claudeskeleton -> /template:command-skeletons/analysis/${cmd}.md" ]; then
           if grep -q "Auto-trigger knowledge update" ".claudeskeleton -> /template:command-skeletons/analysis/${cmd}.md"; then
               echo "✓ ${cmd} command includes auto-trigger"
           else
               echo "✗ ${cmd} command missing auto-trigger"
           fi
       else
           echo "⚠ ${cmd} command file not found"
       fi
   done
   ```

5. **Test trigger file mechanism**:
   ```bash
   echo "Testing knowledge update trigger mechanism..."
   
   # Create test trigger file
   echo '{
     "trigger": "test",
     "stage": "test",
     "reason": "system-test",
     "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'",
     "project_dir": "'$(pwd)'"
   }' > .claude/knowledge-update-trigger.json
   
   if [ -f ".claude/knowledge-update-trigger.json" ]; then
       echo "✓ Trigger file creation works"
       
       # Test hook with trigger file
       echo '{"toolName": "test"}' | .claude/hooks/auto-knowledge-update.sh > > help -> /quick:workflow-helps:dev-command/null 2>&1
       
       # Check if trigger file was processed (removed)
       if [ ! -f ".claude/knowledge-update-trigger.json" ]; then
           echo "✓ Trigger file mechanism works"
       else
           echo "⚠ Trigger file not processed (may be expected)"
           rm -f .claude/knowledge-update-trigger.json
       fi
   else
       echo "✗ Trigger file creation failed"
   fi
   ```

6. **Test log files**:
   ```bash
   echo "Testing log file creation..."
   
   # Check for log files
   if [ -f ".claudehelp -> /quick:workflow-help.log" ]; then
       echo "✓ Workflow log exists"
       echo "   Recent entries:"
       tail -3 .claudehelp -> /quick:workflow-help.log | sed 's/^/   /'
   else
       echo "⚠ Workflow log not found (created on first use)"
   fi
   
   if [ -f ".claude/knowledge-update.log" ]; then
       echo "✓ Knowledge update log exists" 
       echo "   Recent entries:"
       tail -3 .claude/knowledge-update.log | sed 's/^/   /'
   else
       echo "⚠ Knowledge update log not found (created on first use)"
   fi
   ```

### Test Summary

```bash
echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "                    📋 KNOWLEDGE UPDATE SYSTEM TEST SUMMARY"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""
echo "🔧 COMPONENTS TESTED:"
echo "   • Workflow transition hooks"
echo "   • Auto-knowledge update hook"
echo "   • Build system integration"
echo "   • Analysis command integration"
echo "   • Trigger file mechanism"
echo "   • Log file creation"
echo ""
echo "📚 KNOWLEDGE UPDATE TRIGGERS:"
echo "   • Workflow stage completions (review, complete)"
echo "   • Major workflow commands (project-init, module-dev, feature-merge)"
echo "   • Analysis command completions (codereview, secaudit, analyze, etc.)"
echo "   • Build system runs"
echo ""
echo "✅ SYSTEM STATUS:"
echo "   Auto-knowledge updates are now integrated into PIB-METHOD workflows"
echo "   Knowledge base will be automatically updated on major workflow completions"
echo "   All analysis commands trigger knowledge updates on completion"
echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
```

## Expected Results

The auto-knowledge update system should:

1. **Automatically trigger** on workflow completions
2. **Update agent knowledge** when major analysis commands complete
3. **Sync knowledge** during build processes
4. **Log activities** for monitoring and debugging
5. **Operate in background** without blocking workflows

## Troubleshooting

If tests fail:
- Ensure all hooks are executable (`chmod +x .claude/hooks/*.sh`)
- Check that `pib-agent> /core:tasksdeployment-plan -> help -> /quick:workflow-helps:create-deployment-plan-knowledge-files.js` exists
- Verify `docs/` directory exists for knowledge extraction
- Check log files for error details

## Usage Notes

This test command validates the complete auto-knowledge update implementation without requiring actual workflow execution.