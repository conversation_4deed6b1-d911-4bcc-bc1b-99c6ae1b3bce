# PIB Method Claude Commands

This directory contains Claude Code custom commands for the PIB Method workflow system.

## Directory Structure

```
.claudeskeleton -> /template:command-skeletons/
├── core/              # Core orchestrator commands
│   ├── help.md        # Display PIB command reference
│   ├── agents.md      # List available agent personas
│   ├── switch-agent.md # Switch between agents
│   └── tasks.md       # List available tasks
├── agents/            # Agent-specific commands  
│   ├── analyst-brief.md    # Create project brief
│   ├── pm-prd.md          # Create PRD
│   └── architect-design.md # Create architecture
├── workflows/         # Multi-step workflows
│   ├── project-init.md        # Complete project initialization
│   ├── module-dev.md          # Module development workflow
│   ├── legacy-fix.md          # Legacy module remediation
│   ├── create-epic.md         # Create epics with template
│   └── epic-to-stories.md     # Generate stories from epics
├── knowledge/         # Knowledge management
│   ├── update-knowledge.md    # Update agent knowledge
│   └── memory-extract.md      # Extract conversation insights
└── quick/             # Quick utility commands
    └── doc-out.md     # Output full documents
```

## Document Organization Standards

All commands follow these file organization rules:
- **PRDs**: `/docs/prd.md`
- **Epics**: `/docs/epics/[epic-name]-epic.md`
- **Stories**: `/docs/stories/[epic].[story-num].[title].md`
- **Architecture**: `/docs/architecture.md`
- **Module PRDs**: `/docs/modules/[module]/module-prd.md`

## Command Categories

### Core Commands (`/core/`)
Essential commands for navigating and managing the PIB Method:
- `> /core:help` - Display comprehensive command reference
- `> /core:agents` - List all available agent personas
- `agent -> /core:switch-agent-agent` - Switch to specific agent context
- `> /core:tasks` - Show available tasks for current agent

### Agent Commands (`> /core:agents/`)
Direct agent task execution:
- `> /core:agents:analyst-brief` - Create project brief through research
- `> /core:agents:pm-prd` - Create Product Requirements Document
- `> /core:agents:architect-design` - Design system architecture

### Workflow Commands (`help -> /quick:workflow-helps/`)
Complete multi-step workflows:
- `init -> help -> /quick:workflow-helps:project-init-init` - Full project initialization from brief to deployment
- `dev -> help -> /quick:workflow-helps:module-dev-dev` - Add new modules to existing projects
- `fix -> help -> /quick:workflow-helps:legacy-fix-fix` - Remediate problematic existing modules
- `epic -> /workflows:create-epic-epic` - Create new epic with goals and requirements
- `to-stories -> /workflows:epic-to-stories` - Generate stories from epic files
- `start -> help -> /quick:workflow-helps:feature-start-start` - Start new feature with worktree

### Knowledge Commands (`/knowledge/`)
Knowledge and memory management:
- `knowledge -> /knowledge:update-knowledge-knowledge` - Synchronize agent knowledge base
- `extract -> /knowledge:memory-extract-extract` - Extract insights from conversations
- `organize -> /knowledge:organize-docs-organize` - Organize /docs folder to PIB standards
- `verify -> /knowledge:verify-access-verify` - Verify agents have proper document access

### Quick Commands (`/quick/`)
Utility commands for common tasks:
- `out -> /quick:doc-out-out` - Display full document contents without truncation

## Usage Examples

### Starting a New Project
```bash
init -> help -> /quick:workflow-helps:project-init-init
```

### Adding a New Module
```bash
dev -> help -> /quick:workflow-helps:module-dev-dev user-authentication
```

### Fixing Legacy Code
```bash
fix -> help -> /quick:workflow-helps:legacy-fix-fix billing-system
```

### Switching Contexts
```bash
agent -> /core:switch-agent-agent analyst
> /core:agents:analyst-brief
agent -> /core:switch-agent-agent pm
> /core:agents:pm-prd
```

## Integration with PIB System

These commands integrate seamlessly with the existing PIB Method files:

- **Personas**: Load from `pib-core/agents/core/`
- **Tasks**: Execute from `pib-agent> /core:tasks/`
- **Templates**: Use from `pib-agent/templates/`
- **Checklists**: Apply from `pib-agentmode -> /quick:check-modelists/`
- **Knowledge**: Read/write to `.ai/` directory

## Command Development

Each command file follows this structure:
- **Purpose**: Clear description of command function
- **Usage**: Command syntax and options
- **Implementation**: Step-by-step execution details
- **Integration**: How it connects to PIB system
- **Quality Checks**: Validation and verification steps
- **Related Commands**: Cross-references to other commands

## Benefits

- **Native Integration**: Works directly with Claude Code
- **Streamlined Workflows**: Reduces complex multi-step processes
- **Project Context**: Maintains context across agent switches
- **Quality Assurance**: Built-in checklists and validation
- **Discoverability**: Easy-to-find `skeleton -> /template:command-skeleton` syntax

## Getting Started

1. Start with `> /core:help` to see all available commands
2. Use `> /core:agents` to understand available personas
3. Execute `init -> help -> /quick:workflow-helps:project-init-init` for new projects
4. Use `agent -> /core:switch-agent-agent` to work with different specialists
5. Run `knowledge -> /knowledge:update-knowledge-knowledge` after major project phases

For detailed information about the PIB Method, see `pib-agentskeleton -> /template:command-skeletons.md`.