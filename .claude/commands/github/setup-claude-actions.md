# Claude G<PERSON>Hub Actions Setup

Automated setup for Claude Code GitHub Actions integration, enabling @claude mentions in issues and pull requests.

## Usage
```bash
claude-actions -> /github:setup-claude-actions-claude-actions [--repo repository] [--verify] [--update]
```

## Examples
```bash
# Setup Claude Actions in current repository
claude-actions -> /github:setup-claude-actions-claude-actions

# Setup in specific repository
claude-actions -> /github:setup-claude-actions-claude-actions --repo username/repository-name

# Verify existing setup
claude-actions -> /github:setup-claude-actions-claude-actions --verify

# Update existing configuration
claude-actions -> /github:setup-claude-actions-claude-actions --update
```

## Setup Process

### 1. Prerequisites Check
- Verifies GitHub CLI is installed and authenticated
- Checks repository access permissions
- Validates Anthropic API key availability
- Confirms repository structure

### 2. GitHub App Installation
- Guides through <PERSON> App installation
- Handles app permissions and authorization
- Configures repository-specific settings
- Validates app installation success

### 3. API Key Configuration
```bash
# Securely add Anthropic API key to repository secrets
gh secret set ANTHROPIC_API_KEY --body "your-api-key-here"

# Alternative: OAuth token method
gh secret set CLAUDE_CODE_OAUTH_TOKEN --body "your-oauth-token"
```

### 4. Workflow File Creation
Creates `.githubhelp -> /quick:workflow-helps/claude-assistant.yml` with:
- Trigger events (issue comments, PR comments, reviews)
- Claude Code action configuration
- PIB-METHOD specific settings
- LEVER framework integration

### 5. Configuration Validation
- Tests @claude mention triggers
- Verifies GitHub Actions execution
- Confirms API connectivity
- Validates permissions and access

## Generated Workflow Configuration

### Standard Configuration (`claude-assistant.yml`)
```yaml
name: Claude Code Assistant
on:
  issue_comment:
    types: [created]
  pull_request_review_comment:
    types: [created]
  pull_request_review:
    types: [submitted]

jobs:
  claude-response:
    if: contains(github.event.comment.body, '@claude') || contains(github.event.review.body, '@claude')
    runs-on: ubuntu-latest
    steps:
      - name: Claude Code Action
        uses: anthropics/claude-code-action@beta
        with:
          anthropic_api_key: ${{ secrets.ANTHROPIC_API_KEY }}
          github_token: ${{ secrets.GITHUB_TOKEN }}
          trigger_phrase: "@claude"
          pib_method_enabled: true
          lever_compliance: true
```

### PIB-METHOD Enhancements
- **LEVER Framework**: Automatic compliance checking
- **Pattern Recognition**: Uses existing codebase patterns
- **Quality Gates**: Enforces PIB coding standards
- **Context Integration**: Includes CLAUDE.md instructions
- **MCP Tools**: Enables Context7, Perplexity integration

## Setup Options

### Basic Setup
```bash
# Minimal configuration for small projects
claude-actions -> /github:setup-claude-actions-claude-actions --basic
```

### Advanced Setup
```bash
# Full PIB-METHOD integration with all features
claude-actions -> /github:setup-claude-actions-claude-actions --advanced --mcp-tools --lever-scoring
```

### Enterprise Setup
```bash
# Enhanced security and compliance features
claude-actions -> /github:setup-claude-actions-claude-actions --enterprise --audit-logging --approval-required
```

## API Key Management

### Secure Key Storage
1. **Never commit keys to repository**
2. **Use GitHub Secrets exclusively**
3. **Rotate keys regularly**
4. **Monitor usage and costs**

### Key Sources
- **Direct Anthropic API**: `ANTHROPIC_API_KEY`
- **Claude Code OAuth**: `CLAUDE_CODE_OAUTH_TOKEN`
- **AWS Bedrock**: Alternative API endpoint
- **Google Vertex AI**: Alternative API endpoint

## Configuration Validation

### Automatic Tests
The setup process runs validation tests:
```bash
# Test @claude mention trigger
echo "Testing @claude hello" | gh issue comment create 123

# Verify workflow execution
gh run list --workflow=claude-assistant.yml

# Check API connectivity
curl -H "Authorization: Bearer $ANTHROPIC_API_KEY" https://api.anthropic.com/v1/messages
```

### Manual Verification
1. Create test issue with @claude mention
2. Monitor GitHub Actions tab for workflow execution
3. Confirm Claude responds appropriately
4. Verify LEVER compliance integration

## Troubleshooting

### Common Setup Issues

#### GitHub App Not Installed
```bash
# Error: Claude GitHub App not found
# Solution: Install from GitHub Marketplace
gh browse --repo username/repository-name/settings/installations
```

#### API Key Issues
```bash
# Error: Invalid or missing API key
# Solution: Verify key format and permissions
claude-actions -> /github:setup-claude-actions-claude-actions --verify-key
```

#### Workflow Permissions
```bash
# Error: GitHub Actions permissions denied
# Solution: Enable Actions in repository settings
gh api repos/username/repository-name --method PATCH --field has_actions=true
```

#### Trigger Not Working
```bash
# Error: @claude mentions not triggering workflow
# Solution: Check workflow file syntax and triggers
claude-actions -> /github:setup-claude-actions-claude-actions --debug-triggers
```

### Debug Mode
```bash
# Enable detailed logging for troubleshooting
claude-actions -> /github:setup-claude-actions-claude-actions --debug --verbose
```

## Integration with PIB-METHOD

### Automatic Sync
The setup integrates with PIB-METHOD sync system:
- Workflow files are synced to all projects
- Configuration templates are distributed
- Updates are propagated automatically

### LEVER Framework Integration
Claude Actions automatically include:
- **L**everage: Pattern recognition from codebase
- **E**xtend: Preference for extending existing code
- **V**erify: Comprehensive test generation
- **E**liminate: Duplication detection and removal
- **R**educe: Complexity minimization

### MCP Tools Integration
Enables Claude to use:
- **Context7**: Library documentation lookup
- **Perplexity**: Web research capabilities
- **GitHub CI**: Repository status monitoring
- **Custom Tools**: Project-specific integrations

## Security Considerations

### Access Control
- Repository-level permissions enforced
- Secret access limited to authorized workflows
- Rate limiting to prevent abuse
- Audit logging for compliance

### Best Practices
- Regular API key rotation
- Monitor usage and costs
- Review Claude responses before merging
- Maintain human oversight of all changes

## Related Commands
- `issue -> /github:ai-issue-issue` - Create issues with Claude assignment
- `issue -> /github:ai-issue-assign` - Assign existing items to Claude
- `issue -> /github:ai-issue-status` - Monitor Claude work progress
- `issue -> /github:ai-issue-pr` - Add Claude comments to PRs

## Support and Updates
- Configuration updates automatically synced via PIB-METHOD
- Issues tracked in PIB-METHOD repository
- Documentation maintained in `.github/` directory
- Community support through GitHub Discussions