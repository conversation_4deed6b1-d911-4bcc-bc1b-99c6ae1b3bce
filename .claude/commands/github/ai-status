#!/bin/bash

# AI Work Status Monitor
# Monitor the progress and status of AI assistants (<PERSON><PERSON><PERSON> and <PERSON>) working on GitHub issues and pull requests

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Check prerequisites
check_prerequisites() {
    # Check if gh CLI is installed
    if ! command -v gh &> /dev/null; then
        print_status $RED "Error: GitHub CLI (gh) is not installed"
        print_status $YELLOW "Install with: brew install gh"
        exit 1
    fi
    
    # Check if gh is authenticated
    if ! gh auth status &> /dev/null; then
        print_status $RED "Error: GitHub CLI is not authenticated"
        print_status $YELLOW "Run: gh auth login"
        exit 1
    fi
    
    # Check if we're in a git repository
    if ! git rev-parse --is-inside-work-tree &> /dev/null; then
        print_status $RED "Error: Not in a git repository"
        exit 1
    fi
    
    # Check if jq is available for JSON parsing
    if ! command -v jq &> /dev/null; then
        print_status $YELLOW "Warning: jq not installed - output will be less formatted"
        print_status $YELLOW "Install with: brew install jq"
        USE_JQ=false
    else
        USE_JQ=true
    fi
}

# Parse command line arguments
parse_arguments() {
    AI_FILTER=""
    OUTPUT_FORMAT="table"
    REPOSITORY=""
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            copilot|claude)
                AI_FILTER="$1"
                shift
                ;;
            --repo)
                REPOSITORY="$2"
                shift 2
                ;;
            --format)
                OUTPUT_FORMAT="$2"
                shift 2
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                AI_FILTER="$1"
                shift
                ;;
        esac
    done
    
    # Validate format
    if [ "$OUTPUT_FORMAT" != "table" ] && [ "$OUTPUT_FORMAT" != "json" ] && [ "$OUTPUT_FORMAT" != "summary" ]; then
        print_status $RED "Error: Format must be 'table', 'json', or 'summary'"
        exit 1
    fi
}

show_help() {
    echo "Usage: /ai-status [ai] [--repo repository] [--format table|json|summary]"
    echo
    echo "Examples:"
    echo "  /ai-status                    # Show all AI work"
    echo "  /ai-status copilot           # Show only Copilot work"
    echo "  /ai-status claude            # Show only Claude work"
    echo "  /ai-status --format summary  # Show summary view"
}

# Get status emoji based on state
get_status_emoji() {
    local status="$1"
    case "$status" in
        "OPEN"|"open")
            echo "⏳"
            ;;
        "DRAFT"|"draft")
            echo "🔄"
            ;;
        "READY_FOR_REVIEW")
            echo "👀"
            ;;
        "MERGED"|"merged")
            echo "✅"
            ;;
        "CLOSED"|"closed")
            echo "❌"
            ;;
        *)
            echo "🔍"
            ;;
    esac
}

# Get relative time
get_relative_time() {
    local timestamp="$1"
    local now=$(date +%s)
    local then=$(date -d "$timestamp" +%s 2>/dev/null || date -j -f "%Y-%m-%dT%H:%M:%SZ" "$timestamp" +%s 2>/dev/null || echo "$now")
    local diff=$((now - then))
    
    if [ $diff -lt 60 ]; then
        echo "${diff}s ago"
    elif [ $diff -lt 3600 ]; then
        echo "$((diff / 60))m ago"
    elif [ $diff -lt 86400 ]; then
        echo "$((diff / 3600))h ago"
    else
        echo "$((diff / 86400))d ago"
    fi
}

# Check for Copilot work
get_copilot_status() {
    local copilot_issues=""
    local copilot_prs=""
    
    # Get issues assigned to copilot
    if [ "$USE_JQ" = true ]; then
        copilot_issues=$(gh issue list --assignee "copilot" --json number,title,state,updatedAt,url 2>/dev/null || echo "[]")
    else
        copilot_issues=$(gh issue list --assignee "copilot" 2>/dev/null || echo "")
    fi
    
    # Get PRs created by copilot
    if [ "$USE_JQ" = true ]; then
        copilot_prs=$(gh pr list --author "app/copilot" --json number,title,state,updatedAt,url,isDraft 2>/dev/null || echo "[]")
    else
        copilot_prs=$(gh pr list --author "app/copilot" 2>/dev/null || echo "")
    fi
    
    echo "$copilot_issues|$copilot_prs"
}

# Check for Claude work (GitHub Actions based)
get_claude_status() {
    local claude_issues=""
    local claude_prs=""
    
    # Look for issues with @claude mentions in recent comments
    if [ "$USE_JQ" = true ]; then
        # Get recent issues and check for @claude mentions
        local issues=$(gh issue list --limit 20 --json number,title,state,updatedAt,url 2>/dev/null || echo "[]")
        claude_issues="[]"
        
        # Check each issue for @claude mentions (simplified approach)
        while IFS= read -r issue_number; do
            if [ -n "$issue_number" ] && [ "$issue_number" != "null" ]; then
                local comments=$(gh issue view "$issue_number" --json comments --jq '.comments[].body' 2>/dev/null || echo "")
                if echo "$comments" | grep -q "@claude"; then
                    local issue_info=$(echo "$issues" | jq ".[] | select(.number == $issue_number)")
                    if [ "$claude_issues" = "[]" ]; then
                        claude_issues="[$issue_info]"
                    else
                        claude_issues=$(echo "$claude_issues" | jq ". + [$issue_info]")
                    fi
                fi
            fi
        done <<< "$(echo "$issues" | jq -r '.[].number' 2>/dev/null)"
        
        # Similar approach for PRs
        local prs=$(gh pr list --limit 20 --json number,title,state,updatedAt,url,isDraft 2>/dev/null || echo "[]")
        claude_prs="[]"
        
        while IFS= read -r pr_number; do
            if [ -n "$pr_number" ] && [ "$pr_number" != "null" ]; then
                local comments=$(gh pr view "$pr_number" --json comments --jq '.comments[].body' 2>/dev/null || echo "")
                if echo "$comments" | grep -q "@claude"; then
                    local pr_info=$(echo "$prs" | jq ".[] | select(.number == $pr_number)")
                    if [ "$claude_prs" = "[]" ]; then
                        claude_prs="[$pr_info]"
                    else
                        claude_prs=$(echo "$claude_prs" | jq ". + [$pr_info]")
                    fi
                fi
            fi
        done <<< "$(echo "$prs" | jq -r '.[].number' 2>/dev/null)"
    else
        claude_issues=""
        claude_prs=""
    fi
    
    echo "$claude_issues|$claude_prs"
}

# Display table format
display_table() {
    local copilot_data="$1"
    local claude_data="$2"
    
    print_status $GREEN "PIB-METHOD AI Work Status"
    print_status $GREEN "========================="
    echo
    
    printf "%-8s %-6s %-4s %-30s %-12s %-10s\n" "AI" "Type" "#" "Title" "Status" "Updated"
    printf "%-8s %-6s %-4s %-30s %-12s %-10s\n" "--------" "------" "----" "------------------------------" "------------" "----------"
    
    local total_active=0
    
    # Process Copilot data
    if [ "$AI_FILTER" = "" ] || [ "$AI_FILTER" = "copilot" ]; then
        local copilot_issues=$(echo "$copilot_data" | cut -d'|' -f1)
        local copilot_prs=$(echo "$copilot_data" | cut -d'|' -f2)
        
        if [ "$USE_JQ" = true ] && [ "$copilot_issues" != "[]" ]; then
            echo "$copilot_issues" | jq -r '.[] | [.number, .title, .state, .updatedAt] | @tsv' | while IFS=$'\t' read -r number title state updated; do
                local emoji=$(get_status_emoji "$state")
                local rel_time=$(get_relative_time "$updated")
                local short_title=$(echo "$title" | cut -c1-28)
                printf "%-8s %-6s %-4s %-30s %-12s %-10s\n" "copilot" "issue" "$number" "$short_title" "$emoji $state" "$rel_time"
                total_active=$((total_active + 1))
            done
        fi
        
        if [ "$USE_JQ" = true ] && [ "$copilot_prs" != "[]" ]; then
            echo "$copilot_prs" | jq -r '.[] | [.number, .title, .state, .updatedAt, .isDraft] | @tsv' | while IFS=$'\t' read -r number title state updated draft; do
                local status="$state"
                if [ "$draft" = "true" ]; then
                    status="DRAFT"
                fi
                local emoji=$(get_status_emoji "$status")
                local rel_time=$(get_relative_time "$updated")
                local short_title=$(echo "$title" | cut -c1-28)
                printf "%-8s %-6s %-4s %-30s %-12s %-10s\n" "copilot" "pr" "$number" "$short_title" "$emoji $status" "$rel_time"
                total_active=$((total_active + 1))
            done
        fi
    fi
    
    # Process Claude data
    if [ "$AI_FILTER" = "" ] || [ "$AI_FILTER" = "claude" ]; then
        local claude_issues=$(echo "$claude_data" | cut -d'|' -f1)
        local claude_prs=$(echo "$claude_data" | cut -d'|' -f2)
        
        if [ "$USE_JQ" = true ] && [ "$claude_issues" != "[]" ]; then
            echo "$claude_issues" | jq -r '.[] | [.number, .title, .state, .updatedAt] | @tsv' | while IFS=$'\t' read -r number title state updated; do
                local emoji=$(get_status_emoji "$state")
                local rel_time=$(get_relative_time "$updated")
                local short_title=$(echo "$title" | cut -c1-28)
                printf "%-8s %-6s %-4s %-30s %-12s %-10s\n" "claude" "issue" "$number" "$short_title" "$emoji $state" "$rel_time"
                total_active=$((total_active + 1))
            done
        fi
        
        if [ "$USE_JQ" = true ] && [ "$claude_prs" != "[]" ]; then
            echo "$claude_prs" | jq -r '.[] | [.number, .title, .state, .updatedAt, .isDraft] | @tsv' | while IFS=$'\t' read -r number title state updated draft; do
                local status="$state"
                if [ "$draft" = "true" ]; then
                    status="DRAFT"
                fi
                local emoji=$(get_status_emoji "$status")
                local rel_time=$(get_relative_time "$updated")
                local short_title=$(echo "$title" | cut -c1-28)
                printf "%-8s %-6s %-4s %-30s %-12s %-10s\n" "claude" "pr" "$number" "$short_title" "$emoji $status" "$rel_time"
                total_active=$((total_active + 1))
            done
        fi
    fi
    
    echo
    if [ $total_active -eq 0 ]; then
        print_status $YELLOW "No active AI work found"
        print_status $BLUE "Use '/ai-issue' or '/ai-assign' to create new AI assignments"
    else
        print_status $GREEN "Total active items: $total_active"
    fi
}

# Display summary format
display_summary() {
    local copilot_data="$1"
    local claude_data="$2"
    
    print_status $GREEN "PIB-METHOD AI Status Summary"
    print_status $GREEN "==========================="
    echo
    
    local copilot_count=0
    local claude_count=0
    
    if [ "$USE_JQ" = true ]; then
        local copilot_issues=$(echo "$copilot_data" | cut -d'|' -f1)
        local copilot_prs=$(echo "$copilot_data" | cut -d'|' -f2)
        local claude_issues=$(echo "$claude_data" | cut -d'|' -f1)
        local claude_prs=$(echo "$claude_data" | cut -d'|' -f2)
        
        copilot_count=$(echo "$copilot_issues $copilot_prs" | jq -s 'add | length' 2>/dev/null || echo 0)
        claude_count=$(echo "$claude_issues $claude_prs" | jq -s 'add | length' 2>/dev/null || echo 0)
    fi
    
    print_status $CYAN "GitHub Copilot: $copilot_count active tasks"
    print_status $CYAN "Claude Code: $claude_count active tasks"
    print_status $BLUE "Total: $((copilot_count + claude_count)) active tasks"
    
    if [ $((copilot_count + claude_count)) -gt 0 ]; then
        echo
        print_status $YELLOW "Estimated completion: 2-4 hours (varies by complexity)"
        print_status $BLUE "Use '/ai-status --format table' for detailed view"
    else
        echo
        print_status $YELLOW "No active AI work found"
        print_status $BLUE "Use '/ai-issue' or '/ai-assign' to create new assignments"
    fi
}

# Main execution
main() {
    check_prerequisites
    parse_arguments "$@"
    
    print_status $BLUE "Fetching AI work status..."
    echo
    
    local copilot_data=""
    local claude_data=""
    
    if [ "$AI_FILTER" = "" ] || [ "$AI_FILTER" = "copilot" ]; then
        copilot_data=$(get_copilot_status)
    fi
    
    if [ "$AI_FILTER" = "" ] || [ "$AI_FILTER" = "claude" ]; then
        claude_data=$(get_claude_status)
    fi
    
    case "$OUTPUT_FORMAT" in
        "table")
            display_table "$copilot_data" "$claude_data"
            ;;
        "summary")
            display_summary "$copilot_data" "$claude_data"
            ;;
        "json")
            echo "{\"copilot\": $copilot_data, \"claude\": $claude_data}" | jq . 2>/dev/null || echo "$copilot_data|$claude_data"
            ;;
    esac
    
    echo
    print_status $BLUE "Legend: ⏳ Open  🔄 In Progress  👀 Review Ready  ✅ Completed  ❌ Closed"
}

# Run main function with all arguments
main "$@"