# AI Assignment Manager

Assign existing GitHub issues or pull requests to AI assistants (<PERSON><PERSON><PERSON> or <PERSON>) directly from the terminal.

## Usage
```bash
issue -> /github:ai-issue-assign <ai> <type> <number> ["<additional_context>"]
issue -> /github:ai-issue-assign <ai> <github_url> ["<additional_context>"]
```

## Examples
```bash
# Assign existing issue to Co<PERSON>lot
issue -> /github:ai-issue-assign copilot issue 123
issue -> /github:ai-issue-assign copilot issue 456 "Focus on performance optimization"

# Assign PR to <PERSON> for review
issue -> /github:ai-issue-assign claude pr 789 "Please check LEVER compliance and suggest improvements"

# Using GitHub URLs
issue -> /github:ai-issue-assign copilot https://github.com/user/repo/issues/123
issue -> /github:ai-issue-assign claude https://github.com/user/repo/pull/456 "Analyze for security vulnerabilities"
```

## AI Assignment Behavior

### GitHub Copilot (Issues)
- **Direct Assignment**: Adds copilot as assignee to the issue
- **Auto-Activation**: <PERSON><PERSON><PERSON> immediately starts analyzing and planning
- **Progress Updates**: Co<PERSON><PERSON> adds reactions and status comments
- **PR Creation**: Automatically creates draft PR when implementation begins

### GitHub Copilot (PRs)
- **Comment Addition**: Adds review request comment with @copilot mention
- **Review Mode**: Copilot provides comprehensive PR review
- **Inline Suggestions**: Can add line-specific improvement suggestions
- **Approval Process**: Follows repository review requirements

### Claude Code (Issues)
- **Context Comment**: Adds @claude mention with issue context
- **GitHub Actions**: Triggers Claude workflow if configured
- **Analysis Phase**: Claude analyzes existing codebase patterns
- **Implementation**: Creates detailed implementation plan and code

### Claude Code (PRs)
- **Review Comment**: Adds @claude mention requesting review
- **LEVER Analysis**: Provides detailed LEVER framework scoring
- **Improvement Suggestions**: Specific actionable recommendations
- **Pattern Validation**: Checks against repository conventions

## Parameters
- `<ai>`: Either `copilot` or `claude`
- `<type>`: Either `issue` or `pr` (not needed with URLs)
- `<number>`: GitHub issue or PR number
- `<github_url>`: Full GitHub URL to issue or PR
- `<additional_context>`: Optional specific instructions or focus areas

## Assignment Strategies

### For Issues
```bash
# Simple assignment
issue -> /github:ai-issue-assign copilot issue 123

# With specific focus
issue -> /github:ai-issue-assign claude issue 456 "Implement following existing authentication patterns in the codebase"

# Complex requirements
issue -> /github:ai-issue-assign copilot issue 789 "This is a critical bug affecting production users. Please prioritize security and backward compatibility."
```

### For Pull Requests
```bash
# Standard review request
issue -> /github:ai-issue-assign claude pr 123

# Security-focused review
issue -> /github:ai-issue-assign copilot pr 456 "Focus on security implications and potential vulnerabilities"

# Performance review
issue -> /github:ai-issue-assign claude pr 789 "Analyze for performance bottlenecks and optimization opportunities"
```

## Prerequisites
- GitHub CLI installed and authenticated (`gh auth login`)
- Repository access permissions
- For Claude: GitHub Actions configured (run `claude-actions -> /github:setup-claude-actions-claude-actions` first)
- Valid issue/PR number or URL

## LEVER Framework Integration
All assignments include LEVER framework requirements:
- **L**everage: Must identify and reuse existing patterns
- **E**xtend: Prefer extending over creating new code
- **V**erify: Include comprehensive testing requirements
- **E**liminate: Remove any code duplication
- **R**educe: Minimize implementation complexity

## Output
The command will:
1. Validate the issue/PR exists and is accessible
2. Add appropriate AI assignment or comment
3. Include PIB-METHOD context and standards
4. Confirm assignment success
5. Provide tracking information and next steps

## Advanced Features

### Bulk Assignment
```bash
# Assign multiple issues to same AI (future enhancement)
issue -> /github:ai-issue-assign copilot issues 123,456,789 "Fix all authentication-related bugs"
```

### Context Templates
```bash
# Use predefined context templates
issue -> /github:ai-issue-assign claude issue 123 --template security-review
issue -> /github:ai-issue-assign copilot pr 456 --template performance-optimization
```

## Error Handling
- Validates GitHub CLI authentication
- Checks issue/PR existence and accessibility
- Verifies assignment permissions
- Handles already-assigned items gracefully
- Provides clear error messages with resolution steps

## Status Tracking
After assignment, use `issue -> /github:ai-issue-status` to monitor:
- AI response timeframes
- Work progress updates
- Completion notifications
- Review requirements

## Related Commands
- `issue -> /github:ai-issue-status` - Monitor AI work progress
- `issue -> /github:ai-issue-issue` - Create new issues with AI assignment
- `issue -> /github:ai-issue-pr` - Add AI comments to PRs
- `claude-actions -> /github:setup-claude-actions-claude-actions` - Configure Claude GitHub Actions