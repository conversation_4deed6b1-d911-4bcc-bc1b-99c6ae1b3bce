# AI Work Status Monitor

Monitor the progress and status of AI assistants (<PERSON><PERSON><PERSON> and <PERSON>) working on GitHub issues and pull requests.

## Usage
```bash
issue -> /github:ai-issue-status [ai] [--repo repository] [--format table|json|summary]
```

## Examples
```bash
# Show all AI work in current repository
issue -> /github:ai-issue-status

# Show only Copilot work
issue -> /github:ai-issue-status copilot

# Show only Claude work
issue -> /github:ai-issue-status claude

# Show work across specific repository
issue -> /github:ai-issue-status --repo username/repository-name

# Show detailed JSON output
issue -> /github:ai-issue-status --format json

# Show summary view
issue -> /github:ai-issue-status --format summary
```

## Status Information Displayed

### GitHub Copilot Status
- **Assigned Issues**: Issues currently assigned to copilot user
- **Active PRs**: Pull requests created by copilot
- **Response Time**: Time since assignment
- **Current Phase**: Planning, Implementation, Testing, Review
- **Progress Indicators**: Emoji reactions and status comments
- **Completion Status**: Draft/Ready for review

### Claude Code Status
- **Triggered Issues**: Issues with @claude mentions
- **GitHub Actions**: Workflow run status and logs
- **Processing Phase**: Analysis, Implementation, Testing
- **PR Status**: Created PRs and their review state
- **Error States**: Failed workflows or blocked processes
- **Response Logs**: Detailed execution information

## Status Categories

### ⏳ **In Progress**
- Issues assigned but not yet started
- AI is analyzing requirements
- Implementation in progress
- Tests being written

### 🔄 **Active Development**
- Code is being written
- PRs are being createdknowledge -> /knowledge:update-knowledged
- Tests are running
- Continuous integration in progress

### 👀 **Awaiting Review**
- AI has completed work
- PR is ready for human review
- Waiting for feedback or approval
- Review comments being addressed

### ✅ **Completed**
- Work has been reviewed and approved
- PRs have been merged
- Issues have been closed
- Tasks successfully finished

### ❌ **Blocked/Failed**
- GitHub Actions failed
- API rate limits exceeded
- Permission issues
- Configuration problems

## Output Formats

### Table Format (Default)
```
AI       Type    #    Title                    Status        Updated
copilot  issue   123  Implement JWT auth       In Progress   2m ago
claude   pr      456  Security review          Review Ready  5m ago
copilot  issue   789  Fix login bug           Completed     1h ago
```

### Summary Format
```
GitHub Copilot: 2 active, 1 completed, 0 failed
Claude Code: 1 active, 0 completed, 0 failed
Total: 3 active tasks, estimated completion in 2-4 hours
```

### JSON Format
```json
{
  "copilot": {
    "active": [
      {
        "type": "issue",
        "number": 123,
        "title": "Implement JWT auth",
        "status": "in_progress",
        "updated": "2024-01-15T10:30:00Z",
        "url": "https://github.com/user/repo/issues/123"
      }
    ],
    "completed": [...],
    "failed": [...]
  },
  "claude": {...}
}
```

## Advanced Filtering

### By Time Period
```bash
# Show work from last 24 hours
issue -> /github:ai-issue-status --since 24h

# Show work from last week
issue -> /github:ai-issue-status --since 1w
```

### By Status
```bash
# Show only active work
issue -> /github:ai-issue-status --status active

# Show only failed/blocked work
issue -> /github:ai-issue-status --status failed
```

### By Type
```bash
# Show only issues
issue -> /github:ai-issue-status --type issue

# Show only pull requests
issue -> /github:ai-issue-status --type pr
```

## Notifications and Alerts

### Status Changes
- **New Assignments**: Notification when AI receives new work
- **Completion**: Alert when AI finishes tasks
- **Failures**: Immediate notification of failed processes
- **Review Ready**: Alert when human review is needed

### Reminder System
- **Stale Work**: Notify about work inactive for > 2 hours
- **Pending Reviews**: Remind about PRs awaiting review
- **Failed Actions**: Alert about GitHub Actions needing attention

## Integration Features

### Auto-Refresh
```bash
# Monitor with auto-refresh every 30 seconds
issue -> /github:ai-issue-status --watch 30s
```

### Export Options
```bash
# Export to file for analysis
issue -> /github:ai-issue-status --export work-status-$(date +%Y%m%d).json
```

## Prerequisites
- GitHub CLI installed and authenticated (`gh auth login`)
- Repository access permissions
- For Claude status: GitHub Actions API access

## Troubleshooting

### Common Issues
- **No Status Shown**: Check GitHub CLI authentication
- **Claude Status Missing**: Verify GitHub Actions are configured
- **Permission Errors**: Ensure repository access rights
- **Rate Limits**: GitHub API limits may delay updates

### Resolution Commands
```bash
# Refresh GitHub CLI authentication
gh auth refresh

# Check Claude Actions setup
claude-actions -> /github:setup-claude-actions-claude-actions --verify

# Validate repository access
gh repo view
```

## Dashboard Integration
Status information can be exported for integration with:
- Project management tools
- CI/CD dashboards
- Team notification systems
- Progress tracking applications

## Related Commands
- `issue -> /github:ai-issue-issue` - Create new AI assignments
- `issue -> /github:ai-issue-assign` - Assign existing items to AI
- `issue -> /github:ai-issue-pr` - Add AI comments to PRs
- `claude-actions -> /github:setup-claude-actions-claude-actions` - Configure Claude integration