# AI Issue Creator

Create GitHub issues and assign them to AI assistants (<PERSON><PERSON><PERSON> or <PERSON>) directly from the terminal.

## Usage
```bash
issue -> /github:ai-issue-issue <ai> "<issue_title>" ["<issue_body>"] [--labels "<labels>"]
```

## Examples
```bash
# Assign to GitHub Copilot
issue -> /github:ai-issue-issue copilot "Implement JWT authentication system"
issue -> /github:ai-issue-issue copilot "Add user dashboard" "Create a responsive user dashboard with analytics widgets"

# Assign to Claude Code  
issue -> /github:ai-issue-issue claude "Review authentication patterns and implement OAuth"
issue -> /github:ai-issue-issue claude "Fix failing tests" "The auth tests are failing after the recent changes" --labels "bug,testing"

# With custom labels
issue -> /github:ai-issue-issue copilot "Implement dark mode toggle" --labels "enhancement,ui"
```

## AI Assignment Methods

### GitHub Copilot
- **Direct Assignment**: Issues are assigned to the `copilot` user
- **Auto-starts**: <PERSON><PERSON><PERSON> automatically begins work and creates PRs
- **Progress Tracking**: Copilot updates with emoji reactions and comments

### Claude Code
- **@claude Mention**: Issue body includes `@claude` mention for auto-trigger
- **Requires Setup**: GitHub Actions must be configured first (use `claude-actions -> /github:setup-claude-actions-claude-actions`)
- **Context-Aware**: Claude analyzes the full issue context before implementation

## Options
- `<ai>`: Either `copilot` or `claude`
- `<issue_title>`: Brief descriptive title for the issue
- `<issue_body>`: Optional detailed description (defaults to title if not provided)
- `--labels`: Optional comma-separated labels (e.g., "bug,enhancement,urgent")

## Prerequisites
- GitHub CLI installed and authenticated (`gh auth login`)
- For Claude: GitHub Actions configured (run `claude-actions -> /github:setup-claude-actions-claude-actions` first)
- Repository permissions to create issues

## LEVER Framework Integration
All AI-created issues automatically include LEVER compliance requirements:
- **L**everage: Search existing patterns first
- **E**xtend: Extend rather than create new
- **V**erify: Include comprehensive tests
- **E**liminate: Remove any duplication
- **R**educe: Minimize complexity

## Output
The command will:
1. Create the GitHub issue with specified title and body
2. Assign to the appropriate AI assistant
3. Add PIB-METHOD standard labels and templates
4. Return the issue URL for tracking
5. Display next steps for monitoring progress

## Error Handling
- Validates GitHub CLI authentication
- Checks repository permissions
- Verifies AI assistant availability
- Provides clear error messages with resolution steps

## Related Commands
- `issue -> /github:ai-issue-status` - Check AI work progress
- `issue -> /github:ai-issue-assign` - Assign existing issues to AI
- `claude-actions -> /github:setup-claude-actions-claude-actions` - Configure Claude GitHub Actions