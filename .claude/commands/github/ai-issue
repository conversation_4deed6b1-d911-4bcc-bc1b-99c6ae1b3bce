#!/bin/bash

# AI Issue Creator
# Create GitHub issues and assign them to AI assistants (<PERSON><PERSON><PERSON> or <PERSON>) directly from the terminal

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Check prerequisites
check_prerequisites() {
    # Check if gh CLI is installed
    if ! command -v gh &> /dev/null; then
        print_status $RED "Error: GitHub CLI (gh) is not installed"
        print_status $YELLOW "Install with: brew install gh"
        exit 1
    fi
    
    # Check if gh is authenticated
    if ! gh auth status &> /dev/null; then
        print_status $RED "Error: GitHub CLI is not authenticated"
        print_status $YELLOW "Run: gh auth login"
        exit 1
    fi
    
    # Check if we're in a git repository
    if ! git rev-parse --is-inside-work-tree &> /dev/null; then
        print_status $RED "Error: Not in a git repository"
        exit 1
    fi
}

# Parse command line arguments
parse_arguments() {
    if [ $# -lt 2 ]; then
        print_status $RED "Error: Insufficient arguments"
        echo
        echo "Usage: /ai-issue <ai> \"<issue_title>\" [\"<issue_body>\"] [--labels \"<labels>\"] [--planning-mode] [--plan session_id]"
        echo
        echo "Examples:"
        echo "  /ai-issue copilot \"Implement JWT authentication system\""
        echo "  /ai-issue claude \"Review auth patterns\" \"Analyze existing authentication patterns\" --labels \"enhancement,security\""
        echo "  /ai-issue claude \"implement user dashboard\" --planning-mode"
        exit 1
    fi
    
    AI_TYPE="$1"
    ISSUE_TITLE="$2"
    ISSUE_BODY="${3:-$ISSUE_TITLE}"
    LABELS=""
    PLANNING_MODE=false
    PLAN_ID=""
    
    shift 2
    if [ $# -gt 0 ] && [[ "$1" != --* ]]; then
        ISSUE_BODY="$1"
        shift
    fi
    
    # Parse remaining options
    while [[ $# -gt 0 ]]; do
        case $1 in
            --labels)
                LABELS="$2"
                shift 2
                ;;
            --planning-mode)
                PLANNING_MODE=true
                shift
                ;;
            --plan)
                PLAN_ID="$2"
                shift 2
                ;;
            *)
                print_status $RED "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Validate AI type
    if [ "$AI_TYPE" != "copilot" ] && [ "$AI_TYPE" != "claude" ]; then
        print_status $RED "Error: AI type must be 'copilot' or 'claude'"
        exit 1
    fi
}

# Create GitHub issue
create_issue() {
    local enhanced_body=""
    local enhanced_labels="pib-method"
    
    # Add PIB-METHOD context to issue body
    if [ "$PLANNING_MODE" = true ]; then
        enhanced_body="$ISSUE_BODY

## Q-LEVER Planning Mode 🤔
This issue requires interactive planning before implementation following the Q-LEVER framework:

### Phase 1: Question & Clarify (MANDATORY FIRST)
- **Q**: Ask clarifying questions about requirements, scope, and assumptions
- **Ask about**: Technical constraints, integration points, user requirements, success criteria
- **Validate**: All assumptions before proceeding to planning
- **Document**: Questions and answers for reference

### Phase 2: Plan with LEVER Framework
After questions are answered, create detailed plan using:
- **L**everage: Identify existing patterns to reuse
- **E**xtend: Plan extensions rather than new creation
- **V**erify: Define comprehensive testing strategy
- **E**liminate: Remove any duplication opportunities
- **R**educe: Minimize implementation complexity

### Phase 3: Approval Required
- Present complete implementation plan
- Wait for explicit approval via /confirm-approach
- Only proceed to implementation after approval

---"
        
        # Add planning-specific labels
        enhanced_labels="$enhanced_labels,planning,q-lever"
        
        # Add AI-specific planning instructions
        if [ "$AI_TYPE" = "claude" ]; then
            enhanced_body="$enhanced_body
@claude Please start with Phase 1 - ask clarifying questions about this task. Do NOT provide implementation suggestions until all assumptions are validated."
        fi
    else
        enhanced_body="$ISSUE_BODY

## PIB-METHOD Context
This issue follows PIB-METHOD standards and requires Q-LEVER framework compliance:

### Q-LEVER Requirements
- **Q**uestion: Validate assumptions and clarify requirements first
- **L**everage: Search and reuse existing patterns
- **E**xtend: Extend existing code rather than creating new
- **V**erify: Include comprehensive tests (>80% coverage)
- **E**liminate: Remove any code duplication
- **R**educe: Minimize implementation complexity

### Quality Gates
- [ ] Assumptions validated through questions
- [ ] Q-LEVER score minimum 4/5
- [ ] Pattern recognition completed
- [ ] Test coverage >80%
- [ ] Security review passed
- [ ] Documentation updated

---"
        
        # Add AI-specific instructions
        if [ "$AI_TYPE" = "claude" ]; then
            enhanced_body="$enhanced_body
@claude Please implement this following PIB-METHOD Q-LEVER guidelines. Start by asking any clarifying questions."
        fi
    fi
    
    # Add plan reference if provided
    if [ -n "$PLAN_ID" ]; then
        enhanced_body="$enhanced_body

## Implementation Plan Reference
This issue implements approved plan: **$PLAN_ID**
Follow the architecture decisions and implementation steps from the approved planning session."
        enhanced_labels="$enhanced_labels,planned-implementation"
    fi
    
    # Combine labels
    if [ -n "$LABELS" ]; then
        enhanced_labels="$enhanced_labels,$LABELS"
    fi
    
    print_status $BLUE "Creating GitHub issue..."
    print_status $YELLOW "  Title: $ISSUE_TITLE"
    print_status $YELLOW "  AI: $AI_TYPE"
    print_status $YELLOW "  Labels: $enhanced_labels"
    
    # Create the issue
    local issue_url
    if [ "$AI_TYPE" = "copilot" ]; then
        # Create issue and assign to copilot
        issue_url=$(gh issue create \
            --title "$ISSUE_TITLE" \
            --body "$enhanced_body" \
            --label "$enhanced_labels" \
            --assignee "copilot" 2>/dev/null || gh issue create \
            --title "$ISSUE_TITLE" \
            --body "$enhanced_body" \
            --label "$enhanced_labels")
        
        print_status $GREEN "✓ Issue created and assigned to GitHub Copilot"
        print_status $BLUE "  Copilot will automatically start working and add 👀 emoji reaction"
    else
        # Create issue for Claude (will be triggered by @claude mention)
        issue_url=$(gh issue create \
            --title "$ISSUE_TITLE" \
            --body "$enhanced_body" \
            --label "$enhanced_labels")
        
        print_status $GREEN "✓ Issue created with @claude mention"
        print_status $BLUE "  Claude will respond via GitHub Actions (ensure setup is complete)"
    fi
    
    echo
    print_status $GREEN "Issue URL: $issue_url"
    
    # Show next steps
    echo
    print_status $BLUE "Next Steps:"
    if [ "$AI_TYPE" = "copilot" ]; then
        echo "  1. Monitor issue for Copilot's 👀 reaction (indicates it's working)"
        echo "  2. Watch for draft PR creation"
        echo "  3. Review and approve when Copilot requests review"
    else
        echo "  1. Monitor GitHub Actions tab for Claude workflow"
        echo "  2. Watch for Claude's analysis and implementation plan"
        echo "  3. Provide feedback if Claude requests clarification"
    fi
    echo "  • Use '/ai-status $AI_TYPE' to monitor progress"
}

# Main execution
main() {
    print_status $GREEN "PIB-METHOD AI Issue Creator"
    print_status $GREEN "=========================="
    echo
    
    check_prerequisites
    parse_arguments "$@"
    create_issue
}

# Run main function with all arguments
main "$@"