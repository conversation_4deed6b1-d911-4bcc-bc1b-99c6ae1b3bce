# AI Pull Request Comments

Add AI assistant comments to pull requests for reviews, feedback, or specific requests directly from the terminal.

## Usage
```bash
issue -> /github:ai-issue-pr <ai> <pr_number> "<comment_text>"
issue -> /github:ai-issue-pr <ai> <pr_url> "<comment_text>"
```

## Examples
```bash
# Request Copilot to review a PR
issue -> /github:ai-issue-pr copilot 123 "Please review this PR for security vulnerabilities and performance issues"

# Ask <PERSON> to check LEVER compliance
issue -> /github:ai-issue-pr claude 456 "Review this code for LEVER framework compliance and suggest improvements"

# Request specific analysis
issue -> /github:ai-issue-pr copilot https://github.com/user/repo/pull/789 "Analyze the database queries for optimization opportunities"

# Ask for implementation guidance
issue -> /github:ai-issue-pr claude 321 "How should I refactor this authentication logic to follow existing patterns?"
```

## AI Interaction Methods

### GitHub Copilot
- **@copilot Mentions**: Comments automatically include `@copilot` mention
- **Auto-Response**: <PERSON><PERSON><PERSON> responds directly in the PR conversation
- **Code Suggestions**: Can provide inline code suggestions and fixes
- **Review Integration**: Works with GitHub's native review system

### Claude Code  
- **@claude Mentions**: Comments include `@claude` mention to trigger GitHub Actions
- **Detailed Analysis**: Provides comprehensive code analysis and suggestions
- **LEVER Scoring**: Automatically evaluates code against LEVER framework
- **Context-Aware**: Considers entire PR context and repository patterns

## AI Capabilities

### Code Review Tasks
- Security vulnerability analysis
- Performance optimization suggestions
- Code quality and style checking
- LEVER framework compliance verification
- Architecture pattern validation

### Implementation Guidance
- Refactoring recommendations
- Design pattern suggestions
- Best practice enforcement
- Technical debt identification
- Test coverage analysis

## Options
- `<ai>`: Either `copilot` or `claude`
- `<pr_number>`: GitHub PR number (e.g., 123)
- `<pr_url>`: Full GitHub PR URL
- `<comment_text>`: Your request or question for the AI

## Prerequisites
- GitHub CLI installed and authenticated (`gh auth login`)
- For Claude: GitHub Actions configured (run `claude-actions -> /github:setup-claude-actions-claude-actions` first)
- Repository permissions to comment on PRs
- Valid PR number or URL

## LEVER Framework Integration
All AI PR interactions include LEVER compliance checking:
- **L**everage: Identifies existing patterns that could be reused
- **E**xtend: Suggests extending current implementations
- **V**erify: Recommends additional test coverage
- **E**liminate: Flags code duplication
- **R**educe: Suggests complexity reduction

## Output
The command will:
1. Add the comment to the specified PR
2. Tag the appropriate AI assistant
3. Include PIB-METHOD context and standards
4. Return the comment URL for tracking
5. Display expected response timeframe

## Advanced Usage
```bash
# Request comprehensive review
issue -> /github:ai-issue-pr claude 123 "Perform a complete LEVER framework review with scoring and specific improvement recommendations"

# Ask for specific expertise
issue -> /github:ai-issue-pr copilot 456 "As a security expert, analyze this authentication implementation for vulnerabilities"

# Request performance analysis
issue -> /github:ai-issue-pr claude 789 "Analyze this database layer for performance bottlenecks and suggest optimizations"
```

## Error Handling
- Validates GitHub CLI authentication
- Verifies PR exists and is accessible
- Checks comment permissions
- Handles API rate limits gracefully
- Provides clear error messages with solutions

## Related Commands
- `issue -> /github:ai-issue-status` - Check AI response progress
- `issue -> /github:ai-issue-issue` - Create new issues with AI assignment
- `issue -> /github:ai-issue-assign` - Assign existing issues/PRs to AI