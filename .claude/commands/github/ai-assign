#!/bin/bash

# AI Assignment Manager
# Assign existing GitHub issues or pull requests to AI assistants (<PERSON><PERSON><PERSON> or <PERSON>) directly from the terminal

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Check prerequisites
check_prerequisites() {
    # Check if gh CLI is installed
    if ! command -v gh &> /dev/null; then
        print_status $RED "Error: GitHub CLI (gh) is not installed"
        print_status $YELLOW "Install with: brew install gh"
        exit 1
    fi
    
    # Check if gh is authenticated
    if ! gh auth status &> /dev/null; then
        print_status $RED "Error: GitHub CLI is not authenticated"
        print_status $YELLOW "Run: gh auth login"
        exit 1
    fi
    
    # Check if we're in a git repository
    if ! git rev-parse --is-inside-work-tree &> /dev/null; then
        print_status $RED "Error: Not in a git repository"
        exit 1
    fi
}

# Parse command line arguments
parse_arguments() {
    if [ $# -lt 3 ]; then
        print_status $RED "Error: Insufficient arguments"
        echo
        echo "Usage: /ai-assign <ai> <type> <number> [\"<additional_context>\"]"
        echo "   or: /ai-assign <ai> <github_url> [\"<additional_context>\"]"
        echo
        echo "Examples:"
        echo "  /ai-assign copilot issue 123"
        echo "  /ai-assign claude pr 456 \"Focus on LEVER compliance\""
        echo "  /ai-assign copilot https://github.com/user/repo/issues/123"
        exit 1
    fi
    
    AI_TYPE="$1"
    
    # Validate AI type
    if [ "$AI_TYPE" != "copilot" ] && [ "$AI_TYPE" != "claude" ]; then
        print_status $RED "Error: AI type must be 'copilot' or 'claude'"
        exit 1
    fi
    
    # Parse second argument - could be type or URL
    if [[ "$2" =~ ^https://github.com/.*/((issues|pull)/([0-9]+)) ]]; then
        # It's a GitHub URL
        GITHUB_URL="$2"
        if [[ "${BASH_REMATCH[2]}" == "issues" ]]; then
            ITEM_TYPE="issue"
        else
            ITEM_TYPE="pr"
        fi
        ITEM_NUMBER="${BASH_REMATCH[3]}"
        ADDITIONAL_CONTEXT="${3:-}"
    else
        # It's type and number format
        ITEM_TYPE="$2"
        ITEM_NUMBER="$3"
        ADDITIONAL_CONTEXT="${4:-}"
        
        # Validate type
        if [ "$ITEM_TYPE" != "issue" ] && [ "$ITEM_TYPE" != "pr" ]; then
            print_status $RED "Error: Type must be 'issue' or 'pr'"
            exit 1
        fi
        
        # Validate number
        if ! [[ "$ITEM_NUMBER" =~ ^[0-9]+$ ]]; then
            print_status $RED "Error: Invalid number format"
            exit 1
        fi
    fi
}

# Validate item exists
validate_item() {
    if [ "$ITEM_TYPE" = "issue" ]; then
        if ! gh issue view "$ITEM_NUMBER" &> /dev/null; then
            print_status $RED "Error: Issue #$ITEM_NUMBER does not exist or is not accessible"
            exit 1
        fi
    else
        if ! gh pr view "$ITEM_NUMBER" &> /dev/null; then
            print_status $RED "Error: PR #$ITEM_NUMBER does not exist or is not accessible"
            exit 1
        fi
    fi
}

# Assign to AI
assign_to_ai() {
    local success=false
    
    print_status $BLUE "Assigning $ITEM_TYPE #$ITEM_NUMBER to $AI_TYPE..."
    
    if [ "$ITEM_TYPE" = "issue" ]; then
        if [ "$AI_TYPE" = "copilot" ]; then
            # Try to assign issue directly to copilot user
            if gh issue edit "$ITEM_NUMBER" --add-assignee "copilot" &> /dev/null; then
                print_status $GREEN "✓ Issue assigned directly to GitHub Copilot"
                success=true
            else
                # Fallback: add comment mentioning copilot
                local comment_body="@copilot Please work on this issue."
                if [ -n "$ADDITIONAL_CONTEXT" ]; then
                    comment_body="@copilot $ADDITIONAL_CONTEXT"
                fi
                
                comment_body="$comment_body

## PIB-METHOD Assignment

This issue has been assigned following PIB-METHOD standards. Please ensure:

### LEVER Framework Compliance
- **L**everage existing patterns from the codebase
- **E**xtend current implementations rather than creating new
- **V**erify through comprehensive testing (>80% coverage)
- **E**liminate any code duplication
- **R**educe implementation complexity

### Quality Requirements
- LEVER score minimum 4/5
- Security review included
- Performance considerations addressed
- Documentation updated

---
*Automated assignment via PIB-METHOD*"
                
                gh issue comment "$ITEM_NUMBER" --body "$comment_body"
                print_status $GREEN "✓ Added @copilot mention to issue (direct assignment failed)"
                success=true
            fi
            
            if [ "$success" = true ]; then
                echo
                print_status $BLUE "Next Steps:"
                echo "  1. Copilot will add 👀 emoji reaction when it starts working"
                echo "  2. Monitor for draft PR creation"
                echo "  3. Review and approve when Copilot requests review"
            fi
        else
            # Claude assignment via comment
            local comment_body="@claude Please implement this issue following PIB-METHOD guidelines."
            if [ -n "$ADDITIONAL_CONTEXT" ]; then
                comment_body="@claude $ADDITIONAL_CONTEXT"
            fi
            
            comment_body="$comment_body

## PIB-METHOD Assignment

This issue requires implementation following PIB-METHOD standards:

### LEVER Framework Requirements
- **L**everage: Search existing codebase patterns first
- **E**xtend: Extend existing code rather than creating new
- **V**erify: Include comprehensive tests and validation
- **E**liminate: Remove any code duplication
- **R**educe: Minimize implementation complexity

### Implementation Checklist
- [ ] Pattern recognition completed
- [ ] LEVER score ≥ 4/5
- [ ] Test coverage > 80%
- [ ] Security review passed
- [ ] Documentation updated

---
*Automated assignment via PIB-METHOD*"
            
            gh issue comment "$ITEM_NUMBER" --body "$comment_body"
            print_status $GREEN "✓ Added @claude mention to issue"
            success=true
            
            echo
            print_status $BLUE "Next Steps:"
            echo "  1. Claude will respond via GitHub Actions workflow"
            echo "  2. Monitor GitHub Actions tab for processing"
            echo "  3. Claude will provide detailed analysis and implementation plan"
        fi
    else
        # PR assignment - add review comment
        local ai_mention=""
        local review_focus=""
        
        if [ "$AI_TYPE" = "copilot" ]; then
            ai_mention="@copilot"
            review_focus="comprehensive review with inline suggestions"
        else
            ai_mention="@claude"
            review_focus="detailed LEVER framework analysis"
        fi
        
        local comment_body="$ai_mention Please review this pull request."
        if [ -n "$ADDITIONAL_CONTEXT" ]; then
            comment_body="$ai_mention $ADDITIONAL_CONTEXT"
        fi
        
        comment_body="$comment_body

## PIB-METHOD Review Request

Please provide $review_focus following these standards:

### LEVER Framework Assessment
- **L**everage: Identify reusable patterns in the changes
- **E**xtend: Evaluate if existing code was properly extended
- **V**erify: Assess test coverage and validation completeness
- **E**liminate: Flag any code duplication introduced
- **R**educe: Suggest complexity reduction opportunities

### Review Checklist
- [ ] Security vulnerability assessment
- [ ] Performance impact analysis
- [ ] Code quality and maintainability
- [ ] LEVER compliance score (0-5)
- [ ] Test coverage adequacy
- [ ] Documentation completeness

---
*Automated review request via PIB-METHOD*"
        
        gh pr comment "$ITEM_NUMBER" --body "$comment_body"
        print_status $GREEN "✓ Added $ai_mention review request to PR"
        success=true
        
        echo
        print_status $BLUE "Next Steps:"
        if [ "$AI_TYPE" = "copilot" ]; then
            echo "  1. Copilot will provide inline code suggestions"
            echo "  2. Look for overall PR review and recommendations"
            echo "  3. Copilot may request changes or approve"
        else
            echo "  1. Claude will provide detailed LEVER framework analysis"
            echo "  2. Monitor GitHub Actions for Claude processing"
            echo "  3. Expect comprehensive scoring and recommendations"
        fi
    fi
    
    if [ "$success" = true ]; then
        echo "  • Use '/ai-status $AI_TYPE' to monitor progress"
        
        # Show item info
        echo
        print_status $BLUE "$ITEM_TYPE Information:"
        if [ "$ITEM_TYPE" = "issue" ]; then
            gh issue view "$ITEM_NUMBER" --json title,author,url,state | \
                jq -r '"  Title: " + .title + "\n  Author: " + .author.login + "\n  State: " + .state + "\n  URL: " + .url'
        else
            gh pr view "$ITEM_NUMBER" --json title,author,url,isDraft | \
                jq -r '"  Title: " + .title + "\n  Author: " + .author.login + "\n  Draft: " + (.isDraft | tostring) + "\n  URL: " + .url'
        fi
    fi
}

# Main execution
main() {
    print_status $GREEN "PIB-METHOD AI Assignment Manager"
    print_status $GREEN "==============================="
    echo
    
    check_prerequisites
    parse_arguments "$@"
    validate_item
    assign_to_ai
}

# Run main function with all arguments
main "$@"