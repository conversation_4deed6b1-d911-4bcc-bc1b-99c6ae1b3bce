#!/bin/bash

# Claude GitHub Actions Setup
# Automated setup for Claude Code GitHub Actions integration, enabling @claude mentions in issues and pull requests

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Check prerequisites
check_prerequisites() {
    # Check if gh CLI is installed
    if ! command -v gh &> /dev/null; then
        print_status $RED "Error: GitHub CLI (gh) is not installed"
        print_status $YELLOW "Install with: brew install gh"
        exit 1
    fi
    
    # Check if gh is authenticated
    if ! gh auth status &> /dev/null; then
        print_status $RED "Error: GitHub CLI is not authenticated"
        print_status $YELLOW "Run: gh auth login"
        exit 1
    fi
    
    # Check if we're in a git repository
    if ! git rev-parse --is-inside-work-tree &> /dev/null; then
        print_status $RED "Error: Not in a git repository"
        exit 1
    fi
    
    # Check repository permissions
    local repo_info
    repo_info=$(gh repo view --json owner,name,permissions 2>/dev/null || echo '{}')
    local can_admin=$(echo "$repo_info" | jq -r '.permissions.admin // false' 2>/dev/null || echo "false")
    local can_push=$(echo "$repo_info" | jq -r '.permissions.push // false' 2>/dev/null || echo "false")
    
    if [ "$can_admin" != "true" ] && [ "$can_push" != "true" ]; then
        print_status $RED "Error: Insufficient repository permissions"
        print_status $YELLOW "You need push or admin access to set up GitHub Actions"
        exit 1
    fi
}

# Parse command line arguments
parse_arguments() {
    REPOSITORY=""
    VERIFY_ONLY=false
    UPDATE_MODE=false
    DEBUG_MODE=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --repo)
                REPOSITORY="$2"
                shift 2
                ;;
            --verify)
                VERIFY_ONLY=true
                shift
                ;;
            --update)
                UPDATE_MODE=true
                shift
                ;;
            --debug)
                DEBUG_MODE=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_status $RED "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

show_help() {
    echo "Usage: /setup-claude-actions [options]"
    echo
    echo "Options:"
    echo "  --repo <repository>    Setup in specific repository"
    echo "  --verify              Verify existing setup"
    echo "  --update              Update existing configuration"
    echo "  --debug               Enable debug output"
    echo "  --help                Show this help"
    echo
    echo "Examples:"
    echo "  /setup-claude-actions                    # Setup in current repo"
    echo "  /setup-claude-actions --verify          # Check existing setup"
    echo "  /setup-claude-actions --update          # Update configuration"
}

# Verify existing setup
verify_setup() {
    print_status $BLUE "Verifying Claude GitHub Actions setup..."
    echo
    
    local verification_passed=true
    
    # Check if workflow file exists
    if [ -f ".github/workflows/claude-assistant.yml" ]; then
        print_status $GREEN "✓ Workflow file exists"
    else
        print_status $RED "✗ Workflow file missing"
        verification_passed=false
    fi
    
    # Check if API key secret exists
    local secrets_output
    secrets_output=$(gh secret list 2>/dev/null || echo "")
    if echo "$secrets_output" | grep -q "ANTHROPIC_API_KEY"; then
        print_status $GREEN "✓ ANTHROPIC_API_KEY secret configured"
    else
        print_status $RED "✗ ANTHROPIC_API_KEY secret missing"
        verification_passed=false
    fi
    
    # Check if Actions are enabled
    local repo_info
    repo_info=$(gh repo view --json hasActionsEnabled 2>/dev/null || echo '{"hasActionsEnabled": false}')
    local actions_enabled=$(echo "$repo_info" | jq -r '.hasActionsEnabled' 2>/dev/null || echo "false")
    
    if [ "$actions_enabled" = "true" ]; then
        print_status $GREEN "✓ GitHub Actions enabled"
    else
        print_status $RED "✗ GitHub Actions disabled"
        verification_passed=false
    fi
    
    # Test workflow trigger (if debug mode)
    if [ "$DEBUG_MODE" = true ]; then
        print_status $BLUE "Debug: Testing workflow trigger..."
        local recent_runs
        recent_runs=$(gh run list --workflow="claude-assistant.yml" --limit=1 --json status 2>/dev/null || echo "[]")
        local run_count=$(echo "$recent_runs" | jq 'length' 2>/dev/null || echo "0")
        
        if [ "$run_count" -gt 0 ]; then
            print_status $GREEN "✓ Workflow has been triggered before"
        else
            print_status $YELLOW "⚠ No workflow runs found (may not have been triggered yet)"
        fi
    fi
    
    echo
    if [ "$verification_passed" = true ]; then
        print_status $GREEN "✅ Claude GitHub Actions setup is complete and functional"
        print_status $BLUE "You can now use @claude mentions in issues and PRs"
    else
        print_status $RED "❌ Setup verification failed"
        print_status $YELLOW "Run '/setup-claude-actions' without --verify to fix issues"
        exit 1
    fi
}

# Setup API key
setup_api_key() {
    print_status $BLUE "Setting up Anthropic API key..."
    
    # Check if secret already exists
    local secrets_output
    secrets_output=$(gh secret list 2>/dev/null || echo "")
    if echo "$secrets_output" | grep -q "ANTHROPIC_API_KEY" && [ "$UPDATE_MODE" = false ]; then
        print_status $GREEN "✓ ANTHROPIC_API_KEY already configured"
        return 0
    fi
    
    # Prompt for API key
    echo
    print_status $YELLOW "Please provide your Anthropic API key:"
    print_status $CYAN "You can get this from: https://console.anthropic.com/settings/keys"
    echo
    read -s -p "Enter API key: " api_key
    echo
    
    if [ -z "$api_key" ]; then
        print_status $RED "Error: API key cannot be empty"
        exit 1
    fi
    
    # Validate API key format (basic check)
    if [[ ! "$api_key" =~ ^sk-ant-api03- ]]; then
        print_status $YELLOW "Warning: API key format doesn't match expected pattern"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_status $YELLOW "Setup cancelled"
            exit 0
        fi
    fi
    
    # Set the secret
    if echo "$api_key" | gh secret set ANTHROPIC_API_KEY; then
        print_status $GREEN "✓ API key configured successfully"
    else
        print_status $RED "✗ Failed to set API key secret"
        exit 1
    fi
}

# Create workflow file
create_workflow() {
    print_status $BLUE "Creating GitHub Actions workflow..."
    
    # Create .github/workflows directory
    mkdir -p .github/workflows
    
    # Check if workflow already exists
    if [ -f ".github/workflows/claude-assistant.yml" ] && [ "$UPDATE_MODE" = false ]; then
        print_status $YELLOW "Workflow file already exists"
        read -p "Overwrite? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_status $BLUE "Keeping existing workflow file"
            return 0
        fi
    fi
    
    # Find PIB-METHOD template if available
    local template_file=""
    if [ -f "../PIB-METHOD/.github/workflows/claude-assistant.yml" ]; then
        template_file="../PIB-METHOD/.github/workflows/claude-assistant.yml"
    elif [ -f "$HOME/Projects/PIB-METHOD/.github/workflows/claude-assistant.yml" ]; then
        template_file="$HOME/Projects/PIB-METHOD/.github/workflows/claude-assistant.yml"
    fi
    
    if [ -n "$template_file" ]; then
        print_status $BLUE "Using PIB-METHOD template workflow"
        cp "$template_file" .github/workflows/claude-assistant.yml
    else
        print_status $BLUE "Creating basic workflow file"
        cat > .github/workflows/claude-assistant.yml << 'EOF'
name: Claude Code Assistant

on:
  issue_comment:
    types: [created, edited]
  pull_request_review_comment:
    types: [created, edited]
  pull_request_review:
    types: [submitted]
  issues:
    types: [opened, edited]
  pull_request:
    types: [opened, edited, synchronize]

jobs:
  claude-response:
    # Only run if comment/description contains @claude mention
    if: >
      (github.event_name == 'issue_comment' && contains(github.event.comment.body, '@claude')) ||
      (github.event_name == 'pull_request_review_comment' && contains(github.event.comment.body, '@claude')) ||
      (github.event_name == 'pull_request_review' && contains(github.event.review.body, '@claude')) ||
      (github.event_name == 'issues' && contains(github.event.issue.body, '@claude')) ||
      (github.event_name == 'pull_request' && contains(github.event.pull_request.body, '@claude'))
    
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    permissions:
      contents: read
      issues: write
      pull-requests: write
      checks: read
      actions: read
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Claude Code Action
        uses: anthropics/claude-code-action@beta
        with:
          anthropic_api_key: ${{ secrets.ANTHROPIC_API_KEY }}
          github_token: ${{ secrets.GITHUB_TOKEN }}
          trigger_phrase: "@claude"
          pib_method_enabled: true
          lever_compliance: true
          max_tokens: 4096
          temperature: 0.1
        env:
          PIB_METHOD_VERSION: "3.0"
          LEVER_FRAMEWORK_ENABLED: "true"
EOF
    fi
    
    print_status $GREEN "✓ Workflow file created"
}

# Enable GitHub Actions
enable_actions() {
    print_status $BLUE "Ensuring GitHub Actions are enabled..."
    
    # Check current status
    local repo_info
    repo_info=$(gh repo view --json hasActionsEnabled 2>/dev/null || echo '{"hasActionsEnabled": false}')
    local actions_enabled=$(echo "$repo_info" | jq -r '.hasActionsEnabled' 2>/dev/null || echo "false")
    
    if [ "$actions_enabled" = "true" ]; then
        print_status $GREEN "✓ GitHub Actions already enabled"
        return 0
    fi
    
    # Try to enable Actions (may require manual action)
    print_status $YELLOW "GitHub Actions need to be enabled for this repository"
    print_status $BLUE "Please enable Actions manually in repository settings:"
    
    local repo_url=$(gh repo view --json url --jq '.url' 2>/dev/null || echo "")
    if [ -n "$repo_url" ]; then
        print_status $CYAN "$repo_url/settings/actions"
    fi
    
    read -p "Press Enter after enabling Actions in repository settings..."
    
    # Verify Actions are now enabled
    repo_info=$(gh repo view --json hasActionsEnabled 2>/dev/null || echo '{"hasActionsEnabled": false}')
    actions_enabled=$(echo "$repo_info" | jq -r '.hasActionsEnabled' 2>/dev/null || echo "false")
    
    if [ "$actions_enabled" = "true" ]; then
        print_status $GREEN "✓ GitHub Actions enabled"
    else
        print_status $RED "✗ GitHub Actions still disabled - please enable manually"
        exit 1
    fi
}

# Create test issue
create_test() {
    print_status $BLUE "Creating test issue to verify setup..."
    
    local test_title="Test Claude Actions Setup"
    local test_body="@claude Hello! This is a test to verify the GitHub Actions setup is working correctly.

Please respond with a simple acknowledgment to confirm the integration is functional.

## PIB-METHOD Test
This test follows PIB-METHOD standards. Please confirm:
- [x] Claude Actions workflow is triggered
- [x] API key authentication works
- [x] Response generation is functional

---
*Automated test created by PIB-METHOD setup script*"
    
    local issue_url
    issue_url=$(gh issue create \
        --title "$test_title" \
        --body "$test_body" \
        --label "test,pib-method,claude-actions" 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        print_status $GREEN "✓ Test issue created: $issue_url"
        print_status $BLUE "Monitor the issue for Claude's response to verify setup"
        echo
        print_status $YELLOW "Expected: Claude should respond within 2-5 minutes"
        print_status $YELLOW "If no response, check GitHub Actions tab for workflow status"
    else
        print_status $YELLOW "⚠ Could not create test issue (may be due to permissions)"
        print_status $BLUE "Setup complete - test manually by creating an issue with @claude mention"
    fi
}

# Main setup process
perform_setup() {
    print_status $GREEN "PIB-METHOD Claude GitHub Actions Setup"
    print_status $GREEN "====================================="
    echo
    
    # Show repository info
    local repo_info
    repo_info=$(gh repo view --json nameWithOwner,url 2>/dev/null || echo '{}')
    local repo_name=$(echo "$repo_info" | jq -r '.nameWithOwner // "unknown"' 2>/dev/null || echo "unknown")
    local repo_url=$(echo "$repo_info" | jq -r '.url // ""' 2>/dev/null || echo "")
    
    print_status $BLUE "Repository: $repo_name"
    if [ -n "$repo_url" ]; then
        print_status $BLUE "URL: $repo_url"
    fi
    echo
    
    # Setup steps
    print_status $CYAN "Step 1/5: Setting up API key..."
    setup_api_key
    echo
    
    print_status $CYAN "Step 2/5: Creating workflow file..."
    create_workflow
    echo
    
    print_status $CYAN "Step 3/5: Enabling GitHub Actions..."
    enable_actions
    echo
    
    print_status $CYAN "Step 4/5: Committing changes..."
    if git diff --quiet && git diff --cached --quiet; then
        print_status $BLUE "No changes to commit"
    else
        git add .github/workflows/claude-assistant.yml 2>/dev/null || true
        if git commit -m "feat: Add Claude GitHub Actions integration

- Enable @claude mentions in issues and PRs
- PIB-METHOD compliance built-in
- LEVER framework integration
- Automated code analysis and suggestions

🤖 Generated with PIB-METHOD setup script" 2>/dev/null; then
            print_status $GREEN "✓ Changes committed"
        else
            print_status $YELLOW "⚠ Could not commit changes - you may need to commit manually"
        fi
    fi
    echo
    
    print_status $CYAN "Step 5/5: Creating test issue..."
    create_test
    echo
    
    print_status $GREEN "🎉 Setup Complete!"
    print_status $GREEN "=================="
    echo
    print_status $BLUE "Claude GitHub Actions is now configured for this repository."
    print_status $BLUE "You can now use @claude mentions in issues and pull requests."
    echo
    print_status $YELLOW "Next Steps:"
    echo "  1. Monitor the test issue for Claude's response"
    echo "  2. Try creating a new issue with @claude mention"
    echo "  3. Use '/ai-issue claude \"task description\"' for quick assignments"
    echo "  4. Use '/ai-status' to monitor AI work progress"
    echo
    print_status $CYAN "Commands now available:"
    echo "  • /ai-issue claude \"implement feature X\""
    echo "  • /ai-pr claude 123 \"review this PR\""
    echo "  • /ai-assign claude issue 456"
    echo "  • /ai-status claude"
}

# Main execution
main() {
    check_prerequisites
    parse_arguments "$@"
    
    if [ "$VERIFY_ONLY" = true ]; then
        verify_setup
    else
        perform_setup
    fi
}

# Run main function with all arguments
main "$@"