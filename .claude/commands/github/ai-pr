#!/bin/bash

# AI Pull Request Comments
# Add AI assistant comments to pull requests for reviews, feedback, or specific requests directly from the terminal

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Check prerequisites
check_prerequisites() {
    # Check if gh CLI is installed
    if ! command -v gh &> /dev/null; then
        print_status $RED "Error: GitHub CLI (gh) is not installed"
        print_status $YELLOW "Install with: brew install gh"
        exit 1
    fi
    
    # Check if gh is authenticated
    if ! gh auth status &> /dev/null; then
        print_status $RED "Error: GitHub CLI is not authenticated"
        print_status $YELLOW "Run: gh auth login"
        exit 1
    fi
    
    # Check if we're in a git repository
    if ! git rev-parse --is-inside-work-tree &> /dev/null; then
        print_status $RED "Error: Not in a git repository"
        exit 1
    fi
}

# Parse command line arguments
parse_arguments() {
    if [ $# -lt 3 ]; then
        print_status $RED "Error: Insufficient arguments"
        echo
        echo "Usage: /ai-pr <ai> <pr_number|pr_url> \"<comment_text>\""
        echo
        echo "Examples:"
        echo "  /ai-pr copilot 123 \"Please review this PR for security vulnerabilities\""
        echo "  /ai-pr claude 456 \"Review this code for LEVER framework compliance\""
        echo "  /ai-pr copilot https://github.com/user/repo/pull/789 \"Analyze database queries for optimization\""
        exit 1
    fi
    
    AI_TYPE="$1"
    PR_IDENTIFIER="$2"
    COMMENT_TEXT="$3"
    
    # Validate AI type
    if [ "$AI_TYPE" != "copilot" ] && [ "$AI_TYPE" != "claude" ]; then
        print_status $RED "Error: AI type must be 'copilot' or 'claude'"
        exit 1
    fi
    
    # Extract PR number from URL if needed
    if [[ "$PR_IDENTIFIER" =~ ^https://github.com/.*/pull/([0-9]+) ]]; then
        PR_NUMBER="${BASH_REMATCH[1]}"
    elif [[ "$PR_IDENTIFIER" =~ ^[0-9]+$ ]]; then
        PR_NUMBER="$PR_IDENTIFIER"
    else
        print_status $RED "Error: Invalid PR number or URL format"
        exit 1
    fi
}

# Validate PR exists
validate_pr() {
    if ! gh pr view "$PR_NUMBER" &> /dev/null; then
        print_status $RED "Error: PR #$PR_NUMBER does not exist or is not accessible"
        exit 1
    fi
}

# Create enhanced comment
create_pr_comment() {
    local enhanced_comment=""
    local ai_mention=""
    
    # Set AI mention based on type
    if [ "$AI_TYPE" = "copilot" ]; then
        ai_mention="@copilot"
    else
        ai_mention="@claude"
    fi
    
    # Create enhanced comment with PIB-METHOD context
    enhanced_comment="$ai_mention $COMMENT_TEXT

## PIB-METHOD Analysis Request

Please provide analysis following PIB-METHOD standards:

### LEVER Framework Review
- **L**everage: Identify existing patterns that could be reused
- **E**xtend: Suggest extending current implementations vs creating new
- **V**erify: Recommend additional test coverage needed
- **E**liminate: Flag any code duplication found
- **R**educe: Suggest complexity reduction opportunities

### Review Checklist
- [ ] Security vulnerability assessment
- [ ] Performance impact analysis
- [ ] Code quality and maintainability
- [ ] Test coverage adequacy
- [ ] Documentation completeness
- [ ] LEVER score (0-5 scale)

### Expected Output"
    
    if [ "$AI_TYPE" = "copilot" ]; then
        enhanced_comment="$enhanced_comment
- Inline code suggestions where applicable
- Overall PR review with approval/changes requested
- Performance and security recommendations"
    else
        enhanced_comment="$enhanced_comment
- Detailed LEVER framework scoring with explanations
- Specific improvement recommendations
- Pattern recognition analysis from codebase context"
    fi
    
    enhanced_comment="$enhanced_comment

---
*Automated request via PIB-METHOD AI integration*"
    
    print_status $BLUE "Adding comment to PR #$PR_NUMBER..."
    print_status $YELLOW "  AI: $AI_TYPE"
    print_status $YELLOW "  Request: $COMMENT_TEXT"
    
    # Add comment to PR
    local comment_url
    comment_url=$(gh pr comment "$PR_NUMBER" --body "$enhanced_comment")
    
    print_status $GREEN "✓ Comment added successfully"
    print_status $GREEN "Comment URL: $comment_url"
    
    # Show next steps
    echo
    print_status $BLUE "Next Steps:"
    if [ "$AI_TYPE" = "copilot" ]; then
        echo "  1. Copilot will respond directly in the PR conversation"
        echo "  2. Look for inline code suggestions and review comments"
        echo "  3. Copilot may request changes or approve the PR"
        echo "  4. Response typically within 1-2 minutes"
    else
        echo "  1. Claude will respond via GitHub Actions workflow"
        echo "  2. Monitor GitHub Actions tab for Claude processing"
        echo "  3. Claude will provide detailed LEVER analysis"
        echo "  4. Response typically within 2-5 minutes"
    fi
    echo "  • Use '/ai-status $AI_TYPE' to monitor response progress"
    
    # Show PR info
    echo
    print_status $BLUE "PR Information:"
    gh pr view "$PR_NUMBER" --json title,author,url,isDraft | \
        jq -r '"  Title: " + .title + "\n  Author: " + .author.login + "\n  Draft: " + (.isDraft | tostring) + "\n  URL: " + .url'
}

# Main execution
main() {
    print_status $GREEN "PIB-METHOD AI PR Comment Tool"
    print_status $GREEN "============================="
    echo
    
    check_prerequisites
    parse_arguments "$@"
    validate_pr
    create_pr_comment
}

# Run main function with all arguments
main "$@"