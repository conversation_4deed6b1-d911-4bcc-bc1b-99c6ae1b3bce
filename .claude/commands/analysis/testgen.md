---
description: Comprehensive test generation following PIB testing principles and best practices
---

# PIB Test Generation Command

**Usage**: `/analysis:testgen [scope] [test-type]`

## Purpose

Generate comprehensive test suites with edge case coverage, following PIB testing principles and agent-specific testing expertise for maximum code quality assurance.

## LEVER Framework Testing Analysis

Before test generation, establish testing principles:
- **L** - Leverage: What existing test patterns, frameworks, and utilities can we build upon?
- **E** - Extend: How can we enhance existing test coverage rather than starting fresh?
- **V** - Verify: How can we validate that our tests truly verify the intended behavior?
- **E** - Eliminate: What redundant or ineffective test patterns can we remove?
- **R** - Reduce: What's the simplest test approach that provides comprehensive coverage?

## Agent-Specific Testing Focus

### Current Agent Context
Adapt test generation based on active agent persona:

#### If Developer Agent Active
- Focus on unit tests and component-level testing
- Generate integration tests for complex workflows
- Create edge case and boundary condition tests
- Develop regression tests for bug fixes

#### If QA Agent Active
- Focus on comprehensive functional testing scenarios
- Generate user acceptance test cases
- Create end-to-end workflow validation tests
- Develop usability and accessibility test cases

#### If Performance Agent Active
- Focus on performance and load testing scenarios
- Generate stress tests and capacity validation
- Create benchmark and performance regression tests
- Develop resource utilization monitoring tests

#### If Security Agent Active
- Focus on security testing scenarios
- Generate penetration test cases
- Create input validation and injection tests
- Develop authentication and authorization tests

## Test Generation Categories

### 1. Unit Test Generation

#### Function-Level Testing
```bash
# Analyze functions requiring tests
grep -r "^def\|^function\|^public.*(" --include="*.py" --include="*.js" --include="*.java" . | \
  head -20

# Check existing test coverage
find . -name "*test*" -o -name "*spec*" | wc -l
```

- **Happy Path Tests**: Normal operation with valid inputs
- **Edge Case Tests**: Boundary conditions and limit values
- **Error Handling Tests**: Invalid inputs and exception scenarios
- **State Testing**: Object state changes and side effects

#### Class-Level Testing
- **Constructor Tests**: Object initialization and validation
- **Method Interaction Tests**: Methods working together correctly
- **Inheritance Tests**: Proper behavior in inheritance hierarchies
- **Interface Tests**: Contract compliance and API behavior

### 2. Integration Test Generation

#### Component Integration
- **API Integration**: Service-to-service communication
- **Database Integration**: Data persistence and retrieval
- **External Service Integration**: Third-party service interactions
- **Configuration Integration**: Environment and settings validation

#### System Integration
- **End-to-End Workflows**: Complete user journey testing
- **Cross-Component Communication**: Message passing and events
- **Data Flow Testing**: Information flow through system layers
- **Error Propagation Testing**: Error handling across boundaries

### 3. Test Pattern Recognition

#### Existing Test Analysis
```bash
# Analyze current test patterns
find . -name "*test*.py" -o -name "*test*.js" | xargs grep -l "assert\|expect\|should" | head -10
grep -r "setUp\|beforeEach\|@pytest.fixture" --include="*test*" . | head -5
```

- **Setup Patterns**: Common test initialization approaches
- **Assertion Patterns**: Existing verification methods
- **Mock Patterns**: Current mocking and stubbing strategies
- **Cleanup Patterns**: Resource cleanup and teardown methods

#### Framework-Specific Patterns
- **pytest**: Fixtures, parametrized tests, markers
- **Jest**: Describe/it blocks, beforeEach/afterEach
- **JUnit**: @Test annotations, @Before/@After methods
- **Mocha**: Describe/it structure, hooks

### 4. Edge Case and Boundary Testing

#### Input Validation Tests
- **Null/Undefined Values**: Handling missing or empty inputs
- **Type Mismatch**: Wrong data types and conversion errors
- **Range Violations**: Values outside expected boundaries
- **Format Violations**: Invalid formats and malformed data

#### Resource Constraint Tests
- **Memory Limits**: Large data sets and memory exhaustion
- **Time Limits**: Timeout scenarios and long-running operations
- **Concurrency**: Multi-threaded access and race conditions
- **Network Failures**: Connection issues and service unavailability

#### Business Logic Edge Cases
- **Boundary Conditions**: Min/max values and threshold testing
- **State Transitions**: Invalid state changes and transitions
- **Workflow Interruptions**: Partial completions and recovery
- **Data Consistency**: Concurrent modifications and conflicts

### 5. Test Suite Architecture

#### Test Organization Structure
```bash
# Analyze test structure
find . -type d -name "*test*" | head -10
tree tests/ 2>> /workflows:dev-command/null || echo "No tests directory found"
```

- **Test Directory Structure**: Logical organization of test files
- **Test Categories**: Unit, integration, end-to-end separation
- **Test Utilities**: Shared helpers and test infrastructure
- **Test Data Management**: Test fixtures and data generation

#### Test Configuration
- **Environment Setup**: Test environment configuration
- **Database Setup**: Test database initialization and cleanup
- **Mock Configuration**: Service mocking and stubbing setup
- **Test Runner Configuration**: Framework settings and options

### 6. LEVER Testing Validation

#### Leverage Testing Analysis
- Identify existing test frameworks and utilities to reuse
- Assess current test patterns and successful approaches
- Leverage established testing libraries and tools
- Utilize existing CI/CD testing infrastructure

#### Extension Testing Assessment
- Enhance existing test coverage rather than replacing
- Build upon current test patterns and structures
- Extend existing test utilities and helpers
- Improve current assertion and verification methods

#### Verification Testing Methods
- Validate test effectiveness through coverage metrics
- Verify test reliability through execution stability
- Test the tests through mutation testing
- Confirm test maintainability through code quality

#### Elimination Testing Priorities
- Remove redundant and duplicate test cases
- Eliminate flaky and unreliable tests
- Remove overly complex test setups
- Eliminate brittle integration tests

#### Reduction Testing Strategies
- Simplify test setup and teardown procedures
- Reduce test execution time through optimization
- Minimize test maintenance burden
- Focus on high-value test scenarios

## Test Generation Strategies

### 1. Property-Based Testing
```python
# Example property-based test generation
from hypothesis import given, strategies as st

@given(st.integers())
def test_function_properties(value):
    result = function_under_test(value)
    assert result >= 0  # Property: always returns non-negative
```

### 2. Parameterized Testing
```python
# Example parameterized test generation
import pytest

@pytest.mark.parametrize("input,expected", [
    (0, 0),
    (1, 1),
    (-1, 1),
    (5, 25),
])
def test_function_cases(input, expected):
    assert function_under_test(input) == expected
```

### 3. Mock and Stub Generation
```python
# Example mock generation
from unittest.mock import Mock, patch

def test_service_integration():
    with patch('external_service.call') as mock_service:
        mock_service.return_value = {"status": "success"}
        result = service_under_test()
        assert result.success is True
```

## Output Format

### Test Generation Summary
- Overall test coverage analysis
- Agent perspective and testing expertise applied
- Priority-ranked testing opportunities
- LEVER compliance evaluation

### Generated Test Cases
- Complete test code with proper structure
- Comprehensive coverage of identified scenarios
- Framework-appropriate patterns and conventions
- Proper setup, execution, and teardown procedures

### Test Strategy Documentation
- Testing approach and methodology
- Coverage goals and success criteria
- Test execution and maintenance procedures
- Integration with CI/CD pipelines

### Edge Case Coverage
- Identified boundary conditions and edge cases
- Error scenario testing strategies
- Performance and resource constraint tests
- Security and input validation tests

### Implementation Plan
- Step-by-step test implementation roadmap
- Test execution and validation procedures
- Coverage measurement and improvement tracking
- Maintenance and update strategies

### Workflow Integration
- Update workflow state with testing progress
- Document testing decisions in knowledge base
- **Auto-trigger knowledge update** on test generation completion
- Create test task tracking and coverage monitoring
- Schedule test reviews and quality assessments

## Integration with PIB System

### Workflow Hooks
- Triggers `read-workflow.sh` for LEVER principles reminder
- Updates workflow state through `workflow-transition.sh`
- Logs test generation through `notification-hook.sh`

### Knowledge Management
- Saves test suite to `.aiknowledge-update -> /quick:test-knowledge-updateing/suite-[timestamp].md`
- Updates test coverage metrics and quality tracking
- Creates testing task lists and progress monitoring
- Updates testing standards and best practices documentation

### Follow-up Commands
Test generation prepares context for:
- `*codereview` - Test quality code review
- `*analyze` - Test coverage analysis
- `*debug` - Test failure investigation
- `*thinkdeep` - Strategic testing planning

## Example Usage

```bash
# Generate unit tests for specific module
/analysis:testgen src/auth/ unit

# Create integration tests for API endpoints
/analysis:testgen src/api/ integration

# Generate comprehensive test suite
/analysis:testgen src/ comprehensive

# Create performance tests
/analysis:testgen src/core/ performance
```

## Quality Gates

All test generation must meet PIB standards:
- **LEVER Compliance**: Demonstrates leverage, extension, verification, elimination, reduction
- **Coverage Focus**: Achieves comprehensive test coverage with meaningful tests
- **Agent Alignment**: Respects current agent testing expertise and perspective
- **Framework Compliance**: Follows established testing patterns and conventions
- **Maintainable Output**: Creates tests that are easy to understand and maintain
