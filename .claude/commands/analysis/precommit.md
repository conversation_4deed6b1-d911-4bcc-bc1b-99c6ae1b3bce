---
description: Comprehensive pre-commit validation following PIB quality standards
---

# PIB Pre-commit Validation Command

**Usage**: `/analysis:precommit [scope] [validation-type]`

## Purpose

Perform comprehensive pre-commit validation to ensure code quality, security, and compliance before changes are committed, following PIB principles and agent-specific expertise.

## LEVER Framework Pre-commit Analysis

Before pre-commit validation, establish quality principles:
- **L** - Leverage: What existing quality tools, standards, and processes can we utilize?
- **E** - Extend: How can we enhance existing validation rather than creating new checks?
- **V** - Verify: How can we validate that our validation process catches real issues?
- **E** - Eliminate: What redundant or ineffective validation steps can we remove?
- **R** - Reduce: What's the simplest validation approach that ensures quality?

## Agent-Specific Pre-commit Focus

### Current Agent Context
Adapt pre-commit validation based on active agent persona:

#### If Developer Agent Active
- Focus on code quality and implementation standards
- Validate coding conventions and best practices
- Check unit test coverage and test quality
- Verify error handling and edge case coverage

#### If Security Agent Active
- Focus on security vulnerabilities and compliance
- Validate input sanitization and authentication
- Check for hardcoded secrets and credentials
- Verify encryption and data protection measures

#### If DevOps Agent Active
- Focus on deployment and infrastructure concerns
- Validate CI/CD pipeline configuration changes
- Check container and orchestration configurations
- Verify monitoring and logging implementations

#### If Performance Agent Active
- Focus on performance implications of changes
- Validate algorithm complexity and resource usage
- Check for performance regressions and bottlenecks
- Verify caching and optimization implementations

## Pre-commit Validation Categories

### 1. Git Change Analysis

#### Modified Files Analysis
```bash
# Analyze staged changes
git diff --cached --name-only | head -20
git diff --cached --stat

# Check change scope and impact
git log --oneline -10
git status --porcelain
```

- **Change Scope**: Files modified, added, or removed
- **Change Impact**: Lines changed and complexity assessment
- **Commit History**: Recent changes and patterns
- **Branch Status**: Current branch and upstream tracking

#### Diff Analysis
```bash
# Detailed diff analysis
git diff --cached --unified=3
git diff --cached --word-diff

# Change statistics
git diff --cached --numstat | awk '{added+=$1; removed+=$2} END {print "Added:", added, "Removed:", removed}'
```

- **Code Changes**: Added, modified, and deleted lines
- **Semantic Changes**: Logic modifications and new functionality
- **Structural Changes**: Class, function, and module modifications
- **Configuration Changes**: Settings and environment modifications

### 2. Code Quality Validation

#### Static Analysis Checks
```bash
# Python code quality
if find . -name "*.py" -path "./$(git rev-parse --show-cdup)" | head -1 >> /workflows:dev-command/null 2>&1; then
    echo "Running Python static analysis..."
    # pylint, flake8, mypy, bandit
fi

# JavaScript code quality
if find . -name "*.js" -o -name "*.ts" | head -1 >> /workflows:dev-command/null 2>&1; then
    echo "Running JavaScript static analysis..."
    # eslint, tsc, prettier
fi

# Java code quality
if find . -name "*.java" | head -1 >> /workflows:dev-command/null 2>&1; then
    echo "Running Java static analysis..."
    # checkstyle, spotbugs, pmd
fi
```

- **Syntax Validation**: Code compiles and parses correctly
- **Style Compliance**: Adherence to coding standards
- **Complexity Analysis**: Cyclomatic complexity and maintainability
- **Type Safety**: Type checking and interface compliance

#### Security Validation
```bash
# Security scanning
grep -r "password\|secret\|key" --include="*.py" --include="*.js" --include="*.java" . | \
  grep -v "test\|example\|sample" | head -10

# Dependency vulnerability scanning
if [ -f "requirements.txt" ]; then
    echo "Checking Python dependencies for vulnerabilities..."
fi

if [ -f "package.json" ]; then
    echo "Checking Node.js dependencies for vulnerabilities..."
fi
```

- **Secret Detection**: Hardcoded credentials and API keys
- **Vulnerability Scanning**: Known security issues in dependencies
- **Input Validation**: SQL injection and XSS prevention
- **Authentication**: Secure authentication implementations

### 3. Test Coverage Analysis

#### Test Execution
```bash
# Run test suites
if [ -f "pytest.ini" ] || [ -f "pyproject.toml" ]; then
    echo "Running Python tests..."
    # pytest with coverage
fi

if [ -f "package.json" ]; then
    echo "Running JavaScript tests..."
    # npm test or yarn test
fi

if [ -f "pom.xml" ] || [ -f "build.gradle" ]; then
    echo "Running Java tests..."
    # maven test or gradle test
fi
```

- **Test Execution**: All tests pass successfully
- **Coverage Analysis**: Code coverage metrics and gaps
- **New Code Coverage**: Coverage for modified code
- **Test Quality**: Test effectiveness and edge case coverage

#### Test Validation
- **Test Completeness**: New functionality has corresponding tests
- **Test Isolation**: Tests don't depend on external state
- **Test Performance**: Tests execute within reasonable time
- **Test Maintainability**: Tests are clear and well-structured

### 4. Documentation Validation

#### Documentation Coverage
```bash
# Check for documentation updates
git diff --cached --name-only | grep -E "\.md$|README|CHANGELOG|docs/"

# Validate documentation formatting
if command -v markdownlint >> /workflows:dev-command/null 2>&1; then
    echo "Validating Markdown documentation..."
fi
```

- **Documentation Updates**: Code changes include documentation updates
- **API Documentation**: Public interfaces have documentation
- **Change Documentation**: CHANGELOG and release notes updated
- **Format Validation**: Documentation follows formatting standards

#### Code Comments
- **Comment Quality**: Comments explain why, not what
- **Documentation Strings**: Functions and classes have docstrings
- **TODO Comments**: TODO items are tracked and managed
- **Comment Accuracy**: Comments reflect actual implementation

### 5. Performance Validation

#### Performance Impact Analysis
```bash
# Analyze potential performance impacts
git diff --cached | grep -E "loop|for|while|recursive|database|query" | head -10

# Check for performance anti-patterns
git diff --cached | grep -E "nested.*loop|O\(n.*n\)|\.findAll|SELECT.*\*" | head -5
```

- **Algorithm Complexity**: No performance regressions introduced
- **Resource Usage**: Memory and CPU impact assessment
- **Database Queries**: Efficient query patterns and indexing
- **Caching Strategy**: Appropriate caching implementations

#### Benchmark Validation
- **Performance Tests**: Benchmarks pass within acceptable limits
- **Load Testing**: System handles expected load patterns
- **Memory Analysis**: No memory leaks or excessive usage
- **Response Time**: API response times within SLA limits

### 6. LEVER Pre-commit Validation

#### Leverage Pre-commit Analysis
- Utilize existing pre-commit hooks and validation tools
- Leverage established coding standards and style guides
- Use existing test frameworks and coverage tools
- Leverage automated security scanning tools

#### Extension Pre-commit Assessment
- Enhance existing validation checks rather than replacing
- Build upon current quality gates and standards
- Extend existing documentation and review processes
- Improve current automated validation pipelines

#### Verification Pre-commit Methods
- Validate that validation catches real issues
- Verify validation performance and execution time
- Test validation accuracy through false positive analysis
- Confirm validation maintainability and updates

#### Elimination Pre-commit Priorities
- Remove redundant and overlapping validation checks
- Eliminate false positive validations
- Remove slow or ineffective validation steps
- Eliminate manual validation that can be automated

#### Reduction Pre-commit Strategies
- Simplify validation configuration and setup
- Reduce validation execution time
- Minimize false positive alerts
- Focus on high-value validation checks

## Validation Execution Strategy

### 1. Fast Validation (< 30 seconds)
- Syntax and format validation
- Basic security scans
- Lint and style checks
- Unit test execution

### 2. Comprehensive Validation (< 5 minutes)
- Full test suite execution
- Integration test validation
- Security vulnerability scanning
- Performance regression testing

### 3. Extended Validation (Background)
- End-to-end test execution
- Load and stress testing
- Comprehensive security auditing
- Documentation generation and validation

## Output Format

### Pre-commit Summary
- Overall validation status and results
- Agent perspective and expertise applied
- Critical issues requiring attention
- LEVER compliance evaluation

### Quality Assessment
- Code quality metrics and standards compliance
- Test coverage analysis and gaps
- Security vulnerability assessment
- Performance impact evaluation

### Issue Report
- Identified issues with severity and impact ratings
- Specific file and line number references
- Remediation suggestions and best practices
- Blocking vs. warning issues classification

### Validation Results
- Passed validation checks and quality gates
- Failed validation checks requiring fixes
- Warning conditions for attention
- Performance metrics and trends

### Implementation Recommendations
- Immediate fixes required before commit
- Suggested improvements for code quality
- Long-term refactoring opportunities
- Process and tooling improvements

### Workflow Integration
- Update workflow state with validation results
- Document validation decisions in knowledge base
- **Auto-trigger knowledge update** on pre-commit validation completion
- Create issue tracking for identified problems
- Schedule follow-up reviews and improvements

## Integration with PIB System

### Workflow Hooks
- Triggers `read-workflow.sh` for LEVER principles reminder
- Updates workflow state through `workflow-transition.sh`
- Logs validation results through `notification-hook.sh`

### Knowledge Management
- Saves validation report to `.ai/validation> /analysis:precommit-[timestamp].md`
- Updates quality metrics and compliance tracking
- Creates issue tracking and remediation plans
- Updates validation standards and best practices

### Follow-up Commands
Pre-commit validation prepares context for:
- `*codereview` - Detailed code review
- `*testgen` - Additional test creation
- `*secaudit` - Security audit for flagged issues
- `*refactor` - Code improvement planning

## Example Usage

```bash
# Comprehensive pre-commit validation
/analysis:precommit . comprehensive

# Quick validation for small changes
/analysis:precommit src/ quick

# Security-focused validation
/analysis:precommit . security

# Performance impact validation
/analysis:precommit src/core/ performance
```

## Quality Gates

All pre-commit validation must meet PIB standards:
- **LEVER Compliance**: Demonstrates leverage, extension, verification, elimination, reduction
- **Quality Focus**: Ensures code quality and maintainability standards
- **Agent Alignment**: Respects current agent validation expertise and perspective
- **Automation Priority**: Maximizes automated validation over manual checks
- **Actionable Output**: Provides specific, implementable fixes and improvements
