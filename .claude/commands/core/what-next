#!/bin/bash

# What Next - Smart Command Suggestions
# Analyzes your current context and suggests the most appropriate PIB-METHOD commands to run next

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Find project root
find_project_root() {
    local dir="$(pwd)"
    while [ "$dir" != "/" ]; do
        if [ -d "$dir/.claude" ]; then
            echo "$dir"
            return 0
        fi
        dir=$(dirname "$dir")
    done
    return 1
}

# Parse command line arguments
parse_arguments() {
    VERBOSE=false
    WORKFLOW_TYPE=""
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --verbose)
                VERBOSE=true
                shift
                ;;
            --workflow)
                WORKFLOW_TYPE="$2"
                shift 2
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_status $RED "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # Validate workflow type
    if [ -n "$WORKFLOW_TYPE" ]; then
        case "$WORKFLOW_TYPE" in
            planning|development|testing|review|debug)
                ;;
            *)
                print_status $RED "Error: Workflow type must be one of: planning, development, testing, review, debug"
                exit 1
                ;;
        esac
    fi
}

show_help() {
    echo "What Next - Smart Command Suggestions"
    echo
    echo "Usage: /what-next [options]"
    echo
    echo "Options:"
    echo "  --verbose                Show detailed explanations"
    echo "  --workflow <type>        Filter for specific workflow"
    echo "  --help                   Show this help"
    echo
    echo "Workflow types: planning, development, testing, review, debug"
    echo
    echo "Examples:"
    echo "  /what-next"
    echo "  /what-next --verbose"
    echo "  /what-next --workflow planning"
}

# Analyze git context
analyze_git_context() {
    local context=""
    
    # Check if in git repo
    if ! git rev-parse --is-inside-work-tree &> /dev/null; then
        context="not_git_repo"
        return
    fi
    
    # Get current branch
    CURRENT_BRANCH=$(git branch --show-current 2>/dev/null || echo "detached")
    
    # Check for uncommitted changes
    if ! git diff --quiet 2>/dev/null || ! git diff --cached --quiet 2>/dev/null; then
        UNCOMMITTED_CHANGES=true
    else
        UNCOMMITTED_CHANGES=false
    fi
    
    # Check for untracked files
    if [ -n "$(git ls-files --others --exclude-standard 2>/dev/null)" ]; then
        UNTRACKED_FILES=true
    else
        UNTRACKED_FILES=false
    fi
    
    # Get last commit time
    LAST_COMMIT_TIME=$(git log -1 --format=%ct 2>/dev/null || echo "0")
    CURRENT_TIME=$(date +%s)
    HOURS_SINCE_COMMIT=$(( (CURRENT_TIME - LAST_COMMIT_TIME) / 3600 ))
    
    # Determine git status
    if [ "$UNCOMMITTED_CHANGES" = true ]; then
        GIT_STATUS="uncommitted_changes"
    elif [ "$UNTRACKED_FILES" = true ]; then
        GIT_STATUS="untracked_files"
    elif [ "$CURRENT_BRANCH" = "main" ] || [ "$CURRENT_BRANCH" = "master" ]; then
        GIT_STATUS="main_branch"
    elif [[ "$CURRENT_BRANCH" == feature/* ]]; then
        GIT_STATUS="feature_branch"
    else
        GIT_STATUS="unknown_branch"
    fi
}

# Analyze planning context
analyze_planning_context() {
    local project_root
    project_root=$(find_project_root)
    
    ACTIVE_PLANNING_SESSIONS=0
    APPROVED_PLANNING_SESSIONS=0
    LATEST_SESSION=""
    
    if [ -n "$project_root" ] && [ -d "$project_root/.claude/state/planning" ]; then
        # Count planning sessions
        if command -v jq &> /dev/null; then
            for session_file in "$project_root"/.claude/state/planning/plan-*.json; do
                if [ -f "$session_file" ]; then
                    local status=$(jq -r '.status' "$session_file" 2>/dev/null || echo "unknown")
                    if [ "$status" = "planning" ] || [ "$status" = "questioning" ]; then
                        ACTIVE_PLANNING_SESSIONS=$((ACTIVE_PLANNING_SESSIONS + 1))
                        LATEST_SESSION=$(basename "$session_file" .json)
                    elif [ "$status" = "approved" ]; then
                        APPROVED_PLANNING_SESSIONS=$((APPROVED_PLANNING_SESSIONS + 1))
                        LATEST_SESSION=$(basename "$session_file" .json)
                    fi
                fi
            done
        fi
    fi
}

# Analyze project context
analyze_project_context() {
    # Check for common project files and determine type
    PROJECT_TYPE="unknown"
    TEST_COMMAND=""
    BUILD_COMMAND=""
    
    if [ -f "package.json" ]; then
        PROJECT_TYPE="node"
        if command -v jq &> /dev/null && [ -f "package.json" ]; then
            local scripts=$(jq -r '.scripts // {} | keys[]' package.json 2>/dev/null || echo "")
            if echo "$scripts" | grep -q "test"; then
                TEST_COMMAND="npm test"
            fi
            if echo "$scripts" | grep -q "build"; then
                BUILD_COMMAND="npm run build"
            fi
        fi
    elif [ -f "Cargo.toml" ]; then
        PROJECT_TYPE="rust"
        TEST_COMMAND="cargo test"
        BUILD_COMMAND="cargo build"
    elif [ -f "go.mod" ]; then
        PROJECT_TYPE="go"
        TEST_COMMAND="go test ./..."
        BUILD_COMMAND="go build"
    elif [ -f "requirements.txt" ] || [ -f "pyproject.toml" ]; then
        PROJECT_TYPE="python"
        TEST_COMMAND="pytest"
    fi
    
    # Check for recent test runs (look for common test output files)
    RECENT_TESTS=false
    for test_file in .coverage coverage.xml test-results.xml .nyc_output; do
        if [ -e "$test_file" ]; then
            local file_age=$(( $(date +%s) - $(stat -c %Y "$test_file" 2>/dev/null || stat -f %m "$test_file" 2>/dev/null || echo 0) ))
            if [ $file_age -lt 7200 ]; then  # 2 hours
                RECENT_TESTS=true
                break
            fi
        fi
    done
}

# Analyze AI work context
analyze_ai_context() {
    ACTIVE_AI_TASKS=0
    
    # This would check GitHub issues assigned to AI assistants
    # For now, we'll use a simplified approach
    if command -v gh &> /dev/null && gh auth status &> /dev/null 2>&1; then
        # Check for issues assigned to AI assistants
        local ai_issues=$(gh issue list --assignee copilot --json number 2>/dev/null | jq length 2>/dev/null || echo 0)
        ACTIVE_AI_TASKS=$((ACTIVE_AI_TASKS + ai_issues))
    fi
}

# Determine workflow stage
determine_workflow_stage() {
    # Logic to determine current workflow stage based on context
    if [ $ACTIVE_PLANNING_SESSIONS -gt 0 ]; then
        WORKFLOW_STAGE="planning"
    elif [ "$UNCOMMITTED_CHANGES" = true ] || [ "$UNTRACKED_FILES" = true ]; then
        WORKFLOW_STAGE="development"
    elif [ "$GIT_STATUS" = "feature_branch" ] && [ "$UNCOMMITTED_CHANGES" = false ]; then
        if [ "$RECENT_TESTS" = false ] && [ -n "$TEST_COMMAND" ]; then
            WORKFLOW_STAGE="testing"
        else
            WORKFLOW_STAGE="review"
        fi
    elif [ "$GIT_STATUS" = "main_branch" ]; then
        WORKFLOW_STAGE="planning"
    else
        WORKFLOW_STAGE="development"
    fi
}

# Generate suggestions based on context
generate_suggestions() {
    local high_priority=()
    local medium_priority=()
    local low_priority=()
    
    # Filter by workflow type if specified
    if [ -n "$WORKFLOW_TYPE" ] && [ "$WORKFLOW_TYPE" != "$WORKFLOW_STAGE" ]; then
        WORKFLOW_STAGE="$WORKFLOW_TYPE"
    fi
    
    case "$WORKFLOW_STAGE" in
        planning)
            if [ $ACTIVE_PLANNING_SESSIONS -eq 0 ]; then
                high_priority+=("plan-session \"<task description>\"" "Start Q-LEVER planning for your next feature")
                medium_priority+=("ai-issue claude \"<task>\" --planning-mode" "Create GitHub issue that starts with questions")
            else
                high_priority+=("clarify-requirements \"<additional context>\"" "Add clarifications to active planning session")
                medium_priority+=("confirm-approach" "Review and approve your current plan")
            fi
            low_priority+=("feature-list" "See all active feature branches")
            ;;
        development)
            if [ "$UNCOMMITTED_CHANGES" = true ]; then
                high_priority+=("git add . && git commit -m \"<commit message>\"" "Save your current work")
            fi
            if [ $APPROVED_PLANNING_SESSIONS -gt 0 ]; then
                high_priority+=("dev --use-plan $LATEST_SESSION" "Implement using your approved plan")
                medium_priority+=("ai-issue claude \"implement feature\" --plan $LATEST_SESSION" "Create GitHub issue with plan context")
            else
                medium_priority+=("plan-session \"<task description>\"" "Plan before implementing to follow Q-LEVER")
                medium_priority+=("dev \"<task description>\"" "Start development task")
            fi
            medium_priority+=("ai-status" "Check progress on AI work")
            ;;
        testing)
            if [ -n "$TEST_COMMAND" ]; then
                high_priority+=("$TEST_COMMAND" "Run tests to validate your implementation")
            fi
            medium_priority+=("ai-assign claude issue-<number> \"analyze test coverage\"" "Get AI help with testing")
            low_priority+=("what-next --workflow review" "Move to review workflow after tests pass")
            ;;
        review)
            high_priority+=("gh pr create --title \"<title>\" --body \"<description>\"" "Create pull request for review")
            medium_priority+=("ai-pr claude <pr_number> \"review for Q-LEVER compliance\"" "Request AI code review")
            low_priority+=("ai-status claude" "Monitor AI review progress")
            ;;
        debug)
            high_priority+=("ai-assign claude issue-<number> \"debug failing tests\"" "Get AI help with debugging")
            medium_priority+=("clarify-requirements \"tests failing - need help\"" "Clarify requirements if expectations unclear")
            ;;
    esac
    
    # Store in global arrays for display
    HIGH_PRIORITY_SUGGESTIONS=("${high_priority[@]}")
    MEDIUM_PRIORITY_SUGGESTIONS=("${medium_priority[@]}")
    LOW_PRIORITY_SUGGESTIONS=("${low_priority[@]}")
}

# Display context analysis
display_context() {
    print_status $WHITE "🎯 PIB-METHOD Workflow Analysis"
    echo
    print_status $CYAN "Current Context:"
    print_status $BLUE "• Branch: $CURRENT_BRANCH"
    
    if [ $ACTIVE_PLANNING_SESSIONS -gt 0 ]; then
        print_status $BLUE "• Planning: $ACTIVE_PLANNING_SESSIONS active session(s)"
    elif [ $APPROVED_PLANNING_SESSIONS -gt 0 ]; then
        print_status $BLUE "• Planning: $LATEST_SESSION (approved)"
    else
        print_status $BLUE "• Planning: No active sessions"
    fi
    
    if [ "$UNCOMMITTED_CHANGES" = true ]; then
        print_status $YELLOW "• Git: Uncommitted changes present"
    elif [ "$UNTRACKED_FILES" = true ]; then
        print_status $YELLOW "• Git: Untracked files present"
    else
        print_status $GREEN "• Git: Clean working directory"
    fi
    
    if [ "$RECENT_TESTS" = true ]; then
        print_status $GREEN "• Tests: Recently run ✅"
    elif [ -n "$TEST_COMMAND" ]; then
        print_status $YELLOW "• Tests: Not run recently"
    else
        print_status $BLUE "• Tests: No test command detected"
    fi
    
    if [ $ACTIVE_AI_TASKS -gt 0 ]; then
        print_status $BLUE "• AI Work: $ACTIVE_AI_TASKS active task(s)"
    else
        print_status $BLUE "• AI Work: No active tasks"
    fi
    
    print_status $MAGENTA "• Workflow Stage: $WORKFLOW_STAGE"
    echo
}

# Display suggestions
display_suggestions() {
    if [ ${#HIGH_PRIORITY_SUGGESTIONS[@]} -gt 0 ]; then
        print_status $RED "🔥 High Priority Actions:"
        for i in $(seq 0 2 $((${#HIGH_PRIORITY_SUGGESTIONS[@]} - 1))); do
            local cmd="${HIGH_PRIORITY_SUGGESTIONS[$i]}"
            local desc="${HIGH_PRIORITY_SUGGESTIONS[$((i + 1))]}"
            local num=$(( (i / 2) + 1 ))
            print_status $WHITE "$num. $cmd"
            if [ "$VERBOSE" = true ]; then
                print_status $YELLOW "   → $desc"
            fi
            echo
        done
    fi
    
    if [ ${#MEDIUM_PRIORITY_SUGGESTIONS[@]} -gt 0 ]; then
        print_status $YELLOW "💡 Medium Priority Actions:"
        for i in $(seq 0 2 $((${#MEDIUM_PRIORITY_SUGGESTIONS[@]} - 1))); do
            local cmd="${MEDIUM_PRIORITY_SUGGESTIONS[$i]}"
            local desc="${MEDIUM_PRIORITY_SUGGESTIONS[$((i + 1))]}"
            local num=$(( (i / 2) + 1 ))
            print_status $WHITE "$num. $cmd"
            if [ "$VERBOSE" = true ]; then
                print_status $CYAN "   → $desc"
            fi
            echo
        done
    fi
    
    if [ ${#LOW_PRIORITY_SUGGESTIONS[@]} -gt 0 ]; then
        print_status $BLUE "📋 Optional Actions:"
        for i in $(seq 0 2 $((${#LOW_PRIORITY_SUGGESTIONS[@]} - 1))); do
            local cmd="${LOW_PRIORITY_SUGGESTIONS[$i]}"
            local desc="${LOW_PRIORITY_SUGGESTIONS[$((i + 1))]}"
            local num=$(( (i / 2) + 1 ))
            print_status $WHITE "$num. $cmd"
            if [ "$VERBOSE" = true ]; then
                print_status $BLUE "   → $desc"
            fi
            echo
        done
    fi
    
    if [ ${#HIGH_PRIORITY_SUGGESTIONS[@]} -eq 0 ] && [ ${#MEDIUM_PRIORITY_SUGGESTIONS[@]} -eq 0 ] && [ ${#LOW_PRIORITY_SUGGESTIONS[@]} -eq 0 ]; then
        print_status $GREEN "✨ Everything looks good! Your workflow is clean."
        print_status $BLUE "Consider starting a new feature with: /plan-session \"<new task>\""
    fi
}

# Main execution
main() {
    parse_arguments "$@"
    
    # Analyze all contexts
    analyze_git_context
    analyze_planning_context
    analyze_project_context
    analyze_ai_context
    determine_workflow_stage
    generate_suggestions
    
    # Display results
    display_context
    display_suggestions
    
    print_status $CYAN "💡 Tip: Run '/what-next --verbose' for detailed explanations"
    print_status $CYAN "📖 Run '/help' for information about any command"
}

# Run main function with all arguments
main "$@"