# PIB Method Help Command

Display the PIB Method command reference guide and available workflows.

## Usage
Display comprehensive help with all PIB commands and workflows:

```
> /core:help
```

Display help for a specific category:
```
> /core:help agents
> /core:help workflows  
> /core:help knowledge
```

## Implementation
Read and display the contents of `.claude/SLASH-COMMANDS-REFERENCE.md` which contains the complete PIB Method Command Reference Guide including:

- Core Orchestrator Commands
- Web-Specific Commands
- Knowledge Management Commands
- Memory Management Commands
- Project Workflow Commands
- Machine-Powered Capabilities
- Common Scenarios and Workflows
- Best Practices

Focus on the most commonly used commands and direct users to specific workflow commands for complex scenarios.

## Key Workflows to Highlight
1. Complete Project Initialization: `init -> help -> /quick:workflow-helps:project-init-init`
2. Module Development: `dev -> help -> /quick:workflow-helps:module-dev-dev`
3. Legacy Module Remediation: `fix -> help -> /quick:workflow-helps:legacy-fix-fix`
4. Competitive Analysis Enhancement: `/competitive-analysis`

## Related Commands
- `> /core:agents` - List all available PIB agent personas
- `> /core:tasks` - List tasks available to current agent
- `help -> /quick:workflow-helps` - Show available workflow commands