# Smart Suggestions

View and manage contextual suggestions based on your project activity.

## Usage

View all active suggestions:
```bash
/core:suggestions
```

View by priority:
```bash
/core:suggestions --priority high
/core:suggestions --priority medium
/core:suggestions --priority low
```

View by category:
```bash
/core:suggestions --type workflow
/core:suggestions --type optimization
/core:suggestions --type learning
```

Dismiss a suggestion:
```bash
/core:suggestions --dismiss <suggestion-id>
```

Clear all suggestions:
```bash
/core:suggestions --clear
```

## How It Works

The smart suggestion system automatically analyzes:

### Project State Detection
- **Setup**: New project without documentation
- **Documented**: Has PRD and architecture
- **Testing**: Active test development
- **Mature**: Established project with history

### Activity Patterns
- **Documentation**: Recent work on docs
- **Frontend**: Component development
- **Backend**: API development
- **Testing**: Test file modifications

### Context Triggers
- **File Changes**: Specific file types modified
- **Command Usage**: Recent command patterns
- **Technology Detection**: Framework usage patterns
- **Module Discovery**: New module creation

## Suggestion Types

### 🔄 Workflow Suggestions
Improve your development workflow:
- "Run project-init to create documentation"
- "Document the user-auth module"
- "Generate API documentation"

### ⚡ Optimization Suggestions
Make your setup more efficient:
- "Create React specialist (used in 15+ files)"
- "Add testing patterns to your library"
- "Update tech stack detection"

### 📚 Learning Suggestions
Discover helpful resources:
- "Check out error handling patterns"
- "Review security patterns for auth"
- "Learn component testing approaches"

## Example Suggestions

### After Creating Multiple React Components
```
💡 Create React Specialist
React is used in 12+ files. Creating a specialist will improve development speed.
Command: /workflows:create-tech-agent "React" --research
```

### After Modifying package.json
```
💡 Update Tech Detection
Dependencies changed. Refresh technology detection for better routing.
Command: /workflows:project-init-tech --research
```

### Working in /modules/auth/
```
💡 Document Module: auth
Create comprehensive documentation for the auth module.
Command: /workflows:module-dev auth
```

## Suggestion Categories

### Priority Levels
- **High**: Important for project setup or critical issues
- **Medium**: Helpful optimizations and improvements
- **Low**: Learning opportunities and nice-to-haves

### Context Awareness
- **File-based**: Triggered by specific file modifications
- **Pattern-based**: Based on usage patterns over time
- **State-based**: Depends on current project state
- **Activity-based**: Recent development activity

## Configuration

Customize suggestions in `.claude/smart-suggestions.json`:

```json
{
  "user_preferences": {
    "suggestion_frequency": "normal",
    "categories_enabled": ["workflow", "optimization", "learning"],
    "dismissed_suggestions": ["react-specialist"],
    "auto_display": true
  }
}
```

### Frequency Options
- **minimal**: Only high-priority suggestions
- **normal**: All relevant suggestions
- **verbose**: Include learning suggestions

### Category Control
- **workflow**: Commands to improve workflow
- **optimization**: Performance and efficiency
- **learning**: Educational resources

## Smart Timing

Suggestions appear at optimal times:
- **After major commands**: project-init, create-tech-agent
- **During file operations**: When patterns detected
- **Periodic reviews**: Weekly summary of opportunities
- **Context switches**: Moving between different work types

## Benefits

1. **Discoverability**: Learn about PIB-METHOD features
2. **Optimization**: Improve your development setup
3. **Consistency**: Follow established patterns
4. **Efficiency**: Automate repetitive documentation tasks
5. **Learning**: Discover best practices

## Privacy

- **Local only**: Suggestions stored in your project
- **Pattern analysis**: Only usage patterns, no content
- **Opt-out available**: Can disable entirely
- **History tracking**: For improving suggestions

This intelligent system learns your patterns and suggests improvements at the right time!