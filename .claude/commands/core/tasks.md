# Tasks Command

List all available tasks for the current agent context or a specific agent.

## Usage
```
> /core:tasks
```

Show tasks for a specific agent:
```
> /core:tasks <agent-name>
```

## Implementation
Based on the current agent context, display available tasks from the `pib-agent> /core:tasks/` directory:

### Core Tasks by Agent:

**Analyst Tasks:**
- Research project requirements
- Analyze existing projects  
- Research competitor features
- Research API capabilities
- Research database options
- Research platform requirements
- Performance analysis

**PM Tasks:**
- Create PRD
- Create module PRD
- Update PRD with modules
- Create UI PRD
- Create API integration PRD
- Create migration PRD
- Create optimization PRD
- Create remediation PRD
- Create enhancement PRD

**Architect Tasks:**
- Create architecture
- Create module architecture
- Document current architecture
- Design API integration
- Design database migration
- Performance optimization plan
- Compare competitor features

**Dev Tasks:**
- Implement stories
- Implement modules
- Run tests
- Code review
- Explain concepts

**QA Tasks:**
- Create test plans
- Run tests
- Create performance test plans

**SM Tasks:**
- Create stories
- Course correction (pivot)
- Story validation checklist
- Document sharding

### Universal Tasks:
- Update agent knowledge
- Memory extraction
- Knowledge validation
- Core dump
- Generate knowledge maps

## Task Execution
Most tasks can be executed with:
```
/core:run-task <task-name>
```

Or use specific agent commands like:
```
/agents:analyst-brief
/agents:pm-prd  
/agents:architect-design
```

## Related Commands
- `> /core:agents` - List available agents
- `agent -> /core:switch-agent-agent` - Switch to different agent
- `> /core:help` - Show command reference