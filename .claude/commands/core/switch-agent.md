# Switch Agent Command

Switch to a specific PIB agent persona with full context loading.

## Usage
```
agent -> /core:switch-agent-agent <agent-name>
```

## Available Agents
- `analyst` - Research and requirements analysis
- `pm` - Product management and PRD creation
- `architect` - System architecture, design, and UI/UX
- `platform-engineer` - Platform infrastructure
- `dev` - Development implementation
- `qa` - Testing and quality assurance
- `devops` - Deployment and infrastructure
- `data-scientist` - Data analysis and insights
- `sm` - Story management
- `po` - Product ownership and organization
- `pib` - Orchestrator and knowledge management

## Implementation
1. Load the appropriate persona file from `pib-core/agents/core/{agent}.md`
2. Read current project context from `.ai/` directory if available
3. Load agent-specific knowledge and constraints
4. Set up agent-specific task availability
5. Greet user with agent identity and available tasks

## Context Loading
When switching agents, automatically load:
- Project context from `.aiinit -> /workflows:project-init-context.md`
- Tech stack from `.ai/tech-stack.md`
- Data models from `.ai/data-models.md`
- Deployment info from `.ai/deployment-info.md`
- Agent-specific checklists from `pib-agentmode -> /quick:check-modelists/`

## Agent-Specific Greetings
Each agent should introduce themselves with:
- Their role and specialization
- Current project context understanding
- Available tasks and commands
- Relevant checklists they follow

## Related Commands
- `> /core:agents` - List all available agents
- `> /core:tasks` - Show tasks for current agent
- `> /core:help` - Show command reference