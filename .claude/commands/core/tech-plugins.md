# Tech Plugins Management

Manage lightweight technology plugins for the universal-dev-agent.

## Usage

List all available plugins:
```bash
/core:tech-plugins
```

Show plugin details:
```bash
/core:tech-plugins --show react-patterns
```

Create new plugin:
```bash
/core:tech-plugins --create "technology-name"
```

## Available Plugins

### Frontend
- **react-patterns**: React 18+ hooks, performance, best practices
- **vue-patterns**: Vue 3 composition API, reactivity
- **angular-patterns**: Angular 15+, RxJS, dependency injection
- **svelte-patterns**: Svelte 4, stores, compilation

### Backend  
- **express-patterns**: Express.js middleware, REST, error handling
- **fastapi-patterns**: FastAPI async, validation, OpenAPI
- **django-patterns**: Django 4+ ORM, admin, middleware
- **rails-patterns**: Rails 7, Active Record, conventions

### Database
- **postgres-patterns**: PostgreSQL optimization, indexes, transactions
- **mongodb-patterns**: MongoDB aggregation, schema design
- **redis-patterns**: Redis caching, pub/sub, data structures

### Testing
- **jest-patterns**: Jest mocking, assertions, coverage
- **pytest-patterns**: Pytest fixtures, parametrization
- **playwright-patterns**: E2E testing, page objects

## Plugin Structure

Each plugin contains:
1. **Core Patterns**: Common implementation patterns
2. **Best Practices**: Framework-specific guidelines  
3. **Anti-Patterns**: What to avoid
4. **LEVER Optimizations**: Reuse and simplification strategies
5. **Code Examples**: Ready-to-use snippets

## Creating New Plugins

### Quick Creation
```bash
/core:tech-plugins --create "tailwindcss"
```

Creates a plugin template for the specified technology.

### Manual Creation
1. Create file: `pib-core/agents/tech-plugins/category/name-patterns.md`
2. Use template structure
3. Add to tech-detection-config.json
4. Test with universal-dev-agent

## Plugin Categories

### By Complexity
- **Lightweight**: CSS frameworks, utility libraries
- **Standard**: Major frameworks, databases
- **Complex**: Multi-component systems

### By Stack Layer
- **Frontend**: UI frameworks, state management
- **Backend**: Server frameworks, APIs
- **Database**: SQL/NoSQL, caching
- **DevOps**: CI/CD, deployment
- **Testing**: Test frameworks, tools

## Integration

Plugins integrate with:
- **Universal Dev Agent**: Automatic loading
- **Tech Detection**: Pattern matching
- **Complexity Assessment**: Routing decisions
- **LEVER Framework**: Optimization strategies

## Benefits

1. **Modularity**: Add technologies without modifying core agent
2. **Performance**: Load only what's needed
3. **Maintainability**: Update patterns independently
4. **Extensibility**: Community-contributed plugins

## Examples

### Using a Plugin
When universal-dev-agent detects React:
1. Loads react-patterns plugin
2. Applies React best practices
3. Uses React-specific LEVER optimizations
4. Generates idiomatic React code

### Plugin in Action
```typescript
// Task: "Add error boundary"
// Plugin: react-patterns
// Result: Proper error boundary with logging

class ErrorBoundary extends Component<Props, State> {
  static getDerivedStateFromError(error: Error) {
    return { hasError: true };
  }
  
  componentDidCatch(error: Error, info: ErrorInfo) {
    logErrorToService(error, info);
  }
  
  render() {
    if (this.state.hasError) {
      return <ErrorFallback />;
    }
    return this.props.children;
  }
}
```

## Maintenance

### Updating Plugins
- Plugins updated based on framework changes
- Community contributions welcome
- Version-specific patterns supported

### Quality Standards
- Each plugin must include examples
- LEVER optimizations required
- Anti-patterns documented
- Testing patterns included