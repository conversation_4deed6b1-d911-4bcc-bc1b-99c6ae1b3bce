# What Next - Smart Command Suggestions

Analyzes your current context and suggests the most appropriate PIB-METHOD commands to run next.

## Usage
```bash
next -> /core:what-next-next [--verbose] [--workflow workflow_type]
```

## Examples
```bash
# Basic context analysis and suggestions
next -> /core:what-next-next

# Detailed analysis with explanations
next -> /core:what-next-next --verbose

# Workflow-specific suggestions
next -> /core:what-next-next --workflow planning
next -> /core:what-next-next --workflow development
next -> /core:what-next-next --workflow testing
```

## What This Command Does

### 1. Context Analysis
Analyzes your current situation by checking:
- **Git status**: Branch, uncommitted changes, recent commits
- **Active planning sessions**: Ongoing Q-LEVER sessions
- **GitHub issues/PRs**: Assigned work and status
- **Project state**: Recent activity and workflow stage
- **Failed processes**: Lint errors, test failures, etc.

### 2. Workflow Detection
Automatically detects which workflow stage you're in:
- **🤔 Planning**: Need to clarify requirements or create plans
- **🚀 Development**: Ready to implement or in progress
- **🔍 Review**: Code ready for review or feedback needed
- **🧪 Testing**: Need to run tests or fix failures
- **🔧 Debugging**: Issues that need investigation
- **📦 Deployment**: Ready to deploy or release

### 3. Smart Suggestions
Provides prioritized command suggestions based on context:
- **High Priority**: Urgent actions (failing tests, blocked work)
- **Medium Priority**: Next logical steps in workflow
- **Low Priority**: Optional improvements or maintenance

## Context Detection Examples

### Planning Phase
```
Current Context: No active planning sessions, new feature request

Suggestions:
🔥 High Priority:
  session -> session -> help -> /quick:workflow-helps:planning-session:plan-sessionning:plan-session "implement user dashboard"
  - You have unplanned work that needs Q-LEVER analysis

💡 Medium Priority:
  /github:ai-issue claude "analyze requirements" --planning-mode
  - Start with GitHub issue for requirement gathering
```

### Development Phase
```
Current Context: Active planning session approved, on feature branch

Suggestions:
🔥 High Priority:
  help -> /quick:workflow-helps:dev-command --use-plan plan-20250125-143022
  - Implement using your approved plan

💡 Medium Priority:
  /github:ai-issue claude "implement auth system" --plan plan-20250125-143022
  - Create GitHub issue with plan context
```

### Review Phase
```
Current Context: Uncommitted changes, no active PR

Suggestions:
🔥 High Priority:
  git add . && git commit -m "feat: implement user auth"
  - Commit your changes first

💡 Medium Priority:
  /github:ai-pr claude <pr_number> "review for Q-LEVER compliance"
  - Request AI review once PR is created
```

### Testing Phase
```
Current Context: New code committed, no recent test runs

Suggestions:
🔥 High Priority:
  npm test (or equivalent test command)
  - Run tests to validate implementation

💡 Medium Priority:
  /github:ai-assign claude pr-123 "analyze test coverage"
  - Get AI analysis of test completeness
```

### Debug Phase
```
Current Context: Test failures detected, error logs present

Suggestions:
🔥 High Priority:
  /github:ai-assign claude issue-456 "debug failing tests"
  - Get AI help with test failures

💡 Medium Priority:
  session -> session -> help -> /quick:workflow-helps:planning-session:plan-sessionning:clarify-requirements "tests failing - need help understanding expected behavior"
  - Clarify requirements if test expectations unclear
```

## Workflow-Specific Suggestions

### Planning Workflow (`--workflow planning`)
- `session -> session -> help -> /quick:workflow-helps:planning-session:plan-sessionning:plan-session` - Start new planning session
- `session -> session -> help -> /quick:workflow-helps:planning-session:plan-sessionning:clarify-requirements` - Add missing context
- `session -> session -> help -> /quick:workflow-helps:planning-session:plan-sessionning:confirm-approach` - Approve completed plans
- `/github:ai-issue --planning-mode` - Create planning issues

### Development Workflow (`--workflow development`)
- `help -> /quick:workflow-helps:dev-command` - Start development tasks
- `help -> /quick:workflow-helps:feature-start` - Create feature branches
- `/github:ai-issue` - Create implementation issues
- `/github:ai-status` - Monitor AI work progress

### Testing Workflow (`--workflow testing`)
- Test commands based on project type
- `/github:ai-assign` - Get AI help with testing
- Coverage analysis suggestions
- Performance testing recommendations

### Review Workflow (`--workflow review`)
- `/github:ai-pr` - Request AI code reviews
- Git commands for PR creation
- `/github:ai-status` - Check review progress
- Quality gate validations

## Parameters
- `--verbose`: Show detailed explanations for each suggestion
- `--workflow`: Filter suggestions for specific workflow type

## Smart Features

### Learning from Patterns
The command learns from your usage patterns:
- **Recent commands**: Suggests logical next steps
- **Project patterns**: Adapts to your project's workflow
- **Time-based**: Different suggestions based on time of day
- **Failure recovery**: Suggests fixes for common issues

### Integration Points
- **Git hooks**: Triggered after commits, merges, etc.
- **CI/CD integration**: Responds to build status
- **GitHub integration**: Knows about issues and PRs
- **Planning sessions**: Tracks Q-LEVER workflow state

### Contextual Intelligence
```bash
# After running session -> session -> help -> /quick:workflow-helps:planning-session:plan-sessionning:plan-session
→ Suggests session -> session -> help -> /quick:workflow-helps:planning-session:plan-sessionning:clarify-requirements or session -> session -> help -> /quick:workflow-helps:planning-session:plan-sessionning:confirm-approach

# After git commit
→ Suggests creating PR or running tests

# After test failures
→ Suggests debugging commands or AI assistance

# After AI assignment
→ Suggests /github:ai-status to monitor progress
```

## Example Outputs

### Busy Development Context
```
🎯 PIB-METHOD Workflow Analysis

Current Context:
• Branch: feature/user-auth-20250125
• Planning: plan-20250125-143022 (approved)
• Git: 3 uncommitted files
• Tests: Last run 2 hours ago
• AI Work: 1 active Claude task

🔥 High Priority Actions:
1. git add . && git commit -m "feat: implement JWT auth middleware"
   → You have uncommitted changes that should be saved

2. npm test
   → Tests haven't run recently, validate your changes

💡 Medium Priority Actions:
3. /github:ai-status claude
   → Check progress on your active AI task

4. /github:ai-pr claude <pr_number> "review auth implementation"
   → Request AI review once you create PR

📋 Optional Actions:
5. next -> /core:what-next-next --workflow testing
   → Get testing-specific suggestions
```

### Clean State Context
```
🎯 PIB-METHOD Workflow Analysis

Current Context:
• Branch: main (clean)
• Planning: No active sessions
• Git: All changes committed
• Tests: All passing ✅
• AI Work: No active tasks

💡 Ready for New Work:
1. session -> session -> help -> /quick:workflow-helps:planning-session:plan-sessionning:plan-session "<new feature description>"
   → Start planning your next feature

2. help -> /quick:workflow-helps:feature-start <feature-name>
   → Create new feature branch for parallel work

3. /github:ai-issue claude "<task>" --planning-mode
   → Create planned GitHub issue

📋 Maintenance Options:
4. /github:ai-status
   → Check if any AI work needs attention

5. git pull origin main
   → Sync with latest changes
```

## Integration with Other Commands

### Auto-suggestions in Other Commands
Many commands will now automatically call `next -> /core:what-next-next`:
- After `session -> session -> help -> /quick:workflow-helps:planning-session:plan-sessionning:confirm-approach` → Suggests implementation commands
- After git commits → Suggests testing or PR creation
- After test failures → Suggests debugging approaches
- After AI task completion → Suggests next workflow steps

### Workflow State Persistence
- Tracks your current workflow stage
- Remembers context between sessions
- Learns from your command patterns
- Adapts suggestions to your preferences

## Related Commands
- `help -> /quick:workflow-helps:workflow-status` - Show detailed current workflow state
- `/core:help` - Get help with specific commands
- `/core:agents` - See available sub-agents for delegation
- All planning, development, and AI commands