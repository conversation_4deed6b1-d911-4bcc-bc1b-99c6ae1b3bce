# Usage Analytics

View project usage analytics to make smart decisions about specialists and patterns.

## Usage

View current analytics:
```bash
/core:usage-analytics
```

View specific category:
```bash
/core:usage-analytics --category technology
/core:usage-analytics --category files
/core:usage-analytics --category commands
/core:usage-analytics --category suggestions
```

Reset analytics:
```bash
/core:usage-analytics --reset
```

## What It Tracks

### Technology Usage
- Frameworks detected in files (React, Express, Django)
- Libraries used across the project
- Patterns referenced
- Usage frequency counts

### File Activity
- Most modified files
- Activity by file extension
- Activity by directory
- Recent changes

### Command Usage
- Most used commands
- Command frequency
- Success rates

### Smart Suggestions
- Technologies that should have specialists
- Patterns that should be added
- Commands worth learning

## Analytics Output

```json
{
  "technology_usage": {
    "frameworks": {
      "react": 45,    // 45 files use React
      "express": 12,  // 12 files use Express
      "jest": 8       // 8 test files
    }
  },
  "suggestions": {
    "create_specialists": ["react"],  // React used enough for specialist
    "patterns_to_add": ["tsx"],       // TypeScript pattern needed
    "commands_to_learn": ["/workflows:dev-command"]
  }
}
```

## Smart Decision Making

### When to Create Specialists
Based on usage analytics:
- **10+ files** using a technology = Create specialist
- **20+ modifications** in a week = High-frequency usage
- **Critical paths** modified often = Specialist needed

### When to Add Patterns
- **20+ files** of same type = Pattern opportunity
- **Repeated code structures** = Extract to pattern
- **Common error handling** = Standardize pattern

### Command Optimization
- **High-usage commands** = Add to quick access
- **Low-usage commands** = Consider deprecation
- **Error-prone commands** = Improve documentation

## Implementation

The analytics system:
1. **Tracks automatically** via hooks
2. **Lightweight collection** - no performance impact
3. **Privacy-focused** - only usage patterns, no content
4. **Actionable insights** - specific recommendations

## Benefits

1. **Data-Driven Decisions**: Create specialists based on actual usage
2. **Optimize Workflows**: Focus on what's actually used
3. **Identify Patterns**: Spot opportunities for standardization
4. **Track Progress**: See how project evolves

## Privacy & Performance

- **No content tracked** - only patterns and counts
- **Local storage only** - stays in your project
- **Minimal overhead** - runs in background
- **Optional tracking** - can be disabled

## Configuration

In `.claude/usage-analytics.json`:
```json
{
  "tracking_enabled": true,
  "exclude_paths": [".git", "node_modules"],
  "technology_thresholds": {
    "specialist_creation": 10,
    "pattern_extraction": 20
  }
}
```

## Use Cases

### New Project Takeover
```bash
/core:usage-analytics
# See what technologies are actually used
# Create specialists for high-usage items
```

### Quarterly Review
```bash
/core:usage-analytics --category suggestions
# Review recommendations
# Clean up unused specialists
# Add new patterns
```

### Performance Optimization
```bash
/core:usage-analytics --category commands
# See which commands are used most
# Optimize frequent workflows
# Simplify complex processes
```

This smart analytics helps you make informed decisions about where to invest effort!