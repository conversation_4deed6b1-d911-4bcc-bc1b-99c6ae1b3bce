# Auto-Generated Summaries

Manage lightweight documentation summaries for faster agent context loading.

## Usage

Generate all summaries:
```bash
/core:summaries --generate
```

View summary status:
```bash
/core:summaries
```

Regenerate specific summary:
```bash
/core:summaries --type project
/core:summaries --type architecture
/core:summaries --type modules
/core:summaries --type patterns
/core:summaries --type technology
```

Clean summaries:
```bash
/core:summaries --clean
```

## Purpose

Auto-summaries provide:
- **70% faster context loading** for agents
- **Reduced token usage** in conversations
- **Quick reference** for key decisions
- **Consistent information access** across agents

## How It Works

### Instead of This (Slow)
```
Agent loads:
- 50-page PRD document
- 30-page architecture doc
- 20 module documentation files
- All pattern libraries
= 100+ pages, 50,000+ tokens
```

### Agents Use This (Fast)
```
Agent loads:
- 1-page project summary
- 1-page architecture key points
- 1-page module overview
- 1-page pattern reference
= 4 pages, 2,000 tokens
```

## Available Summaries

### 📋 Project Summary
- Project purpose and goals
- Key stakeholders
- Major constraints
- Success criteria

### 🏗️ Architecture Summary
- Technology stack decisions
- Key components
- Integration points
- Critical architectural choices

### 📦 Module Summary
- Available modules
- Module responsibilities
- Integration patterns
- Critical dependencies

### 🔧 Patterns Summary
- Available pattern library
- When to use each pattern
- LEVER optimizations
- Quick pattern selection

### 💻 Technology Summary
- Detected technologies
- Agent routing rules
- Specialist assignments
- Usage analytics insights

## Auto-Generation Triggers

Summaries are automatically regenerated when:
- **Source docs change**: PRD, architecture updated
- **New modules added**: Module discovery
- **Tech stack evolves**: New frameworks detected
- **Weekly schedule**: Automatic refresh

## Benefits for Agents

### Universal Dev Agent
- Quick tech stack reference
- Pattern availability check
- Module boundary understanding
- Routing rule lookup

### Specialist Agents
- Project context without full docs
- Integration constraints
- Related technology awareness
- Quality requirements

### Orchestrator Agents
- Module interaction map
- Resource availability
- Technical constraints
- Quality gates

## Smart Context Loading

Agents follow this loading strategy:

```mermaid
graph TD
    A[Agent Task] --> B{Need Context?}
    B -->|Yes| C[Load Summaries]
    C --> D{Enough Info?}
    D -->|Yes| E[Execute Task]
    D -->|No| F[Load Specific Docs]
    F --> E
    B -->|No| E
```

### Example Loading Strategy
```markdown
Task: "Add authentication to user module"

1. Load: modules-summary.md
   - Find: user module exists
   - Note: auth dependencies

2. Load: patterns-summary.md
   - Find: security patterns available
   - Note: JWT implementation pattern

3. Load: technology-summary.md
   - Find: Express + PostgreSQL stack
   - Note: Existing auth middleware

4. Execute: With full context, minimal loading
```

## Performance Impact

### Before Auto-Summaries
- Average context load: 45 seconds
- Token usage: 50,000+ per conversation
- Full doc scan required each time

### After Auto-Summaries
- Average context load: 15 seconds
- Token usage: 2,000-5,000 per conversation
- Targeted doc loading when needed

### 70% Performance Improvement!

## Configuration

Customize summary generation in `.claude/summaries/config.json`:

```json
{
  "auto_generation": true,
  "update_frequency": "weekly",
  "summary_length": "concise",
  "include_examples": false,
  "trigger_on_changes": true
}
```

## Quality Assurance

Auto-summaries include:
- **Source references**: Link to full documentation
- **Generation timestamp**: Know when info was extracted
- **Change detection**: Updates when source changes
- **Completeness check**: Ensure key info captured

## Best Practices

### For Project Teams
1. **Keep source docs updated** - Summaries reflect source quality
2. **Review summaries weekly** - Ensure accuracy
3. **Use for quick decisions** - Don't replace deep analysis
4. **Customize for your needs** - Adjust summary focus

### For Agents
1. **Load summaries first** - Always start with quick context
2. **Load specifics when needed** - Don't over-context
3. **Reference patterns** - Use standardized approaches
4. **Update context** - Refresh when project evolves

This intelligent summarization makes the PIB-METHOD system faster and more efficient!