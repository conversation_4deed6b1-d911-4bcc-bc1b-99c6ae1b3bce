# Agent Creation vs Documentation Retrieval Strategy

## The Smart Decision Framework

The system should intelligently decide when to create a specialized agent versus
just pulling documentation on-demand. Here's the decision framework:

## When to Create a Specialized Agent

### Create an Agent When:

1. **High Frequency Usage** (Used Daily/Weekly)
   - Core framework of the project (React, Express, Django)
   - Primary database system
   - Main testing framework
   - Languages used throughout

2. **Complex Integration Patterns**
   - Framework requires specific architectural patterns
   - Has unique conventions (like Angular's dependency injection)
   - Complex state management (Redux, MobX)
   - Requires deep understanding of lifecycle

3. **Project-Specific Customization**
   - Company has custom patterns on top of framework
   - Modified or extended framework behavior
   - Specific version with unique features
   - Internal libraries or frameworks

4. **Critical Quality Requirements**
   - Security-critical implementations (auth, payments)
   - Performance-critical systems
   - Compliance-required patterns
   - Error-prone areas needing expertise

### Decision Matrix

```
Usage Frequency × Complexity × Criticality = Agent Need Score

High (3) × High (3) × High (3) = 27 → Definitely create agent
High (3) × Low (1) × Low (1) = 3 → Maybe create agent
Low (1) × High (3) × High (3) = 9 → Consider creating agent
Low (1) × Low (1) × Low (1) = 1 → Use documentation only
```

## When to Use Documentation Only

### Pull Docs On-Demand When:

1. **One-Off Usage**
   - Temporary library for specific feature
   - Migration tool used once
   - Experimental library being evaluated
   - Third-party API integration

2. **Simple Libraries**
   - Utility libraries (lodash, date-fns)
   - Simple validation libraries
   - Basic UI component libraries
   - Well-documented standard libraries

3. **Rapidly Changing Libraries**
   - Beta/alpha libraries
   - Frequently breaking changes
   - Better to get latest docs each time

4. **External Services**
   - Third-party APIs (Stripe, Twilio)
   - Cloud services (AWS, Google Cloud)
   - SaaS integrations
   - Latest API changes important

## Implementation Enhancement

### Current State

The universal-dev-agent currently:

- Loads plugins for known technologies
- Falls back to general knowledge
- Doesn't dynamically fetch documentation

### Proposed Enhancement

```typescript
// Enhanced decision logic for universal-dev-agent
async function determineApproach(task: Task, tech: Technology) {
  const usage = await analyzeTechUsage(tech);

  if (usage.frequency === 'high' || usage.complexity === 'high') {
    // Check if specialist exists
    if (await specialistExists(tech)) {
      return 'use-specialist';
    } else if (usage.score > 15) {
      // High score but no specialist
      return 'create-specialist-first';
    }
  }

  // For medium/low usage, check if we can use Context7
  if (await context7HasDocs(tech)) {
    return 'fetch-docs-on-demand';
  }

  // Fallback to general knowledge
  return 'use-general-knowledge';
}
```

### Enhanced Workflow

```mermaid
graph TD
    A[Task Received] --> B{Detect Technology}
    B --> C{Check Usage Pattern}
    C -->|High Frequency| D{Specialist Exists?}
    C -->|Low Frequency| E{Context7 Has Docs?}
    D -->|Yes| F[Use Specialist]
    D -->|No| G{Worth Creating?}
    G -->|Yes| H[Create Specialist First]
    G -->|No| E
    E -->|Yes| I[Fetch Docs & Execute]
    E -->|No| J[Use General Knowledge]
```

## Practical Examples

### Example 1: React in Main Project

- **Usage**: Every component file (HIGH)
- **Complexity**: Hooks, performance, patterns (HIGH)
- **Critical**: User interface (MEDIUM)
- **Decision**: CREATE SPECIALIST ✓

### Example 2: Moment.js for Date Formatting

- **Usage**: Few utility functions (LOW)
- **Complexity**: Simple API (LOW)
- **Critical**: Display only (LOW)
- **Decision**: USE DOCUMENTATION ✓

### Example 3: Stripe Payment Integration

- **Usage**: Payment module only (MEDIUM)
- **Complexity**: Complex API (HIGH)
- **Critical**: Payments (HIGH)
- **Decision**: CONSIDER SPECIALIST (or fetch latest docs)

### Example 4: Internal Company Framework

- **Usage**: Every module (HIGH)
- **Complexity**: Custom patterns (HIGH)
- **Critical**: Core business (HIGH)
- **Decision**: DEFINITELY CREATE SPECIALIST ✓

## Configuration in CLAUDE.md

Add usage patterns to help decide:

```markdown
## Technology Usage Patterns

### High Frequency (Create Specialists)

- React - Used in all UI components
- Express - All API endpoints
- PostgreSQL - Primary database

### Medium Frequency (Consider Specialists)

- Redis - Caching layer
- Jest - Testing framework
- Stripe - Payment processing

### Low Frequency (Documentation Only)

- CSV Parser - Data import only
- PDFKit - Report generation
- Nodemailer - Email sending
```

## Benefits of This Approach

1. **Efficiency**: Don't create unnecessary specialists
2. **Freshness**: Always get latest docs for external APIs
3. **Performance**: Specialists for frequently used tech
4. **Flexibility**: Adapt to project needs
5. **Cost-Effective**: Reduce agent proliferation

## Implementation Guidelines

### For Universal Dev Agent

1. Check CLAUDE.md for usage patterns
2. Analyze file count using the technology
3. Consider task frequency
4. Make intelligent decision
5. Log decision for future reference

### For Project Teams

1. Document usage patterns in CLAUDE.md
2. Create specialists for core technologies
3. Let one-off tasks use documentation
4. Review and adjust quarterly

## Automatic Usage Analysis

The system could analyze:

```bash
# Count files using React
find . -name "*.tsx" -o -name "*.jsx" | wc -l
# Result: 150+ files = HIGH usage

# Count Express route files
find . -path "*/routes/*" -name "*.js" | wc -l
# Result: 20+ files = HIGH usage

# Count PDF generation files
grep -r "PDFDocument" --include="*.js" | wc -l
# Result: 2 files = LOW usage
```

## Summary

The system should be smart about when to invest in creating a specialized agent
versus just fetching documentation:

- **Create agents** for: Core technologies, complex frameworks, high-frequency
  usage
- **Use documentation** for: One-off libraries, simple utilities, external APIs
- **Hybrid approach**: Fetch docs but cache patterns for medium usage

This ensures efficient use of resources while maintaining high-quality
implementations!
