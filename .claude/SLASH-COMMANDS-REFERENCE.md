# PIB-METHOD Slash Commands Reference

## Overview

All PIB-METHOD slash commands use the `namespace:command` format. This ensures
commands appear in autocomplete and are properly organized.

## Command Format

```
/namespace:command [arguments]
```

## Complete Command Reference

### Core Commands (`/core:`)

- `/core:help` - Display comprehensive help
- `/core:agents` - List available agent personas
- `/core:tasks` - Show tasks for current agent
- `/core:switch-agent <name>` - Switch to specific agent
- `/core:what-next` - Get contextual command suggestions
- `/core:lever-check` - Check LEVER compliance
- `/core:enable-lever-gates` - Enable LEVER quality gates
- `/core:ultrathink-task` - Ultra-deep thinking task
- `/core:run-task <task>` - Run a specific task

### Analysis Commands (`/analysis:`)

- `/analysis:analyze` - General code analysis
- `/analysis:thinkdeep` - Deep research and analysis
- `/analysis:codereview` - Code review workflow
- `/analysis:debug` - Debug assistance
- `/analysis:refactor` - Refactoring suggestions
- `/analysis:secaudit` - Security audit
- `/analysis:testgen` - Test generation
- `/analysis:docgen` - Documentation generation
- `/analysis:precommit` - Pre-commit checks
- `/analysis:analyze-competitor-ux` - Competitor UX analysis
- `/analysis:compare-competitor-features` - Feature comparison
- `/analysis:review` - General review command

### Workflows Commands (`/workflows:`)

- `/workflows:dev-command` - Main development workflow
- `/workflows:project-init` - Initialize new project
- `/workflows:project-init-tech-init-tech` - Initialize with technology
  detection
- `/workflows:create-tech-agent-create-agent` - Create technology-specific agent
- `/workflows:module-dev` - Develop new module
- `/workflows:legacy-fix` - Fix legacy code
- `/workflows:feature-start` - Start new feature branch
- `/workflows:feature-list` - List active features
- `/workflows:feature-merge` - Merge feature branch
- `/workflows:feature-pr` - Create pull request
- `/workflows:feature-cleanup` - Clean up feature
- `/workflows:correct-course` - Course correction
- `/workflows:create-deployment-plan` - Deployment planning
- `/workflows:create-enhancement-prd` - Enhancement PRD
- `/workflows:create-infrastructure-architecture` - Infrastructure design
- `/workflows:create-uxui-spec` - UX/UI specification
- `/workflows:epic-to-stories` - Convert epic to stories
- `/workflows:lever-workflow` - LEVER workflow
- `/workflows:review-infrastructure` - Infrastructure review
- `/workflows:spawn-subagents` - Spawn sub-agents
- `/workflows:sub-agent-coordination` - Coordinate agents
- `/workflows:switch-mode` - Switch workflow mode
- `/workflows:validate-infrastructure` - Validate infrastructure
- `/workflows:planning-session` - Planning session
- `/workflows:workflow-status` - Check workflow status

### Planning Commands (`/planning:`)

- `/planning:plan-session` - Start planning session
- `/planning:clarify-requirements` - Clarify requirements
- `/planning:confirm-approach` - Confirm approach

### GitHub Commands (`/github:`)

- `/github:ai-issue` - Create AI-assigned issue
- `/github:ai-pr` - AI pull request review
- `/github:ai-assign` - Assign AI to issue/PR
- `/github:ai-status` - Check AI work status
- `/github:setup-claude-actions` - Setup GitHub Actions

### Agent Commands (`/agents:`)

- `/agents:analyst-brief` - Analyst briefing
- `/agents:architect-design` - Architecture design
- `/agents:pm-orchestrate` - PM orchestration
- `/agents:pm-prd` - Create PRD
- `/agents:bill-orchestrate` - Bill orchestration
- `/agents:tech-lead` - Technology specialist orchestration

### Knowledge Commands (`/knowledge:`)

- `/knowledge:update-knowledge` - Update knowledge base
- `/knowledge:memory-extract` - Extract memory

### Sessions Commands (`/sessions:`)

- `/sessions:session-start` - Start session
- `/sessions:session-end` - End session
- `/sessions:session-list` - List sessions
- `/sessions:session-current` - Current session info
- `/sessions:session-report` - Session report
- `/sessions:session-continue` - Continue session
- `/sessions:session-help` - Session help
- `/sessions:session-updates` - Session updates

### Quick Commands (`/quick:`)

- `/quick:check-mode` - Check current mode
- `/quick:dev-mode` - Development mode
- `/quick:doc-out` - Documentation output
- `/quick:prompt-enhancer` - Enhance prompts
- `/quick:test-knowledge-update` - Test knowledge update
- `/quick:workflow-help` - Workflow help

### Testing Commands (`/testing:`)

- `/testing:test-config` - Test configuration
- `/testing:test-headless` - Headless testing
- `/testing:test-visible` - Visible testing
- `/testing:test-login` - Login testing
- `/testing:test-register` - Registration testing
- `/testing:test-accessibility` - Accessibility testing
- `/testing:test-performance` - Performance testing
- `/testing:test-visual-regression` - Visual regression
- `/testing:test-mobile` - Mobile testing
- `/testing:test-port` - Port testing
- `/testing:headless-mode` - Enable headless mode
- `/testing:visible-mode` - Enable visible mode
- `/testing:save-workflow` - Save test workflow
- `/testing:record-workflow` - Record workflow
- `/testing:run-workflow` - Run workflow
- `/testing:list-workflows` - List workflows
- `/testing:update-workflow` - Update workflow
- `/testing:delete-workflow` - Delete workflow
- `/testing:export-workflow` - Export workflow
- `/testing:import-workflow` - Import workflow
- `/testing:workflow-status` - Workflow status
- `/testing:discover-routes` - Discover routes

### Orchestration Commands (`/orchestration:`)

- `/orchestration:dev-orchestrator` - Development orchestration

### Template Commands (`/template:`)

- `/template:command-skeleton` - Command template

### Credentials Commands (`/credentials:`)

- `/credentials:save-credentials` - Save credentials
- `/credentials:list-credentials` - List credentials
- `/credentials:delete-credentials` - Delete credentials
- `/credentials:test-auth` - Test authentication

## Common Workflows

### Starting a New Project

```bash
/workflows:project-init
```

### Adding a Feature

```bash
/workflows:feature-start my-feature
/workflows:dev-command "implement feature X"
/workflows:feature-merge
```

### Getting Help

```bash
/core:help
/core:what-next
/core:agents
```

### Code Review

```bash
/analysis:codereview
/core:lever-check
```

### Planning Session

```bash
/planning:plan-session "new feature"
/planning:clarify-requirements "what about X?"
/planning:confirm-approach
```

## Tips

1. **Always use namespace:command format** - This ensures commands appear in
   autocomplete
2. **Use `/core:what-next`** - Get contextual suggestions for next steps
3. **Check LEVER compliance** - Use `/core:lever-check` regularly
4. **Start with planning** - Use `/planning:plan-session` for new work
5. **Leverage sub-agents** - Different agents have different capabilities

## Troubleshooting

### Command Not Found

- Ensure you're using the correct namespace
- Check spelling and format: `/namespace:command`
- Use `/core:help` to find the right command

### Autocomplete Not Working

- Commands must use namespace:command format
- Try typing the namespace first: `/core:`
- Restart your editor if needed

### Wrong Suggestions from `/core:what-next`

- The command analyzes your current context
- Ensure git status is up to date
- Check active planning sessions

## Migration from Old Format

| Old Format       | New Format                 |
| ---------------- | -------------------------- |
| `/dev`           | `/workflows:dev-command`   |
| `/feature-start` | `/workflows:feature-start` |
| `/plan-session`  | `/planning:plan-session`   |
| `/ai-issue`      | `/github:ai-issue`         |
| `/help`          | `/core:help`               |
| `/agents`        | `/core:agents`             |
| `/analyze`       | `/analysis:analyze`        |

## Related Documentation

- `.claude/commands/` - Command implementation files
- `.claude/agents/` - Sub-agent definitions
- `CLAUDE.md` - Main system documentation
