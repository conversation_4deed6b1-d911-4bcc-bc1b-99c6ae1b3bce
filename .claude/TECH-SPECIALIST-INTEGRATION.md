# Technology Specialist Integration Guide

## How Technology Specialists Work in PIB-METHOD

Technology specialists integrate seamlessly into existing PIB-METHOD workflows and hooks, providing framework-specific expertise while maintaining quality standards.

## Integration Points

### 1. **Automatic Detection & Routing**

#### Tech Specialist Router Hook
The `tech-specialist-router.sh` hook automatically:
- Detects technology stack from file patterns and content
- Routes tasks to appropriate specialists
- Creates routing instructions for Claude Code
- Integrates with existing orchestration hooks

#### Detection Methods
```bash
# File pattern detection
/src/components/*.jsx → react-specialist
/app/Http/Controllers/*.php → laravel-specialist
/src/api/*.js → express-specialist

# Content pattern detection
import React, { useState } → react-specialist
from django.models import → django-specialist
app.get('/api', → express-specialist
```

### 2. **Workflow Command Integration**

#### Dev Command Enhancement
The `/workflows:dev-command` now includes:
- Technology stack detection phase
- Specialist routing based on task context
- Enhanced work specifications with tech-specific guidance

```bash
> /workflows:dev-command "create user profile component"

[Tech Detection]
File: /src/components/UserProfile.jsx
Technology: React 18
Routing to: @react-specialist
```

#### Project Init Integration
During project initialization:
- Phase 0 detects technology stack
- Creates specialist assignments in CLAUDE.md
- Sets up routing rules for the project

### 3. **Hook System Integration**

#### Hook Execution Order
1. **tech-specialist-router.sh** - Detects and routes to specialists
2. **agent-orchestration-hook.sh** - Manages workflow transitions
3. **dev-workflow-enhancer.sh** - Enhances context for development
4. **Other hooks** - Continue normal execution

#### Specialist Routing in Hooks
```javascript
// Enhanced JSON passed between hooks
{
  "event": "PreToolUse",
  "toolName": "Edit",
  "params": { "file_path": "/src/components/Button.jsx" },
  "techSpecialist": {
    "recommended": "react-specialist",
    "operation": "modify",
    "autoRoute": true
  }
}
```

### 4. **Sub-Agent Delegation**

#### Automatic Specialist Selection
Claude Code automatically delegates to specialists when:
- File patterns match specialist expertise
- Task description includes technology keywords
- Project configuration specifies routing rules

#### Manual Specialist Invocation
```bash
# Explicit specialist request
> Use the react-specialist sub-agent to create a custom hook

# Task with automatic routing
> Create a new API endpoint for user management
[Automatically routes to express-specialist]
```

### 5. **Quality Gate Integration**

#### Specialist Review Process
1. **Implementation**: Tech specialist creates code
2. **Review**: Code-reviewer validates with tech context
3. **Feedback**: Tech specialist addresses feedback
4. **Verification**: QA-tester validates functionality

#### LEVER Compliance
All specialists maintain LEVER standards:
- Research existing patterns before creating
- Extend current functionality when possible
- Include verification mechanisms
- Eliminate duplication
- Reduce complexity

### 6. **Context Preservation**

#### Specialist Context Package
Each specialist receives:
```markdown
## Task Context
- Original request
- File technology detection
- Project tech stack
- LEVER compliance requirements
- Related patterns from Context7

## Routing Information
- Previous agent: dev-agent
- Current specialist: react-specialist
- Next step: code-reviewer
```

### 7. **JIT Tool Selection Integration**

#### Specialist-Aware Tool Selection
The JIT tool selector considers:
- Specialist type when selecting MCP tools
- Technology-specific tool preferences
- Framework documentation needs

```bash
# React specialist gets Context7 for React docs
# Express specialist gets Context7 for Express docs
# All specialists maintain standard tool access
```

## Usage Examples

### Example 1: React Component Development
```bash
> /workflows:dev-command "create a reusable modal component"

[Workflow]
1. Tech detection: React project detected
2. Route to: react-specialist
3. Specialist creates component with:
   - React 18 best practices
   - Proper hook usage
   - Accessibility features
   - Performance optimization
```

### Example 2: API Endpoint Creation
```bash
> Edit /src/api/users.js

[Hook Activation]
1. tech-specialist-router detects Express.js
2. Creates routing instruction for express-specialist
3. Specialist handles with:
   - RESTful conventions
   - Middleware patterns
   - Error handling
   - Security best practices
```

### Example 3: Database Migration
```bash
> Create a new migration for user roles

[Automatic Routing]
1. Context indicates database work
2. Project uses PostgreSQL
3. Routes to postgresql-expert
4. Expert creates optimized migration
```

## Configuration

### Enhanced CLAUDE.md Format
```markdown
## Technology Stack
- Frontend: React 18, TypeScript
- Backend: Express.js, Node.js
- Database: PostgreSQL, Redis

## Agent Assignments
- /src/components/* → @react-specialist
- /src/hooks/* → @react-specialist
- /src/api/* → @express-specialist
- /src/db/* → @postgresql-expert

## Workflow Rules
- "create component" → @react-specialist → @code-reviewer
- "add endpoint" → @express-specialist → @qa-tester
- "optimize query" → @postgresql-expert → @performance-optimizer
```

### Settings Configuration
In `.claude/settings.dev.json`:
```json
{
  "hooks": {
    "PreToolUse": [
      {
        "matcher": "^(Write|Edit|MultiEdit|Read)$",
        "hooks": [
          {
            "type": "command",
            "command": "... tech-specialist-router.sh ..."
          }
        ]
      }
    ]
  }
}
```

## Benefits

### 1. **Automatic Excellence**
- No manual routing needed
- Framework best practices by default
- Consistent code quality

### 2. **Seamless Integration**
- Works with existing workflows
- Enhances current processes
- No disruption to development

### 3. **Technology Expertise**
- Deep framework knowledge
- Current best practices
- Idiomatic code generation

### 4. **Quality Maintenance**
- LEVER compliance preserved
- PIB standards upheld
- Enhanced review process

## Troubleshooting

### Specialist Not Triggering
1. Check CLAUDE.md has technology stack defined
2. Verify file patterns match routing rules
3. Ensure hooks are properly configured
4. Check tech-detection.log for routing decisions

### Wrong Specialist Selected
1. Update CLAUDE.md with correct stack
2. Add explicit routing rules
3. Use manual specialist invocation

### Integration Issues
1. Verify hook execution order
2. Check JSON enhancement between hooks
3. Review workflow state management
4. Ensure specialist agents exist

## Future Enhancements

### Planned Features
1. **Auto-learning**: Specialists learn from project patterns
2. **Cross-specialist collaboration**: Multiple specialists on complex tasks
3. **Performance tracking**: Measure specialist effectiveness
4. **Custom specialists**: Project-specific specialist creation

### Extension Points
- Add new specialists as needed
- Customize routing rules per project
- Integrate with CI/CD pipelines
- Enhanced metrics and analytics

This integration ensures technology specialists enhance rather than replace existing PIB-METHOD workflows, providing the best of both worlds: deep technical expertise with proven quality processes.