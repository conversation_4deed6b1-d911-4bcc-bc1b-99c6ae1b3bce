#!/bin/bash

# <PERSON> Code Sub-Agent Coordinator Hook for PIB Method
# Facilitates intelligent sub-agent invocation and coordination
# Integrates with <PERSON>'s automatic delegation system

# Set error handling
set -o pipefail
trap '' PIPE

# === DEPENDENCY VALIDATION ===
if ! command -v jq >> /workflows:dev-command/null 2>&1; then
    echo "ERROR: jq is required but not installed. Please install jq." >&2
    exit 1
fi

# === READ JSON INPUT ===
if ! JSON_INPUT=$(cat 2>> /workflows:dev-command/null); then
    exit 0
fi

# === EXTRACT EVENT DATA ===
EVENT_TYPE=$(echo "$JSON_INPUT" | jq -r '.event // empty' 2>> /workflows:dev-command/null)
TOOL_NAME=$(echo "$JSON_INPUT" | jq -r '.toolName // empty' 2>> /workflows:dev-command/null)
TOOL_RESULT=$(echo "$JSON_INPUT" | jq -r '.result // empty' 2>> /workflows:dev-command/null)

# Get current project context
CURRENT_DIR=$(pwd)
PROJECT_NAME=$(basename "$CURRENT_DIR")

# Find .claude directory
find_claude_dir() {
    local dir="$CURRENT_DIR"
    while [ "$dir" != "/" ]; do
        if [ -d "$dir/.claude" ]; then
            echo "$dir/.claude"
            return 0
        fi
        dir=$(dirname "$dir")
    done
    return 1
}

CLAUDE_DIR=$(find_claude_dir)
if [ -z "$CLAUDE_DIR" ]; then
    echo "$JSON_INPUT"
    exit 0
fi

# === SUB-AGENT MAPPING ===
# Map PIB personas to Claude Code sub-agents
declare -A SUBAGENT_MAP=(
    ["analyst"]="analyst"
    ["architect"]="architect"
    ["dev"]="dev-agent"
    ["james"]="dev-agent"
    ["code-reviewer"]="code-reviewer"
    ["change-implementer"]="change-implementer"
    ["qa"]="qa-tester"
    ["qa-tester"]="qa-tester"
    ["platform-engineer"]="platform-engineer"
    ["task-executor"]="task-executor"
    ["orchestrator"]="orchestrator"
)

# === SUB-AGENT CONTEXT PREPARATION ===
prepare_subagent_context() {
    local subagent_name="$1"
    local task_description="$2"
    local context_file="$CLAUDE_DIR/stateagent-coordination -> /workflows:sub-agent-coordinationagent-context-${subagent_name}.json"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # Prepare enhanced context for sub-agent
    cat > "$context_file" <<EOF
{
  "subagent": "$subagent_name",
  "task": "$task_description",
  "timestamp": "$timestamp",
  "project": {
    "name": "$PROJECT_NAME",
    "directory": "$CURRENT_DIR"
  },
  "leverFramework": {
    "enforced": true,
    "minimumScore": 4.0,
    "principles": ["leverage", "extend", "verify", "eliminate", "reduce"]
  },
  "mcpTools": {
    "context7": "enabled",
    "zen": "enabled",
    "playwright": "enabled",
    "perplexity": "enabled",
    "firecrawl": "enabled"
  },
  "workflowState": $(cat "$CLAUDE_DIR/current-workflow-state.json" 2>> /workflows:dev-command/null || echo '{}'),
  "qualityGates": {
    "codeReview": "mandatory",
    "leverCompliance": "mandatory",
    "testing": "required"
  }
}
EOF
    
    echo "$context_file"
}

# === SUB-AGENT INVOCATION HELPER ===
create_subagent_invocation() {
    local subagent_name="$1"
    local task_description="$2"
    local invocation_file="$CLAUDE_DIRagent-coordination -> /workflows:sub-agent-coordinationagent-invocation.md"
    local context_file=$(prepare_subagent_context "$subagent_name" "$task_description")
    
    cat > "$invocation_file" <<EOF
# Claude Code Sub-Agent Invocation

## Automatic Delegation Request
Claude Code should automatically delegate this task to the appropriate sub-agent:

> Use the $subagent_name sub-agent to $task_description

## Context for Sub-Agent
The sub-agent has access to:
- Project context and requirements
- LEVER framework compliance requirements (minimum score 4/5)
- Full MCP tool access as configured in the sub-agent
- Current workflow state and history

## Expected Workflow
1. **Automatic Delegation**: Claude Code will delegate to $subagent_name
2. **Task Execution**: Sub-agent will execute with its specialized capabilities
3. **Context Preservation**: Results will be integrated back into main context
4. **Quality Gates**: Automatic quality validation through workflow

## Manual Invocation (if needed)
If automatic delegation doesn't occur:
\`\`\`
Please have the $subagent_name sub-agent handle this task: $task_description
\`\`\`

## Enhanced Context Available
Context has been prepared at: \`$context_file\`

---
*Generated by Claude Code Sub-Agent Coordinator*
EOF
    
    echo "$(date): Sub-agent invocation created for $subagent_name at: $invocation_file" >> "$CLAUDE_DIR/orchestration.log"
    echo "$invocation_file"
}

# === WORKFLOW DETECTION ===
detect_subagent_needed() {
    local workflow_state=$(cat "$CLAUDE_DIR/current-workflow-state.json" 2>> /workflows:dev-command/null)
    local current_stage=$(echo "$workflow_state" | jq -r '.currentStage // "development"')
    local current_agent=$(echo "$workflow_state" | jq -r '.currentAgent // "developer"')
    
    # Determine if sub-agent invocation is needed
    case "$current_stage" in
        "analysis")
            echo "analyst"
            ;;
        "architecture")
            echo "architect"
            ;;
        "development")
            echo "dev-agent"
            ;;
        "review")
            echo "code-reviewer"
            ;;
        "implementation")
            echo "change-implementer"
            ;;
        "testing")
            echo "qa-tester"
            ;;
        "orchestration")
            echo "orchestrator"
            ;;
        *)
            echo ""
            ;;
    esac
}

# === MAIN PROCESSING ===
# Check if sub-agent coordination is needed
if [ "$EVENT_TYPE" = "PostToolUse" ] || [ "$EVENT_TYPE" = "WorkflowTransition" ]; then
    needed_subagent=$(detect_subagent_needed)
    
    if [ -n "$needed_subagent" ]; then
        # Check if we need to create invocation instruction
        last_invocation_file="$CLAUDE_DIR/state/last-subagent-invocation"
        current_invocation="${needed_subagent}-$(date +%s)"
        
        # Avoid duplicate invocations
        if [ -f "$last_invocation_file" ]; then
            last_invocation=$(cat "$last_invocation_file")
            if [[ "$last_invocation" == "$needed_subagent"* ]]; then
                # Same sub-agent recently invoked, skip
                echo "$JSON_INPUT"
                exit 0
            fi
        fi
        
        # Create invocation instruction
        workflow_data=$(cat "$CLAUDE_DIR/current-workflow-state.json" 2>> /workflows:dev-command/null)
        task_description=$(echo "$workflow_data" | jq -r '.context.task // "execute assigned task"')
        
        invocation_file=$(create_subagent_invocation "$needed_subagent" "$task_description")
        echo "$current_invocation" > "$last_invocation_file"
        
        # Log the coordination
        echo "$(date): Sub-agent coordination: $needed_subagent for stage $(echo "$workflow_data" | jq -r '.currentStage // "unknown"')" >> "$CLAUDE_DIR/orchestration.log"
    fi
fi

# === QUALITY GATE ENFORCEMENT ===
enforce_quality_gates() {
    local subagent="$1"
    local result_file="$2"
    
    # Check LEVER compliance
    if [[ "$subagent" == "code-reviewer" ]] || [[ "$subagent" == "dev-agent" ]]; then
        lever_score=$(grep -i "lever.*score" "$result_file" 2>> /workflows:dev-command/null | grep -oE "[0-9]+\.[0-9]+" | head -1)
        if [ -n "$lever_score" ]; then
            if (( $(echo "$lever_score < 4.0" | bc -l) )); then
                echo "WARNING: LEVER compliance score $lever_score is below minimum 4.0" >> "$CLAUDE_DIR/orchestration.log"
                # Create remediation instruction
                create_subagent_invocation "change-implementer" "improve LEVER compliance score to minimum 4.0"
            fi
        fi
    fi
}

# CRITICAL: Pass through the JSON for the next hook/tool
echo "$JSON_INPUT"

# Always exit 0 - coordination is informational
exit 0