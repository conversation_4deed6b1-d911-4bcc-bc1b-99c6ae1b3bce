#!/bin/bash

# Auto Knowledge Update Hook
# Detects major workflow completions and triggers knowledge updates
# Claude Code compatible - reads JSON from stdin

# Read JSON input from stdin (required for Claude Code hooks)
JSON_INPUT=$(cat)

# Extract tool information from JSON
TOOL_NAME=$(echo "$JSON_INPUT" | jq -r '.toolName // empty' 2>> /workflows:dev-command/null)
TOOL_ARGS=$(echo "$JSON_INPUT" | jq -r '.arguments // empty' 2>> /workflows:dev-command/null)

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CLAUDE_DIR="$(dirname "$SCRIPT_DIR")"

KNOWLEDGE_LOG="$CLAUDE_DIR/knowledge-update.log"

# Function to log knowledge update events
log_knowledge() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" >> "$KNOWLEDGE_LOG"
}

# Function to check if knowledge update is needed
should_update_knowledge() {
    local tool_name="$1"
    local context="$2"
    
    # Major workflow completion triggers
    case "$tool_name" in
        # Workflow commands that generate significant knowledge
        "project-init"|"module-dev"|"legacy-fix")
            return 0
            ;;
        # Architecture and design commands
        "architect-design"|"architect-create")
            return 0
            ;;
        # Quality gate completions
        "codereview"|"secaudit"|"analyze")
            # Only update if this was a comprehensive analysis
            if echo "$context" | grep -q "complete\|finished\|analysis complete"; then
                return 0
            fi
            ;;
        # Feature development completions
        "feature-merge"|"feature-cleanup")
            return 0
            ;;
        # PM/Orchestration completions
        "pm-prd"|"bill-orchestrate")
            return 0
            ;;
        *)
            return 1
            ;;
    esac
}

# Function to execute knowledge update
execute_knowledge_update() {
    local trigger_reason="$1"
    local tool_name="$2"
    
    log_knowledge "Auto-update triggered: $trigger_reason (from $tool_name)"
    
    # Check if we have the necessary files
    if [ ! -f "pib-agent> /core:tasksdeployment-plan -> /workflows:create-deployment-plan-knowledge-files.js" ]; then
        log_knowledge "Knowledge update skipped - create-knowledge-files.js not found"
        return 1
    fi
    
    # Check if docs directory exists (needed for knowledge extraction)
    if [ ! -d "docs" ]; then
        log_knowledge "Knowledge update skipped - docs directory not found"
        return 1
    fi
    
    echo "📚 Auto-updating PIB agent knowledge base..."
    echo "   Trigger: $trigger_reason"
    
    # Execute knowledge update in background to avoid blocking
    (
        # Run the knowledge file creation
        if node pib-agent> /core:tasksdeployment-plan -> /workflows:create-deployment-plan-knowledge-files.js > "$CLAUDE_DIR/knowledge-update-output.log" 2>&1; then
            log_knowledge "Knowledge update completed successfully"
            echo "✅ Agent knowledge base updated"
            
            # Create completion marker
            echo "{
  \"status\": \"completed\",
  \"trigger\": \"$trigger_reason\",
  \"timestamp\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\",
  \"tool\": \"$tool_name\"
}" > "$CLAUDE_DIR/last-knowledge-update.json"
        else
            log_knowledge "Knowledge update failed - check output log"
            echo "⚠️  Knowledge update failed - check $CLAUDE_DIR/knowledge-update-output.log"
        fi
    ) &
}

# Function to check for completion indicators in conversation
detect_workflow_completion() {
    local tool_name="$1"
    
    # Check for completion keywords that indicate a workflow finished
    completion_keywords="complete|completed|finished|done|ready|implementation complete|analysis complete|review complete|architecture complete|design complete"
    
    # Look for completion indicators in recent conversation context
    # This is a heuristic approach since we don't have access to full conversation
    if echo "$JSON_INPUT" | grep -E -i "$completion_keywords" > > /workflows:dev-command/null; then
        return 0
    fi
    
    # Specific tool completion patterns
    case "$tool_name" in
        # These tools typically indicate completion when called
        "feature-merge"|"feature-cleanup"|"project-init")
            return 0
            ;;
        # These might be exploratory, check for completion context
        "analyze"|"codereview"|"secaudit")
            if echo "$JSON_INPUT" | grep -E -i "final|summary|conclusion|recommendation" > > /workflows:dev-command/null; then
                return 0
            fi
            ;;
    esac
    
    return 1
}

# Main logic
if [ -n "$TOOL_NAME" ]; then
    # Check if this tool warrants a knowledge update
    if should_update_knowledge "$TOOL_NAME" "$JSON_INPUT"; then
        # Additional check for completion context
        if detect_workflow_completion "$TOOL_NAME"; then
            execute_knowledge_update "workflow-completion" "$TOOL_NAME"
        else
            # For major tools, update regardless of completion detection
            case "$TOOL_NAME" in
                "project-init"|"module-dev"|"feature-merge"|"pm-prd"|"architect-design")
                    execute_knowledge_update "major-workflow-tool" "$TOOL_NAME"
                    ;;
                *)
                    log_knowledge "Tool $TOOL_NAME detected but no completion context found"
                    ;;
            esac
        fi
    fi
fi

# Check for manual knowledge update triggers
if [ -f "$CLAUDE_DIR/knowledge-update-trigger.json" ]; then
    TRIGGER_INFO=$(cat "$CLAUDE_DIR/knowledge-update-trigger.json")
    TRIGGER_REASON=$(echo "$TRIGGER_INFO" | jq -r '.reason // "manual-trigger"')
    execute_knowledge_update "$TRIGGER_REASON" "manual-trigger"
fi

# Pass through the JSON for the next hook/tool
echo "$JSON_INPUT"
exit 0