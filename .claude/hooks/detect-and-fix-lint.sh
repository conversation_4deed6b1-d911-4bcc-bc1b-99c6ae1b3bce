#!/bin/bash

# Universal Linting Detection and Auto-Fix Script
# Claude Code compatible hook that detects and runs linters

# Read JSON input from stdin (required for Claude Code hooks)
JSON_INPUT=$(cat)

# Extract tool name and file path from JSON
TOOL_NAME=$(echo "$JSON_INPUT" | jq -r '.toolName // empty' 2>> /workflows:dev-command/null)
MODIFIED_FILE=$(echo "$JSON_INPUT" | jq -r '.params.file_path // empty' 2>> /workflows:dev-command/null)

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CLAUDE_DIR="$(dirname "$SCRIPT_DIR")"

LINTER_CONFIG="$CLAUDE_DIRinit -> /workflows:project-init-linters.json"
LINT_LOG="$CLAUDE_DIR/lint.log"
LINT_REPORT="$CLAUDE_DIR/last-lint-report.md"

# Function to log
log_lint() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" >> "$LINT_LOG"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >> /workflows:dev-command/null 2>&1
}

# Function to find project root (where .git exists)
find_project_root() {
    local dir="$PWD"
    while [ "$dir" != "/" ]; do
        if [ -d "$dir/.git" ]; then
            echo "$dir"
            return
        fi
        dir=$(dirname "$dir")
    done
    echo "$PWD"
}

PROJECT_ROOT=$(find_project_root)
cd "$PROJECT_ROOT"

# Initialize report
echo "# Linting Report" > "$LINT_REPORT"
echo "Generated: $(date)" >> "$LINT_REPORT"
echo "Project: $PROJECT_ROOT" >> "$LINT_REPORT"
echo "" >> "$LINT_REPORT"

# Initialize linter detection
LINTERS_FOUND=()
LINTERS_RUN=()
ISSUES_FIXED=0

# Load cached linter configuration if exists
if [ -f "$LINTER_CONFIG" ]; then
    log_lint "Loading cached linter configuration"
fi

# Detect JavaScript/TypeScript linters
if [ -f "package.json" ]; then
    # Check for ESLint
    if grep -q "eslint" package.json || [ -f ".eslintrc.js" ] || [ -f ".eslintrc.json" ] || [ -f ".eslintrc.yml" ]; then
        LINTERS_FOUND+=("eslint")
        log_lint "ESLint detected"
    fi
    
    # Check for Prettier
    if grep -q "prettier" package.json || [ -f ".prettierrc" ] || [ -f ".prettierrc.js" ] || [ -f ".prettierrc.json" ]; then
        LINTERS_FOUND+=("prettier")
        log_lint "Prettier detected"
    fi
    
    # Check for lint scripts in package.json
    if command_exists jq && jq -e '.scripts | has("lint")' package.json >> /workflows:dev-command/null 2>&1; then
        LINTERS_FOUND+=("npm-lint")
        log_lint "npm lint script detected"
    fi
fi

# Detect Python linters
if [ -f "pyproject.toml" ] || [ -f "setup.py" ] || [ -f "requirements.txt" ]; then
    # Check for Ruff
    if command_exists ruff || grep -q "ruff" pyproject.toml 2>> /workflows:dev-command/null; then
        LINTERS_FOUND+=("ruff")
        log_lint "Ruff detected"
    fi
    
    # Check for Black
    if command_exists black || grep -q "black" pyproject.toml 2>> /workflows:dev-command/null; then
        LINTERS_FOUND+=("black")
        log_lint "Black detected"
    fi
    
    # Check for flake8
    if command_exists flake8 || [ -f ".flake8" ] || [ -f "setup.cfg" ]; then
        LINTERS_FOUND+=("flake8")
        log_lint "Flake8 detected"
    fi
fi

# Detect Rust formatter
if [ -f "Cargo.toml" ] && command_exists rustfmt; then
    LINTERS_FOUND+=("rustfmt")
    log_lint "Rustfmt detected"
fi

# Detect Go formatter
if command_exists gofmt && ([ -f "go.mod" ] || find . -name "*.go" -quit); then
    LINTERS_FOUND+=("gofmt")
    log_lint "gofmt detected"
fi

# Detect Ruby linter
if [ -f "Gemfile" ] && command_exists rubocop; then
    LINTERS_FOUND+=("rubocop")
    log_lint "RuboCop detected"
fi

# Detect Markdown linter
if command_exists markdownlint || [ -f ".markdownlint.json" ]; then
    LINTERS_FOUND+=("markdownlint")
    log_lint "Markdownlint detected"
fi

echo "## Detected Linters" >> "$LINT_REPORT"
if [ ${#LINTERS_FOUND[@]} -eq 0 ]; then
    echo "No linters detected in this project." >> "$LINT_REPORT"
    log_lint "No linters found"
else
    for linter in "${LINTERS_FOUND[@]}"; do
        echo "- $linter" >> "$LINT_REPORT"
    done
fi
echo "" >> "$LINT_REPORT"

# Run detected linters
echo "## Linting Results" >> "$LINT_REPORT"
echo "" >> "$LINT_REPORT"

# Function to run linter and capture results
run_linter() {
    local linter_name="$1"
    local linter_cmd="$2"
    local file_pattern="$3"
    
    echo "### $linter_name" >> "$LINT_REPORT"
    
    # Run the linter
    if [ -n "$MODIFIED_FILE" ] && [[ "$MODIFIED_FILE" =~ $file_pattern ]]; then
        # Run on specific file if it matches pattern
        output=$($linter_cmd "$MODIFIED_FILE" 2>&1)
        exit_code=$?
    elif [ -z "$MODIFIED_FILE" ]; then
        # Run on all files if no specific file
        output=$($linter_cmd 2>&1)
        exit_code=$?
    else
        echo "Skipped (file doesn't match pattern)" >> "$LINT_REPORT"
        return
    fi
    
    if [ $exit_code -eq 0 ]; then
        echo "✅ No issues found" >> "$LINT_REPORT"
    else
        echo "🔧 Fixed issues:" >> "$LINT_REPORT"
        echo '```' >> "$LINT_REPORT"
        echo "$output" | head -20 >> "$LINT_REPORT"
        echo '```' >> "$LINT_REPORT"
        ISSUES_FIXED=$((ISSUES_FIXED + 1))
    fi
    echo "" >> "$LINT_REPORT"
    
    LINTERS_RUN+=("$linter_name")
    log_lint "Ran $linter_name with exit code $exit_code"
}

# Run each detected linter
for linter in "${LINTERS_FOUND[@]}"; do
    case "$linter" in
        "eslint")
            if command_exists npx; then
                run_linter "ESLint" "npx eslint --fix" "\.(js|jsx|ts|tsx)$"
            fi
            ;;
        "prettier")
            if command_exists npx; then
                run_linter "Prettier" "npx prettier --write" "\.(js|jsx|ts|tsx|json|css|md)$"
            fi
            ;;
        "npm-lint")
            run_linter "npm lint" "npm run lint --fix" ".*"
            ;;
        "ruff")
            run_linter "Ruff" "ruff check --fix" "\.py$"
            ;;
        "black")
            run_linter "Black" "black" "\.py$"
            ;;
        "flake8")
            # Flake8 doesn't have auto-fix, just report
            run_linter "Flake8" "flake8" "\.py$"
            ;;
        "rustfmt")
            run_linter "Rustfmt" "rustfmt" "\.rs$"
            ;;
        "gofmt")
            run_linter "gofmt" "gofmt -w" "\.go$"
            ;;
        "rubocop")
            run_linter "RuboCop" "rubocop -a" "\.rb$"
            ;;
        "markdownlint")
            if command_exists markdownlint; then
                run_linter "Markdownlint" "markdownlint --fix" "\.md$"
            fi
            ;;
    esac
done

# Save linter configuration for future use
if [ ${#LINTERS_FOUND[@]} -gt 0 ]; then
    echo "{" > "$LINTER_CONFIG"
    echo "  \"detected\": [" >> "$LINTER_CONFIG"
    for i in "${!LINTERS_FOUND[@]}"; do
        if [ $i -eq $((${#LINTERS_FOUND[@]} - 1)) ]; then
            echo "    \"${LINTERS_FOUND[$i]}\"" >> "$LINTER_CONFIG"
        else
            echo "    \"${LINTERS_FOUND[$i]}\"," >> "$LINTER_CONFIG"
        fi
    done
    echo "  ]," >> "$LINTER_CONFIG"
    echo "  \"lastRun\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"" >> "$LINTER_CONFIG"
    echo "}" >> "$LINTER_CONFIG"
fi

# Summary
echo "## Summary" >> "$LINT_REPORT"
echo "- Linters detected: ${#LINTERS_FOUND[@]}" >> "$LINT_REPORT"
echo "- Linters run: ${#LINTERS_RUN[@]}" >> "$LINT_REPORT"
echo "- Files with fixes: $ISSUES_FIXED" >> "$LINT_REPORT"

# Send notification if issues were fixed
if [ $ISSUES_FIXED -gt 0 ]; then
    osascript -e "display notification \"Fixed linting issues in $ISSUES_FIXED file(s)\" with title \"PIB Linting\" sound name \"Pop\""
fi

log_lint "Linting complete. Issues fixed: $ISSUES_FIXED"


# Pass through the JSON for the next hook/tool
echo "$JSON_INPUT"
exit 0