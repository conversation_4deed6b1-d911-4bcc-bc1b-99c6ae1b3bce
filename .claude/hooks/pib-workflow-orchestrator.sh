#!/bin/bash

# PIB Workflow Orchestrator - Consolidated Hook System
# Replaces: agent-orchestration-hook.sh + dev-workflow-enhancer.sh + claude-subagent-coordinator.sh
# Ultra-efficient, single-point orchestration for PIB-METHOD + Claude Code sub-agents

set -o pipefail
trap '' PIPE

# === DEPENDENCY VALIDATION ===
if ! command -v jq >> /workflows:dev-command/null 2>&1; then
    echo "$JSON_INPUT"
    exit 0
fi

# === READ JSON INPUT ===
if ! JSON_INPUT=$(cat 2>> /workflows:dev-command/null); then
    exit 0
fi

# === EXTRACT EVENT DATA ===
EVENT_TYPE=$(echo "$JSON_INPUT" | jq -r '.event // empty' 2>> /workflows:dev-command/null)
TOOL_NAME=$(echo "$JSON_INPUT" | jq -r '.toolName // empty' 2>> /workflows:dev-command/null)
TOOL_RESULT=$(echo "$JSON_INPUT" | jq -r '.result // empty' 2>> /workflows:dev-command/null)

# Get current project context
CURRENT_DIR=$(pwd)
PROJECT_NAME=$(basename "$CURRENT_DIR")

# Find .claude directory
find_claude_dir() {
    local dir="$CURRENT_DIR"
    while [ "$dir" != "/" ]; do
        if [ -d "$dir/.claude" ]; then
            echo "$dir/.claude"
            return 0
        fi
        dir=$(dirname "$dir")
    done
    return 1
}

CLAUDE_DIR=$(find_claude_dir)
if [ -z "$CLAUDE_DIR" ]; then
    echo "$JSON_INPUT"
    exit 0
fi

# === SMART WORKFLOW DETECTION ===
detect_workflow_trigger() {
    # Code changes that trigger sub-agent workflows
    case "$TOOL_NAME" in
        "Write"|"Edit"|"MultiEdit")
            echo "code_change"
            ;;
        "Bash")
            if echo "$TOOL_RESULT" | grep -qE "(git commit|build|deploy|test)"; then
                echo "build_process"
            fi
            ;;
        *)
            # Check for > /workflows:dev-command command
            if echo "$TOOL_RESULT" | grep -q "> /workflows:dev-command\s"; then
                echo "dev_command"
            fi
            ;;
    esac
}

# === EFFICIENT SUB-AGENT COORDINATION ===
coordinate_subagent() {
    local trigger_type="$1"
    local instruction_file="$CLAUDE_DIR/active-subagent-instruction.md"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    case "$trigger_type" in
        "code_change")
            # Streamlined code review trigger
            cat > "$instruction_file" <<EOF
# Code Review Required

> Use the code-reviewer sub-agent to review recent changes with LEVER framework compliance validation.

**Files Changed**: $(get_changed_files)
**Review Focus**: LEVER compliance (minimum 4/5), code quality, PIB standards
**Next Step**: If changes needed, change-implementer sub-agent will be automatically suggested.

---
*Auto-triggered by PIB Workflow Orchestrator*
EOF
            ;;
        "dev_command")
            # Enhanced > /workflows:dev-command command processing
            local dev_request=$(echo "$TOOL_RESULT" | sed -n 's/.*\> /workflows:dev-command[[:space:]]*"\([^"]*\)".*/\1/p')
            cat > "$instruction_file" <<EOF
# PIB Development Workflow

> Use the dev-agent sub-agent to implement: "$dev_request"

**Requirements**:
- LEVER framework compliance (minimum 4/5 score)
- Context7 MCP research for existing patterns
- Quality-first implementation approach

**Workflow**:
1. **dev-agent**: Implementation with LEVER compliance
2. **code-reviewer**: Automatic quality validation
3. **change-implementer**: Address any feedback (if needed)
4. **qa-tester**: Comprehensive testing (for complex features)

---
*Enhanced PIB Development Workflow*
EOF
            ;;
        "build_process")
            # Build/deployment coordination
            cat > "$instruction_file" <<EOF
# Build Process Coordination

> Use the qa-tester sub-agent to validate the build and run comprehensive tests.

**Build Context**: Recent build/deployment activity detected
**Validation Required**: 
- All tests passing
- LEVER compliance maintained
- No regressions introduced

---
*Build Process Quality Gate*
EOF
            ;;
    esac
    
    # Log the coordination
    echo "$(date): PIB Orchestrator: $trigger_type -> sub-agent coordination" >> "$CLAUDE_DIR/orchestration.log"
}

# === OPTIMIZED FILE DETECTION ===
get_changed_files() {
    # Fast git-based detection
    if command -v git >> /workflows:dev-command/null 2>&1 && git rev-parse --git-dir >> /workflows:dev-command/null 2>&1; then
        # Get recent changes (last 5 minutes)
        git diff --name-only HEAD@{5.minutes.ago} HEAD 2>> /workflows:dev-command/null | head -5
    fi
}

# === WORKFLOW STATE MANAGEMENT ===
update_workflow_state() {
    local workflow_file="$CLAUDE_DIR/current-workflow-state.json"
    local temp_file=$(mktemp)
    
    # Ultra-efficient state update
    if [ -f "$workflow_file" ]; then
        jq --arg event "$EVENT_TYPE" --arg tool "$TOOL_NAME" --arg timestamp "$(date -u +"%Y-%m-%dT%H:%M:%SZ")" '
        .lastEvent = $event |
        .lastTool = $tool |
        .lastUpdate = $timestamp |
        .orchestrator = "pib-workflow-orchestrator" |
        .subAgentArchitecture = "claude-code"
        ' "$workflow_file" > "$temp_file" && mv "$temp_file" "$workflow_file"
    fi
}

# === LEVER COMPLIANCE MONITORING ===
monitor_lever_compliance() {
    local changed_files="$(get_changed_files)"
    
    if [ -n "$changed_files" ]; then
        # Quick LEVER compliance check
        local lever_indicators=0
        
        # Check for reuse patterns
        if echo "$changed_files" | xargs grep -l "reuse\|existing\|leverage" 2>> /workflows:dev-command/null | wc -l | grep -q "[1-9]"; then
            lever_indicators=$((lever_indicators + 1))
        fi
        
        # Check for extension patterns  
        if echo "$changed_files" | xargs grep -l "extend\|enhance\|improve" 2>> /workflows:dev-command/null | wc -l | grep -q "[1-9]"; then
            lever_indicators=$((lever_indicators + 1))
        fi
        
        # Log LEVER compliance hints
        if [ "$lever_indicators" -lt 2 ]; then
            echo "$(date): PIB Orchestrator: LEVER compliance indicators low - review recommended" >> "$CLAUDE_DIR/orchestration.log"
        fi
    fi
}

# === MAIN ORCHESTRATION LOGIC ===
main_orchestration() {
    # Only process relevant events efficiently
    if [ "$EVENT_TYPE" != "PostToolUse" ]; then
        echo "$JSON_INPUT"
        exit 0
    fi
    
    # Detect workflow trigger
    local trigger=$(detect_workflow_trigger)
    
    if [ -n "$trigger" ]; then
        # Update state first
        update_workflow_state
        
        # Coordinate sub-agents
        coordinate_subagent "$trigger"
        
        # Monitor LEVER compliance
        monitor_lever_compliance
        
        # Minimal logging
        echo "$(date): PIB Orchestrator: $trigger workflow activated" >> "$CLAUDE_DIR/orchestration.log"
    fi
}

# === PERFORMANCE OPTIMIZATION ===
# Only run orchestration for relevant events
case "$EVENT_TYPE" in
    "PostToolUse"|"WorkflowTransition"|"SubAgentComplete")
        main_orchestration
        ;;
    *)
        # Pass through without processing
        ;;
esac

# CRITICAL: Always pass through JSON
echo "$JSON_INPUT"
exit 0