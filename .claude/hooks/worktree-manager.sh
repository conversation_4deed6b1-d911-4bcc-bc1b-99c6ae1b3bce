#!/bin/bash

# Git Worktree Manager for PIB-METHOD
# Provides automated worktree creation, management, and cleanup

set -euo pipefail

# Configuration
# Find project root and .claude directory
find_project_root() {
    local dir="$(pwd)"
    while [ "$dir" != "/" ]; do
        if [ -d "$dir/.claude" ]; then
            echo "$dir"
            return 0
        fi
        dir=$(dirname "$dir")
    done
    return 1
}

PROJECT_ROOT=$(find_project_root)
if [ -z "$PROJECT_ROOT" ]; then
    echo "ERROR: Could not find project root with .claude directory" >&2
    exit 1
fi

WORKTREE_STATE_DIR="$PROJECT_ROOT/.claude/state/worktrees"
WORKTREE_LOG="$PROJECT_ROOT/.claude/state/worktree-operations.log"
WORKTREE_INDEX="$PROJECT_ROOT/.claude/state/worktree-index.json"

# Ensure required directories exist
mkdir -p "$WORKTREE_STATE_DIR"

# Initialize logging
log_operation() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$WORKTREE_LOG"
}

# Initialize worktree index if it doesn't exist
initialize_worktree_index() {
    if [[ ! -f "$WORKTREE_INDEX" ]]; then
        cat > "$WORKTREE_INDEX" << 'EOF'
{
  "active_worktrees": {},
  "completed_worktrees": {},
  "last_updated": null
}
EOF
    fi
}

# Detect if current directory is a worktree
detect_worktree_context() {
    if [[ -f .git ]] && grep -q "gitdir:" .git 2>> /workflows:dev-command/null; then
        # This is a worktree
        local worktree_name=$(basename "$(pwd)")
        if [[ "$worktree_name" == pib-* ]]; then
            echo "${worktree_name#pib-}"
        else
            echo "unknown"
        fi
    else
        echo "main"
    fi
}

# Get worktree state directory
get_worktree_state_dir() {
    local worktree_context="$1"
    if [[ "$worktree_context" == "main" ]]; then
        echo "$PROJECT_ROOT/.claude/state/main"
    else
        echo "$WORKTREE_STATE_DIR/$worktree_context"
    fi
}

# Create new worktree with isolated state
create_worktree() {
    local feature_name="$1"
    local base_branch="${2:-main}"
    
    # Validate feature name
    if [[ ! "$feature_name" =~ ^[a-zA-Z0-9-]+$ ]]; then
        echo "ERROR: Feature name must contain only letters, numbers, and hyphens" >&2
        return 1
    fi
    
    local timestamp=$(date +%Y%m%d)
    local branch_name="feature/${feature_name}-${timestamp}"
    local parent_dir=$(dirname "$PROJECT_ROOT")
    local worktree_dir="$parent_dir/pib-${feature_name}"
    local state_dir="$WORKTREE_STATE_DIR/$feature_name"
    
    log_operation "Creating worktree: $feature_name"
    
    # Check parent directory write permissions
    if [[ ! -w "$parent_dir" ]]; then
        echo "ERROR: No write permissions for parent directory: $parent_dir" >&2
        echo "       Cannot create worktree outside the project." >&2
        return 1
    fi
    
    # Check if worktree already exists
    if [[ -d "$worktree_dir" ]]; then
        echo "ERROR: Worktree directory already exists: $worktree_dir" >&2
        return 1
    fi
    
    # Check if we're in the main repository
    if [[ "$(detect_worktree_context)" != "main" ]]; then
        echo "ERROR: Must be in main repository to create worktree" >&2
        return 1
    fi
    
    # Ensure we're on the base branch and it's up to date
    if ! git checkout "$base_branch" 2>> /workflows:dev-command/null; then
        echo "ERROR: Could not checkout base branch: $base_branch" >&2
        return 1
    fi
    
    # Parent directory should already exist, no need to create
    
    # Create the worktree
    if ! git worktree add -b "$branch_name" "$worktree_dir" "$base_branch"; then
        echo "ERROR: Failed to create worktree" >&2
        return 1
    fi
    
    # Initialize state directory using state-isolator
    local state_isolator="$PROJECT_ROOT/.claude/hooks/state-isolator.sh"
    if [[ -x "$state_isolator" ]]; then
        "$state_isolator" initialize "$feature_name" "$worktree_dir"
    else
        # Fallback initialization
        mkdir -p "$state_dir"
        
        # Copy essential configuration from main state
        if [[ -d "$PROJECT_ROOT/.claude/state/main" ]]; then
            cp -r "$PROJECT_ROOT/.claude/state/main"/* "$state_dir/" 2>> /workflows:dev-command/null || true
        fi
    fi
    
    # Copy all .env files from source to worktree
    echo "🔍 Searching for .env files to copy..."
    local env_files_found=0
    while IFS= read -r -d '' env_file; do
        # Get relative path from project root
        local rel_path="${env_file#$PROJECT_ROOT/}"
        local target_dir="$worktree_dir/$(dirname "$rel_path")"
        
        # Create target directory if needed
        mkdir -p "$target_dir"
        
        # Copy the .env file
        cp "$env_file" "$target_dir/"
        echo "  ✓ Copied: $rel_path"
        ((env_files_found++))
    done < <(find "$PROJECT_ROOT" -name ".env*" -type f -not -path "*/.git/*" -not -path "*/node_modules/*" -not -path "*/.claude/*" -print0)
    
    if [[ $env_files_found -eq 0 ]]; then
        echo "  ℹ No .env files found in source project"
    else
        echo "  ✅ Copied $env_files_found .env file(s) to worktree"
    fi
    
    # Update worktree index
    update_worktree_index "add" "$feature_name" "$worktree_dir" "$branch_name" "active"
    
    log_operation "Worktree created successfully: $feature_name -> $worktree_dir"
    
    # Output success message with instructions
    cat << EOF

🎯 Worktree Created Successfully!

Feature: $feature_name
Directory: $worktree_dir
Branch: $branch_name
State: $state_dir

Next Steps:
1. Open a new Claude Code instance
2. Change to directory: $worktree_dir
3. Start developing your feature
4. Use normal PIB workflows (they will use isolated state)
5. When ready, run start -> /workflows:feature-start-merge to integrate your changes

The worktree has isolated state management - your work won't interfere with other features!

EOF
    
    return 0
}

# List all active worktrees
list_worktrees() {
    local format="${1:-table}"
    
    initialize_worktree_index
    
    echo "# Active PIB Worktrees"
    echo ""
    
    # Get worktrees from Git
    local git_worktrees
    git_worktrees=$(git worktree list --porcelain 2>> /workflows:dev-command/null | grep -E '^worktree|^branch' | paste - - | grep pib- || echo "")
    
    if [[ -z "$git_worktrees" ]]; then
        echo "No active worktrees found."
        return 0
    fi
    
    echo "| Feature | Directory | Branch | Status |"
    echo "|---------|-----------|--------|--------|"
    
    while IFS=$'\t' read -r worktree_line branch_line; do
        local worktree_path=$(echo "$worktree_line" | cut -d' ' -f2-)
        local branch_name=$(echo "$branch_line" | cut -d' ' -f2-)
        local feature_name=$(basename "$worktree_path" | sed 's/^pib-//')
        
        # Get last activity
        local last_activity="Unknown"
        if [[ -d "$worktree_path" ]]; then
            last_activity=$(cd "$worktree_path" && git log -1 --format="%cr" 2>> /workflows:dev-command/null || echo "No commits")
        fi
        
        # Check if there are uncommitted changes
        local status="Clean"
        if [[ -d "$worktree_path" ]]; then
            cd "$worktree_path"
            if ! git diff --quiet 2>> /workflows:dev-command/null || ! git diff --cached --quiet 2>> /workflows:dev-command/null; then
                status="Modified"
            fi
            cd - >> /workflows:dev-command/null
        fi
        
        echo "| $feature_name | $worktree_path | $branch_name | $status |"
    done <<< "$git_worktrees"
    
    echo ""
    echo "Use start -> /workflows:feature-start-merge from within a worktree to integrate changes."
    echo "Use start -> /workflows:feature-start-cleanup to remove completed worktrees."
}

# Remove completed worktree
remove_worktree() {
    local feature_name="$1"
    local force="${2:-false}"
    
    local parent_dir=$(dirname "$PROJECT_ROOT")
    local worktree_dir="$parent_dir/pib-${feature_name}"
    local state_dir="$WORKTREE_STATE_DIR/$feature_name"
    
    log_operation "Removing worktree: $feature_name"
    
    # Check if worktree exists
    if [[ ! -d "$worktree_dir" ]]; then
        echo "ERROR: Worktree not found: $worktree_dir" >&2
        return 1
    fi
    
    # Check for uncommitted changes unless force is specified
    if [[ "$force" != "true" ]]; then
        cd "$worktree_dir"
        if ! git diff --quiet 2>> /workflows:dev-command/null || ! git diff --cached --quiet 2>> /workflows:dev-command/null; then
            echo "ERROR: Worktree has uncommitted changes. Commit or use --force to override." >&2
            return 1
        fi
        cd - >> /workflows:dev-command/null
    fi
    
    # Remove the worktree
    if ! git worktree remove "$worktree_dir" ${force:+--force}; then
        echo "ERROR: Failed to remove worktree" >&2
        return 1
    fi
    
    # Archive state directory
    if [[ -d "$state_dir" ]]; then
        local archive_dir="$PROJECT_ROOT/.claude/state/archived/worktrees"
        mkdir -p "$archive_dir"
        local timestamp=$(date +%Y%m%d-%H%M%S)
        mv "$state_dir" "$archive_dir/${feature_name}-${timestamp}"
        log_operation "State archived: $feature_name -> $archive_dir/${feature_name}-${timestamp}"
    fi
    
    # Update worktree index
    update_worktree_index "remove" "$feature_name"
    
    log_operation "Worktree removed successfully: $feature_name"
    
    echo "✅ Worktree '$feature_name' removed successfully"
    echo "   State has been archived for future reference"
}

# Merge worktree changes back to main
merge_worktree() {
    local feature_name="$1"
    local target_branch="${2:-main}"
    local auto_cleanup="${3:-true}"
    
    log_operation "Merging worktree: $feature_name"
    
    # Detect current worktree context
    local current_context=$(detect_worktree_context)
    if [[ "$current_context" == "main" ]]; then
        echo "ERROR: Must be in a worktree to merge. Use this command from within your feature worktree." >&2
        return 1
    fi
    
    if [[ "$current_context" != "$feature_name" ]]; then
        echo "WARNING: Current worktree context ($current_context) doesn't match specified feature ($feature_name)"
        echo "Proceeding with current context: $current_context"
        feature_name="$current_context"
    fi
    
    # Get current branch
    local current_branch=$(git branch --show-current)
    
    # Ensure all changes are committed
    if ! git diff --quiet || ! git diff --cached --quiet; then
        echo "ERROR: Please commit all changes before merging" >&2
        return 1
    fi
    
    # Switch to main repository and merge
    cd "$PROJECT_ROOT"
    
    # Checkout target branch
    if ! git checkout "$target_branch"; then
        echo "ERROR: Could not checkout target branch: $target_branch" >&2
        return 1
    fi
    
    # Pull latest changes
    git pull origin "$target_branch" || echo "WARNING: Could not pull latest changes"
    
    # Merge the feature branch
    if ! git merge --no-ff "$current_branch" -m "feat: Merge $feature_name

Merged from worktree: $current_branch
Feature: $feature_name

🤖 Generated with Claude Code
"; then
        echo "ERROR: Merge failed. Please resolve conflicts manually." >&2
        return 1
    fi
    
    log_operation "Merge completed successfully: $feature_name -> $target_branch"
    
    echo "✅ Feature '$feature_name' merged successfully into '$target_branch'"
    
    # Auto-cleanup if requested
    if [[ "$auto_cleanup" == "true" ]]; then
        echo "🧹 Cleaning up worktree..."
        remove_worktree "$feature_name" "true"
    fi
    
    echo ""
    echo "🎉 Feature integration complete!"
    echo "   Branch: $current_branch"
    echo "   Target: $target_branch"
    echo "   Worktree: $(if [[ "$auto_cleanup" == "true" ]]; then echo "Cleaned up"; else echo "Still active"; fi)"
}

# Update worktree index for tracking
update_worktree_index() {
    local action="$1"
    local feature_name="$2"
    local worktree_dir="${3:-}"
    local branch_name="${4:-}"
    local status="${5:-}"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    initialize_worktree_index
    
    case "$action" in
        "add")
            jq --arg name "$feature_name" --arg dir "$worktree_dir" --arg branch "$branch_name" --arg status "$status" --arg ts "$timestamp" \
                '.active_worktrees[$name] = {
                    "directory": $dir,
                    "branch": $branch,
                    "status": $status,
                    "created": $ts,
                    "last_updated": $ts
                } | .last_updated = $ts' \
                "$WORKTREE_INDEX" > "$WORKTREE_INDEX.tmp" && mv "$WORKTREE_INDEX.tmp" "$WORKTREE_INDEX"
            ;;
        "remove")
            jq --arg name "$feature_name" --arg ts "$timestamp" \
                'if .active_worktrees[$name] then
                    .completed_worktrees[$name] = (.active_worktrees[$name] + {"completed": $ts}) |
                    del(.active_worktrees[$name]) |
                    .last_updated = $ts
                else . end' \
                "$WORKTREE_INDEX" > "$WORKTREE_INDEX.tmp" && mv "$WORKTREE_INDEX.tmp" "$WORKTREE_INDEX"
            ;;
        "update")
            jq --arg name "$feature_name" --arg status "$status" --arg ts "$timestamp" \
                'if .active_worktrees[$name] then
                    .active_worktrees[$name].status = $status |
                    .active_worktrees[$name].last_updated = $ts |
                    .last_updated = $ts
                else . end' \
                "$WORKTREE_INDEX" > "$WORKTREE_INDEX.tmp" && mv "$WORKTREE_INDEX.tmp" "$WORKTREE_INDEX"
            ;;
    esac
}

# Cleanup orphaned worktrees
cleanup_orphaned_worktrees() {
    echo "🧹 Cleaning up orphaned worktrees..."
    
    # Use git worktree prune to remove orphaned references
    git worktree prune
    
    # Clean up orphaned state directories
    if [[ -d "$WORKTREE_STATE_DIR" ]]; then
        local orphaned_states=()
        
        for state_dir in "$WORKTREE_STATE_DIR"/*; do
            if [[ -d "$state_dir" ]]; then
                local feature_name=$(basename "$state_dir")
                local parent_dir=$(dirname "$PROJECT_ROOT")
                local worktree_dir="$parent_dir/pib-${feature_name}"
                
                if [[ ! -d "$worktree_dir" ]]; then
                    orphaned_states+=("$feature_name")
                fi
            fi
        done
        
        if [[ ${#orphaned_states[@]} -gt 0 ]]; then
            echo "Found orphaned state directories: ${orphaned_states[*]}"
            
            for orphaned in "${orphaned_states[@]}"; do
                echo "Archiving orphaned state: $orphaned"
                local archive_dir="$PROJECT_ROOT/.claude/state/archived/orphaned"
                mkdir -p "$archive_dir"
                local timestamp=$(date +%Y%m%d-%H%M%S)
                mv "$WORKTREE_STATE_DIR/$orphaned" "$archive_dir/${orphaned}-${timestamp}"
                
                # Remove from active index
                update_worktree_index "remove" "$orphaned"
            done
        fi
    fi
    
    echo "✅ Cleanup completed"
}

# Main function
main() {
    local action="${1:-help}"
    
    case "$action" in
        "create")
            if [[ $# -ge 2 ]]; then
                create_worktree "$2" "${3:-main}"
            else
                echo "Usage: $0 create <feature_name> [base_branch]"
                exit 1
            fi
            ;;
        "list")
            list_worktrees "${2:-table}"
            ;;
        "remove")
            if [[ $# -ge 2 ]]; then
                remove_worktree "$2" "${3:-false}"
            else
                echo "Usage: $0 remove <feature_name> [force]"
                exit 1
            fi
            ;;
        "merge")
            if [[ $# -ge 2 ]]; then
                merge_worktree "$2" "${3:-main}" "${4:-true}"
            else
                # Try to detect current worktree
                local current_context=$(detect_worktree_context)
                if [[ "$current_context" != "main" ]]; then
                    merge_worktree "$current_context" "${2:-main}" "${3:-true}"
                else
                    echo "Usage: $0 merge <feature_name> [target_branch] [auto_cleanup]"
                    echo "   or: Run from within a worktree without feature_name"
                    exit 1
                fi
            fi
            ;;
        "cleanup")
            cleanup_orphaned_worktrees
            ;;
        "context")
            detect_worktree_context
            ;;
        "state-dir")
            local context=$(detect_worktree_context)
            get_worktree_state_dir "$context"
            ;;
        "help"|*)
            cat << EOF
Git Worktree Manager for PIB-METHOD

Usage: $0 <action> [arguments...]

Actions:
  create <feature_name> [base_branch]
    Create a new worktree with isolated state
    
  list [format]
    List all active worktrees
    
  remove <feature_name> [force]
    Remove a completed worktree
    
  merge [feature_name] [target_branch] [auto_cleanup]
    Merge worktree changes back to main branch
    If run from within a worktree, feature_name is optional
    
  cleanup
    Clean up orphaned worktrees and state directories
    
  context
    Show current worktree context
    
  state-dir
    Show current state directory path
    
  help
    Show this help message

Examples:
  $0 create user-authentication
  $0 list
  $0 merge user-authentication main true
  $0 remove user-authentication
  $0 cleanup

Note: Use the slash commands (start -> /workflows:feature-start-start, start -> /workflows:feature-start-merge, etc.) for a more user-friendly interface.
EOF
            ;;
    esac
}

# Execute main function if script is run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi