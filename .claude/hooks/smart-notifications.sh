#!/bin/bash

# Smart Notification Hook - Minimal, Focused Notifications
# Only triggers for: Stop, Sub Agent Stop, and Claude Code notifications

set -o pipefail
trap '' PIPE

# === READ JSON INPUT ===
if ! JSON_INPUT=$(cat 2>> /workflows:dev-command/null); then
    exit 0
fi

# === EXTRACT EVENT DATA ===
EVENT_TYPE=$(echo "$JSON_INPUT" | jq -r '.event // empty' 2>> /workflows:dev-command/null)
MESSAGE=$(echo "$JSON_INPUT" | jq -r '.message // empty' 2>> /workflows:dev-command/null)
TOOL_NAME=$(echo "$JSON_INPUT" | jq -r '.toolName // empty' 2>> /workflows:dev-command/null)

# Get current project context
CURRENT_DIR=$(pwd)
PROJECT_NAME=$(basename "$CURRENT_DIR")

# Find .claude directory
find_claude_dir() {
    local dir="$CURRENT_DIR"
    while [ "$dir" != "/" ]; do
        if [ -d "$dir/.claude" ]; then
            echo "$dir/.claude"
            return 0
        fi
        dir=$(dirname "$dir")
    done
    return 1
}

CLAUDE_DIR=$(find_claude_dir)
if [ -z "$CLAUDE_DIR" ]; then
    echo "$JSON_INPUT"
    exit 0
fi

# === NOTIFICATION LOGIC ===
send_notification() {
    local title="$1"
    local message="$2"
    local priority="$3"
    
    # Log the notification
    echo "$(date -u +"%Y-%m-%dT%H:%M:%SZ"): [$priority] $title - $message" >> "$CLAUDE_DIR/notifications.log"
    
    # Send desktop notification if available
    if command -v notify-send >> /workflows:dev-command/null 2>&1; then
        notify-send "$title" "$message" 2>> /workflows:dev-command/null || true
    elif command -v osascript >> /workflows:dev-command/null 2>&1; then
        # macOS notification
        osascript -e "display notification \"$message\" with title \"$title\"" 2>> /workflows:dev-command/null || true
    fi
}

# === SMART FILTERING ===
case "$EVENT_TYPE" in
    "Stop")
        # Claude Code finished responding
        send_notification "PIB-METHOD" "Claude Code session completed for $PROJECT_NAME" "INFO"
        ;;
    "SubAgentStop")
        # Sub-agent task completed
        local subagent_name=$(echo "$JSON_INPUT" | jq -r '.subAgent // "Unknown"')
        send_notification "PIB Sub-Agent" "$subagent_name completed task in $PROJECT_NAME" "INFO"
        ;;
    "Notification")
        # Claude Code sending a notification
        local notification_message=$(echo "$JSON_INPUT" | jq -r '.notification // .message // "Notification from Claude Code"')
        send_notification "Claude Code" "$notification_message" "ALERT"
        ;;
    "WorkflowComplete")
        # PIB workflow completed (custom event)
        send_notification "PIB Workflow" "Development workflow completed successfully" "SUCCESS"
        ;;
    "LeverComplianceFailure")
        # LEVER compliance issue (custom event)
        send_notification "LEVER Alert" "Code review failed LEVER compliance - attention needed" "WARNING"
        ;;
    *)
        # All other events are filtered out - no notification
        ;;
esac

# === WORKFLOW STATE INTEGRATION ===
# Update notification preferences in workflow state
if [ -f "$CLAUDE_DIR/current-workflow-state.json" ]; then
    local temp_file=$(mktemp)
    jq '.notifications = {
        "enabled": true,
        "filter": "minimal",
        "events": ["Stop", "SubAgentStop", "Notification", "WorkflowComplete", "LeverComplianceFailure"],
        "lastNotification": "'$(date -u +"%Y-%m-%dT%H:%M:%SZ")'"
    }' "$CLAUDE_DIR/current-workflow-state.json" > "$temp_file" && mv "$temp_file" "$CLAUDE_DIR/current-workflow-state.json"
fi

# CRITICAL: Pass through the JSON for the next hook
echo "$JSON_INPUT"
exit 0