#!/bin/bash

# Technology Specialist Router Hook
# Automatically routes tasks to universal-dev-agent with appropriate plugins based on file patterns and context

set -euo pipefail

# === CONFIGURATION ===
CLAUDE_DIR="$(dirname "$0")/.."
TECH_DETECTION_CONFIG="$CLAUDE_DIR/tech-detection-config.json"
TECH_ROUTING_LOG="$CLAUDE_DIR/tech-routing.log"
COMPLEXITY_THRESHOLD=3  # Number of technologies before complex routing

# === DEPENDENCY CHECK ===
if ! command -v jq >/dev/null 2>&1; then
    echo "ERROR: jq is required but not installed" >&2
    exit 1
fi

# === READ JSON INPUT ===
if ! JSON_INPUT=$(cat 2>/dev/null); then
    exit 0
fi

# === EXTRACT EVENT DATA ===
EVENT_TYPE=$(echo "$JSON_INPUT" | jq -r '.event // empty' 2>/dev/null)
TOOL_NAME=$(echo "$JSON_INPUT" | jq -r '.toolName // empty' 2>/dev/null)
TOOL_PARAMS=$(echo "$JSON_INPUT" | jq -r '.params // empty' 2>/dev/null)

# === TECHNOLOGY DETECTION ===
detect_technology_from_file() {
    local file_path="$1"
    local tech_stack=""
    
    # Frontend technologies
    if [[ "$file_path" =~ \.(jsx?|tsx?)$ ]]; then
        # Check for React patterns
        if grep -q "import.*React\|from.*react\|useEffect\|useState\|Component" "$file_path" 2>/dev/null; then
            tech_stack="react"
        # Check for Vue patterns
        elif grep -q "vue\|<template>\|<script setup>" "$file_path" 2>/dev/null; then
            tech_stack="vue"
        # Check for Angular patterns
        elif grep -q "@angular\|@Component\|NgModule" "$file_path" 2>/dev/null; then
            tech_stack="angular"
        else
            tech_stack="javascript"
        fi
    fi
    
    # Backend technologies
    if [[ "$file_path" =~ \.(js|ts)$ ]]; then
        # Express.js
        if grep -q "express\|app\.\(get\|post\|put\|delete\)\|router\." "$file_path" 2>/dev/null; then
            tech_stack="${tech_stack:+$tech_stack,}express"
        fi
    elif [[ "$file_path" =~ \.py$ ]]; then
        # Django
        if grep -q "django\|from django\|models\.Model" "$file_path" 2>/dev/null; then
            tech_stack="${tech_stack:+$tech_stack,}django"
        # FastAPI
        elif grep -q "fastapi\|from fastapi" "$file_path" 2>/dev/null; then
            tech_stack="${tech_stack:+$tech_stack,}fastapi"
        else
            tech_stack="${tech_stack:+$tech_stack,}python"
        fi
    elif [[ "$file_path" =~ \.php$ ]]; then
        # Laravel
        if grep -q "Laravel\|Illuminate\|artisan" "$file_path" 2>/dev/null; then
            tech_stack="${tech_stack:+$tech_stack,}laravel"
        else
            tech_stack="${tech_stack:+$tech_stack,}php"
        fi
    fi
    
    # Database files
    if [[ "$file_path" =~ \.(sql|migration)$ ]] || [[ "$file_path" =~ migrations/ ]]; then
        tech_stack="${tech_stack:+$tech_stack,}database"
    fi
    
    echo "$tech_stack"
}

# === LOAD PROJECT TECH STACK ===
load_project_tech_stack() {
    local claude_md="$CLAUDE_DIR/../CLAUDE.md"
    local tech_stack=""
    
    if [[ -f "$claude_md" ]]; then
        # Extract tech stack from CLAUDE.md if it has the enhanced format
        if grep -q "## Technology Stack" "$claude_md" 2>/dev/null; then
            tech_stack=$(awk '/## Technology Stack/,/## Agent Assignments/' "$claude_md" | grep -E "Frontend:|Backend:|Database:" | tr -d ' ' | cut -d: -f2 | tr '\n' ',' | sed 's/,$//')
        fi
    fi
    
    # Fallback to package.json detection
    if [[ -z "$tech_stack" ]] && [[ -f "package.json" ]]; then
        if grep -q '"react"' package.json 2>/dev/null; then
            tech_stack="${tech_stack:+$tech_stack,}react"
        fi
        if grep -q '"express"' package.json 2>/dev/null; then
            tech_stack="${tech_stack:+$tech_stack,}express"
        fi
        if grep -q '"vue"' package.json 2>/dev/null; then
            tech_stack="${tech_stack:+$tech_stack,}vue"
        fi
    fi
    
    echo "$tech_stack"
}

# === SPECIALIST SELECTION ===
select_specialist_for_technology() {
    local technology="$1"
    local context="$2"
    
    case "$technology" in
        *react*)
            echo "react-specialist"
            ;;
        *vue*)
            echo "vue-specialist"
            ;;
        *angular*)
            echo "angular-specialist"
            ;;
        *express*)
            echo "express-specialist"
            ;;
        *django*)
            echo "django-specialist"
            ;;
        *laravel*)
            echo "laravel-specialist"
            ;;
        *postgresql*|*postgres*)
            echo "postgresql-expert"
            ;;
        *mongodb*|*mongo*)
            echo "mongodb-expert"
            ;;
        *)
            # Default to generic agents based on context
            case "$context" in
                frontend)
                    echo "frontend-developer"
                    ;;
                backend)
                    echo "backend-developer"
                    ;;
                database)
                    echo "database-expert"
                    ;;
                *)
                    echo ""  # No specialist needed
                    ;;
            esac
            ;;
    esac
}

# === CREATE ROUTING INSTRUCTION ===
create_specialist_routing_instruction() {
    local specialist="$1"
    local file_path="$2"
    local operation="$3"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    local routing_file="$CLAUDE_DIR/pending-specialist-routing.md"
    
    cat > "$routing_file" <<EOF
# Technology Specialist Routing

## Context
- **File**: $file_path
- **Operation**: $operation
- **Detected Specialist**: $specialist
- **Timestamp**: $timestamp

## Recommended Sub-Agent
Based on the technology stack and file context, the following specialist should handle this task:

### Use the $specialist sub-agent

This specialist has deep expertise in the specific technology stack and patterns relevant to this file.

## Routing Rationale
The $specialist was selected because:
- File pattern matches their technology expertise
- Operation type aligns with their specialization
- Project uses this technology stack extensively

## Alternative Approach
If automatic delegation doesn't occur, explicitly request:

\`\`\`
Please use the $specialist sub-agent to handle the changes to $file_path
\`\`\`

---
*Generated by Technology Specialist Router*
EOF
    
    echo "$(date): Routed to $specialist for $file_path" >> "$TECH_DETECTION_LOG"
}

# === ENHANCE JSON WITH SPECIALIST ===
enhance_json_with_specialist() {
    local specialist="$1"
    local operation="$2"
    
    # Add specialist recommendation to JSON
    echo "$JSON_INPUT" | jq \
        --arg specialist "$specialist" \
        --arg op "$operation" \
        '.techSpecialist = {
            "recommended": $specialist,
            "operation": $op,
            "autoRoute": true
        }'
}

# === MAIN ROUTING LOGIC ===
route_to_specialist() {
    local file_path=""
    local operation=""
    local detected_tech=""
    local project_tech=""
    local selected_specialist=""
    
    # Extract file path based on tool
    case "$TOOL_NAME" in
        "Read")
            file_path=$(echo "$TOOL_PARAMS" | jq -r '.file_path // empty' 2>/dev/null)
            operation="read"
            ;;
        "Write"|"Edit"|"MultiEdit")
            file_path=$(echo "$TOOL_PARAMS" | jq -r '.file_path // empty' 2>/dev/null)
            operation="modify"
            ;;
        *)
            # No routing needed for other tools
            echo "$JSON_INPUT"
            exit 0
            ;;
    esac
    
    if [[ -z "$file_path" ]]; then
        echo "$JSON_INPUT"
        exit 0
    fi
    
    # Detect technology from file
    detected_tech=$(detect_technology_from_file "$file_path")
    
    # Load project tech stack
    project_tech=$(load_project_tech_stack)
    
    # Combine detected and project tech
    combined_tech="${detected_tech}${project_tech:+,$project_tech}"
    
    # Determine context
    local context="general"
    if [[ "$file_path" =~ (components?|views?|pages?|ui|frontend) ]]; then
        context="frontend"
    elif [[ "$file_path" =~ (api|routes?|controllers?|services?|backend) ]]; then
        context="backend"
    elif [[ "$file_path" =~ (models?|schemas?|migrations?|db|database) ]]; then
        context="database"
    fi
    
    # Select appropriate specialist
    selected_specialist=$(select_specialist_for_technology "$combined_tech" "$context")
    
    if [[ -n "$selected_specialist" ]]; then
        # Create routing instruction
        create_specialist_routing_instruction "$selected_specialist" "$file_path" "$operation"
        
        # Enhance JSON with specialist recommendation
        ENHANCED_JSON=$(enhance_json_with_specialist "$selected_specialist" "$operation")
        echo "$ENHANCED_JSON"
        
        echo "$(date): Technology detected: $combined_tech, Specialist: $selected_specialist" >> "$TECH_DETECTION_LOG"
    else
        # No specialist needed, pass through
        echo "$JSON_INPUT"
    fi
}

# === UPDATE ROUTING CONFIG ===
update_routing_statistics() {
    local specialist="$1"
    local file_path="$2"
    
    if [[ ! -f "$TECH_ROUTING_CONFIG" ]]; then
        echo '{"routing_stats": {}, "last_updated": null}' > "$TECH_ROUTING_CONFIG"
    fi
    
    # Update routing statistics
    jq --arg spec "$specialist" --arg file "$file_path" --arg ts "$(date -u +"%Y-%m-%dT%H:%M:%SZ")" \
        '.routing_stats[$spec] = (.routing_stats[$spec] // 0) + 1 | 
         .last_routing = {"specialist": $spec, "file": $file, "timestamp": $ts} |
         .last_updated = $ts' \
        "$TECH_ROUTING_CONFIG" > "$TECH_ROUTING_CONFIG.tmp" && \
        mv "$TECH_ROUTING_CONFIG.tmp" "$TECH_ROUTING_CONFIG"
}

# === INTEGRATION WITH EXISTING HOOKS ===
integrate_with_orchestration() {
    # Check if agent orchestration hook should handle this after routing
    local orchestration_hook="$CLAUDE_DIR/hooks/agent-orchestration-hook.sh"
    if [[ -x "$orchestration_hook" ]]; then
        # Pass enhanced JSON to orchestration hook
        echo "$JSON_INPUT" | "$orchestration_hook"
    else
        echo "$JSON_INPUT"
    fi
}

# === MAIN EXECUTION ===
main() {
    # Only process relevant events
    if [[ "$EVENT_TYPE" == "PreToolUse" ]] || [[ "$EVENT_TYPE" == "PostToolUse" ]]; then
        # Route to specialist if applicable
        route_to_specialist
    else
        # Pass through for other events
        integrate_with_orchestration
    fi
}

# Execute main function
main

# Always exit 0 - routing is informational
exit 0