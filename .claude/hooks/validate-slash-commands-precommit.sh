#!/bin/bash

# PIB-METHOD Pre-commit Hook for Slash Command Validation
# Ensures all slash commands use the correct namespace:command format

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get the list of files to be committed
FILES=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(md|sh|js|json)$')

if [ -z "$FILES" ]; then
    exit 0
fi

# Counter for issues
ISSUES=0

# Common incorrect patterns
declare -a INCORRECT_PATTERNS=(
    "/dev[^:]"
    "/feature-start[^:]"
    "/plan-session[^:]"
    "/clarify-requirements[^:]"
    "/confirm-approach[^:]"
    "/ai-issue[^:]"
    "/ai-pr[^:]"
    "/ai-assign[^:]"
    "/ai-status[^:]"
    "/help[^:]"
    "/agents[^:]"
    "/tasks[^:]"
    "/what-next[^:]"
    "/switch-agent[^:]"
    "/analyze[^:]"
    "/thinkdeep[^:]"
    "/codereview[^:]"
    "/debug[^:]"
    "/refactor[^:]"
)

echo -e "${BLUE}=== Checking Slash Commands ===${NC}"

# Check each file
for FILE in $FILES; do
    # Skip if file doesn't exist (deleted)
    [ ! -f "$FILE" ] && continue
    
    # Check for incorrect patterns
    for PATTERN in "${INCORRECT_PATTERNS[@]}"; do
        if grep -E "$PATTERN" "$FILE" > /dev/null 2>&1; then
            echo -e "${RED}✗ $FILE contains incorrect slash command format${NC}"
            grep -n -E "$PATTERN" "$FILE" | head -5
            ((ISSUES++))
            break
        fi
    done
done

if [ $ISSUES -gt 0 ]; then
    echo ""
    echo -e "${RED}=== Slash Command Validation Failed ===${NC}"
    echo -e "${YELLOW}Found $ISSUES file(s) with incorrect slash commands.${NC}"
    echo ""
    echo -e "${YELLOW}All slash commands must use the namespace:command format:${NC}"
    echo "  ❌ /dev                → ✅ /workflows:dev-command"
    echo "  ❌ /feature-start      → ✅ /workflows:feature-start"
    echo "  ❌ /plan-session       → ✅ /planning:plan-session"
    echo "  ❌ /help               → ✅ /core:help"
    echo "  ❌ /agents             → ✅ /core:agents"
    echo ""
    echo -e "${YELLOW}To fix:${NC}"
    echo "  1. Run: .claude/scripts/validate-slash-commands.sh true"
    echo "  2. Or manually update the commands to use namespace:command format"
    echo ""
    exit 1
fi

echo -e "${GREEN}✓ All slash commands use correct format${NC}"
exit 0