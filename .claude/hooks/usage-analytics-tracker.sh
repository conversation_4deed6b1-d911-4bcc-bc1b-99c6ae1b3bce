#!/bin/bash

# Usage Analytics Tracker Hook
# Lightweight tracking of technology usage, file activity, and agent usage

set -euo pipefail

# === CONFIGURATION ===
CLAUDE_DIR="$(dirname "$0")/.."
ANALYTICS_FILE="$CLAUDE_DIR/usage-analytics.json"
PROJECT_ROOT="$(dirname "$CLAUDE_DIR")"

# === DEPENDENCY CHECK ===
if ! command -v jq >/dev/null 2>&1; then
    # Skip tracking if jq not available
    exit 0
fi

# === READ EVENT DATA ===
JSON_INPUT=""
if [ -t 0 ]; then
    # No input from stdin, skip
    exit 0
else
    JSON_INPUT=$(cat 2>/dev/null || echo "{}")
fi

if [ -z "$JSON_INPUT" ] || [ "$JSON_INPUT" = "{}" ]; then
    exit 0
fi

# === EXTRACT EVENT DATA ===
EVENT_TYPE=$(echo "$JSON_INPUT" | jq -r '.event // empty' 2>/dev/null || echo "")
TOOL_NAME=$(echo "$JSON_INPUT" | jq -r '.toolName // empty' 2>/dev/null || echo "")
FILE_PATH=$(echo "$JSON_INPUT" | jq -r '.params.file_path // empty' 2>/dev/null || echo "")
COMMAND_NAME=$(echo "$JSON_INPUT" | jq -r '.command // empty' 2>/dev/null || echo "")

# === INITIALIZE ANALYTICS FILE ===
initialize_analytics() {
    if [ ! -f "$ANALYTICS_FILE" ]; then
        cat > "$ANALYTICS_FILE" << 'EOF'
{
  "schema_version": "1.0",
  "last_updated": "",
  "tracking_enabled": true,
  "project_info": {
    "name": "",
    "type": "project",
    "initialized_date": ""
  },
  "technology_usage": {
    "frameworks": {},
    "libraries": {},
    "patterns": {},
    "commands": {}
  },
  "file_activity": {
    "most_modified": [],
    "by_extension": {},
    "by_directory": {}
  },
  "agent_activity": {
    "most_used_agents": {},
    "command_frequency": {},
    "success_rates": {}
  },
  "suggestions": {
    "create_specialists": [],
    "patterns_to_add": [],
    "commands_to_learn": []
  }
}
EOF
    fi
}

# === TRACK FILE ACTIVITY ===
track_file_activity() {
    local file_path="$1"
    local action="$2"  # read, write, edit
    
    if [ -z "$file_path" ] || [ "$file_path" = "null" ]; then
        return 0
    fi
    
    # Get relative path
    local rel_path="${file_path#$PROJECT_ROOT/}"
    local extension="${file_path##*.}"
    local directory=$(dirname "$rel_path")
    
    # Skip if it's in .claude or .git
    if [[ "$rel_path" == .claude/* ]] || [[ "$rel_path" == .git/* ]]; then
        return 0
    fi
    
    # Update analytics
    local temp_file=$(mktemp)
    
    jq --arg path "$rel_path" \
       --arg ext "$extension" \
       --arg dir "$directory" \
       --arg action "$action" \
       --arg timestamp "$(date -u +%Y-%m-%dT%H:%M:%SZ)" \
       '
       .last_updated = $timestamp |
       .file_activity.by_extension[$ext] = (.file_activity.by_extension[$ext] // 0) + 1 |
       .file_activity.by_directory[$dir] = (.file_activity.by_directory[$dir] // 0) + 1 |
       .file_activity.most_modified += [{
         "path": $path,
         "action": $action,
         "timestamp": $timestamp
       }] |
       .file_activity.most_modified = (.file_activity.most_modified | sort_by(.timestamp) | reverse | .[0:50])
       ' "$ANALYTICS_FILE" > "$temp_file" && mv "$temp_file" "$ANALYTICS_FILE"
}

# === DETECT TECHNOLOGY USAGE ===
detect_technology() {
    local file_path="$1"
    
    if [ ! -f "$file_path" ]; then
        return 0
    fi
    
    local technologies=()
    
    # Check file extension and content
    case "${file_path##*.}" in
        "tsx"|"jsx")
            if grep -q "react\|React\|useState\|useEffect" "$file_path" 2>/dev/null; then
                technologies+=("react")
            fi
            ;;
        "py")
            if grep -q "django\|Django" "$file_path" 2>/dev/null; then
                technologies+=("django")
            elif grep -q "fastapi\|FastAPI" "$file_path" 2>/dev/null; then
                technologies+=("fastapi")
            fi
            ;;
        "js")
            if grep -q "express\|app\.get\|router\." "$file_path" 2>/dev/null; then
                technologies+=("express")
            fi
            ;;
    esac
    
    # Update usage counts
    if [ ${#technologies[@]} -gt 0 ]; then
        local temp_file=$(mktemp)
        
        for tech in "${technologies[@]}"; do
            jq --arg tech "$tech" \
               --arg timestamp "$(date -u +%Y-%m-%dT%H:%M:%SZ)" \
               '
               .technology_usage.frameworks[$tech] = (.technology_usage.frameworks[$tech] // 0) + 1 |
               .last_updated = $timestamp
               ' "$ANALYTICS_FILE" > "$temp_file" && mv "$temp_file" "$ANALYTICS_FILE"
        done
    fi
}

# === TRACK COMMAND USAGE ===
track_command_usage() {
    local command="$1"
    
    if [ -z "$command" ] || [ "$command" = "null" ]; then
        return 0
    fi
    
    local temp_file=$(mktemp)
    
    jq --arg cmd "$command" \
       --arg timestamp "$(date -u +%Y-%m-%dT%H:%M:%SZ)" \
       '
       .agent_activity.command_frequency[$cmd] = (.agent_activity.command_frequency[$cmd] // 0) + 1 |
       .last_updated = $timestamp
       ' "$ANALYTICS_FILE" > "$temp_file" && mv "$temp_file" "$ANALYTICS_FILE"
}

# === GENERATE SUGGESTIONS ===
generate_suggestions() {
    # Only run suggestions once per day
    local last_suggestion_file="$CLAUDE_DIR/.last-suggestion"
    local today=$(date +%Y-%m-%d)
    
    if [ -f "$last_suggestion_file" ] && [ "$(cat "$last_suggestion_file")" = "$today" ]; then
        return 0
    fi
    
    # Analyze usage patterns and generate suggestions
    local temp_file=$(mktemp)
    
    # Find technologies with high usage that might need specialists
    jq '
    .suggestions.create_specialists = [
      .technology_usage.frameworks | to_entries[] |
      select(.value > 10) |
      .key
    ] |
    .suggestions.patterns_to_add = [
      .file_activity.by_extension | to_entries[] |
      select(.value > 20) |
      .key
    ] |
    .suggestions.commands_to_learn = [
      .agent_activity.command_frequency | to_entries[] |
      select(.value > 5) |
      .key
    ]
    ' "$ANALYTICS_FILE" > "$temp_file" && mv "$temp_file" "$ANALYTICS_FILE"
    
    echo "$today" > "$last_suggestion_file"
}

# === MAIN PROCESSING ===
main() {
    # Initialize if needed
    initialize_analytics
    
    # Check if tracking is enabled
    local tracking_enabled=$(jq -r '.tracking_enabled // true' "$ANALYTICS_FILE" 2>/dev/null)
    if [ "$tracking_enabled" != "true" ]; then
        exit 0
    fi
    
    # Process different events
    case "$EVENT_TYPE" in
        "tool_call")
            case "$TOOL_NAME" in
                "Read"|"Write"|"Edit"|"MultiEdit")
                    track_file_activity "$FILE_PATH" "${TOOL_NAME,,}"
                    detect_technology "$FILE_PATH"
                    ;;
            esac
            ;;
        "command")
            track_command_usage "$COMMAND_NAME"
            ;;
    esac
    
    # Generate suggestions periodically
    generate_suggestions
}

# === ERROR HANDLING ===
error_handler() {
    # Silently fail - don't break workflows
    exit 0
}

trap error_handler ERR

# Run main function
main "$@"