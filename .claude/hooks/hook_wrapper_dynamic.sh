#!/bin/bash
# Dynamic Hook Wrapper - Works from any project directory
# Automatically finds <PERSON> and runs hooks without UV dependency

# Get the directory where this script is located
HOOK_DIR="$(cd "$(dirname "$0")" && pwd)"

# Get the hook name from the first argument
HOOK_NAME=$1
shift

# Check if the hook exists
HOOK_PATH="$HOOK_DIR/$HOOK_NAME"

if [ ! -f "$HOOK_PATH" ]; then
    echo "Warning: Hook not found: $HOOK_PATH" >&2
    exit 0  # Exit gracefully to not block workflow
fi

# Try to find Python (prefer python3)
PYTHON_CMD=""
if command -v python3 >/dev/null 2>&1; then
    PYTHON_CMD="python3"
elif command -v python >/dev/null 2>&1; then
    PYTHON_CMD="python"
else
    echo "Warning: Python not found, skipping hook: $HOOK_NAME" >&2
    exit 0  # Exit gracefully
fi

# Run the hook with Python directly (no UV required)
"$PYTHON_CMD" "$HOOK_PATH" "$@"