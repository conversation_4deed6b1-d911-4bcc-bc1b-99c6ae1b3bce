#!/bin/bash

# Agent Orchestration Hook for PIB Method
# Automatically triggers code review workflow after code changes
# Implements: code -> review -> change-implementer pipeline

# Set error handling
set -o pipefail
trap '' PIPE

# === DEPENDENCY VALIDATION ===
if ! command -v jq >/dev/null 2>&1; then
    echo "ERROR: jq is required but not installed. Please install jq." >&2
    exit 1
fi

# === READ JSON INPUT ===
if ! JSON_INPUT=$(cat 2>/dev/null); then
    exit 0
fi

# === EXTRACT EVENT DATA ===
EVENT_TYPE=$(echo "$JSON_INPUT" | jq -r '.event // empty' 2>/dev/null)
TOOL_NAME=$(echo "$JSON_INPUT" | jq -r '.toolName // empty' 2>/dev/null)
TOOL_RESULT=$(echo "$JSON_INPUT" | jq -r '.result // empty' 2>/dev/null)

# Get current project context
CURRENT_DIR=$(pwd)
PROJECT_NAME=$(basename "$CURRENT_DIR")

# Find .claude directory
find_claude_dir() {
    local dir="$CURRENT_DIR"
    while [ "$dir" != "/" ]; do
        if [ -d "$dir/.claude" ]; then
            echo "$dir/.claude"
            return 0
        fi
        dir=$(dirname "$dir")
    done
    return 1
}

CLAUDE_DIR=$(find_claude_dir)
if [ -z "$CLAUDE_DIR" ]; then
    echo "$JSON_INPUT"
    exit 0
fi

# === WORKFLOW STATE MANAGEMENT ===
WORKFLOW_STATE_FILE="$CLAUDE_DIR/current-workflow-state.json"
WORKFLOW_CONFIG_FILE="$CLAUDE_DIR/workflow-config.json"

# Load current workflow state
load_workflow_state() {
    if [ -f "$WORKFLOW_STATE_FILE" ]; then
        CURRENT_WORKFLOW=$(jq -r '.workflow // "development"' "$WORKFLOW_STATE_FILE" 2>/dev/null)
        CURRENT_STAGE=$(jq -r '.currentStage // "development"' "$WORKFLOW_STATE_FILE" 2>/dev/null)
        CURRENT_AGENT=$(jq -r '.currentAgent // "developer"' "$WORKFLOW_STATE_FILE" 2>/dev/null)
    else
        CURRENT_WORKFLOW="development"
        CURRENT_STAGE="development"
        CURRENT_AGENT="developer"
    fi
}

# Update workflow state
update_workflow_state() {
    local new_stage="$1"
    local new_agent="$2"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    cat > "$WORKFLOW_STATE_FILE" <<EOF
{
  "workflow": "$CURRENT_WORKFLOW",
  "currentStage": "$new_stage",
  "currentAgent": "$new_agent",
  "lastUpdate": "$timestamp",
  "triggeredBy": "agent-orchestration-hook",
  "context": {
    "lastTool": "$TOOL_NAME",
    "eventType": "$EVENT_TYPE"
  }
}
EOF
}

# === CODE CHANGE DETECTION ===
detect_code_changes() {
    case "$TOOL_NAME" in
        "Write"|"Edit"|"MultiEdit")
            return 0  # Code change detected
            ;;
        *)
            return 1  # No code change
            ;;
    esac
}

# === AGENT INVOCATION ===
invoke_code_review_agent() {
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # Update workflow state to review stage
    update_workflow_state "review" "code-reviewer"
    
    # Create review instruction directly (no separate hook needed)
    create_review_instruction "$timestamp"
    
    # Log the transition
    echo "$(date): Agent orchestration triggered - transitioning to code review stage" >> "$CLAUDE_DIR/orchestration.log"
    
    return 0
}

# === CLAUDE CODE SUB-AGENT INVOCATION ===
create_review_instruction() {
    local timestamp="$1"
    local modified_files=$(get_recently_modified_files)
    local review_scope=$(determine_review_scope "$modified_files")
    
    # Create Claude Code sub-agent invocation instruction
    local review_instruction_file="$CLAUDE_DIR/pending-review-instruction.md"
    cat > "$review_instruction_file" <<EOF
# Automatic Code Review with Claude Code Sub-Agent

## Context
- **Triggered by**: $TOOL_NAME operation
- **Project**: $PROJECT_NAME
- **Review Scope**: $review_scope
- **Timestamp**: $timestamp

## Files to Review
$(echo "$modified_files" | sed 's/^/- /')

$(add_planning_context_to_review "$modified_files")

## Sub-Agent Invocation
Please use the code-reviewer sub-agent to perform a comprehensive code review:

> Use the code-reviewer sub-agent to review the recently modified files with focus on LEVER framework compliance, code quality, and PIB standards adherence.

### Files for Review:
$(echo "$modified_files" | sed 's/^/- /')

### Review Focus Areas:
- **LEVER Framework Compliance**: Assess Leverage, Extend, Verify, Eliminate, Reduce principles
- **Code Quality**: Maintainability, readability, and best practices
- **PIB Standards**: Adherence to project coding standards and conventions
- **Security & Performance**: Basic security and performance considerations
- **Integration**: Proper integration with existing codebase

### Expected Deliverables:
1. Comprehensive review report with LEVER compliance scoring
2. Specific improvement recommendations with Context7 research backing
3. Priority-based feedback (Critical/High/Medium/Low)
4. Clear next steps for any required changes

## Alternative Direct Approach
If the sub-agent automatic delegation doesn't trigger, you can explicitly request:

\`\`\`
Please have the code-reviewer sub-agent review these files: $(echo "$modified_files" | tr '\n' ' ')
Focus on $review_scope with comprehensive LEVER framework assessment.
\`\`\`

## Change Implementation Process
If changes are needed:
1. The code-reviewer sub-agent will provide detailed feedback
2. Use the change-implementer sub-agent to address the feedback
3. Validate changes with the qa-tester sub-agent if needed
4. Complete the review cycle for quality assurance

---
*Generated by PIB Agent Orchestration System - Enhanced for Claude Code Sub-Agents*
EOF
    
    echo "$(date): Claude Code sub-agent review instruction created at: $review_instruction_file" >> "$CLAUDE_DIR/orchestration.log"
}

# === PLANNING CONTEXT INTEGRATION ===
add_planning_context_to_review() {
    local modified_files="$1"
    
    # Check if planning system exists
    local plans_dir="$CLAUDE_DIRsession -> /planning:plan-sessions"
    if [[ ! -d "$plans_dir" ]]; then
        return 0
    fi
    
    # Look for relevant planning context based on modified files
    local active_plans_dir="$plans_dir/active"
    local relevant_planning=""
    
    if [[ -d "$active_plans_dir" ]]; then
        # Extract keywords from modified file paths
        local file_keywords
        file_keywords=$(echo "$modified_files" | tr '/' ' ' | tr '[:upper:]' '[:lower:]' | grep -oE '\b\w{3,}\b' | sort -u | head -5)
        
        # Look for planning sessions that match modified files
        for planning_file in "$active_plans_dir"/*.md; do
            if [[ -f "$planning_file" ]]; then
                local filename=$(basename "$planning_file")
                
                # Check if any keyword matches the planning file
                for keyword in $file_keywords; do
                    if echo "$filename" | grep -qi "$keyword" || head -50 "$planning_file" | grep -qi "$keyword"; then
                        if [[ -z "$relevant_planning" ]]; then
                            relevant_planning="## Planning Context

**Relevant Planning Documentation Found**: The following planning context applies to this review:

"
                        fi
                        relevant_planning+="### $(basename "$planning_file" .md)
$(head -20 "$planning_file" | tail -10)

"
                        break
                    fi
                done
                
                # Always include context bridge files for cross-session continuity
                if echo "$filename" | grep -q "context-bridge"; then
                    if [[ -z "$relevant_planning" ]]; then
                        relevant_planning="## Planning Context

**Cross-Session Context Available**: Planning context from previous sessions:

"
                    fi
                    relevant_planning+="### Context Bridge
$(head -30 "$planning_file" | tail -15)

"
                fi
            fi
        done
        
        # Add integration instructions if planning context was found
        if [[ -n "$relevant_planning" ]]; then
            relevant_planning+="**Review Integration Points**:
- Validate changes align with epic planning decisions
- Ensure LEVER framework compliance as defined in planning
- Check sub-agent coordination requirements from orchestration plans
- Maintain architectural consistency with planning context

"
        fi
    fi
    
    echo "$relevant_planning"
}

# === FILE DETECTION FUNCTIONS ===
get_recently_modified_files() {
    local files=""
    
    # Try to get files from git (staged and unstaged changes)
    if command -v git >/dev/null 2>&1; then
        # Get staged files
        local staged_files=$(git diff --cached --name-only 2>/dev/null)
        # Get unstaged files
        local unstaged_files=$(git diff --name-only 2>/dev/null)
        # Get untracked files (recent ones)
        local untracked_files=$(git ls-files --others --exclude-standard 2>/dev/null | head -10)
        
        files="$staged_files $unstaged_files $untracked_files"
    fi
    
    # If no git files found, use recently modified files
    if [ -z "$files" ]; then
        files=$(find . -name "*.js" -o -name "*.ts" -o -name "*.py" -o -name "*.md" -o -name "*.json" -o -name "*.yml" -o -name "*.yaml" | head -20 2>/dev/null)
    fi
    
    echo "$files" | tr ' ' '\n' | grep -v "^$" | head -10
}

determine_review_scope() {
    local modified_files="$1"
    local scope="quality"  # default scope
    
    # Analyze file types to determine appropriate review focus
    if echo "$modified_files" | grep -q -E "\.(py|js|ts|go|java|cpp|c)$"; then
        scope="implementation"
    elif echo "$modified_files" | grep -q -E "\.(yml|yaml|json|config)$"; then
        scope="configuration"
    elif echo "$modified_files" | grep -q -E "\.(md|txt|rst)$"; then
        scope="documentation"
    elif echo "$modified_files" | grep -q -E "(test|spec)"; then
        scope="testing"
    fi
    
    echo "$scope"
}

invoke_change_implementer_agent() {
    local change_instruction_file="$CLAUDE_DIR/pending-change-implementation.md"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # Create Claude Code sub-agent change implementation instruction
    cat > "$change_instruction_file" <<EOF
# Automatic Change Implementation with Claude Code Sub-Agent

## Context
- **Triggered by**: Review completion
- **Project**: $PROJECT_NAME
- **Timestamp**: $timestamp
- **Previous Stage**: Code review completed

## Sub-Agent Invocation
Please use the change-implementer sub-agent to address the feedback from the code review:

> Use the change-implementer sub-agent to implement the changes requested in the recent code review feedback.

### Implementation Requirements:
- Address all code review feedback systematically
- Maintain existing functionality while making improvements
- Apply LEVER framework principles during changes
- Use Context7 research for implementing best practices
- Test all modifications for correctness
- Update documentation as needed

### Expected Process:
1. **Analyze Feedback**: Parse review feedback thoroughly
2. **Research Solutions**: Use Context7 for implementation patterns
3. **Implement Changes**: Apply modifications systematically
4. **Validate Changes**: Test changes individually and together
5. **Document Updates**: Update affected documentation
6. **Prepare Report**: Create comprehensive change implementation report

### Quality Requirements:
- All review feedback items must be addressed
- LEVER framework compliance must be maintained or improved
- No new issues should be introduced
- Code style and conventions must be preserved
- Integration with existing code must be validated

## Alternative Direct Approach
If the sub-agent automatic delegation doesn't trigger, you can explicitly request:

\`\`\`
Please have the change-implementer sub-agent address the code review feedback and implement the requested changes with full LEVER compliance.
\`\`\`

## Next Steps After Implementation:
1. Changes will be validated by the change-implementer sub-agent
2. Updated code will be prepared for re-review if needed
3. Implementation report will be generated
4. Workflow will continue to next stage (testing/validation)

---
*Generated by PIB Agent Orchestration System - Enhanced for Claude Code Sub-Agents*
EOF
    
    # Update workflow state to implementation stage
    update_workflow_state "implementation" "change-implementer"
    
    # Log the transition
    echo "$(date): Claude Code sub-agent change implementation instruction created at: $change_instruction_file" >> "$CLAUDE_DIR/orchestration.log"
    
    return 0
}

# === WORKFLOW ORCHESTRATION LOGIC ===
orchestrate_workflow() {
    load_workflow_state
    
    case "$EVENT_TYPE" in
        "PostToolUse")
            if detect_code_changes; then
                case "$CURRENT_STAGE" in
                    "development")
                        # Code change during development -> trigger review
                        invoke_code_review_agent
                        echo "$(date): Code change detected in development stage - triggering code review" >> "$CLAUDE_DIR/orchestration.log"
                        ;;
                    "review")
                        # Code change during review -> may need re-review
                        echo "$(date): Code change detected during review stage - may need re-review" >> "$CLAUDE_DIR/orchestration.log"
                        ;;
                    "implementation")
                        # Code change during implementation -> back to review
                        invoke_code_review_agent
                        echo "$(date): Code change detected in implementation stage - triggering re-review" >> "$CLAUDE_DIR/orchestration.log"
                        ;;
                esac
            fi
            ;;
        "ReviewComplete")
            # Review completed -> trigger change implementer
            if [ "$CURRENT_STAGE" = "review" ]; then
                invoke_change_implementer_agent
            fi
            ;;
        "ImplementationComplete")
            # Implementation completed -> back to development
            update_workflow_state "development" "developer"
            echo "$(date): Implementation complete - returning to development stage" >> "$CLAUDE_DIR/orchestration.log"
            ;;
    esac
}

# === INTELLIGENT WORKFLOW DETECTION ===
# Check if we should trigger automatic review based on context
should_trigger_automatic_review() {
    # Don't trigger during certain operations
    case "$TOOL_NAME" in
        "TodoWrite"|"TodoRead")
            return 1  # Skip todo operations
            ;;
        "Bash")
            # Skip bash operations unless they're code generation/build
            if echo "$TOOL_RESULT" | grep -q -E "(build|compile|generate|create.*\.js|create.*\.py|create.*\.ts)"; then
                return 0
            fi
            return 1
            ;;
        "Write"|"Edit"|"MultiEdit")
            # Always trigger for code changes
            return 0
            ;;
    esac
    return 1
}

# === DEV WORKFLOW INTEGRATION ===
# Check if dev workflow enhancer should handle this
DEV_WORKFLOW_ENHANCER="$CLAUDE_DIR/hooks> /workflows:dev-command-workflow-enhancer.sh"
if [ -x "$DEV_WORKFLOW_ENHANCER" ]; then
    # Pass JSON to dev workflow enhancer and capture any modifications
    DEV_ENHANCED_JSON=$(echo "$JSON_INPUT" | "$DEV_WORKFLOW_ENHANCER")
    if [ $? -eq 0 ] && [ -n "$DEV_ENHANCED_JSON" ]; then
        JSON_INPUT="$DEV_ENHANCED_JSON"
    fi
fi

# === MAIN ORCHESTRATION EXECUTION ===
if [ "$EVENT_TYPE" = "PostToolUse" ] && should_trigger_automatic_review; then
    orchestrate_workflow
fi

# === AGENT CONTEXT INJECTION ===
# Inject current agent context into the environment for next command
if [ -f "$WORKFLOW_STATE_FILE" ]; then
    export PIB_CURRENT_AGENT="$CURRENT_AGENT"
    export PIB_CURRENT_STAGE="$CURRENT_STAGE"
    export PIB_WORKFLOW="$CURRENT_WORKFLOW"
fi

# CRITICAL: Pass through the JSON for the next hook/tool
echo "$JSON_INPUT"

# Always exit 0 - orchestration is informational/workflow management
exit 0