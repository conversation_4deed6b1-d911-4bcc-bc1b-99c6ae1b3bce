#!/bin/bash

# Dev Workflow Enhancer Hook for PIB Method
# Enhances the > /workflows:dev-command command workflow with intelligent context engineering and state management
# Integrates with agent-orchestration-hook.sh for seamless workflow coordination

# Set error handling
set -o pipefail
trap '' PIPE

# === DEPENDENCY VALIDATION ===
if ! command -v jq >> /workflows:dev-command/null 2>&1; then
    echo "ERROR: jq is required but not installed. Please install jq." >&2
    exit 1
fi

# === READ JSON INPUT ===
if ! JSON_INPUT=$(cat 2>> /workflows:dev-command/null); then
    exit 0
fi

# === EXTRACT EVENT DATA ===
EVENT_TYPE=$(echo "$JSON_INPUT" | jq -r '.event // empty' 2>> /workflows:dev-command/null)
TOOL_NAME=$(echo "$JSON_INPUT" | jq -r '.toolName // empty' 2>> /workflows:dev-command/null)
TOOL_RESULT=$(echo "$JSON_INPUT" | jq -r '.result // empty' 2>> /workflows:dev-command/null)

# Get current project context
CURRENT_DIR=$(pwd)
PROJECT_NAME=$(basename "$CURRENT_DIR")

# Find .claude directory
find_claude_dir() {
    local dir="$CURRENT_DIR"
    while [ "$dir" != "/" ]; do
        if [ -d "$dir/.claude" ]; then
            echo "$dir/.claude"
            return 0
        fi
        dir=$(dirname "$dir")
    done
    return 1
}

CLAUDE_DIR=$(find_claude_dir)
if [ -z "$CLAUDE_DIR" ]; then
    echo "$JSON_INPUT"
    exit 0
fi

# === DEV WORKFLOW STATE MANAGEMENT ===
DEV_WORKFLOW_STATE_DIR="$CLAUDE_DIR/state> /workflows:dev-command-workflows"
CURRENT_DEV_WORKFLOW="$DEV_WORKFLOW_STATE_DIR/current-dev-workflow.json"
DEV_WORKFLOW_LOG="$DEV_WORKFLOW_STATE_DIR> /workflows:dev-command-workflow.log"

# Ensure dev workflow state directory exists
mkdir -p "$DEV_WORKFLOW_STATE_DIR"

# === DEV COMMAND DETECTION ===
detect_dev_command() {
    # Check if this is a dev command invocation
    if echo "$TOOL_RESULT" | grep -q "> /workflows:dev-command\s"; then
        return 0  # Dev command detected
    fi
    return 1  # No dev command
}

# === EXTRACT DEV COMMAND PARAMETERS ===
extract_dev_parameters() {
    local dev_command="$1"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # Parse the dev command for parameters
    local requirement=$(echo "$dev_command" | sed -n 's/^.*\> /workflows:dev-command\s*"\([^"]*\)".*/\1/p')
    if [ -z "$requirement" ]; then
        requirement=$(echo "$dev_command" | sed -n 's/^.*\> /workflows:dev-command\s*\([^-][^ ]*.*\)$/\1/p' | sed 's/--.*$//')
    fi
    
    # Parse optional parameters
    local priority="medium"
    local focus="general"
    local agent="auto"
    local style="standard"
    
    if echo "$dev_command" | grep -q -- "--priority="; then
        priority=$(echo "$dev_command" | sed -n 's/.*--priority=\([^ ]*\).*/\1/p')
    fi
    
    if echo "$dev_command" | grep -q -- "--focus="; then
        focus=$(echo "$dev_command" | sed -n 's/.*--focus=\([^ ]*\).*/\1/p')
    fi
    
    if echo "$dev_command" | grep -q -- "--agent="; then
        agent=$(echo "$dev_command" | sed -n 's/.*--agent=\([^ ]*\).*/\1/p')
    fi
    
    if echo "$dev_command" | grep -q -- "--style="; then
        style=$(echo "$dev_command" | sed -n 's/.*--style=\([^ ]*\).*/\1/p')
    fi
    
    # Use Context Engineering Engine to create enriched context
    local context_engine="$CLAUDE_DIR/hooks/context-engineering-engine.sh"
    local context_package=""
    
    if [ -x "$context_engine" ]; then
        # Determine task complexity based on requirement length and keywords
        local complexity="medium"
        local word_count=$(echo "$requirement" | wc -w)
        
        if [[ $word_count -lt 5 ]]; then
            complexity="simple"
        elif [[ $word_count -gt 15 ]] || echo "$requirement" | grep -qi "complex\|advanced\|system\|architecture\|integration"; then
            complexity="complex"
        fi
        
        if echo "$requirement" | grep -qi "expert\|comprehensive\|full-stack\|end-to-end"; then
            complexity="expert"
        fi
        
        # Create context package using Context Engineering Engine
        context_package=$("$context_engine" create_package "james" "dev" "$requirement" "$complexity")
        
        # Extract relevant context for logging
        local relevant_context=""
        if [ -f "$context_package" ]; then
            relevant_context=$(head -20 "$context_package" | tail -10)
        fi
        
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] Context Engineering: Created package $context_package (complexity: $complexity)" >> "$DEV_WORKFLOW_LOG"
    else
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] Warning: Context Engineering Engine not available" >> "$DEV_WORKFLOW_LOG"
    fi
    
    # Create dev workflow specification with Claude Code sub-agent support
    cat > "$CURRENT_DEV_WORKFLOW" <<EOF
{
  "workflowId": "dev-$(date +%s)",
  "requirement": "$requirement",
  "parameters": {
    "priority": "$priority",
    "focus": "$focus",
    "agent": "$agent",
    "style": "$style"
  },
  "status": "analyzing",
  "phase": "requirement-analysis",
  "startTime": "$timestamp",
  "lastUpdate": "$timestamp",
  "context": {
    "projectName": "$PROJECT_NAME",
    "workingDirectory": "$CURRENT_DIR"
  },
  "mcpTools": {
    "zenRequired": true,
    "context7Required": true,
    "analysisMode": "comprehensive"
  },
  "claudeCodeSubAgents": {
    "primary": "dev-agent",
    "reviewers": ["code-reviewer"],
    "implementers": ["change-implementer"],
    "testers": ["qa-tester"],
    "coordinators": ["orchestrator"]
  },
  "subAgentInvocation": {
    "automatic": true,
    "explicitBackup": true,
    "contextPreservation": "enhanced"
  }
}
EOF
    
    log_dev_workflow "Dev workflow initialized: $requirement"
    return 0
}

# === DEV WORKFLOW LOGGING ===
log_dev_workflow() {
    local message="$1"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    echo "[$timestamp] $message" >> "$DEV_WORKFLOW_LOG"
}

# === CREATE WORK SPECIFICATION ===
create_work_specification() {
    local workflow_data=$(cat "$CURRENT_DEV_WORKFLOW" 2>> /workflows:dev-command/null)
    local requirement=$(echo "$workflow_data" | jq -r '.requirement // ""')
    local priority=$(echo "$workflow_data" | jq -r '.parameters.priority // "medium"')
    local focus=$(echo "$workflow_data" | jq -r '.parameters.focus // "general"')
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # Create work specification file
    local work_spec_file="$DEV_WORKFLOW_STATE_DIR/work-specification.md"
    cat > "$work_spec_file" <<EOF
# Development Work Specification

## Generated Context
- **Workflow ID**: $(echo "$workflow_data" | jq -r '.workflowId // ""')
- **Timestamp**: $timestamp
- **Priority**: $priority
- **Focus Area**: $focus
- **Project**: $PROJECT_NAME

## Requirements Analysis

### Primary Objective
$requirement

### Quality Requirements
$(generate_quality_requirements "$focus" "$priority")

### Implementation Guidance
$(generate_implementation_guidance "$focus" "$requirement")

## Technical Context

### Research Phase Required
- **Context7 MCP**: Research existing patterns and implementations
- **Zen MCP**: Multi-model analysis for optimal approach
- **Pattern Analysis**: Identify reusable components and approaches

### Recommended Investigation
1. Existing similar functionality in codebase
2. Relevant libraries and frameworks for $focus
3. Best practices for $requirement implementation
4. Integration points and dependencies

## Claude Code Sub-Agent Assignment Strategy

### Primary Implementation
- **Sub-Agent**: dev-agent
- **Focus**: $focus development with LEVER framework compliance
- **Context**: Enhanced with MCP research findings
- **Invocation**: Automatic delegation through Claude Code

### Review Strategy
- **Sub-Agent**: code-reviewer (automatic invocation after code changes)
- **Additional Reviewers**: $(determine_review_strategy "$focus" "$priority")

### Change Implementation
- **Sub-Agent**: change-implementer (triggered after review feedback)
- **Testing**: qa-tester (comprehensive validation)

### Orchestration
- **Sub-Agent**: orchestrator (for complex multi-phase workflows)

### Quality Gates
- LEVER framework compliance validation
- Security and performance considerations
- Integration testing requirements
- Documentation and maintainability standards

## Implementation Plan

### Phase 1: Context Engineering (Status: pending)
- [ ] Zen MCP requirement analysis
- [ ] Context7 MCP pattern research
- [ ] Work specification enrichment
- [ ] Agent context preparation

### Phase 2: Development (Status: pending)
- [ ] Implementation with research guidance
- [ ] LEVER framework compliance
- [ ] Initial testing and validation
- [ ] Documentation creation

### Phase 3: Quality Assurance (Status: pending)
- [ ] Enhanced code review with MCP tools
- [ ] Pattern validation and compliance
- [ ] Integration testing
- [ ] Final validation and approval

## Context Preservation

### MCP Research Results
*To be populated by Context7 MCP during analysis phase*

### Zen Analysis Results
*To be populated by Zen MCP during analysis phase*

### Implementation Context
*To be populated during development phase*

---
*Generated by PIB Dev Workflow Enhancer*
*Part of the PIB Method's intelligent development system*
EOF
    
    log_dev_workflow "Work specification created: $work_spec_file"
    
    # Update workflow status
    update_dev_workflow_status "specification-created" "context-engineering"
}

# === QUALITY REQUIREMENTS GENERATOR ===
generate_quality_requirements() {
    local focus="$1"
    local priority="$2"
    
    case "$focus" in
        "security")
            echo "- Security-first development approach"
            echo "- Input validation and sanitization"
            echo "- Authentication and authorization integration"
            echo "- Security testing and vulnerability assessment"
            ;;
        "performance")
            echo "- Performance benchmarking and optimization"
            echo "- Efficient algorithms and data structures"
            echo "- Resource usage monitoring"
            echo "- Load testing and scalability considerations"
            ;;
        "ui")
            echo "- User experience and accessibility standards"
            echo "- Responsive design and cross-browser compatibility"
            echo "- Component reusability and maintainability"
            echo "- Design system compliance"
            ;;
        "api")
            echo "- RESTful API design principles"
            echo "- Comprehensive API documentation"
            echo "- Error handling and status codes"
            echo "- API versioning and backward compatibility"
            ;;
        "data")
            echo "- Data integrity and consistency"
            echo "- Efficient query optimization"
            echo "- Data validation and migration strategies"
            echo "- Backup and recovery considerations"
            ;;
        *)
            echo "- Code quality and maintainability standards"
            echo "- Comprehensive testing coverage"
            echo "- Documentation and code clarity"
            echo "- PIB operational guidelines compliance"
            ;;
    esac
    
    # Add priority-specific requirements
    if [ "$priority" = "high" ] || [ "$priority" = "critical" ]; then
        echo "- Enhanced code review with multiple validators"
        echo "- Comprehensive testing including edge cases"
        echo "- Performance impact assessment"
        echo "- Security impact analysis"
    fi
}

# === IMPLEMENTATION GUIDANCE GENERATOR ===
generate_implementation_guidance() {
    local focus="$1"
    local requirement="$2"
    
    echo "### LEVER Framework Compliance"
    echo "- **Leverage**: Research existing patterns using Context7 MCP"
    echo "- **Extend**: Identify existing functionality to extend rather than create new"
    echo "- **Verify**: Use Zen MCP tools for continuous validation"
    echo "- **Eliminate**: Detect and remove duplication"
    echo "- **Reduce**: Minimize complexity through intelligent design"
    echo ""
    echo "### MCP Tool Integration"
    echo "- Use Context7 for library and pattern research"
    echo "- Use Zen debug tools for complex technical issues"
    echo "- Use Zen codereview for quality validation"
    echo "- Use Zen analyze for impact assessment"
    echo ""
    echo "### Implementation Approach"
    case "$focus" in
        "security")
            echo "- Follow OWASP security guidelines"
            echo "- Implement defense in depth strategies"
            echo "- Use established security libraries and frameworks"
            ;;
        "performance")
            echo "- Profile and benchmark current performance"
            echo "- Identify bottlenecks before optimization"
            echo "- Use proven performance patterns and algorithms"
            ;;
        "ui")
            echo "- Follow established design system patterns"
            echo "- Ensure accessibility compliance (WCAG)"
            echo "- Test across different devices and browsers"
            ;;
        "api")
            echo "- Follow OpenAPI specification standards"
            echo "- Implement comprehensive error handling"
            echo "- Ensure proper HTTP status code usage"
            ;;
        "data")
            echo "- Design for data integrity and consistency"
            echo "- Implement proper validation and constraints"
            echo "- Consider migration and versioning strategies"
            ;;
        *)
            echo "- Follow PIB coding standards and conventions"
            echo "- Prioritize readability and maintainability"
            echo "- Implement comprehensive testing strategy"
            ;;
    esac
}

# === REVIEW STRATEGY GENERATOR ===
determine_review_strategy() {
    local focus="$1"
    local priority="$2"
    
    case "$focus" in
        "security")
            echo "- **Primary Reviewer**: Security specialist or senior developer"
            echo "- **Security Review**: OWASP compliance and vulnerability assessment"
            echo "- **Zen Security Audit**: Comprehensive security analysis using MCP tools"
            ;;
        "performance")
            echo "- **Primary Reviewer**: Performance specialist or architect"
            echo "- **Performance Review**: Benchmarking and optimization validation"
            echo "- **Load Testing**: Performance under realistic conditions"
            ;;
        "ui")
            echo "- **Primary Reviewer**: UI/UX specialist or frontend lead"
            echo "- **Design Review**: Design system compliance and user experience"
            echo "- **Accessibility Review**: WCAG compliance validation"
            ;;
        *)
            echo "- **Primary Reviewer**: Senior developer or tech lead"
            echo "- **Code Review**: Quality, maintainability, and standards compliance"
            echo "- **Integration Review**: System integration and compatibility"
            ;;
    esac
    
    if [ "$priority" = "high" ] || [ "$priority" = "critical" ]; then
        echo "- **Enhanced Review**: Multiple reviewers for critical changes"
        echo "- **Architect Review**: Technical architecture and design validation"
        echo "- **Security Validation**: Additional security review regardless of focus"
    fi
}

# === UPDATE DEV WORKFLOW STATUS ===
update_dev_workflow_status() {
    local new_status="$1"
    local new_phase="$2"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    if [ -f "$CURRENT_DEV_WORKFLOW" ]; then
        # Update the workflow status
        local updated_workflow=$(jq --arg status "$new_status" --arg phase "$new_phase" --arg timestamp "$timestamp" '
            .status = $status |
            .phase = $phase |
            .lastUpdate = $timestamp
        ' "$CURRENT_DEV_WORKFLOW")
        
        echo "$updated_workflow" > "$CURRENT_DEV_WORKFLOW"
        log_dev_workflow "Workflow status updated: $new_status, phase: $new_phase"
    fi
}

# === TRIGGER CONTEXT ENGINEERING ===
trigger_context_engineering() {
    local workflow_data=$(cat "$CURRENT_DEV_WORKFLOW" 2>> /workflows:dev-command/null)
    local requirement=$(echo "$workflow_data" | jq -r '.requirement // ""')
    
    # Create context engineering instruction
    local context_instruction="$DEV_WORKFLOW_STATE_DIR/context-engineering-instruction.md"
    cat > "$context_instruction" <<EOF
# Context Engineering Required

## Workflow Context
- **Requirement**: $requirement
- **Project**: $PROJECT_NAME
- **Workflow**: $(echo "$workflow_data" | jq -r '.workflowId // ""')

## Required MCP Analysis

### Zen MCP Tasks
1. **Requirement Analysis**: Use Zen chat or thinkdeep for comprehensive requirement analysis
2. **Technical Assessment**: Evaluate complexity and implementation approaches
3. **Quality Strategy**: Determine appropriate quality gates and validation approaches

### Context7 MCP Tasks
1. **Pattern Research**: Search for existing implementation patterns
2. **Library Research**: Identify relevant libraries and frameworks
3. **Best Practices**: Research industry best practices for this type of implementation

## Expected Outputs
1. **Enhanced Work Specification**: Enriched with research findings and technical context
2. **Implementation Plan**: Detailed plan with specific tasks and approaches
3. **Agent Context**: Prepared context for seamless agent handoff

## Next Steps
After context engineering completion:
1. Enhanced work specification will be created
2. Agent will be assigned with enriched context
3. Development phase will begin with full context and guidance

---
*Generated by PIB Dev Workflow Enhancer*
EOF
    
    log_dev_workflow "Context engineering instruction created: $context_instruction"
    update_dev_workflow_status "context-engineering-required" "context-engineering"
    
    # Create a pending instruction for the user to see
    cp "$context_instruction" "$CLAUDE_DIR/pending-context-engineering.md"
}

# === AGENT CONTEXT ENHANCEMENT ===
enhance_agent_context() {
    local workflow_data=$(cat "$CURRENT_DEV_WORKFLOW" 2>> /workflows:dev-command/null)
    local requirement=$(echo "$workflow_data" | jq -r '.requirement // ""')
    local focus=$(echo "$workflow_data" | jq -r '.parameters.focus // "general"')
    
    # Create enhanced agent instruction
    local agent_instruction="$DEV_WORKFLOW_STATE_DIR/enhanced-agent-instruction.md"
    cat > "$agent_instruction" <<EOF
# Enhanced Agent Development Instruction

## Development Context
- **Primary Objective**: $requirement
- **Focus Area**: $focus
- **Priority**: $(echo "$workflow_data" | jq -r '.parameters.priority // "medium"')
- **Style**: $(echo "$workflow_data" | jq -r '.parameters.style // "standard"')

## Enhanced Context Available
- **Work Specification**: Complete development specification with requirements and guidance
- **Research Findings**: Pattern research and library recommendations from Context7 MCP
- **Technical Analysis**: Multi-model analysis and recommendations from Zen MCP
- **Quality Framework**: Specific quality gates and validation criteria

## Implementation Instructions
Please review the complete work specification and implement the requirements following:

1. **LEVER Framework Compliance**: Leverage research findings to minimize new code creation
2. **Quality Standards**: Follow enhanced quality requirements for $focus development
3. **MCP Tool Usage**: Continue using MCP tools for validation and assistance during development
4. **Context Preservation**: Update work specification with implementation progress and decisions

## Files to Review
- Work Specification: \`$DEV_WORKFLOW_STATE_DIR/work-specification.md\`
- Context Engineering Results: \`$DEV_WORKFLOW_STATE_DIR/context-engineering-results.md\`
- Implementation Plan: \`$DEV_WORKFLOW_STATE_DIR/implementation-plan.md\`

## Quality Gates
- Code review using Zen codereview tools
- Pattern validation against research findings
- Integration testing and validation
- Documentation and maintainability review

---
*Generated by PIB Dev Workflow Enhancer*
*Ready for James (Developer Agent) implementation*
EOF
    
    log_dev_workflow "Enhanced agent instruction created: $agent_instruction"
    update_dev_workflow_status "agent-context-ready" "development"
    
    # Copy to main claude directory for visibility
    cp "$agent_instruction" "$CLAUDE_DIR/pending-enhanced-development.md"
}

# === MAIN DEV WORKFLOW ORCHESTRATION ===
orchestrate_dev_workflow() {
    if detect_dev_command; then
        local dev_command="$TOOL_RESULT"
        log_dev_workflow "Dev command detected: $dev_command"
        
        # Extract parameters and create initial workflow
        extract_dev_parameters "$dev_command"
        
        # Create work specification
        create_work_specification
        
        # Trigger context engineering phase
        trigger_context_engineering
        
        echo "$(date): Dev workflow initiated - context engineering phase triggered" >> "$CLAUDE_DIR/orchestration.log"
        return 0
    fi
    return 1
}

# === CONTEXT ENGINEERING COMPLETION DETECTION ===
detect_context_engineering_completion() {
    # Check if context engineering results are available
    if [ -f "$DEV_WORKFLOW_STATE_DIR/context-engineering-results.md" ]; then
        local current_status=$(jq -r '.status // ""' "$CURRENT_DEV_WORKFLOW" 2>> /workflows:dev-command/null)
        if [ "$current_status" = "context-engineering-required" ]; then
            log_dev_workflow "Context engineering completion detected"
            enhance_agent_context
            return 0
        fi
    fi
    return 1
}

# === MAIN EXECUTION ===
if [ "$EVENT_TYPE" = "PostToolUse" ]; then
    # Check for dev command initiation
    if orchestrate_dev_workflow; then
        # Dev workflow initiated
        :
    elif detect_context_engineering_completion; then
        # Context engineering completed, enhance agent context
        :
    fi
fi

# === PASS THROUGH JSON ===
echo "$JSON_INPUT"
exit 0