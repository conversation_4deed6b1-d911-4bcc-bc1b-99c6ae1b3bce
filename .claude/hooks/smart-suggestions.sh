#!/bin/bash

# Smart Command Suggestions Hook
# Provides contextual suggestions based on recent activity and project state

set -euo pipefail

# === CONFIGURATION ===
CLAUDE_DIR="$(dirname "$0")/.."
SUGGESTIONS_FILE="$CLAUDE_DIR/smart-suggestions.json"
ANALYTICS_FILE="$CLAUDE_DIR/usage-analytics.json"
PROJECT_ROOT="$(dirname "$CLAUDE_DIR")"

# === DEPENDENCY CHECK ===
if ! command -v jq >/dev/null 2>&1; then
    exit 0
fi

# === READ EVENT DATA ===
JSON_INPUT=""
if [ -t 0 ]; then
    exit 0
else
    JSON_INPUT=$(cat 2>/dev/null || echo "{}")
fi

if [ -z "$JSON_INPUT" ] || [ "$JSON_INPUT" = "{}" ]; then
    exit 0
fi

# === EXTRACT EVENT DATA ===
EVENT_TYPE=$(echo "$JSON_INPUT" | jq -r '.event // empty' 2>/dev/null || echo "")
TOOL_NAME=$(echo "$JSON_INPUT" | jq -r '.toolName // empty' 2>/dev/null || echo "")
FILE_PATH=$(echo "$JSON_INPUT" | jq -r '.params.file_path // empty' 2>/dev/null || echo "")
COMMAND_NAME=$(echo "$JSON_INPUT" | jq -r '.command // empty' 2>/dev/null || echo "")

# === INITIALIZE SUGGESTIONS ===
initialize_suggestions() {
    if [ ! -f "$SUGGESTIONS_FILE" ]; then
        cat > "$SUGGESTIONS_FILE" << 'EOF'
{
  "last_updated": "",
  "context": {
    "recent_activity": [],
    "project_state": "unknown",
    "detected_patterns": []
  },
  "active_suggestions": [],
  "suggestion_history": [],
  "user_preferences": {
    "suggestion_frequency": "normal",
    "categories_enabled": ["workflow", "optimization", "learning"],
    "dismissed_suggestions": []
  }
}
EOF
    fi
}

# === DETECT PROJECT STATE ===
detect_project_state() {
    local state="development"
    
    # Check for common project states
    if [ -f "$PROJECT_ROOT/package.json" ] && [ ! -d "$PROJECT_ROOT/node_modules" ]; then
        state="setup"
    elif [ -f "$PROJECT_ROOT/docs/prd.md" ] && [ -f "$PROJECT_ROOT/docs/architecture.md" ]; then
        state="documented"
    elif find "$PROJECT_ROOT" -name "*.test.*" -o -name "*.spec.*" | head -1 | grep -q .; then
        state="testing"
    elif [ -d "$PROJECT_ROOT/.git" ]; then
        local commits=$(git -C "$PROJECT_ROOT" rev-list --count HEAD 2>/dev/null || echo "0")
        if [ "$commits" -gt 50 ]; then
            state="mature"
        fi
    fi
    
    echo "$state"
}

# === ANALYZE RECENT ACTIVITY ===
analyze_recent_activity() {
    local activity_pattern=""
    
    # Check last 10 file operations
    if [ -f "$ANALYTICS_FILE" ]; then
        local recent_files=$(jq -r '.file_activity.most_modified[:10][].path' "$ANALYTICS_FILE" 2>/dev/null || echo "")
        
        # Detect patterns
        if echo "$recent_files" | grep -q "\.test\.\|\.spec\."; then
            activity_pattern="testing"
        elif echo "$recent_files" | grep -q "docs/"; then
            activity_pattern="documentation"
        elif echo "$recent_files" | grep -q "components/\|pages/"; then
            activity_pattern="frontend"
        elif echo "$recent_files" | grep -q "api/\|routes/\|controllers/"; then
            activity_pattern="backend"
        elif echo "$recent_files" | grep -q "README\|\.md$"; then
            activity_pattern="documentation"
        fi
    fi
    
    echo "$activity_pattern"
}

# === GENERATE CONTEXTUAL SUGGESTIONS ===
generate_suggestions() {
    local suggestions=()
    local project_state=$(detect_project_state)
    local activity_pattern=$(analyze_recent_activity)
    local timestamp=$(date -u +%Y-%m-%dT%H:%M:%SZ)
    
    # State-based suggestions
    case "$project_state" in
        "setup")
            suggestions+=('{
                "id": "project-init",
                "type": "workflow",
                "priority": "high",
                "title": "Initialize Project Documentation",
                "description": "Run project-init to create comprehensive project documentation",
                "command": "/workflows:project-init --existing-project",
                "context": "New project detected without full documentation"
            }')
            ;;
        "documented")
            if [ -f "$ANALYTICS_FILE" ]; then
                local react_usage=$(jq -r '.technology_usage.frameworks.react // 0' "$ANALYTICS_FILE" 2>/dev/null)
                if [ "$react_usage" -gt 10 ]; then
                    suggestions+=('{
                        "id": "react-specialist",
                        "type": "optimization",
                        "priority": "medium",
                        "title": "Create React Specialist",
                        "description": "High React usage detected. Create specialist for better patterns",
                        "command": "/workflows:create-tech-agent \"React\" --research",
                        "context": "React used in 10+ files"
                    }')
                fi
            fi
            ;;
    esac
    
    # Activity-based suggestions
    case "$activity_pattern" in
        "testing")
            suggestions+=('{
                "id": "test-patterns",
                "type": "learning",
                "priority": "low",
                "title": "Learn Testing Patterns",
                "description": "Check out testing patterns for consistent test structure",
                "command": "Open patterns/testing-patterns.md",
                "context": "Recent testing activity detected"
            }')
            ;;
        "documentation")
            suggestions+=('{
                "id": "doc-generation",
                "type": "workflow",
                "priority": "medium",
                "title": "Generate API Documentation",
                "description": "Auto-generate API docs from your endpoints",
                "command": "/analysis:docgen --api",
                "context": "Documentation work in progress"
            }')
            ;;
        "frontend")
            suggestions+=('{
                "id": "component-patterns",
                "type": "learning",
                "priority": "low",
                "title": "Review Component Patterns",
                "description": "Check patterns for consistent component structure",
                "command": "Open patterns/api-patterns.md#react-patterns",
                "context": "Frontend development detected"
            }')
            ;;
    esac
    
    # File-specific suggestions
    if [[ "$FILE_PATH" == *"package.json" ]]; then
        suggestions+=('{
            "id": "dependency-check",
            "type": "optimization",
            "priority": "medium",
            "title": "Update Tech Stack Detection",
            "description": "Dependencies changed. Refresh tech detection",
            "command": "/workflows:project-init-tech --research",
            "context": "package.json modified"
        }')
    fi
    
    # Module detection
    if [[ "$FILE_PATH" == *"/modules/"* ]] || [[ "$FILE_PATH" == *"/services/"* ]]; then
        local module_name=$(echo "$FILE_PATH" | sed -n 's|.*/\(modules\|services\)/\([^/]*\)/.*|\2|p')
        if [ -n "$module_name" ]; then
            suggestions+=('{
                "id": "module-docs",
                "type": "workflow",
                "priority": "medium",
                "title": "Document Module: '"$module_name"'",
                "description": "Create comprehensive documentation for this module",
                "command": "/workflows:module-dev '"$module_name"'",
                "context": "Module development detected"
            }')
        fi
    fi
    
    # Update suggestions file
    if [ ${#suggestions[@]} -gt 0 ]; then
        local temp_file=$(mktemp)
        
        # Convert array to JSON array
        local suggestions_json="["
        for i in "${!suggestions[@]}"; do
            if [ $i -gt 0 ]; then
                suggestions_json+=","
            fi
            suggestions_json+="${suggestions[i]}"
        done
        suggestions_json+="]"
        
        jq --argjson new_suggestions "$suggestions_json" \
           --arg timestamp "$timestamp" \
           --arg state "$project_state" \
           --arg activity "$activity_pattern" \
           '
           .last_updated = $timestamp |
           .context.project_state = $state |
           .context.detected_patterns = [$activity] |
           .active_suggestions = ($new_suggestions + .active_suggestions | unique_by(.id) | .[0:5]) |
           .context.recent_activity += [{
             "timestamp": $timestamp,
             "state": $state,
             "activity": $activity
           }] |
           .context.recent_activity = (.context.recent_activity | .[0:20])
           ' "$SUGGESTIONS_FILE" > "$temp_file" && mv "$temp_file" "$SUGGESTIONS_FILE"
    fi
}

# === DISPLAY SUGGESTIONS ===
display_suggestions() {
    if [ ! -f "$SUGGESTIONS_FILE" ]; then
        return 0
    fi
    
    local high_priority=$(jq -r '.active_suggestions[] | select(.priority == "high") | .title' "$SUGGESTIONS_FILE" 2>/dev/null || echo "")
    
    if [ -n "$high_priority" ]; then
        echo ""
        echo "💡 Smart Suggestion:"
        echo "$high_priority"
        echo "   Run: $(jq -r '.active_suggestions[] | select(.priority == "high") | .command' "$SUGGESTIONS_FILE" | head -1)"
        echo ""
    fi
}

# === MAIN PROCESSING ===
main() {
    initialize_suggestions
    
    # Only process certain events
    case "$EVENT_TYPE" in
        "tool_call")
            if [ "$TOOL_NAME" = "Write" ] || [ "$TOOL_NAME" = "Edit" ]; then
                generate_suggestions
            fi
            ;;
        "command_complete")
            generate_suggestions
            # Display suggestions after major commands
            if [[ "$COMMAND_NAME" == *"init"* ]] || [[ "$COMMAND_NAME" == *"create"* ]]; then
                display_suggestions
            fi
            ;;
    esac
}

# === ERROR HANDLING ===
error_handler() {
    exit 0
}

trap error_handler ERR

# Run main function
main "$@"