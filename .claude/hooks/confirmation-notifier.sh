#!/bin/bash

# Confirmation Notifier Hook for <PERSON> Code
# Detects when <PERSON> might be waiting for confirmation and sends notifications

# Trap SIGPIPE to prevent errors
trap '' PIPE

# Read JSON input from stdin with error handling
if ! JSON_INPUT=$(cat 2>> /workflows:dev-command/null); then
    exit 0
fi

# Extract event type and tool name
EVENT_TYPE=$(echo "$JSON_INPUT" | jq -r '.event // empty' 2>> /workflows:dev-command/null)
TOOL_NAME=$(echo "$JSON_INPUT" | jq -r '.toolName // empty' 2>> /workflows:dev-command/null)

# Function to send macOS notification
send_notification() {
    local title="$1"
    local message="$2"
    local sound="${3:-default}"
    
    # Use osascript for macOS notifications
    osascript -e "display notification \"$message\" with title \"$title\" sound name \"$sound\"" 2>> /workflows:dev-command/null || true
}

# Function to check if <PERSON> might be waiting
check_for_waiting_state() {
    # Get project name for notifications
    local PROJECT_NAME=$(basename "$(pwd)")
    
    # Check if we're in a Stop event (might be waiting for user input)
    if [[ "$EVENT_TYPE" == "Stop" ]]; then
        send_notification "Claude Code - $PROJECT_NAME" "Session stopped - might be waiting for your input" "Glass"
        { echo "⏸️  Claude Code session stopped - check if it needs your input"; } 2>> /workflows:dev-command/null || true
    fi
    
    # For PostToolUse events, check if certain tools were used that might need follow-up
    if [[ "$EVENT_TYPE" == "PostToolUse" ]]; then
        case "$TOOL_NAME" in
            "Write"|"Edit"|"MultiEdit")
                # Extract file path to show in notification
                local FILE_PATH=$(echo "$JSON_INPUT" | jq -r '.params.file_path // empty' 2>> /workflows:dev-command/null)
                local FILE_NAME=$(basename "$FILE_PATH")
                { echo "✏️  Modified: $FILE_NAME"; } 2>> /workflows:dev-command/null || true
                ;;
            "Bash")
                # Check if bash command had errors
                local EXIT_CODE=$(echo "$JSON_INPUT" | jq -r '.result.exitCode // 0' 2>> /workflows:dev-command/null)
                if [[ "$EXIT_CODE" != "0" ]]; then
                    send_notification "Claude Code - $PROJECT_NAME" "Command failed - may need your attention" "Basso"
                    { echo "❌ Command failed with exit code $EXIT_CODE"; } 2>> /workflows:dev-command/null || true
                fi
                ;;
            "TodoWrite")
                { echo "📋 Todo list updated"; } 2>> /workflows:dev-command/null || true
                ;;
        esac
    fi
    
    # Special case: Check for confirmation prompts in Bash commands
    if [[ "$EVENT_TYPE" == "PreToolUse" ]] && [[ "$TOOL_NAME" == "Bash" ]]; then
        local COMMAND=$(echo "$JSON_INPUT" | jq -r '.params.command // empty' 2>> /workflows:dev-command/null)
        if [[ "$COMMAND" == *"read -p"* ]] || [[ "$COMMAND" == *"confirm"* ]]; then
            send_notification "Claude Code - $PROJECT_NAME" "Command may require confirmation" "Ping"
            { echo "⚠️  Command may require user confirmation"; } 2>> /workflows:dev-command/null || true
        fi
    fi
}

# Run confirmation check
check_for_waiting_state

# Pass through the JSON with error handling
{ echo "$JSON_INPUT"; } 2>> /workflows:dev-command/null || true

# Always exit 0 - this is informational only
exit 0