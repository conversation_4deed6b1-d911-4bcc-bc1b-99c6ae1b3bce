#!/bin/bash

# Selective Auto-Documentation Hook
# Automatically updates ONLY critical technical documentation (not business docs)

set -euo pipefail

# === CONFIGURATION ===
CLAUDE_DIR="$(dirname "$0")/.."
PROJECT_ROOT="$(dirname "$CLAUDE_DIR")"
CLAUDE_MD="$PROJECT_ROOT/CLAUDE.md"

# === DEPENDENCY CHECK ===
if ! command -v jq >/dev/null 2>&1; then
    exit 0
fi

# === READ EVENT DATA ===
JSON_INPUT=""
if [ -t 0 ]; then
    exit 0
else
    JSON_INPUT=$(cat 2>/dev/null || echo "{}")
fi

if [ -z "$JSON_INPUT" ] || [ "$JSON_INPUT" = "{}" ]; then
    exit 0
fi

# === EXTRACT EVENT DATA ===
EVENT_TYPE=$(echo "$JSON_INPUT" | jq -r '.event // empty' 2>/dev/null || echo "")
TOOL_NAME=$(echo "$JSON_INPUT" | jq -r '.toolName // empty' 2>/dev/null || echo "")
FILE_PATH=$(echo "$JSON_INPUT" | jq -r '.params.file_path // empty' 2>/dev/null || echo "")

# === AUTO-UPDATE TECH STACK IN CLAUDE.MD ===
update_tech_stack() {
    if [ ! -f "$CLAUDE_MD" ]; then
        return 0
    fi
    
    # Only update if package.json, requirements.txt, or similar changed
    local file_name=$(basename "$FILE_PATH")
    case "$file_name" in
        "package.json"|"requirements.txt"|"Gemfile"|"composer.json"|"Cargo.toml")
            echo "# Auto-updating tech stack in CLAUDE.md..."
            update_tech_stack_section
            ;;
    esac
}

update_tech_stack_section() {
    local detected_tech=""
    
    # Detect from package.json
    if [ -f "$PROJECT_ROOT/package.json" ]; then
        local frameworks=""
        if grep -q "react" "$PROJECT_ROOT/package.json"; then
            frameworks+="React, "
        fi
        if grep -q "vue" "$PROJECT_ROOT/package.json"; then
            frameworks+="Vue, "
        fi
        if grep -q "express" "$PROJECT_ROOT/package.json"; then
            frameworks+="Express.js, "
        fi
        if grep -q "next" "$PROJECT_ROOT/package.json"; then
            frameworks+="Next.js, "
        fi
        
        frameworks=${frameworks%, }  # Remove trailing comma
        if [ -n "$frameworks" ]; then
            detected_tech+="Frontend/Backend: $frameworks\n"
        fi
    fi
    
    # Detect from Python requirements
    if [ -f "$PROJECT_ROOT/requirements.txt" ]; then
        local python_frameworks=""
        if grep -q "django" "$PROJECT_ROOT/requirements.txt"; then
            python_frameworks+="Django, "
        fi
        if grep -q "fastapi" "$PROJECT_ROOT/requirements.txt"; then
            python_frameworks+="FastAPI, "
        fi
        if grep -q "flask" "$PROJECT_ROOT/requirements.txt"; then
            python_frameworks+="Flask, "
        fi
        
        python_frameworks=${python_frameworks%, }
        if [ -n "$python_frameworks" ]; then
            detected_tech+="Python: $python_frameworks\n"
        fi
    fi
    
    # Update CLAUDE.md if we detected anything
    if [ -n "$detected_tech" ]; then
        # Create backup only if AUTO_DOC_NO_BACKUP is not set
        if [ "${AUTO_DOC_NO_BACKUP:-}" != "true" ]; then
            cp "$CLAUDE_MD" "$CLAUDE_MD.backup"
        fi
        
        # Update tech stack section
        local temp_file=$(mktemp)
        local in_tech_section=false
        local tech_section_updated=false
        
        while IFS= read -r line; do
            if [[ "$line" == "## Detected Stack"* ]]; then
                echo "$line" >> "$temp_file"
                echo -e "$detected_tech" >> "$temp_file"
                in_tech_section=true
                tech_section_updated=true
            elif [[ "$line" == "## "* ]] && [ "$in_tech_section" = true ]; then
                echo "$line" >> "$temp_file"
                in_tech_section=false
            elif [ "$in_tech_section" = false ]; then
                echo "$line" >> "$temp_file"
            fi
        done < "$CLAUDE_MD"
        
        # If no tech section exists, add one
        if [ "$tech_section_updated" = false ]; then
            echo "" >> "$temp_file"
            echo "## Detected Stack" >> "$temp_file"
            echo -e "$detected_tech" >> "$temp_file"
        fi
        
        mv "$temp_file" "$CLAUDE_MD"
        echo "✓ Tech stack updated in CLAUDE.md"
    fi
}

# === AUTO-UPDATE MODULE LIST ===
update_module_list() {
    local module_dirs=(
        "$PROJECT_ROOT/modules"
        "$PROJECT_ROOT/src/modules"
        "$PROJECT_ROOT/services"
        "$PROJECT_ROOT/src/services"
        "$PROJECT_ROOT/domains"
        "$PROJECT_ROOT/src/domains"
    )
    
    local detected_modules=""
    
    for module_dir in "${module_dirs[@]}"; do
        if [ -d "$module_dir" ]; then
            for module in "$module_dir"/*; do
                if [ -d "$module" ]; then
                    local module_name=$(basename "$module")
                    detected_modules+="- $module_name ($(basename "$module_dir")/$module_name)\n"
                fi
            done
        fi
    done
    
    if [ -n "$detected_modules" ] && [ -f "$CLAUDE_MD" ]; then
        # Check if module section needs update
        if ! grep -q "## Module Structure" "$CLAUDE_MD"; then
            # Add module section
            local temp_file=$(mktemp)
            cp "$CLAUDE_MD" "$temp_file"
            
            echo "" >> "$temp_file"
            echo "## Module Structure" >> "$temp_file"
            echo "### Detected Modules" >> "$temp_file"
            echo -e "$detected_modules" >> "$temp_file"
            
            mv "$temp_file" "$CLAUDE_MD"
            echo "✓ Module structure added to CLAUDE.md"
        fi
    fi
}

# === AUTO-UPDATE AGENT ROUTING ===
update_agent_routing() {
    # Only update routing when new specialists are created
    local specialists_dir="$CLAUDE_DIR/agents/tech-specialists"
    
    if [ -d "$specialists_dir" ]; then
        local routing_rules=""
        
        # Find all specialists
        for category in "$specialists_dir"/*; do
            if [ -d "$category" ]; then
                local category_name=$(basename "$category")
                for specialist in "$category"/*.md; do
                    if [ -f "$specialist" ]; then
                        local specialist_name=$(basename "$specialist" .md)
                        local tech_name=${specialist_name%-specialist}
                        
                        case "$category_name" in
                            "frontend")
                                routing_rules+="- /src/components/* → @$specialist_name\n"
                                routing_rules+="- *.tsx, *.jsx → @$specialist_name\n"
                                ;;
                            "backend")
                                routing_rules+="- /src/api/* → @$specialist_name\n"
                                routing_rules+="- /routes/* → @$specialist_name\n"
                                ;;
                            "database")
                                routing_rules+="- /src/db/* → @$specialist_name\n"
                                routing_rules+="- *.sql → @$specialist_name\n"
                                ;;
                        esac
                    fi
                done
            fi
        done
        
        # Update CLAUDE.md routing section if we have rules
        if [ -n "$routing_rules" ] && [ -f "$CLAUDE_MD" ]; then
            if ! grep -q "## Agent Assignments" "$CLAUDE_MD"; then
                local temp_file=$(mktemp)
                cp "$CLAUDE_MD" "$temp_file"
                
                echo "" >> "$temp_file"
                echo "## Agent Assignments" >> "$temp_file"
                echo "### Routing Rules" >> "$temp_file"
                echo -e "$routing_rules" >> "$temp_file"
                
                mv "$temp_file" "$CLAUDE_MD"
                echo "✓ Agent routing added to CLAUDE.md"
            fi
        fi
    fi
}

# === AUTO-UPDATE USAGE PATTERNS ===
update_usage_patterns() {
    local analytics_file="$CLAUDE_DIR/usage-analytics.json"
    
    if [ -f "$analytics_file" ] && [ -f "$CLAUDE_MD" ]; then
        # Extract high-usage technologies
        local high_usage=""
        local medium_usage=""
        local low_usage=""
        
        if command -v jq >/dev/null 2>&1; then
            # Technologies with >10 uses = high
            high_usage=$(jq -r '.technology_usage.frameworks | to_entries[] | select(.value > 10) | "- " + .key + " (" + (.value | tostring) + " files)"' "$analytics_file" 2>/dev/null | tr '\n' ' ')
            
            # Technologies with 5-10 uses = medium
            medium_usage=$(jq -r '.technology_usage.frameworks | to_entries[] | select(.value >= 5 and .value <= 10) | "- " + .key + " (" + (.value | tostring) + " files)"' "$analytics_file" 2>/dev/null | tr '\n' ' ')
            
            # Technologies with <5 uses = low
            low_usage=$(jq -r '.technology_usage.frameworks | to_entries[] | select(.value < 5) | "- " + .key + " (" + (.value | tostring) + " files)"' "$analytics_file" 2>/dev/null | tr '\n' ' ')
        fi
        
        # Update usage section if we have data
        if [ -n "$high_usage" ] || [ -n "$medium_usage" ] || [ -n "$low_usage" ]; then
            if ! grep -q "## Technology Usage Patterns" "$CLAUDE_MD"; then
                local temp_file=$(mktemp)
                cp "$CLAUDE_MD" "$temp_file"
                
                echo "" >> "$temp_file"
                echo "## Technology Usage Patterns" >> "$temp_file"
                
                if [ -n "$high_usage" ]; then
                    echo "### High Frequency (Create Specialists)" >> "$temp_file"
                    echo "$high_usage" | tr ' ' '\n' >> "$temp_file"
                fi
                
                if [ -n "$medium_usage" ]; then
                    echo "### Medium Frequency (Consider Specialists)" >> "$temp_file"
                    echo "$medium_usage" | tr ' ' '\n' >> "$temp_file"
                fi
                
                if [ -n "$low_usage" ]; then
                    echo "### Low Frequency (Documentation Only)" >> "$temp_file"
                    echo "$low_usage" | tr ' ' '\n' >> "$temp_file"
                fi
                
                mv "$temp_file" "$CLAUDE_MD"
                echo "✓ Usage patterns updated in CLAUDE.md"
            fi
        fi
    fi
}

# === MAIN PROCESSING ===
main() {
    # Only process specific file change events
    if [ "$EVENT_TYPE" = "tool_call" ] && [ "$TOOL_NAME" = "Write" ]; then
        
        case "$FILE_PATH" in
            */package.json|*/requirements.txt|*/Gemfile|*/composer.json|*/Cargo.toml)
                update_tech_stack
                ;;
            */modules/*|*/services/*|*/domains/*)
                update_module_list
                ;;
            */.claude/agents/tech-specialists/*)
                update_agent_routing
                ;;
            */.claude/usage-analytics.json)
                update_usage_patterns
                ;;
        esac
    fi
}

# === ERROR HANDLING ===
error_handler() {
    # Restore backup if it exists and backups are enabled
    if [ "${AUTO_DOC_NO_BACKUP:-}" != "true" ] && [ -f "$CLAUDE_MD.backup" ]; then
        mv "$CLAUDE_MD.backup" "$CLAUDE_MD"
    fi
    exit 0
}

trap error_handler ERR

# Run main function
main "$@"