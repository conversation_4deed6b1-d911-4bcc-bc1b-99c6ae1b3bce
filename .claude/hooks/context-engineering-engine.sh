#!/bin/bash

# Dynamic Context Engineering Engine
# Provides the right information and tools, in the right format, at the right time

set -euo pipefail

# Configuration
# Find project root and .claude directory
find_project_root() {
    local dir="$(pwd)"
    while [ "$dir" != "/" ]; do
        if [ -d "$dir/.claude" ]; then
            echo "$dir"
            return 0
        fi
        dir=$(dirname "$dir")
    done
    return 1
}

PROJECT_ROOT=$(find_project_root)
if [ -z "$PROJECT_ROOT" ]; then
    echo "ERROR: Could not find project root with .claude directory" >&2
    exit 1
fi

# Worktree-aware state directory detection
get_worktree_state_dir() {
    if [[ -f .git ]] && grep -q "gitdir:" .git 2>> /workflows:dev-command/null; then
        # This is a worktree
        local worktree_name=$(basename "$(pwd)")
        if [[ "$worktree_name" == pib-* ]]; then
            local feature_name="${worktree_name#pib-}"
            echo "$PROJECT_ROOT/.claude/state/worktrees/$feature_name"
        else
            echo "$PROJECT_ROOT/.claude/state/main"
        fi
    else
        # This is the main repository
        echo "$PROJECT_ROOT/.claude/state/main"
    fi
}

# Use worktree-aware state directory
STATE_BASE_DIR=$(get_worktree_state_dir)
CONTEXT_ENGINE_DIR="$STATE_BASE_DIR/context-engine"
CONTEXT_CACHE_DIR="$CONTEXT_ENGINE_DIR/cache"
RELEVANCE_SCORES_FILE="$CONTEXT_ENGINE_DIR/relevance-scores.json"
CONTEXT_PATTERNS_FILE="$CONTEXT_ENGINE_DIR/successful-patterns.json"
TOOL_USAGE_STATS="$CONTEXT_ENGINE_DIR/tool-usage-stats.json"

# Ensure directories exist
mkdir -p "$CONTEXT_ENGINE_DIR" "$CONTEXT_CACHE_DIR"

# Initialize data files if they don't exist
initialize_context_data() {
    if [[ ! -f "$RELEVANCE_SCORES_FILE" ]]; then
        cat > "$RELEVANCE_SCORES_FILE" << 'EOF'
{
  "task_patterns": {},
  "context_weights": {
    "immediate": 1.0,
    "session": 0.8,
    "project": 0.6,
    "domain": 0.4
  },
  "last_updated": null
}
EOF
    fi

    if [[ ! -f "$CONTEXT_PATTERNS_FILE" ]]; then
        cat > "$CONTEXT_PATTERNS_FILE" << 'EOF'
{
  "successful_contexts": [],
  "failed_contexts": [],
  "optimization_patterns": {},
  "last_updated": null
}
EOF
    fi

    if [[ ! -f "$TOOL_USAGE_STATS" ]]; then
        cat > "$TOOL_USAGE_STATS" << 'EOF'
{
  "tool_effectiveness": {},
  "context_tool_mappings": {},
  "usage_frequency": {},
  "success_rates": {},
  "last_updated": null
}
EOF
    fi
}

# Context relevance scoring algorithm
calculate_context_relevance() {
    local task_type="$1"
    local context_item="$2"
    local user_input="$3"
    
    local relevance_score=0.0
    
    # Keyword matching (basic implementation)
    local keywords
    keywords=$(echo "$user_input" | tr '[:upper:]' '[:lower:]' | grep -oE '\b\w{3,}\b' | head -10)
    
    for keyword in $keywords; do
        if echo "$context_item" | grep -qi "$keyword"; then
            relevance_score=$(echo "$relevance_score + 0.1" | bc -l)
        fi
    done
    
    # Task type matching
    case "$task_type" in
        "dev"|"implement"|"code"|"fix")
            if echo "$context_item" | grep -qi "code\|implementation\|function\|class\|method"; then
                relevance_score=$(echo "$relevance_score + 0.3" | bc -l)
            fi
            ;;
        "review"|"analyze"|"check")
            if echo "$context_item" | grep -qi "review\|quality\|standard\|guideline"; then
                relevance_score=$(echo "$relevance_score + 0.3" | bc -l)
            fi
            ;;
        "architecture"|"design"|"plan")
            if echo "$context_item" | grep -qi "architecture\|design\|pattern\|structure"; then
                relevance_score=$(echo "$relevance_score + 0.3" | bc -l)
            fi
            ;;
    esac
    
    # Recency boost
    if [[ -f "$context_item" ]]; then
        local file_age
        file_age=$(stat -c %Y "$context_item" 2>> /workflows:dev-command/null || echo "0")
        local current_time
        current_time=$(date +%s)
        local age_hours=$(( (current_time - file_age) / 3600 ))
        
        if [[ $age_hours -lt 24 ]]; then
            relevance_score=$(echo "$relevance_score + 0.2" | bc -l)
        elif [[ $age_hours -lt 168 ]]; then  # 1 week
            relevance_score=$(echo "$relevance_score + 0.1" | bc -l)
        fi
    fi
    
    echo "$relevance_score"
}

# Extract relevant context based on task and user input
extract_relevant_context() {
    local task_type="$1"
    local user_input="$2"
    local score_threshold="${3:-0.3}"
    
    local relevant_context=""
    local context_sources=(
        ".aiinit -> /workflows:project-init-context.md"
        "docsdesign -> /agents:architect-designure.md"
        "docs/prd.md"
        "CLAUDE.md"
        "README.md"
    )
    
    # Add dynamic context sources based on task type
    case "$task_type" in
        "dev"|"implement")
            context_sources+=("pib-agent/personas> /workflows:dev-command.ide.md")
            context_sources+=("docs/tech-stack.md")
            ;;
        "review"|"analyze")
            context_sources+=("pib-agent/personas/code-reviewer.md")
            context_sources+=("pib-agentmode -> /quick:check-modelists/")
            ;;
        "architecture"|"design")
            context_sources+=("pib-agent/personasdesign -> /agents:architect-design.md")
            context_sources+=("docs/frontend-architecture.md")
            ;;
    esac
    
    # Add planning context sources
    add_planning_context_sources context_sources "$user_input"
    
    # Score and filter context
    for source in "${context_sources[@]}"; do
        if [[ -f "$source" ]]; then
            local score
            score=$(calculate_context_relevance "$task_type" "$source" "$user_input")
            
            if (( $(echo "$score >= $score_threshold" | bc -l) )); then
                echo "# Relevant Context: $source (Score: $score)"
                echo ""
                head -100 "$source" 2>> /workflows:dev-command/null || echo "Could not read $source"
                echo ""
                echo "---"
                echo ""
            fi
        elif [[ -d "$source" ]]; then
            # Handle directories (like checklists/)
            for file in "$source"*.md; do
                if [[ -f "$file" ]]; then
                    local score
                    score=$(calculate_context_relevance "$task_type" "$file" "$user_input")
                    
                    if (( $(echo "$score >= $score_threshold" | bc -l) )); then
                        echo "# Relevant Context: $file (Score: $score)"
                        echo ""
                        head -50 "$file" 2>> /workflows:dev-command/null || echo "Could not read $file"
                        echo ""
                        echo "---"
                        echo ""
                    fi
                fi
            done
        fi
    done
}

# Just-in-time tool selection based on context
select_optimal_tools() {
    local task_complexity="$1"
    local domain="$2"
    local context_needs="$3"
    
    local selected_tools=()
    
    # Base tools for all tasks
    selected_tools+=("basic")
    
    # Complexity-based tool selection
    case "$task_complexity" in
        "simple")
            # No additional tools needed
            ;;
        "medium")
            selected_tools+=("zen-chat")
            if [[ "$domain" == "code" ]]; then
                selected_tools+=("zen-analyze")
            fi
            ;;
        "complex")
            selected_tools+=("zen-chat" "zen-analyze" "zen-thinkdeep")
            if [[ "$context_needs" == "research" ]]; then
                selected_tools+=("context7" "perplexity")
            fi
            ;;
        "expert")
            selected_tools+=("zen-chat" "zen-analyze" "zen-thinkdeep" "zen-consensus")
            selected_tools+=("context7" "perplexity" "firecrawl")
            if [[ "$domain" == "ui" ]]; then
                selected_tools+=("playwright")
            fi
            ;;
    esac
    
    # Domain-specific tool additions
    case "$domain" in
        "security")
            selected_tools+=("zen-secaudit")
            ;;
        "testing")
            selected_tools+=("zen-testgen" "playwright")
            ;;
        "documentation")
            selected_tools+=("zen-docgen" "context7")
            ;;
        "research")
            selected_tools+=("perplexity" "firecrawl" "context7")
            ;;
    esac
    
    # Remove duplicates and return
    printf '%s\n' "${selected_tools[@]}" | sort -u
}

# Context evolution during workflow execution
update_context_during_workflow() {
    local workflow_stage="$1"
    local new_findings="$2"
    local agent_context_file="$3"
    
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # Append new findings to context
    cat >> "$agent_context_file" << EOF

## Updated Context - $workflow_stage ($timestamp)

$new_findings

---

EOF
    
    # Update relevance scores based on findings
    update_relevance_scores "$workflow_stage" "$new_findings"
    
    # Prune irrelevant context if file is getting too large
    if [[ $(wc -l < "$agent_context_file") -gt 500 ]]; then
        prune_irrelevant_context "$agent_context_file"
    fi
}

# Update relevance scores based on successful patterns
update_relevance_scores() {
    local stage="$1"
    local findings="$2"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # Update the relevance scores file
    jq --arg stage "$stage" --arg findings "$findings" --arg ts "$timestamp" '
        .task_patterns[$stage] = {
            "recent_findings": $findings,
            "updated": $ts
        } |
        .last_updated = $ts
    ' "$RELEVANCE_SCORES_FILE" > "$RELEVANCE_SCORES_FILE.tmp" && mv "$RELEVANCE_SCORES_FILE.tmp" "$RELEVANCE_SCORES_FILE"
}

# Prune irrelevant context to keep files manageable
prune_irrelevant_context() {
    local context_file="$1"
    local temp_file="${context_file}.pruned"
    
    # Keep the first 50 lines (header/important info) and last 200 lines (recent updates)
    {
        head -50 "$context_file"
        echo ""
        echo "## [Context pruned for relevance - $(date)]"
        echo ""
        tail -200 "$context_file"
    } > "$temp_file"
    
    mv "$temp_file" "$context_file"
}

# Add planning context sources based on user input and active planning sessions
add_planning_context_sources() {
    local context_sources_var_name="$1"
    local user_input="$2"
    
    # Check if planning system exists
    local plans_dir="$PROJECT_ROOT/.claudesession -> session -> /workflows:planning-session:plan-sessions"
    if [[ ! -d "$plans_dir" ]]; then
        return 0
    fi
    
    # Add planning index if it exists
    local planning_index="$plans_dirsession -> session -> /workflows:planning-session:plan-sessionning-index.json"
    if [[ -f "$planning_index" ]]; then
        context_sources_ref+=("$planning_index")
    fi
    
    # Look for relevant active planning sessions
    local active_plans_dir="$plans_dir/active"
    if [[ -d "$active_plans_dir" ]]; then
        # Extract keywords from user input for matching
        local keywords
        keywords=$(echo "$user_input" | tr '[:upper:]' '[:lower:]' | grep -oE '\b\w{3,}\b' | head -5)
        
        # Look for planning sessions that match user input keywords
        for planning_file in "$active_plans_dir"/*.md; do
            if [[ -f "$planning_file" ]]; then
                local filename=$(basename "$planning_file")
                local match_found=false
                
                # Check if any keyword matches the planning file name or content
                for keyword in $keywords; do
                    if echo "$filename" | grep -qi "$keyword" || head -20 "$planning_file" | grep -qi "$keyword"; then
                        eval "${context_sources_var_name}+=(\"$planning_file\")"
                        match_found=true
                        break
                    fi
                done
                
                # Always include context bridge files as they contain cross-session context
                if echo "$filename" | grep -q "context-bridge"; then
                    context_sources_ref+=("$planning_file")
                fi
            fi
        done
    fi
    
    # Look for recently completed planning sessions that might be relevant
    local completed_plans_dir="$plans_dir/completed"
    if [[ -d "$completed_plans_dir" ]]; then
        # Find recently completed plans (within last 30 days)
        find "$completed_plans_dir" -name "*.md" -mtime -30 -type f | head -3 | while read -r completed_plan; do
            if [[ -f "$completed_plan" ]]; then
                eval "${context_sources_var_name}+=(\"$completed_plan\")"
            fi
        done
    fi
}

# Add planning context to agent context package
add_planning_context_to_package() {
    local context_package_file="$1"
    local user_input="$2"
    
    # Check if planning system exists
    local plans_dir="$PROJECT_ROOT/.claudesession -> session -> /workflows:planning-session:plan-sessions"
    if [[ ! -d "$plans_dir" ]]; then
        return 0
    fi
    
    # Look for relevant planning context
    local active_plans_dir="$plans_dir/active"
    local has_planning_context=false
    
    if [[ -d "$active_plans_dir" ]]; then
        # Extract keywords from user input for matching
        local keywords
        keywords=$(echo "$user_input" | tr '[:upper:]' '[:lower:]' | grep -oE '\b\w{3,}\b' | head -5)
        
        # Find relevant planning files
        local relevant_plans=()
        for planning_file in "$active_plans_dir"/*.md; do
            if [[ -f "$planning_file" ]]; then
                local filename=$(basename "$planning_file")
                
                # Check if any keyword matches the planning file name or content
                for keyword in $keywords; do
                    if echo "$filename" | grep -qi "$keyword" || head -20 "$planning_file" | grep -qi "$keyword"; then
                        relevant_plans+=("$planning_file")
                        has_planning_context=true
                        break
                    fi
                done
                
                # Always include context bridge files
                if echo "$filename" | grep -q "context-bridge"; then
                    relevant_plans+=("$planning_file")
                    has_planning_context=true
                fi
            fi
        done
        
        # Add planning context section if we found relevant planning
        if [[ "$has_planning_context" == true ]]; then
            cat >> "$context_package_file" << EOF

## Planning Context (Cross-Session Continuity)

**Epic Planning Context Available**: Persistent planning documentation found for this task.

EOF
            
            # Add summaries of relevant planning files
            for plan_file in "${relevant_plans[@]}"; do
                local plan_name=$(basename "$plan_file" .md)
                cat >> "$context_package_file" << EOF
### $plan_name
$(head -30 "$plan_file" | tail -20)

EOF
            done
            
            cat >> "$context_package_file" << EOF
**Key Planning Integration Points**:
- Review planning decisions before implementation
- Follow established architecture patterns from planning
- Apply LEVER framework as defined in planning context
- Coordinate with sub-agent assignments from planning
- Maintain context bridge for cross-session continuity

**Planning Files Reference**:
$(printf '- %s\n' "${relevant_plans[@]}")

EOF
        fi
    fi
}

# Learn from successful context patterns
record_successful_pattern() {
    local task_type="$1"
    local context_used="$2"
    local tools_used="$3"
    local outcome="$4"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    jq --arg type "$task_type" --arg context "$context_used" --arg tools "$tools_used" --arg outcome "$outcome" --arg ts "$timestamp" '
        .successful_contexts += [{
            "task_type": $type,
            "context_used": $context,
            "tools_used": $tools,
            "outcome": $outcome,
            "timestamp": $ts
        }] |
        .last_updated = $ts
    ' "$CONTEXT_PATTERNS_FILE" > "$CONTEXT_PATTERNS_FILE.tmp" && mv "$CONTEXT_PATTERNS_FILE.tmp" "$CONTEXT_PATTERNS_FILE"
}

# Generate context package for agent
create_agent_context_package() {
    local agent_type="$1"
    local task_type="$2"
    local user_input="$3"
    local complexity="${4:-medium}"
    local sub_agent_focus="${5:-general}"
    
    local context_package_file="$CONTEXT_CACHE_DIR/${agent_type}-${task_type}-${sub_agent_focus}-context.md"
    
    cat > "$context_package_file" << EOF
# Dynamic Context Package for $agent_type
Generated: $(date)
Task Type: $task_type
Complexity: $complexity

## Relevant Context
EOF
    
    # Add filtered, relevant context
    extract_relevant_context "$task_type" "$user_input" >> "$context_package_file"
    
    # Add planning context if available
    add_planning_context_to_package "$context_package_file" "$user_input"
    
    cat >> "$context_package_file" << EOF

## Recommended Tools
EOF
    
    # Add tool recommendations
    local domain
    case "$agent_type" in
        "dev"|"james") domain="code" ;;
        "reviewer") domain="review" ;;
        "architect") domain="architecture" ;;
        "analyst") domain="research" ;;
        *) domain="general" ;;
    esac
    
    select_optimal_tools "$complexity" "$domain" "standard" >> "$context_package_file"
    
    echo "$context_package_file"
}

# Create focused sub-agent context package with domain-specific filtering
create_subagent_context_package() {
    local sub_agent_type="$1"  # frontend, backend, devops, testing
    local task_list="$2"       # tasks assigned to this sub-agent
    local user_input="$3"
    local planning_context="$4" # optional planning context reference
    local complexity="${5:-medium}"
    
    local context_package_file="$CONTEXT_CACHE_DIRagent-coordination -> /workflows:sub-agent-coordinationagent-${sub_agent_type}-context.md"
    
    cat > "$context_package_file" << EOF
# Sub-Agent Context Package: $(echo "$sub_agent_type" | awk '{print toupper(substr($0,1,1))tolower(substr($0,2))}')
Generated: $(date)
Sub-Agent Type: $sub_agent_type
Complexity: $complexity
Tasks: $task_list

## Domain-Specific Context
EOF
    
    # Add domain-specific context sources
    case "$sub_agent_type" in
        "frontend")
            add_frontend_context "$context_package_file" "$user_input"
            ;;
        "backend") 
            add_backend_context "$context_package_file" "$user_input"
            ;;
        "devops")
            add_devops_context "$context_package_file" "$user_input"
            ;;
        "testing")
            add_testing_context "$context_package_file" "$user_input"
            ;;
    esac
    
    # Add planning context if provided
    if [[ -n "$planning_context" && -f "$planning_context" ]]; then
        cat >> "$context_package_file" << EOF

## Planning Context (Domain-Filtered)
EOF
        # Extract domain-relevant sections from planning context
        extract_domain_planning_context "$planning_context" "$sub_agent_type" >> "$context_package_file"
    fi
    
    # Add coordination context
    add_coordination_context "$context_package_file" "$sub_agent_type" "$user_input"
    
    # Add domain-specific tools
    cat >> "$context_package_file" << EOF

## Recommended Tools (Domain-Optimized)
EOF
    select_subagent_tools "$sub_agent_type" "$complexity" >> "$context_package_file"
    
    echo "$context_package_file"
}

# Add frontend-specific context
add_frontend_context() {
    local context_file="$1"
    local user_input="$2"
    
    cat >> "$context_file" << EOF

### Frontend Architecture Context
$(extract_relevant_context "ui" "$user_input" "0.4" | head -50)

### Component Library & Design System
$(find_and_extract_content "component" "design" "ui" | head -30)

### Frontend Performance Standards
- Core Web Vitals compliance
- Accessibility (WCAG 2.1 AA)
- Responsive design requirements
- Browser compatibility standards

EOF
}

# Add backend-specific context
add_backend_context() {
    local context_file="$1"
    local user_input="$2"
    
    cat >> "$context_file" << EOF

### Backend Architecture Context
$(extract_relevant_context "api" "$user_input" "0.4" | head -50)

### Database & Data Models
$(find_and_extract_content "database" "api" "data" | head -30)

### Backend Performance Standards
- API response time requirements
- Database query optimization
- Security standards (authentication, authorization)
- Error handling and logging

EOF
}

# Add devops-specific context
add_devops_context() {
    local context_file="$1"
    local user_input="$2"
    
    cat >> "$context_file" << EOF

### Infrastructure Architecture Context
$(extract_relevant_context "infrastructure" "$user_input" "0.4" | head -50)

### Deployment & Configuration
$(find_and_extract_content "deployment" "infrastructure" "config" | head -30)

### DevOps Standards
- Infrastructure as code requirements
- CI/CD pipeline standards
- Monitoring and alerting requirements
- Security and compliance standards

EOF
}

# Add testing-specific context
add_testing_context() {
    local context_file="$1"
    local user_input="$2"
    
    cat >> "$context_file" << EOF

### Testing Architecture Context
$(extract_relevant_context "testing" "$user_input" "0.4" | head -50)

### Test Strategy & Coverage
$(find_and_extract_content "test" "quality" "coverage" | head -30)

### Testing Standards
- Test coverage requirements (>90%)
- Testing pyramid strategy
- Performance testing requirements
- Security testing standards

EOF
}

# Extract domain-relevant planning context
extract_domain_planning_context() {
    local planning_file="$1"
    local domain="$2"
    
    # Extract sections relevant to the domain
    case "$domain" in
        "frontend")
            grep -A 10 -B 2 -i "ui\|frontend\|component\|design" "$planning_file" | head -20
            ;;
        "backend")
            grep -A 10 -B 2 -i "api\|backend\|database\|server" "$planning_file" | head -20
            ;;
        "devops")
            grep -A 10 -B 2 -i "infrastructure\|deployment\|devops\|config" "$planning_file" | head -20
            ;;
        "testing")
            grep -A 10 -B 2 -i "test\|quality\|validation\|qa" "$planning_file" | head -20
            ;;
    esac
}

# Add coordination context for sub-agent
add_coordination_context() {
    local context_file="$1"
    local sub_agent_type="$2"
    local user_input="$3"
    
    cat >> "$context_file" << EOF

## Inter-Sub-Agent Coordination

### Dependencies
EOF
    
    case "$sub_agent_type" in
        "frontend")
            echo "- Backend API endpoints and data contracts" >> "$context_file"
            echo "- DevOps environment configuration" >> "$context_file"
            echo "- Testing framework and validation requirements" >> "$context_file"
            ;;
        "backend")
            echo "- Frontend API requirements and data needs" >> "$context_file"
            echo "- DevOps database and infrastructure setup" >> "$context_file"
            echo "- Testing API validation and performance requirements" >> "$context_file"
            ;;
        "devops")
            echo "- Frontend deployment and environment requirements" >> "$context_file"
            echo "- Backend infrastructure and scaling needs" >> "$context_file"
            echo "- Testing infrastructure and automation requirements" >> "$context_file"
            ;;
        "testing")
            echo "- Frontend component and integration testing" >> "$context_file"
            echo "- Backend API and performance testing" >> "$context_file"
            echo "- DevOps deployment and infrastructure testing" >> "$context_file"
            ;;
    esac
    
    cat >> "$context_file" << EOF

### Communication Protocols
- Report completion to Bill (PM) for orchestration updates
- Coordinate with dependent sub-agents before proceeding
- Escalate blockers immediately to appropriate resolver
- Maintain context bridge updates for cross-session continuity

EOF
}

# Select domain-optimized tools for sub-agent
select_subagent_tools() {
    local sub_agent_type="$1"
    local complexity="$2"
    
    # Base tools for all sub-agents
    local tools=("zen-chat" "context7")
    
    # Add domain-specific tools
    case "$sub_agent_type" in
        "frontend")
            tools+=("playwright" "zen-analyze")
            ;;
        "backend")
            tools+=("zen-analyze" "zen-debug" "zen-secaudit")
            ;;
        "devops")
            tools+=("zen-analyze" "zen-secaudit")
            ;;
        "testing")
            tools+=("playwright" "zen-testgen" "zen-analyze")
            ;;
    esac
    
    # Add complexity-based tools
    if [[ "$complexity" == "complex" || "$complexity" == "expert" ]]; then
        tools+=("zen-thinkdeep" "zen-consensus")
    fi
    
    printf '%s\n' "${tools[@]}"
}

# Helper function to find and extract content
find_and_extract_content() {
    local keywords="$*"
    local content=""
    
    # Search common locations for relevant content
    local search_paths=(
        "docs/"
        "pib-agent/"
        "README.md"
        "CLAUDE.md"
    )
    
    for path in "${search_paths[@]}"; do
        if [[ -e "$path" ]]; then
            for keyword in $keywords; do
                local matches
                matches=$(find "$path" -type f -name "*.md" -exec grep -l -i "$keyword" {} \; 2>> /workflows:dev-command/null | head -3)
                for match in $matches; do
                    content+="$(head -10 "$match" 2>> /workflows:dev-command/null)"$'\n'
                done
            done
        fi
    done
    
    echo "$content" | head -20
}

# Main context engineering function
main() {
    local action="${1:-help}"
    
    initialize_context_data
    
    case "$action" in
        "extract_context")
            if [[ $# -ge 3 ]]; then
                extract_relevant_context "$2" "$3" "${4:-0.3}"
            else
                echo "Usage: $0 extract_context <task_type> <user_input> [threshold]"
                exit 1
            fi
            ;;
        "select_tools")
            if [[ $# -ge 4 ]]; then
                select_optimal_tools "$2" "$3" "$4"
            else
                echo "Usage: $0 select_tools <complexity> <domain> <context_needs>"
                exit 1
            fi
            ;;
        "update_context")
            if [[ $# -ge 4 ]]; then
                update_context_during_workflow "$2" "$3" "$4"
            else
                echo "Usage: $0 update_context <stage> <findings> <context_file>"
                exit 1
            fi
            ;;
        "create_package")
            if [[ $# -ge 4 ]]; then
                create_agent_context_package "$2" "$3" "$4" "${5:-medium}"
            else
                echo "Usage: $0 create_package <agent_type> <task_type> <user_input> [complexity]"
                exit 1
            fi
            ;;
        "record_success")
            if [[ $# -ge 5 ]]; then
                record_successful_pattern "$2" "$3" "$4" "$5"
            else
                echo "Usage: $0 record_success <task_type> <context> <tools> <outcome>"
                exit 1
            fi
            ;;
        "prune")
            if [[ $# -ge 2 ]]; then
                prune_irrelevant_context "$2"
            else
                echo "Usage: $0 prune <context_file>"
                exit 1
            fi
            ;;
        "help"|*)
            cat << EOF
Dynamic Context Engineering Engine

Usage: $0 <action> [arguments...]

Actions:
  extract_context <task_type> <user_input> [threshold]
  select_tools <complexity> <domain> <context_needs>
  update_context <stage> <findings> <context_file>
  create_package <agent_type> <task_type> <user_input> [complexity]
  record_success <task_type> <context> <tools> <outcome>
  prune <context_file>
  help

Examples:
  $0 extract_context "dev" "implement user login" 0.4
  $0 select_tools "complex" "code" "research"
  $0 create_package "james" "dev" "add authentication" "medium"
EOF
            ;;
    esac
}

# Execute main function if script is run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi