#!/bin/bash

# Universal Hook Runner v2 - Enhanced error handling and debugging
# Works in ANY project, ANY directory with better error reporting

# Set error handling
set -o pipefail
trap '' PIPE

# Function to find .claude directory from current location
find_project_claude_dir() {
    local search_dir="$PWD"
    
    # Walk up directory tree until we find .claude or reach root
    while [ "$search_dir" != "/" ]; do
        if [ -d "$search_dir/.claude" ]; then
            echo "$search_dir/.claude"
            return 0
        fi
        search_dir=$(dirname "$search_dir")
    done
    
    # Not found
    return 1
}

# Get the hook name from command line argument
HOOK_NAME="$1"
if [ -z "$HOOK_NAME" ]; then
    echo "Error: Hook name required" >&2
    exit 2
fi
shift  # Remove hook name from arguments

# Debug: Show current directory
# echo "Debug: Running from $(pwd)" >&2

# Find the .claude directory for THIS project
CLAUDE_DIR=$(find_project_claude_dir)
if [ -z "$CLAUDE_DIR" ]; then
    echo "Error: Cannot find .claude directory from $(pwd)" >&2
    echo "Debug: Searched from $PWD up to root" >&2
    exit 2
fi

# Debug: Show found directory
# echo "Debug: Found .claude at $CLAUDE_DIR" >&2

# Construct hook path
HOOK_SCRIPT="$CLAUDE_DIR/hooks/$HOOK_NAME"

# Verify hook exists and is executable
if [ ! -f "$HOOK_SCRIPT" ]; then
    echo "Error: Hook not found: $HOOK_SCRIPT" >&2
    # List available hooks for debugging
    echo "Available hooks in $CLAUDE_DIR/hooks/:" >&2
    ls -la "$CLAUDE_DIR/hooks/" 2>&1 | head -5 >&2
    exit 2
fi

if [ ! -x "$HOOK_SCRIPT" ]; then
    echo "Error: Hook not executable: $HOOK_SCRIPT" >&2
    echo "Attempting to make it executable..." >&2
    chmod +x "$HOOK_SCRIPT" 2>> /workflows:dev-command/null
    if [ ! -x "$HOOK_SCRIPT" ]; then
        echo "Failed to make hook executable" >&2
        exit 2
    fi
fi

# MCP Conversation Flow Validation (optional)
MCP_VALIDATOR="$CLAUDE_DIR/hooks/mcp-conversation-validator.sh"
if [ -x "$MCP_VALIDATOR" ]; then
    # Capture stdin for validation
    STDIN_DATA=$(cat)
    
    # Validate MCP conversation flow if this looks like MCP data
    if echo "$STDIN_DATA" | grep -q "tool_use\|tool_result\|mcp__"; then
        if ! echo "$STDIN_DATA" | "$MCP_VALIDATOR" validate 2>> /workflows:dev-command/null; then
            echo "Warning: MCP conversation flow validation failed" >&2
            # Attempt recovery
            "$MCP_VALIDATOR" recover graceful_degradation >&2 2>> /workflows:dev-command/null || true
        fi
    fi
    
    # Execute the hook with validated stdin
    echo "$STDIN_DATA" | "$HOOK_SCRIPT" "$@"
    hook_exit_code=$?
else
    # Fallback to direct execution if validator not available
    cat | "$HOOK_SCRIPT" "$@"
    hook_exit_code=$?
fi

# Optional: Debug output to stderr (won't interfere with JSON)
# echo "Debug: Executed $HOOK_NAME from $CLAUDE_DIR (exit: $hook_exit_code)" >&2

exit $hook_exit_code