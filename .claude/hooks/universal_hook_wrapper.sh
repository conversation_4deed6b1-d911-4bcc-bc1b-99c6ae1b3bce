#!/bin/bash
# Universal Hook Wrapper - Works from any project, checks for PIB-METHOD hooks
# This wrapper can be symlinked or copied to any project

# Try to find the PIB-METHOD directory
PIB_METHOD_DIR=""

# Check common locations for PIB-METHOD
POSSIBLE_LOCATIONS=(
    "$HOME/Projects/PIB-METHOD"
    "$HOME/projects/PIB-METHOD"
    "$HOME/pib-method"
    "$HOME/PIB-METHOD"
    "../PIB-METHOD"
    "../../PIB-METHOD"
    "/Users/<USER>/Projects/PIB-METHOD"
)

for location in "${POSSIBLE_LOCATIONS[@]}"; do
    if [ -d "$location/.claude/hooks" ]; then
        PIB_METHOD_DIR="$location"
        break
    fi
done

# If PIB-METHOD not found, check if we're already in it
if [ -z "$PIB_METHOD_DIR" ] && [ -d "$PWD/.claude/hooks" ]; then
    PIB_METHOD_DIR="$PWD"
fi

# Get the hook name from the first argument
HOOK_NAME=$1
shift

# If no PIB-METHOD found or no hook name, exit gracefully
if [ -z "$PIB_METHOD_DIR" ] || [ -z "$HOOK_NAME" ]; then
    exit 0
fi

# Check if the hook exists
HOOK_PATH="$PIB_METHOD_DIR/.claude/hooks/$HOOK_NAME"

if [ ! -f "$HOOK_PATH" ]; then
    # Silently exit - hook doesn't exist
    exit 0
fi

# Try to find Python (prefer python3)
PYTHON_CMD=""
if command -v python3 >/dev/null 2>&1; then
    PYTHON_CMD="python3"
elif command -v python >/dev/null 2>&1; then
    PYTHON_CMD="python"
else
    # No Python available, exit gracefully
    exit 0
fi

# Run the hook with Python directly
"$PYTHON_CMD" "$HOOK_PATH" "$@" 2>/dev/null || true

# Always exit successfully to not block workflow
exit 0