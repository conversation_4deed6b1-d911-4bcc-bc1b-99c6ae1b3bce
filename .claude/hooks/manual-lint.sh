#!/bin/bash

# Manual Lint Runner
# Run this when you want to lint your code manually

# Set error handling
set -o pipefail

# Get project name for display
PROJECT_NAME=$(basename "$(pwd)")
echo "🔍 Running lint for project: $PROJECT_NAME"

# Function to detect and run appropriate linter
run_lint() {
    # Check if we're in a project with package.json
    if [ -f "package.json" ]; then
        echo "📦 Detected Node.js project"
        
        # Check for different lint commands
        if grep -q '"lint"' package.json; then
            echo "▶️  Running: npm run lint"
            npm run lint
        elif grep -q '"eslint"' package.json; then
            echo "▶️  Running: npx eslint ."
            npx eslint .
        elif command -v pnpm >> /workflows:dev-command/null 2>&1; then
            echo "▶️  Running: pnpm lint"
            pnpm lint 2>> /workflows:dev-command/null || pnpm run lint 2>> /workflows:dev-command/null || echo "❌ No lint script found"
        else
            echo "❌ No lint configuration found"
            return 1
        fi
        
        # Also run type checking if available
        if grep -q '"typecheck"' package.json; then
            echo "🔧 Running type check..."
            npm run typecheck 2>> /workflows:dev-command/null || pnpm typecheck 2>> /workflows:dev-command/null || echo "⚠️  Type check not available"
        fi
        
    else
        echo "❌ No package.json found - not a Node.js project"
        return 1
    fi
}

# Run the linter
if run_lint; then
    echo "✅ Lint completed for $PROJECT_NAME"
    
    # Send notification
    osascript -e "display notification \"Lint completed\" with title \"PIB - $PROJECT_NAME\" sound name \"Glass\"" 2>> /workflows:dev-command/null || true
else
    echo "❌ Lint failed for $PROJECT_NAME"
    
    # Send error notification
    osascript -e "display notification \"Lint failed - check terminal\" with title \"PIB - $PROJECT_NAME\" sound name \"Basso\"" 2>> /workflows:dev-command/null || true
    exit 1
fi