{"detection_rules": {"frontend": {"react": {"file_patterns": ["*.jsx", "*.tsx"], "import_patterns": ["from 'react'", "from \"react\"", "React", "useState", "useEffect"], "config_files": ["next.config.js", "craco.config.js"], "package_deps": ["react", "react-dom", "next", "@types/react"], "plugin": "react-patterns"}, "vue": {"file_patterns": ["*.vue"], "import_patterns": ["from 'vue'", "from \"vue\"", "createApp", "defineComponent"], "config_files": ["vue.config.js", "vite.config.js"], "package_deps": ["vue", "@vue/cli", "nuxt"], "plugin": "vue-patterns"}, "angular": {"file_patterns": ["*.component.ts", "*.module.ts"], "import_patterns": ["from '@angular", "@Component", "@NgModule"], "config_files": ["angular.json", ".angular-cli.json"], "package_deps": ["@angular/core", "@angular/cli"], "plugin": "angular-patterns"}}, "backend": {"express": {"file_patterns": ["*routes*.js", "*controller*.js", "app.js", "server.js"], "import_patterns": ["require('express')", "from 'express'", "app.get", "router."], "config_files": [], "package_deps": ["express", "body-parser", "cors"], "plugin": "express-patterns"}, "fastapi": {"file_patterns": ["*.py"], "import_patterns": ["from fastapi", "FastAPI", "@app.", "@router."], "config_files": ["pyproject.toml"], "package_deps": ["<PERSON><PERSON><PERSON>", "u<PERSON><PERSON>", "pydantic"], "plugin": "fastapi-patterns"}, "django": {"file_patterns": ["models.py", "views.py", "urls.py", "serializers.py"], "import_patterns": ["from django", "django.db", "django.views"], "config_files": ["manage.py", "settings.py"], "package_deps": ["django", "djangorestframework"], "plugin": "django-patterns"}}, "database": {"postgresql": {"file_patterns": ["*migration*.sql", "*schema*.sql"], "import_patterns": ["pg.Client", "new Pool", "postgres://", "postgresql://"], "config_files": [], "package_deps": ["pg", "node-postgres", "psycopg2", "asyncpg"], "plugin": "postgres-patterns"}, "mongodb": {"file_patterns": ["*model*.js", "*schema*.js"], "import_patterns": ["mongoose", "MongoClient", "mongodb://"], "config_files": [], "package_deps": ["mongoose", "mongodb"], "plugin": "mongodb-patterns"}, "redis": {"file_patterns": [], "import_patterns": ["redis.createClient", "Redis", "i<PERSON>is"], "config_files": [], "package_deps": ["redis", "i<PERSON>is", "node-redis"], "plugin": "redis-patterns"}}, "testing": {"jest": {"file_patterns": ["*.test.js", "*.spec.js", "*.test.ts", "*.spec.ts"], "import_patterns": ["describe", "it(", "expect", "jest.fn"], "config_files": ["jest.config.js", "jest.config.ts"], "package_deps": ["jest", "@types/jest", "ts-jest"], "plugin": "jest-patterns"}, "pytest": {"file_patterns": ["test_*.py", "*_test.py"], "import_patterns": ["import pytest", "def test_", "@pytest."], "config_files": ["pytest.ini", "tox.ini"], "package_deps": ["pytest", "pytest-cov", "pytest-asyncio"], "plugin": "pytest-patterns"}}}, "complexity_indicators": {"simple": {"max_files": 1, "max_technologies": 1, "max_lines": 100, "indicators": ["single file change", "ui only", "config update", "simple crud"]}, "moderate": {"max_files": 5, "max_technologies": 2, "max_lines": 500, "indicators": ["api endpoint", "component with state", "database query", "form handling"]}, "complex": {"max_files": null, "max_technologies": null, "max_lines": null, "indicators": ["multi-service", "real-time", "authentication", "payment integration", "microservices"]}}, "routing_rules": {"simple_tasks": {"agent": "universal-dev-agent", "skip_orchestration": true, "auto_plugin_load": true}, "moderate_tasks": {"agent": "universal-dev-agent", "skip_orchestration": true, "multi_plugin_support": true}, "complex_tasks": {"agent": "tech-lead", "orchestration_required": true, "specialist_coordination": true}}}