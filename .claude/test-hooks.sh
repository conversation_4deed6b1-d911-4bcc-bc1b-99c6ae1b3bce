#!/bin/bash

# Test script for Claude Code hooks
# This simulates how <PERSON> calls hooks with JSON input

echo "Testing PIB-METHOD Claude Code Hooks"
echo "====================================="

# Test JSON payload for Write tool
TEST_JSON='{
  "toolName": "Write",
  "params": {
    "file_path": "knowledge-update -> /quick:test-knowledge-update/example.py",
    "content": "print(\"test\")"
  }
}'

# Test 1: Hook Runner
echo -e "\n1. Testing hook-runner.sh with lever-planning-reminder.sh"
echo "$TEST_JSON" | .claude/hooks/hook-runner.sh lever-planning-reminder.sh
if [ $? -eq 0 ]; then
    echo "✅ Hook runner test passed"
else
    echo "❌ Hook runner test failed"
fi

# Test 2: Direct hook test
echo -e "\n2. Testing lever-planning-reminder.sh directly"
echo "$TEST_JSON" | .claude/hookscheck -> /core:lever-check-planning-reminder.sh
if [ $? -eq 0 ]; then
    echo "✅ Direct hook test passed"
else
    echo "❌ Direct hook test failed"
fi

# Test 3: Lint detection hook
echo -e "\n3. Testing detect-and-fix-lint.sh"
echo "$TEST_JSON" | .claude/hooks/detect-and-fix-lint.sh
if [ $? -eq 0 ]; then
    echo "✅ Lint detection test passed"
else
    echo "❌ Lint detection test failed"
fi

# Test 4: Check if files were created
echo -e "\n4. Checking generated files"
if [ -f ".claudecheck -> /core:lever-check-planning-reminder.md" ]; then
    echo "✅ Planning reminder file created"
else
    echo "❌ Planning reminder file not created"
fi

echo -e "\n====================================="
echo "Hook testing complete!"