# Module-Based Project Initialization Guide

## Current Situation

The current project-init doesn't automatically detect and document modules, but
you can achieve comprehensive module documentation using a combination of
commands.

## Recommended Workflow for Module-Based Projects

### Step 1: Run Standard Project Init

```bash
/workflows:project-init --existing-project
```

This will:

- Detect your tech stack
- Create overall project documentation
- Set up technology specialists

### Step 2: Manually Document Module Structure

After project-init, add a module section to your CLAUDE.md:

```markdown
## Module Architecture

### Detected Modules

1. **Authentication Module** (`/src/modules/auth`)
   - Purpose: User authentication, JWT, permissions
   - Entry: `index.ts`
   - Key Components: AuthController, AuthService, AuthMiddleware

2. **Payment Module** (`/src/modules/payment`)
   - Purpose: Payment processing, subscriptions
   - Entry: `index.ts`
   - Key Components: PaymentController, StripeService

3. **User Module** (`/src/modules/user`)
   - Purpose: User management, profiles
   - Entry: `index.ts`
   - Key Components: UserController, UserService

### Module Routing Rules

- /src/modules/auth/\* → Use auth context
- /src/modules/payment/\* → Use payment context
- /src/modules/user/\* → Use user context
```

### Step 3: Create Module-Specific Documentation

For each major module, run:

```bash
# For each module in your project
/workflows:module-dev auth
/workflows:module-dev payment
/workflows:module-dev user
```

This creates:

- `docs/modules/{module-name}/module-brief.md`
- `docs/modules/{module-name}/module-prd.md`
- `docs/modules/{module-name}/module-architecture.md`

### Step 4: Create Module Integration Guide

Create a manual integration guide:

```bash
# Create this file manually
docs/module-integration-guide.md
```

Content:

```markdown
# Module Integration Guide

## Module Dependencies

- Auth → Used by all modules for authentication
- User → Depends on Auth for permissions
- Payment → Depends on User for customer data

## Shared Components

- Database connections (shared)
- Redis cache (shared)
- Common utilities (/shared)

## Integration Points

- Auth middleware used globally
- User service called by Payment
- Event bus for module communication
```

## Practical Example

### For a Node.js Modular Project:

```
my-app/
├── src/
│   ├── modules/
│   │   ├── auth/
│   │   ├── payment/
│   │   ├── user/
│   │   └── reports/
│   └── shared/
```

**Workflow:**

1. Run `/workflows:project-init --existing-project`
2. Edit CLAUDE.md to add module structure
3. Run `/workflows:module-dev` for each module
4. Create integration guide

### For a Microservices Project:

```
my-platform/
├── services/
│   ├── auth-service/
│   ├── user-service/
│   ├── payment-service/
│   └── notification-service/
```

**Workflow:**

1. Treat each service as a module
2. Run project-init at platform level
3. Document service boundaries in CLAUDE.md
4. Use module-dev for each service

## Quick Module Documentation Template

Create `docs/modules/MODULE_NAME/module-brief.md`:

```markdown
# [Module Name] Module

## Purpose

Brief description of what this module does

## Responsibilities

- Key responsibility 1
- Key responsibility 2
- Key responsibility 3

## API Surface

### Public Exports

- `function1()` - Description
- `Class1` - Description
- `interface1` - Description

### Internal Components

- Private services
- Internal utilities

## Dependencies

### Depends On

- Other module 1
- Other module 2

### Depended By

- Module that uses this
- Another module

## Technology Stack

- Specific to this module
- Special libraries used

## Entry Points

- Main file: `index.ts`
- Routes: `/api/module/*`
- Events: `module.*` events
```

## Tips for Module-Based Projects

1. **Clear Boundaries**: Document what each module exports
2. **Dependency Direction**: Show which modules depend on which
3. **Shared Code**: Put in `/shared` or `/common`
4. **Module Specialists**: Consider creating module-specific agents:

   ```bash
   /workflows:create-tech-agent "Auth Module" --research
   ```

5. **Testing Strategy**: Document module-specific testing
6. **Deployment**: Note if modules deploy separately

## Future Enhancement

We're planning to add automatic module detection to project-init. Until then,
this manual process ensures your modules are properly documented and specialists
understand your architecture.

## Benefits of Module Documentation

1. **Clearer Development**: Developers know module boundaries
2. **Better Specialists**: Agents understand module context
3. **Easier Maintenance**: Module purposes are clear
4. **Proper Integration**: Dependencies documented
5. **Focused Changes**: Work within module scope

The key is to run project-init first for overall setup, then use module-dev for
each module to get comprehensive documentation!
