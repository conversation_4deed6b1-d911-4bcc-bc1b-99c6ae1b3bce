#!/bin/bash

# PIB-METHOD to Claude Code Sub-Agent Migration Utility
# Migrates existing persona-based workflows to Claude Code sub-agent architecture

set -e

# === CONFIGURATION ===
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CLAUDE_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_ROOT="$(dirname "$CLAUDE_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# === LOGGING ===
MIGRATION_LOG="$CLAUDE_DIR/migration.log"

log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    echo "[$timestamp] [$level] $message" | tee -a "$MIGRATION_LOG"
}

info() {
    log "INFO" "$@"
    echo -e "${BLUE}[INFO]${NC} $*"
}

success() {
    log "SUCCESS" "$@"
    echo -e "${GREEN}[SUCCESS]${NC} $*"
}

warning() {
    log "WARNING" "$@"
    echo -e "${YELLOW}[WARNING]${NC} $*"
}

error() {
    log "ERROR" "$@"
    echo -e "${RED}[ERROR]${NC} $*" >&2
}

# === VALIDATION ===
validate_environment() {
    info "Validating migration environment..."
    
    # Check if .claude directory exists
    if [ ! -d "$CLAUDE_DIR" ]; then
        error "Claude directory not found at $CLAUDE_DIR"
        exit 1
    fi
    
    # Check if agents directory exists
    if [ ! -d "$CLAUDE_DIR> /core:agents" ]; then
        error "Sub-agents directory not found. Please ensure sub-agents have been created."
        exit 1
    fi
    
    # Check if jq is available
    if ! command -v jq >/dev/null 2>&1; then
        error "jq is required but not installed. Please install jq."
        exit 1
    fi
    
    success "Environment validation completed"
}

# === SUB-AGENT VALIDATION ===
validate_subagents() {
    info "Validating Claude Code sub-agents..."
    
    local required_subagents=(
        "analyst"
        "architect" 
        "dev-agent"
        "code-reviewer"
        "change-implementer"
        "qa-tester"
        "platform-engineer"
        "task-executor"
        "orchestrator"
    )
    
    local missing_subagents=()
    
    for subagent in "${required_subagents[@]}"; do
        if [ ! -f "$CLAUDE_DIR> /core:agents/${subagent}.md" ]; then
            missing_subagents+=("$subagent")
        fi
    done
    
    if [ ${#missing_subagents[@]} -gt 0 ]; then
        error "Missing required sub-agents: ${missing_subagents[*]}"
        info "Please create the missing sub-agents before running migration"
        exit 1
    fi
    
    success "All required sub-agents are present"
}

# === PERSONA BACKUP ===
backup_personas() {
    info "Creating backup of existing persona files..."
    
    local backup_dir="$CLAUDE_DIR/persona-backup-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$backup_dir"
    
    if [ -d "$PROJECT_ROOT/pib-agent/personas" ]; then
        cp -r "$PROJECT_ROOT/pib-agent/personas" "$backup_dir/"
        success "Personas backed up to $backup_dir"
    else
        warning "No personas directory found to backup"
    fi
}

# === WORKFLOW MIGRATION ===
migrate_workflow_references() {
    info "Migrating workflow references from personas to sub-agents..."
    
    # Create mapping file
    local mapping_file="$CLAUDE_DIR/persona-subagent-mapping.json"
    cat > "$mapping_file" <<EOF
{
  "analyst": "analyst",
  "architect": "architect",
  "dev": "dev-agent",
  "james": "dev-agent",
  "developer": "dev-agent",
  "code-reviewer": "code-reviewer",
  "change-implementer": "change-implementer",
  "qa": "qa-tester",
  "qa-tester": "qa-tester",
  "platform-engineer": "platform-engineer",
  "task-executor": "task-executor",
  "orchestrator": "orchestrator"
}
EOF
    
    success "Persona-to-sub-agent mapping created"
    
    # Update command files
    info "Updating command files..."
    find "$CLAUDE_DIRskeleton -> /template:command-skeletons" -name "*.md" -type f | while read -r cmd_file; do
        if grep -q "persona\|agent.*james\|switch-agent" "$cmd_file"; then
            info "Updating command file: $(basename "$cmd_file")"
            # Create backup
            cp "$cmd_file" "${cmd_file}.backup"
            
            # Update references (this is a simplified example)
            sed -i.tmp 'sagent -> /core:switch-agent-agent james/use dev-agent sub-agent/g' "$cmd_file"
            sed -i.tmp 's/persona.*james> help -> /quick:workflow-helps:dev-command-agent sub-agent/g' "$cmd_file"
            rm -f "${cmd_file}.tmp"
        fi
    done
    
    success "Command files updated"
}

# === HOOK VALIDATION ===
validate_hooks() {
    info "Validating updated hook system..."
    
    local critical_hooks=(
        "agent-orchestration-hook.sh"
        "dev-workflow-enhancer.sh"
        "claude-subagent-coordinator.sh"
    )
    
    for hook in "${critical_hooks[@]}"; do
        local hook_path="$CLAUDE_DIR/hooks/$hook"
        if [ -f "$hook_path" ]; then
            if [ -x "$hook_path" ]; then
                success "Hook validated: $hook"
            else
                warning "Hook not executable: $hook"
                chmod +x "$hook_path"
                success "Made hook executable: $hook"
            fi
        else
            error "Critical hook missing: $hook"
            exit 1
        fi
    done
}

# === CONFIGURATION UPDATE ===
update_configuration() {
    info "Updating PIB configuration for sub-agent architecture..."
    
    # Update settings files if they exist
    for settings_file in "$CLAUDE_DIR/settings.json" "$CLAUDE_DIR/settings.local.json" "$CLAUDE_DIR/settings.dev.json"; do
        if [ -f "$settings_file" ]; then
            info "Updating settings file: $(basename "$settings_file")"
            # Create backup
            cp "$settings_file" "${settings_file}.backup"
            
            # Add sub-agent configuration section (using jq)
            local temp_file=$(mktemp)
            jq '. + {
                "subAgents": {
                    "enabled": true,
                    "architecture": "claude-code",
                    "leverFramework": {
                        "enforced": true,
                        "minimumScore": 4.0
                    },
                    "qualityGates": {
                        "codeReview": "mandatory",
                        "leverCompliance": "mandatory"
                    }
                }
            }' "$settings_file" > "$temp_file" && mv "$temp_file" "$settings_file"
            
            success "Updated settings: $(basename "$settings_file")"
        fi
    done
}

# === WORKFLOW STATE MIGRATION ===
migrate_workflow_state() {
    info "Migrating workflow state to sub-agent architecture..."
    
    local workflow_state="$CLAUDE_DIR/current-workflow-state.json"
    if [ -f "$workflow_state" ]; then
        # Backup current state
        cp "$workflow_state" "${workflow_state}.backup"
        
        # Update workflow state with sub-agent references
        local temp_file=$(mktemp)
        jq '.currentAgent = (.currentAgent | 
            if . == "james" or . == "developer" then "dev-agent"
            elif . == "qa" then "qa-tester"
            else . end) |
            .architecture = "claude-code-subagents" |
            .migrationCompleted = true |
            .migrationTimestamp = "'$(date -u +"%Y-%m-%dT%H:%M:%SZ")'"' \
            "$workflow_state" > "$temp_file" && mv "$temp_file" "$workflow_state"
        
        success "Workflow state migrated"
    fi
}

# === VALIDATION TESTS ===
run_validation_tests() {
    info "Running post-migration validation tests..."
    
    # Test 1: Sub-agent files validation
    info "Test 1: Sub-agent files validation"
    local test_passed=true
    
    for subagent_file in "$CLAUDE_DIR> /core:agents"/*.md; do
        if [ -f "$subagent_file" ]; then
            # Validate YAML frontmatter
            if head -10 "$subagent_file" | grep -q "^name:" && head -10 "$subagent_file" | grep -q "^description:"; then
                success "Sub-agent valid: $(basename "$subagent_file")"
            else
                error "Sub-agent invalid YAML frontmatter: $(basename "$subagent_file")"
                test_passed=false
            fi
        fi
    done
    
    # Test 2: Hook system validation
    info "Test 2: Hook system validation"
    if [ -x "$CLAUDE_DIR/hooks/agent-orchestration-hook.sh" ]; then
        # Test if hook references sub-agents
        if grep -q "sub-agent" "$CLAUDE_DIR/hooks/agent-orchestration-hook.sh"; then
            success "Hook system updated for sub-agents"
        else
            warning "Hook system may not be fully updated"
        fi
    fi
    
    # Test 3: Configuration validation
    info "Test 3: Configuration validation"
    for settings_file in "$CLAUDE_DIR/settings"*.json; do
        if [ -f "$settings_file" ]; then
            if jq -e '.subAgents.enabled' "$settings_file" >/dev/null 2>&1; then
                success "Configuration updated: $(basename "$settings_file")"
            else
                warning "Configuration may not be fully updated: $(basename "$settings_file")"
            fi
        fi
    done
    
    if [ "$test_passed" = true ]; then
        success "All validation tests passed"
        return 0
    else
        error "Some validation tests failed"
        return 1
    fi
}

# === POST-MIGRATION CLEANUP ===
cleanup() {
    info "Performing post-migration cleanup..."
    
    # Remove temporary files
    find "$CLAUDE_DIR" -name "*.tmp" -delete 2>/dev/null || true
    
    # Archive old persona references (optional)
    if [ -d "$PROJECT_ROOT/pib-agent/personas" ]; then
        info "Archiving old persona directory..."
        mv "$PROJECT_ROOT/pib-agent/personas" "$PROJECT_ROOT/pib-agent/personas-archived-$(date +%Y%m%d)"
        success "Old personas archived"
    fi
    
    success "Cleanup completed"
}

# === MIGRATION REPORT ===
generate_migration_report() {
    info "Generating migration report..."
    
    local report_file="$CLAUDE_DIR/migration-report.md"
    cat > "$report_file" <<EOF
# PIB-METHOD to Claude Code Sub-Agent Migration Report

## Migration Summary
- **Date**: $(date -u +"%Y-%m-%d %H:%M:%S UTC")
- **Status**: Completed Successfully
- **Architecture**: Claude Code Sub-Agents

## Sub-Agents Created
$(ls -1 "$CLAUDE_DIR> /core:agents"/*.md | xargs -I {} basename {} .md | sed 's/^/- /')

## Configuration Updates
- Settings files updated with sub-agent configuration
- Workflow state migrated to sub-agent architecture
- Hook system updated for sub-agent coordination

## Backup Locations
- Persona backup: $CLAUDE_DIR/persona-backup-*
- Settings backup: $CLAUDE_DIR/settings*.backup
- Workflow state backup: $CLAUDE_DIR/current-workflow-state.json.backup

## Next Steps
1. Test the new sub-agent system with simple tasks
2. Validate LEVER framework integration
3. Monitor sub-agent delegation and coordination
4. Update team documentation and training

## Migration Log
See detailed migration log at: $MIGRATION_LOG

---
*Generated by PIB-METHOD Migration Utility*
EOF
    
    success "Migration report generated: $report_file"
}

# === MAIN MIGRATION PROCESS ===
main() {
    echo -e "${BLUE}PIB-METHOD to Claude Code Sub-Agent Migration${NC}"
    echo "============================================="
    echo
    
    info "Starting migration process..."
    
    # Pre-migration validation
    validate_environment
    validate_subagents
    
    # Create backups
    backup_personas
    
    # Perform migration
    migrate_workflow_references
    validate_hooks
    update_configuration
    migrate_workflow_state
    
    # Post-migration validation
    if run_validation_tests; then
        cleanup
        generate_migration_report
        
        echo
        success "Migration completed successfully!"
        echo -e "${GREEN}Your PIB-METHOD system has been upgraded to use Claude Code sub-agents.${NC}"
        echo -e "${BLUE}Next steps:${NC}"
        echo "1. Test the system with: help -> /quick:workflow-helps:dev-command \"test sub-agent integration\""
        echo "2. Review the migration report at: $CLAUDE_DIR/migration-report.md"
        echo "3. Monitor the orchestration logs for any issues"
        echo
    else
        error "Migration completed with warnings. Please review the logs."
        exit 1
    fi
}

# Run migration if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi