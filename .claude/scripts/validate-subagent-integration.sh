#!/bin/bash

# Claude Code Sub-Agent Integration Validation Script
# Comprehensive testing of the PIB-METHOD to Claude Code sub-agent migration

set -e

# === CONFIGURATION ===
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CLAUDE_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_ROOT="$(dirname "$CLAUDE_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
NC='\033[0m' # No Color

# === LOGGING ===
VALIDATION_LOG="$CLAUDE_DIR/validation.log"

log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    echo "[$timestamp] [$level] $message" | tee -a "$VALIDATION_LOG"
}

info() {
    log "INFO" "$@"
    echo -e "${BLUE}[INFO]${NC} $*"
}

success() {
    log "SUCCESS" "$@"
    echo -e "${GREEN}[SUCCESS]${NC} $*"
}

warning() {
    log "WARNING" "$@"
    echo -e "${YELLOW}[WARNING]${NC} $*"
}

error() {
    log "ERROR" "$@"
    echo -e "${RED}[ERROR]${NC} $*" >&2
}

test_header() {
    echo -e "\n${MAGENTA}=== $1 ===${NC}"
    info "Starting test: $1"
}

# === VALIDATION TESTS ===
test_subagent_files() {
    test_header "Sub-Agent Files Validation"
    
    local required_subagents=(
        "analyst:Expert research and analysis specialist"
        "architect:Solution Architect & Technical Leader"
        "dev-agent:Expert Senior Software Engineer"
        "code-reviewer:Code Review Agent focused on quality"
        "change-implementer:Change Implementation Agent"
        "qa-tester:Quality Assurance Engineer"
        "platform-engineer:Platform Engineer specializing in infrastructure"
        "task-executor:Task Executor Agent"
        "orchestrator:PIB Method workflow orchestration"
    )
    
    local test_passed=true
    
    for subagent_info in "${required_subagents[@]}"; do
        local subagent_name="${subagent_info%%:*}"
        local expected_desc="${subagent_info#*:}"
        local subagent_file="$CLAUDE_DIR> /core:agents/${subagent_name}.md"
        
        if [ -f "$subagent_file" ]; then
            # Validate YAML frontmatter
            local name_line=$(head -10 "$subagent_file" | grep "^name:" | head -1)
            local desc_line=$(head -10 "$subagent_file" | grep "^description:" | head -1)
            local tools_line=$(head -10 "$subagent_file" | grep "^tools:" | head -1)
            
            if [ -n "$name_line" ] && [ -n "$desc_line" ]; then
                # Validate name matches
                local actual_name=$(echo "$name_line" | sed 's/name: *//')
                if [ "$actual_name" = "$subagent_name" ]; then
                    success "✓ Sub-agent valid: $subagent_name"
                else
                    error "✗ Sub-agent name mismatch: expected '$subagent_name', got '$actual_name'"
                    test_passed=false
                fi
                
                # Check for LEVER framework integration
                if grep -q "LEVER" "$subagent_file"; then
                    success "  ✓ LEVER framework integrated in $subagent_name"
                else
                    warning "  ! LEVER framework not mentioned in $subagent_name"
                fi
                
                # Check for MCP tool integration
                if [ -n "$tools_line" ]; then
                    success "  ✓ MCP tools configured for $subagent_name"
                else
                    warning "  ! No MCP tools specified for $subagent_name"
                fi
                
            else
                error "✗ Sub-agent invalid YAML frontmatter: $subagent_name"
                test_passed=false
            fi
        else
            error "✗ Sub-agent file missing: $subagent_name"
            test_passed=false
        fi
    done
    
    return $([[ "$test_passed" == "true" ]] && echo 0 || echo 1)
}

test_hook_system() {
    test_header "Hook System Integration"
    
    local critical_hooks=(
        "agent-orchestration-hook.sh:sub-agent"
        "dev-workflow-enhancer.sh:claudeCodeSubAgents"
        "claude-subagent-coordinator.sh:subagent"
        "universal-hook-runner.sh:hook"
    )
    
    local test_passed=true
    
    for hook_info in "${critical_hooks[@]}"; do
        local hook_name="${hook_info%%:*}"
        local required_content="${hook_info#*:}"
        local hook_path="$CLAUDE_DIR/hooks/$hook_name"
        
        if [ -f "$hook_path" ]; then
            if [ -x "$hook_path" ]; then
                success "✓ Hook executable: $hook_name"
                
                # Check for sub-agent integration
                if grep -q "$required_content" "$hook_path"; then
                    success "  ✓ Sub-agent integration found in $hook_name"
                else
                    warning "  ! Sub-agent integration not found in $hook_name"
                fi
            else
                error "✗ Hook not executable: $hook_name"
                test_passed=false
            fi
        else
            error "✗ Hook missing: $hook_name"
            test_passed=false
        fi
    done
    
    return $([[ "$test_passed" == "true" ]] && echo 0 || echo 1)
}

test_workflow_commands() {
    test_header "Workflow Command Integration"
    
    local workflow_commands=(
        "dev-command.md:Claude Code Sub-Agent"
        "agents.md:sub-agent"
    )
    
    local test_passed=true
    
    for cmd_info in "${workflow_commands[@]}"; do
        local cmd_file="${cmd_info%%:*}"
        local required_content="${cmd_info#*:}"
        local cmd_path="$CLAUDE_DIRskeleton -> /template:command-skeletonshelp -> /quick:workflow-helps/$cmd_file"
        
        # Also check in other command directories
        if [ ! -f "$cmd_path" ]; then
            cmd_path="$CLAUDE_DIRskeleton -> /template:command-skeletons/core/$cmd_file"
        fi
        
        if [ -f "$cmd_path" ]; then
            if grep -qi "$required_content" "$cmd_path"; then
                success "✓ Command updated for sub-agents: $cmd_file"
            else
                warning "! Command may not be fully updated: $cmd_file"
            fi
        else
            warning "! Command file not found: $cmd_file"
        fi
    done
    
    return $([[ "$test_passed" == "true" ]] && echo 0 || echo 1)
}

test_configuration_files() {
    test_header "Configuration Files Validation"
    
    local test_passed=true
    
    # Check for updated settings files
    for settings_file in "$CLAUDE_DIR/settings"*.json; do
        if [ -f "$settings_file" ]; then
            info "Checking settings file: $(basename "$settings_file")"
            
            # Validate JSON syntax
            if jq empty "$settings_file" >/dev/null 2>&1; then
                success "  ✓ Valid JSON syntax: $(basename "$settings_file")"
                
                # Check for sub-agent configuration
                if jq -e '.subAgents' "$settings_file" >/dev/null 2>&1; then
                    success "  ✓ Sub-agent configuration present"
                else
                    info "  ! Sub-agent configuration not present (may be added by migration)"
                fi
            else
                error "  ✗ Invalid JSON syntax: $(basename "$settings_file")"
                test_passed=false
            fi
        fi
    done
    
    # Check workflow state file
    local workflow_state="$CLAUDE_DIR/current-workflow-state.json"
    if [ -f "$workflow_state" ]; then
        if jq empty "$workflow_state" >/dev/null 2>&1; then
            success "✓ Workflow state file is valid JSON"
        else
            error "✗ Workflow state file has invalid JSON"
            test_passed=false
        fi
    fi
    
    return $([[ "$test_passed" == "true" ]] && echo 0 || echo 1)
}

test_lever_framework_integration() {
    test_header "LEVER Framework Integration"
    
    local test_passed=true
    local lever_mentions=0
    
    # Check sub-agent files for LEVER integration
    for subagent_file in "$CLAUDE_DIR> /core:agents"/*.md; do
        if [ -f "$subagent_file" ]; then
            local subagent_name=$(basename "$subagent_file" .md)
            
            # Count LEVER mentions
            local lever_count=$(grep -c "LEVER" "$subagent_file" 2>/dev/null || echo 0)
            if [ "$lever_count" -gt 0 ]; then
                success "  ✓ LEVER framework in $subagent_name ($lever_count mentions)"
                lever_mentions=$((lever_mentions + lever_count))
            else
                warning "  ! No LEVER framework mentions in $subagent_name"
            fi
            
            # Check for specific LEVER principles
            local principles=("Leverage" "Extend" "Verify" "Eliminate" "Reduce")
            for principle in "${principles[@]}"; do
                if grep -q "$principle" "$subagent_file"; then
                    success "    ✓ $principle principle mentioned"
                fi
            done
        fi
    done
    
    info "Total LEVER framework mentions across sub-agents: $lever_mentions"
    
    if [ "$lever_mentions" -gt 20 ]; then
        success "✓ Strong LEVER framework integration detected"
    elif [ "$lever_mentions" -gt 10 ]; then
        success "✓ Good LEVER framework integration detected"
    elif [ "$lever_mentions" -gt 0 ]; then
        warning "! Moderate LEVER framework integration detected"
    else
        error "✗ No LEVER framework integration detected"
        test_passed=false
    fi
    
    return $([[ "$test_passed" == "true" ]] && echo 0 || echo 1)
}

test_mcp_tool_integration() {
    test_header "MCP Tool Integration"
    
    local test_passed=true
    local mcp_tools=(
        "context7__resolve-library-id"
        "context7__get-library-docs" 
        "playwright__navigate"
        "perplexity__search"
        "Firecrawl__firecrawl_search"
    )
    
    # Check if sub-agents have appropriate MCP tools
    for subagent_file in "$CLAUDE_DIR> /core:agents"/*.md; do
        if [ -f "$subagent_file" ]; then
            local subagent_name=$(basename "$subagent_file" .md)
            local tools_line=$(head -10 "$subagent_file" | grep "^tools:" | head -1)
            
            if [ -n "$tools_line" ]; then
                # Check for MCP tool patterns
                local mcp_count=0
                for tool in "${mcp_tools[@]}"; do
                    if echo "$tools_line" | grep -q "$tool"; then
                        mcp_count=$((mcp_count + 1))
                    fi
                done
                
                if [ "$mcp_count" -gt 0 ]; then
                    success "  ✓ MCP tools configured in $subagent_name ($mcp_count tools)"
                else
                    # Check if it should have MCP tools based on the sub-agent type
                    if [[ "$subagent_name" =~ ^(analyst|architect|dev-agent|qa-tester)$ ]]; then
                        warning "  ! Expected MCP tools not found in $subagent_name"
                    else
                        info "  - No MCP tools expected for $subagent_name"
                    fi
                fi
            else
                warning "  ! No tools configuration found in $subagent_name"
            fi
        fi
    done
    
    return $([[ "$test_passed" == "true" ]] && echo 0 || echo 1)
}

test_migration_artifacts() {
    test_header "Migration Artifacts"
    
    local test_passed=true
    
    # Check for migration script
    if [ -x "$CLAUDE_DIR/scripts/migrate-to-subagents.sh" ]; then
        success "✓ Migration script is present and executable"
    else
        error "✗ Migration script missing or not executable"
        test_passed=false
    fi
    
    # Check for persona backups (if migration was run)
    local backup_dirs=("$CLAUDE_DIR"/persona-backup-*)
    if [ -d "${backup_dirs[0]}" ] 2>/dev/null; then
        success "✓ Persona backup directory exists"
    else
        info "! No persona backup found (migration may not have been run)"
    fi
    
    # Check for mapping files
    if [ -f "$CLAUDE_DIR/persona-subagent-mapping.json" ]; then
        success "✓ Persona-to-sub-agent mapping exists"
        
        # Validate mapping JSON
        if jq empty "$CLAUDE_DIR/persona-subagent-mapping.json" >/dev/null 2>&1; then
            success "  ✓ Mapping JSON is valid"
        else
            error "  ✗ Mapping JSON is invalid"
            test_passed=false
        fi
    else
        info "! Persona-to-sub-agent mapping not found (created during migration)"
    fi
    
    return $([[ "$test_passed" == "true" ]] && echo 0 || echo 1)
}

# === INTEGRATION TESTS ===
run_integration_tests() {
    test_header "Integration Tests"
    
    local test_passed=true
    
    # Test 1: Sub-agent invocation pattern
    info "Testing sub-agent invocation pattern generation..."
    
    # Simulate creating a sub-agent invocation
    local test_invocation_file="$CLAUDE_DIRknowledge-update -> /quick:test-knowledge-update-subagent-invocation.md"
    cat > "$test_invocation_file" <<EOF
# Test Sub-Agent Invocation

> Use the dev-agent sub-agent to implement a simple test function with LEVER framework compliance.

## Expected Behavior
1. Claude Code should automatically delegate to dev-agent
2. dev-agent should use Context7 for pattern research
3. Implementation should follow LEVER principles
4. code-reviewer should validate the implementation

## Test Passed If
- Automatic delegation occurs
- LEVER compliance is achieved
- Quality gates are enforced
EOF
    
    if [ -f "$test_invocation_file" ]; then
        success "✓ Sub-agent invocation pattern created successfully"
        rm -f "$test_invocation_file"
    else
        error "✗ Failed to create sub-agent invocation pattern"
        test_passed=false
    fi
    
    # Test 2: Hook integration
    info "Testing hook integration..."
    
    # Check if hooks can handle sub-agent references
    local test_json='{"event":"PostToolUse","toolName":"Write","result":"test"}'
    
    # Test agent orchestration hook (if it exists and is executable)
    local orchestration_hook="$CLAUDE_DIR/hooks/agent-orchestration-hook.sh"
    if [ -x "$orchestration_hook" ]; then
        # Test that hook can process JSON without errors
        if echo "$test_json" | "$orchestration_hook" >/dev/null 2>&1; then
            success "✓ Agent orchestration hook processes input correctly"
        else
            warning "! Agent orchestration hook may have processing issues"
        fi
    fi
    
    return $([[ "$test_passed" == "true" ]] && echo 0 || echo 1)
}

# === PERFORMANCE TESTS ===
run_performance_tests() {
    test_header "Performance Tests"
    
    local test_passed=true
    
    # Test sub-agent file load times
    info "Testing sub-agent file load performance..."
    
    local start_time=$(date +%s%3N)
    local file_count=0
    
    for subagent_file in "$CLAUDE_DIR> /core:agents"/*.md; do
        if [ -f "$subagent_file" ]; then
            # Simulate reading the file (for size check)
            wc -l "$subagent_file" >/dev/null 2>&1
            file_count=$((file_count + 1))
        fi
    done
    
    local end_time=$(date +%s%3N)
    local duration=$((end_time - start_time))
    
    info "Loaded $file_count sub-agent files in ${duration}ms"
    
    if [ "$duration" -lt 1000 ]; then
        success "✓ Sub-agent file loading performance is excellent"
    elif [ "$duration" -lt 3000 ]; then
        success "✓ Sub-agent file loading performance is good"
    else
        warning "! Sub-agent file loading performance is slower than expected"
    fi
    
    return $([[ "$test_passed" == "true" ]] && echo 0 || echo 1)
}

# === MAIN VALIDATION PROCESS ===
main() {
    echo -e "${BLUE}PIB-METHOD Claude Code Sub-Agent Integration Validation${NC}"
    echo "======================================================"
    echo
    
    info "Starting comprehensive validation..."
    
    local total_tests=0
    local passed_tests=0
    local failed_tests=0
    
    # Run all validation tests
    local tests=(
        "test_subagent_files"
        "test_hook_system"
        "test_workflow_commands"
        "test_configuration_files"
        "test_lever_framework_integration"
        "test_mcp_tool_integration"
        "test_migration_artifacts"
        "run_integration_tests"
        "run_performance_tests"
    )
    
    for test_func in "${tests[@]}"; do
        total_tests=$((total_tests + 1))
        
        if $test_func; then
            passed_tests=$((passed_tests + 1))
            success "Test PASSED: $test_func"
        else
            failed_tests=$((failed_tests + 1))
            error "Test FAILED: $test_func"
        fi
        echo
    done
    
    # Generate validation report
    echo -e "${MAGENTA}=== VALIDATION SUMMARY ===${NC}"
    echo "Total Tests: $total_tests"
    echo -e "Passed: ${GREEN}$passed_tests${NC}"
    echo -e "Failed: ${RED}$failed_tests${NC}"
    echo
    
    if [ "$failed_tests" -eq 0 ]; then
        success "🎉 ALL VALIDATION TESTS PASSED!"
        echo -e "${GREEN}Your PIB-METHOD to Claude Code sub-agent integration is working correctly.${NC}"
        echo
        echo -e "${BLUE}You can now use the system with commands like:${NC}"
        echo "  help -> /quick:workflow-helps:dev-command \"implement user authentication\""
        echo "  > Use the analyst sub-agent to research market trends"
        echo "  > Use the architect sub-agent to design the system architecture"
        echo
        return 0
    else
        error "Some validation tests failed. Please review the issues above."
        echo -e "${YELLOW}Common fixes:${NC}"
        echo "1. Run the migration script: .claude/scripts/migrate-to-subagents.sh"
        echo "2. Check file permissions on hooks"
        echo "3. Validate JSON syntax in configuration files"
        echo "4. Ensure all required sub-agent files exist"
        echo
        return 1
    fi
}

# Run validation if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi