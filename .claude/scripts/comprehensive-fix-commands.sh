#!/bin/bash

# Comprehensive Command Fix Script
# Fixes all corrupted command references found in the audit

set -eo pipefail

echo "🔧 Comprehensive Command Corruption Fix"
echo "======================================"

# Function to fix a single file
fix_file() {
    local file="$1"
    echo "📝 Fixing: $file"
    
    # Skip if file doesn't exist
    if [ ! -f "$file" ]; then
        return 0
    fi
    
    # Create backup
    cp "$file" "$file.fixing" 2>/dev/null || return 0
    
    # Fix corrupted arrow commands
    sed -i '' 's|brief -> /agents:analyst-brief-brief|/agents:analyst-brief|g' "$file"
    sed -i '' 's|design -> /agents:architect-design-design|/agents:architect-design|g' "$file"
    sed -i '' 's|orchestrate -> /agents:pm-orchestrate-prd|/agents:pm-prd|g' "$file"
    sed -i '' 's|orchestrate -> /agents:pm-orchestrate-orchestrate|/agents:pm-orchestrate|g' "$file"
    sed -i '' 's|orchestrate -> /agents:bill-orchestrate-orchestrate|/agents:bill-orchestrate|g' "$file"
    
    # Fix workflow commands
    sed -i '' 's|help -> /quick:workflow-helps:epic-to-stories|/workflows:epic-to-stories|g' "$file"
    sed -i '' 's|help -> /quick:workflow-helps:dev-command|/workflows:dev-command|g' "$file"
    sed -i '' 's|help -> /quick:workflow-helps:module-dev|/workflows:module-dev|g' "$file"
    sed -i '' 's|help -> /quick:workflow-helps:project-init|/workflows:project-init|g' "$file"
    sed -i '' 's|help -> /quick:workflow-helps:feature-start|/workflows:feature-start|g' "$file"
    sed -i '' 's|help -> /quick:workflow-helps:feature-list|/workflows:feature-list|g' "$file"
    sed -i '' 's|help -> /quick:workflow-helps:feature-merge|/workflows:feature-merge|g' "$file"
    sed -i '' 's|help -> /quick:workflow-helps:create-uxui-spec|/workflows:create-uxui-spec|g' "$file"
    sed -i '' 's|help -> /quick:workflow-helps:create-deployment-plan|/workflows:create-deployment-plan|g' "$file"
    sed -i '' 's|help -> /quick:workflow-helps:planning-session|/planning:plan-session|g' "$file"
    sed -i '' 's|help -> /quick:workflow-helps:sub-agent-coordination|/workflows:spawn-subagents|g' "$file"
    
    # Fix planning commands
    sed -i '' 's|session -> /planning:plan-session:plan-sessionning-session|/planning:plan-session|g' "$file"
    sed -i '' 's|to-stories -> /workflows:epic-to-stories-to-stories|/workflows:epic-to-stories|g' "$file"
    
    # Fix corrupted path references
    sed -i '' 's|pib-agent[a-zA-Z0-9-]*brief[a-zA-Z0-9-]*\.md|pib-agent/personas/analyst.md|g' "$file"
    sed -i '' 's|pib-agent[a-zA-Z0-9-]*orchestrate[a-zA-Z0-9-]*\.md|pib-agent/personas/pm.md|g' "$file"
    sed -i '' 's|pib-agent[a-zA-Z0-9-]*design[a-zA-Z0-9-]*\.md|pib-agent/personas/architect.md|g' "$file"
    
    # Fix docs path references
    sed -i '' 's|docs[a-zA-Z0-9-]*init[a-zA-Z0-9-]*brief\.md|docs/project-brief.md|g' "$file"
    sed -i '' 's|docs[a-zA-Z0-9-]*design[a-zA-Z0-9-]*ure\.md|docs/architecture.md|g' "$file"
    sed -i '' 's|docs[a-zA-Z0-9-]*dev[a-zA-Z0-9-]*\/\{module-name\}[a-zA-Z0-9-]*\.md|docs/modules/{module-name}/module-prd.md|g' "$file"
    sed -i '' 's|docs[a-zA-Z0-9-]*to-stories[a-zA-Z0-9-]*s\/|docs/epics/|g' "$file"
    
    # Fix specific corrupted command patterns
    sed -i '' 's|> help -> /quick:workflow-helps:feature-start-start|/workflows:feature-start|g' "$file"
    sed -i '' 's|> help -> /quick:workflow-helps:feature-start-list|/workflows:feature-list|g' "$file"
    sed -i '' 's|> help -> /quick:workflow-helps:feature-start-merge|/workflows:feature-merge|g' "$file"
    
    # Fix knowledge commands
    sed -i '' 's|knowledge -> /knowledge:update-knowledge-knowledge|/knowledge:update-knowledge|g' "$file"
    sed -i '' 's|knowledge -> /knowledge:update-knowledge-module-knowledge|/knowledge:update-knowledge --module|g' "$file"
    
    # Fix analysis commands
    sed -i '' 's|> /analysis:analyze|/analysis:analyze|g' "$file"
    sed -i '' 's|/analysis:codereview|/analysis:codereview|g' "$file"
    
    # Fix mode commands
    sed -i '' 's|mode -> /quick:check-mode[a-zA-Z0-9-]*|/core:check-mode|g' "$file"
    sed -i '' 's|> /quick:dev-mode|/core:dev-mode|g' "$file"
    
    # Fix scripts references
    sed -i '' 's|\.claude/scripts[a-zA-Z0-9-]*infrastructure[a-zA-Z0-9-]*\.sh|.claude/scripts/validate-subagent-integration.sh|g' "$file"
    sed -i '' 's|\.claude[a-zA-Z0-9-]*skeleton[a-zA-Z0-9-]*help[a-zA-Z0-9-]*/|.claude/commands/workflows/|g' "$file"
    
    # Remove backup if no changes
    if diff "$file.fixing" "$file" >/dev/null 2>&1; then
        rm "$file.fixing"
    else
        rm "$file.fixing"
        echo "  ✓ Fixed command references"
    fi
}

# Fix specific high-priority files first
echo "🎯 Fixing high-priority command files..."

# Agent command files
for agent_file in .claude/commands/agents/*.md; do
    if [ -f "$agent_file" ] && [[ "$agent_file" != *.bak ]]; then
        fix_file "$agent_file"
    fi
done

# Workflow command files
for workflow_file in .claude/commands/workflows/*.md; do
    if [ -f "$workflow_file" ] && [[ "$workflow_file" != *.bak ]]; then
        fix_file "$workflow_file"
    fi
done

# Analysis command files
for analysis_file in .claude/commands/analysis/*.md; do
    if [ -f "$analysis_file" ] && [[ "$analysis_file" != *.bak ]]; then
        fix_file "$analysis_file"
    fi
done

# Core command files
for core_file in .claude/commands/core/*.md; do
    if [ -f "$core_file" ] && [[ "$core_file" != *.bak ]]; then
        fix_file "$core_file"
    fi
done

# Planning command files
for planning_file in .claude/commands/planning/*.md; do
    if [ -f "$planning_file" ] && [[ "$planning_file" != *.bak ]]; then
        fix_file "$planning_file"
    fi
done

echo ""
echo "🔧 Fixing documentation and reference files..."

# Fix reference files in .claude directory
while IFS= read -r -d '' file; do
    if [[ "$file" != *.bak ]] && [[ "$file" == *.md ]]; then
        fix_file "$file"
    fi
done < <(find .claude -maxdepth 1 -name "*.md" -type f -print0)

# Fix plans directory
while IFS= read -r -d '' file; do
    if [[ "$file" != *.bak ]] && [[ "$file" == *.md ]]; then
        fix_file "$file"
    fi
done < <(find .claude/plans -name "*.md" -type f -print0 2>/dev/null || true)

echo ""
echo "🔧 Adding missing usage syntax to command files..."

# Fix module-dev command usage syntax
if [ -f ".claude/commands/workflows/module-dev.md" ]; then
    if ! grep -q "^/workflows:module-dev" ".claude/commands/workflows/module-dev.md"; then
        echo "Adding usage syntax to module-dev.md..."
        sed -i '' '6i\
/workflows:module-dev\
' ".claude/commands/workflows/module-dev.md"
    fi
fi

echo ""
echo "✅ Comprehensive fix completed!"
echo ""
echo "Summary of fixes applied:"
echo "- Fixed corrupted arrow command patterns"
echo "- Fixed malformed namespace commands"
echo "- Fixed corrupted file path references"
echo "- Fixed workflow command references"
echo "- Fixed planning command references"
echo "- Fixed docs path references"
echo "- Added missing usage syntax"
echo ""
echo "🔍 Run the audit script again to verify fixes:"
echo "./.claude/scripts/comprehensive-command-audit.sh"