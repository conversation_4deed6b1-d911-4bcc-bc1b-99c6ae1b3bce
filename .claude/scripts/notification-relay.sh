#!/bin/bash

# Claude Code Notification Relay for Docker
# Runs on macOS host to receive notifications from Docker containers
# Usage: ./notification-relay.sh [port]

set -e

# Configuration
PORT="${1:-9999}"
LOGFILE="/tmp/claude-notification-relay.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to log with timestamp
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOGFILE"
}

# Function to send macOS notification
send_mac_notification() {
    local title="$1"
    local message="$2"
    local sound="${3:-Glass}"
    
    osascript -e 'on run {title, message, sound}' \
              -e 'display notification message with title title sound name sound' \
              -e 'end run' \
              "$title" "$message" "$sound" 2>> /workflows:dev-command/null || true
}

# Function to process notification request
process_notification() {
    local request="$1"
    
    # Handle simple netcat format: "NOTIFY: [title] message"
    if [[ "$request" =~ ^NOTIFY:\ \[([^\]]+)\]\ (.+)$ ]]; then
        local title="${BASH_REMATCH[1]}"
        local message="${BASH_REMATCH[2]}"
        send_mac_notification "$title" "$message"
        log_message "Notification sent: [$title] $message"
        return
    fi
    
    # Handle JSON format
    if echo "$request" | jq . >> /workflows:dev-command/null 2>&1; then
        local title=$(echo "$request" | jq -r '.title // "Claude Code"')
        local message=$(echo "$request" | jq -r '.message // "Notification"')
        local sound=$(echo "$request" | jq -r '.sound // "Glass"')
        send_mac_notification "$title" "$message" "$sound"
        log_message "JSON notification sent: [$title] $message"
        return
    fi
    
    # Fallback for plain text
    send_mac_notification "Claude Code" "$request"
    log_message "Plain notification sent: $request"
}

# Function to start HTTP server
start_http_server() {
    print_status $BLUE "Starting HTTP notification relay on port $PORT..."
    
    while true; do
        # Use nc to listen for HTTP requests
        {
            echo -e "HTTP/1.1 200 OK\r\nContent-Type: application/json\r\nAccess-Control-Allow-Origin: *\r\nAccess-Control-Allow-Methods: POST\r\nAccess-Control-Allow-Headers: Content-Type\r\n\r\n{\"status\":\"ok\"}"
        } | nc -l "$PORT" | while IFS= read -r line; do
            # Extract JSON body from HTTP request
            if [[ "$line" =~ ^\{.*\}$ ]]; then
                process_notification "$line"
            fi
        done 2>> /workflows:dev-command/null
    done
}

# Function to start simple netcat server
start_nc_server() {
    print_status $BLUE "Starting netcat notification relay on port $PORT..."
    
    while true; do
        nc -l "$PORT" | while IFS= read -r line; do
            if [ -n "$line" ]; then
                process_notification "$line"
            fi
        done 2>> /workflows:dev-command/null
    done
}

# Function to check dependencies
check_dependencies() {
    if ! command -v nc >> /workflows:dev-command/null 2>&1; then
        print_status $RED "Error: netcat (nc) is required but not installed"
        exit 1
    fi
    
    if ! command -v osascript >> /workflows:dev-command/null 2>&1; then
        print_status $RED "Error: osascript is required (macOS only)"
        exit 1
    fi
    
    if ! command -v jq >> /workflows:dev-command/null 2>&1; then
        print_status $YELLOW "Warning: jq not found, JSON parsing will be limited"
    fi
}

# Function to cleanup on exit
cleanup() {
    print_status $YELLOW "\nShutting down notification relay..."
    log_message "Notification relay stopped"
    exit 0
}

# Main function
main() {
    print_status $GREEN "Claude Code Notification Relay"
    print_status $GREEN "=============================="
    
    # Check dependencies
    check_dependencies
    
    # Setup signal handlers
    trap cleanup SIGINT SIGTERM
    
    # Check if port is available
    if lsof -Pi :$PORT -sTCP:LISTEN -t >> /workflows:dev-command/null 2>&1; then
        print_status $RED "Error: Port $PORT is already in use"
        exit 1
    fi
    
    log_message "Starting notification relay on port $PORT"
    print_status $GREEN "Notification relay starting on port $PORT"
    print_status $BLUE "Log file: $LOGFILE"
    print_status $YELLOW "\nTo use from Docker container:"
    print_status $YELLOW "  export NOTIFICATION_HOST=host.docker.internal"
    print_status $YELLOW "  export NOTIFICATION_PORT=$PORT"
    print_status $YELLOW "\nOr use curl:"
    print_status $YELLOW "  curl -X POST http://host.docker.internal:$PORT -H 'Content-Type: application/json' -d '{\"title\":\"Test\",\"message\":\"Hello from Docker\"}'"
    print_status $YELLOW "\nPress Ctrl+C to stop"
    echo ""
    
    # Start the appropriate server
    if command -v jq >> /workflows:dev-command/null 2>&1; then
        start_http_server
    else
        start_nc_server
    fi
}

# Show usage if help requested
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    cat << EOF
Claude Code Notification Relay for Docker

USAGE:
    $0 [port]

DESCRIPTION:
    Runs a notification relay service on macOS host to receive notifications
    from Claude Code running in Docker containers.

ARGUMENTS:
    port    Port to listen on (default: 9999)

DOCKER SETUP:
    1. Start this relay on your Mac:
       ./notification-relay.sh

    2. In your Docker container, set environment variables:
       export NOTIFICATION_HOST=host.docker.internal
       export NOTIFICATION_PORT=9999

    3. Or use with curl:
       curl -X POST http://host.docker.internal:9999 \\
            -H 'Content-Type: application/json' \\
            -d '{"title":"Claude Code","message":"Hello from Docker"}'

REQUIREMENTS:
    - macOS (for osascript)
    - netcat (nc)
    - jq (optional, for JSON parsing)

EXAMPLES:
    $0              # Start on default port 9999
    $0 8080         # Start on port 8080

EOF
    exit 0
fi

# Run main function
main "$@"