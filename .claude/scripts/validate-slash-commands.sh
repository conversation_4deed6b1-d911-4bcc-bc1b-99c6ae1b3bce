#!/bin/bash

# PIB-METHOD Slash Command Validation Script
# Validates and auto-fixes slash command usage across the codebase

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Auto-fix mode (default: true)
AUTO_FIX="${1:-true}"

# Report file
REPORT_FILE="$PROJECT_ROOT/.claude/slash-commands-validation-report.md"

# Temporary files for tracking
ISSUES_FILE=$(mktemp)
FIXES_FILE=$(mktemp)
LOG_FILE=$(mktemp)

# Initialize counters
echo "0" > "$ISSUES_FILE"
echo "0" > "$FIXES_FILE"

# Create command mappings
cat > /tmp/slash-command-mappings.txt << 'EOF'
> help -> /quick:workflow-helps:dev-command -> help -> /quick:workflow-helps:dev-command
start -> help -> /quick:workflow-helps:feature-start-start -> help -> /quick:workflow-helps:feature-start
start -> help -> /quick:workflow-helps:feature-start-list -> help -> /quick:workflow-helps:feature-list
start -> help -> /quick:workflow-helps:feature-start-merge -> help -> /quick:workflow-helps:feature-merge
start -> help -> /quick:workflow-helps:feature-start-pr -> help -> /quick:workflow-helps:feature-pr
start -> help -> /quick:workflow-helps:feature-start-cleanup -> help -> /quick:workflow-helps:feature-cleanup
session -> session -> help -> /quick:workflow-helps:planning-session:plan-session-session -> session -> session -> help -> /quick:workflow-helps:planning-session:plan-sessionning:plan-session
requirements -> session -> help -> /quick:workflow-helps:planning-session:clarify-requirements-requirements -> session -> session -> help -> /quick:workflow-helps:planning-session:plan-sessionning:clarify-requirements
approach -> session -> help -> /quick:workflow-helps:planning-session:confirm-approach-approach -> session -> session -> help -> /quick:workflow-helps:planning-session:plan-sessionning:confirm-approach
issue -> /github:ai-issue-issue -> /github:ai-issue
issue -> /github:ai-issue-pr -> /github:ai-pr
issue -> /github:ai-issue-assign -> /github:ai-assign
issue -> /github:ai-issue-status -> /github:ai-status
claude-actions -> /github:setup-claude-actions-claude-actions -> /github:setup-claude-actions
> /core:help -> /core:help
> /core:agents -> /core:agents
> /core:tasks -> /core:tasks
next -> /core:what-next-next -> /core:what-next
agent -> /core:switch-agent-agent -> /core:switch-agent
check -> /core:lever-check-check -> /core:lever-check
lever-gates -> /core:enable-lever-gates-lever-gates -> /core:enable-lever-gates
> /analysis:analyze -> /analysis:analyze
> /analysis:thinkdeep -> /analysis:thinkdeep
> /analysis:codereview -> /analysis:codereview
> /analysis:debug -> /analysis:debug
> /analysis:refactor -> /analysis:refactor
> /analysis:secaudit -> /analysis:secaudit
> /analysis:testgen -> /analysis:testgen
> /analysis:docgen -> /analysis:docgen
> /analysis:precommit -> /analysis:precommit
> /analysis:analyze-competitor-ux -> /analysis:analyze-competitor-ux
competitor-features -> /analysis:compare-competitor-features-competitor-features -> /analysis:compare-competitor-features
task -> /core:ultrathink-task-task -> /core:ultrathink-task
> /analysis:review -> /analysis:review
knowledge -> /knowledge:update-knowledge-knowledge -> /knowledge:update-knowledge
extract -> /knowledge:memory-extract-extract -> /knowledge:memory-extract
start -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-start-start -> start -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-starts:session-start
start -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-start-end -> start -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-starts:session-end
start -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-start-list -> start -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-starts:session-list
start -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-start-current -> start -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-starts:session-current
start -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-start-report -> start -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-starts:session-report
start -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-start-continue -> start -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-starts:session-continue
start -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-start-help -> start -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-starts:session-help
start -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-start-updates -> start -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-starts:session-updates
> help -> /quick:workflow-helps:dev-command-orchestrator -> /orchestration:dev-orchestrator
brief -> /agents:analyst-brief-brief -> > /core:agents:analyst-brief
design -> /agents:architect-design-design -> > /core:agents:architect-design
orchestrate -> /agents:pm-orchestrate-orchestrate -> > /core:agents:pm-orchestrate
orchestrate -> /agents:pm-orchestrate-prd -> > /core:agents:pm-prd
orchestrate -> /agents:bill-orchestrate-orchestrate -> > /core:agents:bill-orchestrate
init -> help -> /quick:workflow-helps:project-init-init -> help -> /quick:workflow-helps:project-init
dev -> help -> /quick:workflow-helps:module-dev-dev -> help -> /quick:workflow-helps:module-dev
fix -> help -> /quick:workflow-helps:legacy-fix-fix -> help -> /quick:workflow-helps:legacy-fix
course -> help -> /quick:workflow-helps:correct-course-course -> help -> /quick:workflow-helps:correct-course
deployment-plan -> help -> /quick:workflow-helps:create-deployment-plan-deployment-plan -> help -> /quick:workflow-helps:create-deployment-plan
deployment-plan -> help -> /quick:workflow-helps:create-deployment-plan-enhancement-prd -> help -> /quick:workflow-helps:create-enhancement-prd
deployment-plan -> help -> /quick:workflow-helps:create-deployment-plan-infrastructure-architecture -> help -> /quick:workflow-helps:create-infrastructure-architecture
deployment-plan -> help -> /quick:workflow-helps:create-deployment-plan-uxui-spec -> help -> /quick:workflow-helps:create-uxui-spec
to-stories -> help -> /quick:workflow-helps:epic-to-stories-to-stories -> help -> /quick:workflow-helps:epic-to-stories
check -> /core:lever-check-workflow -> help -> /quick:workflow-helps:lever-workflow
> /analysis:review-infrastructure -> help -> /quick:workflow-helps:review-infrastructure
subagents -> help -> /quick:workflow-helps:spawn-subagents-subagents -> help -> /quick:workflow-helps:spawn-subagents
agent-coordination -> help -> /quick:workflow-helps:sub-agent-coordination-agent-coordination -> help -> /quick:workflow-helps:sub-agent-coordination
agent -> /core:switch-agent-mode -> help -> /quick:workflow-helps:switch-mode
infrastructure -> help -> /quick:workflow-helps:validate-infrastructure-infrastructure -> help -> /quick:workflow-helps:validate-infrastructure
session -> session -> help -> /quick:workflow-helps:planning-session:plan-sessionning-session -> help -> /quick:workflow-helps:planning-session
mode -> /quick:check-mode-mode -> /quick:check-mode
> help -> /quick:workflow-helps:dev-command-mode -> /quick:dev-mode
out -> /quick:doc-out-out -> /quick:doc-out
enhancer -> /quick:prompt-enhancer-enhancer -> /quick:prompt-enhancer
knowledge-update -> /quick:test-knowledge-update-knowledge-update -> /quick:test-knowledge-update
help -> /quick:workflow-help-help -> /quick:workflow-help
skeleton -> /template:command-skeleton-skeleton -> /template:command-skeleton
help -> /quick:workflow-help-status -> help -> /quick:workflow-helps:workflow-status
task -> /core:run-task-task -> /core:run-task
EOF

# Function to check if file should be processed
should_process_file() {
    local file="$1"
    
    # Skip if not a file
    [[ ! -f "$file" ]] && return 1
    
    # Skip binary files
    file "$file" | grep -q "binary" && return 1
    
    # Skip certain paths
    [[ "$file" =~ /(node_modules|\.git|\.next|build|dist|coverage|\.ai)/ ]] && return 1
    
    # Skip certain files
    [[ "$file" =~ (package-lock\.json|yarn\.lock) ]] && return 1
    
    return 0
}

# Function to process a single file
process_file() {
    local file="$1"
    local temp_file=$(mktemp)
    local changes_made=false
    local file_issues=0
    local file_fixes=0
    
    # Copy original to temp
    cp "$file" "$temp_file"
    
    # Read mappings into memory
    while IFS=' -> ' read -r old new; do
        # Count occurrences
        count=$(grep -o "$old" "$file" 2>/dev/null | wc -l)
        
        if [[ $count -gt 0 ]]; then
            file_issues=$((file_issues + count))
            
            if [[ "$AUTO_FIX" == "true" ]]; then
                # Perform replacement
                sed -i.bak "s|$old|$new|g" "$temp_file"
                changes_made=true
                file_fixes=$((file_fixes + count))
                echo -e "${YELLOW}  Fixed: $old → $new ($count occurrences)${NC}" >> "$LOG_FILE"
            else
                echo -e "${RED}  Found: $old should be $new ($count occurrences)${NC}" >> "$LOG_FILE"
            fi
        fi
    done < /tmp/slash-command-mappings.txt
    
    # Apply changes if any were made
    if [[ "$changes_made" == "true" ]]; then
        cp "$temp_file" "$file"
        echo -e "${GREEN}✓ Fixed $file_fixes issues in ${file#$PROJECT_ROOT/}${NC}" >> "$LOG_FILE"
    elif [[ $file_issues -gt 0 ]]; then
        echo -e "${RED}✗ Found $file_issues issues in ${file#$PROJECT_ROOT/}${NC}" >> "$LOG_FILE"
    fi
    
    # Update global counters
    echo $(($(cat "$ISSUES_FILE") + file_issues)) > "$ISSUES_FILE"
    echo $(($(cat "$FIXES_FILE") + file_fixes)) > "$FIXES_FILE"
    
    # Cleanup
    rm -f "$temp_file" "$temp_file.bak"
}

# Main execution
echo -e "${BLUE}=== PIB-METHOD Slash Command Validation ===${NC}"
echo -e "${BLUE}Project Root: $PROJECT_ROOT${NC}"
echo -e "${BLUE}Auto-fix: $AUTO_FIX${NC}"
echo ""

# Start report
cat > "$REPORT_FILE" << EOF
# Slash Command Validation Report

Date: $(date)
Auto-fix: $AUTO_FIX
Project: $PROJECT_ROOT

## Files Processed
EOF

# Process files
echo -e "${YELLOW}Scanning for files to process...${NC}"
file_count=0

# Find all relevant files
find "$PROJECT_ROOT" -type f \( -name "*.md" -o -name "*.sh" -o -name "*.js" -o -name "*.json" \) | while read -r file; do
    if should_process_file "$file"; then
        ((file_count++))
        echo -e "${YELLOW}Processing: ${file#$PROJECT_ROOT/}${NC}"
        process_file "$file"
        
        # Add to report
        echo "- ${file#$PROJECT_ROOT/}" >> "$REPORT_FILE"
    fi
done

# Read final counters
TOTAL_ISSUES=$(cat "$ISSUES_FILE")
TOTAL_FIXES=$(cat "$FIXES_FILE")

# Complete report
cat >> "$REPORT_FILE" << EOF

## Summary

- Total issues found: $TOTAL_ISSUES
- Issues fixed: $TOTAL_FIXES
- Issues remaining: $((TOTAL_ISSUES - TOTAL_FIXES))

## Details

$(cat "$LOG_FILE")

## Command Mappings

All slash commands should use the namespace:command format:

$(cat /tmp/slash-command-mappings.txt | sed 's/ -> / → /')
EOF

# Display summary
echo ""
echo -e "${BLUE}=== Validation Complete ===${NC}"
echo -e "${YELLOW}Total issues found: $TOTAL_ISSUES${NC}"

if [[ "$AUTO_FIX" == "true" ]]; then
    echo -e "${GREEN}Issues fixed: $TOTAL_FIXES${NC}"
    if [[ $((TOTAL_ISSUES - TOTAL_FIXES)) -gt 0 ]]; then
        echo -e "${RED}Issues remaining: $((TOTAL_ISSUES - TOTAL_FIXES))${NC}"
    fi
else
    echo -e "${RED}Run with 'true' parameter to auto-fix issues${NC}"
fi

echo ""
echo -e "${BLUE}Report saved to: ${REPORT_FILE#$PROJECT_ROOT/}${NC}"

# Cleanup
rm -f "$ISSUES_FILE" "$FIXES_FILE" "$LOG_FILE" /tmp/slash-command-mappings.txt

# Exit with error if issues remain
if [[ $TOTAL_ISSUES -gt 0 ]] && [[ "$AUTO_FIX" != "true" ]]; then
    exit 1
elif [[ $((TOTAL_ISSUES - TOTAL_FIXES)) -gt 0 ]]; then
    exit 1
fi

exit 0