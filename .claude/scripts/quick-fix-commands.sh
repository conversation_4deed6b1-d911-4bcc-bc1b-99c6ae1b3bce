#!/bin/bash

# Quick Fix for Common Command Corruptions
# Fixes the most common corrupted command patterns

set -eo pipefail

echo "🔧 Quick-fixing common command corruptions..."

# Function to fix a single file
fix_file() {
    local file="$1"
    echo "📝 Fixing: $file"
    
    # Create backup
    cp "$file" "$file.fixing"
    
    # Most common fixes
    sed -i '' 's|orchestrate -> /agents:pm-orchestrate-prd|/agents:pm-prd|g' "$file"
    sed -i '' 's|brief -> /agents:analyst-brief-brief|/agents:analyst-brief|g' "$file"
    sed -i '' 's|design -> /agents:architect-design-design|/agents:architect-design|g' "$file"
    sed -i '' 's|orchestrate -> /agents:pm-orchestrate-orchestrate|/agents:pm-orchestrate|g' "$file"
    sed -i '' 's|orchestrate -> /agents:bill-orchestrate-orchestrate|/agents:bill-orchestrate|g' "$file"
    
    # Fix corrupted path references
    sed -i '' 's|pib-agentbrief -> /agents:analyst-brief|pib-agent/personas/analyst.md|g' "$file"
    sed -i '' 's|pib-agentorchestrate -> /agents:pm-orchestrate|pib-agent/personas/pm.md|g' "$file"
    sed -i '' 's|pib-agent.*brief.*\.md|pib-agent/personas/analyst.md|g' "$file"
    sed -i '' 's|pib-agent.*orchestrate.*\.md|pib-agent/personas/pm.md|g' "$file"
    
    # Fix workflow commands
    sed -i '' 's|help -> /quick:workflow-helps:epic-to-stories|/workflows:epic-to-stories|g' "$file"
    sed -i '' 's|help -> /quick:workflow-helps:dev-command|/workflows:dev-command|g' "$file"
    sed -i '' 's|help -> /quick:workflow-helps:module-dev|/workflows:module-dev|g' "$file"
    sed -i '' 's|help -> /quick:workflow-helps:project-init|/workflows:project-init|g' "$file"
    
    # Fix core commands  
    sed -i '' 's|knowledge -> /knowledge:update-knowledge-knowledge|/knowledge:update-knowledge|g' "$file"
    sed -i '' 's|> /core:agents:|/agents:|g' "$file"
    
    # Fix analysis commands
    sed -i '' 's|/analysis:analyze|/analysis:analyze|g' "$file"
    
    # Remove backup if no changes
    if diff "$file.fixing" "$file" >/dev/null 2>&1; then
        rm "$file.fixing"
    else
        rm "$file.fixing"
        echo "  ✓ Fixed command references"
    fi
}

# Fix the specific problematic file
if [ -f ".claude/commands/agents/pm-prd.md" ]; then
    fix_file ".claude/commands/agents/pm-prd.md"
fi

# Fix other agent files
for agent_file in .claude/commands/agents/*.md; do
    if [ -f "$agent_file" ]; then
        fix_file "$agent_file"
    fi
done

echo "✅ Quick fixes completed!"
echo ""
echo "The command '/agents:pm-prd' should now work correctly."