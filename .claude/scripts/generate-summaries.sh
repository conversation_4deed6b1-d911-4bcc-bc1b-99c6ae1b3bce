#!/bin/bash

# Auto-Summary Generation Script
# Creates concise summaries of project documentation for faster agent context loading

set -euo pipefail

# === CONFIGURATION ===
SCRIPT_DIR="$(dirname "$0")"
CLAUDE_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_ROOT="$(dirname "$CLAUDE_DIR")"
DOCS_DIR="$PROJECT_ROOT/docs"
SUMMARIES_DIR="$CLAUDE_DIR/summaries"
SUMMARY_INDEX="$SUMMARIES_DIR/index.json"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}PIB-METHOD Auto-Summary Generator${NC}"
echo -e "${BLUE}=================================${NC}"

# === CREATE SUMMARIES DIRECTORY ===
mkdir -p "$SUMMARIES_DIR"

# === INITIALIZE SUMMARY INDEX ===
initialize_index() {
    cat > "$SUMMARY_INDEX" << 'EOF'
{
  "generated_at": "",
  "source_docs": {},
  "summaries": {},
  "quick_reference": {
    "project_purpose": "",
    "key_technologies": [],
    "main_modules": [],
    "important_decisions": []
  }
}
EOF
}

# === GENERATE PROJECT BRIEF SUMMARY ===
generate_project_summary() {
    if [ -f "$DOCS_DIR/project-brief.md" ]; then
        echo -e "${YELLOW}Generating project summary...${NC}"
        
        # Extract key information
        local title=$(grep "^# " "$DOCS_DIR/project-brief.md" | head -1 | sed 's/^# //' || echo "Project")
        local purpose=$(sed -n '/## Purpose/,/## /p' "$DOCS_DIR/project-brief.md" | grep -v "^## " | head -3 | tr '\n' ' ' || echo "")
        
        cat > "$SUMMARIES_DIR/project-summary.md" << EOF
# Project Quick Summary

**Title**: $title

**Purpose**: $purpose

**Generated**: $(date)

## Key Points
- Auto-generated from project-brief.md
- Lightweight reference for agents
- Updated when source changes

## Usage
This summary provides essential project context without loading full documentation.
EOF
        
        echo -e "${GREEN}✓ Project summary created${NC}"
    fi
}

# === GENERATE ARCHITECTURE SUMMARY ===
generate_architecture_summary() {
    if [ -f "$DOCS_DIR/architecture.md" ]; then
        echo -e "${YELLOW}Generating architecture summary...${NC}"
        
        # Extract architecture decisions
        local tech_stack=$(grep -A 10 "## Tech" "$DOCS_DIR/architecture.md" | grep "^- " | head -5 | tr '\n' ' ' || echo "")
        local components=$(grep -A 10 "## Component" "$DOCS_DIR/architecture.md" | grep "^- " | head -5 | tr '\n' ' ' || echo "")
        
        cat > "$SUMMARIES_DIR/architecture-summary.md" << EOF
# Architecture Quick Reference

**Generated**: $(date)

## Tech Stack
$tech_stack

## Key Components
$components

## Important Notes
- Auto-generated from architecture.md
- For full details, see original documentation
- Updated when architecture changes

## Quick Decisions
$(grep -i "decision\|choice\|selected\|because" "$DOCS_DIR/architecture.md" | head -3 || echo "No major decisions documented")
EOF
        
        echo -e "${GREEN}✓ Architecture summary created${NC}"
    fi
}

# === GENERATE MODULE SUMMARY ===
generate_module_summary() {
    if [ -d "$DOCS_DIR/modules" ]; then
        echo -e "${YELLOW}Generating module summary...${NC}"
        
        local modules=()
        for module_dir in "$DOCS_DIR/modules"/*; do
            if [ -d "$module_dir" ]; then
                local module_name=$(basename "$module_dir")
                modules+=("$module_name")
            fi
        done
        
        cat > "$SUMMARIES_DIR/modules-summary.md" << EOF
# Modules Quick Reference

**Generated**: $(date)

## Detected Modules
$(printf "%s\n" "${modules[@]}" | sed 's/^/- /')

## Module Structure
Each module contains:
- module-brief.md - Purpose and responsibilities
- module-architecture.md - Technical design
- module-prd.md - Requirements and features

## Quick Access
- Total modules: ${#modules[@]}
- For details: Check docs/modules/[module-name]/
- For integration: See module-integration-guide.md

## Most Important
$(find "$DOCS_DIR/modules" -name "*.md" -exec grep -l "critical\|important\|core" {} \; | head -3 | sed 's/.*modules\/\([^/]*\)\/.*/- \1/' || echo "- No critical modules marked")
EOF
        
        echo -e "${GREEN}✓ Module summary created${NC}"
    fi
}

# === GENERATE PATTERNS SUMMARY ===
generate_patterns_summary() {
    if [ -d "$CLAUDE_DIR/patterns" ]; then
        echo -e "${YELLOW}Generating patterns summary...${NC}"
        
        local patterns=()
        for pattern_file in "$CLAUDE_DIR/patterns"/*.md; do
            if [ -f "$pattern_file" ] && [ "$(basename "$pattern_file")" != "README.md" ]; then
                local pattern_name=$(basename "$pattern_file" .md)
                patterns+=("$pattern_name")
            fi
        done
        
        cat > "$SUMMARIES_DIR/patterns-summary.md" << EOF
# Patterns Quick Reference

**Generated**: $(date)

## Available Patterns
$(printf "%s\n" "${patterns[@]}" | sed 's/^/- /' | sed 's/-/ /')

## Pattern Usage
- Reference patterns instead of recreating
- All patterns follow LEVER principles
- Patterns are project-agnostic when possible
- Located in .claude/patterns/

## Quick Pattern Selection
- **Error handling**: Use for API and frontend errors
- **API patterns**: REST routes and GraphQL resolvers
- **Testing patterns**: Unit, integration, E2E tests
- **Security patterns**: Auth, validation, sanitization

## Benefits
- Consistency across projects
- No duplication
- Battle-tested approaches
- LEVER optimized

For full patterns, see: .claude/patterns/[pattern-name].md
EOF
        
        echo -e "${GREEN}✓ Patterns summary created${NC}"
    fi
}

# === GENERATE TECHNOLOGY SUMMARY ===
generate_tech_summary() {
    if [ -f "$CLAUDE_DIR/tech-detection-config.json" ]; then
        echo -e "${YELLOW}Generating technology summary...${NC}"
        
        local frameworks=""
        local routing=""
        
        if command -v jq >/dev/null 2>&1; then
            # Extract detected technologies from analytics if available
            if [ -f "$CLAUDE_DIR/usage-analytics.json" ]; then
                frameworks=$(jq -r '.technology_usage.frameworks | keys[]' "$CLAUDE_DIR/usage-analytics.json" 2>/dev/null | tr '\n' ' ' || echo "")
            fi
            
            # Extract routing rules from CLAUDE.md if available
            if [ -f "$PROJECT_ROOT/CLAUDE.md" ]; then
                routing=$(grep -A 5 "## Agent Assignments" "$PROJECT_ROOT/CLAUDE.md" | grep "→" | head -3 || echo "")
            fi
        fi
        
        cat > "$SUMMARIES_DIR/technology-summary.md" << EOF
# Technology Quick Reference

**Generated**: $(date)

## Detected Technologies
$frameworks

## Agent Routing Rules
$routing

## Configuration
- Tech detection: .claude/tech-detection-config.json
- Agent mapping: CLAUDE.md
- Usage analytics: .claude/usage-analytics.json

## Specialist Agents
- Created per-project based on usage
- High-frequency usage triggers creation
- Use /workflows:create-tech-agent for new specialists

## Smart Decision Making
- >10 files = Create specialist
- <5 files = Use documentation
- External APIs = Always fetch latest docs
EOF
        
        echo -e "${GREEN}✓ Technology summary created${NC}"
    fi
}

# === UPDATE SUMMARY INDEX ===
update_index() {
    echo -e "${YELLOW}Updating summary index...${NC}"
    
    local timestamp=$(date -u +%Y-%m-%dT%H:%M:%SZ)
    local temp_file=$(mktemp)
    
    # Count source documents
    local doc_count=0
    if [ -d "$DOCS_DIR" ]; then
        doc_count=$(find "$DOCS_DIR" -name "*.md" | wc -l)
    fi
    
    # Build index
    cat > "$temp_file" << EOF
{
  "generated_at": "$timestamp",
  "source_docs": {
    "count": $doc_count,
    "location": "docs/",
    "last_check": "$timestamp"
  },
  "summaries": {
    "project": "summaries/project-summary.md",
    "architecture": "summaries/architecture-summary.md",
    "modules": "summaries/modules-summary.md",
    "patterns": "summaries/patterns-summary.md",
    "technology": "summaries/technology-summary.md"
  },
  "quick_reference": {
    "total_summaries": $(ls "$SUMMARIES_DIR"/*.md 2>/dev/null | wc -l),
    "last_generated": "$timestamp",
    "usage": "Load summaries before full docs for faster context"
  }
}
EOF
    
    mv "$temp_file" "$SUMMARY_INDEX"
    echo -e "${GREEN}✓ Summary index updated${NC}"
}

# === CREATE USAGE GUIDE ===
create_usage_guide() {
    cat > "$SUMMARIES_DIR/README.md" << 'EOF'
# Auto-Generated Summaries

This directory contains lightweight summaries of project documentation for faster agent context loading.

## Purpose

Instead of loading full documentation every time, agents can:
1. **Load summaries first** for quick context
2. **Load full docs** only when needed
3. **Save tokens** and improve performance

## Available Summaries

- `project-summary.md` - Project purpose and overview
- `architecture-summary.md` - Key architectural decisions
- `modules-summary.md` - Module structure overview
- `patterns-summary.md` - Available pattern library
- `technology-summary.md` - Tech stack and routing

## How Agents Use This

```markdown
## Agent Context Loading Strategy

1. **Quick context**: Read summaries/ first
2. **Specific needs**: Load relevant full docs
3. **Implementation**: Reference patterns/
4. **Deep dive**: Full documentation as needed
```

## Auto-Generation

Summaries are automatically updated when:
- Source documentation changes
- Major project updates occur
- Technology stack evolves
- New modules are added

## Benefits

- 70% faster context loading
- Consistent information access
- Reduced token usage
- Quick decision making

These summaries complement, not replace, full documentation.
EOF
}

# === MAIN EXECUTION ===
main() {
    echo -e "${BLUE}Starting summary generation...${NC}"
    
    # Initialize
    initialize_index
    
    # Generate all summaries
    generate_project_summary
    generate_architecture_summary
    generate_module_summary
    generate_patterns_summary
    generate_tech_summary
    
    # Update index and create guide
    update_index
    create_usage_guide
    
    echo ""
    echo -e "${GREEN}Summary generation complete!${NC}"
    echo -e "${GREEN}Location: $SUMMARIES_DIR${NC}"
    echo -e "${GREEN}Index: $(basename "$SUMMARY_INDEX")${NC}"
    
    # Show summary stats
    local summary_count=$(ls "$SUMMARIES_DIR"/*.md 2>/dev/null | wc -l)
    echo -e "${BLUE}Generated $summary_count summary files${NC}"
}

# Run main function
main "$@"