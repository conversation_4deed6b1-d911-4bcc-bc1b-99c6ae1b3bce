#!/bin/bash

echo "=== Testing Agent Namespace Commands ==="
echo ""

CLAUDE_DIR="/Users/<USER>/Projects/PIB-METHOD/.claude"

# Test each agent command for proper formatting
AGENT_COMMANDS=(
    "pm-orchestrate"
    "pm-prd"
    "analyst-brief"
    "architect-design"
    "bill-orchestrate"
)

echo "Checking agent command files..."
echo "================================"

for cmd in "${AGENT_COMMANDS[@]}"; do
    file_path="$CLAUDE_DIR/commands/agents/$cmd.md"
    
    echo ""
    echo "Testing: /agents:$cmd"
    echo "File: $file_path"
    
    if [ ! -f "$file_path" ]; then
        echo "  ❌ File missing!"
        continue
    fi
    
    # Check for usage pattern
    if grep -q "/agents:$cmd" "$file_path"; then
        echo "  ✅ Usage pattern found"
        
        # Show the usage section
        echo "  📄 Usage section:"
        grep -A 3 "## Usage" "$file_path" | head -5 | sed 's/^/     /'
    else
        echo "  ❌ Usage pattern NOT found - command won't be recognized!"
    fi
    
    # Check file size (empty files won't work)
    size=$(stat -f%z "$file_path" 2>/dev/null || stat -c%s "$file_path" 2>/dev/null)
    if [ "$size" -lt 100 ]; then
        echo "  ⚠️  File seems too small ($size bytes)"
    fi
    
    # Check for common corruption patterns
    if grep -q " -> /" "$file_path"; then
        echo "  ⚠️  Contains corrupted arrow patterns"
    fi
done

echo ""
echo "================================"
echo "Summary:"
echo "- All agent command files should exist"
echo "- All should have proper /agents:command-name usage"
echo "- None should have corrupted patterns"
echo ""

# Check if agents directory is properly structured
echo "Agents directory structure:"
ls -la "$CLAUDE_DIR/commands/agents/" | grep -v "^total" | grep -v ".bak$" | head -10

echo ""
echo "If commands still fail:"
echo "1. Restart Claude Code to reload definitions"
echo "2. Check for syntax errors in command files"
echo "3. Verify no duplicate command definitions"