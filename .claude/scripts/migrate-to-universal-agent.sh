#!/bin/bash

# Migration Script: dev-agent → universal-dev-agent
# Helps transition projects to the new universal agent system

set -euo pipefail

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# Configuration
CLAUDE_DIR="$(dirname "$0")/.."
BACKUP_DIR="$CLAUDE_DIR/backups/agent-migration-$(date +%Y%m%d-%H%M%S)"

echo -e "${GREEN}PIB-METHOD Agent Migration Tool${NC}"
echo -e "${GREEN}==============================${NC}"
echo ""
echo "This script will help migrate from dev-agent to universal-dev-agent"
echo ""

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Function to backup a file
backup_file() {
    local file="$1"
    local backup_path="$BACKUP_DIR/$(basename "$file")"
    cp "$file" "$backup_path"
    echo -e "${YELLOW}Backed up: $(basename "$file")${NC}"
}

# Function to update agent references
update_agent_references() {
    local file="$1"
    local updated=false
    
    # Check if file contains dev-agent references
    if grep -q "dev-agent" "$file" 2>/dev/null; then
        backup_file "$file"
        
        # Update references
        sed -i.tmp 's/subagent_type": "dev-agent"/subagent_type": "universal-dev-agent"/g' "$file"
        sed -i.tmp 's/delegate to dev-agent/delegate to universal-dev-agent/g' "$file"
        sed -i.tmp 's/@dev-agent/@universal-dev-agent/g' "$file"
        
        # Clean up temp files
        rm -f "$file.tmp"
        
        updated=true
        echo -e "${GREEN}Updated: $file${NC}"
    fi
    
    echo "$updated"
}

# Step 1: Check for existing dev-agent references
echo -e "\n${YELLOW}Step 1: Scanning for dev-agent references...${NC}"
DEV_AGENT_REFS=$(grep -r "dev-agent" "$CLAUDE_DIR" --include="*.md" --include="*.json" --include="*.sh" 2>/dev/null | grep -v "universal-dev-agent" | wc -l || echo "0")

if [ "$DEV_AGENT_REFS" -eq 0 ]; then
    echo -e "${GREEN}No dev-agent references found. System may already be migrated.${NC}"
    exit 0
fi

echo -e "${YELLOW}Found $DEV_AGENT_REFS references to migrate${NC}"

# Step 2: Create universal-dev-agent if it doesn't exist
echo -e "\n${YELLOW}Step 2: Checking universal-dev-agent...${NC}"
if [ ! -f "$CLAUDE_DIR/agents/universal-dev-agent.md" ]; then
    echo -e "${RED}Universal-dev-agent not found! Please run the implementation first.${NC}"
    exit 1
fi
echo -e "${GREEN}Universal-dev-agent exists ✓${NC}"

# Step 3: Update workflow files
echo -e "\n${YELLOW}Step 3: Updating workflow files...${NC}"
for workflow in "$CLAUDE_DIR/commands/workflows"/*.md; do
    if [ -f "$workflow" ]; then
        update_agent_references "$workflow"
    fi
done

# Step 4: Update hook files
echo -e "\n${YELLOW}Step 4: Updating hook files...${NC}"
for hook in "$CLAUDE_DIR/hooks"/*.sh; do
    if [ -f "$hook" ]; then
        update_agent_references "$hook"
    fi
done

# Step 5: Update settings files
echo -e "\n${YELLOW}Step 5: Updating settings files...${NC}"
for settings in "$CLAUDE_DIR"/*.json; do
    if [ -f "$settings" ]; then
        update_agent_references "$settings"
    fi
done

# Step 6: Create migration report
echo -e "\n${YELLOW}Step 6: Creating migration report...${NC}"
cat > "$CLAUDE_DIR/AGENT-MIGRATION-REPORT.md" << EOF
# Agent Migration Report

**Date**: $(date +%Y-%m-%d)
**Time**: $(date +%H:%M:%S)

## Migration Summary

- Original agent: dev-agent
- New agent: universal-dev-agent
- References updated: $DEV_AGENT_REFS
- Backup location: $BACKUP_DIR

## What Changed

1. **Agent Name**: dev-agent → universal-dev-agent
2. **Capabilities**: Added dynamic plugin loading
3. **Routing**: Smart complexity-based routing
4. **Performance**: Direct execution for simple tasks

## New Features

- Automatic technology detection
- Plugin-based specialization
- Reduced orchestration overhead
- LEVER framework integration

## Next Steps

1. Test updated workflows
2. Monitor performance improvements
3. Report any issues
4. Gradually adopt new features

## Rollback Instructions

If needed, restore from backup:
\`\`\`bash
cp $BACKUP_DIR/* $CLAUDE_DIR/
\`\`\`
EOF

echo -e "${GREEN}Created migration report${NC}"

# Step 7: Verify migration
echo -e "\n${YELLOW}Step 7: Verifying migration...${NC}"
REMAINING_REFS=$(grep -r "dev-agent" "$CLAUDE_DIR" --include="*.md" --include="*.json" --include="*.sh" 2>/dev/null | grep -v "universal-dev-agent" | grep -v "backups" | wc -l || echo "0")

if [ "$REMAINING_REFS" -eq 0 ]; then
    echo -e "${GREEN}Migration completed successfully!${NC}"
    echo -e "${GREEN}All dev-agent references have been updated.${NC}"
else
    echo -e "${YELLOW}Warning: $REMAINING_REFS references may still need manual update${NC}"
    echo -e "${YELLOW}Run 'grep -r \"dev-agent\" $CLAUDE_DIR' to find them${NC}"
fi

echo -e "\n${GREEN}Migration Summary:${NC}"
echo -e "- Backup created at: $BACKUP_DIR"
echo -e "- Report saved to: AGENT-MIGRATION-REPORT.md"
echo -e "- Original references: $DEV_AGENT_REFS"
echo -e "- Remaining references: $REMAINING_REFS"
echo -e "\n${GREEN}Done! Please test your workflows to ensure everything works correctly.${NC}"

# Make script executable
chmod +x "$0"