#!/bin/bash

# Hook Validation Script
# Validates that all critical Python hooks exist and are executable

set -e

HOOKS_DIR=".claude/hooks"
CRITICAL_HOOKS=(
    "stop.py"
    "session_start.py"
    "pre_tool_use.py"
    "post_tool_use.py"
    "user_prompt_submit.py"
)

echo "🔍 Validating Claude Code hooks..."

# Check if hooks directory exists
if [ ! -d "$HOOKS_DIR" ]; then
    echo "❌ Hooks directory not found: $HOOKS_DIR"
    exit 1
fi

# Check each critical hook
for hook in "${CRITICAL_HOOKS[@]}"; do
    hook_path="$HOOKS_DIR/$hook"
    
    if [ ! -f "$hook_path" ]; then
        echo "❌ Missing critical hook: $hook"
        exit 1
    fi
    
    if [ ! -x "$hook_path" ]; then
        echo "⚠️  Hook not executable: $hook (fixing...)"
        chmod +x "$hook_path"
    fi
    
    # Check shebang line
    first_line=$(head -n1 "$hook_path")
    if [[ "$first_line" == *"uv run --script"* ]]; then
        echo "❌ Hook has problematic shebang: $hook"
        echo "   Found: $first_line"
        echo "   Should be: #!/usr/bin/env python3"
        exit 1
    fi
    
    # Test hook execution
    if ! echo '{}' | uv run "$hook_path" >/dev/null 2>&1; then
        echo "❌ Hook execution failed: $hook"
        exit 1
    fi
    
    echo "✅ $hook"
done

echo "🎉 All hooks validated successfully!"
