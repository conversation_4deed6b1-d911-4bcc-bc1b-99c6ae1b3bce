#!/bin/bash

# Test script for recently fixed commands
echo "Testing recently fixed PIB-METHOD commands..."
echo "============================================="

CLAUDE_DIR="/Users/<USER>/Projects/PIB-METHOD/.claude"

# Test commands that were reported as broken
COMMANDS_TO_TEST=(
    "agents:pm-prd"
    "agents:architect-design" 
    "workflows:create-uxui-spec"
    "workflows:sub-agent-coordination"
    "knowledge:update-knowledge"
    "knowledge:memory-extract"
)

echo "Checking command files exist and have proper syntax..."
echo ""

for cmd in "${COMMANDS_TO_TEST[@]}"; do
    # Convert namespace:command to file path
    namespace=$(echo $cmd | cut -d: -f1)
    command=$(echo $cmd | cut -d: -f2)
    
    file_path="$CLAUDE_DIR/commands/$namespace/$command.md"
    
    if [ -f "$file_path" ]; then
        echo "✅ /$cmd - File exists"
        
        # Check for proper usage syntax
        if grep -q "/$cmd" "$file_path"; then
            echo "  ✅ Proper usage syntax found"
        else
            echo "  ❌ Missing proper usage syntax"
        fi
        
        # Check for old corrupted patterns
        if grep -q " -> /" "$file_path"; then
            echo "  ⚠️  Still contains arrow-based corrupted commands"
        else
            echo "  ✅ No corrupted arrow commands found"
        fi
        
    else
        echo "❌ /$cmd - File missing at: $file_path"
    fi
    echo ""
done

echo "============================================="
echo "Command validation complete."