#!/bin/bash
# UV Installation Helper for PIB-METHOD
# This script helps install and configure UV for all projects

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# UV settings
UV_INSTALLER_URL="https://astral.sh/uv/install.sh"
UV_PATH="$HOME/.local/bin"

echo -e "${BLUE}PIB-METHOD UV Installation Helper${NC}"
echo -e "${BLUE}=================================${NC}"
echo ""

# Check if UV is already installed
if command -v uv >/dev/null 2>&1; then
    UV_VERSION=$(uv --version 2>/dev/null || echo "unknown")
    echo -e "${GREEN}✓ UV is already installed: $UV_VERSION${NC}"
    echo ""
    echo "UV is working correctly. No action needed."
    exit 0
fi

# Check if UV exists but not in PATH
if [ -f "$UV_PATH/uv" ]; then
    echo -e "${YELLOW}UV is installed but not in your PATH${NC}"
    echo ""
    echo "Add this line to your shell configuration file:"
    echo ""
    
    # Detect shell
    if [ -n "$ZSH_VERSION" ]; then
        SHELL_RC="$HOME/.zshrc"
        echo "  echo 'export PATH=\"$UV_PATH:\$PATH\"' >> ~/.zshrc"
    elif [ -n "$BASH_VERSION" ]; then
        SHELL_RC="$HOME/.bashrc"
        echo "  echo 'export PATH=\"$UV_PATH:\$PATH\"' >> ~/.bashrc"
    else
        SHELL_RC="your shell configuration file"
        echo "  export PATH=\"$UV_PATH:\$PATH\""
    fi
    
    echo ""
    echo "Then reload your shell configuration:"
    echo "  source $SHELL_RC"
    echo ""
    
    read -p "Would you like me to add UV to your PATH automatically? (y/N): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if [ -n "$ZSH_VERSION" ]; then
            echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.zshrc
            echo -e "${GREEN}✓ Added UV to ~/.zshrc${NC}"
            echo "Please run: source ~/.zshrc"
        elif [ -n "$BASH_VERSION" ]; then
            echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
            echo -e "${GREEN}✓ Added UV to ~/.bashrc${NC}"
            echo "Please run: source ~/.bashrc"
        fi
    fi
    
    exit 0
fi

# UV not found - install it
echo -e "${YELLOW}UV is not installed${NC}"
echo ""
echo "UV is required for PIB-METHOD hooks to function properly."
echo "It's a fast Python package and project manager."
echo ""
read -p "Would you like to install UV now? (y/N): " -n 1 -r
echo ""

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}Installation cancelled${NC}"
    echo ""
    echo "You can install UV later by running:"
    echo "  curl -LsSf $UV_INSTALLER_URL | sh"
    exit 0
fi

# Install UV
echo -e "${BLUE}Installing UV...${NC}"
if curl -LsSf "$UV_INSTALLER_URL" | sh; then
    echo -e "${GREEN}✓ UV installed successfully!${NC}"
    echo ""
    
    # Add to PATH instructions
    echo -e "${YELLOW}Important: Add UV to your PATH${NC}"
    echo ""
    
    if [ -n "$ZSH_VERSION" ]; then
        echo "Add this line to your ~/.zshrc:"
        echo '  export PATH="$HOME/.local/bin:$PATH"'
        echo ""
        echo "Then run: source ~/.zshrc"
        
        echo ""
        read -p "Would you like me to add this automatically? (y/N): " -n 1 -r
        echo ""
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.zshrc
            echo -e "${GREEN}✓ Added to ~/.zshrc${NC}"
            echo "Please run: source ~/.zshrc"
        fi
    elif [ -n "$BASH_VERSION" ]; then
        echo "Add this line to your ~/.bashrc:"
        echo '  export PATH="$HOME/.local/bin:$PATH"'
        echo ""
        echo "Then run: source ~/.bashrc"
        
        echo ""
        read -p "Would you like me to add this automatically? (y/N): " -n 1 -r
        echo ""
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
            echo -e "${GREEN}✓ Added to ~/.bashrc${NC}"
            echo "Please run: source ~/.bashrc"
        fi
    else
        echo "Add this line to your shell configuration:"
        echo '  export PATH="$HOME/.local/bin:$PATH"'
    fi
    
    echo ""
    echo -e "${GREEN}UV installation complete!${NC}"
    echo ""
    echo "Next steps:"
    echo "1. Reload your shell or open a new terminal"
    echo "2. Run the PIB-METHOD sync script: ./sync-pib-complete.sh"
    echo "3. Restart Claude Code to load the hooks"
else
    echo -e "${RED}Failed to install UV${NC}"
    echo ""
    echo "Please try installing manually:"
    echo "  curl -LsSf $UV_INSTALLER_URL | sh"
    exit 1
fi