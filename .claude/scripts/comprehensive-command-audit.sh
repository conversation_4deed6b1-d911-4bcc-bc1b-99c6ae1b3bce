#!/bin/bash

# Comprehensive Command Audit Script
# Systematic search for corrupted command references across PIB-METHOD

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔍 PIB-METHOD Comprehensive Command Audit${NC}"
echo -e "${BLUE}=========================================${NC}"

# Audit Results
TOTAL_ISSUES=0
CRITICAL_ISSUES=0
MINOR_ISSUES=0

# Function to report issue
report_issue() {
    local severity="$1"
    local file="$2"
    local line="$3"
    local issue="$4"
    local context="$5"
    
    if [ "$severity" = "CRITICAL" ]; then
        echo -e "${RED}❌ CRITICAL: $file:$line${NC}"
        CRITICAL_ISSUES=$((CRITICAL_ISSUES + 1))
    else
        echo -e "${YELLOW}⚠️  MINOR: $file:$line${NC}"
        MINOR_ISSUES=$((MINOR_ISSUES + 1))
    fi
    
    echo "   Issue: $issue"
    echo "   Context: $context"
    echo ""
    
    TOTAL_ISSUES=$((TOTAL_ISSUES + 1))
}

echo -e "\n${YELLOW}1. Scanning for corrupted command patterns...${NC}"

# Pattern 1: Arrow-based corrupted commands
echo "🔎 Checking for arrow-based corrupted patterns..."
while IFS= read -r -d '' file; do
    while IFS=: read -r line_num line_content; do
        if [[ $line_content =~ (brief|design|orchestrate|help|knowledge|mode|dev|init)\ -\>\ /[a-z]+: ]]; then
            report_issue "CRITICAL" "$file" "$line_num" "Corrupted arrow command" "$line_content"
        fi
    done < <(grep -n " -> /" "$file" 2>/dev/null || true)
done < <(find .claude -name "*.md" -type f -print0)

# Pattern 2: Malformed namespace commands
echo "🔎 Checking for malformed namespace commands..."
while IFS= read -r -d '' file; do
    while IFS=: read -r line_num line_content; do
        if [[ $line_content =~ /[a-z-]+:[a-z-]+-[a-z-]+-[a-z-]+ ]]; then
            report_issue "CRITICAL" "$file" "$line_num" "Malformed namespace command" "$line_content"
        fi
    done < <(grep -n "/[a-z-]*:[a-z-]*-[a-z-]*-" "$file" 2>/dev/null || true)
done < <(find .claude -name "*.md" -type f -print0)

# Pattern 3: Corrupted file paths
echo "🔎 Checking for corrupted file paths..."
while IFS= read -r -d '' file; do
    while IFS=: read -r line_num line_content; do
        if [[ $line_content =~ pib-agent[a-z-]+\ -\>\ /[a-z]+: ]]; then
            report_issue "CRITICAL" "$file" "$line_num" "Corrupted pib-agent path" "$line_content"
        fi
    done < <(grep -n "pib-agent.*-> /" "$file" 2>/dev/null || true)
done < <(find .claude -name "*.md" -type f -print0)

# Pattern 4: Mixed path corruption
echo "🔎 Checking for mixed path corruption..."
while IFS= read -r -d '' file; do
    while IFS=: read -r line_num line_content; do
        if [[ $line_content =~ docs[a-z-]+\ -\>\ /[a-z]+: ]]; then
            report_issue "CRITICAL" "$file" "$line_num" "Corrupted docs path" "$line_content"
        fi
    done < <(grep -n "docs.*-> /" "$file" 2>/dev/null || true)
done < <(find .claude -name "*.md" -type f -print0)

echo -e "\n${YELLOW}2. Scanning for command definition consistency...${NC}"

# Check for missing command definitions
echo "🔎 Checking command file existence..."
declare -a EXPECTED_COMMANDS=(
    "agents:pm-prd"
    "agents:architect-design"
    "agents:analyst-brief"
    "agents:pm-orchestrate"
    "agents:bill-orchestrate"
    "analysis:analyze"
    "analysis:codereview"
    "analysis:debug"
    "analysis:testgen"
    "workflows:dev-command"
    "workflows:project-init"
    "workflows:module-dev"
    "core:agents"
    "core:tasks"
    "core:help"
)

for cmd in "${EXPECTED_COMMANDS[@]}"; do
    namespace="${cmd%:*}"
    command_name="${cmd#*:}"
    file_path=".claude/commands/$namespace/$command_name.md"
    
    if [ ! -f "$file_path" ]; then
        report_issue "CRITICAL" "$file_path" "0" "Missing command file" "Expected command /$cmd not found"
    else
        # Check if file contains correct usage syntax
        if ! grep -q "/$cmd" "$file_path"; then
            report_issue "MINOR" "$file_path" "?" "Usage syntax missing" "File exists but missing /$cmd usage syntax"
        fi
    fi
done

echo -e "\n${YELLOW}3. Scanning for consistency issues...${NC}"

# Check for inconsistent command references
echo "🔎 Checking for command reference consistency..."
while IFS= read -r -d '' file; do
    while IFS=: read -r line_num line_content; do
        # Look for command patterns
        if [[ $line_content =~ (/[a-z-]+:[a-z-]+) ]]; then
            local cmd="${BASH_REMATCH[1]}"
            local namespace="${cmd%:*}"
            local command_name="${cmd#*:}"
            local expected_file=".claude/commands/${namespace#/}/$command_name.md"
            
            if [ ! -f "$expected_file" ]; then
                report_issue "MINOR" "$file" "$line_num" "Referenced command file missing" "Command $cmd references missing file $expected_file"
            fi
        fi
    done < <(grep -n "/[a-z-]*:[a-z-]*" "$file" 2>/dev/null || true)
done < <(find .claude -name "*.md" -type f -print0)

echo -e "\n${YELLOW}4. Scanning for .bak file references...${NC}"

# Check for .bak file references in content
echo "🔎 Checking for .bak file references in content..."
while IFS= read -r -d '' file; do
    if [[ "$file" != *.bak ]]; then  # Don't check .bak files themselves
        while IFS=: read -r line_num line_content; do
            if [[ $line_content =~ \.bak ]]; then
                report_issue "MINOR" "$file" "$line_num" "Reference to .bak file" "$line_content"
            fi
        done < <(grep -n "\.bak" "$file" 2>/dev/null || true)
    fi
done < <(find .claude -name "*.md" -type f -print0)

echo -e "\n${YELLOW}5. Scanning for workflow integration issues...${NC}"

# Check for workflow state files
echo "🔎 Checking workflow integration..."
if [ ! -f ".claude/current-workflow-state.json" ]; then
    report_issue "MINOR" ".claude/current-workflow-state.json" "0" "Missing workflow state file" "Workflow state tracking may be broken"
fi

if [ ! -f ".claude/current-review-context.json" ]; then
    report_issue "MINOR" ".claude/current-review-context.json" "0" "Missing review context file" "Review context tracking may be broken"
fi

echo -e "\n${YELLOW}6. Checking for hook execution permissions...${NC}"

# Check hook permissions
echo "🔎 Checking hook script permissions..."
for hook_file in .claude/hooks/*.sh; do
    if [ -f "$hook_file" ] && [ ! -x "$hook_file" ]; then
        report_issue "MINOR" "$hook_file" "0" "Hook not executable" "Hook script missing execute permission"
    fi
done

echo -e "\n${BLUE}🔍 AUDIT SUMMARY${NC}"
echo -e "${BLUE}================${NC}"
echo -e "Total Issues Found: $TOTAL_ISSUES"
echo -e "${RED}Critical Issues: $CRITICAL_ISSUES${NC}"
echo -e "${YELLOW}Minor Issues: $MINOR_ISSUES${NC}"

if [ $CRITICAL_ISSUES -eq 0 ]; then
    echo -e "\n${GREEN}✅ No critical command corruption found!${NC}"
    if [ $MINOR_ISSUES -eq 0 ]; then
        echo -e "${GREEN}✅ System appears to be in good health!${NC}"
    else
        echo -e "${YELLOW}ℹ️  Minor issues found - consider cleanup but not blocking${NC}"
    fi
else
    echo -e "\n${RED}❌ Critical issues found that need immediate attention${NC}"
    echo -e "${YELLOW}Run the enhanced fix script to resolve these issues${NC}"
fi

echo -e "\n${BLUE}Audit completed: $(date)${NC}"

# Return appropriate exit code
if [ $CRITICAL_ISSUES -gt 0 ]; then
    exit 1
else
    exit 0
fi