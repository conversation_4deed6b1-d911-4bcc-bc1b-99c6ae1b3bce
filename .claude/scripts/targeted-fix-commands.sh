#!/bin/bash

# Targeted Command Fix Script
# Fixes the most critical corrupted command references

set -eo pipefail

echo "🎯 Targeted Command Corruption Fix"
echo "================================="

# Function to safely fix a file
safe_fix_file() {
    local file="$1"
    
    if [ ! -f "$file" ] || [[ "$file" == *.bak ]]; then
        return 0
    fi
    
    echo "📝 Fixing: $file"
    
    # Use simpler, safer sed commands
    sed -i.safebak \
        -e 's|help -> /quick:workflow-helps:dev-command|/workflows:dev-command|g' \
        -e 's|help -> /quick:workflow-helps:epic-to-stories|/workflows:epic-to-stories|g' \
        -e 's|help -> /quick:workflow-helps:module-dev|/workflows:module-dev|g' \
        -e 's|help -> /quick:workflow-helps:project-init|/workflows:project-init|g' \
        -e 's|help -> /quick:workflow-helps:feature-start|/workflows:feature-start|g' \
        -e 's|help -> /quick:workflow-helps:create-uxui-spec|/workflows:create-uxui-spec|g' \
        -e 's|session -> /planning:plan-session:plan-sessionning-session|/planning:plan-session|g' \
        -e 's|to-stories -> /workflows:epic-to-stories-to-stories|/workflows:epic-to-stories|g' \
        -e 's|knowledge -> /knowledge:update-knowledge-knowledge|/knowledge:update-knowledge|g' \
        -e 's|docsinit -> /workflows:project-init-brief\.md|docs/project-brief.md|g' \
        -e 's|docsdesign -> /agents:architect-designure\.md|docs/architecture.md|g' \
        "$file"
    
    # Remove backup if file was actually changed
    if ! diff "$file" "$file.safebak" >/dev/null 2>&1; then
        echo "  ✓ Fixed command references"
    fi
    rm -f "$file.safebak"
}

# Fix most critical files
echo "🎯 Fixing critical command files..."

# High priority files
critical_files=(
    ".claude/commands/agents/pm-prd.md"
    ".claude/commands/agents/architect-design.md"
    ".claude/commands/agents/analyst-brief.md"
    ".claude/commands/agents/pm-orchestrate.md"
    ".claude/commands/agents/bill-orchestrate.md"
    ".claude/commands/workflows/dev-command.md"
    ".claude/commands/workflows/epic-to-stories.md"
    ".claude/commands/workflows/module-dev.md"
    ".claude/commands/workflows/project-init.md"
    ".claude/commands/planning/plan-session.md"
)

for file in "${critical_files[@]}"; do
    safe_fix_file "$file"
done

echo ""
echo "🔧 Fixing workflow files..."
for workflow_file in .claude/commands/workflows/*.md; do
    safe_fix_file "$workflow_file"
done

echo ""
echo "🔧 Fixing analysis files..."  
for analysis_file in .claude/commands/analysis/*.md; do
    safe_fix_file "$analysis_file"
done

echo ""
echo "✅ Targeted fixes completed!"
echo ""
echo "🧪 Testing critical commands..."

# Test the fixed commands
test_commands=(
    "/agents:pm-prd"
    "/agents:architect-design"
    "/agents:analyst-brief"
    "/workflows:dev-command"
    "/workflows:epic-to-stories"
)

echo ""
for cmd in "${test_commands[@]}"; do
    namespace="${cmd%:*}"
    command_name="${cmd#*:}"
    file_path=".claude/commands/${namespace#/}/$command_name.md"
    
    if [ -f "$file_path" ] && grep -q "$cmd" "$file_path"; then
        echo "✅ $cmd - OK"
    else
        echo "❌ $cmd - STILL BROKEN"
    fi
done

echo ""
echo "🔍 Run audit again to check remaining issues:"
echo "./.claude/scripts/comprehensive-command-audit.sh"