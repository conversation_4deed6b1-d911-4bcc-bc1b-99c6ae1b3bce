#!/bin/bash

# Test Agent Commands Script
# Validates that all agent commands are properly defined and accessible

set -eo pipefail

echo "🧪 Testing Agent Command Definitions..."

# Test commands to verify
declare -a COMMANDS=(
    "/agents:pm-prd"
    "/agents:architect-design"
    "/agents:analyst-brief" 
    "/agents:pm-orchestrate"
    "/agents:bill-orchestrate"
)

echo ""
echo "📋 Testing ${#COMMANDS[@]} agent commands..."

# Function to test if command file exists and is valid
test_command() {
    local cmd="$1"
    local namespace="${cmd%:*}"
    local command_name="${cmd#*:}"
    local file_path=".claude/commands/${namespace#/}/${command_name}.md"
    
    echo -n "Testing $cmd ... "
    
    # Check if file exists
    if [ ! -f "$file_path" ]; then
        echo "❌ File not found: $file_path"
        return 1
    fi
    
    # Check if file contains the correct usage syntax
    if ! grep -q "^$cmd" "$file_path"; then
        echo "❌ Usage syntax not found in $file_path"
        return 1
    fi
    
    # Check for corrupted references
    if grep -q "orchestrate -> /agents:" "$file_path" || \
       grep -q "brief -> /agents:" "$file_path" || \
       grep -q "design -> /agents:" "$file_path"; then
        echo "❌ Corrupted references found in $file_path"
        return 1
    fi
    
    echo "✅"
    return 0
}

# Test all commands
failed_tests=0
passed_tests=0

for cmd in "${COMMANDS[@]}"; do
    if test_command "$cmd"; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
done

echo ""
echo "📊 Test Results:"
echo "✅ Passed: $passed_tests"
echo "❌ Failed: $failed_tests"

if [ $failed_tests -eq 0 ]; then
    echo ""
    echo "🎉 All agent commands are properly defined!"
    echo ""
    echo "You can now use:"
    for cmd in "${COMMANDS[@]}"; do
        echo "  $cmd"
    done
    echo ""
    echo "Try running: orchestrator(Document current architecture)"
else
    echo ""
    echo "❌ Some agent commands need fixing."
    exit 1
fi