# PIB-METHOD Slash Commands Complete Inventory

## Format: /namespace:command

### Agents Commands

- `> /core:agents:analyst-brief` - Analyst research and briefing
- `> /core:agents:architect-design` - Architecture design and review
- `> /core:agents:bill-orchestrate` - Bill orchestration workflow
- `> /core:agents:pm-orchestrate` - Project management orchestration
- `> /core:agents:pm-prd` - Product requirement document generation

### Analysis Commands

- `/analysis:analyze` - General analysis command
- `/analysis:analyze-competitor-ux` - Competitor UX analysis
- `/analysis:codereview` - Code review workflow
- `/analysis:compare-competitor-features` - Feature comparison
- `/analysis:debug` - Debug assistance
- `/analysis:docgen` - Documentation generation
- `/analysis:precommit` - Pre-commit checks
- `/analysis:refactor` - Refactoring assistance
- `/analysis:secaudit` - Security audit
- `/analysis:testgen` - Test generation
- `/analysis:thinkdeep` - Deep thinking analysis

### Core Commands

- `/core:agents` - List available agents
- `/core:enable-lever-gates` - Enable LEVER quality gates
- `/core:help` - Help system
- `/core:lever-check` - Check LEVER compliance
- `/core:switch-agent` - Switch between agents
- `/core:tasks` - Task management
- `/core:what-next` - Workflow suggestions

### Credentials Commands

- `/credentials:delete-credentials` - Delete saved credentials
- `/credentials:list-credentials` - List saved credentials
- `/credentials:save-credentials` - Save credentials
- `/credentials:test-auth` - Test authentication

### GitHub Commands

- `/github:ai-assign` - Assign AI to issues/PRs
- `/github:ai-issue` - Create AI-assigned issues
- `/github:ai-pr` - AI pull request review
- `/github:ai-status` - Check AI work status
- `/github:setup-claude-actions` - Setup Claude GitHub Actions

### Knowledge Commands

- `/knowledge:memory-extract` - Extract memory/knowledge
- `/knowledge:update-knowledge` - Update knowledge base

### Orchestration Commands

- `/orchestration:dev-orchestrator` - Development orchestration

### Planning Commands

- `session -> session -> help -> /quick:workflow-helps:planning-session:plan-sessionning:clarify-requirements` -
  Clarify requirements
- `session -> session -> help -> /quick:workflow-helps:planning-session:plan-sessionning:confirm-approach` -
  Confirm approach
- `session -> session -> help -> /quick:workflow-helps:planning-session:plan-sessionning:plan-session` -
  Start planning session

### Quick Commands

- `/quick:check-mode` - Check current mode
- `/quick:dev-mode` - Development mode
- `/quick:doc-out` - Documentation output
- `/quick:prompt-enhancer` - Enhance prompts
- `/quick:test-knowledge-update` - Test knowledge update
- `/quick:workflow-help` - Workflow help

### Sessions Commands

- `start -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-starts:session-continue` -
  Continue session
- `start -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-starts:session-current` -
  Current session info
- `start -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-starts:session-end` -
  End session
- `start -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-starts:session-help` -
  Session help
- `start -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-starts:session-list` -
  List sessions
- `start -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-starts:session-report` -
  Session report
- `start -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-starts:session-start` -
  Start session
- `start -> end -> list -> current -> report -> continue -> help -> updates -> /sessions:session-updatess:session-helps:session-continues:session-reports:session-currents:session-lists:session-ends:session-starts:session-updates` -
  Session updates

### Template Commands

- `/template:command-skeleton` - Command template

### Testing Commands

- `knowledge-update -> /quick:test-knowledge-updateing:delete-workflow` - Delete
  test workflow
- `knowledge-update -> /quick:test-knowledge-updateing:discover-routes` -
  Discover routes
- `knowledge-update -> /quick:test-knowledge-updateing:export-workflow` - Export
  workflow
- `knowledge-update -> /quick:test-knowledge-updateing:headless-mode` - Headless
  mode
- `knowledge-update -> /quick:test-knowledge-updateing:import-workflow` - Import
  workflow
- `knowledge-update -> /quick:test-knowledge-updateing:list-workflows` - List
  workflows
- `knowledge-update -> /quick:test-knowledge-updateing:record-workflow` - Record
  workflow
- `knowledge-update -> /quick:test-knowledge-updateing:run-workflow` - Run
  workflow
- `knowledge-update -> /quick:test-knowledge-updateing:save-workflow` - Save
  workflow
- `knowledge-update -> /quick:test-knowledge-updateing:test-accessibility` -
  Accessibility testing
- `knowledge-update -> /quick:test-knowledge-updateing:test-config` - Test
  configuration
- `knowledge-update -> /quick:test-knowledge-updateing:test-headless` - Headless
  testing
- `knowledge-update -> /quick:test-knowledge-updateing:test-login` - Login
  testing
- `knowledge-update -> /quick:test-knowledge-updateing:test-mobile` - Mobile
  testing
- `knowledge-update -> /quick:test-knowledge-updateing:test-performance` -
  Performance testing
- `knowledge-update -> /quick:test-knowledge-updateing:test-port` - Port testing
- `knowledge-update -> /quick:test-knowledge-updateing:test-register` -
  Registration testing
- `knowledge-update -> /quick:test-knowledge-updateing:test-visible` - Visible
  testing
- `knowledge-update -> /quick:test-knowledge-updateing:test-visual-regression` -
  Visual regression testing
- `knowledge-update -> /quick:test-knowledge-updateing:update-workflow` - Update
  workflow
- `knowledge-update -> /quick:test-knowledge-updateing:visible-mode` - Visible
  mode
- `knowledge-update -> /quick:test-knowledge-updateing:workflow-status` -
  Workflow status

### Workflows Commands

- `help -> /quick:workflow-helps:correct-course` - Course correction
- `help -> /quick:workflow-helps:create-deployment-plan` - Create deployment
  plan
- `help -> /quick:workflow-helps:create-enhancement-prd` - Create enhancement
  PRD
- `help -> /quick:workflow-helps:create-infrastructure-architecture` - Create
  infrastructure architecture
- `help -> /quick:workflow-helps:create-uxui-spec` - Create UX/UI specification
- `help -> /quick:workflow-helps:dev-command` - Development command
- `help -> /quick:workflow-helps:epic-to-stories` - Convert epic to stories
- `help -> /quick:workflow-helps:feature-cleanup` - Feature cleanup
- `help -> /quick:workflow-helps:feature-list` - List features
- `help -> /quick:workflow-helps:feature-merge` - Merge feature
- `help -> /quick:workflow-helps:feature-pr` - Feature pull request
- `help -> /quick:workflow-helps:feature-start` - Start feature
- `help -> /quick:workflow-helps:legacy-fix` - Legacy fix workflow
- `help -> /quick:workflow-helps:lever-workflow` - LEVER workflow
- `help -> /quick:workflow-helps:module-dev` - Module development
- `help -> /quick:workflow-helps:planning-session` - Planning session (duplicate
  of session -> session -> help ->
  /quick:workflow-helps:planning-session:plan-sessionning:plan-session)
- `help -> /quick:workflow-helps:project-init` - Initialize project
- `help -> /quick:workflow-helps:review-infrastructure` - Review infrastructure
- `help -> /quick:workflow-helps:spawn-subagents` - Spawn subagents
- `help -> /quick:workflow-helps:sub-agent-coordination` - Sub-agent
  coordination
- `help -> /quick:workflow-helps:switch-mode` - Switch mode
- `help -> /quick:workflow-helps:validate-infrastructure` - Validate
  infrastructure

### Standalone Commands (need namespace)

- `task -> /core:ultrathink-task-task` - Should be `/core:ultrathink-task`
- `> /analysis:review` - Should be `/analysis:review`
