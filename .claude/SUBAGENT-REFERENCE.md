# PIB-METHOD Subagent Reference Guide

## Available Subagent Types (for Task tool)

### Core Subagents
These are the built-in `subagent_type` values available in all projects:
- `general-purpose` - General tasks and research
- `task-executor` - Specific task implementation
- `dev-agent` - Development and coding tasks
- `change-implementer` - Implementing code review feedback
- `platform-engineer` - Infrastructure and platform tasks
- `analyst` - Research and analysis tasks
- `architect` - System architecture AND UI/UX design
- `qa-tester` - Testing and quality assurance
- `orchestrator` - Workflow orchestration
- `code-reviewer` - Code review and quality checks

### Project-Specific Subagents
Projects can register custom agents in `.claude/project-agents.json`. See `PROJECT-AGENTS.md` for details.

Example custom agents:
- `fabric-specialist` - Fabric.js canvas expert (if registered)
- `three-js-expert` - Three.js 3D graphics specialist (if registered)
- `your-custom-agent` - Any agent you create and register

## Common Mistakes

### ❌ INCORRECT - These agent types DO NOT exist:
- `design-architect` - Use `architect` instead
- `devops` - Use `platform-engineer` instead
- `sm` or `scrum-master` - Not available as subagent
- `pm` or `product-manager` - Not available as subagent
- `po` or `product-owner` - Not available as subagent
- `data-scientist` - Not available as subagent

### ✅ CORRECT Usage Examples:

**For UI/UX Design Tasks:**
```javascript
Task({
  description: "Create UX/UI specification",
  prompt: "Design the user interface...",
  subagent_type: "architect"  // NOT "design-architect"
})
```

**For Infrastructure Tasks:**
```javascript
Task({
  description: "Set up deployment pipeline",
  prompt: "Configure CI/CD...",
  subagent_type: "platform-engineer"  // NOT "devops"
})
```

**For Analysis Tasks:**
```javascript
Task({
  description: "Analyze competitor features",
  prompt: "Research and analyze...",
  subagent_type: "analyst"
})
```

## Subagent Selection Guide

| Task Type | Use Subagent | Why |
|-----------|--------------|-----|
| UI/UX Design | `architect` | Handles both system and UI architecture |
| Frontend Development | `dev-agent` | General development tasks |
| Backend Development | `dev-agent` | General development tasks |
| Code Reviews | `code-reviewer` | Specialized for reviews |
| Bug Fixes | `change-implementer` | Implements specific changes |
| Research | `analyst` | Research and analysis |
| Testing | `qa-tester` | QA and testing tasks |
| Infrastructure | `platform-engineer` | Platform and DevOps |
| Complex Workflows | `orchestrator` | Multi-step coordination |
| General Tasks | `general-purpose` | Fallback for unclear tasks |

## Integration with Commands

When commands need to invoke subagents, they should use the correct types:
- `help -> /quick:workflow-helps:create-uxui-spec` → Uses `architect` subagent
- `> /core:agents:architect-design` → Uses `architect` for all design
- `/analysis:analyze` → Uses `analyst` subagent
- `knowledge-update -> /quick:test-knowledge-updateing:*` commands → Use `qa-tester` subagent

## Project Agent Registration

To use custom agents with the Task tool:
1. Create your agent using `/workflows:create-tech-agent`
2. Register it in `.claude/project-agents.json`
3. Use it with `subagent_type: "your-agent-name"`

See `PROJECT-AGENTS.md` for detailed instructions.