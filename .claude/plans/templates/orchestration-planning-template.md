# Orchestration Plan: {EPIC_NAME}

**Date Created**: {CURRENT_DATE}  
**Planning Session**: {PLANNING_SESSION_ID}  
**Epic Reference**: {EPIC_FILE_PATH}  
**Stories Source**: {STORY_GENERATION_FILE}

## Orchestration Overview

### Epic Scope

- **Epic Name**: {EPIC_NAME}
- **Total Stories**: {TOTAL_STORY_COUNT}
- **Estimated Duration**: {TOTAL_ESTIMATED_DURATION}
- **Agent Coordination Strategy**: {COORDINATION_STRATEGY}

### Success Metrics

- **Story Completion Rate**: Target 100%
- **Quality Gate Compliance**: Target 100%
- **Timeline Adherence**: Target ±10%
- **LEVER Framework Score**: Target 4/5 minimum

## Multi-Agent Coordination Strategy

### Sub-Agent Definitions

#### Frontend Sub-Agent

- **Primary Agent**: <PERSON> (Full Stack Engineer) - Frontend Focus
- **Assigned Stories**: {FRONTEND_STORY_LIST}
- **Reviewer**: UI-Reviewer
- **Changer**: UI-Changer
- **Estimated Duration**: {FRONTEND_DURATION}

#### Backend Sub-Agent

- **Primary Agent**: James (Full Stack Engineer) - Backend Focus
- **Assigned Stories**: {BACKEND_STORY_LIST}
- **Reviewer**: Code-Reviewer
- **Changer**: Code-Changer
- **Estimated Duration**: {BACKEND_DURATION}

#### DevOps Sub-Agent

- **Primary Agent**: James (Full Stack Engineer) - Infrastructure Focus
- **Assigned Stories**: {DEVOPS_STORY_LIST}
- **Reviewer**: Infrastructure-Reviewer
- **Changer**: Infrastructure-Changer
- **Estimated Duration**: {DEVOPS_DURATION}

#### Testing Sub-Agent

- **Primary Agent**: James (Full Stack Engineer) - Testing Focus
- **Assigned Stories**: {TESTING_STORY_LIST}
- **Reviewer**: QA-Reviewer
- **Changer**: Test-Changer
- **Estimated Duration**: {TESTING_DURATION}

## Execution Matrix

### Phase 1: Foundation Stories (Sequential)

| Order | Story                | Sub-Agent   | Dependencies | Est. Duration | Status     |
| ----- | -------------------- | ----------- | ------------ | ------------- | ---------- |
| 1     | {FOUNDATION_STORY_1} | {SUB_AGENT} | None         | {DURATION}    | ⏳ Pending |
| 2     | {FOUNDATION_STORY_2} | {SUB_AGENT} | Story 1      | {DURATION}    | ⏳ Pending |

### Phase 2: Core Features (Parallel)

| Group | Story          | Sub-Agent | Dependencies        | Est. Duration | Status     |
| ----- | -------------- | --------- | ------------------- | ------------- | ---------- |
| A1    | {CORE_STORY_1} | Frontend  | Foundation Complete | {DURATION}    | ⏳ Pending |
| A2    | {CORE_STORY_2} | Backend   | Foundation Complete | {DURATION}    | ⏳ Pending |
| B1    | {CORE_STORY_3} | DevOps    | Foundation Complete | {DURATION}    | ⏳ Pending |
| B2    | {CORE_STORY_4} | Testing   | A1, A2 Complete     | {DURATION}    | ⏳ Pending |

### Phase 3: Integration & Polish (Sequential)

| Order | Story                 | Sub-Agent   | Dependencies         | Est. Duration | Status     |
| ----- | --------------------- | ----------- | -------------------- | ------------- | ---------- |
| 1     | {INTEGRATION_STORY_1} | Multi-Agent | Phase 2 Complete     | {DURATION}    | ⏳ Pending |
| 2     | {POLISH_STORY_1}      | {SUB_AGENT} | Integration Complete | {DURATION}    | ⏳ Pending |

## Coordination Protocols

### Daily Standup Structure

**Bill (PM/Orchestrator) Daily Report:**

- **Yesterday**: {COMPLETED_TASKS}
- **Today**: {PLANNED_ASSIGNMENTS}
- **Blockers**: {IDENTIFIED_BLOCKERS}
- **Sub-Agent Status**: {SUB_AGENT_STATUS_SUMMARY}

### Agent Communication Framework

- **Real-time Coordination**: Direct sub-agent communication for immediate needs
- **Progress Updates**: Sub-agents report completion to Bill for orchestration
  updates
- **Dependency Coordination**: Proactive communication when dependencies are
  ready
- **Blocker Escalation**: Immediate escalation to appropriate resolver

### Quality Gate Management

- **Per-Story Gates**: Dev → Reviewer → Changer → Approval
- **Phase Gates**: All phase stories must complete before next phase
- **Epic Gates**: Final integration and acceptance testing

## Resource Management

### Shared Resource Coordination

- **Shared Databases**: {DATABASE_COORDINATION_STRATEGY}
- **Shared APIs**: {API_COORDINATION_STRATEGY}
- **Shared Environments**: {ENVIRONMENT_COORDINATION_STRATEGY}
- **Shared Dependencies**: {DEPENDENCY_COORDINATION_STRATEGY}

### Parallel Execution Limits

- **Maximum Concurrent Sub-Agents**: 3 (optimal for quality)
- **James Time Allocation**: {TIME_ALLOCATION_STRATEGY}
- **Reviewer Scheduling**: {REVIEWER_SCHEDULING_STRATEGY}
- **Integration Synchronization**: {INTEGRATION_SYNC_STRATEGY}

## Risk Management & Mitigation

### Identified Risks

1. **Risk**: {RISK_DESCRIPTION}
   - **Impact**: {RISK_IMPACT}
   - **Probability**: {RISK_PROBABILITY}
   - **Mitigation**: {MITIGATION_STRATEGY}
   - **Owner**: {RISK_OWNER}

2. **Risk**: {DEPENDENCY_BOTTLENECK}
   - **Impact**: {DEPENDENCY_IMPACT}
   - **Probability**: {DEPENDENCY_PROBABILITY}
   - **Mitigation**: {DEPENDENCY_MITIGATION}
   - **Owner**: {DEPENDENCY_OWNER}

### Contingency Plans

- **Critical Path Delays**: {CRITICAL_PATH_CONTINGENCY}
- **Resource Conflicts**: {RESOURCE_CONFLICT_RESOLUTION}
- **Quality Issues**: {QUALITY_ISSUE_RESPONSE}
- **Integration Failures**: {INTEGRATION_FAILURE_RESPONSE}

## Context Preservation Strategy

### Cross-Session Context Bridge

- **Epic Vision Preservation**: {EPIC_VISION_CONTEXT}
- **Architecture Decision Log**: {ARCHITECTURE_DECISIONS}
- **Story Relationship Map**: {STORY_RELATIONSHIP_CONTEXT}
- **Agent Assignment Rationale**: {ASSIGNMENT_RATIONALE}

### Planning Artifact Integration

- **Planning Session Reference**: {PLANNING_SESSION_FILE}
- **Story Generation Results**: {STORY_GENERATION_FILE}
- **Context Bridge Document**: {CONTEXT_BRIDGE_FILE}
- **Orchestration State Tracking**: {STATE_TRACKING_SYSTEM}

## Success Criteria & Validation

### Epic Completion Criteria

- [ ] All stories completed and approved
- [ ] Integration testing passed
- [ ] LEVER framework compliance validated
- [ ] Performance requirements met
- [ ] Security requirements validated
- [ ] Documentation completed and updated

### Quality Metrics Targets

- **Code Coverage**: {TARGET_COVERAGE}%
- **LEVER Compliance Score**: {TARGET_LEVER_SCORE}/5
- **Review Approval Rate**: {TARGET_APPROVAL_RATE}%
- **Defect Escape Rate**: <{TARGET_DEFECT_RATE}%

## Timeline & Milestones

### Key Milestones

- **Foundation Complete**: {FOUNDATION_MILESTONE_DATE}
- **Core Features Complete**: {CORE_FEATURES_MILESTONE_DATE}
- **Integration Complete**: {INTEGRATION_MILESTONE_DATE}
- **Epic Complete**: {EPIC_COMPLETION_DATE}

### Progress Tracking

- **Daily Progress Reviews**: Bill updates orchestration status
- **Weekly Milestone Reviews**: Cross-functional validation
- **Phase Gate Reviews**: Formal approval before next phase
- **Epic Retrospective**: Lessons learned and process improvement

---

**Orchestration Plan Status**: {DRAFT|ACTIVE|COMPLETED}  
**Current Phase**: {CURRENT_PHASE}  
**Next Review Date**: {NEXT_REVIEW_DATE}  
**Generated by**: PIB-METHOD Orchestration Planning System
