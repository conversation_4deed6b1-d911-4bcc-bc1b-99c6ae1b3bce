# Context Bridge: {EPIC_NAME}

**Created**: {CURRENT_DATE}  
**Epic Reference**: {EPIC_FILE_PATH}  
**Session ID**: {SESSION_ID}  
**Bridge Type**: Cross-Session Continuity

## Purpose

This document preserves critical planning context across chat sessions, ensuring
that agents can seamlessly continue work on the epic even when conversations
restart.

## Epic Context Preservation

### Core Epic Vision

**Epic Goal**: {EPIC_GOAL}

**Business Value**: {BUSINESS_VALUE_STATEMENT}

**Success Definition**: {SUCCESS_DEFINITION}

### Key Architectural Decisions

1. **Decision**: {ARCHITECTURE_DECISION_1}
   - **Rationale**: {DECISION_RATIONALE_1}
   - **Impact**: {DECISION_IMPACT_1}
   - **Alternatives Considered**: {ALTERNATIVES_1}

2. **Decision**: {ARCHITECTURE_DECISION_2}
   - **Rationale**: {DECISION_RATIONALE_2}
   - **Impact**: {DECISION_IMPACT_2}
   - **Alternatives Considered**: {ALTERNATIVES_2}

### Technical Context

- **Technology Stack**: {TECHNOLOGY_STACK}
- **Architecture Pattern**: {ARCHITECTURE_PATTERN}
- **Integration Requirements**: {INTEGRATION_REQUIREMENTS}
- **Performance Requirements**: {PERFORMANCE_REQUIREMENTS}
- **Security Requirements**: {SECURITY_REQUIREMENTS}

## Story Relationship Map

### Story Dependencies

```mermaid
graph TD
    A[{FOUNDATION_STORY}] --> B[{CORE_STORY_1}]
    A --> C[{CORE_STORY_2}]
    B --> D[{INTEGRATION_STORY}]
    C --> D
    D --> E[{POLISH_STORY}]
```

### Sub-Agent Assignment Context

- **Frontend Stories**: {FRONTEND_STORY_ASSIGNMENTS}
  - **Rationale**: {FRONTEND_ASSIGNMENT_RATIONALE}
  - **Dependencies**: {FRONTEND_DEPENDENCIES}

- **Backend Stories**: {BACKEND_STORY_ASSIGNMENTS}
  - **Rationale**: {BACKEND_ASSIGNMENT_RATIONALE}
  - **Dependencies**: {BACKEND_DEPENDENCIES}

- **DevOps Stories**: {DEVOPS_STORY_ASSIGNMENTS}
  - **Rationale**: {DEVOPS_ASSIGNMENT_RATIONALE}
  - **Dependencies**: {DEVOPS_DEPENDENCIES}

- **Testing Stories**: {TESTING_STORY_ASSIGNMENTS}
  - **Rationale**: {TESTING_ASSIGNMENT_RATIONALE}
  - **Dependencies**: {TESTING_DEPENDENCIES}

## Implementation Context

### LEVER Framework Application

- **Leverage**: {LEVERAGE_STRATEGY}
  - **Existing Components**: {EXISTING_COMPONENTS_TO_REUSE}
  - **Patterns to Follow**: {PATTERNS_TO_FOLLOW}

- **Extend**: {EXTENSION_STRATEGY}
  - **Components to Extend**: {COMPONENTS_TO_EXTEND}
  - **Extension Points**: {EXTENSION_POINTS}

- **Verify**: {VERIFICATION_STRATEGY}
  - **Validation Methods**: {VALIDATION_METHODS}
  - **Testing Strategy**: {TESTING_STRATEGY}

- **Eliminate**: {ELIMINATION_STRATEGY}
  - **Duplication to Remove**: {DUPLICATION_TO_REMOVE}
  - **Cleanup Tasks**: {CLEANUP_TASKS}

- **Reduce**: {REDUCTION_STRATEGY}
  - **Complexity Reduction**: {COMPLEXITY_REDUCTION_AREAS}
  - **Simplification Opportunities**: {SIMPLIFICATION_OPPORTUNITIES}

### MCP Tool Strategy

- **Zen MCP Usage**: {ZEN_MCP_STRATEGY}
  - **Analysis Needs**: {ZEN_ANALYSIS_REQUIREMENTS}
  - **Validation Requirements**: {ZEN_VALIDATION_REQUIREMENTS}

- **Context7 MCP Usage**: {CONTEXT7_STRATEGY}
  - **Research Areas**: {CONTEXT7_RESEARCH_AREAS}
  - **Documentation Needs**: {CONTEXT7_DOCUMENTATION_NEEDS}

## Orchestration Context

### Current Status

- **Planning Phase**: {CURRENT_PLANNING_PHASE}
- **Stories Generated**: {STORIES_GENERATED_COUNT}
- **Orchestration Plan**: {ORCHESTRATION_PLAN_STATUS}
- **Implementation Phase**: {IMPLEMENTATION_PHASE_STATUS}

### Agent Workflow State

- **Primary Agent**: James (Full Stack Engineer)
- **Active Sub-Agents**: {ACTIVE_SUB_AGENTS}
- **Current Focus**: {CURRENT_FOCUS_AREA}
- **Next Assignment**: {NEXT_AGENT_ASSIGNMENT}

### Quality Gates

- **Completed Gates**: {COMPLETED_QUALITY_GATES}
- **Current Gate**: {CURRENT_QUALITY_GATE}
- **Upcoming Gates**: {UPCOMING_QUALITY_GATES}

## Cross-Session Instructions

### For Planning Continuation

When resuming planning in a new chat session:

1. **Review Epic Context**: Read epic vision and architectural decisions
2. **Check Story Status**: Validate current story completion state
3. **Update Planning**: Continue from current planning phase
4. **Preserve Context**: Update this bridge document with new decisions

### For Development Continuation

When resuming development in a new chat session:

1. **Load Epic Context**: Understand overall epic goals and constraints
2. **Review Assignment**: Check current sub-agent assignments and priorities
3. **Check Dependencies**: Validate dependency status before proceeding
4. **Apply LEVER**: Ensure LEVER framework compliance in implementation

### For Review Continuation

When resuming review in a new chat session:

1. **Context Loading**: Understand epic scope and quality standards
2. **Review Criteria**: Apply epic-specific review criteria
3. **Integration Focus**: Consider integration points and dependencies
4. **Quality Validation**: Ensure LEVER compliance and architectural alignment

## Planning Artifacts Reference

### Generated Documents

- **Planning Session**: `{PLANNING_SESSION_FILE}`
- **Story Generation**: `{STORY_GENERATION_FILE}`
- **Orchestration Plan**: `{ORCHESTRATION_PLAN_FILE}`
- **Story Files**: {STORY_FILES_LIST}

### State Tracking

- **Planning Index Entry**: {PLANNING_INDEX_REFERENCE}
- **Workflow State**: {WORKFLOW_STATE_REFERENCE}
- **Context Evolution**: {CONTEXT_EVOLUTION_REFERENCE}

## Knowledge Base Integration

### Key Learnings Captured

1. **Learning**: {KEY_LEARNING_1}
   - **Context**: {LEARNING_CONTEXT_1}
   - **Application**: {LEARNING_APPLICATION_1}

2. **Learning**: {KEY_LEARNING_2}
   - **Context**: {LEARNING_CONTEXT_2}
   - **Application**: {LEARNING_APPLICATION_2}

### Pattern Recognition

- **Successful Patterns**: {SUCCESSFUL_PATTERNS_IDENTIFIED}
- **Anti-Patterns Avoided**: {ANTI_PATTERNS_AVOIDED}
- **Optimization Opportunities**: {OPTIMIZATION_OPPORTUNITIES}

## Emergency Context Recovery

### Critical Information for Recovery

If this epic needs to be resumed with minimal context:

1. **Essential Goal**: {ESSENTIAL_EPIC_GOAL}
2. **Core Architecture**: {CORE_ARCHITECTURE_DECISION}
3. **Current Story**: {CURRENT_ACTIVE_STORY}
4. **Next Action**: {NEXT_CRITICAL_ACTION}

### Quick Start Commands

```bash
# Resume planning session
session -> session -> help -> /quick:workflow-helps:planning-session:plan-sessionning-session {EPIC_NAME} --resume

# Continue story development
help -> /quick:workflow-helps:dev-command "{CURRENT_STORY_CONTEXT}"

# Review orchestration status
/orchestration-plan {EPIC_NAME} --status
```

---

**Bridge Status**: {ACTIVE|ARCHIVED|SUPERSEDED}  
**Last Updated**: {LAST_UPDATE_DATE}  
**Update Trigger**: {UPDATE_TRIGGER}  
**Generated by**: PIB-METHOD Context Bridge System
