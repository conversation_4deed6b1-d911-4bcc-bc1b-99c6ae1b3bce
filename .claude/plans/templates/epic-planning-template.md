# EPIC Planning Session: {EPIC_NAME}

**Date Created**: {CURRENT_DATE}  
**Planning Session ID**: {SESSION_ID}  
**Epic Source**: {EPIC_FILE_PATH}

## Epic Overview

### Epic Goal

{EPIC_GOAL_DESCRIPTION}

### Success Criteria

- [ ] {SUCCESS_CRITERIA_1}
- [ ] {SUCCESS_CRITERIA_2}
- [ ] {SUCCESS_CRITERIA_3}

### Complexity Assessment

- **Technical Complexity**: {LOW|MEDIUM|HIGH|EXPERT}
- **Business Impact**: {LOW|MEDIUM|HIGH|CRITICAL}
- **Implementation Time**: {DAYS|WEEKS|MONTHS}

## Story Breakdown Strategy

### Core Stories Identified

1. **{STORY_1_NAME}** - {BRIEF_DESCRIPTION}
   - Priority: {HIGH|MEDIUM|LOW}
   - Complexity: {SIMPLE|MEDIUM|COMPLEX}
   - Dependencies: {NONE|LIST_DEPENDENCIES}

2. **{STORY_2_NAME}** - {BRIEF_DESCRIPTION}
   - Priority: {HIGH|MEDIUM|LOW}
   - Complexity: {SIMPLE|MEDIUM|COMPLEX}
   - Dependencies: {NONE|LIST_DEPENDENCIES}

3. **{STORY_3_NAME}** - {BRIEF_DESCRIPTION}
   - Priority: {HIGH|MEDIUM|LOW}
   - Complexity: {SIMPLE|MEDIUM|COMPLEX}
   - Dependencies: {NONE|LIST_DEPENDENCIES}

### Story Sequencing Strategy

- **Phase 1 (Foundation)**: {FOUNDATIONAL_STORIES}
- **Phase 2 (Core Features)**: {CORE_FEATURE_STORIES}
- **Phase 3 (Enhancement)**: {ENHANCEMENT_STORIES}

## Sub-Agent Coordination Plan

### Parallel Execution Opportunities

- **Frontend Sub-Agent**: {FRONTEND_STORIES}
- **Backend Sub-Agent**: {BACKEND_STORIES}
- **DevOps Sub-Agent**: {INFRASTRUCTURE_STORIES}
- **Testing Sub-Agent**: {TESTING_STORIES}

### Sequential Dependencies

- {STORY_A} must complete before {STORY_B}
- {STORY_C} depends on {EXTERNAL_DEPENDENCY}

### Integration Points

- **API Contracts**: {API_INTEGRATION_REQUIREMENTS}
- **Database Changes**: {DATABASE_SCHEMA_CHANGES}
- **Shared Components**: {SHARED_COMPONENT_DEPENDENCIES}

## Technical Architecture Considerations

### LEVER Framework Analysis

- **Leverage**: {EXISTING_PATTERNS_TO_REUSE}
- **Extend**: {EXISTING_FUNCTIONALITY_TO_EXTEND}
- **Verify**: {VALIDATION_STRATEGIES}
- **Eliminate**: {DUPLICATION_TO_REMOVE}
- **Reduce**: {COMPLEXITY_REDUCTION_OPPORTUNITIES}

### MCP Tool Requirements

- **Zen MCP Usage**: {ZEN_TOOL_REQUIREMENTS}
- **Context7 MCP Usage**: {CONTEXT7_RESEARCH_NEEDS}
- **Other MCP Tools**: {ADDITIONAL_TOOL_REQUIREMENTS}

## Risk Assessment

### Technical Risks

- **Risk 1**: {RISK_DESCRIPTION} - Mitigation: {MITIGATION_STRATEGY}
- **Risk 2**: {RISK_DESCRIPTION} - Mitigation: {MITIGATION_STRATEGY}

### Integration Risks

- **Risk 1**: {INTEGRATION_RISK} - Mitigation: {MITIGATION_STRATEGY}
- **Risk 2**: {INTEGRATION_RISK} - Mitigation: {MITIGATION_STRATEGY}

### Timeline Risks

- **Risk 1**: {TIMELINE_RISK} - Mitigation: {MITIGATION_STRATEGY}
- **Risk 2**: {TIMELINE_RISK} - Mitigation: {MITIGATION_STRATEGY}

## Implementation Timeline

### Estimated Duration

- **Total Epic Duration**: {TOTAL_ESTIMATED_TIME}
- **Story Development**: {DEVELOPMENT_TIME}
- **Integration & Testing**: {INTEGRATION_TIME}
- **Review & Refinement**: {REVIEW_TIME}

### Milestone Schedule

- **Milestone 1**: {MILESTONE_DESCRIPTION} - {TARGET_DATE}
- **Milestone 2**: {MILESTONE_DESCRIPTION} - {TARGET_DATE}
- **Milestone 3**: {MILESTONE_DESCRIPTION} - {TARGET_DATE}

## Quality Gates

### Development Quality

- [ ] LEVER framework compliance validated
- [ ] Code review completed for each story
- [ ] Unit tests written and passing
- [ ] Integration tests completed

### Epic Quality

- [ ] All stories completed and approved
- [ ] End-to-end testing completed
- [ ] User acceptance criteria met
- [ ] Documentation updated

## Context Bridge for Cross-Session Continuity

### Key Context to Preserve

- **Epic Vision**: {CORE_EPIC_VISION}
- **Architecture Decisions**: {KEY_ARCHITECTURAL_DECISIONS}
- **Story Relationships**: {STORY_DEPENDENCY_MAP}
- **Agent Assignments**: {SUB_AGENT_ASSIGNMENT_STRATEGY}

### Planning Artifacts Created

- [ ] Epic breakdown document
- [ ] Story files generated
- [ ] Orchestration plan created
- [ ] Context bridge established

---

**Planning Session Status**: {DRAFT|IN_PROGRESS|COMPLETED}  
**Next Action**: {NEXT_PLANNING_ACTION}  
**Generated by**: PIB-METHOD Planning System
