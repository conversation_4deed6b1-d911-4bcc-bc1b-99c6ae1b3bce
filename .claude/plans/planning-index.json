{"version": "1.0", "created": "2025-07-08T12:00:00Z", "last_updated": "2025-07-08T12:00:00Z", "epics": {}, "active_sessions": {}, "completed_epics": {}, "schema": {"epic_entry": {"epic_id": "string", "epic_name": "string", "epic_file": "string", "planning_session_file": "string", "story_generation_file": "string", "orchestration_plan_file": "string", "context_bridge_file": "string", "status": "planning|story_generation|orchestration|implementation|completed|archived", "created_date": "ISO_8601_date", "stories": [{"story_id": "string", "story_file": "string", "story_title": "string", "priority": "high|medium|low", "sub_agent": "frontend|backend|devops|testing", "status": "pending|in_progress|review|completed", "dependencies": ["story_id_list"], "estimated_duration": "string"}], "dependencies": ["epic_id_list"], "sub_agent_assignments": {"frontend": ["story_id_list"], "backend": ["story_id_list"], "devops": ["story_id_list"], "testing": ["story_id_list"]}, "quality_metrics": {"lever_compliance_score": "number_0_to_5", "story_completion_rate": "percentage", "quality_gate_pass_rate": "percentage"}}}}