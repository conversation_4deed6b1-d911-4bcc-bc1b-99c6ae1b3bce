# PIB-METHOD Project Initialization Guide

## Quick Answers to Your Questions

### 1. Does project-init include specialized agents?

**YES!** The standard `/workflows:project-init` command includes technology
detection and specialized agent creation in Phase 0. It automatically:

- Detects your technology stack
- Creates missing specialized agents
- Sets up routing rules in CLAUDE.md
- Configures agent assignments

### 2. For existing projects (takeovers), is project-init the first step?

**YES, but with modifications.** Use project-init but skip certain phases:

```bash
# For existing projects with code
/workflows:project-init --existing-project

# Or manually run just the parts you need:
/workflows:project-init-tech --research --create-missing
```

### 3. Will complete documentation be created?

**YES!** Project-init creates comprehensive documentation:

- Project brief
- PRD (Product Requirements Document)
- Architecture documentation
- Frontend/backend architecture
- UX/UI specifications
- Technology stack documentation
- Agent routing configuration
- Implementation stories
- Orchestration guide

## Detailed Workflows

### For Brand New Projects (Starting from Scratch)

Run the complete initialization:

```bash
/workflows:project-init
```

This creates:

1. **Technology Detection** (Phase 0)
   - Scans for tech stack
   - Creates specialized agents
   - Configures routing

2. **Complete Documentation** (Phase 1)
   - Project brief
   - PRD with epics
   - System architecture
   - Frontend architecture
   - UX/UI specifications

3. **Implementation Setup** (Phase 2-4)
   - Knowledge distribution
   - Story creation
   - Orchestration guide
   - Development kickoff

### For Existing Projects (Takeovers)

#### Option 1: Modified Project Init

```bash
# This will adapt to existing code
/workflows:project-init --existing-project
```

Features:

- Skips project brief creation
- Analyzes existing code for requirements
- Detects current tech stack
- Creates missing documentation
- Generates specialized agents

#### Option 2: Tech Detection Only

```bash
# Just detect stack and create agents
/workflows:project-init-tech --research --create-missing
```

This is perfect when you:

- Already have some documentation
- Just need tech specialists
- Want minimal disruption

#### Option 3: Custom Workflow

For maximum control, run phases individually:

```bash
# 1. Tech detection and agents
/workflows:project-init-tech --research --create-missing

# 2. Analyze existing code for PRD
/agents:analyst-brief --from-existing-code

# 3. Create architecture from current state
/agents:architect-design --reverse-engineer

# 4. Create stories for improvements
/workflows:epic-to-stories --improvement-focus
```

## What Gets Created

### 1. Technology Configuration (CLAUDE.md)

```markdown
# Project Technology Configuration

## Detected Stack

- Frontend: React 18.2, TypeScript 5.0
- Backend: Express.js 4.18, Node.js 18
- Database: PostgreSQL 15, Redis 7
- Testing: Jest, Playwright

## Agent Assignments

### Routing Rules

- /src/components/\* → @react-specialist
- /src/hooks/\* → @react-hooks-expert
- /src/api/\* → @express-specialist
- /src/db/\* → @postgresql-expert
```

### 2. Specialized Agents (Project-Specific)

```
.claude/agents/tech-specialists/
├── frontend/
│   ├── react-specialist.md
│   └── typescript-specialist.md
├── backend/
│   ├── express-specialist.md
│   └── nodejs-specialist.md
└── database/
    ├── postgresql-specialist.md
    └── redis-specialist.md
```

### 3. Complete Documentation Set

```
docs/
├── project-brief.md          # Project overview
├── prd.md                    # Product requirements
├── architecture.md           # System design
├── frontend-architecture.md  # UI architecture
├── uxui-spec.md             # UX/UI details
├── tech-stack.md            # Technology decisions
└── stories/                 # Implementation stories
    ├── epic-1/
    │   ├── 1.1.story.md
    │   └── 1.2.story.md
    └── orchestration-guide.md
```

## Existing Project Takeover Checklist

When taking over an existing project:

1. **Initial Setup**

   ```bash
   # Sync PIB-METHOD to the project
   cd existing-project
   # Copy PIB files (if not already present)
   ```

2. **Run Tech Detection**

   ```bash
   /workflows:project-init-tech --research --create-missing
   ```

3. **Analyze Existing Code**

   ```bash
   /agents:analyst-brief --from-existing-code
   ```

4. **Generate Missing Docs**
   - If no PRD: Generate from code analysis
   - If no architecture: Reverse engineer
   - If no stories: Create improvement epics

5. **Create Specialists**
   - Automatically created by project-init-tech
   - Tailored to detected versions
   - Research-based best practices

## Benefits of This Approach

### For New Projects

- Complete setup in one command
- All documentation created
- Tech specialists ready
- Development can start immediately

### For Existing Projects

- Non-destructive analysis
- Fills documentation gaps
- Creates project-specific specialists
- Preserves existing work

### Smart Defaults

- Detects what exists
- Creates what's missing
- Adapts to project state
- Maintains consistency

## Common Scenarios

### Scenario 1: React/Node Project Takeover

```bash
# Run this first
/workflows:project-init-tech --research --create-missing

# Results:
- Creates react-specialist for React 18
- Creates express-specialist for backend
- Updates CLAUDE.md with routing
- Ready for development
```

### Scenario 2: Python/Django Takeover

```bash
# Same command works
/workflows:project-init-tech --research --create-missing

# Results:
- Creates django-specialist
- Creates python-specialist
- Detects Django version
- Configures appropriate patterns
```

### Scenario 3: Full Stack with Missing Docs

```bash
# Use full init for existing project
/workflows:project-init --existing-project

# Results:
- Analyzes all code
- Creates complete documentation
- Sets up all specialists
- Ready for enhancements
```

## Summary

1. **Standard project-init DOES include tech specialists** - It's built into
   Phase 0
2. **For existing projects, YES use project-init** - With --existing-project
   flag
3. **Complete documentation IS created** - All architectural and planning docs

The system is designed to be smart about what exists and what needs to be
created, making it perfect for both new projects and takeovers!
