# Technology Specialist Commands Guide

## Available Commands

### 1. Create Technology-Specific Agent
Creates a new specialized agent with research-based expertise for specific technologies.

**Command:**
```bash
create-agent -> /workflows:create-tech-agent "<technology>" [--research] [--version <version>]
```

**Examples:**
```bash
# Create React specialist with research
create-agent -> /workflows:create-tech-agent "React" --research --version 18

# Create PostgreSQL expert
create-agent -> /workflows:create-tech-agent "PostgreSQL" --research

# Create Vue.js specialist
create-agent -> /workflows:create-tech-agent "Vue.js" --research --version 3
```

**What it does:**
- Uses Perplexity MCP to research best practices
- Creates agent with framework-specific knowledge
- Validates against official documentation
- Sets up delegation patterns

### 2. Initialize Project with Technology Detection
Initialize projects with automatic technology stack detection and specialized agent assignment.

**Command:**
```bash
init-tech -> /workflows:project-init-tech [--research] [--create-missing]
```

**Examples:**
```bash
# Basic technology detection
init-tech -> /workflows:project-init-tech

# With research and agent creation
init-tech -> /workflows:project-init-tech --research --create-missing
```

**What it does:**
- Scans project for technology stack
- Assigns appropriate specialists
- Creates routing rules in CLAUDE.md
- Creates missing specialists if requested

### 3. Use Technology Lead Orchestrator
Orchestrate technology specialists for complex tasks.

**Command:**
```bash
tech-lead -> /agents:tech-lead
```

**What it does:**
- Analyzes task requirements
- Routes to appropriate specialists
- Coordinates multi-technology workflows
- Manages handoffs between specialists

## Usage in Development Workflow

### Scenario 1: New React Project
```bash
# 1. Initialize project with tech detection
init-tech -> /workflows:project-init-tech --research

# 2. If React specialist doesn't exist, create it
create-agent -> /workflows:create-tech-agent "React" --research --version 18

# 3. Start development with automatic routing
/workflows:dev-command "create user dashboard component"
[Automatically routes to react-specialist]
```

### Scenario 2: Adding PostgreSQL to Project
```bash
# 1. Create PostgreSQL specialist
create-agent -> /workflows:create-tech-agent "PostgreSQL" --research

# 2. Update project configuration
init-tech -> /workflows:project-init-tech

# 3. Database tasks now route to specialist
> Create a migration for user roles table
[Automatically routes to postgresql-expert]
```

### Scenario 3: Multi-Stack Feature
```bash
# 1. Use tech lead for orchestration
tech-lead -> /agents:tech-lead

# 2. Request complex feature
> Implement real-time chat with React frontend, Express backend, and PostgreSQL storage

[Tech lead coordinates:
- react-specialist for UI
- express-specialist for API
- postgresql-expert for database]
```

## Integration with Existing Commands

### Enhanced Dev Command
The `/workflows:dev-command` now includes technology detection:
```bash
/workflows:dev-command "add authentication to React app"

[Detects React, routes to react-specialist with authentication patterns]
```

### Project Init Enhancement
The standard `/workflows:project-init` now includes Phase 0 for tech detection:
```bash
init -> /workflows:project-init

[Phase 0: Runs technology detection and specialist assignment]
[Continues with standard initialization]
```

## Command Format in PIB-METHOD

PIB-METHOD uses a consistent command format:
- Format: `keyword -> /namespace:command-name`
- The keyword helps with command discovery and categorization
- Examples:
  - `init-tech ->` for technology initialization
  - `create-agent ->` for agent creation
  - `tech-lead ->` for orchestration

## Troubleshooting

### Command Not Found
If commands aren't recognized:
1. Use the full format: `keyword -> /namespace:command`
2. Ensure command files exist in `.claude/commands/workflows/`
3. Try typing just the keyword (e.g., `init-tech`) and wait for autocomplete

### Specialist Not Available
If a specialist isn't found:
1. Create it with `create-agent -> /workflows:create-tech-agent`
2. Check `.claude/agents/tech-specialists/` directory
3. Verify agent metadata has correct name

### Routing Not Working
If automatic routing fails:
1. Check CLAUDE.md has technology stack section
2. Verify hook is configured in settings.dev.json
3. Check tech-detection.log for errors
4. Use explicit sub-agent invocation as fallback

## Best Practices

1. **Create Specialists Early**: Run tech detection at project start
2. **Use Research Flag**: Always use `--research` for current best practices
3. **Update Regularly**: Recreate specialists when major versions change
4. **Let Routing Work**: Trust automatic routing before manual override
5. **Check LEVER Compliance**: All specialists maintain LEVER standards

## Quick Reference

```bash
# Detect and initialize tech stack
init-tech -> /workflows:project-init-tech --research --create-missing

# Create a new specialist
create-agent -> /workflows:create-tech-agent "React" --research

# Use tech lead for complex tasks
tech-lead -> /agents:tech-lead
```

## Related Documentation
- `.claude/TECH-SPECIALIST-IMPLEMENTATION.md` - Implementation details
- `.claude/TECH-SPECIALIST-INTEGRATION.md` - Integration guide
- `.claude/agents/templates/tech-specialist-template.md` - Agent template