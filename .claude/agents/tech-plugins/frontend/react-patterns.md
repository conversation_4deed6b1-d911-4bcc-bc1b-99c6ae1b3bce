# React Patterns Plugin

**Type**: Lightweight Tech Plugin for Universal Dev Agent
**Framework**: React 18+
**Focus**: Component patterns, hooks, performance optimization

## Core Patterns

### Component Structure
```typescript
// Functional component with TypeScript
interface ComponentProps {
  title: string;
  onAction?: () => void;
}

export const Component: React.FC<ComponentProps> = ({ title, onAction }) => {
  // Hooks at top
  const [state, setState] = useState(false);
  
  // Effects after state
  useEffect(() => {
    // Cleanup pattern
    return () => {};
  }, [dependencies]);
  
  // Handlers before render
  const handleClick = useCallback(() => {
    onAction?.();
  }, [onAction]);
  
  // Render
  return <div onClick={handleClick}>{title}</div>;
};
```

### State Management Patterns
1. **Local State**: useState for component-specific state
2. **Lifted State**: Prop drilling for 1-2 levels
3. **Context**: For cross-cutting concerns (theme, auth)
4. **External Store**: For complex app state (Redux, Zustand)

### Performance Patterns
- `React.memo()` for expensive components
- `useMemo()` for expensive computations
- `useCallback()` for stable function references
- Lazy loading with `React.lazy()` and Suspense

### Common Hooks Patterns
```typescript
// Custom hook pattern
const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState(value);
  
  useEffect(() => {
    const handler = setTimeout(() => setDebouncedValue(value), delay);
    return () => clearTimeout(handler);
  }, [value, delay]);
  
  return debouncedValue;
};
```

### Error Handling
```typescript
// Error boundary pattern
class ErrorBoundary extends Component {
  state = { hasError: false };
  
  static getDerivedStateFromError() {
    return { hasError: true };
  }
  
  componentDidCatch(error: Error, info: ErrorInfo) {
    console.error('Error caught:', error, info);
  }
}
```

### Testing Patterns
- Use React Testing Library
- Test user interactions, not implementation
- Mock external dependencies
- Use data-testid for reliable queries

### Anti-Patterns to Avoid
- ❌ Modifying state directly
- ❌ Using array indexes as keys in dynamic lists
- ❌ Excessive prop drilling (use Context)
- ❌ useEffect without cleanup
- ❌ Inline function definitions in render

### File Organization
```
components/
├── Button/
│   ├── Button.tsx
│   ├── Button.test.tsx
│   ├── Button.module.css
│   └── index.ts
```

## LEVER Optimizations

### Leverage
- Use existing UI component libraries (MUI, Ant Design)
- Reuse custom hooks across components
- Extend base components rather than recreating

### Extend
- Prefer composition over inheritance
- Use HOCs and render props for reusability
- Extend existing components with styled-components

### Verify
- PropTypes or TypeScript for type checking
- ESLint rules for React best practices
- Unit tests for component behavior

### Eliminate
- Remove duplicate state management
- Consolidate similar components
- Use shared utilities

### Reduce
- Minimize bundle size with code splitting
- Reduce re-renders with proper memoization
- Simplify component hierarchies