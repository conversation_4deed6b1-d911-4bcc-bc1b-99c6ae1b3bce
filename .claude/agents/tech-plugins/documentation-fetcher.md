# Documentation Fetcher Plugin

**Type**: Dynamic Documentation Retrieval for Universal Dev Agent
**Tools**: mcp__context7__resolve-library-id, mcp__context7__get-library-docs
**Purpose**: Fetch documentation on-demand for low-frequency libraries

## When This Plugin Activates

Activates when:
- Technology detected but no specialist exists
- Library usage is low-frequency (< 5 files)
- External API or service detected
- One-off library usage identified

## Decision Logic

```typescript
// Pseudo-code for activation
if (!specialistExists(tech) && !pluginExists(tech)) {
  const usage = analyzeUsage(tech);
  
  if (usage.frequency < 5 || usage.type === 'external-api') {
    // Use documentation fetcher
    return activateDocumentationFetcher(tech);
  }
}
```

## Documentation Retrieval Process

### Step 1: Identify Library
```typescript
// Use Context7 to resolve library
const libraryId = await context7.resolveLibraryId(libraryName);
```

### Step 2: Fetch Relevant Docs
```typescript
// Get documentation for specific topic
const docs = await context7.getLibraryDocs({
  libraryId: libraryId,
  topic: specificFeature, // e.g., "authentication", "file upload"
  tokens: 5000 // Enough for implementation
});
```

### Step 3: Extract Patterns
From fetched documentation, extract:
- Code examples
- Best practices
- Common patterns
- API methods
- Error handling

### Step 4: Apply to Task
Use extracted patterns to:
- Implement the feature
- Follow library conventions
- Handle errors properly
- Use latest API

## Examples

### Example 1: Stripe Payment (One-off)
```javascript
// Task: "Add Stripe payment to checkout"
// Detection: Stripe not used elsewhere
// Action: Fetch Stripe docs

const stripeDocs = await fetchDocs('stripe', 'payment-intents');
// Implement using latest Stripe patterns
```

### Example 2: PDF Generation (Rare Use)
```javascript
// Task: "Generate PDF report"
// Detection: PDFKit used in 2 files only
// Action: Fetch PDFKit docs

const pdfDocs = await fetchDocs('pdfkit', 'document-creation');
// Implement using current PDFKit API
```

### Example 3: Email Service (External)
```javascript
// Task: "Send email via SendGrid"
// Detection: External API
// Action: Fetch SendGrid docs

const sendgridDocs = await fetchDocs('sendgrid', 'send-email');
// Use latest SendGrid API
```

## Integration with Universal Dev Agent

The universal-dev-agent should:

1. **Check Frequency First**
   ```javascript
   const usage = await checkLibraryUsage(library);
   if (usage.count < 5) {
     useDynamicDocs = true;
   }
   ```

2. **Fetch When Needed**
   ```javascript
   if (useDynamicDocs && !cachedDocs[library]) {
     const docs = await fetchLibraryDocs(library, feature);
     cachedDocs[library] = docs; // Cache for session
   }
   ```

3. **Apply Patterns**
   ```javascript
   const patterns = extractPatterns(docs);
   const implementation = applyPatterns(patterns, task);
   ```

## Advantages

1. **Always Current**: Latest API documentation
2. **No Maintenance**: No outdated specialists
3. **Efficient**: Only fetch what's needed
4. **Flexible**: Works with any library
5. **Smart Caching**: Reuse within session

## When NOT to Use

Don't use documentation fetcher for:
- Core project frameworks (create specialists)
- Complex integrations (need deep knowledge)
- Proprietary/internal libraries (no public docs)
- Performance-critical code (need optimization patterns)

## Configuration

In CLAUDE.md, specify:
```markdown
## Documentation-Only Libraries
These libraries use dynamic documentation fetching:
- stripe - Payment processing (external API)
- pdfkit - PDF generation (rarely used)
- nodemailer - Email sending (simple usage)
- csv-parser - Data import (one-off)
```

## Cache Strategy

Cache fetched docs for:
- Current session only
- Specific feature/topic
- Clear after task completion
- Refresh if > 1 week old

This approach ensures the universal-dev-agent can handle any library intelligently without creating unnecessary specialists!