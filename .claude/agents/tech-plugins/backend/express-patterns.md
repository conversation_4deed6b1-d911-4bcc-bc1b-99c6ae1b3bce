# Express.js Patterns Plugin

**Type**: Lightweight Tech Plugin for Universal Dev Agent
**Framework**: Express.js 4.x
**Focus**: REST APIs, middleware, error handling, security

## Core Patterns

### Application Structure
```javascript
// Modular route organization
app.use('/api/users', userRoutes);
app.use('/api/posts', postRoutes);
app.use('/api/auth', authRoutes);

// Error handling middleware (always last)
app.use(errorHandler);
```

### Route Patterns
```javascript
// RESTful route structure
router.get('/', controller.list);        // GET /users
router.get('/:id', controller.get);      // GET /users/:id
router.post('/', controller.create);     // POST /users
router.put('/:id', controller.update);   // PUT /users/:id
router.delete('/:id', controller.delete); // DELETE /users/:id
```

### Middleware Patterns
```javascript
// Authentication middleware
const authenticate = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.split(' ')[1];
    const user = await verifyToken(token);
    req.user = user;
    next();
  } catch (error) {
    res.status(401).json({ error: 'Unauthorized' });
  }
};

// Validation middleware
const validate = (schema) => (req, res, next) => {
  const { error } = schema.validate(req.body);
  if (error) {
    return res.status(400).json({ error: error.details[0].message });
  }
  next();
};
```

### Error Handling Patterns
```javascript
// Async error wrapper
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Global error handler
const errorHandler = (err, req, res, next) => {
  const status = err.status || 500;
  const message = err.message || 'Internal Server Error';
  
  res.status(status).json({
    error: message,
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
};
```

### Controller Patterns
```javascript
// Clean controller with error handling
const userController = {
  list: asyncHandler(async (req, res) => {
    const { page = 1, limit = 10 } = req.query;
    const users = await User.findAll({ page, limit });
    res.json({ users, page, limit });
  }),
  
  create: asyncHandler(async (req, res) => {
    const user = await User.create(req.body);
    res.status(201).json(user);
  })
};
```

### Security Patterns
```javascript
// Essential security middleware
app.use(helmet()); // Security headers
app.use(cors({ origin: process.env.ALLOWED_ORIGINS }));
app.use(express.json({ limit: '10mb' }));
app.use(rateLimiter); // Rate limiting

// Input sanitization
const sanitizeInput = (req, res, next) => {
  req.body = sanitize(req.body);
  req.query = sanitize(req.query);
  req.params = sanitize(req.params);
  next();
};
```

### Database Integration Patterns
```javascript
// Connection pooling
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000
});

// Transaction pattern
const createUserWithProfile = async (userData, profileData) => {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');
    const user = await client.query('INSERT INTO users...', userData);
    const profile = await client.query('INSERT INTO profiles...', profileData);
    await client.query('COMMIT');
    return { user, profile };
  } catch (e) {
    await client.query('ROLLBACK');
    throw e;
  } finally {
    client.release();
  }
};
```

### Testing Patterns
```javascript
// Supertest for API testing
describe('POST /api/users', () => {
  it('should create a new user', async () => {
    const res = await request(app)
      .post('/api/users')
      .send({ name: 'Test User', email: '<EMAIL>' })
      .expect(201);
    
    expect(res.body).toHaveProperty('id');
    expect(res.body.email).toBe('<EMAIL>');
  });
});
```

### File Organization
```
src/
├── routes/
│   ├── userRoutes.js
│   └── authRoutes.js
├── controllers/
│   ├── userController.js
│   └── authController.js
├── middleware/
│   ├── auth.js
│   └── validate.js
├── models/
├── services/
└── utils/
```

## LEVER Optimizations

### Leverage
- Use established middleware (helmet, cors, compression)
- Implement standard REST conventions
- Use validation libraries (Joi, express-validator)

### Extend
- Build on Express Router for modular routes
- Extend error classes for custom errors
- Use middleware composition

### Verify
- Input validation on all routes
- JWT verification for protected routes
- Request/response logging

### Eliminate
- Remove redundant middleware
- Consolidate similar routes
- Use shared error handlers

### Reduce
- Minimize middleware stack
- Optimize database queries
- Implement caching strategies