# Technology Specialists Directory

This directory contains project-specific technology specialists.

## Important Notes

1. **Not Synced**: This directory is excluded from `sync-pib-complete.sh`
2. **Project-Specific**: Each project creates its own specialists
3. **Created On-Demand**: Use `/workflows:create-tech-agent` to create

## Why Project-Specific?

- Different projects use different technology versions
- Custom patterns vary between projects
- Avoids conflicts when syncing
- Allows project-specific optimizations

## Creating Specialists

For your project, create specialists as needed:

```bash
# Create React specialist for this project
/workflows:create-tech-agent "React" --research --version 18

# Create PostgreSQL expert
/workflows:create-tech-agent "PostgreSQL" --research
```

## Directory Structure

```
tech-specialists/
├── README.md (this file)
├── frontend/
│   └── [project-specific specialists]
├── backend/
│   └── [project-specific specialists]
├── database/
│   └── [project-specific specialists]
└── testing/
    └── [project-specific specialists]
```

Each project maintains its own set of specialists based on its technology stack.
