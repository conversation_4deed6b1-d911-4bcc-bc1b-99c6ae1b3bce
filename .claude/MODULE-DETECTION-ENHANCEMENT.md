# Module Detection Enhancement for Project Init

## Current Gap

The current project-init workflow doesn't automatically detect and document
module-based architectures. For projects with modular structure, we need
enhanced detection and documentation.

## Proposed Enhancement

### 1. Module Detection Patterns

Add to `/workflows:project-init-tech` the ability to detect:

#### Common Module Patterns

```javascript
// Node.js module pattern
modules/
├── auth/
│   ├── index.js
│   ├── controllers/
│   ├── models/
│   └── routes/
├── payments/
├── users/
└── reports/

// Python module pattern
modules/
├── __init__.py
├── auth/
│   ├── __init__.py
│   ├── views.py
│   └── models.py
└── payments/

// Microservices pattern
services/
├── auth-service/
├── payment-service/
└── user-service/

// Domain-driven design
src/
├── domains/
│   ├── user/
│   ├── order/
│   └── inventory/
└── shared/
```

### 2. Enhanced CLAUDE.md Structure

When modules are detected, generate:

```markdown
# Project Technology Configuration

## Detected Stack

- Frontend: React 18.2, TypeScript 5.0
- Backend: Express.js 4.18, Node.js 18
- Database: PostgreSQL 15, Redis 7
- Architecture: Module-based

## Module Structure

### Detected Modules

1. **Authentication Module** (`/modules/auth`)
   - Purpose: User authentication and authorization
   - Technologies: Express, JWT, bcrypt
   - Database: Users table in PostgreSQL
   - Specialists: @express-specialist, @security-specialist

2. **Payment Module** (`/modules/payments`)
   - Purpose: Payment processing and billing
   - Technologies: Express, Stripe SDK
   - Database: Payments, subscriptions tables
   - Specialists: @express-specialist, @payment-specialist

3. **Reporting Module** (`/modules/reports`)
   - Purpose: Analytics and reporting
   - Technologies: Express, Chart.js
   - Database: Read replicas, Redis cache
   - Specialists: @express-specialist, @data-specialist

## Module Routing Rules

- /modules/auth/\* → @auth-module-specialist
- /modules/payments/\* → @payment-module-specialist
- /modules/reports/\* → @reporting-module-specialist
- /shared/\* → @shared-module-specialist
```

### 3. Automatic Module Documentation

For each detected module, create:

```
docs/
├── modules/
│   ├── auth/
│   │   ├── module-brief.md
│   │   ├── module-prd.md
│   │   ├── module-architecture.md
│   │   └── module-api.md
│   ├── payments/
│   │   └── [same structure]
│   └── reports/
│       └── [same structure]
├── module-integration-guide.md
└── module-dependency-map.md
```

### 4. Module Detection Logic

```javascript
// Pseudo-code for module detection
function detectModules(projectPath) {
  const moduleIndicators = [
    { pattern: /modules?\//, type: 'explicit-modules' },
    { pattern: /services?\//, type: 'microservices' },
    { pattern: /domains?\//, type: 'domain-driven' },
    { pattern: /features?\//, type: 'feature-based' },
    { pattern: /components?\/[A-Z]\w+\//, type: 'component-modules' },
  ];

  const modules = [];

  // Scan directory structure
  for (const indicator of moduleIndicators) {
    const matches = findDirectories(projectPath, indicator.pattern);
    if (matches.length > 0) {
      modules.push(...analyzeModules(matches, indicator.type));
    }
  }

  // Analyze each module
  return modules.map(module => ({
    name: module.name,
    path: module.path,
    type: detectModuleType(module),
    technologies: detectModuleTech(module),
    dependencies: detectModuleDeps(module),
    purpose: inferModulePurpose(module),
  }));
}
```

### 5. Integration with Project Init

Update `/workflows:project-init` Phase 0:

```markdown
### Phase 0: Technology & Architecture Detection

0. **Tech Lead** - Analyze Project Structure
   - Run `/workflows:project-init-tech --research --create-missing`
   - Detect frameworks, languages, and tools
   - **NEW: Detect module architecture**
   - **NEW: Create module-specific documentation**
   - Create/assign specialized agents
   - Generate tech-specific routing rules
   - **NEW: Generate module routing rules**
   - Update CLAUDE.md with stack and module configuration
```

### 6. Module-Specific Specialists

When modules are detected, optionally create:

- Module-specific specialists (auth-specialist, payment-specialist)
- Cross-module integration specialist
- Module testing specialist

## Implementation Steps

### Quick Fix (Manual)

For now, after running project-init, manually:

1. **Identify Modules**

   ```bash
   find . -type d -name "modules" -o -name "services" -o -name "domains"
   ```

2. **Document Each Module**

   ```bash
   # For each module found
   /workflows:module-dev <module-name>
   ```

3. **Update CLAUDE.md** Add module structure section manually

### Proper Implementation

1. Enhance `project-init-tech` to detect modules
2. Auto-generate module documentation structure
3. Create module-specific routing rules
4. Generate module dependency graph
5. Set up module-specific agents if needed

## Benefits

1. **Better Organization**: Each module properly documented
2. **Clearer Boundaries**: Module interfaces defined
3. **Specialist Routing**: Right expert for each module
4. **Dependency Tracking**: Understanding module relationships
5. **Easier Maintenance**: Module-specific documentation

## Example Output

When project-init detects modules:

```
[Technology Detection]
✓ Frontend: React 18
✓ Backend: Express.js
✓ Database: PostgreSQL

[Architecture Detection]
✓ Module-based architecture detected
✓ Found 5 modules:
  - auth (Authentication & Authorization)
  - payments (Payment Processing)
  - users (User Management)
  - reports (Analytics & Reporting)
  - shared (Common Utilities)

[Documentation Creation]
✓ Creating module documentation structure
✓ Generating module briefs
✓ Creating module architectures
✓ Documenting module APIs
✓ Creating integration guide

[Specialist Assignment]
✓ auth/* → @auth-module-specialist
✓ payments/* → @payment-module-specialist
✓ users/* → @user-module-specialist
✓ reports/* → @reporting-module-specialist
✓ shared/* → @shared-utilities-specialist
```

This enhancement would make PIB-METHOD much more effective for module-based
projects!
