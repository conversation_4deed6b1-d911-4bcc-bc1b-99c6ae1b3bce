{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(uv:*)", "Bash(find:*)", "<PERSON><PERSON>(mv:*)", "Bash(grep:*)", "Bash(npm:*)", "Bash(ls:*)", "Bash(cp:*)", "Write", "Edit", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(touch:*)", "Bash(rm:*)", "Bash(ls:*)", "WebFetch(domain:docs.anthropic.com)", "WebFetch(domain:github.com)", "Bash(grep:*)", "<PERSON><PERSON>(chmod:*)", "Bash(.claude/scripts/infrastructure -> /workflows:validate-infrastructure-slash-commands.sh:*)", "Bash(./.claude/scripts/validate-slash-commands.sh:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(./sync-pib-complete.sh:*)", "Bash(./.claude/scripts/validate-sync-logic.sh:*)", "Bash(./.claude/commands/fix-command-references.sh:*)", "Bash(./.claude/scripts/quick-fix-commands.sh:*)", "Bash(./.claude/scripts/test-agent-commands.sh:*)", "Bash(./.claude/scripts/comprehensive-command-audit.sh:*)", "Bash(./.claude/scripts/comprehensive-fix-commands.sh:*)", "Bash(./.claude/scripts/targeted-fix-commands.sh:*)", "Bash(/Users/<USER>/Projects/PIB-METHOD/.claude/scripts/test-fixed-commands.sh:*)", "Bash(/Users/<USER>/Projects/PIB-METHOD/.claude/scripts/final-command-verification.sh)", "<PERSON><PERSON>(sed:*)", "Bash(/Users/<USER>/Projects/PIB-METHOD/.claude/scripts/test-agent-namespace.sh:*)"], "deny": []}, "hooks": {"PreToolUse": [{"matcher": "", "hooks": [{"type": "command", "command": "bash \"$PWD/.claude/hooks/hook_wrapper_dynamic.sh\" pre_tool_use.py"}]}], "PostToolUse": [{"matcher": "", "hooks": [{"type": "command", "command": "bash \"$PWD/.claude/hooks/hook_wrapper_dynamic.sh\" post_tool_use.py"}]}], "Notification": [{"matcher": "", "hooks": [{"type": "command", "command": "bash \"$PWD/.claude/hooks/hook_wrapper_dynamic.sh\" notification.py --notify"}]}], "Stop": [{"matcher": "", "hooks": [{"type": "command", "command": "bash \"$PWD/.claude/hooks/hook_wrapper_dynamic.sh\" stop.py"}]}], "SessionStart": [{"matcher": "", "hooks": [{"type": "command", "command": "bash \"$PWD/.claude/hooks/hook_wrapper_dynamic.sh\" session_start.py"}]}], "UserPromptSubmit": [{"matcher": "", "hooks": [{"type": "command", "command": "bash \"$PWD/.claude/hooks/hook_wrapper_dynamic.sh\" user_prompt_submit.py"}]}], "SubagentStop": [{"matcher": "", "hooks": [{"type": "command", "command": "bash \"$PWD/.claude/hooks/hook_wrapper_dynamic.sh\" subagent_stop.py"}]}], "PreCompact": [{"matcher": "", "hooks": [{"type": "command", "command": "bash \"$PWD/.claude/hooks/hook_wrapper_dynamic.sh\" pre_compact.py"}]}]}}