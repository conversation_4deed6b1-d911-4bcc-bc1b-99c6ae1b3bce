{"leverTriggers": {"planningCommands": ["*plan-workflow", "session -> /planning:plan-session-workflow", "*SM create", "*SM create-with-orchestration", "*Architect Create Architecture", "*Design Architect Create Frontend Architecture", "*DevOps infra-plan", "*Architect module-design", "*Platform Engineer design"], "description": "PIB commands that should trigger LEVER planning reminders"}, "leverIntegration": {"claudePlanMode": true, "pibCommands": true, "autoReminder": true}}