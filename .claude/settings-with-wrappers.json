{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(uv:*)", "Bash(find:*)", "<PERSON><PERSON>(mv:*)", "Bash(grep:*)", "Bash(npm:*)", "Bash(ls:*)", "Bash(cp:*)", "Write", "Edit", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(touch:*)"], "deny": []}, "hooks": {"PreToolUse": [{"matcher": "", "hooks": [{"type": "command", "command": "bash .claude/hooks/hook_wrapper.sh pre_tool_use.py"}]}], "PostToolUse": [{"matcher": "", "hooks": [{"type": "command", "command": "bash .claude/hooks/hook_wrapper.sh post_tool_use.py"}]}], "Notification": [{"matcher": "", "hooks": [{"type": "command", "command": "bash .claude/hooks/hook_wrapper.sh notification.py --notify"}]}], "Stop": [{"matcher": "", "hooks": [{"type": "command", "command": "bash .claude/hooks/hook_wrapper.sh stop.py --chat"}]}, {"matcher": "", "hooks": [{"type": "command", "command": "bash -c 'DIR=$(pwd); while [ \"$DIR\" != \"/\" ] && [ ! -d \"$DIR/.claude\" ]; do DIR=$(dirname \"$DIR\"); done; [ -d \"$DIR/.claude\" ] && cat | \"$DIR/.claude/hooks/universal-hook-runner.sh\" notification-hook.sh || exit 2'"}]}], "SubagentStop": [{"matcher": "", "hooks": [{"type": "command", "command": "bash .claude/hooks/hook_wrapper.sh subagent_stop.py"}]}, {"matcher": "", "hooks": [{"type": "command", "command": "bash -c 'DIR=$(pwd); while [ \"$DIR\" != \"/\" ] && [ ! -d \"$DIR/.claude\" ]; do DIR=$(dirname \"$DIR\"); done; [ -d \"$DIR/.claude\" ] && cat | \"$DIR/.claude/hooks/universal-hook-runner.sh\" notification-hook.sh || exit 2'"}]}], "UserPromptSubmit": [{"hooks": [{"type": "command", "command": "bash .claude/hooks/hook_wrapper.sh user_prompt_submit.py --log-only"}]}], "PreCompact": [{"matcher": "", "hooks": [{"type": "command", "command": "bash .claude/hooks/hook_wrapper.sh pre_compact.py"}]}], "SessionStart": [{"matcher": "", "hooks": [{"type": "command", "command": "bash .claude/hooks/hook_wrapper.sh session_start.py"}]}]}}