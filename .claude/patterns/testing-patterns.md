# Testing Patterns

Reusable testing patterns for unit, integration, and E2E tests.

## Unit Testing Patterns

### Basic Test Structure
```javascript
describe('ComponentName', () => {
  // Setup
  beforeEach(() => {
    // Reset state, mocks, etc.
  });
  
  afterEach(() => {
    // Cleanup
  });
  
  describe('methodName', () => {
    it('should handle normal case', () => {
      // Arrange
      const input = 'test';
      const expected = 'TEST';
      
      // Act
      const result = toUpperCase(input);
      
      // Assert
      expect(result).toBe(expected);
    });
    
    it('should handle edge case', () => {
      // Test edge cases
    });
    
    it('should handle error case', () => {
      // Test error scenarios
    });
  });
});
```

### Mock Patterns
```javascript
// Mock external dependencies
jest.mock('../services/api', () => ({
  fetchUser: jest.fn(),
  updateUser: jest.fn()
}));

// Mock with different implementations
const mockApi = {
  fetchUser: jest.fn()
    .mockResolvedValueOnce({ id: 1, name: 'Test' })
    .mockRejectedValueOnce(new Error('Network error'))
};

// Spy on existing methods
const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
```

### React Component Testing
```typescript
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

describe('UserForm', () => {
  const mockOnSubmit = jest.fn();
  
  beforeEach(() => {
    mockOnSubmit.mockClear();
  });
  
  it('should submit form with valid data', async () => {
    const user = userEvent.setup();
    
    render(<UserForm onSubmit={mockOnSubmit} />);
    
    // Fill form
    await user.type(screen.getByLabelText('Name'), 'John Doe');
    await user.type(screen.getByLabelText('Email'), '<EMAIL>');
    
    // Submit
    await user.click(screen.getByRole('button', { name: 'Submit' }));
    
    // Assert
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        name: 'John Doe',
        email: '<EMAIL>'
      });
    });
  });
  
  it('should show validation errors', async () => {
    const user = userEvent.setup();
    
    render(<UserForm onSubmit={mockOnSubmit} />);
    
    // Submit empty form
    await user.click(screen.getByRole('button', { name: 'Submit' }));
    
    // Check errors
    expect(screen.getByText('Name is required')).toBeInTheDocument();
    expect(screen.getByText('Email is required')).toBeInTheDocument();
    expect(mockOnSubmit).not.toHaveBeenCalled();
  });
});
```

## Integration Testing Patterns

### API Route Testing
```javascript
const request = require('supertest');
const app = require('../app');

describe('User API', () => {
  let authToken;
  
  beforeAll(async () => {
    // Setup test database
    await setupTestDatabase();
    
    // Get auth token
    const response = await request(app)
      .post('/api/auth/login')
      .send({ email: '<EMAIL>', password: 'password' });
    
    authToken = response.body.token;
  });
  
  afterAll(async () => {
    await cleanupTestDatabase();
  });
  
  describe('GET /api/users', () => {
    it('should return paginated users', async () => {
      const response = await request(app)
        .get('/api/users')
        .set('Authorization', `Bearer ${authToken}`)
        .query({ page: 1, limit: 10 })
        .expect(200);
      
      expect(response.body).toMatchObject({
        success: true,
        data: expect.any(Array),
        pagination: {
          page: 1,
          limit: 10,
          total: expect.any(Number),
          pages: expect.any(Number)
        }
      });
    });
    
    it('should require authentication', async () => {
      await request(app)
        .get('/api/users')
        .expect(401);
    });
  });
  
  describe('POST /api/users', () => {
    it('should create user with valid data', async () => {
      const newUser = {
        name: 'New User',
        email: '<EMAIL>',
        role: 'user'
      };
      
      const response = await request(app)
        .post('/api/users')
        .set('Authorization', `Bearer ${authToken}`)
        .send(newUser)
        .expect(201);
      
      expect(response.body.data).toMatchObject({
        name: newUser.name,
        email: newUser.email,
        role: newUser.role
      });
    });
    
    it('should validate required fields', async () => {
      const response = await request(app)
        .post('/api/users')
        .set('Authorization', `Bearer ${authToken}`)
        .send({})
        .expect(400);
      
      expect(response.body.errors).toBeDefined();
    });
  });
});
```

### Database Testing
```javascript
describe('User Model', () => {
  beforeEach(async () => {
    await User.deleteMany({});
  });
  
  it('should hash password before saving', async () => {
    const user = new User({
      name: 'Test User',
      email: '<EMAIL>',
      password: 'plaintext'
    });
    
    await user.save();
    
    expect(user.password).not.toBe('plaintext');
    expect(user.password).toMatch(/^\$2[aby]\$.{56}$/); // bcrypt pattern
  });
  
  it('should enforce unique email', async () => {
    const userData = {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password'
    };
    
    await User.create(userData);
    
    await expect(User.create(userData))
      .rejects
      .toThrow(/duplicate key error/);
  });
});
```

## E2E Testing Patterns

### Playwright Test Structure
```typescript
import { test, expect } from '@playwright/test';

test.describe('User Registration Flow', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/register');
  });
  
  test('should complete registration successfully', async ({ page }) => {
    // Fill registration form
    await page.fill('[name="name"]', 'John Doe');
    await page.fill('[name="email"]', '<EMAIL>');
    await page.fill('[name="password"]', 'SecurePass123!');
    await page.fill('[name="confirmPassword"]', 'SecurePass123!');
    
    // Accept terms
    await page.check('[name="acceptTerms"]');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Wait for redirect
    await page.waitForURL('/dashboard');
    
    // Verify welcome message
    await expect(page.locator('h1')).toContainText('Welcome, John');
  });
  
  test('should show validation errors', async ({ page }) => {
    // Submit empty form
    await page.click('button[type="submit"]');
    
    // Check all error messages are visible
    await expect(page.locator('.error-message')).toHaveCount(4);
    await expect(page.locator('text=Name is required')).toBeVisible();
    await expect(page.locator('text=Email is required')).toBeVisible();
    await expect(page.locator('text=Password is required')).toBeVisible();
    await expect(page.locator('text=You must accept the terms')).toBeVisible();
  });
});
```

### Page Object Pattern
```typescript
// Page Object
class LoginPage {
  constructor(private page: Page) {}
  
  async navigate() {
    await this.page.goto('/login');
  }
  
  async login(email: string, password: string) {
    await this.page.fill('[name="email"]', email);
    await this.page.fill('[name="password"]', password);
    await this.page.click('button[type="submit"]');
  }
  
  async getErrorMessage() {
    return this.page.locator('.error-message').textContent();
  }
  
  async isLoggedIn() {
    await this.page.waitForURL('/dashboard', { timeout: 5000 });
    return this.page.url().includes('/dashboard');
  }
}

// Usage in tests
test('should login with valid credentials', async ({ page }) => {
  const loginPage = new LoginPage(page);
  
  await loginPage.navigate();
  await loginPage.login('<EMAIL>', 'password');
  
  expect(await loginPage.isLoggedIn()).toBe(true);
});
```

## Test Data Patterns

### Factory Pattern
```javascript
const { faker } = require('@faker-js/faker');

const userFactory = {
  build: (overrides = {}) => ({
    name: faker.person.fullName(),
    email: faker.internet.email(),
    password: 'TestPass123!',
    role: 'user',
    isActive: true,
    ...overrides
  }),
  
  buildList: (count, overrides = {}) => {
    return Array.from({ length: count }, () => 
      userFactory.build(overrides)
    );
  },
  
  create: async (overrides = {}) => {
    const userData = userFactory.build(overrides);
    return User.create(userData);
  }
};

// Usage
const testUser = userFactory.build({ role: 'admin' });
const users = userFactory.buildList(5);
const savedUser = await userFactory.create();
```

### Test Database Seeding
```javascript
const seedDatabase = async () => {
  // Clear existing data
  await Promise.all([
    User.deleteMany({}),
    Post.deleteMany({}),
    Comment.deleteMany({})
  ]);
  
  // Create test users
  const users = await User.insertMany([
    { name: 'Admin User', email: '<EMAIL>', role: 'admin' },
    { name: 'Regular User', email: '<EMAIL>', role: 'user' },
    { name: 'Inactive User', email: '<EMAIL>', isActive: false }
  ]);
  
  // Create related data
  const posts = await Post.insertMany(
    users.map(user => ({
      title: `Post by ${user.name}`,
      content: faker.lorem.paragraphs(3),
      authorId: user._id
    }))
  );
  
  return { users, posts };
};
```

## Performance Testing Pattern
```javascript
describe('Performance Tests', () => {
  it('should handle 1000 concurrent requests', async () => {
    const promises = Array.from({ length: 1000 }, () =>
      request(app).get('/api/health').expect(200)
    );
    
    const startTime = Date.now();
    await Promise.all(promises);
    const duration = Date.now() - startTime;
    
    expect(duration).toBeLessThan(5000); // Should complete in 5 seconds
  });
  
  it('should query large dataset efficiently', async () => {
    // Seed with large dataset
    await userFactory.createList(10000);
    
    const startTime = Date.now();
    const response = await request(app)
      .get('/api/users')
      .query({ page: 1, limit: 100 })
      .expect(200);
    
    const duration = Date.now() - startTime;
    
    expect(duration).toBeLessThan(200); // Should respond in 200ms
    expect(response.body.data).toHaveLength(100);
  });
});
```

## LEVER Optimizations

### Leverage
- Reuse test utilities
- Share mock data
- Common test setups

### Extend
- Build on test helpers
- Extend base test classes
- Add to existing assertions

### Verify
- Test the tests
- Coverage reports
- Flaky test detection

### Eliminate
- Remove duplicate tests
- Consolidate helpers
- Unify test patterns

### Reduce
- Minimize test complexity
- Faster test execution
- Simpler assertions