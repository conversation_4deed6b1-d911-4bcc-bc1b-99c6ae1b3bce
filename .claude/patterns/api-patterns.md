# API Patterns

Reusable patterns for building REST and GraphQL APIs.

## RESTful API Patterns

### Standard CRUD Routes
```javascript
// Resource routes pattern
router.get('/resources', controller.list);         // GET all
router.get('/resources/:id', controller.get);      // GET one
router.post('/resources', controller.create);      // CREATE
router.put('/resources/:id', controller.update);   // UPDATE (full)
router.patch('/resources/:id', controller.patch);  // UPDATE (partial)
router.delete('/resources/:id', controller.delete); // DELETE
```

### Controller Pattern
```javascript
const resourceController = {
  // List with pagination
  list: asyncHandler(async (req, res) => {
    const { page = 1, limit = 20, sort = '-createdAt' } = req.query;
    
    const skip = (page - 1) * limit;
    const sortField = sort.startsWith('-') ? sort.slice(1) : sort;
    const sortOrder = sort.startsWith('-') ? -1 : 1;
    
    const [resources, total] = await Promise.all([
      Resource.find()
        .sort({ [sortField]: sortOrder })
        .skip(skip)
        .limit(limit),
      Resource.countDocuments()
    ]);
    
    res.json({
      data: resources,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  }),
  
  // Get single resource
  get: asyncHandler(async (req, res) => {
    const resource = await Resource.findById(req.params.id);
    
    if (!resource) {
      throw new NotFoundError('Resource');
    }
    
    res.json({ data: resource });
  }),
  
  // Create with validation
  create: asyncHandler(async (req, res) => {
    const resource = await Resource.create(req.validatedBody);
    
    res.status(201).json({
      data: resource,
      message: 'Resource created successfully'
    });
  })
};
```

### API Response Format
```typescript
// Consistent response structure
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    code?: string;
    details?: any;
  };
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
  meta?: Record<string, any>;
}

// Success response helper
const successResponse = <T>(data: T, meta?: any): ApiResponse<T> => ({
  success: true,
  data,
  ...(meta && { meta })
});

// Error response helper
const errorResponse = (message: string, code?: string, details?: any): ApiResponse<null> => ({
  success: false,
  error: {
    message,
    ...(code && { code }),
    ...(details && { details })
  }
});
```

## Authentication Patterns

### JWT Middleware
```javascript
const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN
  
  if (!token) {
    return res.status(401).json(
      errorResponse('Access token required', 'NO_TOKEN')
    );
  }
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = await User.findById(decoded.id).select('-password');
    
    if (!req.user) {
      throw new Error('User not found');
    }
    
    next();
  } catch (error) {
    return res.status(403).json(
      errorResponse('Invalid or expired token', 'INVALID_TOKEN')
    );
  }
};
```

### Role-Based Access Control
```javascript
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json(
        errorResponse('Authentication required', 'NOT_AUTHENTICATED')
      );
    }
    
    if (!roles.includes(req.user.role)) {
      return res.status(403).json(
        errorResponse('Insufficient permissions', 'FORBIDDEN')
      );
    }
    
    next();
  };
};

// Usage
router.delete('/users/:id', 
  authenticateToken, 
  authorize('admin'), 
  userController.delete
);
```

## Query Building Patterns

### Advanced Filtering
```javascript
const buildQuery = (queryParams) => {
  const query = {};
  
  // Text search
  if (queryParams.search) {
    query.$or = [
      { name: { $regex: queryParams.search, $options: 'i' } },
      { description: { $regex: queryParams.search, $options: 'i' } }
    ];
  }
  
  // Date range
  if (queryParams.startDate || queryParams.endDate) {
    query.createdAt = {};
    if (queryParams.startDate) {
      query.createdAt.$gte = new Date(queryParams.startDate);
    }
    if (queryParams.endDate) {
      query.createdAt.$lte = new Date(queryParams.endDate);
    }
  }
  
  // Status filter
  if (queryParams.status) {
    query.status = { $in: queryParams.status.split(',') };
  }
  
  // Numeric ranges
  if (queryParams.minPrice || queryParams.maxPrice) {
    query.price = {};
    if (queryParams.minPrice) {
      query.price.$gte = Number(queryParams.minPrice);
    }
    if (queryParams.maxPrice) {
      query.price.$lte = Number(queryParams.maxPrice);
    }
  }
  
  return query;
};
```

## Rate Limiting Pattern
```javascript
const createRateLimiter = (options = {}) => {
  const {
    windowMs = 15 * 60 * 1000, // 15 minutes
    max = 100, // limit each IP to 100 requests per windowMs
    message = 'Too many requests'
  } = options;
  
  const requests = new Map();
  
  return (req, res, next) => {
    const ip = req.ip;
    const now = Date.now();
    
    // Clean old entries
    const windowStart = now - windowMs;
    const userRequests = requests.get(ip) || [];
    const recentRequests = userRequests.filter(time => time > windowStart);
    
    if (recentRequests.length >= max) {
      return res.status(429).json(
        errorResponse(message, 'RATE_LIMIT_EXCEEDED')
      );
    }
    
    recentRequests.push(now);
    requests.set(ip, recentRequests);
    next();
  };
};
```

## GraphQL Patterns

### Resolver Pattern
```javascript
const resolvers = {
  Query: {
    user: async (_, { id }, { user }) => {
      if (!user) throw new AuthenticationError('Not authenticated');
      
      const userData = await User.findById(id);
      if (!userData) throw new UserInputError('User not found');
      
      return userData;
    },
    
    users: async (_, { filter, pagination }, { user }) => {
      if (!user) throw new AuthenticationError('Not authenticated');
      
      const { page = 1, limit = 20 } = pagination || {};
      const skip = (page - 1) * limit;
      
      const query = buildGraphQLFilter(filter);
      const users = await User.find(query)
        .skip(skip)
        .limit(limit);
      
      return users;
    }
  },
  
  Mutation: {
    createUser: async (_, { input }, { user }) => {
      if (!user || user.role !== 'admin') {
        throw new ForbiddenError('Admin access required');
      }
      
      const newUser = await User.create(input);
      return newUser;
    }
  },
  
  User: {
    // Field resolver for related data
    posts: async (parent) => {
      return Post.find({ userId: parent.id });
    }
  }
};
```

## API Versioning Pattern
```javascript
// Version through URL path
app.use('/api/v1', v1Routes);
app.use('/api/v2', v2Routes);

// Version through headers
const apiVersion = (req, res, next) => {
  const version = req.headers['api-version'] || 'v1';
  req.apiVersion = version;
  next();
};

// Version-specific handling
const versionedController = {
  getUser: async (req, res) => {
    const user = await User.findById(req.params.id);
    
    if (req.apiVersion === 'v2') {
      // V2 response format
      res.json({
        data: {
          ...user.toObject(),
          fullName: `${user.firstName} ${user.lastName}`
        }
      });
    } else {
      // V1 response format
      res.json({ user });
    }
  }
};
```

## LEVER Optimizations

### Leverage
- Use existing REST conventions
- Reuse authentication middleware
- Standard response formats

### Extend
- Build on framework routers
- Extend base controllers
- Add to existing middleware

### Verify
- API testing patterns
- Response validation
- Auth verification

### Eliminate
- Remove duplicate routes
- Consolidate controllers
- Unify response formats

### Reduce
- Simplify endpoint structure
- Minimize API versions
- Streamline auth flows