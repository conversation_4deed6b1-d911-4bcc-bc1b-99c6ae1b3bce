# Security Patterns

Essential security patterns for web applications.

## Authentication Patterns

### Secure Password Hashing
```javascript
const bcrypt = require('bcrypt');

const SALT_ROUNDS = 12;

// Hash password
const hashPassword = async (password) => {
  return bcrypt.hash(password, SALT_ROUNDS);
};

// Verify password
const verifyPassword = async (password, hash) => {
  return bcrypt.compare(password, hash);
};

// Password requirements
const passwordSchema = {
  minLength: 8,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
  
  validate: (password) => {
    const errors = [];
    
    if (password.length < passwordSchema.minLength) {
      errors.push(`Password must be at least ${passwordSchema.minLength} characters`);
    }
    
    if (passwordSchema.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('Password must contain uppercase letter');
    }
    
    if (passwordSchema.requireLowercase && !/[a-z]/.test(password)) {
      errors.push('Password must contain lowercase letter');
    }
    
    if (passwordSchema.requireNumbers && !/\d/.test(password)) {
      errors.push('Password must contain number');
    }
    
    if (passwordSchema.requireSpecialChars && !/[!@#$%^&*]/.test(password)) {
      errors.push('Password must contain special character');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
};
```

### JWT Token Management
```javascript
const jwt = require('jsonwebtoken');
const crypto = require('crypto');

// Token configuration
const tokenConfig = {
  accessTokenSecret: process.env.JWT_ACCESS_SECRET,
  refreshTokenSecret: process.env.JWT_REFRESH_SECRET,
  accessTokenExpiry: '15m',
  refreshTokenExpiry: '7d'
};

// Generate token pair
const generateTokens = (userId) => {
  const payload = { id: userId, type: 'access' };
  
  const accessToken = jwt.sign(
    payload,
    tokenConfig.accessTokenSecret,
    { expiresIn: tokenConfig.accessTokenExpiry }
  );
  
  const refreshToken = jwt.sign(
    { id: userId, type: 'refresh' },
    tokenConfig.refreshTokenSecret,
    { expiresIn: tokenConfig.refreshTokenExpiry }
  );
  
  return { accessToken, refreshToken };
};

// Secure token storage pattern
const storeRefreshToken = async (userId, refreshToken) => {
  const hashedToken = crypto
    .createHash('sha256')
    .update(refreshToken)
    .digest('hex');
  
  await RefreshToken.create({
    userId,
    token: hashedToken,
    expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
  });
};
```

### Session Security
```javascript
const session = require('express-session');
const MongoStore = require('connect-mongo');

const sessionConfig = {
  secret: process.env.SESSION_SECRET,
  name: 'sessionId', // Don't use default 'connect.sid'
  resave: false,
  saveUninitialized: false,
  store: MongoStore.create({
    mongoUrl: process.env.MONGODB_URI,
    touchAfter: 24 * 3600 // Lazy session update
  }),
  cookie: {
    secure: process.env.NODE_ENV === 'production', // HTTPS only in production
    httpOnly: true, // Prevent XSS
    maxAge: 1000 * 60 * 60 * 24, // 24 hours
    sameSite: 'strict' // CSRF protection
  }
};

app.use(session(sessionConfig));
```

## Input Validation & Sanitization

### SQL Injection Prevention
```javascript
// Parameterized queries (PostgreSQL)
const getUserById = async (userId) => {
  const query = 'SELECT * FROM users WHERE id = $1';
  const values = [userId];
  
  const result = await pool.query(query, values);
  return result.rows[0];
};

// Multiple parameters
const searchUsers = async (name, email, role) => {
  const query = `
    SELECT * FROM users 
    WHERE name ILIKE $1 
    AND email ILIKE $2 
    AND role = $3
  `;
  const values = [`%${name}%`, `%${email}%`, role];
  
  const result = await pool.query(query, values);
  return result.rows;
};
```

### XSS Prevention
```javascript
const DOMPurify = require('isomorphic-dompurify');

// Sanitize HTML content
const sanitizeHtml = (dirtyHtml) => {
  return DOMPurify.sanitize(dirtyHtml, {
    ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'a', 'p', 'br'],
    ALLOWED_ATTR: ['href', 'target']
  });
};

// Escape for HTML attributes
const escapeHtmlAttr = (str) => {
  return str
    .replace(/&/g, '&amp;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;');
};

// React automatically escapes, but for dangerouslySetInnerHTML:
const SafeContent = ({ html }) => {
  return (
    <div 
      dangerouslySetInnerHTML={{ 
        __html: DOMPurify.sanitize(html) 
      }} 
    />
  );
};
```

### Input Validation Patterns
```javascript
const validator = require('validator');

const validateUserInput = {
  email: (email) => {
    if (!validator.isEmail(email)) {
      throw new ValidationError('Invalid email format');
    }
    return validator.normalizeEmail(email);
  },
  
  url: (url) => {
    if (!validator.isURL(url, { protocols: ['http', 'https'] })) {
      throw new ValidationError('Invalid URL');
    }
    return url;
  },
  
  phoneNumber: (phone) => {
    if (!validator.isMobilePhone(phone, 'any')) {
      throw new ValidationError('Invalid phone number');
    }
    return phone;
  },
  
  sanitizeFilename: (filename) => {
    // Remove path traversal attempts
    return filename
      .replace(/\.\./g, '')
      .replace(/[\/\\]/g, '')
      .replace(/[^a-zA-Z0-9._-]/g, '');
  }
};
```

## CSRF Protection

### CSRF Token Implementation
```javascript
const csrf = require('csurf');

// CSRF middleware
const csrfProtection = csrf({ 
  cookie: {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict'
  }
});

// Apply to state-changing routes
app.use('/api', csrfProtection);

// Provide token to frontend
app.get('/api/csrf-token', (req, res) => {
  res.json({ csrfToken: req.csrfToken() });
});

// Frontend usage
const fetchWithCsrf = async (url, options = {}) => {
  const tokenResponse = await fetch('/api/csrf-token');
  const { csrfToken } = await tokenResponse.json();
  
  return fetch(url, {
    ...options,
    headers: {
      ...options.headers,
      'X-CSRF-Token': csrfToken
    }
  });
};
```

## Rate Limiting & DDoS Protection

### Advanced Rate Limiting
```javascript
const rateLimit = require('express-rate-limit');
const RedisStore = require('rate-limit-redis');

// Different limits for different endpoints
const createRateLimiter = (options) => {
  return rateLimit({
    store: new RedisStore({
      client: redisClient,
      prefix: 'rl:'
    }),
    ...options
  });
};

// Strict limit for auth endpoints
const authLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 requests per window
  message: 'Too many authentication attempts',
  standardHeaders: true,
  legacyHeaders: false
});

// More relaxed for API
const apiLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000,
  max: 100,
  message: 'Too many requests'
});

// Apply limiters
app.use('/auth', authLimiter);
app.use('/api', apiLimiter);
```

## File Upload Security

### Secure File Upload
```javascript
const multer = require('multer');
const path = require('path');
const crypto = require('crypto');

const fileUploadConfig = {
  // File type validation
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif|pdf/;
    const extname = allowedTypes.test(
      path.extname(file.originalname).toLowerCase()
    );
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Invalid file type'));
    }
  },
  
  // Size limits
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB
  },
  
  // Secure filename
  filename: (req, file, cb) => {
    const uniqueSuffix = crypto.randomBytes(16).toString('hex');
    const sanitizedName = file.originalname
      .replace(/[^a-zA-Z0-9.-]/g, '')
      .toLowerCase();
    
    cb(null, `${uniqueSuffix}-${sanitizedName}`);
  }
};

const upload = multer(fileUploadConfig);

// Virus scanning integration
const scanFile = async (filePath) => {
  // Integrate with ClamAV or similar
  const result = await clamav.scanFile(filePath);
  if (result.infected) {
    await fs.unlink(filePath);
    throw new Error('Malicious file detected');
  }
};
```

## API Security Headers

### Security Headers Configuration
```javascript
const helmet = require('helmet');

app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", 'data:', 'https:'],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"]
    }
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

// Additional security headers
app.use((req, res, next) => {
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
  next();
});
```

## Secrets Management

### Environment Variables Pattern
```javascript
const requiredEnvVars = [
  'JWT_SECRET',
  'DATABASE_URL',
  'REDIS_URL',
  'SESSION_SECRET'
];

// Validate on startup
const validateEnvironment = () => {
  const missing = requiredEnvVars.filter(
    varName => !process.env[varName]
  );
  
  if (missing.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missing.join(', ')}`
    );
  }
  
  // Validate format
  if (process.env.JWT_SECRET.length < 32) {
    throw new Error('JWT_SECRET must be at least 32 characters');
  }
};

// Secure configuration object
const config = {
  jwt: {
    secret: process.env.JWT_SECRET,
    expiresIn: process.env.JWT_EXPIRES_IN || '1h'
  },
  database: {
    url: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production'
  },
  // Never log sensitive values
  toJSON() {
    return {
      ...this,
      jwt: { ...this.jwt, secret: '[REDACTED]' },
      database: { ...this.database, url: '[REDACTED]' }
    };
  }
};
```

## LEVER Optimizations

### Leverage
- Use established security libraries
- Follow OWASP guidelines
- Reuse validation patterns

### Extend
- Build on framework security
- Extend authentication systems
- Add to security middleware

### Verify
- Security testing
- Penetration testing
- Vulnerability scanning

### Eliminate
- Remove security vulnerabilities
- Consolidate auth logic
- Unify validation

### Reduce
- Minimize attack surface
- Simplify security rules
- Streamline auth flows