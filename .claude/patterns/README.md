# PIB-METHOD Pattern Library

Centralized, reusable patterns that all agents can reference to avoid duplication and ensure consistency.

## Purpose

Instead of each agent having its own copy of common patterns, this library provides:
- **Single source of truth** for implementation patterns
- **Consistent approaches** across all projects
- **Easy updates** - change once, applies everywhere
- **Reduced context** - agents reference patterns instead of storing them

## Available Patterns

### 🛡️ [Error Handling](./error-handling.md)
- API error handling
- Custom error classes
- Frontend error boundaries
- Database transaction rollbacks
- Validation error formatting

### 🌐 [API Patterns](./api-patterns.md)
- RESTful route structure
- Controller patterns
- Authentication middleware
- Query building
- API versioning
- GraphQL resolvers

### 🧪 [Testing Patterns](./testing-patterns.md)
- Unit test structure
- React component testing
- API integration tests
- E2E with Playwright
- Test data factories
- Performance testing

### 🔒 [Security Patterns](./security-patterns.md)
- Password hashing
- JWT implementation
- CSRF protection
- Input sanitization
- Rate limiting
- Security headers

## How Agents Use Patterns

Instead of implementing from scratch, agents reference patterns:

```markdown
## Agent Implementation

For error handling, I'll use the async error wrapper pattern from:
`/patterns/error-handling.md#express-async-wrapper`

For authentication, I'll implement the JWT middleware from:
`/patterns/security-patterns.md#jwt-middleware`
```

## Adding New Patterns

When you identify a pattern used across multiple projects:

1. **Create pattern file**: `patterns/new-pattern.md`
2. **Structure content**:
   - Pattern name and purpose
   - Implementation code
   - Usage examples
   - LEVER optimizations
3. **Update this README**
4. **Remove duplicates** from agents/plugins

## Pattern Guidelines

### Good Patterns
- Used in multiple places
- Well-tested approaches
- Framework-agnostic when possible
- Include error handling
- Follow LEVER principles

### Not Patterns
- Project-specific code
- One-off implementations
- Experimental approaches
- External library APIs
- Business logic

## Benefits

1. **Consistency**: Same patterns everywhere
2. **Maintenance**: Update once, applies globally
3. **Quality**: Tested, proven approaches
4. **Efficiency**: No reimplementation
5. **Learning**: New agents learn faster

## Usage in Projects

Agents automatically check patterns when implementing:

```javascript
// Agent thinking process:
// 1. Task: Implement error handling
// 2. Check: patterns/error-handling.md
// 3. Apply: Async wrapper pattern
// 4. Customize: Add project-specific errors
```

## LEVER Compliance

All patterns follow LEVER principles:
- **L**everage: Build on existing solutions
- **E**xtend: Easy to extend for specific needs
- **V**erify: Include testing approaches
- **E**liminate: No duplication
- **R**educe: Minimal, clean implementations

## Contributing Patterns

See a pattern used repeatedly? Add it!

1. Check it doesn't exist
2. Follow the structure
3. Include examples
4. Add LEVER optimizations
5. Update documentation

Together, we build a stronger foundation for all projects!