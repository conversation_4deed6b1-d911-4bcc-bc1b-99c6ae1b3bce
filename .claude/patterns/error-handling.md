# Error Handling Patterns

Common error handling patterns that all agents can reference.

## API Error Handling

### Express.js Pattern
```javascript
// Async error wrapper
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Global error handler
const errorHandler = (err, req, res, next) => {
  const status = err.status || 500;
  const message = err.message || 'Internal Server Error';
  
  // Log error
  console.error(`Error ${status}: ${message}`, err.stack);
  
  // Send response
  res.status(status).json({
    error: {
      message,
      ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
    }
  });
};
```

### Custom Error Classes
```javascript
class AppError extends Error {
  constructor(message, status = 500) {
    super(message);
    this.status = status;
    this.isOperational = true;
  }
}

class ValidationError extends AppError {
  constructor(message) {
    super(message, 400);
  }
}

class NotFoundError extends AppError {
  constructor(resource = 'Resource') {
    super(`${resource} not found`, 404);
  }
}

class UnauthorizedError extends AppError {
  constructor(message = 'Unauthorized') {
    super(message, 401);
  }
}
```

## Frontend Error Handling

### React Error Boundary
```typescript
class ErrorBoundary extends React.Component<Props, State> {
  state = { hasError: false, error: null };
  
  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }
  
  componentDidCatch(error: Error, info: ErrorInfo) {
    // Log to error reporting service
    console.error('React error:', error, info);
    logErrorToService(error, info);
  }
  
  render() {
    if (this.state.hasError) {
      return <ErrorFallback error={this.state.error} />;
    }
    
    return this.props.children;
  }
}
```

### Async Error Handling in Components
```typescript
const useAsyncError = () => {
  const [error, setError] = useState<Error | null>(null);
  
  const resetError = () => setError(null);
  
  const throwError = (error: Error) => {
    setError(error);
  };
  
  return { error, resetError, throwError };
};

// Usage
const MyComponent = () => {
  const { error, throwError, resetError } = useAsyncError();
  
  const fetchData = async () => {
    try {
      const data = await api.getData();
      // Process data
    } catch (err) {
      throwError(err);
    }
  };
  
  if (error) {
    return <ErrorDisplay error={error} onRetry={resetError} />;
  }
  
  // Normal render
};
```

## Database Error Handling

### Transaction Rollback Pattern
```javascript
const executeTransaction = async (operations) => {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    const results = [];
    for (const operation of operations) {
      results.push(await operation(client));
    }
    
    await client.query('COMMIT');
    return results;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
};
```

### Retry Pattern
```javascript
const retry = async (fn, retries = 3, delay = 1000) => {
  try {
    return await fn();
  } catch (error) {
    if (retries === 0) throw error;
    
    await new Promise(resolve => setTimeout(resolve, delay));
    return retry(fn, retries - 1, delay * 2);
  }
};

// Usage
const result = await retry(
  () => database.query('SELECT * FROM users'),
  3,  // max retries
  1000 // initial delay
);
```

## Validation Error Handling

### Input Validation Pattern
```javascript
const validateInput = (schema) => (req, res, next) => {
  const { error, value } = schema.validate(req.body, {
    abortEarly: false,
    stripUnknown: true
  });
  
  if (error) {
    const errors = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message
    }));
    
    return res.status(400).json({ errors });
  }
  
  req.validatedBody = value;
  next();
};
```

## Logging Patterns

### Structured Logging
```javascript
const logger = {
  error: (message, error, context = {}) => {
    console.error(JSON.stringify({
      level: 'error',
      message,
      error: {
        message: error.message,
        stack: error.stack,
        code: error.code
      },
      context,
      timestamp: new Date().toISOString()
    }));
  },
  
  warn: (message, context = {}) => {
    console.warn(JSON.stringify({
      level: 'warn',
      message,
      context,
      timestamp: new Date().toISOString()
    }));
  }
};
```

## LEVER Optimizations

### Leverage
- Use existing error handling middleware
- Extend standard Error class
- Reuse validation schemas

### Extend
- Build on framework error handlers
- Extend existing error boundaries
- Add to current logging

### Verify
- Test error scenarios
- Verify error messages
- Check error recovery

### Eliminate
- Remove duplicate error handling
- Consolidate error types
- Unify error responses

### Reduce
- Simplify error messages
- Minimize error types
- Streamline error flows