# Project-Specific Agent Registry

This file allows projects to register their custom agents for use with the Task tool and other PIB-METHOD features.

## How to Register Custom Agents

When you create a custom agent in your project (e.g., using `/workflows:create-tech-agent`), add it to your project's `.claude/project-agents.json` file:

```json
{
  "custom_agents": [
    {
      "name": "fabric-specialist",
      "type": "fabric-specialist",
      "description": "Fabric.js canvas manipulation expert",
      "path": ".claude/agents/tech-specialists/frontend/fabric-specialist.md",
      "tools": ["Read", "Write", "Edit", "MultiEdit", "Grep", "Glob"],
      "categories": ["frontend", "canvas", "graphics"]
    },
    {
      "name": "three-js-expert",
      "type": "three-js-expert", 
      "description": "Three.js 3D graphics specialist",
      "path": ".claude/agents/tech-specialists/frontend/three-js-expert.md",
      "tools": ["Read", "Write", "Edit", "MultiEdit", "Grep", "Glob"],
      "categories": ["frontend", "3d", "graphics"]
    }
  ]
}
```

## Integration with Task Tool

Once registered in `project-agents.json`, your custom agents can be used with the Task tool:

```javascript
Task({
  description: "Fix canvas initialization",
  prompt: "Fix the book editor canvas initialization issue...",
  subagent_type: "fabric-specialist"  // Your custom agent
})
```

## Auto-Discovery

The PIB-METHOD system will automatically discover agents in these locations:
1. Core agents in `.claude/agents/` (built-in)
2. Tech specialists in `.claude/agents/tech-specialists/` (created via commands)
3. Project-specific agents registered in `.claude/project-agents.json`

## Best Practices

1. **Naming Convention**: Use lowercase with hyphens (e.g., `fabric-specialist`, not `FabricSpecialist`)
2. **Clear Descriptions**: Provide clear descriptions for agent purpose
3. **Tool Access**: Only grant necessary tools to custom agents
4. **Categories**: Use categories to help with agent discovery and routing

## Example: Creating and Registering a Custom Agent

```bash
# Step 1: Create the agent
/workflows:create-tech-agent "Fabric.js" --research

# Step 2: Add to project-agents.json
{
  "custom_agents": [
    {
      "name": "fabric-specialist",
      "type": "fabric-specialist",
      "description": "Fabric.js canvas manipulation expert",
      "path": ".claude/agents/tech-specialists/frontend/fabric-specialist.md",
      "tools": ["Read", "Write", "Edit", "MultiEdit", "Grep", "Glob"],
      "categories": ["frontend", "canvas", "graphics"]
    }
  ]
}

# Step 3: Use in your workflow
Task({
  description: "Fix canvas issue",
  prompt: "...",
  subagent_type: "fabric-specialist"
})
```

## Troubleshooting

If your custom agent is not found:
1. Check that `project-agents.json` exists in your project's `.claude/` directory
2. Verify the JSON syntax is correct
3. Ensure the agent file exists at the specified path
4. Check that the `type` field matches what you're using in `subagent_type`

## Related Documentation
- `/workflows:create-tech-agent` - Create technology-specific agents
- `SUBAGENT-REFERENCE.md` - Core agent reference
- `CLAUDE.md` - Project configuration