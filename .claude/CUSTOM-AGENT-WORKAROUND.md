# Custom Agent Workaround Guide

Since <PERSON>'s Task tool has a fixed list of recognized agent types, here's how to effectively use your custom agents:

## The Issue
When you create a custom agent (e.g., `fabric-specialist`), the Task tool doesn't recognize it because the agent types are hardcoded in Claude Code.

## Workarounds

### 1. Embed Agent Instructions in Task Prompt

Instead of relying on `subagent_type`, include the agent's expertise in your prompt:

```javascript
// ❌ This won't work
Task({
  description: "Fix canvas issue",
  prompt: "Fix the book editor canvas initialization",
  subagent_type: "fabric-specialist"
})

// ✅ This works
Task({
  description: "Fix canvas issue", 
  prompt: `[Acting as Fabric.js Specialist]
  
  You are an expert in:
  - Fabric.js canvas manipulation
  - Object model and event handling
  - Performance optimization
  - Browser compatibility
  
  Task: Fix the book editor canvas initialization issue
  
  Specific areas to check:
  - Canvas context initialization
  - Event listener setup
  - Object rendering pipeline
  - Memory management`,
  subagent_type: "task-executor"
})
```

### 2. Create Agent Mapping Commands

Create slash commands that map to your custom agents:

```markdown
# .claude/commands/agents/fabric-specialist.md
---
description: Invoke Fabric.js canvas specialist
---

Use the task-executor agent with the following context:

You are a Fabric.js specialist with expertise in:
- Canvas manipulation and rendering
- Fabric.js object model
- Performance optimization
- Cross-browser compatibility

[Include full agent instructions from your fabric-specialist.md]
```

Then use: `/agents:fabric-specialist "Fix canvas initialization"`

### 3. Use Developer Agent with Context

The `dev-agent` is flexible and can be given specific context:

```javascript
Task({
  description: "Fix canvas issue",
  prompt: `[Context: This requires Fabric.js expertise]
  
  Refer to our Fabric.js patterns in:
  - .claude/agents/tech-specialists/frontend/fabric-specialist.md
  
  Fix the book editor canvas initialization following those patterns.`,
  subagent_type: "dev-agent"
})
```

### 4. Project-Level Agent Registry (Future Enhancement)

While we've created the infrastructure for `project-agents.json`, Claude Code doesn't yet read from it. For now, document your custom agents in your project's CLAUDE.md:

```markdown
# Project Custom Agents

## fabric-specialist
- **Purpose**: Fabric.js canvas manipulation expert
- **Location**: .claude/agents/tech-specialists/frontend/fabric-specialist.md
- **Usage**: Use task-executor with Fabric.js context
- **Expertise**: Canvas rendering, object model, events, performance

## three-js-expert  
- **Purpose**: Three.js 3D graphics specialist
- **Location**: .claude/agents/tech-specialists/frontend/three-js-expert.md
- **Usage**: Use dev-agent with Three.js context
- **Expertise**: 3D rendering, WebGL, shaders, animations
```

## Best Practices

1. **Rich Context**: Always provide detailed context in your Task prompts
2. **Reference Agent Files**: Point to your custom agent definitions
3. **Use Appropriate Base Agent**: Choose the closest matching core agent
4. **Document Usage**: Keep clear documentation on how to invoke each custom agent

## Example: Complete Custom Agent Usage

```javascript
// Step 1: Create your agent
// /workflows:create-tech-agent "Fabric.js" --research

// Step 2: Document in CLAUDE.md
// Add agent details and usage instructions

// Step 3: Use in your workflow
Task({
  description: "Fix canvas initialization - Fabric.js expert task",
  prompt: `[Fabric.js Specialist Context]
  
  Reference: .claude/agents/tech-specialists/frontend/fabric-specialist.md
  
  You are an expert in Fabric.js with deep knowledge of:
  - Canvas initialization and lifecycle
  - Object creation and manipulation
  - Event handling and interactions
  - Performance optimization
  - Memory management
  
  Issue: The book editor canvas is not initializing properly
  
  Requirements:
  1. Diagnose the initialization problem
  2. Fix the canvas setup code
  3. Ensure proper event handling
  4. Add error recovery
  5. Optimize for performance
  
  Use Fabric.js best practices and patterns.`,
  subagent_type: "task-executor"
})
```

## Future Enhancement

We're working on a solution to make Claude Code recognize project-specific agents. Until then, these workarounds provide effective ways to leverage your custom agents.