/**
 * CRM Reminder Notification System
 * Handles automated reminder notifications for CRM follow-ups
 */

import { ref, computed } from 'vue'
import {
  collection,
  query,
  where,
  getDocs,
  updateDoc,
  doc,
  Timestamp,
  serverTimestamp,
  orderBy,
  and,
  or,
  type QueryConstraint
} from 'firebase/firestore'
import { useFirebase } from './useFirebase'
import { useAuth } from './useAuth'
import { useNotifications } from './useNotifications'
import type { CRMFollowUp } from './useCRMInteractions'
import type { FirebaseCRMFollowUp } from './useFirebaseCRM'

export interface ReminderCheckResult {
  followUpsChecked: number
  remindersCreated: number
  errors: string[]
}

export interface ReminderSettings {
  checkInterval: number // in minutes
  advanceNotice: number // in minutes before due date
  overdueThreshold: number // in minutes after due date
  enableRecurring: boolean
  notificationPersistence: boolean
}

export const useCRMReminders = () => {
  // Get dependencies
  const { firestore } = useFirebase()
  const { user } = useAuth()
  const notifications = useNotifications()

  // State
  const isChecking = ref(false)
  const lastCheck = ref<Date | null>(null)
  const checkInterval = ref<NodeJS.Timeout | null>(null)
  const error = ref<string | null>(null)

  // Default reminder settings
  const defaultSettings: ReminderSettings = {
    checkInterval: 30, // Check every 30 minutes
    advanceNotice: 60, // Notify 1 hour before due
    overdueThreshold: 0, // Notify immediately when overdue
    enableRecurring: true,
    notificationPersistence: true
  }

  const settings = ref<ReminderSettings>({ ...defaultSettings })

  // Helper to convert Firestore timestamp to Date
  const timestampToDate = (timestamp: Timestamp | Date): Date => {
    if (timestamp instanceof Timestamp) {
      return timestamp.toDate()
    }
    return timestamp
  }

  /**
   * Get business card details for a follow-up
   */
  const getBusinessCardDetails = async (cardId: string): Promise<{ name?: string; company?: string } | null> => {
    if (!firestore) return null

    try {
      const cardDoc = await getDocs(
        query(collection(firestore, 'business-cards'), where('__name__', '==', cardId))
      )

      if (!cardDoc.empty) {
        const cardData = cardDoc.docs[0].data()
        return {
          name: cardData.fullName || cardData.name || 'Unknown Contact',
          company: cardData.company || ''
        }
      }
    } catch (err) {
      console.error('Error fetching business card details:', err)
    }

    return null
  }

  /**
   * Send a reminder notification for a follow-up
   */
  const sendReminderNotification = async (
    followUp: CRMFollowUp & { cardDetails?: { name?: string; company?: string } }
  ): Promise<void> => {
    const dueDate = new Date(followUp.dueDate)
    const now = new Date()
    const diffMs = dueDate.getTime() - now.getTime()
    const isOverdue = diffMs < 0
    const diffMinutes = Math.abs(Math.floor(diffMs / (1000 * 60)))
    const diffHours = Math.floor(diffMinutes / 60)
    const diffDays = Math.floor(diffHours / 24)

    // Format time difference
    let timeStatus = ''
    if (isOverdue) {
      if (diffDays > 0) {
        timeStatus = `${diffDays} day${diffDays > 1 ? 's' : ''} overdue`
      } else if (diffHours > 0) {
        timeStatus = `${diffHours} hour${diffHours > 1 ? 's' : ''} overdue`
      } else {
        timeStatus = `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} overdue`
      }
    } else {
      if (diffDays > 0) {
        timeStatus = `Due in ${diffDays} day${diffDays > 1 ? 's' : ''}`
      } else if (diffHours > 0) {
        timeStatus = `Due in ${diffHours} hour${diffHours > 1 ? 's' : ''}`
      } else {
        timeStatus = `Due in ${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`
      }
    }

    // Format contact info
    const contactName = followUp.cardDetails?.name || 'Unknown Contact'
    const company = followUp.cardDetails?.company
    const contactInfo = company ? `${contactName} (${company})` : contactName

    // Determine notification type based on priority and overdue status
    const notificationType = isOverdue ? 'warning' : 'info'
    const priorityEmoji = followUp.priority === 'high' ? '🔴' : followUp.priority === 'medium' ? '🟡' : '🟢'

    // Create notification
    notifications.addNotification({
      type: notificationType,
      title: `${priorityEmoji} ${followUp.type.charAt(0).toUpperCase() + followUp.type.slice(1)} Reminder`,
      message: `${followUp.title} with ${contactInfo} - ${timeStatus}`,
      duration: settings.value.notificationPersistence ? 0 : 10000,
      icon: getFollowUpIcon(followUp.type),
      actions: [
        {
          label: 'View',
          action: () => {
            // Navigate to the business card or follow-up details
            navigateTo(`/businesscards/${followUp.cardId}`)
          },
          style: 'primary'
        },
        {
          label: 'Mark Complete',
          action: async () => {
            await markFollowUpComplete(followUp.cardId, followUp.id)
          },
          style: 'secondary'
        }
      ]
    })
  }

  /**
   * Get icon for follow-up type
   */
  const getFollowUpIcon = (type: string): string => {
    const icons: Record<string, string> = {
      call: 'mdi:phone',
      email: 'mdi:email',
      meeting: 'mdi:calendar-account',
      task: 'mdi:check-circle',
      reminder: 'mdi:bell'
    }
    return icons[type] || 'mdi:calendar-check'
  }

  /**
   * Mark a follow-up as having sent a reminder
   */
  const markReminderSent = async (cardId: string, followUpId: string): Promise<void> => {
    if (!firestore) return

    try {
      const docRef = doc(firestore, `business-cards/${cardId}/follow-ups`, followUpId)
      await updateDoc(docRef, {
        reminderSent: true,
        reminderTime: serverTimestamp(),
        updatedAt: serverTimestamp()
      })
    } catch (err) {
      console.error('Error marking reminder as sent:', err)
      throw err
    }
  }

  /**
   * Mark a follow-up as complete
   */
  const markFollowUpComplete = async (cardId: string, followUpId: string): Promise<void> => {
    if (!firestore) return

    try {
      const docRef = doc(firestore, `business-cards/${cardId}/follow-ups`, followUpId)
      await updateDoc(docRef, {
        completed: true,
        completedAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      })

      notifications.success('Follow-up marked as complete')
    } catch (err) {
      console.error('Error marking follow-up as complete:', err)
      notifications.error('Failed to mark follow-up as complete')
    }
  }

  /**
   * Check and send reminders for due follow-ups
   */
  const checkAndSendReminders = async (): Promise<ReminderCheckResult> => {
    if (!firestore || !user.value?.uid) {
      return {
        followUpsChecked: 0,
        remindersCreated: 0,
        errors: ['User not authenticated or Firebase not initialized']
      }
    }

    isChecking.value = true
    error.value = null

    const result: ReminderCheckResult = {
      followUpsChecked: 0,
      remindersCreated: 0,
      errors: []
    }

    try {
      const now = new Date()
      const advanceTime = new Date(now.getTime() + (settings.value.advanceNotice * 60 * 1000))
      const overdueTime = new Date(now.getTime() - (settings.value.overdueThreshold * 60 * 1000))

      // Get all business cards
      const cardsSnapshot = await getDocs(
        query(collection(firestore, 'business-cards'))
      )

      // Process each card's follow-ups
      for (const cardDoc of cardsSnapshot.docs) {
        const cardId = cardDoc.id
        const cardData = cardDoc.data()

        try {
          // Query for follow-ups that:
          // 1. Are not completed
          // 2. Haven't had reminders sent
          // 3. Are either due soon or overdue
          const followUpsRef = collection(firestore, `business-cards/${cardId}/follow-ups`)
          const constraints: QueryConstraint[] = [
            where('completed', '==', false),
            where('reminderSent', '==', false),
            where('userId', '==', user.value.uid),
            orderBy('dueDate', 'asc')
          ]

          const followUpsSnapshot = await getDocs(query(followUpsRef, ...constraints))

          for (const followUpDoc of followUpsSnapshot.docs) {
            result.followUpsChecked++

            const followUpData = followUpDoc.data() as FirebaseCRMFollowUp
            const dueDate = timestampToDate(followUpData.dueDate)

            // Check if reminder should be sent
            const shouldSendReminder = 
              (dueDate <= advanceTime && dueDate > now) || // Due soon
              (dueDate <= now && dueDate >= overdueTime) // Overdue but within threshold

            if (shouldSendReminder) {
              try {
                // Convert to CRMFollowUp format
                const followUp: CRMFollowUp & { cardDetails?: any } = {
                  id: followUpDoc.id,
                  cardId,
                  userId: followUpData.userId,
                  type: followUpData.type,
                  title: followUpData.title,
                  description: followUpData.description,
                  dueDate,
                  completed: followUpData.completed,
                  recurring: followUpData.recurring,
                  reminderSent: followUpData.reminderSent,
                  priority: followUpData.priority,
                  metadata: followUpData.metadata,
                  cardDetails: {
                    name: cardData.fullName || cardData.name || 'Unknown Contact',
                    company: cardData.company || ''
                  }
                }

                // Send reminder notification
                await sendReminderNotification(followUp)

                // Mark reminder as sent
                await markReminderSent(cardId, followUpDoc.id)

                result.remindersCreated++
              } catch (err) {
                console.error('Error sending reminder:', err)
                result.errors.push(`Failed to send reminder for follow-up ${followUpDoc.id}`)
              }
            }
          }
        } catch (err) {
          console.error(`Error processing follow-ups for card ${cardId}:`, err)
          result.errors.push(`Error processing card ${cardId}`)
        }
      }

      lastCheck.value = new Date()
    } catch (err) {
      console.error('Error checking reminders:', err)
      error.value = 'Failed to check reminders'
      result.errors.push('General error checking reminders')
    } finally {
      isChecking.value = false
    }

    return result
  }

  /**
   * Schedule periodic reminder checks
   */
  const scheduleReminders = (customInterval?: number) => {
    // Clear existing interval
    if (checkInterval.value) {
      clearInterval(checkInterval.value)
      checkInterval.value = null
    }

    const intervalMinutes = customInterval || settings.value.checkInterval
    const intervalMs = intervalMinutes * 60 * 1000

    // Schedule periodic checks
    checkInterval.value = setInterval(async () => {
      try {
        const result = await checkAndSendReminders()
        
        if (result.remindersCreated > 0) {
          console.log(`Sent ${result.remindersCreated} reminder notifications`)
        }

        if (result.errors.length > 0) {
          console.error('Reminder check errors:', result.errors)
        }
      } catch (err) {
        console.error('Error in scheduled reminder check:', err)
      }
    }, intervalMs)

    // Run an initial check
    checkAndSendReminders()
  }

  /**
   * Stop scheduled reminders
   */
  const stopScheduledReminders = () => {
    if (checkInterval.value) {
      clearInterval(checkInterval.value)
      checkInterval.value = null
    }
  }

  /**
   * Update reminder settings
   */
  const updateSettings = (newSettings: Partial<ReminderSettings>) => {
    settings.value = { ...settings.value, ...newSettings }

    // Restart scheduling if interval changed
    if (newSettings.checkInterval !== undefined) {
      scheduleReminders()
    }
  }

  /**
   * Get reminder statistics
   */
  const getReminderStats = async (): Promise<{
    totalPending: number
    dueSoon: number
    overdue: number
    highPriority: number
  }> => {
    if (!firestore || !user.value?.uid) {
      return { totalPending: 0, dueSoon: 0, overdue: 0, highPriority: 0 }
    }

    const stats = {
      totalPending: 0,
      dueSoon: 0,
      overdue: 0,
      highPriority: 0
    }

    try {
      const now = new Date()
      const oneDayFromNow = new Date(now.getTime() + (24 * 60 * 60 * 1000))

      // Get all business cards
      const cardsSnapshot = await getDocs(
        query(collection(firestore, 'business-cards'))
      )

      for (const cardDoc of cardsSnapshot.docs) {
        const followUpsRef = collection(firestore, `business-cards/${cardDoc.id}/follow-ups`)
        const followUpsSnapshot = await getDocs(
          query(
            followUpsRef,
            where('completed', '==', false),
            where('userId', '==', user.value.uid)
          )
        )

        for (const followUpDoc of followUpsSnapshot.docs) {
          const data = followUpDoc.data() as FirebaseCRMFollowUp
          const dueDate = timestampToDate(data.dueDate)

          stats.totalPending++

          if (dueDate < now) {
            stats.overdue++
          } else if (dueDate <= oneDayFromNow) {
            stats.dueSoon++
          }

          if (data.priority === 'high') {
            stats.highPriority++
          }
        }
      }
    } catch (err) {
      console.error('Error getting reminder stats:', err)
    }

    return stats
  }

  // Clean up on unmount
  onUnmounted(() => {
    stopScheduledReminders()
  })

  return {
    // State
    isChecking: computed(() => isChecking.value),
    lastCheck: computed(() => lastCheck.value),
    error: computed(() => error.value),
    settings: computed(() => settings.value),

    // Methods
    checkAndSendReminders,
    sendReminderNotification,
    scheduleReminders,
    stopScheduledReminders,
    updateSettings,
    markFollowUpComplete,
    getReminderStats
  }
}