#!/usr/bin/env node

/**
 * Seed Development Data for Covalonic CRM
 * Creates comprehensive test data for all CRM features
 */

const admin = require('firebase-admin');
const { faker } = require('@faker-js/faker');
const path = require('path');

// Initialize Firebase Admin with emulator settings (matching firebase.json config)
process.env.FIRESTORE_EMULATOR_HOST = 'localhost:8081';
process.env.FIREBASE_AUTH_EMULATOR_HOST = 'localhost:9098';
process.env.FIREBASE_STORAGE_EMULATOR_HOST = 'localhost:9198';

// Initialize admin SDK
admin.initializeApp({
  projectId: 'covalonic-7127a',
  storageBucket: 'covalonic-7127a.appspot.com'
});

const db = admin.firestore();
const auth = admin.auth();
const storage = admin.storage();

// Configuration
const CONFIG = {
  users: 15,
  cardsPerUser: 7,
  unclaimedRatio: 0.4,
  categories: [
    { name: 'Technology', subcategories: ['Software', 'Hardware', 'AI/ML', 'Cybersecurity'] },
    { name: 'Healthcare', subcategories: ['Medical Devices', 'Pharmaceuticals', 'Digital Health'] },
    { name: 'Finance', subcategories: ['Banking', 'Insurance', 'Fintech', 'Investment'] },
    { name: 'Marketing', subcategories: ['Digital Marketing', 'Advertising', 'PR', 'Content'] },
    { name: 'Real Estate', subcategories: ['Commercial', 'Residential', 'Property Management'] }
  ],
  countries: [
    { code: 'US', name: 'United States', regions: ['California', 'New York', 'Texas', 'Florida'] },
    { code: 'UK', name: 'United Kingdom', regions: ['England', 'Scotland', 'Wales'] },
    { code: 'CA', name: 'Canada', regions: ['Ontario', 'Quebec', 'British Columbia'] },
    { code: 'AU', name: 'Australia', regions: ['New South Wales', 'Victoria', 'Queensland'] }
  ]
};

// Helper functions
function getRandomElement(array) {
  return array[Math.floor(Math.random() * array.length)];
}

function generateGeoHash(lat, lng) {
  // Simplified geohash for demo purposes
  return `${Math.floor(lat)}_${Math.floor(lng)}`;
}

function generateBusinessCard(userId, uploaderId, claimed = true) {
  const country = getRandomElement(CONFIG.countries);
  const category = getRandomElement(CONFIG.categories);
  const lat = faker.location.latitude();
  const lng = faker.location.longitude();

  return {
    userId: claimed ? userId : null,
    uploaderId: uploaderId,
    name: faker.person.fullName(),
    email: faker.internet.email(),
    phone: faker.phone.number(),
    company: faker.company.name(),
    title: faker.person.jobTitle(),
    website: faker.internet.url(),
    address: {
      street: faker.location.streetAddress(),
      city: faker.location.city(),
      state: getRandomElement(country.regions),
      country: country.name,
      countryCode: country.code,
      postalCode: faker.location.zipCode()
    },
    location: {
      lat,
      lng,
      geoHash: generateGeoHash(lat, lng)
    },
    category: category.name,
    subCategory: getRandomElement(category.subcategories),
    status: claimed ? 'claimed' : 'unclaimed',
    visibility: faker.helpers.arrayElement(['public', 'private', 'connections']),
    tags: faker.helpers.arrayElements(['supplier', 'client', 'partner', 'lead', 'vendor'], 3),
    createdAt: admin.firestore.FieldValue.serverTimestamp(),
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    claimedAt: claimed ? admin.firestore.FieldValue.serverTimestamp() : null,
    verifiedEmail: claimed ? faker.datatype.boolean() : false,
    imageUrl: `https://api.dicebear.com/7.x/professional/svg?seed=${faker.string.alphanumeric(10)}`
  };
}

function generateCRMData(cardId, userId) {
  const interactions = [];
  const followUps = [];
  
  // Generate 3-8 interactions per card
  const interactionCount = faker.number.int({ min: 3, max: 8 });
  for (let i = 0; i < interactionCount; i++) {
    interactions.push({
      id: faker.string.uuid(),
      cardId,
      userId,
      type: faker.helpers.arrayElement(['note', 'meeting', 'call', 'email']),
      content: faker.lorem.paragraph(),
      timestamp: faker.date.recent({ days: 90 }),
      attachments: faker.datatype.boolean() ? [`attachment_${i}.pdf`] : []
    });
  }

  // Generate 0-3 follow-ups per card
  const followUpCount = faker.number.int({ min: 0, max: 3 });
  for (let i = 0; i < followUpCount; i++) {
    followUps.push({
      id: faker.string.uuid(),
      cardId,
      userId,
      type: faker.helpers.arrayElement(['call', 'email', 'meeting', 'task']),
      title: faker.lorem.sentence(5),
      description: faker.lorem.sentences(2),
      dueDate: faker.date.soon({ days: 30 }),
      completed: faker.datatype.boolean(),
      recurring: faker.helpers.arrayElement([null, 'weekly', 'monthly']),
      reminderSent: false
    });
  }

  return { interactions, followUps };
}

// Main seeding function
async function seedDatabase() {
  console.log('🌱 Starting database seeding...\n');

  try {
    // Clear existing data
    console.log('🗑️  Clearing existing data...');
    const collections = ['users', 'business-cards', 'interactions', 'follow-ups'];
    for (const collection of collections) {
      const snapshot = await db.collection(collection).get();
      const batch = db.batch();
      snapshot.docs.forEach(doc => batch.delete(doc.ref));
      await batch.commit();
    }

    // Create users
    console.log('\n👥 Creating users...');
    const users = [];
    const authUsers = [];

    for (let i = 0; i < CONFIG.users; i++) {
      const email = faker.internet.email();
      const uid = faker.string.uuid();
      
      // Create auth user
      try {
        const authUser = await auth.createUser({
          uid,
          email,
          password: 'testpass123',
          displayName: faker.person.fullName(),
          emailVerified: true
        });
        authUsers.push(authUser);

        // Create Firestore user document
        const userData = {
          uid,
          email,
          displayName: authUser.displayName,
          photoURL: `https://api.dicebear.com/7.x/avatars/svg?seed=${uid}`,
          subscription: faker.helpers.arrayElement(['free', 'premium', 'enterprise']),
          role: i === 0 ? 'admin' : 'user',
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          lastLogin: admin.firestore.FieldValue.serverTimestamp(),
          settings: {
            notifications: {
              email: true,
              push: true,
              followUpReminders: true
            },
            privacy: {
              profileVisibility: 'public',
              showEmail: faker.datatype.boolean()
            }
          }
        };

        await db.collection('users').doc(uid).set(userData);
        users.push({ ...userData, uid });
        console.log(`  ✓ Created user ${i + 1}/${CONFIG.users}: ${email}`);
      } catch (error) {
        console.error(`  ✗ Error creating user ${email}:`, error.message);
      }
    }

    // Create business cards
    console.log('\n💳 Creating business cards...');
    let totalCards = 0;
    let unclaimedCards = 0;

    for (const user of users) {
      const cardCount = faker.number.int({ min: 5, max: 10 });
      
      for (let i = 0; i < cardCount; i++) {
        const isUnclaimed = Math.random() < CONFIG.unclaimedRatio;
        const card = generateBusinessCard(
          isUnclaimed ? null : user.uid,
          user.uid,
          !isUnclaimed
        );

        const cardRef = await db.collection('business-cards').add(card);
        totalCards++;
        if (isUnclaimed) unclaimedCards++;

        // Add CRM data for claimed cards
        if (!isUnclaimed) {
          const { interactions, followUps } = generateCRMData(cardRef.id, user.uid);
          
          // Add interactions
          for (const interaction of interactions) {
            await db.collection('interactions').add({
              ...interaction,
              cardId: cardRef.id
            });
          }

          // Add follow-ups
          for (const followUp of followUps) {
            await db.collection('follow-ups').add({
              ...followUp,
              cardId: cardRef.id
            });
          }
        }
      }
      console.log(`  ✓ Created ${cardCount} cards for ${user.email}`);
    }

    // Create some sample categories in a separate collection
    console.log('\n📁 Creating categories...');
    for (const category of CONFIG.categories) {
      await db.collection('categories').doc(category.name).set({
        name: category.name,
        subcategories: category.subcategories,
        cardCount: 0,
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      });
    }

    // Create sample email templates
    console.log('\n✉️  Creating email templates...');
    const emailTemplates = [
      {
        name: 'claim-verification',
        subject: 'Verify Your Business Card Claim',
        body: 'Click here to verify your claim: {{verificationLink}}'
      },
      {
        name: 'follow-up-reminder',
        subject: 'Follow-up Reminder: {{contactName}}',
        body: 'You have a scheduled follow-up with {{contactName}} - {{company}}'
      },
      {
        name: 'card-claimed-notification',
        subject: 'Your uploaded card has been claimed',
        body: '{{claimerName}} has claimed the business card you uploaded.'
      }
    ];

    for (const template of emailTemplates) {
      await db.collection('email-templates').doc(template.name).set(template);
    }

    // Summary
    console.log('\n✅ Seeding completed successfully!\n');
    console.log('📊 Summary:');
    console.log(`  - Users created: ${users.length}`);
    console.log(`  - Total cards: ${totalCards}`);
    console.log(`  - Claimed cards: ${totalCards - unclaimedCards}`);
    console.log(`  - Unclaimed cards: ${unclaimedCards}`);
    console.log(`  - Categories: ${CONFIG.categories.length}`);
    console.log('\n🚀 Development environment is ready!');
    console.log('📝 Default password for all users: testpass123');
    console.log('👤 Admin user: ' + users[0].email);

  } catch (error) {
    console.error('\n❌ Seeding failed:', error);
    process.exit(1);
  }
}

// Run seeding
seedDatabase()
  .then(() => process.exit(0))
  .catch(error => {
    console.error(error);
    process.exit(1);
  });