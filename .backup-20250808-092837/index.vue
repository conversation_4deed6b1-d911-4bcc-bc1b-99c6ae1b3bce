<template>
  <div class="min-h-screen font-sans relative overflow-hidden">
    <!-- Dark gradient background -->
    <div class="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900"></div>

    <!-- Decorative elements -->
    <div
      class="absolute top-20 right-20 w-96 h-96 bg-gradient-to-br from-red-400/20 to-purple-400/20 rounded-full blur-3xl"
    ></div>
    <div
      class="absolute bottom-40 left-20 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-red-400/20 rounded-full blur-3xl"
    ></div>
    <div
      class="absolute top-1/2 right-1/3 w-64 h-64 bg-gradient-to-br from-purple-400/15 to-pink-400/15 rounded-full blur-2xl"
    ></div>

    <role-guard role="Admin" show-access-denied redirect-to="/c/dashboard">
      <div class="relative z-10 px-4 md:px-6 py-4 md:py-8 space-y-8">
        <!-- Header with actions -->
        <div class="max-w-screen-xl mx-auto">
          <div
            class="bg-gradient-to-br from-gray-900/90 via-gray-800/90 to-gray-900/90 backdrop-blur-sm rounded-2xl p-4 md:p-8 border border-white/10 shadow-2xl"
          >
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
              <div>
                <div class="flex items-center mb-4">
                  <div
                    class="w-12 h-12 bg-gradient-to-br from-red-500 to-purple-600 rounded-xl flex items-center justify-center mr-4 shadow-lg"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke-width="1.5"
                      stroke="currentColor"
                      class="h-6 w-6 text-white"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h1 class="text-3xl font-bold text-white">Admin Dashboard</h1>
                    <p class="text-red-400 font-medium">System Management & Analytics</p>
                  </div>
                </div>
                <p class="text-gray-300 max-w-3xl leading-relaxed">
                  Comprehensive overview of your application's performance, user activity, and
                  administrative controls.
                </p>
              </div>
              <div class="flex items-center space-x-3 mt-6 md:mt-0">
                <button
                  class="px-6 py-3 bg-gradient-to-r from-red-600 to-purple-600 hover:from-red-700 hover:to-purple-700 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-opacity-50 shadow-lg hover:shadow-xl flex items-center"
                  :class="{ 'opacity-50 cursor-not-allowed': isRefreshing }"
                  :disabled="isRefreshing"
                  @click="refreshAllData"
                >
                  <Icon
                    name="mdi:refresh"
                    class="h-5 w-5 mr-2"
                    :class="{ 'animate-spin': isRefreshing }"
                  />
                  <span class="hidden sm:inline">{{
                    isRefreshing ? 'Refreshing...' : 'Refresh Data'
                  }}</span>
                  <span class="sm:hidden">{{ isRefreshing ? 'Refreshing...' : 'Data' }}</span>
                </button>
                <NuxtLink
                  to="/c/dashboard"
                  class="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 shadow-lg hover:shadow-xl flex items-center"
                >
                  <Icon name="mdi:arrow-left" class="h-5 w-5 mr-2" />
                  <span class="hidden sm:inline">Back to Dashboard</span>
                  <span class="sm:hidden">Back</span>
                </NuxtLink>
              </div>
            </div>
          </div>
        </div>

        <!-- Key metrics section -->
        <section aria-labelledby="metrics-heading" class="max-w-screen-xl mx-auto">
          <div
            class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-2xl p-4 md:p-8 border border-white/10"
          >
            <div class="flex items-center mb-6">
              <div
                class="w-8 h-8 bg-gradient-to-br from-red-500 to-purple-600 rounded-lg flex items-center justify-center mr-3 shadow-lg"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="h-4 w-4 text-white"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z"
                  />
                </svg>
              </div>
              <h2 id="metrics-heading" class="text-xl font-bold text-white">
                Key Performance Metrics
              </h2>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <!-- User Metrics -->
              <UiAdminStatCard
                title="Total Users"
                :value="userMetrics.total.value"
                icon="mdi:account-multiple"
                color="blue"
                suffix="users"
                :format="{ notation: 'compact' }"
                link-text="View all users"
                link-url="/c/admin/users"
              />
              <UiAdminStatCard
                title="Active Users"
                :value="userMetrics.active.value"
                :trend="userMetrics.active.trend"
                icon="mdi:account-check"
                color="green"
                suffix="active"
                :format="{ notation: 'compact' }"
                description="Users active in the last 30 days"
              />
              <UiAdminStatCard
                title="New Registrations"
                :value="userMetrics.newRegistrations.value"
                :trend="userMetrics.newRegistrations.trend"
                icon="mdi:account-plus"
                color="purple"
                suffix="new"
                description="New users in the last 7 days"
              />

              <!-- Content Metrics -->
              <UiAdminStatCard
                title="Total Content"
                :value="contentMetrics.totalUploads.value"
                icon="mdi:file-document-multiple"
                color="indigo"
                suffix="items"
                :format="{ notation: 'compact' }"
              />
              <UiAdminStatCard
                title="Pending Approval"
                :value="contentMetrics.pendingApproval.value"
                :trend="contentMetrics.pendingApproval.trend"
                icon="mdi:clock-outline"
                color="orange"
                suffix="items"
                link-text="Review pending content"
                link-url="/c/admin/moderation"
              />
              <UiAdminStatCard
                title="Recently Approved"
                :value="contentMetrics.recentlyApproved.value"
                :trend="contentMetrics.recentlyApproved.trend"
                icon="mdi:check-circle"
                color="teal"
                suffix="items"
                description="Content approved in the last 7 days"
              />
            </div>
          </div>
        </section>

        <!-- Two-column layout for system status and quick actions -->
        <div class="max-w-screen-xl mx-auto">
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- System status column - takes up 2/3 of the space on large screens -->
            <section aria-labelledby="system-status-heading" class="lg:col-span-2">
              <div
                class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-2xl p-4 md:p-8 border border-white/10 h-full"
              >
                <UiSystemStatus
                  :services="systemHealth.services"
                  :metrics="systemHealth.metrics"
                  :last-updated="systemHealth.lastUpdated"
                  :loading="systemHealthLoading"
                  @refresh="fetchSystemHealth"
                />
              </div>
            </section>

            <!-- Quick actions column - takes up 1/3 of the space on large screens -->
            <section aria-labelledby="quick-actions-heading" class="space-y-6">
              <div
                class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-2xl p-6 border border-white/10"
              >
                <div class="flex items-center mb-6">
                  <div
                    class="w-8 h-8 bg-gradient-to-br from-red-500 to-purple-600 rounded-lg flex items-center justify-center mr-3 shadow-lg"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke-width="1.5"
                      stroke="currentColor"
                      class="h-4 w-4 text-white"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5a2.25 2.25 0 002.25-2.25m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5a2.25 2.25 0 012.25 2.25v7.5"
                      />
                    </svg>
                  </div>
                  <h3 class="text-lg font-bold text-white">Quick Actions</h3>
                </div>

                <div class="space-y-4">
                  <div class="bg-gradient-to-br from-blue-900/20 via-blue-800/20 to-blue-900/20 backdrop-blur-sm rounded-lg p-4 border border-blue-400/20">
                    <div class="flex items-start">
                      <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3 shadow-lg flex-shrink-0">
                        <Icon name="mdi:account-cog" class="h-5 w-5 text-white" />
                      </div>
                      <div class="flex-1">
                        <h4 class="text-sm font-semibold text-white mb-1">User Management</h4>
                        <p class="text-xs text-gray-400 mb-3">Manage users, roles and permissions</p>
                        <button
                          class="inline-flex items-center px-3 py-1 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white text-xs font-medium rounded-md transition-all duration-200"
                          @click="navigateTo('/c/admin/users')"
                        >
                          <Icon name="mdi:account-group" class="h-3 w-3 mr-1" />
                          Manage users
                        </button>
                      </div>
                    </div>
                  </div>

                  <div class="bg-gradient-to-br from-orange-900/20 via-orange-800/20 to-orange-900/20 backdrop-blur-sm rounded-lg p-4 border border-orange-400/20">
                    <div class="flex items-start">
                      <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mr-3 shadow-lg flex-shrink-0">
                        <Icon name="mdi:file-document-edit" class="h-5 w-5 text-white" />
                      </div>
                      <div class="flex-1">
                        <h4 class="text-sm font-semibold text-white mb-1">Content Moderation</h4>
                        <p class="text-xs text-gray-400 mb-3">Review and manage pending content</p>
                        <NuxtLink
                          to="/c/admin/moderation"
                          class="inline-flex items-center px-3 py-1 bg-gradient-to-r from-orange-600 to-orange-700 hover:from-orange-700 hover:to-orange-800 text-white text-xs font-medium rounded-md transition-all duration-200"
                        >
                          <Icon name="mdi:eye" class="h-3 w-3 mr-1" />
                          Go to moderation
                        </NuxtLink>
                      </div>
                    </div>
                  </div>

                  <div class="bg-gradient-to-br from-purple-900/20 via-purple-800/20 to-purple-900/20 backdrop-blur-sm rounded-lg p-4 border border-purple-400/20">
                    <div class="flex items-start">
                      <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-3 shadow-lg flex-shrink-0">
                        <Icon name="mdi:card-account-details" class="h-5 w-5 text-white" />
                      </div>
                      <div class="flex-1">
                        <h4 class="text-sm font-semibold text-white mb-1">Business Card Moderation</h4>
                        <p class="text-xs text-gray-400 mb-3">Review and approve business cards for public display</p>
                        <NuxtLink
                          to="/c/admin/business-cards"
                          class="inline-flex items-center px-3 py-1 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white text-xs font-medium rounded-md transition-all duration-200"
                        >
                          <Icon name="mdi:eye-check" class="h-3 w-3 mr-1" />
                          Review business cards
                        </NuxtLink>
                      </div>
                    </div>
                  </div>

                  <div class="bg-gradient-to-br from-indigo-900/20 via-indigo-800/20 to-indigo-900/20 backdrop-blur-sm rounded-lg p-4 border border-indigo-400/20">
                    <div class="flex items-start">
                      <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center mr-3 shadow-lg flex-shrink-0">
                        <Icon name="mdi:bell" class="h-5 w-5 text-white" />
                      </div>
                      <div class="flex-1">
                        <h4 class="text-sm font-semibold text-white mb-1">Push Notifications</h4>
                        <p class="text-xs text-gray-400 mb-3">Send notifications to your users</p>
                        <NuxtLink
                          to="/c/admin/notifications"
                          class="inline-flex items-center px-3 py-1 bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-700 hover:to-indigo-800 text-white text-xs font-medium rounded-md transition-all duration-200"
                        >
                          <Icon name="mdi:send" class="h-3 w-3 mr-1" />
                          Manage notifications
                        </NuxtLink>
                      </div>
                    </div>
                  </div>

                  <div class="bg-gradient-to-br from-blue-900/20 via-blue-800/20 to-blue-900/20 backdrop-blur-sm rounded-lg p-4 border border-blue-400/20">
                    <div class="flex items-start">
                      <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3 shadow-lg flex-shrink-0">
                        <Icon name="mdi:view-dashboard-outline" class="h-5 w-5 text-white" />
                      </div>
                      <div class="flex-1">
                        <h4 class="text-sm font-semibold text-white mb-1">Ad Spot Management</h4>
                        <p class="text-xs text-gray-400 mb-3">Manage advertising spots and subscriptions</p>
                        <NuxtLink
                          to="/c/admin/ad-spots"
                          class="inline-flex items-center px-3 py-1 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white text-xs font-medium rounded-md transition-all duration-200"
                        >
                          <Icon name="mdi:view-grid" class="h-3 w-3 mr-1" />
                          Go to ad spots
                        </NuxtLink>
                      </div>
                    </div>
                  </div>

                  <div class="bg-gradient-to-br from-emerald-900/20 via-emerald-800/20 to-emerald-900/20 backdrop-blur-sm rounded-lg p-4">
                    <div class="flex items-start">
                      <div class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-lg flex items-center justify-center mr-3 shadow-lg flex-shrink-0">
                        <Icon name="mdi:chart-pie" class="h-5 w-5 text-white" />
                      </div>
                      <div class="flex-1">
                        <h4 class="text-sm font-semibold text-white mb-1">Site Analytics</h4>
                        <p class="text-xs text-gray-400 mb-3">View comprehensive site metrics and user insights</p>
                        <NuxtLink
                          to="/c/admin/analytics"
                          class="inline-flex items-center px-3 py-1 bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white text-xs font-medium rounded-md transition-all duration-200"
                        >
                          <Icon name="mdi:analytics" class="h-3 w-3 mr-1" />
                          View site analytics
                        </NuxtLink>
                      </div>
                    </div>
                  </div>

                  <div class="bg-gradient-to-br from-cyan-900/20 via-cyan-800/20 to-cyan-900/20 backdrop-blur-sm rounded-lg p-4 border border-cyan-400/20">
                    <div class="flex items-start">
                      <div class="w-10 h-10 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-lg flex items-center justify-center mr-3 shadow-lg flex-shrink-0">
                        <Icon name="mdi:chart-line" class="h-5 w-5 text-white" />
                      </div>
                      <div class="flex-1">
                        <h4 class="text-sm font-semibold text-white mb-1">Ad Analytics</h4>
                        <p class="text-xs text-gray-400 mb-3">View performance metrics and insights</p>
                        <button
                          class="inline-flex items-center px-3 py-1 bg-gradient-to-r from-cyan-600 to-cyan-700 hover:from-cyan-700 hover:to-cyan-800 text-white text-xs font-medium rounded-md transition-all duration-200"
                          @click="navigateTo('/c/admin/ad-spots/analytics')"
                        >
                          <Icon name="mdi:chart-bar" class="h-3 w-3 mr-1" />
                          View analytics
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          </div>
        </div>

        <!-- Activity feed -->
        <section aria-labelledby="activity-heading" class="max-w-screen-xl mx-auto">
          <div
            class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-2xl p-4 md:p-8 border border-white/10"
          >
            <UiAdminActivityFeed
              title="Recent Activity"
              :activities="recentActivity"
              :loading="activityLoading"
              show-filters
              @refresh="refreshActivity"
              @filter="handleActivityFilter"
            />
          </div>
        </section>
      </div>
    </role-guard>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { navigateTo } from '#app';

import { useAdminDashboard } from '~/composables/admin-dashboard';

definePageMeta({
  middleware: ['admin-async'],
  layout: 'dashboard',
});

// Initialize the admin dashboard composable
const {
  userMetrics,
  contentMetrics,
  systemHealth,
  recentActivity,
  activityLoading,
  systemHealthLoading,
  initDashboard,
  refreshDashboard,
  fetchSystemHealth,
} = useAdminDashboard();

// State
const isRefreshing = ref(false);

// Handle actions from the quick action cards

// Handle activity feed filters
const handleActivityFilter = (selectedTypes: string[]) => {
  console.log('Filtering activity by types:', selectedTypes);
  // In a real implementation, this would update the activity feed
};

// Refresh all dashboard data
const refreshActivity = () => {
  // This will be handled by the real-time listener in the composable
  console.log('Refreshing activity feed');
};

// Refresh dashboard data with loading indicator
const refreshAllData = async() => {
  isRefreshing.value = true;
  await refreshDashboard();
  isRefreshing.value = false;
};

// Initialize on component mount
onMounted(() => {
  initDashboard();
});
// Remove this function. In Nuxt 3, definePageMeta is a macro provided by the framework
// and should not be redefined or implemented manually.
// If you see "Function not implemented" errors, ensure your IDE recognizes Nuxt macros
// and that your build setup supports them.
</script>
