<script setup lang="ts">
import { ref } from 'vue'
import { useCurrentUser } from '~/composables/useCurrentUser'
import { useRouter } from '#app'
import { useContacts } from '~/composables/useContacts'
import { useBusinesses } from '~/composables/useBusinesses'

definePageMeta({
  layout: "covalonic",
  middleware: "auth"
});

const router = useRouter()
const { currentUser } = useCurrentUser()
const { createContact } = useContacts()
const { createBusiness } = useBusinesses()

// Form state - Always add as lead
const isAddAsLead = ref(true)
const leadNotes = ref('')
const showLeadInfo = ref(false)

// Helper function to remove undefined fields from objects
const removeUndefinedFields = (obj: any): any => {
  const cleaned: any = {}
  for (const key in obj) {
    if (obj.hasOwnProperty(key) && obj[key] !== undefined && obj[key] !== null) {
      if (typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
        const cleanedNested = removeUndefinedFields(obj[key])
        if (Object.keys(cleanedNested).length > 0) {
          cleaned[key] = cleanedNested
        }
      } else {
        cleaned[key] = obj[key]
      }
    }
  }
  return cleaned
}

// Pass custom handler to the create component
const handleBusinessCardSubmit = async (response: any) => {
  console.log('Business card response:', response)
  
  try {
    // Extract the actual data from the response
    const businessCardData = response.id ? response : response.data || response
    
    // Clean the data to remove any Firebase error objects
    const cleanData = (obj: any) => {
      const cleaned: any = {}
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          const value = obj[key]
          // Skip if value is a Firebase error or undefined
          if (value instanceof Error || 
              (value && typeof value === 'object' && value.constructor && value.constructor.name === 'FirebaseError') ||
              value === undefined ||
              value === null) {
            continue
          }
          // Handle nested objects
          if (typeof value === 'object' && !Array.isArray(value)) {
            cleaned[key] = cleanData(value)
          } else {
            cleaned[key] = value
          }
        }
      }
      return cleaned
    }
    
    const cleanedData = cleanData(businessCardData)
    
    // First, save the business card to get its ID
    const businessCardResponse = await $fetch('/api/firestore/add', {
      method: 'POST',
      query: { col: 'business-cards' },
      body: {
        imageUrl: cleanedData.image?.url || cleanedData.image || '',
        ocrData: cleanedData,
        status: isAddAsLead.value ? 'unclaimed' : 'claimed',
        claimedBy: currentUser.value?.uid || '',
        scannedAt: new Date().toISOString(),
        scannedBy: currentUser.value?.uid || ''
      }
    })
    
    if (businessCardResponse.error) {
      throw new Error(businessCardResponse.error)
    }
    
    const businessCardId = businessCardResponse.result?.id
    
    // Create business entity if company name exists
    let businessId: string | undefined
    let businessCreationFailed = false
    
    if (cleanedData.company) {
      try {
        const businessData: any = {
          name: cleanedData.company,
          type: isAddAsLead.value ? 'prospect' : 'client',
          website: cleanedData.website,
          headquarters: removeUndefinedFields({
            street: cleanedData.address_street || cleanedData.address,
            city: cleanedData.address_city || cleanedData.city,
            state: cleanedData.address_state || cleanedData.state,
            country: cleanedData.address_country || cleanedData.country,
            postalCode: cleanedData.address_zip || cleanedData.postal_code || cleanedData.zip
          })
        }
        
        // Remove empty headquarters object if all fields are undefined
        if (Object.keys(businessData.headquarters).length === 0) {
          delete businessData.headquarters
        }
        
        businessId = await createBusiness(businessData)
        console.log('Business created with ID:', businessId)
      } catch (err) {
        console.error('Failed to create business:', err)
        businessCreationFailed = true
        
        // For leads, company is important - ask user if they want to continue
        if (isAddAsLead.value) {
          const { $swal } = useNuxtApp()
          if ($swal) {
            const result = await $swal.fire({
              title: 'Business Creation Failed',
              text: 'Failed to create the company record. Do you want to continue creating the contact without a company link?',
              icon: 'warning',
              showCancelButton: true,
              confirmButtonText: 'Continue Without Company',
              cancelButtonText: 'Cancel'
            })
            
            if (!result.isConfirmed) {
              throw new Error('User cancelled operation due to business creation failure')
            }
          }
        }
      }
    } else if (isAddAsLead.value) {
      // For leads, warn if no company name is provided
      const { $swal } = useNuxtApp()
      if ($swal) {
        await $swal.fire({
          title: 'No Company Name',
          text: 'Creating a lead without a company name. The lead will not be associated with any business.',
          icon: 'info',
          confirmButtonText: 'OK'
        })
      }
    }
    
    // Create contact entity - filter out undefined fields
    const contactData: any = {
      type: isAddAsLead.value ? 'lead' : 'contact',
      status: 'active',
      stage: isAddAsLead.value ? 'new' : 'converted',
      firstName: cleanedData.first_name || '',
      lastName: cleanedData.last_name || '',
      email: cleanedData.email,
      phone: cleanedData.phone,
      jobTitle: cleanedData.job_title || cleanedData.title || '',
      businessCardId: businessCardId,
      leadSource: 'scan',
      address: {
        street: cleanedData.address_street || cleanedData.address,
        city: cleanedData.address_city || cleanedData.city,
        state: cleanedData.address_state || cleanedData.state,
        country: cleanedData.address_country || cleanedData.country,
        postalCode: cleanedData.address_zip || cleanedData.postal_code || cleanedData.zip
      },
      website: cleanedData.website,
      tags: cleanedData.tags || []
    }
    
    // Only add businessId if it exists
    if (businessId) {
      contactData.businessId = businessId
    }
    
    // Only add leadNotes if it's a lead and notes exist
    if (isAddAsLead.value && leadNotes.value) {
      contactData.leadNotes = leadNotes.value
    }
    
    // Clean the contact data to remove any undefined fields
    const cleanedContactData = removeUndefinedFields(contactData)
    
    const contactId = await createContact(cleanedContactData)
    console.log('Contact created with ID:', contactId)
    
    // Update business card with the created entities
    if (businessCardId) {
      const updateData: any = {
        contactId: contactId
      }
      
      // Only add businessId if it exists
      if (businessId) {
        updateData.businessId = businessId
      }
      
      await $fetch('/api/firestore/update', {
        method: 'POST',
        query: { col: 'business-cards', id: businessCardId },
        body: updateData
      })
    }
    
    // Show success message with appropriate details
    const { $swal } = useNuxtApp()
    if ($swal) {
      let successMessage = ''
      if (isAddAsLead.value) {
        successMessage = 'Lead has been added to your CRM successfully.'
        if (businessCreationFailed && cleanedData.company) {
          successMessage += ' Note: The company record could not be created.'
        } else if (!cleanedData.company) {
          successMessage += ' Note: No company associated with this lead.'
        }
      } else {
        successMessage = 'Contact has been saved successfully.'
      }
      
      await $swal.fire({
        title: 'Success!',
        text: successMessage,
        icon: 'success',
        confirmButtonText: 'View Contact'
      })
    }
    
    // Navigate to the contact page
    await router.push(`/c/contacts/${contactId}`)
    
  } catch (error) {
    console.error('Failed to save business card:', error)
    
    // Show error message
    const { $swal } = useNuxtApp()
    if ($swal) {
      await $swal.fire({
        title: 'Error',
        text: 'Failed to save business card. Please try again.',
        icon: 'error',
        confirmButtonText: 'OK'
      })
    }
  }
}
</script>

<template>
  <div class="min-h-screen font-sans relative overflow-hidden">
    <!-- Dark gradient background -->
    <div class="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900"></div>

    <!-- Decorative elements -->
    <div class="absolute top-20 right-20 w-96 h-96 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl"></div>
    <div class="absolute bottom-40 left-20 w-80 h-80 bg-gradient-to-br from-purple-400/20 to-blue-400/20 rounded-full blur-3xl"></div>
    <div class="absolute top-1/2 right-1/3 w-64 h-64 bg-gradient-to-br from-red-400/15 to-pink-400/15 rounded-full blur-2xl"></div>

    <div class="relative z-10 px-4 md:px-6 py-4 md:py-8">
      <div class="max-w-screen-xl mx-auto">
        <!-- Header Section -->
        <div class="mb-8">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <Icon name="mdi:camera" class="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 class="text-2xl font-bold text-white">Scan Business Card</h1>
              <p class="text-gray-400">Upload a business card or enter contact information manually</p>
            </div>
          </div>
        </div>

        <div class="w-full max-w-4xl mx-auto">
          <!-- Lead Notes Section -->
          <div class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-xl border border-white/10 hover:border-white/20 transition-all duration-300 p-4 md:p-6 mb-6">
            <div class="flex items-center mb-4">
              <Icon name="mdi:account-plus" class="w-5 h-5 text-blue-400 mr-2" />
              <h3 class="text-lg font-semibold text-white">CRM Lead Information</h3>
            </div>
            <p class="text-gray-300 text-sm mb-4">
              All scanned business cards are automatically added as leads to your CRM for follow-up.
            </p>
            
            <div class="p-4 bg-gray-800/50 rounded-lg border border-white/5">
              <label class="block text-sm font-medium text-gray-300 mb-2">
                Lead Notes (Optional)
              </label>
              <textarea
                v-model="leadNotes"
                rows="3"
                placeholder="Add any notes about this lead (e.g., met at conference, interested in product X)"
                class="w-full px-3 py-2 bg-gray-700/50 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              ></textarea>
              <p class="mt-2 text-xs text-gray-400">
                <Icon name="mdi:information-outline" class="inline w-3 h-3 mr-1" />
                This card will be saved as an unclaimed lead in your CRM
              </p>
            </div>
          </div>

          <!-- Business Card Create Component -->
          <BusinesscardsCreate 
            :homepage="false" 
            :custom-submit-handler="handleBusinessCardSubmit"
            :hide-navigation="true"
          />
        </div>
      </div>
    </div>
  </div>
</template>