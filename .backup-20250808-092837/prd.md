# Business Card OCR + CRM Integration Brownfield Enhancement PRD

## Intro Project Analysis and Context

### Analysis Source
- Document-project analysis available - using existing technical documentation
- Brownfield architecture document available at `docs/brownfield-architecture.md`
- Technical analysis shows Nuxt 3 + Vue 3 + Firebase system with ad system in transition

### Current Project State
From the existing documentation:
- **Primary Platform**: Nuxt 3.17.7 web application with Vue 3 frontend
- **Database**: Firebase Firestore with Authentication and Storage
- **Core Features**: Advertising system (in transition from legacy to unified model)
- **User Management**: Role-based system with admin capabilities
- **Current Focus**: Ad system simplification is in progress

### Enhancement Scope Definition

#### Enhancement Type
- ✅ New Feature Addition (Business card OCR + comprehensive CRM)
- ✅ Integration with New Systems (OCR processing, task management, invoicing)
- ✅ Significant Impact - Creates new core business functionality

#### Enhancement Description
Implement a business card upload system with OCR-powered CRM integration that automatically creates client and business records. Include admin approval workflow for content quality, task management with reminders, and dual-purpose invoicing system supporting both user-to-client and admin-to-user transactions.

#### Impact Assessment
- ✅ Significant Impact - New database collections, user workflows, and admin processes
- ✅ Architectural additions - OCR integration, notification system, invoice generation

### Goals and Background Context

#### Goals
- Enable OCR-powered business card capture to eliminate manual data entry friction
- Create unified business management platform combining visibility (ads) with operations (CRM)
- Implement content quality control through efficient admin approval workflow
- Provide task/reminder system to prevent lost follow-up opportunities
- Enable revenue generation through integrated invoicing for all user types
- Reduce platform switching by consolidating business tools in one ecosystem

#### Background Context
Our analysis reveals the root need is creating a unified platform where business visibility seamlessly connects with business operations. Users currently managing their presence through ads need integrated tools for the entire business development lifecycle - from initial contact capture through invoicing. The dual-user model (business users and admins) requires careful workflow design to serve both user types effectively while maintaining content quality and platform integrity.

### Change Log
| Change | Date | Version | Description | Author |
|--------|------|---------|-------------|---------|
| Initial | 2025-08-04 | 1.0 | Created brownfield PRD for CRM enhancement | John (PM) |

## Requirements

### Functional Requirements

- **FR1**: The system shall provide a business card upload interface accepting image files (JPEG, PNG) up to 10MB
- **FR2**: The system shall integrate OCR processing to extract contact information (name, company, title, phone, email, address) from uploaded business card images
- **FR3**: The system shall create CRM Client records with extracted data, allowing user editing before final save
- **FR4**: The system shall create or link Business entities based on company information from business cards
- **FR5**: The system shall implement admin approval queue where uploaded cards await activation before public visibility at /businesscards
- **FR6**: The system shall display uploaded cards and associated CRM data in user's backend dashboard immediately (pre-approval)
- **FR7**: The system shall provide task creation for CRM clients with customizable reminder dates/times
- **FR8**: The system shall send notifications (email and/or push) for task reminders
- **FR9**: The system shall generate quotes for clients with item/service line items and conversion to invoices
- **FR10**: The system shall enable admin users to create invoices for platform users (subscriptions, ad charges)
- **FR11**: The system shall track payment status for all invoices with automated reminder capabilities

### Non-Functional Requirements

- **NFR1**: OCR processing shall complete within 5 seconds for 95% of business card images
- **NFR2**: The system shall maintain existing Firebase authentication and role-based access patterns
- **NFR3**: All CRM data shall be encrypted at rest using Firebase security rules
- **NFR4**: The admin approval interface shall handle 100+ pending cards without performance degradation
- **NFR5**: Invoice generation shall produce PDF format compatible with standard accounting software
- **NFR6**: The system shall maintain current page load times (< 3 seconds) with new CRM features

### Compatibility Requirements

- **CR1**: Existing API endpoints shall remain unchanged to maintain backward compatibility
- **CR2**: New Firestore collections (clients, businesses, tasks, invoices) shall follow existing naming conventions and security patterns
- **CR3**: UI components shall use existing Tailwind CSS classes and dark mode theme consistency
- **CR4**: Integration with existing user management and role system without modifying current authentication flow

## Technical Constraints and Integration Requirements

### Existing Technology Stack
Based on the document-project analysis:

**Languages**: TypeScript, JavaScript (Node.js 18.x)  
**Frameworks**: Nuxt 3.17.7, Vue 3 (Composition API)  
**Database**: Firebase Firestore 11.10.0  
**Infrastructure**: Vercel deployment, Firebase Functions  
**External Dependencies**: Stripe 18.1.0 (payments), Firebase Auth/Storage

### Integration Approach

**Database Integration Strategy**: 
- Create new Firestore collections: `business_cards`, `crm_clients`, `crm_businesses`, `crm_tasks`, `invoices`
- Implement compound indexes for efficient CRM queries (client + user, task + due_date)
- Maintain referential integrity through Cloud Functions triggers

**API Integration Strategy**:
- Extend existing `/server/api/` structure with new CRM endpoints
- Integrate Google Cloud Vision API for OCR processing
- Reuse existing Stripe integration for invoice payments
- Add webhook endpoints for payment status updates

**Frontend Integration Strategy**:
- Create new composables: `useCRM.ts`, `useOCR.ts`, `useInvoicing.ts`
- Extend admin dashboard with approval queue components
- Reuse existing dark mode Tailwind theme and gradient patterns
- Add CRM section to user dashboard at `/c/dashboard/crm`

**Testing Integration Strategy**:
- Add Vitest unit tests for new composables
- Mock OCR responses for consistent testing
- Extend existing test patterns for CRM operations

### Code Organization and Standards

**File Structure Approach**:
```
composables/
  ├── useCRM.ts         # Client/business management
  ├── useOCR.ts         # Business card processing
  └── useInvoicing.ts   # Quote/invoice generation
components/
  ├── crm/
  │   ├── ClientManager.vue
  │   ├── TaskReminder.vue
  │   └── BusinessCardUpload.vue
  └── invoicing/
      ├── QuoteBuilder.vue
      └── InvoiceGenerator.vue
```

**Naming Conventions**: 
- Follow existing camelCase for functions, PascalCase for components
- Prefix CRM types with `ICRM` (e.g., `ICRMClient`, `ICRMTask`)

**Coding Standards**: 
- TypeScript strict mode for all new code
- Composition API with `<script setup>` pattern
- Existing ESLint/Prettier configuration

**Documentation Standards**: 
- JSDoc comments for public composable functions
- README.md for each major feature module

### Deployment and Operations

**Build Process Integration**: 
- No changes to existing `npm run build` process
- Add environment variables for OCR API keys

**Deployment Strategy**: 
- Deploy OCR functions separately to Firebase Functions
- Staged rollout: Admin features → Beta users → All users

**Monitoring and Logging**: 
- Extend existing Firebase Analytics for CRM events
- Log OCR processing success/failure rates
- Monitor invoice generation performance

**Configuration Management**: 
- Add OCR provider settings to runtime config
- Invoice template configuration in Firestore
- Task notification preferences per user

### Risk Assessment and Mitigation

**Technical Risks**: 
- OCR accuracy varies with image quality → Implement manual correction UI
- Firestore query limits with large CRM datasets → Implement pagination early
- Storage costs for business card images → Compress and archive after processing

**Integration Risks**: 
- OCR API rate limits → Implement queuing system with retry logic
- Existing user roles may not map to CRM permissions → Extend role system carefully
- Payment webhook conflicts → Namespace invoice webhooks separately

**Deployment Risks**: 
- Database migration complexity → No migration needed, purely additive
- User confusion with new features → Feature flags for gradual rollout
- Performance impact → Monitor and optimize queries before full release

**Mitigation Strategies**: 
- Implement comprehensive error handling for OCR failures
- Create admin tools for bulk operations and corrections
- Design for offline-first with sync when online
- Regular backups before major deployments

## Epic and Story Structure

### Epic Approach

**Epic Structure Decision**: Single comprehensive epic with carefully sequenced stories

**Rationale**: While this enhancement has multiple features (OCR, CRM, invoicing), they're all interconnected parts of a unified business management system. A single epic ensures:
- Consistent data models across features
- Shared components and utilities
- Coordinated testing and deployment
- Clear dependency management between stories

## Epic 1: Business Card OCR + CRM Integration

**Epic Goal**: Implement a comprehensive business management system that transforms business cards into actionable CRM data with integrated task management and invoicing capabilities

**Integration Requirements**: 
- Maintain compatibility with existing Firebase authentication and user roles
- Extend current admin dashboard without disrupting ad management workflows  
- Preserve existing UI/UX patterns while adding new CRM sections
- Ensure all new features follow established security and data patterns

### Story 1.1: CRM Data Model and Foundation

As a developer,  
I want to establish the core CRM data models and Firestore collections,  
so that we have a solid foundation for all CRM features.

#### Acceptance Criteria
1. Create Firestore collections: crm_clients, crm_businesses, crm_tasks with proper schemas
2. Implement TypeScript interfaces: ICRMClient, ICRMBusiness, ICRMTask
3. Set up Firebase security rules maintaining existing patterns
4. Create useCRM.ts composable with basic CRUD operations
5. Add unit tests achieving 80%+ coverage for CRM operations

#### Integration Verification
- IV1: Existing user authentication continues to work with new collections
- IV2: Firebase security rules don't conflict with existing rules
- IV3: No performance impact on existing dashboard load times

### Story 1.2: Business Card Upload Interface

As a business user,  
I want to upload business card images through an intuitive interface,  
so that I can quickly capture new contact information.

#### Acceptance Criteria
1. Create BusinessCardUpload.vue component using existing Tailwind patterns
2. Implement image upload with preview (max 10MB, JPEG/PNG)
3. Add upload progress indicator following existing UI patterns
4. Store images temporarily in Firebase Storage
5. Create mobile-responsive design matching existing layouts

#### Integration Verification
- IV1: Upload component integrates with existing dashboard navigation
- IV2: Storage rules align with existing Firebase Storage patterns
- IV3: Component works with current dark mode theme

### Story 1.3: OCR Integration and Processing

As a system,  
I want to process business card images using OCR,  
so that contact information is automatically extracted.

#### Acceptance Criteria
1. Integrate Google Cloud Vision API for text extraction
2. Create Firebase Function for OCR processing
3. Implement useOCR.ts composable with error handling
4. Parse extracted text into structured data (name, company, email, phone)
5. Return results within 5 seconds for 95% of images

#### Integration Verification
- IV1: OCR function deploys alongside existing Firebase Functions
- IV2: API keys stored following existing environment patterns
- IV3: Error handling consistent with current patterns

### Story 1.4: CRM Client Management Interface

As a business user,  
I want to review and edit extracted business card data,  
so that I can ensure accuracy before saving to my CRM.

#### Acceptance Criteria
1. Create ClientManager.vue with edit form for OCR results
2. Implement validation for all contact fields
3. Allow linking to existing businesses or creating new ones
4. Save client data to Firestore with user association
5. Display success/error messages using existing notification patterns

#### Integration Verification
- IV1: Form validation uses existing patterns from ad forms
- IV2: Client list integrates into user dashboard
- IV3: Data saves respect existing user data isolation

### Story 1.5: Admin Approval Queue

As an admin,  
I want to review uploaded business cards before public display,  
so that I can ensure content quality and prevent spam.

#### Acceptance Criteria
1. Create admin queue interface at /c/admin/business-cards
2. Display pending cards with uploaded images and extracted data
3. Implement approve/reject actions with reason field
4. Update card status and notify users of decisions
5. Add bulk operations for efficiency

#### Integration Verification
- IV1: Admin interface follows existing admin dashboard patterns
- IV2: Role-based access uses existing admin middleware
- IV3: Notification system extends current user alerts

### Story 1.6: Task Management System

As a business user,  
I want to create follow-up tasks for my CRM contacts,  
so that I never miss important opportunities.

#### Acceptance Criteria
1. Create TaskReminder.vue component for task creation
2. Implement due date/time selection with reminder settings
3. Store tasks in crm_tasks collection with user/client association
4. Display tasks in user dashboard with filtering options
5. Allow task completion with activity logging

#### Integration Verification
- IV1: Task UI components match existing dashboard widgets
- IV2: Date/time pickers consistent with current forms
- IV3: Task list performance maintains sub-200ms load times

### Story 1.7: Notification and Reminder System

As a business user,  
I want to receive timely reminders for my tasks,  
so that I can follow up with clients promptly.

#### Acceptance Criteria
1. Implement Firebase Function for scheduled task notifications
2. Send email notifications using existing email service
3. Add in-app notifications to user dashboard
4. Allow notification preferences in user settings
5. Include snooze and reschedule options

#### Integration Verification
- IV1: Email templates match existing system emails
- IV2: Notification preferences integrate with user settings
- IV3: Scheduled functions don't impact existing cron jobs

### Story 1.8: Invoice and Quote Foundation

As a developer,  
I want to establish invoice/quote data models and generation logic,  
so that users can create professional documents.

#### Acceptance Criteria
1. Create Firestore collections: invoices, quotes with schemas
2. Implement TypeScript interfaces: IInvoice, IQuote, ILineItem
3. Create useInvoicing.ts composable with CRUD operations
4. Design reusable PDF generation logic
5. Set up invoice numbering system with prefixes

#### Integration Verification
- IV1: Invoice models compatible with existing Stripe integration
- IV2: PDF generation doesn't conflict with existing exports
- IV3: Numbering system allows for existing manual invoices

### Story 1.9: Quote Builder Interface

As a business user,  
I want to create professional quotes for my clients,  
so that I can formalize business proposals.

#### Acceptance Criteria
1. Create QuoteBuilder.vue with line item management
2. Allow selecting clients from CRM with autofill
3. Implement pricing calculations with tax options
4. Add quote templates for common services
5. Generate PDF quotes with company branding

#### Integration Verification
- IV1: Quote builder uses existing form components
- IV2: Client selection integrates with CRM data
- IV3: PDF styling matches platform branding

### Story 1.10: Invoice Generation and Management

As a business user and admin,  
I want to convert quotes to invoices and track payments,  
so that I can manage my business finances effectively.

#### Acceptance Criteria
1. Implement quote-to-invoice conversion workflow
2. Create InvoiceGenerator.vue for direct invoice creation
3. Add payment tracking with status updates
4. Enable admin-to-user invoicing for platform fees
5. Integrate with existing Stripe payment flow

#### Integration Verification
- IV1: Payment processing uses existing Stripe integration
- IV2: Invoice status updates don't conflict with ad payments
- IV3: Admin invoicing respects existing billing cycles