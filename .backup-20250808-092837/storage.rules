rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isValidImageFile() {
      return request.resource.contentType.matches('image/.*') &&
             request.resource.size < 10 * 1024 * 1024; // 10MB max
    }
    
    function isValidDocument() {
      return request.resource.contentType.matches('application/pdf') &&
             request.resource.size < 25 * 1024 * 1024; // 25MB max
    }
    
    // User profile images
    match /users/{userId}/profile/{fileName} {
      allow read: if true; // Public profile images
      allow write: if isOwner(userId) && isValidImageFile();
    }
    
    // Business card images
    match /business-cards/{cardId}/{fileName} {
      allow read: if true; // Public business card images
      allow create: if isAuthenticated() && isValidImageFile();
      allow update, delete: if isAuthenticated() && 
                              (resource.metadata['uploaderId'] == request.auth.uid ||
                               resource.metadata['userId'] == request.auth.uid);
    }
    
    // Business card scans (OCR processing)
    match /card-scans/{userId}/{fileName} {
      allow read: if isOwner(userId);
      allow write: if isOwner(userId) && isValidImageFile();
    }
    
    // CRM business card uploads
    match /business_cards/{userId}/{fileName} {
      allow read: if isOwner(userId);
      allow write: if isOwner(userId) && isValidImageFile();
    }
    
    // CRM attachments (notes, documents)
    match /crm-attachments/{userId}/{interactionId}/{fileName} {
      allow read: if isOwner(userId);
      allow write: if isOwner(userId) && 
                     (isValidImageFile() || isValidDocument());
    }
    
    // Temporary uploads (for processing)
    match /temp/{userId}/{sessionId}/{fileName} {
      allow read: if isOwner(userId);
      allow write: if isOwner(userId) && isValidImageFile();
    }
    
    // Public assets (logos, etc.)
    match /public/{allPaths=**} {
      allow read: if true;
      allow write: if false; // Admin only via console
    }
    
    // Default deny
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
