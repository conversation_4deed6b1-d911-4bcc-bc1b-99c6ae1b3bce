// Enhanced Service Worker for Covalonic - Advanced Offline Functionality
// Story 4.1 - Comprehensive offline support with intelligent caching and background sync

import { precacheAndRoute, cleanupOutdatedCaches } from 'workbox-precaching'
import { registerRoute, NavigationRoute } from 'workbox-routing'
import { 
  StaleWhileRevalidate, 
  CacheFirst, 
  NetworkFirst, 
  NetworkOnly 
} from 'workbox-strategies'
import { ExpirationPlugin } from 'workbox-expiration'
import { CacheableResponsePlugin } from 'workbox-cacheable-response'

// Cache names for different content types
const CACHE_NAMES = {
  APP_SHELL: 'covalonic-app-shell-v1',
  BUSINESS_CARDS: 'covalonic-business-cards-v1',
  IMAGES: 'covalonic-images-v1',
  API_RESPONSES: 'covalonic-api-v1',
  STATIC_ASSETS: 'covalonic-static-v1',
  OCR_ASSETS: 'covalonic-ocr-v1',
  ANALYTICS: 'covalonic-analytics-v1'
}

// Configuration constants
const MAX_ENTRIES = {
  BUSINESS_CARDS: 500,
  IMAGES: 200,
  API_RESPONSES: 100,
  STATIC_ASSETS: 100
}

const MAX_AGE_SECONDS = {
  BUSINESS_CARDS: 30 * 24 * 60 * 60, // 30 days
  IMAGES: 7 * 24 * 60 * 60, // 7 days
  API_RESPONSES: 5 * 60, // 5 minutes
  STATIC_ASSETS: 24 * 60 * 60, // 1 day
  OCR_ASSETS: 30 * 24 * 60 * 60 // 30 days
}

// Precache and clean up outdated caches
precacheAndRoute(self.__WB_MANIFEST)
cleanupOutdatedCaches()

// App Shell - Cache First Strategy (for critical app files)
registerRoute(
  ({ request, url }) => {
    return (
      request.destination === 'document' ||
      url.pathname.includes('/_nuxt/') ||
      url.pathname.includes('/icons/') ||
      url.pathname.includes('/manifest.json')
    )
  },
  new CacheFirst({
    cacheName: CACHE_NAMES.APP_SHELL,
    plugins: [
      new CacheableResponsePlugin({
        statuses: [0, 200]
      }),
      new ExpirationPlugin({
        maxEntries: MAX_ENTRIES.STATIC_ASSETS,
        maxAgeSeconds: MAX_AGE_SECONDS.STATIC_ASSETS
      })
    ]
  })
)

// Business Card API - Network First with offline fallback
registerRoute(
  ({ url }) => {
    return (
      url.pathname.includes('/api/businesscards') ||
      url.pathname.includes('/businesscards')
    )
  },
  new NetworkFirst({
    cacheName: CACHE_NAMES.BUSINESS_CARDS,
    networkTimeoutSeconds: 3,
    plugins: [
      new CacheableResponsePlugin({
        statuses: [0, 200]
      }),
      new ExpirationPlugin({
        maxEntries: MAX_ENTRIES.BUSINESS_CARDS,
        maxAgeSeconds: MAX_AGE_SECONDS.BUSINESS_CARDS
      })
    ]
  })
)

// Business Card Images - Stale While Revalidate
registerRoute(
  ({ request, url }) => {
    return (
      request.destination === 'image' && (
        url.pathname.includes('businesscard') ||
        url.pathname.includes('/uploads/') ||
        url.pathname.includes('/images/')
      )
    )
  },
  new StaleWhileRevalidate({
    cacheName: CACHE_NAMES.IMAGES,
    plugins: [
      new CacheableResponsePlugin({
        statuses: [0, 200]
      }),
      new ExpirationPlugin({
        maxEntries: MAX_ENTRIES.IMAGES,
        maxAgeSeconds: MAX_AGE_SECONDS.IMAGES,
        purgeOnQuotaError: true
      })
    ]
  })
)

// OCR and Tesseract Assets - Cache First (for offline OCR)
registerRoute(
  ({ url }) => {
    return (
      url.href.includes('tesseract') ||
      url.href.includes('/tessdata/') ||
      url.href.includes('worker.min.js') ||
      url.href.includes('core.wasm.js')
    )
  },
  new CacheFirst({
    cacheName: CACHE_NAMES.OCR_ASSETS,
    plugins: [
      new CacheableResponsePlugin({
        statuses: [0, 200]
      }),
      new ExpirationPlugin({
        maxAgeSeconds: MAX_AGE_SECONDS.OCR_ASSETS
      })
    ]
  })
)

// API Responses - Network First with short cache
registerRoute(
  ({ url }) => {
    return (
      url.pathname.includes('/api/') &&
      !url.pathname.includes('/api/businesscards')
    )
  },
  new NetworkFirst({
    cacheName: CACHE_NAMES.API_RESPONSES,
    networkTimeoutSeconds: 2,
    plugins: [
      new CacheableResponsePlugin({
        statuses: [0, 200]
      }),
      new ExpirationPlugin({
        maxEntries: MAX_ENTRIES.API_RESPONSES,
        maxAgeSeconds: MAX_AGE_SECONDS.API_RESPONSES
      })
    ]
  })
)

// Analytics Requests - Network Only (don't cache analytics)
registerRoute(
  ({ url }) => {
    return (
      url.href.includes('google-analytics.com') ||
      url.href.includes('analytics.google.com') ||
      url.pathname.includes('/api/analytics')
    )
  },
  new NetworkOnly()
)

// Navigation Route - Serve app shell for SPA navigation
const navigationRoute = new NavigationRoute(
  new CacheFirst({
    cacheName: CACHE_NAMES.APP_SHELL
  }),
  {
    allowlist: [/^(?!\/__).*/], // Allow all except __webpack_hmr and similar
    denylist: [/\/__.*$/, /\/api\/.*$/, /\.(?:png|jpg|jpeg|svg|gif|webp)$/i]
  }
)
registerRoute(navigationRoute)

// Background Sync for Business Cards
self.addEventListener('sync', (event) => {
  console.log('Service Worker: Background sync event', event.tag)
  
  switch (event.tag) {
    case 'business-card-sync':
      event.waitUntil(syncBusinessCards())
      break
    case 'analytics-sync':
      event.waitUntil(syncAnalytics())
      break
    default:
      console.log('Service Worker: Unknown sync tag:', event.tag)
  }
})

// Sync business cards from IndexedDB to server
async function syncBusinessCards() {
  try {
    console.log('Service Worker: Starting business card sync')
    
    // Open IndexedDB to get pending sync items
    const syncDB = await openSyncDB()
    const pendingItems = await getSyncQueueItems(syncDB)
    
    for (const item of pendingItems) {
      try {
        await processSyncItem(item)
      } catch (error) {
        console.error('Service Worker: Failed to sync item:', item.id, error)
        // Update retry count and reschedule if needed
        await handleSyncError(syncDB, item, error)
      }
    }
    
    console.log('Service Worker: Business card sync completed')
  } catch (error) {
    console.error('Service Worker: Background sync failed:', error)
  }
}

// Sync analytics events
async function syncAnalytics() {
  try {
    console.log('Service Worker: Starting analytics sync')
    
    const analyticsDB = await openAnalyticsDB()
    const pendingEvents = await getAnalyticsEvents(analyticsDB)
    
    // Send analytics events in batches
    const batchSize = 10
    for (let i = 0; i < pendingEvents.length; i += batchSize) {
      const batch = pendingEvents.slice(i, i + batchSize)
      await sendAnalyticsBatch(batch)
      await clearProcessedEvents(analyticsDB, batch)
    }
    
    console.log('Service Worker: Analytics sync completed')
  } catch (error) {
    console.error('Service Worker: Analytics sync failed:', error)
  }
}

// IndexedDB operations for sync queue
async function openSyncDB() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('CovalonicSyncDB', 1)
    
    request.onerror = () => reject(request.error)
    request.onsuccess = () => resolve(request.result)
    
    request.onupgradeneeded = (event) => {
      const db = event.target.result
      
      if (!db.objectStoreNames.contains('syncQueue')) {
        const syncStore = db.createObjectStore('syncQueue', { keyPath: 'id' })
        syncStore.createIndex('status', 'status')
        syncStore.createIndex('timestamp', 'timestamp')
      }
    }
  })
}

// Get sync queue items
async function getSyncQueueItems(db) {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(['syncQueue'], 'readonly')
    const store = transaction.objectStore('syncQueue')
    const index = store.index('status')
    const request = index.getAll('pending')
    
    request.onerror = () => reject(request.error)
    request.onsuccess = () => resolve(request.result)
  })
}

// Process individual sync item
async function processSyncItem(item) {
  const { type, cardId, localData } = item
  let response
  
  switch (type) {
    case 'create':
      response = await fetch('/api/businesscards', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(localData)
      })
      break
    case 'update':
      response = await fetch(`/api/businesscards/${cardId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(localData)
      })
      break
    case 'delete':
      response = await fetch(`/api/businesscards/${cardId}`, {
        method: 'DELETE'
      })
      break
    default:
      throw new Error(`Unknown sync type: ${type}`)
  }
  
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }
  
  // Mark item as completed
  const syncDB = await openSyncDB()
  await updateSyncItemStatus(syncDB, item.id, 'completed')
}

// Handle sync errors with retry logic
async function handleSyncError(db, item, error) {
  item.retryCount = (item.retryCount || 0) + 1
  item.lastError = error.message
  item.lastAttempt = new Date()
  
  const maxRetries = 5
  if (item.retryCount >= maxRetries) {
    item.status = 'failed'
  } else {
    item.status = 'pending'
  }
  
  await updateSyncItem(db, item)
}

// Update sync item status
async function updateSyncItemStatus(db, itemId, status) {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(['syncQueue'], 'readwrite')
    const store = transaction.objectStore('syncQueue')
    const request = store.get(itemId)
    
    request.onsuccess = () => {
      const item = request.result
      if (item) {
        item.status = status
        item.completedAt = new Date()
        const updateRequest = store.put(item)
        updateRequest.onsuccess = () => resolve()
        updateRequest.onerror = () => reject(updateRequest.error)
      } else {
        resolve()
      }
    }
    
    request.onerror = () => reject(request.error)
  })
}

// Update sync item
async function updateSyncItem(db, item) {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(['syncQueue'], 'readwrite')
    const store = transaction.objectStore('syncQueue')
    const request = store.put(item)
    
    request.onsuccess = () => resolve()
    request.onerror = () => reject(request.error)
  })
}

// Analytics IndexedDB operations
async function openAnalyticsDB() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('CovalonicAnalyticsDB', 1)
    
    request.onerror = () => reject(request.error)
    request.onsuccess = () => resolve(request.result)
    
    request.onupgradeneeded = (event) => {
      const db = event.target.result
      
      if (!db.objectStoreNames.contains('events')) {
        const eventsStore = db.createObjectStore('events', { keyPath: 'id' })
        eventsStore.createIndex('timestamp', 'timestamp')
        eventsStore.createIndex('synced', 'synced')
      }
    }
  })
}

// Get analytics events to sync
async function getAnalyticsEvents(db) {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(['events'], 'readonly')
    const store = transaction.objectStore('events')
    const index = store.index('synced')
    const request = index.getAll(false)
    
    request.onerror = () => reject(request.error)
    request.onsuccess = () => resolve(request.result)
  })
}

// Send analytics batch
async function sendAnalyticsBatch(events) {
  const response = await fetch('/api/analytics/batch', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ events })
  })
  
  if (!response.ok) {
    throw new Error(`Analytics batch failed: ${response.status}`)
  }
}

// Clear processed analytics events
async function clearProcessedEvents(db, events) {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(['events'], 'readwrite')
    const store = transaction.objectStore('events')
    
    let completed = 0
    events.forEach(event => {
      const request = store.delete(event.id)
      request.onsuccess = () => {
        completed++
        if (completed === events.length) {
          resolve()
        }
      }
      request.onerror = () => reject(request.error)
    })
    
    if (events.length === 0) resolve()
  })
}

// Enhanced message handling
self.addEventListener('message', (event) => {
  console.log('Service Worker: Message received', event.data)
  
  const { type, data } = event.data || {}
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting()
      break
    case 'TRIGGER_SYNC':
      handleTriggerSync(data)
      break
    case 'CACHE_BUSINESS_CARD':
      handleCacheBusinessCard(data)
      break
    case 'GET_CACHE_STATUS':
      handleGetCacheStatus(event.ports[0])
      break
    case 'CLEAR_CACHE':
      handleClearCache(data, event.ports[0])
      break
    default:
      console.log('Service Worker: Unknown message type:', type)
  }
})

// Handle sync trigger from main thread
async function handleTriggerSync(data) {
  const { tag } = data || {}
  
  if (tag === 'business-card-sync') {
    await syncBusinessCards()
  } else if (tag === 'analytics-sync') {
    await syncAnalytics()
  }
}

// Cache business card for offline access
async function handleCacheBusinessCard(data) {
  try {
    const { cardData, images } = data
    
    // Cache the card data
    const cache = await caches.open(CACHE_NAMES.BUSINESS_CARDS)
    const response = new Response(JSON.stringify(cardData), {
      headers: { 'Content-Type': 'application/json' }
    })
    await cache.put(`/api/businesscards/${cardData.id}`, response)
    
    // Cache associated images
    if (images && images.length > 0) {
      const imageCache = await caches.open(CACHE_NAMES.IMAGES)
      for (const imageUrl of images) {
        try {
          const imageResponse = await fetch(imageUrl)
          if (imageResponse.ok) {
            await imageCache.put(imageUrl, imageResponse.clone())
          }
        } catch (error) {
          console.warn('Service Worker: Failed to cache image:', imageUrl, error)
        }
      }
    }
  } catch (error) {
    console.error('Service Worker: Failed to cache business card:', error)
  }
}

// Get cache status
async function handleGetCacheStatus(port) {
  try {
    const cacheNames = await caches.keys()
    const status = {}
    
    for (const cacheName of cacheNames) {
      const cache = await caches.open(cacheName)
      const keys = await cache.keys()
      status[cacheName] = keys.length
    }
    
    port.postMessage({ type: 'CACHE_STATUS', data: status })
  } catch (error) {
    port.postMessage({ type: 'ERROR', error: error.message })
  }
}

// Clear specific cache
async function handleClearCache(data, port) {
  try {
    const { cacheName } = data
    const success = await caches.delete(cacheName)
    port.postMessage({ type: 'CACHE_CLEARED', success })
  } catch (error) {
    port.postMessage({ type: 'ERROR', error: error.message })
  }
}

// Push notification handling (keeping existing functionality)
self.addEventListener('push', (event) => {
  console.log('Service Worker: Push event received', event)
  
  if (!event.data) return
  
  let data
  try {
    data = event.data.json()
  } catch (error) {
    console.error('Service Worker: Error parsing push data:', error)
    return
  }

  const options = {
    body: data.body || data.message || 'You have a new notification',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    tag: data.notificationId || data.tag || 'general',
    requireInteraction: data.priority === 'high',
    data: {
      notificationId: data.notificationId,
      businessCardId: data.businessCardId,
      eventType: data.eventType,
      url: data.businessCardId ? `/businesscards/${data.businessCardId}` : '/notifications',
      timestamp: Date.now()
    },
    actions: [
      {
        action: 'view',
        title: 'View Details',
        icon: '/icons/view-icon.png'
      },
      {
        action: 'dismiss',
        title: 'Dismiss',
        icon: '/icons/dismiss-icon.png'
      }
    ],
    silent: false,
    vibrate: data.priority === 'high' ? [200, 100, 200] : [100],
    timestamp: Date.now()
  }

  event.waitUntil(
    self.registration.showNotification(data.title || 'Business Card Activity', options)
  )
})

// Notification click handling (keeping existing functionality)
self.addEventListener('notificationclick', (event) => {
  console.log('Service Worker: Notification clicked', event)
  
  const notification = event.notification
  const data = notification.data || {}
  
  notification.close()

  if (event.action === 'dismiss') return
  
  let urlToOpen = data.url || '/notifications'
  
  if (event.action === 'view' && data.businessCardId) {
    urlToOpen = `/businesscards/${data.businessCardId}`
  }

  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true })
      .then((clientList) => {
        for (const client of clientList) {
          if (client.url.includes(urlToOpen.split('?')[0]) && 'focus' in client) {
            return client.focus()
          }
        }
        
        if (clientList.length > 0 && 'navigate' in clientList[0]) {
          return clientList[0].navigate(urlToOpen).then(() => clientList[0].focus())
        }
        
        if (clients.openWindow) {
          return clients.openWindow(urlToOpen)
        }
      })
      .then((windowClient) => {
        if (windowClient && data.notificationId) {
          windowClient.postMessage({
            type: 'NOTIFICATION_CLICKED',
            notificationId: data.notificationId,
            eventType: data.eventType,
            businessCardId: data.businessCardId
          })
        }
      })
  )
})

// Install event - prepare caches
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing enhanced offline service worker...')
  
  event.waitUntil(
    Promise.all([
      // Preload critical OCR assets for offline functionality
      preloadOCRAssets(),
      // Skip waiting to activate immediately
      self.skipWaiting()
    ])
  )
})

// Preload OCR assets for offline operation
async function preloadOCRAssets() {
  try {
    const cache = await caches.open(CACHE_NAMES.OCR_ASSETS)
    
    // Preload Tesseract core files for offline OCR
    const tesseractAssets = [
      'https://cdn.jsdelivr.net/npm/tesseract.js@4/dist/worker.min.js',
      'https://tessdata.projectnaptha.com/4.0.0/eng.traineddata.gz'
    ]
    
    for (const asset of tesseractAssets) {
      try {
        const response = await fetch(asset)
        if (response.ok) {
          await cache.put(asset, response.clone())
        }
      } catch (error) {
        console.warn('Service Worker: Failed to preload OCR asset:', asset, error)
      }
    }
  } catch (error) {
    console.error('Service Worker: Failed to preload OCR assets:', error)
  }
}

// Activate event - claim clients and cleanup
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating enhanced offline service worker...')
  
  event.waitUntil(
    Promise.all([
      // Take control of all clients immediately
      self.clients.claim(),
      // Clean up old caches
      cleanupOldCaches()
    ])
  )
})

// Cleanup old caches
async function cleanupOldCaches() {
  const cacheWhitelist = Object.values(CACHE_NAMES)
  const cacheNames = await caches.keys()
  
  await Promise.all(
    cacheNames.map(cacheName => {
      if (!cacheWhitelist.includes(cacheName)) {
        console.log('Service Worker: Deleting old cache:', cacheName)
        return caches.delete(cacheName)
      }
    })
  )
}

console.log('Service Worker: Enhanced offline service worker loaded successfully')