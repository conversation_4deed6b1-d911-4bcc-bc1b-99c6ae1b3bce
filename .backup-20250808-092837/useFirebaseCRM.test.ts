/**
 * Unit tests for Firebase CRM Composable
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { useFirebaseCRM } from '~/composables/useFirebaseCRM'
import type { CRMInteraction, CRMFollowUp, CRMNote } from '~/composables/useFirebaseCRM'
import type { Unsubscribe } from 'firebase/firestore'

// Mock Firebase
const mockFirestore = {
  collection: vi.fn(),
  doc: vi.fn(),
  getDoc: vi.fn(),
  addDoc: vi.fn(),
  updateDoc: vi.fn(),
  deleteDoc: vi.fn(),
  query: vi.fn(),
  where: vi.fn(),
  orderBy: vi.fn(),
  limit: vi.fn(),
  getDocs: vi.fn(),
  onSnapshot: vi.fn(),
  serverTimestamp: vi.fn(() => new Date()),
  Timestamp: {
    fromDate: vi.fn((date) => ({ toDate: () => date })),
    now: vi.fn(() => ({ toDate: () => new Date() }))
  }
}

// Mock dependencies
vi.mock('~/composables/useFirebase', () => ({
  useFirebase: () => ({
    firestore: mockFirestore
  })
}))

vi.mock('~/composables/useAuth', () => ({
  useAuth: () => ({
    user: { value: { uid: 'test-user-123' } }
  })
}))

describe('useFirebaseCRM', () => {
  let crmService: ReturnType<typeof useFirebaseCRM>
  let mockUnsubscribe: Unsubscribe

  beforeEach(() => {
    vi.clearAllMocks()
    mockUnsubscribe = vi.fn()
    crmService = useFirebaseCRM()
  })

  afterEach(() => {
    // Clean up any active subscriptions
    crmService.unsubscribeAll()
  })

  describe('CRUD Operations - Interactions', () => {
    const mockInteraction: Partial<CRMInteraction> = {
      type: 'meeting',
      title: 'Initial Meeting',
      content: 'Discussed project requirements',
      timestamp: new Date('2024-01-15T10:00:00Z'),
      metadata: {
        duration: 60,
        location: 'Conference Room A',
        priority: 'high',
        tags: ['important', 'project-kickoff']
      }
    }

    describe('createInteraction', () => {
      beforeEach(() => {
        mockFirestore.collection.mockReturnValue({})
        mockFirestore.addDoc.mockResolvedValue({ id: 'interaction-123' })
      })

      it('should create a new interaction successfully', async () => {
        const cardId = 'card-123'
        const result = await crmService.createInteraction(cardId, mockInteraction)

        expect(result).toMatchObject({
          ...mockInteraction,
          id: 'interaction-123',
          cardId,
          userId: 'test-user-123'
        })

        expect(mockFirestore.addDoc).toHaveBeenCalledWith(
          expect.anything(),
          expect.objectContaining({
            cardId,
            userId: 'test-user-123',
            type: 'meeting',
            title: 'Initial Meeting',
            content: 'Discussed project requirements'
          })
        )
      })

      it('should handle creation errors', async () => {
        mockFirestore.addDoc.mockRejectedValue(new Error('Network error'))

        await expect(
          crmService.createInteraction('card-123', mockInteraction)
        ).rejects.toThrow('Network error')

        expect(crmService.error.value).toContain('Failed to create interaction')
      })

      it('should throw error when user is not authenticated', async () => {
        // Mock no user
        vi.mock('~/composables/useAuth', () => ({
          useAuth: () => ({
            user: { value: null }
          })
        }))

        const service = useFirebaseCRM()
        await expect(
          service.createInteraction('card-123', mockInteraction)
        ).rejects.toThrow('Firebase not initialized or user not authenticated')
      })
    })

    describe('getInteractions', () => {
      const mockInteractionDocs = [
        {
          id: 'int-1',
          data: () => ({
            cardId: 'card-123',
            userId: 'test-user-123',
            type: 'call',
            title: 'Follow-up Call',
            content: 'Discussed next steps',
            timestamp: mockFirestore.Timestamp.fromDate(new Date('2024-01-16')),
            createdAt: mockFirestore.Timestamp.fromDate(new Date('2024-01-16')),
            updatedAt: mockFirestore.Timestamp.fromDate(new Date('2024-01-16'))
          })
        },
        {
          id: 'int-2',
          data: () => ({
            cardId: 'card-123',
            userId: 'test-user-123',
            type: 'email',
            title: 'Proposal Sent',
            content: 'Sent project proposal',
            timestamp: mockFirestore.Timestamp.fromDate(new Date('2024-01-15')),
            createdAt: mockFirestore.Timestamp.fromDate(new Date('2024-01-15')),
            updatedAt: mockFirestore.Timestamp.fromDate(new Date('2024-01-15'))
          })
        }
      ]

      beforeEach(() => {
        mockFirestore.collection.mockReturnValue({})
        mockFirestore.query.mockReturnValue({})
        mockFirestore.orderBy.mockReturnValue({})
        mockFirestore.getDocs.mockResolvedValue({ docs: mockInteractionDocs })
      })

      it('should retrieve all interactions for a card', async () => {
        const interactions = await crmService.getInteractions('card-123')

        expect(interactions).toHaveLength(2)
        expect(interactions[0]).toMatchObject({
          id: 'int-1',
          type: 'call',
          title: 'Follow-up Call'
        })
        expect(interactions[1]).toMatchObject({
          id: 'int-2',
          type: 'email',
          title: 'Proposal Sent'
        })

        // Should be ordered by timestamp descending
        expect(mockFirestore.orderBy).toHaveBeenCalledWith('timestamp', 'desc')
      })

      it('should handle empty results', async () => {
        mockFirestore.getDocs.mockResolvedValue({ docs: [] })

        const interactions = await crmService.getInteractions('card-123')
        expect(interactions).toEqual([])
      })

      it('should handle retrieval errors', async () => {
        mockFirestore.getDocs.mockRejectedValue(new Error('Permission denied'))

        await expect(
          crmService.getInteractions('card-123')
        ).rejects.toThrow('Permission denied')

        expect(crmService.error.value).toContain('Failed to get interactions')
      })
    })

    describe('updateInteraction', () => {
      beforeEach(() => {
        mockFirestore.doc.mockReturnValue({})
        mockFirestore.updateDoc.mockResolvedValue(undefined)
      })

      it('should update an interaction successfully', async () => {
        const updates = {
          title: 'Updated Meeting Title',
          content: 'Updated content',
          timestamp: new Date('2024-01-16T14:00:00Z')
        }

        await crmService.updateInteraction('card-123', 'int-123', updates)

        expect(mockFirestore.updateDoc).toHaveBeenCalledWith(
          expect.anything(),
          expect.objectContaining({
            title: 'Updated Meeting Title',
            content: 'Updated content',
            timestamp: expect.any(Object), // Timestamp object
            updatedAt: expect.any(Object)
          })
        )
      })

      it('should handle partial updates', async () => {
        await crmService.updateInteraction('card-123', 'int-123', {
          title: 'Only Title Updated'
        })

        expect(mockFirestore.updateDoc).toHaveBeenCalledWith(
          expect.anything(),
          expect.objectContaining({
            title: 'Only Title Updated',
            updatedAt: expect.any(Object)
          })
        )
      })

      it('should handle update errors', async () => {
        mockFirestore.updateDoc.mockRejectedValue(new Error('Document not found'))

        await expect(
          crmService.updateInteraction('card-123', 'int-123', { title: 'Test' })
        ).rejects.toThrow('Document not found')

        expect(crmService.error.value).toContain('Failed to update interaction')
      })
    })

    describe('deleteInteraction', () => {
      beforeEach(() => {
        mockFirestore.doc.mockReturnValue({})
        mockFirestore.deleteDoc.mockResolvedValue(undefined)
      })

      it('should delete an interaction successfully', async () => {
        await crmService.deleteInteraction('card-123', 'int-123')

        expect(mockFirestore.deleteDoc).toHaveBeenCalled()
        expect(mockFirestore.doc).toHaveBeenCalledWith(
          mockFirestore,
          'business-cards/card-123/interactions',
          'int-123'
        )
      })

      it('should handle deletion errors', async () => {
        mockFirestore.deleteDoc.mockRejectedValue(new Error('Permission denied'))

        await expect(
          crmService.deleteInteraction('card-123', 'int-123')
        ).rejects.toThrow('Permission denied')

        expect(crmService.error.value).toContain('Failed to delete interaction')
      })
    })
  })

  describe('CRUD Operations - Follow-ups', () => {
    const mockFollowUp: Partial<CRMFollowUp> = {
      type: 'call',
      title: 'Follow-up Call',
      description: 'Check on project progress',
      dueDate: new Date('2024-01-20T14:00:00Z'),
      priority: 'high',
      recurring: 'weekly'
    }

    describe('createFollowUp', () => {
      beforeEach(() => {
        mockFirestore.collection.mockReturnValue({})
        mockFirestore.addDoc.mockResolvedValue({ id: 'followup-123' })
      })

      it('should create a new follow-up successfully', async () => {
        const cardId = 'card-123'
        const result = await crmService.createFollowUp(cardId, mockFollowUp)

        expect(result).toMatchObject({
          ...mockFollowUp,
          id: 'followup-123',
          cardId,
          userId: 'test-user-123',
          completed: false,
          reminderSent: false
        })

        expect(mockFirestore.addDoc).toHaveBeenCalledWith(
          expect.anything(),
          expect.objectContaining({
            cardId,
            userId: 'test-user-123',
            type: 'call',
            title: 'Follow-up Call',
            completed: false,
            recurring: 'weekly'
          })
        )
      })

      it('should calculate next due date for recurring follow-ups', async () => {
        await crmService.createFollowUp('card-123', mockFollowUp)

        const addDocCall = mockFirestore.addDoc.mock.calls[0][1]
        expect(addDocCall.nextDueDate).toBeDefined()
      })

      it('should handle creation without recurring', async () => {
        const nonRecurringFollowUp = { ...mockFollowUp, recurring: null }
        await crmService.createFollowUp('card-123', nonRecurringFollowUp)

        const addDocCall = mockFirestore.addDoc.mock.calls[0][1]
        expect(addDocCall.nextDueDate).toBeUndefined()
      })
    })

    describe('getFollowUps', () => {
      const mockFollowUpDocs = [
        {
          id: 'followup-1',
          data: () => ({
            cardId: 'card-123',
            userId: 'test-user-123',
            type: 'call',
            title: 'Urgent Call',
            dueDate: mockFirestore.Timestamp.fromDate(new Date('2024-01-18')),
            completed: false,
            priority: 'high',
            createdAt: mockFirestore.Timestamp.fromDate(new Date('2024-01-15')),
            updatedAt: mockFirestore.Timestamp.fromDate(new Date('2024-01-15'))
          })
        },
        {
          id: 'followup-2',
          data: () => ({
            cardId: 'card-123',
            userId: 'test-user-123',
            type: 'email',
            title: 'Send Proposal',
            dueDate: mockFirestore.Timestamp.fromDate(new Date('2024-01-20')),
            completed: false,
            priority: 'medium',
            createdAt: mockFirestore.Timestamp.fromDate(new Date('2024-01-15')),
            updatedAt: mockFirestore.Timestamp.fromDate(new Date('2024-01-15'))
          })
        }
      ]

      beforeEach(() => {
        mockFirestore.collection.mockReturnValue({})
        mockFirestore.query.mockReturnValue({})
        mockFirestore.orderBy.mockReturnValue({})
        mockFirestore.where.mockReturnValue({})
        mockFirestore.getDocs.mockResolvedValue({ docs: mockFollowUpDocs })
      })

      it('should retrieve all follow-ups for a card', async () => {
        const followUps = await crmService.getFollowUps('card-123')

        expect(followUps).toHaveLength(2)
        expect(followUps[0].type).toBe('call')
        expect(followUps[1].type).toBe('email')

        // Should be ordered by dueDate ascending
        expect(mockFirestore.orderBy).toHaveBeenCalledWith('dueDate', 'asc')
      })

      it('should filter by completion status', async () => {
        await crmService.getFollowUps('card-123', false)

        expect(mockFirestore.where).toHaveBeenCalledWith('completed', '==', false)
      })

      it('should handle date conversions properly', async () => {
        const followUps = await crmService.getFollowUps('card-123')

        expect(followUps[0].dueDate).toBeInstanceOf(Date)
        expect(followUps[0].completedAt).toBeUndefined()
      })
    })

    describe('updateFollowUp', () => {
      beforeEach(() => {
        mockFirestore.doc.mockReturnValue({})
        mockFirestore.updateDoc.mockResolvedValue(undefined)
      })

      it('should update a follow-up successfully', async () => {
        const updates = {
          completed: true,
          completedAt: new Date('2024-01-19T10:00:00Z')
        }

        await crmService.updateFollowUp('card-123', 'followup-123', updates)

        expect(mockFirestore.updateDoc).toHaveBeenCalledWith(
          expect.anything(),
          expect.objectContaining({
            completed: true,
            completedAt: expect.any(Object),
            updatedAt: expect.any(Object)
          })
        )
      })

      it('should handle date field updates', async () => {
        await crmService.updateFollowUp('card-123', 'followup-123', {
          dueDate: new Date('2024-01-25'),
          reminderTime: new Date('2024-01-24T09:00:00Z')
        })

        const updateCall = mockFirestore.updateDoc.mock.calls[0][1]
        expect(updateCall.dueDate).toBeDefined()
        expect(updateCall.reminderTime).toBeDefined()
      })
    })

    describe('deleteFollowUp', () => {
      beforeEach(() => {
        mockFirestore.doc.mockReturnValue({})
        mockFirestore.deleteDoc.mockResolvedValue(undefined)
      })

      it('should delete a follow-up successfully', async () => {
        await crmService.deleteFollowUp('card-123', 'followup-123')

        expect(mockFirestore.deleteDoc).toHaveBeenCalled()
        expect(mockFirestore.doc).toHaveBeenCalledWith(
          mockFirestore,
          'business-cards/card-123/follow-ups',
          'followup-123'
        )
      })
    })
  })

  describe('CRUD Operations - Notes', () => {
    const mockNote: Partial<CRMNote> = {
      content: 'Important client preference: prefers email communication',
      isPrivate: false,
      tags: ['preferences', 'communication']
    }

    describe('createNote', () => {
      beforeEach(() => {
        mockFirestore.collection.mockReturnValue({})
        mockFirestore.addDoc.mockResolvedValue({ id: 'note-123' })
      })

      it('should create a new note successfully', async () => {
        const cardId = 'card-123'
        const result = await crmService.createNote(cardId, mockNote)

        expect(result).toMatchObject({
          ...mockNote,
          id: 'note-123',
          cardId,
          userId: 'test-user-123'
        })

        expect(mockFirestore.addDoc).toHaveBeenCalledWith(
          expect.anything(),
          expect.objectContaining({
            cardId,
            userId: 'test-user-123',
            content: mockNote.content,
            isPrivate: false,
            tags: ['preferences', 'communication']
          })
        )
      })

      it('should handle private notes', async () => {
        const privateNote = { ...mockNote, isPrivate: true }
        await crmService.createNote('card-123', privateNote)

        const addDocCall = mockFirestore.addDoc.mock.calls[0][1]
        expect(addDocCall.isPrivate).toBe(true)
      })

      it('should set default values', async () => {
        await crmService.createNote('card-123', {})

        const addDocCall = mockFirestore.addDoc.mock.calls[0][1]
        expect(addDocCall.content).toBe('')
        expect(addDocCall.isPrivate).toBe(false)
        expect(addDocCall.tags).toEqual([])
      })
    })

    describe('getNotes', () => {
      const mockNoteDocs = [
        {
          id: 'note-1',
          data: () => ({
            cardId: 'card-123',
            userId: 'test-user-123',
            content: 'Public note',
            isPrivate: false,
            tags: ['general'],
            createdAt: mockFirestore.Timestamp.fromDate(new Date('2024-01-16')),
            updatedAt: mockFirestore.Timestamp.fromDate(new Date('2024-01-16'))
          })
        },
        {
          id: 'note-2',
          data: () => ({
            cardId: 'card-123',
            userId: 'test-user-123',
            content: 'Private note',
            isPrivate: true,
            tags: ['sensitive'],
            createdAt: mockFirestore.Timestamp.fromDate(new Date('2024-01-15')),
            updatedAt: mockFirestore.Timestamp.fromDate(new Date('2024-01-15'))
          })
        }
      ]

      beforeEach(() => {
        mockFirestore.collection.mockReturnValue({})
        mockFirestore.query.mockReturnValue({})
        mockFirestore.orderBy.mockReturnValue({})
        mockFirestore.where.mockReturnValue({})
        mockFirestore.getDocs.mockResolvedValue({ docs: mockNoteDocs })
      })

      it('should retrieve all notes including private by default', async () => {
        const notes = await crmService.getNotes('card-123')

        expect(notes).toHaveLength(2)
        expect(notes.some(n => n.isPrivate)).toBe(true)
        expect(notes.some(n => !n.isPrivate)).toBe(true)

        // Should be ordered by createdAt descending
        expect(mockFirestore.orderBy).toHaveBeenCalledWith('createdAt', 'desc')
      })

      it('should filter out private notes when requested', async () => {
        await crmService.getNotes('card-123', false)

        expect(mockFirestore.where).toHaveBeenCalledWith('isPrivate', '==', false)
      })

      it('should handle date conversions', async () => {
        const notes = await crmService.getNotes('card-123')

        expect(notes[0].createdAt).toBeInstanceOf(Date)
        expect(notes[0].updatedAt).toBeInstanceOf(Date)
      })
    })

    describe('updateNote', () => {
      beforeEach(() => {
        mockFirestore.doc.mockReturnValue({})
        mockFirestore.updateDoc.mockResolvedValue(undefined)
      })

      it('should update a note successfully', async () => {
        const updates = {
          content: 'Updated note content',
          tags: ['updated', 'important']
        }

        await crmService.updateNote('card-123', 'note-123', updates)

        expect(mockFirestore.updateDoc).toHaveBeenCalledWith(
          expect.anything(),
          expect.objectContaining({
            content: 'Updated note content',
            tags: ['updated', 'important'],
            updatedAt: expect.any(Object)
          })
        )
      })

      it('should not allow updating createdAt', async () => {
        await crmService.updateNote('card-123', 'note-123', {
          content: 'Updated',
          createdAt: new Date()
        } as any)

        const updateCall = mockFirestore.updateDoc.mock.calls[0][1]
        expect(updateCall.createdAt).toBeUndefined()
      })

      it('should update privacy settings', async () => {
        await crmService.updateNote('card-123', 'note-123', {
          isPrivate: true
        })

        expect(mockFirestore.updateDoc).toHaveBeenCalledWith(
          expect.anything(),
          expect.objectContaining({
            isPrivate: true
          })
        )
      })
    })

    describe('deleteNote', () => {
      beforeEach(() => {
        mockFirestore.doc.mockReturnValue({})
        mockFirestore.deleteDoc.mockResolvedValue(undefined)
      })

      it('should delete a note successfully', async () => {
        await crmService.deleteNote('card-123', 'note-123')

        expect(mockFirestore.deleteDoc).toHaveBeenCalled()
        expect(mockFirestore.doc).toHaveBeenCalledWith(
          mockFirestore,
          'business-cards/card-123/notes',
          'note-123'
        )
      })
    })
  })

  describe('Real-time Subscriptions', () => {
    describe('subscribeToInteractions', () => {
      let mockCallback: vi.Mock

      beforeEach(() => {
        mockCallback = vi.fn()
        mockFirestore.collection.mockReturnValue({})
        mockFirestore.query.mockReturnValue({})
        mockFirestore.orderBy.mockReturnValue({})
        mockFirestore.onSnapshot.mockImplementation((query, onNext, onError) => {
          // Simulate initial data
          onNext({
            docs: [
              {
                id: 'int-1',
                data: () => ({
                  type: 'call',
                  title: 'Test Call',
                  timestamp: mockFirestore.Timestamp.fromDate(new Date())
                })
              }
            ]
          })
          return mockUnsubscribe
        })
      })

      it('should subscribe to interaction updates', () => {
        const unsubscribe = crmService.subscribeToInteractions('card-123', mockCallback)

        expect(mockFirestore.onSnapshot).toHaveBeenCalled()
        expect(mockCallback).toHaveBeenCalledWith([
          expect.objectContaining({
            id: 'int-1',
            type: 'call',
            title: 'Test Call'
          })
        ])
        expect(unsubscribe).toBe(mockUnsubscribe)
      })

      it('should handle subscription errors', () => {
        mockFirestore.onSnapshot.mockImplementation((query, onNext, onError) => {
          onError(new Error('Permission denied'))
          return mockUnsubscribe
        })

        crmService.subscribeToInteractions('card-123', mockCallback)

        expect(crmService.error.value).toContain('Real-time sync error')
      })

      it('should store unsubscribe function', () => {
        crmService.subscribeToInteractions('card-123', mockCallback)

        // Verify the unsubscribe function is stored
        crmService.unsubscribeCard('card-123')
        expect(mockUnsubscribe).toHaveBeenCalled()
      })
    })

    describe('subscribeToFollowUps', () => {
      let mockCallback: vi.Mock

      beforeEach(() => {
        mockCallback = vi.fn()
        mockFirestore.collection.mockReturnValue({})
        mockFirestore.query.mockReturnValue({})
        mockFirestore.orderBy.mockReturnValue({})
        mockFirestore.where.mockReturnValue({})
        mockFirestore.onSnapshot.mockImplementation((query, onNext) => {
          onNext({
            docs: [
              {
                id: 'followup-1',
                data: () => ({
                  type: 'email',
                  title: 'Send Report',
                  dueDate: mockFirestore.Timestamp.fromDate(new Date()),
                  completed: false
                })
              }
            ]
          })
          return mockUnsubscribe
        })
      })

      it('should subscribe to follow-up updates', () => {
        const unsubscribe = crmService.subscribeToFollowUps('card-123', mockCallback)

        expect(mockFirestore.onSnapshot).toHaveBeenCalled()
        expect(mockCallback).toHaveBeenCalledWith([
          expect.objectContaining({
            id: 'followup-1',
            type: 'email',
            title: 'Send Report',
            completed: false
          })
        ])
        expect(unsubscribe).toBe(mockUnsubscribe)
      })

      it('should apply completion filter', () => {
        crmService.subscribeToFollowUps('card-123', mockCallback, false)

        expect(mockFirestore.where).toHaveBeenCalledWith('completed', '==', false)
      })
    })

    describe('subscribeToNotes', () => {
      let mockCallback: vi.Mock

      beforeEach(() => {
        mockCallback = vi.fn()
        mockFirestore.collection.mockReturnValue({})
        mockFirestore.query.mockReturnValue({})
        mockFirestore.orderBy.mockReturnValue({})
        mockFirestore.where.mockReturnValue({})
        mockFirestore.onSnapshot.mockImplementation((query, onNext) => {
          onNext({
            docs: [
              {
                id: 'note-1',
                data: () => ({
                  content: 'Test note',
                  isPrivate: false,
                  createdAt: mockFirestore.Timestamp.fromDate(new Date()),
                  updatedAt: mockFirestore.Timestamp.fromDate(new Date())
                })
              }
            ]
          })
          return mockUnsubscribe
        })
      })

      it('should subscribe to note updates', () => {
        const unsubscribe = crmService.subscribeToNotes('card-123', mockCallback)

        expect(mockFirestore.onSnapshot).toHaveBeenCalled()
        expect(mockCallback).toHaveBeenCalledWith([
          expect.objectContaining({
            id: 'note-1',
            content: 'Test note',
            isPrivate: false
          })
        ])
        expect(unsubscribe).toBe(mockUnsubscribe)
      })

      it('should apply privacy filter', () => {
        crmService.subscribeToNotes('card-123', mockCallback, false)

        expect(mockFirestore.where).toHaveBeenCalledWith('isPrivate', '==', false)
      })
    })
  })

  describe('Business Card Operations', () => {
    describe('addBusinessCard', () => {
      beforeEach(() => {
        mockFirestore.collection.mockReturnValue({})
        mockFirestore.addDoc.mockResolvedValue({ id: 'card-123' })
      })

      it('should add a business card successfully', async () => {
        const businessCardData = {
          name: 'John Doe',
          company: 'Acme Corp',
          email: '<EMAIL>',
          phone: '+1234567890'
        }

        const cardId = await crmService.addBusinessCard(businessCardData)

        expect(cardId).toBe('card-123')
        expect(mockFirestore.addDoc).toHaveBeenCalledWith(
          expect.anything(),
          expect.objectContaining({
            name: 'John Doe',
            company: 'Acme Corp',
            email: '<EMAIL>',
            phone: '+1234567890'
          })
        )
      })

      it('should handle image field conversion', async () => {
        const businessCardData = {
          name: 'John Doe',
          image: { url: 'https://example.com/image.jpg', width: 100, height: 100 }
        }

        await crmService.addBusinessCard(businessCardData)

        const addDocCall = mockFirestore.addDoc.mock.calls[0][1]
        expect(addDocCall.image).toBe('https://example.com/image.jpg')
      })

      it('should remove undefined and null values', async () => {
        const businessCardData = {
          name: 'John Doe',
          company: undefined,
          email: null,
          phone: '+1234567890'
        }

        await crmService.addBusinessCard(businessCardData)

        const addDocCall = mockFirestore.addDoc.mock.calls[0][1]
        expect(addDocCall.company).toBeUndefined()
        expect(addDocCall.email).toBeUndefined()
        expect(addDocCall.phone).toBe('+1234567890')
      })

      it('should return null on error', async () => {
        mockFirestore.addDoc.mockRejectedValue(new Error('Network error'))

        const cardId = await crmService.addBusinessCard({ name: 'Test' })

        expect(cardId).toBeNull()
        expect(crmService.error.value).toContain('Failed to add business card')
      })
    })
  })

  describe('Cleanup and Unsubscribe', () => {
    beforeEach(() => {
      // Setup multiple subscriptions
      mockFirestore.onSnapshot.mockReturnValue(mockUnsubscribe)
    })

    it('should unsubscribe all listeners', () => {
      // Create multiple subscriptions
      crmService.subscribeToInteractions('card-1', vi.fn())
      crmService.subscribeToFollowUps('card-1', vi.fn())
      crmService.subscribeToNotes('card-1', vi.fn())
      crmService.subscribeToInteractions('card-2', vi.fn())

      // Unsubscribe all
      crmService.unsubscribeAll()

      // Each subscription should be unsubscribed
      expect(mockUnsubscribe).toHaveBeenCalledTimes(4)
    })

    it('should unsubscribe specific card listeners', () => {
      // Create subscriptions for multiple cards
      const unsub1 = vi.fn()
      const unsub2 = vi.fn()
      
      mockFirestore.onSnapshot
        .mockReturnValueOnce(unsub1)
        .mockReturnValueOnce(unsub1)
        .mockReturnValueOnce(unsub1)
        .mockReturnValueOnce(unsub2)

      crmService.subscribeToInteractions('card-1', vi.fn())
      crmService.subscribeToFollowUps('card-1', vi.fn())
      crmService.subscribeToNotes('card-1', vi.fn())
      crmService.subscribeToInteractions('card-2', vi.fn())

      // Unsubscribe only card-1
      crmService.unsubscribeCard('card-1')

      // Only card-1 subscriptions should be unsubscribed
      expect(unsub1).toHaveBeenCalledTimes(3)
      expect(unsub2).not.toHaveBeenCalled()
    })

    it('should handle unsubscribe when no subscriptions exist', () => {
      // Should not throw error
      expect(() => crmService.unsubscribeAll()).not.toThrow()
      expect(() => crmService.unsubscribeCard('non-existent')).not.toThrow()
    })
  })

  describe('Error Handling', () => {
    it('should handle missing Firebase instance', async () => {
      // Mock no firestore
      vi.mock('~/composables/useFirebase', () => ({
        useFirebase: () => ({
          firestore: null
        })
      }))

      const service = useFirebaseCRM()

      await expect(
        service.createInteraction('card-123', {})
      ).rejects.toThrow('Firebase not initialized')
    })

    it('should set error state on failures', async () => {
      mockFirestore.addDoc.mockRejectedValue(new Error('Test error'))

      try {
        await crmService.createInteraction('card-123', {})
      } catch {
        // Expected to throw
      }

      expect(crmService.error.value).toContain('Failed to create interaction')
      expect(crmService.isLoading.value).toBe(false)
    })

    it('should clear error state on successful operations', async () => {
      // First set an error
      mockFirestore.addDoc.mockRejectedValueOnce(new Error('Test error'))
      try {
        await crmService.createInteraction('card-123', {})
      } catch {
        // Expected
      }
      expect(crmService.error.value).not.toBeNull()

      // Then perform successful operation
      mockFirestore.addDoc.mockResolvedValue({ id: 'int-123' })
      await crmService.createInteraction('card-123', {})

      expect(crmService.error.value).toBeNull()
    })
  })

  describe('Privacy Controls', () => {
    it('should respect privacy settings when creating notes', async () => {
      mockFirestore.collection.mockReturnValue({})
      mockFirestore.addDoc.mockResolvedValue({ id: 'note-123' })

      await crmService.createNote('card-123', {
        content: 'Sensitive information',
        isPrivate: true
      })

      expect(mockFirestore.addDoc).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          isPrivate: true
        })
      )
    })

    it('should filter private notes when requested', async () => {
      mockFirestore.collection.mockReturnValue({})
      mockFirestore.query.mockReturnValue({})
      mockFirestore.where.mockReturnValue({})
      mockFirestore.orderBy.mockReturnValue({})
      mockFirestore.getDocs.mockResolvedValue({ docs: [] })

      await crmService.getNotes('card-123', false)

      expect(mockFirestore.where).toHaveBeenCalledWith('isPrivate', '==', false)
    })
  })
})