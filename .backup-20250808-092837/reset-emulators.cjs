#!/usr/bin/env node

/**
 * Reset Firebase Emulators
 * Clears all data from Firebase emulators for a fresh start
 */

const admin = require('firebase-admin');
const { exec } = require('child_process');
const util = require('util');
const execPromise = util.promisify(exec);

// Initialize Firebase Admin with emulator settings
process.env.FIRESTORE_EMULATOR_HOST = 'localhost:8080';
process.env.FIREBASE_AUTH_EMULATOR_HOST = 'localhost:9099';
process.env.FIREBASE_STORAGE_EMULATOR_HOST = 'localhost:9199';

admin.initializeApp({
  projectId: 'covalonic-dev'
});

const db = admin.firestore();

async function clearFirestore() {
  console.log('🗑️  Clearing Firestore...');
  
  const collections = [
    'users',
    'business-cards', 
    'interactions',
    'follow-ups',
    'categories',
    'email-templates'
  ];

  for (const collectionName of collections) {
    try {
      const collection = db.collection(collectionName);
      const snapshot = await collection.get();
      
      if (snapshot.empty) {
        console.log(`  ⚪ Collection '${collectionName}' is already empty`);
        continue;
      }

      // Delete in batches
      const batchSize = 100;
      const batches = [];
      let batch = db.batch();
      let count = 0;

      snapshot.docs.forEach((doc) => {
        batch.delete(doc.ref);
        count++;

        if (count >= batchSize) {
          batches.push(batch.commit());
          batch = db.batch();
          count = 0;
        }
      });

      if (count > 0) {
        batches.push(batch.commit());
      }

      await Promise.all(batches);
      console.log(`  ✓ Cleared collection '${collectionName}' (${snapshot.size} documents)`);
    } catch (error) {
      console.error(`  ✗ Error clearing collection '${collectionName}':`, error.message);
    }
  }
}

async function clearAuth() {
  console.log('\n👥 Clearing Authentication...');
  
  try {
    // The Auth emulator doesn't provide a direct API to clear all users
    // We'll use the REST API endpoint
    const { stdout, stderr } = await execPromise(
      'curl -X DELETE "http://localhost:9099/emulator/v1/projects/covalonic-dev/accounts"'
    );
    
    if (stderr) {
      console.error('  ✗ Error clearing auth:', stderr);
    } else {
      console.log('  ✓ Cleared all authentication users');
    }
  } catch (error) {
    console.error('  ✗ Error clearing auth:', error.message);
  }
}

async function clearStorage() {
  console.log('\n📦 Clearing Storage...');
  
  try {
    // Storage emulator clear via REST API
    const { stdout, stderr } = await execPromise(
      'curl -X DELETE "http://localhost:9199/emulator/v1/projects/covalonic-dev/buckets"'
    );
    
    if (stderr) {
      console.error('  ✗ Error clearing storage:', stderr);
    } else {
      console.log('  ✓ Cleared all storage buckets');
    }
  } catch (error) {
    console.error('  ✗ Error clearing storage:', error.message);
  }
}

async function resetEmulators() {
  console.log('🔄 Resetting Firebase Emulators...\n');

  try {
    await clearFirestore();
    await clearAuth();
    await clearStorage();

    console.log('\n✅ All emulators have been reset!');
    console.log('📝 Run "npm run seed" to populate with test data');
  } catch (error) {
    console.error('\n❌ Reset failed:', error);
    process.exit(1);
  }
}

// Run reset
resetEmulators()
  .then(() => process.exit(0))
  .catch(error => {
    console.error(error);
    process.exit(1);
  });