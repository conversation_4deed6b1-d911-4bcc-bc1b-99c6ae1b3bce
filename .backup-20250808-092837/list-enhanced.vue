<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useState } from '#app'
import { useGeographicFiltering, type BusinessCardLocation } from '~/composables/useGeographicFiltering'
import { useContacts } from '~/composables/useContacts'
import { useBusinesses } from '~/composables/useBusinesses'
import { useCurrentUser } from '~/composables/useCurrentUser'
import GeographicFilter from '~/components/businesscards/GeographicFilter.vue'
import BusinessCardMap from '~/components/businesscards/BusinessCardMap.vue'

definePageMeta({
  layout: "covalonic",
  middleware: "auth"
})

// Composables
const { contacts, subscribeToContacts, getFullName } = useContacts()
const { businesses } = useBusinesses()
const { currentUser } = useCurrentUser()

// State
const isLoading = ref(true)
const error = ref<string | null>(null)
const searchQuery = ref('')
const selectedCard = ref<BusinessCardLocation | null>(null)
const viewMode = ref<'list' | 'map' | 'both'>('both')
const showOnlyCRMLeads = ref(false) // Toggle to show only CRM leads

// Geographic filtering
const {
  currentLocation,
  isLocationLoading,
  activeFilters,
  hasActiveFilters,
  filterSummary,
  filterByDistance,
  getCurrentLocation
} = useGeographicFiltering()

// Subscribe to contacts on mount
onMounted(async () => {
  if (currentUser.value?.uid) {
    try {
      // Subscribe based on toggle
      if (showOnlyCRMLeads.value) {
        await subscribeToContacts({ type: 'lead', ownerId: currentUser.value.uid })
      } else {
        await subscribeToContacts({ ownerId: currentUser.value.uid })
      }
    } catch (error) {
      console.error('Error loading contacts:', error)
      error.value = 'Failed to load business cards'
    } finally {
      isLoading.value = false
    }
  } else {
    isLoading.value = false
  }
})

// Watch for toggle changes
watch(showOnlyCRMLeads, async (newValue) => {
  if (currentUser.value?.uid) {
    isLoading.value = true
    error.value = null
    try {
      if (newValue) {
        await subscribeToContacts({ type: 'lead', ownerId: currentUser.value.uid })
      } else {
        await subscribeToContacts({ ownerId: currentUser.value.uid })
      }
    } catch (error) {
      console.error('Error reloading contacts:', error)
      error.value = 'Failed to reload business cards'
    } finally {
      isLoading.value = false
    }
  }
})

// Convert contacts to location format for map
const businessCardLocations = computed<BusinessCardLocation[]>(() => {
  return contacts.value
    .filter(contact => {
      // Filter by CRM leads if enabled
      if (showOnlyCRMLeads.value) {
        return contact.type === 'lead'
      }
      return true
    })
    .map(contact => {
      // Get business info if linked
      const business = contact.businessId 
        ? businesses.value.find(b => b.id === contact.businessId)
        : null
      
      // Extract location data
      const lat = contact.address?.coordinates?.lat || null
      const lng = contact.address?.coordinates?.lng || null
      
      return {
        id: contact.id,
        name: getFullName(contact),
        company: business?.name || 'No company',
        category: contact.tags?.[0] || 'General',
        lat: lat || 37.7749, // Default to SF if no location
        lng: lng || -122.4194,
        address: {
          street: contact.address?.street || '',
          city: contact.address?.city || 'San Francisco',
          state: contact.address?.state || 'CA',
          country: contact.address?.country || 'United States',
          postalCode: contact.address?.postalCode || ''
        },
        phone: contact.phone,
        email: contact.email,
        website: contact.website,
        status: contact.status,
        isLead: contact.type === 'lead',
        leadNotes: contact.leadNotes,
        createdAt: contact.createdAt
      }
    })
})

// Computed properties
const displayedCards = computed(() => {
  let cards = [...businessCardLocations.value]

  // Apply text search filter
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    cards = cards.filter(card => 
      card.name.toLowerCase().includes(query) ||
      card.company.toLowerCase().includes(query) ||
      card.category?.toLowerCase().includes(query) ||
      card.address?.city?.toLowerCase().includes(query) ||
      card.email?.toLowerCase().includes(query) ||
      card.phone?.toLowerCase().includes(query)
    )
  }

  // Apply geographic filters
  if (hasActiveFilters.value) {
    // Filter by text-based location filters
    if (activeFilters.country) {
      cards = cards.filter(card => 
        card.address?.country === activeFilters.country
      )
    }
    if (activeFilters.state) {
      cards = cards.filter(card => 
        card.address?.state === activeFilters.state
      )
    }
    if (activeFilters.city) {
      cards = cards.filter(card => 
        card.address?.city === activeFilters.city
      )
    }

    // Filter by distance if radius is set and we have user location
    if (activeFilters.radius && currentLocation.value) {
      cards = filterByDistance(cards, activeFilters.radius)
    }
  }

  return cards
})

const stats = computed(() => ({
  total: displayedCards.value.length,
  byCategory: displayedCards.value.reduce((acc, card) => {
    const category = card.category || 'Other'
    acc[category] = (acc[category] || 0) + 1
    return acc
  }, {} as Record<string, number>)
}))

// Methods
const handleCardSelect = (card: BusinessCardLocation) => {
  selectedCard.value = card
}

const handleFilterUpdate = (filters: any) => {
  // Filters are automatically applied through the composable
}

const clearSelection = () => {
  selectedCard.value = null
}

// Watch for changes in loading state
const loading = computed(() => isLoading.value || isLocationLoading.value)
</script>

<template>
  <div class="min-h-screen font-sans relative overflow-hidden">
    <!-- Dark gradient background -->
    <div class="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900"></div>

    <!-- Decorative elements -->
    <div class="absolute top-20 right-20 w-96 h-96 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl"></div>
    <div class="absolute bottom-40 left-20 w-80 h-80 bg-gradient-to-br from-purple-400/20 to-blue-400/20 rounded-full blur-3xl"></div>
    <div class="absolute top-1/2 right-1/3 w-64 h-64 bg-gradient-to-br from-red-400/15 to-pink-400/15 rounded-full blur-2xl"></div>

    <div class="relative z-10 px-6 py-8">
      <div class="max-w-screen-xl mx-auto">
        <!-- Header Section -->
        <div class="mb-8">
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div class="mb-4 sm:mb-0">
              <div class="flex items-center">
                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-3 shadow-lg">
                  <Icon name="mdi:map" class="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 class="text-xl font-bold text-white">Business Card Map</h1>
                  <p class="text-blue-300 text-sm">
                    {{ showOnlyCRMLeads ? 'CRM Leads' : 'All Contacts' }} - Find and filter by location
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
            
        <!-- Compact Controls Section -->
        <div class="flex flex-col gap-3 mb-4">
          <!-- Top Row: CRM Toggle and Filters -->
          <div class="flex items-center justify-between gap-3">
            <!-- CRM Leads Toggle - Compact -->
            <div class="flex items-center bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-lg border border-white/10 p-2">
              <label class="flex items-center cursor-pointer">
                <input 
                  v-model="showOnlyCRMLeads" 
                  type="checkbox" 
                  class="sr-only"
                  :id="`crm-toggle-${Math.random()}`"
                >
                <div class="relative">
                  <div 
                    class="w-9 h-5 rounded-full transition-colors duration-200 focus:outline-none cursor-pointer"
                    :class="showOnlyCRMLeads ? 'bg-blue-600' : 'bg-gray-700'"
                  >
                    <div 
                      class="absolute top-0.5 left-0.5 w-4 h-4 bg-white rounded-full transition-transform duration-200 shadow-sm"
                      :class="showOnlyCRMLeads ? 'translate-x-4' : 'translate-x-0'"
                    ></div>
                  </div>
                </div>
                <span class="ml-2 text-xs font-medium text-gray-300">CRM Leads Only</span>
              </label>
            </div>

            <!-- Collapsible Filters -->
            <div class="flex-1 max-w-xs">
              <details class="group">
                <summary class="list-none cursor-pointer">
                  <div class="flex items-center justify-between p-2 bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-lg border border-white/10 hover:border-white/20 transition-all duration-300">
                    <div class="flex items-center">
                      <Icon name="mdi:filter" class="w-4 h-4 mr-2 text-blue-400" />
                      <span class="text-sm font-medium text-white">Filters</span>
                      <span v-if="filterSummary && filterSummary !== 'No location filters'" class="ml-2 text-xs text-blue-400">
                        ({{ filterSummary }})
                      </span>
                    </div>
                    <Icon name="mdi:chevron-down" class="w-4 h-4 text-gray-400 transition-transform duration-200 group-open:rotate-180" />
                  </div>
                </summary>
                <div class="mt-2">
                  <GeographicFilter 
                    :business-cards="businessCardLocations"
                    @filter-update="handleFilterUpdate"
                  />
                </div>
              </details>
            </div>
          </div>

          <!-- Bottom Row: Search and View Toggle -->
          <div class="flex items-center gap-2">
            <!-- Search Bar - Compact -->
            <div class="relative flex-1">
              <input
                v-model="searchQuery"
                type="text"
                placeholder="Search..."
                class="w-full px-3 py-2 pl-9 bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm border border-white/10 rounded-lg text-white text-sm placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              >
              <Icon name="mdi:magnify" class="absolute left-3 top-2.5 w-4 h-4 text-gray-400" />
            </div>

            <!-- View Mode Toggle - Compact -->
            <div class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-lg border border-white/10 p-1">
              <div class="inline-flex" role="group">
                <button
                  @click="viewMode = 'list'"
                  :class="[
                    'px-2 py-1 text-sm rounded-l transition-all duration-200',
                    viewMode === 'list' 
                      ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white' 
                      : 'text-gray-300 hover:text-white hover:bg-white/10'
                  ]"
                >
                  <Icon name="mdi:view-list" class="w-3 h-3" />
                </button>
                <button
                  @click="viewMode = 'both'"
                  :class="[
                    'px-2 py-1 text-sm transition-all duration-200',
                    viewMode === 'both' 
                      ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white' 
                      : 'text-gray-300 hover:text-white hover:bg-white/10'
                  ]"
                >
                  <Icon name="mdi:view-dashboard" class="w-3 h-3" />
                </button>
                <button
                  @click="viewMode = 'map'"
                  :class="[
                    'px-2 py-1 text-sm rounded-r transition-all duration-200',
                    viewMode === 'map' 
                      ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white' 
                      : 'text-gray-300 hover:text-white hover:bg-white/10'
                  ]"
                >
                  <Icon name="mdi:map" class="w-3 h-3" />
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Compact Results Summary -->
        <div class="mb-3 flex items-center justify-between text-sm">
          <div class="text-gray-300">
            <span v-if="loading" class="flex items-center">
              <Icon name="mdi:loading" class="w-4 h-4 mr-1 animate-spin" />
              Loading...
            </span>
            <span v-else class="flex items-center">
              <Icon name="mdi:account-group" class="w-4 h-4 mr-1 text-blue-400" />
              <span class="font-semibold text-white">{{ displayedCards.length }}</span>
              <span class="ml-1">{{ showOnlyCRMLeads ? 'lead' : 'contact' }}{{ displayedCards.length !== 1 ? 's' : '' }}</span>
            </span>
          </div>
          <div v-if="Object.keys(stats.byCategory).length > 0" class="flex items-center gap-1">
            <span 
              v-for="(count, category) in stats.byCategory" 
              :key="category"
              class="inline-flex items-center px-2 py-1 rounded text-xs bg-blue-600/20 text-blue-300"
            >
              {{ category }}: {{ count }}
            </span>
          </div>
        </div>

        <!-- Content Area -->
        <div v-if="loading" class="text-center py-16">
          <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-600/20 to-purple-600/20 rounded-full mb-4">
            <Icon name="mdi:loading" class="w-10 h-10 text-blue-400 animate-spin" />
          </div>
          <p class="text-lg text-gray-300">Loading business cards...</p>
        </div>

        <div v-else-if="error" class="text-center py-16">
          <div class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-xl border border-red-400/20 p-12 max-w-lg mx-auto">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-red-600/20 to-red-700/20 rounded-full mb-6">
              <Icon name="mdi:alert-circle" class="w-12 h-12 text-red-400" />
            </div>
            <h3 class="text-xl font-semibold text-white mb-3">Error Loading Cards</h3>
            <p class="text-red-300 mb-6">{{ error }}</p>
            <button 
              @click="$router.go(0)" 
              class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-opacity-50"
            >
              <Icon name="mdi:refresh" class="mr-2 h-5 w-5" />
              Retry
            </button>
          </div>
        </div>

        <div v-else-if="displayedCards.length === 0" class="text-center py-16">
          <div class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-xl border border-white/10 p-12 max-w-lg mx-auto">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-gray-700/50 to-gray-800/50 rounded-full mb-6">
              <Icon name="mdi:map-marker-off" class="w-12 h-12 text-gray-400" />
            </div>
            <h3 class="text-xl font-semibold text-white mb-3">No business cards found</h3>
            <p class="text-gray-300">
              <span v-if="searchQuery || hasActiveFilters">
                Try adjusting your search or filters
              </span>
              <span v-else-if="showOnlyCRMLeads">
                You haven't added any CRM leads yet.
              </span>
              <span v-else>
                No business cards available.
              </span>
            </p>
            <NuxtLink 
              v-if="!searchQuery && !hasActiveFilters && showOnlyCRMLeads"
              to="/c/businesscards-scan" 
              class="inline-flex items-center mt-6 px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50"
            >
              <Icon name="mdi:qrcode-scan" class="mr-2 h-5 w-5" />
              Scan Your First Card
            </NuxtLink>
          </div>
        </div>

        <div v-else class="grid grid-cols-1" :class="viewMode === 'both' ? 'lg:grid-cols-2' : ''" gap="6">
          <!-- List View -->
          <div v-if="viewMode === 'list' || viewMode === 'both'" class="space-y-4">
            <div
              v-for="card in displayedCards"
              :key="card.id"
              @click="handleCardSelect(card)"
              :class="[
                'group relative overflow-hidden cursor-pointer transition-all duration-300',
                selectedCard?.id === card.id 
                  ? 'scale-[1.02]' 
                  : 'hover:scale-[1.01]'
              ]"
            >
              <!-- Card Background with Gradient -->
              <div :class="[
                'absolute inset-0 bg-gradient-to-br rounded-xl transition-all duration-300',
                selectedCard?.id === card.id
                  ? 'from-blue-600/30 to-purple-600/30'
                  : 'from-gray-900/70 via-gray-800/70 to-gray-900/70 group-hover:from-gray-800/80 group-hover:to-gray-900/80'
              ]"></div>
              
              <!-- Card Border Gradient -->
              <div :class="[
                'relative bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-xl border transition-all duration-300 p-5',
                selectedCard?.id === card.id
                  ? 'border-blue-400/50 shadow-lg shadow-blue-500/20'
                  : 'border-white/10 group-hover:border-white/20'
              ]">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <div class="flex items-center flex-wrap gap-2 mb-2">
                      <h3 class="text-lg font-semibold text-white group-hover:text-blue-300 transition-colors">{{ card.name }}</h3>
                      <span v-if="card.isLead" class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-blue-600/20 to-blue-700/20 text-blue-300 border border-blue-400/20">
                        <Icon name="mdi:account-star" class="w-3 h-3 mr-1" />
                        CRM Lead
                      </span>
                      <span v-if="card.status === 'unclaimed'" class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-orange-600/20 to-orange-700/20 text-orange-300 border border-orange-400/20">
                        <Icon name="mdi:alert-circle" class="w-3 h-3 mr-1" />
                        Unclaimed
                      </span>
                    </div>
                    <p class="text-sm text-gray-300 mb-3">{{ card.company }}</p>
                
                    <div class="space-y-2">
                      <p v-if="card.email" class="flex items-center text-sm text-gray-400 group-hover:text-gray-300 transition-colors">
                        <Icon name="mdi:email" class="w-4 h-4 mr-2 text-blue-400/60" />
                        {{ card.email }}
                      </p>
                      <p v-if="card.phone" class="flex items-center text-sm text-gray-400 group-hover:text-gray-300 transition-colors">
                        <Icon name="mdi:phone" class="w-4 h-4 mr-2 text-green-400/60" />
                        {{ card.phone }}
                      </p>
                      <p class="flex items-center text-sm text-gray-400 group-hover:text-gray-300 transition-colors">
                        <Icon name="mdi:map-marker" class="w-4 h-4 mr-2 text-red-400/60" />
                        {{ card.address.city }}, {{ card.address.state }}
                      </p>
                    </div>

                    <div v-if="card.leadNotes" class="mt-3 p-3 bg-gradient-to-br from-blue-900/20 to-purple-900/20 rounded-lg border border-white/5">
                      <p class="flex items-start text-sm text-gray-300">
                        <Icon name="mdi:note-text" class="w-4 h-4 mr-2 mt-0.5 text-purple-400/60 flex-shrink-0" />
                        {{ card.leadNotes }}
                      </p>
                    </div>
                  </div>
                  
                  <div class="ml-4">
                    <span class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-gradient-to-r from-purple-600/20 to-pink-600/20 text-purple-300 border border-purple-400/20">
                      <Icon name="mdi:tag" class="w-3 h-3 mr-1" />
                      {{ card.category }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Map View -->
          <div v-if="viewMode === 'map' || viewMode === 'both'" class="relative">
            <div class="absolute inset-0 bg-gradient-to-br from-blue-600/10 to-purple-600/10 rounded-xl blur-2xl"></div>
            <div class="relative h-[600px] bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-xl border border-white/10 overflow-hidden">
              <BusinessCardMap
                :business-cards="displayedCards"
                :selected-card="selectedCard"
                :user-location="currentLocation"
                @card-select="handleCardSelect"
              />
            </div>
          </div>
        </div>

        <!-- Selected Card Detail Modal -->
        <Teleport to="body">
          <div 
            v-if="selectedCard" 
            @click="clearSelection"
            class="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          >
            <div 
              @click.stop
              class="relative bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 rounded-2xl shadow-2xl max-w-md w-full p-6 border border-white/20 transform transition-all duration-300 scale-100"
            >
              <!-- Modal Glow Effect -->
              <div class="absolute inset-0 bg-gradient-to-br from-blue-600/20 to-purple-600/20 rounded-2xl blur-xl -z-10"></div>
              <div class="flex items-start justify-between mb-6">
                <div>
                  <h3 class="text-2xl font-bold text-white mb-1">{{ selectedCard.name }}</h3>
                  <p class="text-lg text-gray-300">{{ selectedCard.company }}</p>
                  <div class="flex items-center gap-2 mt-2">
                    <span v-if="selectedCard.isLead" class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-blue-600/20 to-blue-700/20 text-blue-300 border border-blue-400/20">
                      <Icon name="mdi:account-star" class="w-3 h-3 mr-1" />
                      CRM Lead
                    </span>
                    <span v-if="selectedCard.status === 'unclaimed'" class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-orange-600/20 to-orange-700/20 text-orange-300 border border-orange-400/20">
                      <Icon name="mdi:alert-circle" class="w-3 h-3 mr-1" />
                      Unclaimed
                    </span>
                  </div>
                </div>
                <button 
                  @click="clearSelection"
                  class="p-2 text-gray-400 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200"
                >
                  <Icon name="mdi:close" class="w-6 h-6" />
                </button>
              </div>

              <div class="space-y-4">
                <div v-if="selectedCard.email" class="group">
                  <label class="text-xs text-gray-500 uppercase tracking-wider">Email</label>
                  <p class="text-white flex items-center mt-1">
                    <Icon name="mdi:email" class="w-4 h-4 mr-2 text-blue-400" />
                    {{ selectedCard.email }}
                  </p>
                </div>

                <div v-if="selectedCard.phone" class="group">
                  <label class="text-xs text-gray-500 uppercase tracking-wider">Phone</label>
                  <p class="text-white flex items-center mt-1">
                    <Icon name="mdi:phone" class="w-4 h-4 mr-2 text-green-400" />
                    {{ selectedCard.phone }}
                  </p>
                </div>

                <div v-if="selectedCard.website" class="group">
                  <label class="text-xs text-gray-500 uppercase tracking-wider">Website</label>
                  <p class="text-white flex items-center mt-1">
                    <Icon name="mdi:web" class="w-4 h-4 mr-2 text-purple-400" />
                    {{ selectedCard.website }}
                  </p>
                </div>

                <div class="group">
                  <label class="text-xs text-gray-500 uppercase tracking-wider">Address</label>
                  <p class="text-white flex items-start mt-1">
                    <Icon name="mdi:map-marker" class="w-4 h-4 mr-2 mt-0.5 text-red-400 flex-shrink-0" />
                    <span>
                      {{ selectedCard.address.street }}<br v-if="selectedCard.address.street">
                      {{ selectedCard.address.city }}, {{ selectedCard.address.state }} {{ selectedCard.address.postalCode }}<br>
                      {{ selectedCard.address.country }}
                    </span>
                  </p>
                </div>

                <div v-if="selectedCard.leadNotes" class="group">
                  <label class="text-xs text-gray-500 uppercase tracking-wider">Lead Notes</label>
                  <div class="mt-2 p-3 bg-gradient-to-br from-blue-900/20 to-purple-900/20 rounded-lg border border-white/5">
                    <p class="text-gray-300 flex items-start">
                      <Icon name="mdi:note-text" class="w-4 h-4 mr-2 mt-0.5 text-purple-400 flex-shrink-0" />
                      {{ selectedCard.leadNotes }}
                    </p>
                  </div>
                </div>
              </div>

              <div class="mt-6 flex space-x-3">
                <NuxtLink 
                  :to="`/c/contacts/${selectedCard.id}`"
                  class="flex-1 inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50"
                >
                  View
                </NuxtLink>
                <button 
                  @click="clearSelection"
                  class="flex-1 inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-gray-700/50 to-gray-800/50 hover:from-gray-600/50 hover:to-gray-700/50 text-white font-medium rounded-lg transition-all duration-200 border border-white/10 hover:border-white/20"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </Teleport>
      </div>
    </div>
  </div>
</template>