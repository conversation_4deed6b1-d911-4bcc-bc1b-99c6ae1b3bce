#!/usr/bin/env node

/**
 * Check Business Card Collections Script
 * This script checks which business card collections exist and have data
 */

import admin from 'firebase-admin';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Initialize Firebase Admin SDK
const serviceAccount = JSON.parse(
  readFileSync(join(__dirname, '../covalonic-8f02d-firebase-adminsdk-fbsvc-d119b2f775.json'), 'utf8')
);

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  projectId: 'covalonic-8f02d'
});

const db = admin.firestore();

async function checkCollections() {
  try {
    console.log('🔍 Checking business card collections...\n');
    
    const collectionsToCheck = ['business-cards', 'businesscards'];
    
    for (const collectionName of collectionsToCheck) {
      console.log(`📊 Checking collection: ${collectionName}`);
      
      try {
        const collectionRef = db.collection(collectionName);
        const snapshot = await collectionRef.limit(5).get();
        
        if (snapshot.empty) {
          console.log(`   ❌ Collection '${collectionName}' is empty or doesn't exist`);
        } else {
          console.log(`   ✅ Collection '${collectionName}' has ${snapshot.size} documents (showing first 5)`);
          
          snapshot.forEach((doc) => {
            const data = doc.data();
            const cardName = data.name || `${data.first_name || ''} ${data.last_name || ''}`.trim() || 'Unknown';
            console.log(`      - ${doc.id}: ${cardName} (status: ${data.moderationStatus || 'none'}, public: ${data.isPublic || 'false'})`);
          });
        }
      } catch (error) {
        console.log(`   ❌ Error accessing collection '${collectionName}':`, error.message);
      }
      
      console.log(''); // Empty line for readability
    }
    
    // Also list all collections in the database
    console.log('📋 Listing all collections in the database:');
    try {
      const collections = await db.listCollections();
      if (collections.length === 0) {
        console.log('   ❌ No collections found in database');
      } else {
        collections.forEach((collection) => {
          console.log(`   - ${collection.id}`);
        });
      }
    } catch (error) {
      console.log('   ❌ Error listing collections:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Error checking collections:', error);
  } finally {
    process.exit(0);
  }
}

// Run the script
checkCollections();
