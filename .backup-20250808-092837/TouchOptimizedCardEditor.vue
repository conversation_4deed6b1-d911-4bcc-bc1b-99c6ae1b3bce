<template>
  <div 
    ref="editorContainer"
    class="touch-card-editor"
    :class="{ 
      'editing-active': isEditing,
      'gesture-active': gestureState.isActive,
      'multi-touch': gestureState.isMultiTouch
    }"
  >
    <!-- Card Preview Container -->
    <div 
      ref="cardPreview"
      class="card-preview-container"
      :style="cardPreviewStyle"
    >
      <!-- Background Layer -->
      <div 
        class="card-background"
        :style="{ backgroundColor: cardData.backgroundColor || '#ffffff' }"
      />
      
      <!-- Editable Elements Layer -->
      <div class="card-elements-layer">
        <TouchEditableField
          v-for="field in editableFields"
          :key="field.id"
          :field="field"
          :is-selected="selectedFieldId === field.id"
          :scale="zoomLevel"
          @select="selectField"
          @update="updateField"
          @drag-start="onFieldDragStart"
          @drag="onFieldDrag"
          @drag-end="onFieldDragEnd"
          @resize-start="onFieldResizeStart"
          @resize="onFieldResize"
          @resize-end="onFieldResizeEnd"
        />
      </div>
      
      <!-- Touch Overlay for Gestures -->
      <div 
        ref="touchOverlay"
        class="touch-overlay"
        :class="{ 'overlay-active': gestureState.isActive }"
      />
      
      <!-- Selection Handles -->
      <div 
        v-if="selectedField"
        class="selection-handles"
        :style="selectionHandlesStyle"
      >
        <div 
          v-for="handle in selectionHandles"
          :key="handle.position"
          class="selection-handle"
          :class="`handle-${handle.position}`"
          :style="handle.style"
          @touchstart="startHandleDrag(handle, $event)"
        />
      </div>
      
      <!-- Gesture Feedback -->
      <div 
        v-if="showGestureFeedback"
        class="gesture-feedback"
        :class="gestureFeedbackClass"
      >
        <Icon :name="gestureFeedbackIcon" class="w-8 h-8" />
        <span class="gesture-text">{{ gestureFeedbackText }}</span>
      </div>
    </div>
    
    <!-- Touch-Optimized Toolbar -->
    <div 
      v-if="isEditing"
      class="touch-toolbar"
      :class="{ 'toolbar-visible': showToolbar }"
    >
      <!-- Primary Actions -->
      <div class="toolbar-section primary-actions">
        <TouchButton
          icon="mdi:undo"
          :disabled="!canUndo"
          @tap="performUndo"
          haptic="light"
        />
        <TouchButton
          icon="mdi:redo"
          :disabled="!canRedo"
          @tap="performRedo"
          haptic="light"
        />
        <TouchButton
          icon="mdi:content-copy"
          :disabled="!selectedField"
          @tap="copyField"
          haptic="selection"
        />
        <TouchButton
          icon="mdi:content-paste"
          :disabled="!clipboardField"
          @tap="pasteField"
          haptic="selection"
        />
      </div>
      
      <!-- Field Type Actions -->
      <div class="toolbar-section field-actions">
        <TouchButton
          icon="mdi:text-box"
          @tap="addTextField"
          haptic="medium"
        />
        <TouchButton
          icon="mdi:image"
          @tap="addImageField"
          haptic="medium"
        />
        <TouchButton
          icon="mdi:qrcode"
          @tap="addQRField"
          haptic="medium"
        />
      </div>
      
      <!-- Style Actions -->
      <div 
        v-if="selectedField"
        class="toolbar-section style-actions"
      >
        <TouchColorPicker
          v-model="selectedField.color"
          @change="updateFieldStyle"
        />
        <TouchFontSelector
          v-model="selectedField.fontFamily"
          @change="updateFieldStyle"
        />
        <TouchButton
          icon="mdi:format-bold"
          :active="selectedField.fontWeight === 'bold'"
          @tap="toggleBold"
          haptic="light"
        />
        <TouchButton
          icon="mdi:format-italic"
          :active="selectedField.fontStyle === 'italic'"
          @tap="toggleItalic"
          haptic="light"  
        />
      </div>
    </div>
    
    <!-- Touch Keyboard Spacer -->
    <div 
      v-if="isVirtualKeyboardVisible"
      class="keyboard-spacer"
      :style="{ height: `${virtualKeyboardHeight}px` }"
    />
  </div>
</template>

<script setup lang="ts">
import type { BusinessCard, CardField } from '~/types/business-cards'

interface Props {
  card: BusinessCard
  readonly?: boolean
  zoomLevel?: number
  enableAdvancedGestures?: boolean
}

interface Emits {
  (e: 'update:card', card: BusinessCard): void
  (e: 'field-selected', field: CardField | null): void
  (e: 'edit-mode-changed', isEditing: boolean): void
  (e: 'zoom-changed', level: number): void
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  zoomLevel: 1,
  enableAdvancedGestures: true
})

const emit = defineEmits<Emits>()

// Template refs
const editorContainer = ref<HTMLElement>()
const cardPreview = ref<HTMLElement>()
const touchOverlay = ref<HTMLElement>()

// Core state
const cardData = ref<BusinessCard>({ ...props.card })
const isEditing = ref(false)
const selectedFieldId = ref<string | null>(null)
const editableFields = ref<CardField[]>([])

// Touch and gesture state
const showToolbar = ref(false)
const showGestureFeedback = ref(false)
const gestureFeedbackIcon = ref('')
const gestureFeedbackText = ref('')
const gestureFeedbackClass = ref('')

// Virtual keyboard handling
const isVirtualKeyboardVisible = ref(false)
const virtualKeyboardHeight = ref(0)

// Undo/Redo system
const undoStack = ref<BusinessCard[]>([])
const redoStack = ref<BusinessCard[]>([])
const maxUndoSteps = ref(20)

// Clipboard
const clipboardField = ref<CardField | null>(null)

// Advanced gestures integration
const { state: gestureState } = useAdvancedGestures(
  touchOverlay,
  {
    onTap: handleTap,
    onDoubleTap: handleDoubleTap,
    onLongPress: handleLongPress,
    onPinch: handlePinch,
    onRotate: handleRotate,
    onSwipeUp: handleSwipeUp,
    onSwipeDown: handleSwipeDown,
    onTwoFingerSwipe: handleTwoFingerSwipe,
    onThreeFingerSwipe: handleThreeFingerSwipe,
    onDragStart: handleDragStart,
    onDrag: handleDrag,
    onDragEnd: handleDragEnd,
    onGestureStart: handleGestureStart,
    onGestureEnd: handleGestureEnd
  },
  {
    enabled: props.enableAdvancedGestures && !props.readonly,
    enableMultiTouch: true,
    enablePinch: true,
    enableRotation: true,
    enableLongPress: true,
    longPressDelay: 500,
    doubleTapDelay: 300,
    velocityThreshold: 0.5,
    enablePrediction: true,
    enableSmoothing: true
  }
)

// Haptic feedback
const { 
  triggerHaptic, 
  lightHaptic, 
  mediumHaptic, 
  heavyHaptic,
  selectionHaptic,
  errorHaptic,
  successHaptic
} = useHaptics()

// Computed properties
const selectedField = computed(() => 
  selectedFieldId.value 
    ? editableFields.value.find(f => f.id === selectedFieldId.value) 
    : null
)

const canUndo = computed(() => undoStack.value.length > 0)
const canRedo = computed(() => redoStack.value.length > 0)

const cardPreviewStyle = computed(() => ({
  transform: `scale(${props.zoomLevel})`,
  transformOrigin: 'center center',
  transition: gestureState.isActive ? 'none' : 'transform 0.3s ease'
}))

const selectionHandlesStyle = computed(() => {
  if (!selectedField.value) return {}
  
  return {
    left: `${selectedField.value.x}px`,
    top: `${selectedField.value.y}px`,
    width: `${selectedField.value.width}px`,
    height: `${selectedField.value.height}px`,
    transform: `scale(${props.zoomLevel})`
  }
})

const selectionHandles = computed(() => {
  if (!selectedField.value) return []
  
  const handles = [
    { position: 'nw', style: { top: '-4px', left: '-4px' } },
    { position: 'ne', style: { top: '-4px', right: '-4px' } },
    { position: 'sw', style: { bottom: '-4px', left: '-4px' } },
    { position: 'se', style: { bottom: '-4px', right: '-4px' } },
    { position: 'n', style: { top: '-4px', left: '50%', transform: 'translateX(-50%)' } },
    { position: 'e', style: { right: '-4px', top: '50%', transform: 'translateY(-50%)' } },
    { position: 's', style: { bottom: '-4px', left: '50%', transform: 'translateX(-50%)' } },
    { position: 'w', style: { left: '-4px', top: '50%', transform: 'translateY(-50%)' } }
  ]
  
  return handles
})

// Initialize card data
watch(() => props.card, (newCard) => {
  cardData.value = { ...newCard }
  initializeEditableFields()
}, { immediate: true, deep: true })

// Gesture handlers
const handleTap = (state: any) => {
  if (props.readonly) return
  
  const tapPoint = { x: state.center.x, y: state.center.y }
  const tappedField = findFieldAtPoint(tapPoint)
  
  if (tappedField) {
    selectField(tappedField.id)
    selectionHaptic()
  } else {
    selectField(null)
  }
}

const handleDoubleTap = (state: any) => {
  if (props.readonly) return
  
  const tapPoint = { x: state.center.x, y: state.center.y }
  const tappedField = findFieldAtPoint(tapPoint)
  
  if (tappedField && tappedField.type === 'text') {
    // Enter text editing mode
    enterTextEditMode(tappedField)
    mediumHaptic()
  } else {
    // Toggle edit mode
    toggleEditMode()
  }
}

const handleLongPress = (state: any) => {
  if (props.readonly) return
  
  const pressPoint = { x: state.center.x, y: state.center.y }
  const pressedField = findFieldAtPoint(pressPoint)
  
  if (pressedField) {
    // Show context menu
    showFieldContextMenu(pressedField, pressPoint)
  } else {
    // Show general context menu
    showGeneralContextMenu(pressPoint)
  }
}

const handlePinch = (state: any) => {
  if (props.readonly) return
  
  if (selectedField.value) {
    // Scale selected field
    const scaleFactor = state.scale
    resizeField(selectedField.value, scaleFactor)
    lightHaptic()
  } else {
    // Zoom entire card
    const newZoomLevel = Math.max(0.5, Math.min(3, props.zoomLevel * state.scale))
    emit('zoom-changed', newZoomLevel)
  }
  
  showGestureFeedbackWithIcon('mdi:magnify', 'Pinch to zoom')
}

const handleRotate = (state: any) => {
  if (props.readonly || !selectedField.value) return
  
  const rotationDegrees = state.rotation
  rotateField(selectedField.value, rotationDegrees)
  lightHaptic()
  
  showGestureFeedbackWithIcon('mdi:rotate-3d-variant', 'Rotate field')
}

const handleSwipeUp = (state: any) => {
  if (props.readonly) return
  
  // Hide toolbar
  showToolbar.value = false
  lightHaptic()
}

const handleSwipeDown = (state: any) => {
  if (props.readonly) return
  
  // Show toolbar
  showToolbar.value = true
  lightHaptic()
}

const handleTwoFingerSwipe = (state: any) => {
  if (props.readonly) return
  
  // Two-finger swipe for undo/redo
  const swipeDirection = state.movement.angle
  
  if (Math.abs(swipeDirection) < 45) { // Right swipe
    if (canRedo.value) {
      performRedo()
      mediumHaptic()
    }
  } else if (Math.abs(swipeDirection) > 135) { // Left swipe
    if (canUndo.value) {
      performUndo()
      mediumHaptic()
    }
  }
}

const handleThreeFingerSwipe = (state: any) => {
  if (props.readonly) return
  
  // Three-finger swipe to shake-to-undo
  if (canUndo.value) {
    performUndo()
    heavyHaptic()
    showGestureFeedbackWithIcon('mdi:undo', 'Undo')
  } else {
    errorHaptic()
  }
}

const handleDragStart = (state: any) => {
  if (props.readonly) return
  
  const dragPoint = { x: state.center.x, y: state.center.y }
  const draggedField = findFieldAtPoint(dragPoint)
  
  if (draggedField) {
    selectField(draggedField.id)
    // Start field drag operation
    startFieldDrag(draggedField, dragPoint)
    mediumHaptic()
  }
}

const handleDrag = (state: any) => {
  if (props.readonly || !selectedField.value) return
  
  // Update field position based on drag movement
  updateFieldPosition(selectedField.value, state.movement)
}

const handleDragEnd = (state: any) => {
  if (props.readonly) return
  
  // Finalize field position
  finalizeFieldDrag()
  lightHaptic()
}

const handleGestureStart = (state: any) => {
  showGestureFeedback.value = true
}

const handleGestureEnd = (state: any) => {
  showGestureFeedback.value = false
  gestureFeedbackIcon.value = ''
  gestureFeedbackText.value = ''
  gestureFeedbackClass.value = ''
}

// Field management methods
const initializeEditableFields = () => {
  editableFields.value = cardData.value.fields || []
}

const selectField = (fieldId: string | null) => {
  selectedFieldId.value = fieldId
  emit('field-selected', selectedField.value)
}

const updateField = (fieldId: string, updates: Partial<CardField>) => {
  const fieldIndex = editableFields.value.findIndex(f => f.id === fieldId)
  if (fieldIndex >= 0) {
    saveToUndoStack()
    editableFields.value[fieldIndex] = { ...editableFields.value[fieldIndex], ...updates }
    updateCardData()
  }
}

const addTextField = () => {
  const newField: CardField = {
    id: `text-${Date.now()}`,
    type: 'text',
    content: 'New Text',
    x: 50,
    y: 50,
    width: 200,
    height: 40,
    fontSize: 16,
    fontFamily: 'Arial',
    color: '#000000',
    backgroundColor: 'transparent',
    fontWeight: 'normal',
    fontStyle: 'normal',
    textAlign: 'left'
  }
  
  saveToUndoStack()
  editableFields.value.push(newField)
  selectField(newField.id)
  updateCardData()
  successHaptic()
}

const addImageField = () => {
  // Trigger image selection
  // Implementation would open image picker
  mediumHaptic()
}

const addQRField = () => {
  const newField: CardField = {
    id: `qr-${Date.now()}`,
    type: 'qr',
    content: cardData.value.contactInfo?.website || 'https://example.com',
    x: 200,
    y: 200,
    width: 100,
    height: 100,
    backgroundColor: 'transparent'
  }
  
  saveToUndoStack()
  editableFields.value.push(newField)
  selectField(newField.id)
  updateCardData()
  successHaptic()
}

const copyField = () => {
  if (selectedField.value) {
    clipboardField.value = { ...selectedField.value }
    successHaptic()
  }
}

const pasteField = () => {
  if (clipboardField.value) {
    const newField = {
      ...clipboardField.value,
      id: `${clipboardField.value.type}-${Date.now()}`,
      x: clipboardField.value.x + 20,
      y: clipboardField.value.y + 20
    }
    
    saveToUndoStack()
    editableFields.value.push(newField)
    selectField(newField.id)
    updateCardData()
    successHaptic()
  }
}

// Undo/Redo system
const saveToUndoStack = () => {
  undoStack.value.push({ ...cardData.value })
  if (undoStack.value.length > maxUndoSteps.value) {
    undoStack.value.shift()
  }
  redoStack.value = [] // Clear redo stack when new action is performed
}

const performUndo = () => {
  if (canUndo.value) {
    redoStack.value.push({ ...cardData.value })
    const previousState = undoStack.value.pop()!
    cardData.value = previousState
    initializeEditableFields()
    emit('update:card', cardData.value)
    successHaptic()
  }
}

const performRedo = () => {
  if (canRedo.value) {
    undoStack.value.push({ ...cardData.value })
    const nextState = redoStack.value.pop()!
    cardData.value = nextState
    initializeEditableFields()
    emit('update:card', cardData.value)
    successHaptic()
  }
}

// Utility methods
const findFieldAtPoint = (point: { x: number; y: number }): CardField | null => {
  // Find the topmost field at the given point
  const elementsFromPoint = document.elementsFromPoint(point.x, point.y)
  
  for (const element of elementsFromPoint) {
    const fieldId = element.getAttribute('data-field-id')
    if (fieldId) {
      return editableFields.value.find(f => f.id === fieldId) || null
    }
  }
  
  return null
}

const showGestureFeedbackWithIcon = (icon: string, text: string, className = '') => {
  gestureFeedbackIcon.value = icon
  gestureFeedbackText.value = text
  gestureFeedbackClass.value = className
  showGestureFeedback.value = true
  
  // Auto-hide feedback after 2 seconds
  setTimeout(() => {
    if (gestureFeedbackText.value === text) {
      showGestureFeedback.value = false
    }
  }, 2000)
}

const toggleEditMode = () => {
  isEditing.value = !isEditing.value
  emit('edit-mode-changed', isEditing.value)
  
  if (isEditing.value) {
    showToolbar.value = true
    mediumHaptic()
  } else {
    showToolbar.value = false
    selectedFieldId.value = null
    lightHaptic()
  }
}

const updateCardData = () => {
  cardData.value.fields = editableFields.value
  emit('update:card', cardData.value)
}

// Lifecycle
onMounted(() => {
  initializeEditableFields()
  
  // Handle virtual keyboard
  if (typeof window !== 'undefined') {
    const handleResize = () => {
      const viewportHeight = window.visualViewport?.height || window.innerHeight
      const windowHeight = window.innerHeight
      const heightDifference = windowHeight - viewportHeight
      
      isVirtualKeyboardVisible.value = heightDifference > 150
      virtualKeyboardHeight.value = Math.max(0, heightDifference)
    }
    
    window.visualViewport?.addEventListener('resize', handleResize)
    window.addEventListener('resize', handleResize)
    
    onUnmounted(() => {
      window.visualViewport?.removeEventListener('resize', handleResize)
      window.removeEventListener('resize', handleResize)
    })
  }
})

// Expose methods for parent components
defineExpose({
  enterEditMode: () => { isEditing.value = true },
  exitEditMode: () => { isEditing.value = false },
  selectField,
  addTextField,
  addImageField,
  addQRField,
  performUndo,
  performRedo,
  copyField,
  pasteField
})
</script>

<style scoped>
.touch-card-editor {
  @apply relative w-full h-full overflow-hidden;
  min-height: 600px;
  touch-action: none;
  user-select: none;
}

.card-preview-container {
  @apply relative w-full h-full flex items-center justify-center;
  background: #f5f5f5;
  background-image: 
    radial-gradient(circle, #e0e0e0 1px, transparent 1px);
  background-size: 20px 20px;
}

.card-background {
  @apply absolute inset-0 rounded-lg shadow-lg;
  width: 350px;
  height: 200px;
  margin: auto;
}

.card-elements-layer {
  @apply absolute inset-0;
  width: 350px;
  height: 200px;
  margin: auto;
}

.touch-overlay {
  @apply absolute inset-0 z-20;
  background: transparent;
}

.overlay-active {
  @apply z-30;
}

.selection-handles {
  @apply absolute border-2 border-blue-500 pointer-events-none;
  border-style: dashed;
}

.selection-handle {
  @apply absolute w-3 h-3 bg-blue-500 border border-white rounded-full pointer-events-auto;
  transform: translate(-50%, -50%);
}

.selection-handle:active {
  @apply bg-blue-600 scale-125;
}

.gesture-feedback {
  @apply fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2;
  @apply bg-black bg-opacity-80 text-white px-4 py-2 rounded-lg;
  @apply flex items-center space-x-2 z-50;
  @apply animate-fade-in-scale;
}

.gesture-text {
  @apply text-sm font-medium;
}

.touch-toolbar {
  @apply fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200;
  @apply p-4 transform transition-transform duration-300 ease-in-out;
  @apply flex flex-wrap gap-2 justify-center;
  transform: translateY(100%);
  z-index: 1000;
}

.toolbar-visible {
  transform: translateY(0);
}

.toolbar-section {
  @apply flex items-center space-x-2;
}

.toolbar-section + .toolbar-section {
  @apply border-l border-gray-300 pl-4 ml-2;
}

.editing-active .card-preview-container {
  @apply cursor-crosshair;
}

.gesture-active .card-preview-container {
  @apply cursor-grabbing;
}

.multi-touch .touch-overlay {
  @apply bg-blue-500 bg-opacity-10;
}

.keyboard-spacer {
  @apply transition-all duration-300 ease-in-out;
}

/* Animations */
@keyframes fade-in-scale {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.animate-fade-in-scale {
  animation: fade-in-scale 0.2s ease-out;
}

/* Touch optimizations */
@media (hover: none) and (pointer: coarse) {
  .selection-handle {
    @apply w-6 h-6;
  }
  
  .touch-toolbar {
    @apply p-6;
  }
  
  .toolbar-section {
    @apply space-x-4;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .selection-handles {
    @apply border-yellow-400 border-4;
  }
  
  .selection-handle {
    @apply bg-yellow-400 border-black border-2;
  }
  
  .gesture-feedback {
    @apply bg-black text-yellow-400 border-2 border-yellow-400;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .card-preview-container,
  .touch-toolbar,
  .keyboard-spacer {
    transition: none !important;
  }
  
  .animate-fade-in-scale {
    animation: none !important;
  }
}</style>