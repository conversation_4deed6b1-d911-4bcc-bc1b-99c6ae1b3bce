#!/usr/bin/env node

/**
 * Reset Business Cards Moderation Status Script
 * This script sets all business cards to pending moderation status
 */

import admin from 'firebase-admin';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Initialize Firebase Admin SDK
// Use the service account key that's already in your project
const serviceAccount = JSON.parse(
  readFileSync(join(__dirname, '../covalonic-8f02d-firebase-adminsdk-fbsvc-d119b2f775.json'), 'utf8')
);

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  projectId: 'covalonic-8f02d'
});

const db = admin.firestore();

async function resetModerationStatus() {
  try {
    console.log('🔍 Fetching business cards...');
    
    // Get all business cards
    const businessCardsRef = db.collection('business-cards');
    const snapshot = await businessCardsRef.get();
    
    if (snapshot.empty) {
      console.log('❌ No business cards found');
      return;
    }
    
    console.log(`📄 Found ${snapshot.size} business cards`);
    
    const batch = db.batch();
    let updateCount = 0;
    
    snapshot.forEach((doc) => {
      const data = doc.data();
      const cardName = data.name || `${data.first_name || ''} ${data.last_name || ''}`.trim() || 'Unknown';
      
      console.log(`📝 Resetting moderation status for: ${doc.id} (${cardName})`);
      console.log(`   Current status: ${data.moderationStatus || 'none'}, isPublic: ${data.isPublic || 'false'}`);
      
      batch.update(doc.ref, {
        moderationStatus: 'pending',
        // Remove previous moderation data
        moderatedBy: admin.firestore.FieldValue.delete(),
        moderatedAt: admin.firestore.FieldValue.delete(),
        moderationNotes: admin.firestore.FieldValue.delete(),
        // Keep isPublic as is, or set to false if you want to require explicit approval
        // isPublic: false,
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      
      updateCount++;
    });
    
    if (updateCount > 0) {
      console.log(`💾 Updating ${updateCount} business cards to pending status...`);
      await batch.commit();
      console.log('✅ Business cards moderation status reset successfully!');
      console.log(`📊 Set ${updateCount} cards to pending moderation status`);
      console.log('');
      console.log('Next steps:');
      console.log('1. Go to /c/admin/business-cards in your app');
      console.log('2. Review and approve cards as needed');
      console.log('3. Approved cards will appear on the public listing');
    } else {
      console.log('✅ No business cards needed updating');
    }
    
  } catch (error) {
    console.error('❌ Error resetting moderation status:', error);
  } finally {
    process.exit(0);
  }
}

// Run the script
resetModerationStatus();
