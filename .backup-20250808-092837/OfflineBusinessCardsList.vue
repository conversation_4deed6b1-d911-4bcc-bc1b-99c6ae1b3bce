<template>
  <div class="offline-business-cards-list">
    <!-- Offline Status Component -->
    <OfflineStatus />

    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center py-8">
      <div class="flex items-center space-x-2">
        <Icon name="mdi:loading" class="w-5 h-5 animate-spin text-blue-600" />
        <span class="text-gray-600">Loading business cards...</span>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else-if="businessCards.length === 0" class="text-center py-12">
      <Icon name="mdi:card-account-details-outline" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 mb-2">No Business Cards</h3>
      <p class="text-gray-500 mb-4">
        {{ isOnline ? 'No business cards found.' : 'No business cards available offline.' }}
      </p>
      <button
        v-if="isOnline"
        @click="refreshCards"
        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
      >
        Refresh
      </button>
    </div>

    <!-- Business Cards Grid -->
    <div v-else class="space-y-4">
      <!-- Filter and Sort Controls -->
      <div class="flex items-center justify-between bg-white p-4 rounded-lg shadow-sm">
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <Icon name="mdi:filter" class="w-4 h-4 text-gray-500" />
            <select
              v-model="filterType"
              class="text-sm border border-gray-300 rounded px-2 py-1"
            >
              <option value="all">All Cards</option>
              <option value="offline">Available Offline</option>
              <option value="online">Online Only</option>
              <option value="own">My Cards</option>
            </select>
          </div>
          
          <div class="flex items-center space-x-2">
            <Icon name="mdi:sort" class="w-4 h-4 text-gray-500" />
            <select
              v-model="sortBy"
              class="text-sm border border-gray-300 rounded px-2 py-1"
            >
              <option value="name">Name</option>
              <option value="created_at">Date Created</option>
              <option value="last_accessed">Last Accessed</option>
            </select>
          </div>
        </div>

        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-500">
            {{ filteredCards.length }} card{{ filteredCards.length !== 1 ? 's' : '' }}
          </span>
          <button
            @click="refreshCards"
            :disabled="refreshing"
            class="p-2 text-gray-500 hover:text-gray-700 transition-colors"
          >
            <Icon 
              name="mdi:refresh" 
              :class="{ 'animate-spin': refreshing }"
              class="w-4 h-4" 
            />
          </button>
        </div>
      </div>

      <!-- Cards Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div
          v-for="card in filteredCards"
          :key="card.id"
          class="business-card-item bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow cursor-pointer"
          @click="selectCard(card)"
        >
          <!-- Card Header -->
          <div class="p-4 border-b">
            <div class="flex items-center justify-between">
              <h3 class="font-medium text-gray-900 truncate">
                {{ card.name || `${card.first_name} ${card.last_name}` }}
              </h3>
              <div class="flex items-center space-x-2">
                <!-- Offline Indicator -->
                <div
                  v-if="card.source === 'offline'"
                  class="flex items-center space-x-1 text-xs text-green-600"
                  title="Available offline"
                >
                  <Icon name="mdi:wifi-off" class="w-3 h-3" />
                  <span>Offline</span>
                </div>
                
                <!-- Online Indicator -->
                <div
                  v-else-if="card.source === 'network'"
                  class="flex items-center space-x-1 text-xs text-blue-600"
                  title="Online only"
                >
                  <Icon name="mdi:wifi" class="w-3 h-3" />
                  <span>Online</span>
                </div>

                <!-- Actions Menu -->
                <div class="relative">
                  <button
                    @click.stop="toggleCardMenu(card.id)"
                    class="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <Icon name="mdi:dots-vertical" class="w-4 h-4" />
                  </button>
                  
                  <!-- Dropdown Menu -->
                  <div
                    v-if="activeCardMenu === card.id"
                    class="absolute right-0 top-8 bg-white border rounded-lg shadow-lg z-10 min-w-[150px]"
                  >
                    <button
                      @click.stop="viewCard(card)"
                      class="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 flex items-center space-x-2"
                    >
                      <Icon name="mdi:eye" class="w-4 h-4" />
                      <span>View</span>
                    </button>
                    
                    <button
                      v-if="card.source === 'network'"
                      @click.stop="cacheCard(card)"
                      class="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 flex items-center space-x-2"
                    >
                      <Icon name="mdi:download" class="w-4 h-4" />
                      <span>Save Offline</span>
                    </button>
                    
                    <button
                      v-if="card.source === 'offline'"
                      @click.stop="removeFromCache(card)"
                      class="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 flex items-center space-x-2 text-red-600"
                    >
                      <Icon name="mdi:delete" class="w-4 h-4" />
                      <span>Remove Offline</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Card Details -->
            <div class="mt-2 space-y-1">
              <p v-if="card.email" class="text-sm text-gray-600 truncate">
                {{ card.email }}
              </p>
              <p v-if="card.phone" class="text-sm text-gray-600">
                {{ card.phone }}
              </p>
              <p v-if="card.company" class="text-sm text-gray-500 truncate">
                {{ card.company }}
              </p>
            </div>
          </div>

          <!-- Card Image -->
          <div v-if="card.image" class="p-4">
            <img
              :src="card.image"
              :alt="card.name"
              class="w-full h-32 object-cover rounded-lg"
              @error="handleImageError"
            />
          </div>

          <!-- Card Footer -->
          <div class="px-4 pb-4">
            <div class="flex items-center justify-between text-xs text-gray-500">
              <span v-if="card.created_at">
                Created {{ formatDate(card.created_at) }}
              </span>
              <span v-if="card.tags && card.tags.length > 0" class="truncate ml-2">
                {{ card.tags.slice(0, 2).join(', ') }}
                <span v-if="card.tags.length > 2">...</span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Composables
const { 
  getAllCardsWithFallback, 
  getCardWithFallback,
  manualCacheCard,
  manualRemoveCard,
  isOnline,
  autoCacheCard
} = useCacheManager()

const { currentUser } = useAuth()

// Local state
const businessCards = ref<any[]>([])
const loading = ref(true)
const refreshing = ref(false)
const filterType = ref('all')
const sortBy = ref('name')
const activeCardMenu = ref<string | null>(null)

// Computed properties
const filteredCards = computed(() => {
  let filtered = [...businessCards.value]

  // Apply filters
  switch (filterType.value) {
    case 'offline':
      filtered = filtered.filter(card => card.source === 'offline')
      break
    case 'online':
      filtered = filtered.filter(card => card.source === 'network')
      break
    case 'own':
      filtered = filtered.filter(card => card.uid === currentUser.value?.uid)
      break
  }

  // Apply sorting
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'name':
        const nameA = a.name || `${a.first_name} ${a.last_name}`
        const nameB = b.name || `${b.first_name} ${b.last_name}`
        return nameA.localeCompare(nameB)
      case 'created_at':
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      case 'last_accessed':
        return new Date(b.last_accessed || b.created_at).getTime() - new Date(a.last_accessed || a.created_at).getTime()
      default:
        return 0
    }
  })

  return filtered
})

// Methods
const loadCards = async () => {
  try {
    loading.value = true
    const cards = await getAllCardsWithFallback()
    businessCards.value = cards
  } catch (error) {
    console.error('Error loading business cards:', error)
  } finally {
    loading.value = false
  }
}

const refreshCards = async () => {
  try {
    refreshing.value = true
    await loadCards()
  } finally {
    refreshing.value = false
  }
}

const selectCard = (card: any) => {
  // Auto-cache viewed cards
  if (card.source === 'network') {
    autoCacheCard(card)
  }
  
  // Navigate to card detail view
  navigateTo(`/businesscards/${card.id}`)
}

const viewCard = (card: any) => {
  selectCard(card)
  activeCardMenu.value = null
}

const cacheCard = async (card: any) => {
  try {
    const success = await manualCacheCard(card)
    if (success) {
      // Update the card source in the local list
      const index = businessCards.value.findIndex(c => c.id === card.id)
      if (index !== -1) {
        businessCards.value[index].source = 'offline'
      }
      showNotification('Card saved for offline access', 'success')
    } else {
      showNotification('Failed to save card offline', 'error')
    }
  } catch (error) {
    console.error('Error caching card:', error)
    showNotification('Failed to save card offline', 'error')
  }
  activeCardMenu.value = null
}

const removeFromCache = async (card: any) => {
  try {
    const success = await manualRemoveCard(card.id)
    if (success) {
      // Update the card source or remove from list
      const index = businessCards.value.findIndex(c => c.id === card.id)
      if (index !== -1) {
        if (isOnline.value) {
          businessCards.value[index].source = 'network'
        } else {
          businessCards.value.splice(index, 1)
        }
      }
      showNotification('Card removed from offline storage', 'success')
    } else {
      showNotification('Failed to remove card from offline storage', 'error')
    }
  } catch (error) {
    console.error('Error removing card from cache:', error)
    showNotification('Failed to remove card from offline storage', 'error')
  }
  activeCardMenu.value = null
}

const toggleCardMenu = (cardId: string) => {
  activeCardMenu.value = activeCardMenu.value === cardId ? null : cardId
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

const formatDate = (date: string | Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(new Date(date))
}

const showNotification = (message: string, type: 'success' | 'error' | 'info') => {
  console.log(`${type.toUpperCase()}: ${message}`)
  // Replace with your notification system
}

// Close menu when clicking outside
const handleClickOutside = () => {
  activeCardMenu.value = null
}

// Lifecycle
onMounted(() => {
  loadCards()
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// Watch for online status changes
watch(isOnline, (newValue) => {
  if (newValue) {
    // Refresh when coming back online
    refreshCards()
  }
})
</script>

<style scoped>
.business-card-item {
  transition: all 0.2s ease;
}

.business-card-item:hover {
  transform: translateY(-2px);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .grid {
    grid-template-columns: 1fr;
  }
}
</style>
