rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Users collection
    match /users/{userId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && request.auth.uid == userId;
      allow update: if isOwner(userId) || isAdmin();
      allow delete: if isAdmin();
    }
    
    // Business cards collection
    match /business-cards/{cardId} {
      allow read: if resource.data.visibility == 'public' || 
                     (isAuthenticated() && 
                      (resource.data.userId == request.auth.uid || 
                       resource.data.uploaderId == request.auth.uid));
      allow create: if isAuthenticated();
      allow update: if isAuthenticated() && 
                      (resource.data.userId == request.auth.uid || 
                       resource.data.uploaderId == request.auth.uid || 
                       isAdmin());
      allow delete: if isAuthenticated() && 
                      (resource.data.userId == request.auth.uid || 
                       isAdmin());
    }
    
    // Legacy businesscards collection (for backward compatibility)
    match /businesscards/{cardId} {
      allow read: if true; // Public read for legacy cards
      allow write: if isAuthenticated();
    }
    
    // Interactions collection (CRM notes, etc.)
    match /interactions/{interactionId} {
      allow read: if isAuthenticated() && 
                    (resource.data.userId == request.auth.uid || isAdmin());
      allow create: if isAuthenticated() && 
                      request.data.userId == request.auth.uid;
      allow update: if isOwner(resource.data.userId);
      allow delete: if isOwner(resource.data.userId) || isAdmin();
    }
    
    // Follow-ups collection
    match /follow-ups/{followUpId} {
      allow read: if isAuthenticated() && 
                    (resource.data.userId == request.auth.uid || isAdmin());
      allow write: if isAuthenticated() && 
                     (resource.data.userId == request.auth.uid || isAdmin());
    }
    
    // Categories collection (read-only for users)
    match /categories/{categoryId} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // Email templates (admin only)
    match /email-templates/{templateId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }
    
    // Analytics events
    match /analytics-events/{eventId} {
      allow read: if isAdmin();
      allow create: if true; // Allow anonymous analytics
      allow update, delete: if false; // Immutable
    }
    
    // Spaces (for multi-tenant support)
    match /spaces/{spaceId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && 
                     (resource.data.ownerId == request.auth.uid || isAdmin());
    }
    
    // CRM Clients collection - users can only access their own clients
    match /crm_clients/{clientId} {
      allow read: if isAuthenticated() && 
                    (resource.data.user_id == request.auth.uid || isAdmin());
      allow create: if isAuthenticated() && 
                      request.resource.data.user_id == request.auth.uid;
      allow update: if isAuthenticated() && 
                      resource.data.user_id == request.auth.uid;
      allow delete: if isAuthenticated() && 
                      (resource.data.user_id == request.auth.uid || isAdmin());
    }
    
    // CRM Businesses collection - shared across users
    match /crm_businesses/{businessId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && 
                      request.resource.data.created_by == request.auth.uid;
      allow update: if isAuthenticated();
      allow delete: if isAuthenticated() && 
                      (resource.data.created_by == request.auth.uid || isAdmin());
    }
    
    // CRM Tasks collection - user-specific access
    match /crm_tasks/{taskId} {
      allow read: if isAuthenticated() && 
                    (resource.data.user_id == request.auth.uid || isAdmin());
      allow create: if isAuthenticated() && 
                      request.resource.data.user_id == request.auth.uid;
      allow update: if isAuthenticated() && 
                      resource.data.user_id == request.auth.uid;
      allow delete: if isAuthenticated() && 
                      (resource.data.user_id == request.auth.uid || isAdmin());
    }
    
    // Default deny for any unmatched documents
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
