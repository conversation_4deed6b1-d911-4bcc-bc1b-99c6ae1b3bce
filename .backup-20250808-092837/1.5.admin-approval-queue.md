# Story 1.5: Admin Approval Queue

## Status
Completed ✅

## Story
**As an** admin,  
**I want** to review uploaded business cards before public display,  
**so that** I can ensure content quality and prevent spam.

## Acceptance Criteria
1. Create admin queue interface at /c/admin/business-cards
2. Display pending cards with uploaded images and extracted data
3. Implement approve/reject actions with reason field
4. Update card status and notify users of decisions
5. Add bulk operations for efficiency

## Integration Verification
- IV1: Admin interface follows existing admin dashboard patterns
- IV2: Role-based access uses existing admin middleware
- IV3: Notification system extends current user alerts

## Tasks / Subtasks
- [x] Create admin page and routing (AC: 1)
  - [x] Add /c/admin/business-cards route
  - [x] Apply admin middleware for access control
  - [x] Add navigation item to admin sidebar
  - [x] Follow existing admin layout patterns
- [x] Build approval queue interface (AC: 2)
  - [x] Display grid/list of pending business cards
  - [x] Show card image with extracted data
  - [x] Add filtering by date, user, status
  - [x] Implement pagination for large queues
  - [x] Add search functionality
- [x] Implement approval actions (AC: 3)
  - [x] Add approve/reject buttons for each card
  - [x] Create modal for rejection reasons
  - [x] Update card status in Firestore
  - [x] Track approval history and admin actions
  - [x] Handle optimistic UI updates
- [x] Add user notifications (AC: 4)
  - [x] Send notification on approval
  - [x] Include rejection reason if rejected
  - [x] Use existing notification system
  - [x] Add email notification option
- [x] Implement bulk operations (AC: 5)
  - [x] Add checkbox selection for multiple cards
  - [x] Bulk approve with single click
  - [x] Bulk reject with common reason
  - [x] Show progress during bulk operations
  - [x] Handle partial failures gracefully

## Dev Notes

### Testing Standards
- Page test location: `tests/pages/admin/business-cards.test.ts`
- Component test: `tests/components/admin/BusinessCardQueue.test.ts`
- Test role-based access restrictions
- Test bulk operation scenarios
- Mock admin user context

### Relevant Source Tree
```
pages/
  └── c/
      └── admin/
          ├── index.vue              # Admin dashboard
          ├── ad-spots/              # Reference for patterns
          └── business-cards.vue     # NEW - to be created
components/
  └── admin/
      ├── Sidebar.vue              # Add new nav item here
      └── business-cards/
          └── ApprovalQueue.vue    # NEW - queue component
middleware/
  └── admin.ts                     # Existing admin check
```

### Key Technical Context
- Admin pages use consistent layout with sidebar navigation
- All admin routes require `role: 'admin'` check
- Bulk operations should use Firebase batch writes
- Admin actions are logged for audit trail
- Follow existing table/grid patterns from ad management

### Page Structure
```vue
<template>
  <AdminLayout>
    <div class="p-6">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold dark:text-white">
          Business Card Approvals
        </h1>
        <div class="flex space-x-4">
          <Button @click="bulkApprove" :disabled="!hasSelection">
            Bulk Approve ({{ selectedCount }})
          </Button>
          <Button variant="secondary" @click="bulkReject" :disabled="!hasSelection">
            Bulk Reject
          </Button>
        </div>
      </div>
      
      <!-- Filters -->
      <div class="mb-6 flex space-x-4">
        <Select v-model="filter.status" :options="statusOptions" />
        <DatePicker v-model="filter.dateRange" />
        <SearchInput v-model="filter.search" />
      </div>
      
      <!-- Queue Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <BusinessCardApprovalItem
          v-for="card in filteredCards"
          :key="card.id"
          :card="card"
          v-model:selected="selectedCards[card.id]"
          @approve="approveCard"
          @reject="showRejectModal"
        />
      </div>
      
      <!-- Pagination -->
      <Pagination
        v-model:page="currentPage"
        :total="totalCards"
        :per-page="perPage"
      />
    </div>
    
    <!-- Rejection Modal -->
    <Modal v-model="rejectModal.show">
      <!-- Rejection reason form -->
    </Modal>
  </AdminLayout>
</template>
```

### Approval Queue Item Component
```vue
<template>
  <Card class="hover:shadow-lg transition-shadow">
    <div class="relative">
      <Checkbox
        v-model="selected"
        class="absolute top-2 left-2 z-10"
      />
      
      <img 
        :src="card.image_url" 
        class="w-full h-48 object-cover rounded-t"
      />
      
      <div class="p-4">
        <h3 class="font-bold">{{ card.extracted_data.name }}</h3>
        <p class="text-sm text-gray-600">{{ card.extracted_data.company }}</p>
        <p class="text-sm">{{ card.extracted_data.email }}</p>
        <p class="text-sm">{{ card.extracted_data.phone }}</p>
        
        <div class="mt-4 flex justify-between">
          <Button size="sm" @click="$emit('approve', card.id)">
            Approve
          </Button>
          <Button size="sm" variant="danger" @click="$emit('reject', card.id)">
            Reject
          </Button>
        </div>
        
        <div class="mt-2 text-xs text-gray-500">
          Uploaded by {{ card.user_name }} • {{ formatDate(card.created_at) }}
        </div>
      </div>
    </div>
  </Card>
</template>
```

### Firestore Structure for Approvals
```typescript
interface IBusinessCard {
  id: string
  user_id: string
  user_name: string
  image_url: string
  extracted_data: IOCRResult['data']
  status: 'pending' | 'approved' | 'rejected'
  admin_id?: string
  admin_action_at?: Timestamp
  rejection_reason?: string
  created_at: Timestamp
  updated_at: Timestamp
}
```

### Bulk Operations
```typescript
const bulkApprove = async () => {
  const batch = writeBatch(db);
  const selectedIds = Object.keys(selectedCards.value)
    .filter(id => selectedCards.value[id]);
  
  selectedIds.forEach(id => {
    const ref = doc(db, 'business_cards', id);
    batch.update(ref, {
      status: 'approved',
      admin_id: currentUser.value.uid,
      admin_action_at: serverTimestamp(),
      updated_at: serverTimestamp()
    });
  });
  
  await batch.commit();
  showSuccess(`${selectedIds.length} cards approved`);
};
```

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-08-04 | 1.0 | Initial story creation from PRD | John (PM) |

## Dev Agent Record
_To be populated during implementation_

### Agent Model Used
_To be recorded by dev agent_

### Debug Log References
_To be recorded by dev agent_

### Completion Notes List
_To be recorded by dev agent_

### File List
_To be recorded by dev agent_

## QA Results
_To be populated by QA agent_