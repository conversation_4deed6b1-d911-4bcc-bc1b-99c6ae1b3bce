#!/usr/bin/env node

/**
 * Generate Test Business Cards
 * Utility to generate specific types of business cards for testing
 */

const admin = require('firebase-admin');
const { faker } = require('@faker-js/faker');

// Initialize Firebase Admin with emulator settings
process.env.FIRESTORE_EMULATOR_HOST = 'localhost:8080';

admin.initializeApp({
  projectId: 'covalonic-dev'
});

const db = admin.firestore();

// Test scenarios
const TEST_SCENARIOS = {
  // Edge case URLs for testing
  urlTestCases: [
    { website: 'www.example.com', expected: 'https://www.example.com' },
    { website: 'http://example.com', expected: 'http://example.com' },
    { website: 'https://example.com', expected: 'https://example.com' },
    { website: 'example.com', expected: 'https://example.com' },
    { website: 'https://example.com/very/long/path/that/should/be/truncated/in/the/ui', expected: 'truncated' },
    { website: '', expected: 'no-link' },
    { website: 'not-a-url', expected: 'invalid' },
    { website: 'ftp://files.example.com', expected: 'https://files.example.com' },
    { website: 'https://例え.jp', expected: 'international' }
  ],

  // OCR test cases (simulated text extraction)
  ocrTestCases: [
    {
      name: 'Standard Business Card',
      extractedText: `John Smith
Senior Software Engineer
TechCorp Solutions
<EMAIL>
+****************
www.techcorp.com
123 Tech Street, Suite 100
San Francisco, CA 94105`,
      expectedFields: {
        name: 'John Smith',
        title: 'Senior Software Engineer',
        company: 'TechCorp Solutions',
        email: '<EMAIL>',
        phone: '+****************',
        website: 'www.techcorp.com'
      }
    },
    {
      name: 'Minimal Card',
      extractedText: `Jane Doe
<EMAIL>
StartupXYZ`,
      expectedFields: {
        name: 'Jane Doe',
        email: '<EMAIL>',
        company: 'StartupXYZ'
      }
    },
    {
      name: 'International Card',
      extractedText: `田中 太郎
株式会社テクノロジー
<EMAIL>
+81-3-1234-5678
東京都渋谷区`,
      expectedFields: {
        name: '田中 太郎',
        company: '株式会社テクノロジー',
        email: '<EMAIL>',
        phone: '+81-3-1234-5678'
      }
    }
  ],

  // Geographic distribution test
  geographicClusters: [
    {
      country: 'United States',
      state: 'California',
      cities: ['San Francisco', 'Los Angeles', 'San Diego', 'San Jose'],
      count: 20
    },
    {
      country: 'United Kingdom',
      state: 'England',
      cities: ['London', 'Manchester', 'Birmingham', 'Liverpool'],
      count: 15
    },
    {
      country: 'Canada',
      state: 'Ontario',
      cities: ['Toronto', 'Ottawa', 'Hamilton', 'London'],
      count: 10
    }
  ]
};

async function generateURLTestCards() {
  console.log('\n🔗 Generating URL test cards...');
  
  const batch = db.batch();
  
  for (const testCase of TEST_SCENARIOS.urlTestCases) {
    const cardData = {
      name: `Test User - ${testCase.expected}`,
      email: faker.internet.email(),
      company: 'URL Test Company',
      website: testCase.website,
      phone: faker.phone.number(),
      title: 'URL Test Case',
      status: 'claimed',
      userId: 'test-user-id',
      uploaderId: 'test-user-id',
      category: 'Technology',
      subCategory: 'Software',
      visibility: 'public',
      tags: ['test', 'url-validation'],
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      testMetadata: {
        isTestCard: true,
        testType: 'url',
        expectedBehavior: testCase.expected
      }
    };
    
    const docRef = db.collection('business-cards').doc();
    batch.set(docRef, cardData);
  }
  
  await batch.commit();
  console.log(`  ✓ Created ${TEST_SCENARIOS.urlTestCases.length} URL test cards`);
}

async function generateOCRTestCards() {
  console.log('\n📷 Generating OCR test cards...');
  
  for (const testCase of TEST_SCENARIOS.ocrTestCases) {
    const cardData = {
      ...testCase.expectedFields,
      status: 'unclaimed',
      uploaderId: 'ocr-test-user',
      category: 'Technology',
      subCategory: 'Software',
      visibility: 'public',
      tags: ['test', 'ocr'],
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      ocrMetadata: {
        isTestCard: true,
        testType: 'ocr',
        originalText: testCase.extractedText,
        extractionConfidence: faker.number.float({ min: 0.75, max: 0.99 }),
        processingTime: faker.number.int({ min: 500, max: 3000 })
      }
    };
    
    await db.collection('business-cards').add(cardData);
  }
  
  console.log(`  ✓ Created ${TEST_SCENARIOS.ocrTestCases.length} OCR test cards`);
}

async function generateGeographicTestCards() {
  console.log('\n🌍 Generating geographic test cards...');
  
  let totalCreated = 0;
  
  for (const cluster of TEST_SCENARIOS.geographicClusters) {
    const cardsPerCity = Math.floor(cluster.count / cluster.cities.length);
    
    for (const city of cluster.cities) {
      for (let i = 0; i < cardsPerCity; i++) {
        const cardData = {
          name: faker.person.fullName(),
          email: faker.internet.email(),
          company: faker.company.name(),
          phone: faker.phone.number(),
          title: faker.person.jobTitle(),
          website: faker.internet.url(),
          address: {
            city: city,
            state: cluster.state,
            country: cluster.country,
            street: faker.location.streetAddress(),
            postalCode: faker.location.zipCode()
          },
          status: faker.helpers.arrayElement(['claimed', 'unclaimed']),
          userId: faker.datatype.boolean() ? faker.string.uuid() : null,
          uploaderId: faker.string.uuid(),
          category: faker.helpers.arrayElement(['Technology', 'Finance', 'Healthcare']),
          visibility: 'public',
          tags: ['test', 'geographic', city.toLowerCase()],
          createdAt: admin.firestore.FieldValue.serverTimestamp()
        };
        
        await db.collection('business-cards').add(cardData);
        totalCreated++;
      }
    }
    
    console.log(`  ✓ Created ${cluster.count} cards for ${cluster.country}`);
  }
  
  console.log(`  ✓ Total geographic test cards: ${totalCreated}`);
}

async function generateTestCards() {
  console.log('🎯 Generating specialized test cards...\n');
  
  try {
    await generateURLTestCards();
    await generateOCRTestCards();
    await generateGeographicTestCards();
    
    console.log('\n✅ Test card generation completed!');
    console.log('📝 Use these cards to test specific features:');
    console.log('  - URL validation and display');
    console.log('  - OCR extraction accuracy');
    console.log('  - Geographic filtering');
  } catch (error) {
    console.error('\n❌ Test card generation failed:', error);
    process.exit(1);
  }
}

// Command line argument handling
const args = process.argv.slice(2);
const testType = args[0];

async function main() {
  if (!testType || testType === 'all') {
    await generateTestCards();
  } else if (testType === 'url') {
    await generateURLTestCards();
  } else if (testType === 'ocr') {
    await generateOCRTestCards();
  } else if (testType === 'geo') {
    await generateGeographicTestCards();
  } else {
    console.log('Usage: node generate-test-cards.js [all|url|ocr|geo]');
    process.exit(1);
  }
  
  process.exit(0);
}

main().catch(error => {
  console.error(error);
  process.exit(1);
});