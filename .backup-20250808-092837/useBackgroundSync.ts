/**
 * Background Sync and Push Notifications Composable
 * Provides background processing and push notification capabilities
 */

import { ref, computed, readonly, onUnmounted } from 'vue'
import { PushNotifications, Token, PushNotificationSchema, ActionPerformed } from '@capacitor/push-notifications'
import { App, AppState } from '@capacitor/app'
import { Capacitor } from '@capacitor/core'
import { Haptics, ImpactStyle } from '@capacitor/haptics'

export interface PushNotificationPayload {
  title: string
  body: string
  data?: any
  badge?: number
  sound?: string
  tag?: string
  actions?: NotificationAction[]
}

export interface NotificationAction {
  id: string
  title: string
  destructive?: boolean
  input?: boolean
  inputPlaceholder?: string
}

export interface PushPermissionStatus {
  receive: 'granted' | 'denied' | 'prompt'
}

export interface BackgroundSyncOptions {
  syncInterval?: number
  retryAttempts?: number
  batchSize?: number
  onlyWhenCharging?: boolean
  requiresNetwork?: boolean
}

export interface SyncTask {
  id: string
  type: 'business-cards' | 'analytics' | 'user-data' | 'images'
  data: any
  priority: 'low' | 'normal' | 'high'
  createdAt: number
  retryCount: number
  maxRetries: number
}

export interface BackgroundSyncResult {
  success: boolean
  taskId: string
  error?: string
  syncedItems?: number
  timestamp: number
}

export const useBackgroundSync = () => {
  // Notification state
  const pushToken = ref<string | null>(null)
  const isInitialized = ref(false)
  const error = ref<string | null>(null)
  const permissions = ref<PushPermissionStatus | null>(null)
  const lastNotification = ref<PushNotificationSchema | null>(null)
  
  // Background sync state
  const isSyncing = ref(false)
  const syncQueue = ref<SyncTask[]>([])
  const syncHistory = ref<BackgroundSyncResult[]>([])
  const appState = ref<AppState>('active')
  
  // Configuration
  const syncOptions = ref<BackgroundSyncOptions>({
    syncInterval: 300000, // 5 minutes
    retryAttempts: 3,
    batchSize: 50,
    onlyWhenCharging: false,
    requiresNetwork: true
  })

  // Listeners
  let pushListeners: any[] = []
  let appStateListener: any = null
  let syncIntervalId: NodeJS.Timeout | null = null

  /**
   * Check if push notifications are available
   */
  const isNativeAvailable = computed(() => {
    return Capacitor.isNativePlatform() && Capacitor.isPluginAvailable('PushNotifications')
  })

  /**
   * Initialize push notifications and background sync
   */
  const initialize = async (): Promise<void> => {
    try {
      error.value = null

      if (!isNativeAvailable.value) {
        throw new Error('Push notifications not available on this platform')
      }

      // Initialize push notifications
      await initializePushNotifications()
      
      // Initialize background sync
      await initializeBackgroundSync()
      
      // Setup app state monitoring
      await setupAppStateMonitoring()
      
      isInitialized.value = true

    } catch (err) {
      console.error('Background sync initialization failed:', err)
      error.value = err instanceof Error ? err.message : 'Initialization failed'
      throw err
    }
  }

  /**
   * Initialize push notifications
   */
  const initializePushNotifications = async (): Promise<void> => {
    try {
      // Check permissions
      const permissionResult = await PushNotifications.checkPermissions()
      permissions.value = permissionResult as PushPermissionStatus

      if (permissionResult.receive === 'prompt') {
        const requestResult = await PushNotifications.requestPermissions()
        permissions.value = requestResult as PushPermissionStatus
      }

      if (permissions.value?.receive !== 'granted') {
        throw new Error('Push notification permission denied')
      }

      // Register for push notifications
      await PushNotifications.register()

      // Setup listeners
      setupPushNotificationListeners()

    } catch (err) {
      console.error('Push notification initialization failed:', err)
      throw err
    }
  }

  /**
   * Setup push notification listeners
   */
  const setupPushNotificationListeners = (): void => {
    // Registration success
    const registrationListener = PushNotifications.addListener('registration', (token: Token) => {
      console.log('Push registration success, token:', token.value)
      pushToken.value = token.value
      // Send token to server for storing
      sendTokenToServer(token.value)
    })

    // Registration error
    const registrationErrorListener = PushNotifications.addListener('registrationError', (err) => {
      console.error('Registration error:', err)
      error.value = 'Push notification registration failed'
    })

    // Notification received while app is active
    const notificationListener = PushNotifications.addListener(
      'pushNotificationReceived',
      async (notification: PushNotificationSchema) => {
        console.log('Push notification received:', notification)
        lastNotification.value = notification
        
        // Provide haptic feedback
        await Haptics.impact({ style: ImpactStyle.Light })
        
        // Handle notification based on type
        await handleNotificationReceived(notification)
      }
    )

    // Notification tapped/opened
    const notificationActionListener = PushNotifications.addListener(
      'pushNotificationActionPerformed',
      async (notificationAction: ActionPerformed) => {
        console.log('Push notification action performed:', notificationAction)
        
        // Provide haptic feedback
        await Haptics.impact({ style: ImpactStyle.Medium })
        
        // Handle notification action
        await handleNotificationAction(notificationAction)
      }
    )

    pushListeners = [
      registrationListener,
      registrationErrorListener,
      notificationListener,
      notificationActionListener
    ]
  }

  /**
   * Initialize background sync
   */
  const initializeBackgroundSync = async (): Promise<void> => {
    try {
      // Load pending sync tasks from storage
      await loadSyncQueue()
      
      // Start background sync interval
      startBackgroundSync()

    } catch (err) {
      console.error('Background sync initialization failed:', err)
      throw err
    }
  }

  /**
   * Setup app state monitoring
   */
  const setupAppStateMonitoring = async (): Promise<void> => {
    appStateListener = await App.addListener('appStateChange', (state: AppState) => {
      console.log('App state changed:', state)
      appState.value = state
      
      if (state.isActive) {
        // App became active - trigger immediate sync
        triggerImmediateSync()
      } else {
        // App went to background - schedule background tasks
        scheduleBackgroundTasks()
      }
    })
  }

  /**
   * Send push token to server
   */
  const sendTokenToServer = async (token: string): Promise<void> => {
    try {
      // Implementation would send token to your backend
      console.log('Sending push token to server:', token)
      
      // Example API call
      // await $fetch('/api/push-tokens', {
      //   method: 'POST',
      //   body: { token, platform: Capacitor.getPlatform() }
      // })
      
    } catch (err) {
      console.error('Failed to send token to server:', err)
    }
  }

  /**
   * Handle received notification
   */
  const handleNotificationReceived = async (notification: PushNotificationSchema): Promise<void> => {
    try {
      // Handle different notification types
      const notificationType = notification.data?.type
      
      switch (notificationType) {
        case 'business-card-update':
          await handleBusinessCardUpdate(notification.data)
          break
        case 'sync-reminder':
          await triggerImmediateSync()
          break
        case 'marketing':
          await handleMarketingNotification(notification.data)
          break
        default:
          console.log('Unknown notification type:', notificationType)
      }
      
    } catch (err) {
      console.error('Failed to handle notification:', err)
    }
  }

  /**
   * Handle notification action
   */
  const handleNotificationAction = async (action: ActionPerformed): Promise<void> => {
    try {
      const { actionId, notification } = action
      
      switch (actionId) {
        case 'sync-now':
          await triggerImmediateSync()
          break
        case 'view-card':
          // Navigate to business card
          const cardId = notification.data?.businessCardId
          if (cardId) {
            // Implementation would navigate to the card
            console.log('Navigate to business card:', cardId)
          }
          break
        case 'dismiss':
          // Just dismiss - no action needed
          break
        default:
          console.log('Unknown notification action:', actionId)
      }
      
    } catch (err) {
      console.error('Failed to handle notification action:', err)
    }
  }

  /**
   * Add sync task to queue
   */
  const addSyncTask = async (task: Omit<SyncTask, 'id' | 'createdAt' | 'retryCount'>): Promise<string> => {
    const syncTask: SyncTask = {
      ...task,
      id: generateTaskId(),
      createdAt: Date.now(),
      retryCount: 0
    }
    
    syncQueue.value.push(syncTask)
    await saveSyncQueue()
    
    // Trigger immediate sync for high priority tasks
    if (task.priority === 'high') {
      await triggerImmediateSync()
    }
    
    return syncTask.id
  }

  /**
   * Process sync queue
   */
  const processSyncQueue = async (): Promise<void> => {
    if (isSyncing.value || syncQueue.value.length === 0) {
      return
    }

    isSyncing.value = true

    try {
      // Sort tasks by priority and creation time
      const sortedTasks = [...syncQueue.value].sort((a, b) => {
        const priorityOrder = { high: 3, normal: 2, low: 1 }
        const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority]
        if (priorityDiff !== 0) return priorityDiff
        return a.createdAt - b.createdAt
      })

      // Process tasks in batches
      const batchSize = syncOptions.value.batchSize || 50
      const batch = sortedTasks.slice(0, batchSize)

      for (const task of batch) {
        try {
          await processSyncTask(task)
          
          // Remove completed task from queue
          syncQueue.value = syncQueue.value.filter(t => t.id !== task.id)
          
        } catch (err) {
          console.error(`Failed to process sync task ${task.id}:`, err)
          
          // Increment retry count
          task.retryCount++
          
          if (task.retryCount >= task.maxRetries) {
            // Remove failed task after max retries
            syncQueue.value = syncQueue.value.filter(t => t.id !== task.id)
            
            // Log failed task
            syncHistory.value.push({
              success: false,
              taskId: task.id,
              error: err instanceof Error ? err.message : 'Unknown error',
              timestamp: Date.now()
            })
          }
        }
      }

      await saveSyncQueue()

    } finally {
      isSyncing.value = false
    }
  }

  /**
   * Process individual sync task
   */
  const processSyncTask = async (task: SyncTask): Promise<void> => {
    console.log(`Processing sync task: ${task.type} (${task.id})`)
    
    let syncedItems = 0
    
    switch (task.type) {
      case 'business-cards':
        syncedItems = await syncBusinessCards(task.data)
        break
      case 'analytics':
        syncedItems = await syncAnalytics(task.data)
        break
      case 'user-data':
        syncedItems = await syncUserData(task.data)
        break
      case 'images':
        syncedItems = await syncImages(task.data)
        break
      default:
        throw new Error(`Unknown sync task type: ${task.type}`)
    }
    
    // Record successful sync
    syncHistory.value.push({
      success: true,
      taskId: task.id,
      syncedItems,
      timestamp: Date.now()
    })
  }

  /**
   * Sync business cards
   */
  const syncBusinessCards = async (data: any): Promise<number> => {
    // Implementation would sync business card data
    console.log('Syncing business cards:', data)
    return data.cards?.length || 0
  }

  /**
   * Sync analytics data
   */
  const syncAnalytics = async (data: any): Promise<number> => {
    // Implementation would sync analytics events
    console.log('Syncing analytics:', data)
    return data.events?.length || 0
  }

  /**
   * Sync user data
   */
  const syncUserData = async (data: any): Promise<number> => {
    // Implementation would sync user profile data
    console.log('Syncing user data:', data)
    return 1
  }

  /**
   * Sync images
   */
  const syncImages = async (data: any): Promise<number> => {
    // Implementation would sync image uploads
    console.log('Syncing images:', data)
    return data.images?.length || 0
  }

  /**
   * Start background sync
   */
  const startBackgroundSync = (): void => {
    if (syncIntervalId) {
      clearInterval(syncIntervalId)
    }
    
    syncIntervalId = setInterval(() => {
      if (appState.value.isActive === false) {
        // Only sync in background if app is not active
        processSyncQueue()
      }
    }, syncOptions.value.syncInterval)
  }

  /**
   * Stop background sync
   */
  const stopBackgroundSync = (): void => {
    if (syncIntervalId) {
      clearInterval(syncIntervalId)
      syncIntervalId = null
    }
  }

  /**
   * Trigger immediate sync
   */
  const triggerImmediateSync = async (): Promise<void> => {
    await processSyncQueue()
  }

  /**
   * Schedule background tasks
   */
  const scheduleBackgroundTasks = async (): Promise<void> => {
    // Implementation would schedule native background tasks
    console.log('Scheduling background tasks')
  }

  /**
   * Handle business card update notification
   */
  const handleBusinessCardUpdate = async (data: any): Promise<void> => {
    console.log('Handling business card update:', data)
    // Implementation would update local business card data
  }

  /**
   * Handle marketing notification
   */
  const handleMarketingNotification = async (data: any): Promise<void> => {
    console.log('Handling marketing notification:', data)
    // Implementation would handle marketing campaigns
  }

  /**
   * Load sync queue from storage
   */
  const loadSyncQueue = async (): Promise<void> => {
    try {
      // Implementation would load from local storage
      const stored = localStorage.getItem('covalonic_sync_queue')
      if (stored) {
        syncQueue.value = JSON.parse(stored)
      }
    } catch (err) {
      console.error('Failed to load sync queue:', err)
    }
  }

  /**
   * Save sync queue to storage
   */
  const saveSyncQueue = async (): Promise<void> => {
    try {
      localStorage.setItem('covalonic_sync_queue', JSON.stringify(syncQueue.value))
    } catch (err) {
      console.error('Failed to save sync queue:', err)
    }
  }

  /**
   * Generate unique task ID
   */
  const generateTaskId = (): string => {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Clear sync queue
   */
  const clearSyncQueue = async (): Promise<void> => {
    syncQueue.value = []
    await saveSyncQueue()
  }

  /**
   * Get sync statistics
   */
  const getSyncStats = () => {
    const totalTasks = syncHistory.value.length
    const successfulTasks = syncHistory.value.filter(h => h.success).length
    const failedTasks = totalTasks - successfulTasks
    const totalSyncedItems = syncHistory.value
      .filter(h => h.success)
      .reduce((sum, h) => sum + (h.syncedItems || 0), 0)

    return {
      totalTasks,
      successfulTasks,
      failedTasks,
      totalSyncedItems,
      successRate: totalTasks > 0 ? (successfulTasks / totalTasks) * 100 : 0,
      queueLength: syncQueue.value.length
    }
  }

  /**
   * Cleanup resources
   */
  const cleanup = async (): Promise<void> => {
    // Remove push notification listeners
    for (const listener of pushListeners) {
      await listener.remove()
    }
    pushListeners = []

    // Remove app state listener
    if (appStateListener) {
      await appStateListener.remove()
      appStateListener = null
    }

    // Stop background sync
    stopBackgroundSync()

    // Reset state
    isInitialized.value = false
    error.value = null
    pushToken.value = null
    permissions.value = null
    lastNotification.value = null
    isSyncing.value = false
  }

  // Computed properties
  const isReady = computed(() => 
    isNativeAvailable.value && 
    isInitialized.value && 
    permissions.value?.receive === 'granted'
  )

  const hasPermission = computed(() => 
    permissions.value?.receive === 'granted'
  )

  const queueSize = computed(() => 
    syncQueue.value.length
  )

  const isAppActive = computed(() => 
    appState.value?.isActive === true
  )

  // Cleanup on unmount
  onUnmounted(() => {
    cleanup()
  })

  return {
    // State
    pushToken: readonly(pushToken),
    isInitialized: readonly(isInitialized),
    error: readonly(error),
    permissions: readonly(permissions),
    lastNotification: readonly(lastNotification),
    isSyncing: readonly(isSyncing),
    syncQueue: readonly(syncQueue),
    syncHistory: readonly(syncHistory),
    appState: readonly(appState),
    syncOptions: readonly(syncOptions),

    // Computed
    isNativeAvailable,
    isReady,
    hasPermission,
    queueSize,
    isAppActive,

    // Methods
    initialize,
    addSyncTask,
    processSyncQueue,
    triggerImmediateSync,
    clearSyncQueue,
    getSyncStats,
    startBackgroundSync,
    stopBackgroundSync,
    cleanup
  }
}

// Export types for external use
export type {
  PushNotificationPayload,
  NotificationAction,
  PushPermissionStatus,
  BackgroundSyncOptions,
  SyncTask,
  BackgroundSyncResult
}