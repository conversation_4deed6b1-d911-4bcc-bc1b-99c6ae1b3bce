{"indexes": [{"collectionGroup": "business-cards", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "business-cards", "queryScope": "COLLECTION", "fields": [{"fieldPath": "address.country", "order": "ASCENDING"}, {"fieldPath": "address.state", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "business-cards", "queryScope": "COLLECTION", "fields": [{"fieldPath": "category", "order": "ASCENDING"}, {"fieldPath": "subCategory", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "business-cards", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uploaderId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "business-cards", "queryScope": "COLLECTION", "fields": [{"fieldPath": "visibility", "order": "ASCENDING"}, {"fieldPath": "address.country", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "interactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "cardId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "follow-ups", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "dueDate", "order": "ASCENDING"}]}, {"collectionGroup": "follow-ups", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "completed", "order": "ASCENDING"}, {"fieldPath": "dueDate", "order": "ASCENDING"}]}, {"collectionGroup": "analytics-events", "queryScope": "COLLECTION", "fields": [{"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "analytics-events", "queryScope": "COLLECTION", "fields": [{"fieldPath": "targetId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "ads", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "payment_status", "order": "ASCENDING"}, {"fieldPath": "start_date", "order": "ASCENDING"}, {"fieldPath": "end_date", "order": "ASCENDING"}]}, {"collectionGroup": "ad_subscriptions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "payment_status", "order": "ASCENDING"}, {"fieldPath": "start_date", "order": "ASCENDING"}, {"fieldPath": "end_date", "order": "ASCENDING"}]}], "fieldOverrides": [{"collectionGroup": "business-cards", "fieldPath": "tags", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"arrayConfig": "CONTAINS", "queryScope": "COLLECTION"}]}, {"collectionGroup": "users", "fieldPath": "email", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}]}