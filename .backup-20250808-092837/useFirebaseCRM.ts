/**
 * Firebase CRM Integration Composable
 * Handles persistence of CRM data (interactions, notes, follow-ups) to Firebase Firestore
 * Structure: business-cards/{cardId}/interactions, notes, follow-ups
 */

import { ref, computed } from 'vue'
import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDocs,
  getDoc,
  query,
  where,
  orderBy,
  onSnapshot,
  Timestamp,
  serverTimestamp,
  type Unsubscribe,
  type DocumentData,
  type QueryConstraint
} from 'firebase/firestore'
import { useFirebase } from './useFirebase'
import { useAuth } from './useAuth'
import { useCRMValidation } from './useCRMValidation'
import { useRateLimit, type RateLimitOperation } from './useRateLimit'
import type { CRMInteraction, CRMFollowUp } from './useCRMInteractions'

// Extended types for Firebase
export interface FirebaseCRMInteraction extends Omit<CRMInteraction, 'timestamp'> {
  timestamp: Timestamp
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface FirebaseCRMFollowUp extends Omit<CRMFollowUp, 'dueDate' | 'completedAt' | 'nextDueDate' | 'reminderTime'> {
  dueDate: Timestamp
  completedAt?: Timestamp
  nextDueDate?: Timestamp
  reminderTime?: Timestamp
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface CRMNote {
  id: string
  cardId: string
  userId: string
  content: string
  isPrivate: boolean
  tags?: string[]
  createdAt: Date
  updatedAt: Date
}

export interface FirebaseCRMNote extends Omit<CRMNote, 'createdAt' | 'updatedAt'> {
  createdAt: Timestamp
  updatedAt: Timestamp
}

export const useFirebaseCRM = () => {
  // Get Firebase and Auth instances
  const { firestore } = useFirebase()
  const { user } = useAuth()

  // Initialize validation and rate limiting
  const validation = useCRMValidation()
  const rateLimit = useRateLimit()

  // State
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Real-time listeners
  const listeners = ref<Map<string, Unsubscribe>>(new Map())

  // Helper function to get collection paths
  const getCollectionPath = (cardId: string, collectionName: 'interactions' | 'notes' | 'follow-ups') => {
    return `business-cards/${cardId}/${collectionName}`
  }

  // Helper function to convert Firestore timestamp to Date
  const timestampToDate = (timestamp: Timestamp | Date): Date => {
    if (timestamp instanceof Timestamp) {
      return timestamp.toDate()
    }
    return timestamp
  }

  // Helper function to convert Date to Firestore timestamp
  const dateToTimestamp = (date: Date): Timestamp => {
    return Timestamp.fromDate(date)
  }

  // Helper function to handle errors
  const handleError = (operation: string, err: any) => {
    console.error(`[FirebaseCRM] Error in ${operation}:`, err)
    error.value = `Failed to ${operation}: ${err.message}`
    throw err
  }

  // =================
  // INTERACTIONS CRUD
  // =================

  /**
   * Create a new interaction
   */
  const createInteraction = async (cardId: string, interactionData: Partial<CRMInteraction>): Promise<CRMInteraction> => {
    if (!firestore || !user.value?.uid) {
      throw new Error('Firebase not initialized or user not authenticated')
    }

    // Validate interaction data
    const validationResult = validation.validateInteraction(interactionData)
    if (!validationResult.isValid) {
      const errorMsg = `Validation failed: ${validationResult.errors.join(', ')}`
      error.value = errorMsg
      throw new Error(errorMsg)
    }

    isLoading.value = true
    error.value = null

    try {
      // Execute with rate limiting
      return await rateLimit.executeWithRateLimit('create', async () => {
        const collectionRef = collection(firestore, getCollectionPath(cardId, 'interactions'))
        
        // Use sanitized data from validation
        const sanitizedData = validationResult.sanitizedValue!
        
        const firebaseData: Omit<FirebaseCRMInteraction, 'id'> = {
          cardId,
          userId: user.value!.uid,
          type: sanitizedData.type,
          title: sanitizedData.title,
          content: sanitizedData.content || '',
          timestamp: dateToTimestamp(sanitizedData.timestamp || new Date()),
          attachments: sanitizedData.attachments || [],
          metadata: sanitizedData.metadata || {},
          createdAt: serverTimestamp() as Timestamp,
          updatedAt: serverTimestamp() as Timestamp
        }

        const docRef = await addDoc(collectionRef, firebaseData)
        
        // Return the created interaction with the generated ID
        return {
          ...sanitizedData,
          id: docRef.id,
          cardId,
          userId: user.value!.uid,
          timestamp: sanitizedData.timestamp || new Date()
        } as CRMInteraction
      })
    } catch (err) {
      handleError('create interaction', err)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Get all interactions for a business card
   */
  const getInteractions = async (cardId: string): Promise<CRMInteraction[]> => {
    if (!firestore) {
      throw new Error('Firebase not initialized')
    }

    isLoading.value = true
    error.value = null

    try {
      // Execute with rate limiting
      return await rateLimit.executeWithRateLimit('read', async () => {
        const collectionRef = collection(firestore, getCollectionPath(cardId, 'interactions'))
        const q = query(collectionRef, orderBy('timestamp', 'desc'))
        const snapshot = await getDocs(q)

        return snapshot.docs.map(doc => {
          const data = doc.data() as FirebaseCRMInteraction
          return {
            ...data,
            id: doc.id,
            timestamp: timestampToDate(data.timestamp)
          } as CRMInteraction
        })
      })
    } catch (err) {
      handleError('get interactions', err)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Update an interaction
   */
  const updateInteraction = async (cardId: string, interactionId: string, updates: Partial<CRMInteraction>): Promise<void> => {
    if (!firestore) {
      throw new Error('Firebase not initialized')
    }

    // Validate update data if provided
    let sanitizedUpdates = updates
    if (Object.keys(updates).length > 0) {
      const validationResult = validation.validateInteraction({ ...updates, type: updates.type || 'note', title: updates.title || 'Update' })
      if (!validationResult.isValid) {
        const errorMsg = `Validation failed: ${validationResult.errors.join(', ')}`
        error.value = errorMsg
        throw new Error(errorMsg)
      }
      sanitizedUpdates = validationResult.sanitizedValue!
    }

    isLoading.value = true
    error.value = null

    try {
      // Execute with rate limiting
      await rateLimit.executeWithRateLimit('update', async () => {
        const docRef = doc(firestore, getCollectionPath(cardId, 'interactions'), interactionId)
        
        const firebaseUpdates: Partial<FirebaseCRMInteraction> = {
          ...sanitizedUpdates,
          updatedAt: serverTimestamp() as Timestamp
        }

        if (sanitizedUpdates.timestamp) {
          firebaseUpdates.timestamp = dateToTimestamp(sanitizedUpdates.timestamp)
        }

        await updateDoc(docRef, firebaseUpdates)
      })
    } catch (err) {
      handleError('update interaction', err)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Delete an interaction
   */
  const deleteInteraction = async (cardId: string, interactionId: string): Promise<void> => {
    if (!firestore) {
      throw new Error('Firebase not initialized')
    }

    isLoading.value = true
    error.value = null

    try {
      // Execute with rate limiting
      await rateLimit.executeWithRateLimit('delete', async () => {
        const docRef = doc(firestore, getCollectionPath(cardId, 'interactions'), interactionId)
        await deleteDoc(docRef)
      })
    } catch (err) {
      handleError('delete interaction', err)
    } finally {
      isLoading.value = false
    }
  }

  // ===============
  // FOLLOW-UPS CRUD
  // ===============

  /**
   * Create a new follow-up
   */
  const createFollowUp = async (cardId: string, followUpData: Partial<CRMFollowUp>): Promise<CRMFollowUp> => {
    if (!firestore || !user.value?.uid) {
      throw new Error('Firebase not initialized or user not authenticated')
    }

    // Validate follow-up data
    const validationResult = validation.validateFollowUp(followUpData)
    if (!validationResult.isValid) {
      const errorMsg = `Validation failed: ${validationResult.errors.join(', ')}`
      error.value = errorMsg
      throw new Error(errorMsg)
    }

    isLoading.value = true
    error.value = null

    try {
      // Execute with rate limiting
      return await rateLimit.executeWithRateLimit('create', async () => {
        const collectionRef = collection(firestore, getCollectionPath(cardId, 'follow-ups'))
        
        // Use sanitized data from validation
        const sanitizedData = validationResult.sanitizedValue!
        
        const firebaseData: Omit<FirebaseCRMFollowUp, 'id'> = {
          cardId,
          userId: user.value!.uid,
          type: sanitizedData.type || 'task',
          title: sanitizedData.title,
          description: sanitizedData.description || '',
          dueDate: dateToTimestamp(sanitizedData.dueDate),
          completed: sanitizedData.completed || false,
          recurring: sanitizedData.recurring || null,
          reminderSent: sanitizedData.reminderSent || false,
          priority: sanitizedData.priority || 'medium',
          metadata: sanitizedData.metadata || {},
          createdAt: serverTimestamp() as Timestamp,
          updatedAt: serverTimestamp() as Timestamp
        }

        if (sanitizedData.completedAt) {
          firebaseData.completedAt = dateToTimestamp(sanitizedData.completedAt)
        }
        if (sanitizedData.nextDueDate) {
          firebaseData.nextDueDate = dateToTimestamp(sanitizedData.nextDueDate)
        }
        if (sanitizedData.reminderTime) {
          firebaseData.reminderTime = dateToTimestamp(sanitizedData.reminderTime)
        }

        const docRef = await addDoc(collectionRef, firebaseData)
        
        return {
          ...sanitizedData,
          id: docRef.id,
          cardId,
          userId: user.value!.uid,
          dueDate: sanitizedData.dueDate,
          completed: sanitizedData.completed || false,
          reminderSent: sanitizedData.reminderSent || false,
          priority: sanitizedData.priority || 'medium'
        } as CRMFollowUp
      })
    } catch (err) {
      handleError('create follow-up', err)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Get all follow-ups for a business card
   */
  const getFollowUps = async (cardId: string, filterCompleted?: boolean): Promise<CRMFollowUp[]> => {
    if (!firestore) {
      throw new Error('Firebase not initialized')
    }

    isLoading.value = true
    error.value = null

    try {
      // Execute with rate limiting
      return await rateLimit.executeWithRateLimit('read', async () => {
        const collectionRef = collection(firestore, getCollectionPath(cardId, 'follow-ups'))
        
        const constraints: QueryConstraint[] = [orderBy('dueDate', 'asc')]
        if (filterCompleted !== undefined) {
          constraints.unshift(where('completed', '==', filterCompleted))
        }
        
        const q = query(collectionRef, ...constraints)
        const snapshot = await getDocs(q)

        return snapshot.docs.map(doc => {
          const data = doc.data() as FirebaseCRMFollowUp
          return {
            ...data,
            id: doc.id,
            dueDate: timestampToDate(data.dueDate),
            completedAt: data.completedAt ? timestampToDate(data.completedAt) : undefined,
            nextDueDate: data.nextDueDate ? timestampToDate(data.nextDueDate) : undefined,
            reminderTime: data.reminderTime ? timestampToDate(data.reminderTime) : undefined
          } as CRMFollowUp
        })
      })
    } catch (err) {
      handleError('get follow-ups', err)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Update a follow-up
   */
  const updateFollowUp = async (cardId: string, followUpId: string, updates: Partial<CRMFollowUp>): Promise<void> => {
    if (!firestore) {
      throw new Error('Firebase not initialized')
    }

    // Validate update data if provided
    let sanitizedUpdates = updates
    if (Object.keys(updates).length > 0 && (updates.title || updates.description || updates.dueDate)) {
      const validationResult = validation.validateFollowUp({ ...updates, title: updates.title || 'Update', dueDate: updates.dueDate || new Date() })
      if (!validationResult.isValid) {
        const errorMsg = `Validation failed: ${validationResult.errors.join(', ')}`
        error.value = errorMsg
        throw new Error(errorMsg)
      }
      sanitizedUpdates = validationResult.sanitizedValue!
    }

    isLoading.value = true
    error.value = null

    try {
      // Execute with rate limiting
      await rateLimit.executeWithRateLimit('update', async () => {
        const docRef = doc(firestore, getCollectionPath(cardId, 'follow-ups'), followUpId)
        
        const firebaseUpdates: Partial<FirebaseCRMFollowUp> = {
          ...sanitizedUpdates,
          updatedAt: serverTimestamp() as Timestamp
        }

        if (sanitizedUpdates.dueDate) {
          firebaseUpdates.dueDate = dateToTimestamp(sanitizedUpdates.dueDate)
        }
        if (sanitizedUpdates.completedAt) {
          firebaseUpdates.completedAt = dateToTimestamp(sanitizedUpdates.completedAt)
        }
        if (sanitizedUpdates.nextDueDate) {
          firebaseUpdates.nextDueDate = dateToTimestamp(sanitizedUpdates.nextDueDate)
        }
        if (sanitizedUpdates.reminderTime) {
          firebaseUpdates.reminderTime = dateToTimestamp(sanitizedUpdates.reminderTime)
        }

        await updateDoc(docRef, firebaseUpdates)
      })
    } catch (err) {
      handleError('update follow-up', err)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Delete a follow-up
   */
  const deleteFollowUp = async (cardId: string, followUpId: string): Promise<void> => {
    if (!firestore) {
      throw new Error('Firebase not initialized')
    }

    isLoading.value = true
    error.value = null

    try {
      // Execute with rate limiting
      await rateLimit.executeWithRateLimit('delete', async () => {
        const docRef = doc(firestore, getCollectionPath(cardId, 'follow-ups'), followUpId)
        await deleteDoc(docRef)
      })
    } catch (err) {
      handleError('delete follow-up', err)
    } finally {
      isLoading.value = false
    }
  }

  // ==========
  // NOTES CRUD
  // ==========

  /**
   * Create a new note
   */
  const createNote = async (cardId: string, noteData: Partial<CRMNote>): Promise<CRMNote> => {
    if (!firestore || !user.value?.uid) {
      throw new Error('Firebase not initialized or user not authenticated')
    }

    // Validate note data
    const validationResult = validation.validateNote(noteData)
    if (!validationResult.isValid) {
      const errorMsg = `Validation failed: ${validationResult.errors.join(', ')}`
      error.value = errorMsg
      throw new Error(errorMsg)
    }

    isLoading.value = true
    error.value = null

    try {
      // Execute with rate limiting
      return await rateLimit.executeWithRateLimit('create', async () => {
        const collectionRef = collection(firestore, getCollectionPath(cardId, 'notes'))
        
        // Use sanitized data from validation
        const sanitizedData = validationResult.sanitizedValue!
        
        const firebaseData: Omit<FirebaseCRMNote, 'id'> = {
          cardId,
          userId: user.value!.uid,
          content: sanitizedData.content,
          isPrivate: sanitizedData.isPrivate || false,
          tags: sanitizedData.tags || [],
          createdAt: serverTimestamp() as Timestamp,
          updatedAt: serverTimestamp() as Timestamp
        }

        const docRef = await addDoc(collectionRef, firebaseData)
        
        return {
          ...sanitizedData,
          id: docRef.id,
          cardId,
          userId: user.value!.uid,
          content: sanitizedData.content,
          isPrivate: sanitizedData.isPrivate || false,
          tags: sanitizedData.tags || [],
          createdAt: new Date(),
          updatedAt: new Date()
        } as CRMNote
      })
    } catch (err) {
      handleError('create note', err)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Get all notes for a business card
   */
  const getNotes = async (cardId: string, includePrivate = true): Promise<CRMNote[]> => {
    if (!firestore) {
      throw new Error('Firebase not initialized')
    }

    isLoading.value = true
    error.value = null

    try {
      // Execute with rate limiting
      return await rateLimit.executeWithRateLimit('read', async () => {
        const collectionRef = collection(firestore, getCollectionPath(cardId, 'notes'))
        
        const constraints: QueryConstraint[] = [orderBy('createdAt', 'desc')]
        if (!includePrivate && user.value?.uid) {
          // Only show public notes or notes created by the current user
          constraints.unshift(where('isPrivate', '==', false))
        }
        
        const q = query(collectionRef, ...constraints)
        const snapshot = await getDocs(q)

        return snapshot.docs.map(doc => {
          const data = doc.data() as FirebaseCRMNote
          return {
            ...data,
            id: doc.id,
            createdAt: timestampToDate(data.createdAt),
            updatedAt: timestampToDate(data.updatedAt)
          } as CRMNote
        })
      })
    } catch (err) {
      handleError('get notes', err)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Update a note
   */
  const updateNote = async (cardId: string, noteId: string, updates: Partial<CRMNote>): Promise<void> => {
    if (!firestore) {
      throw new Error('Firebase not initialized')
    }

    // Validate update data if provided
    let sanitizedUpdates = updates
    if (Object.keys(updates).length > 0 && updates.content) {
      const validationResult = validation.validateNote({ ...updates, content: updates.content })
      if (!validationResult.isValid) {
        const errorMsg = `Validation failed: ${validationResult.errors.join(', ')}`
        error.value = errorMsg
        throw new Error(errorMsg)
      }
      sanitizedUpdates = validationResult.sanitizedValue!
    }

    isLoading.value = true
    error.value = null

    try {
      // Execute with rate limiting
      await rateLimit.executeWithRateLimit('update', async () => {
        const docRef = doc(firestore, getCollectionPath(cardId, 'notes'), noteId)
        
        const firebaseUpdates: Partial<FirebaseCRMNote> = {
          ...sanitizedUpdates,
          updatedAt: serverTimestamp() as Timestamp
        }

        // Remove date fields from updates as they should not be updated directly
        delete (firebaseUpdates as any).createdAt

        await updateDoc(docRef, firebaseUpdates)
      })
    } catch (err) {
      handleError('update note', err)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Delete a note
   */
  const deleteNote = async (cardId: string, noteId: string): Promise<void> => {
    if (!firestore) {
      throw new Error('Firebase not initialized')
    }

    isLoading.value = true
    error.value = null

    try {
      // Execute with rate limiting
      await rateLimit.executeWithRateLimit('delete', async () => {
        const docRef = doc(firestore, getCollectionPath(cardId, 'notes'), noteId)
        await deleteDoc(docRef)
      })
    } catch (err) {
      handleError('delete note', err)
    } finally {
      isLoading.value = false
    }
  }

  // ====================
  // REAL-TIME LISTENERS
  // ====================

  /**
   * Subscribe to real-time updates for interactions
   */
  const subscribeToInteractions = (cardId: string, callback: (interactions: CRMInteraction[]) => void): Unsubscribe => {
    if (!firestore) {
      throw new Error('Firebase not initialized')
    }

    const collectionRef = collection(firestore, getCollectionPath(cardId, 'interactions'))
    const q = query(collectionRef, orderBy('timestamp', 'desc'))

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const interactions = snapshot.docs.map(doc => {
        const data = doc.data() as FirebaseCRMInteraction
        return {
          ...data,
          id: doc.id,
          timestamp: timestampToDate(data.timestamp)
        } as CRMInteraction
      })
      callback(interactions)
    }, (err) => {
      console.error('[FirebaseCRM] Error in interactions listener:', err)
      error.value = `Real-time sync error: ${err.message}`
    })

    // Store the unsubscribe function
    listeners.value.set(`interactions-${cardId}`, unsubscribe)

    return unsubscribe
  }

  /**
   * Subscribe to real-time updates for follow-ups
   */
  const subscribeToFollowUps = (cardId: string, callback: (followUps: CRMFollowUp[]) => void, filterCompleted?: boolean): Unsubscribe => {
    if (!firestore) {
      throw new Error('Firebase not initialized')
    }

    const collectionRef = collection(firestore, getCollectionPath(cardId, 'follow-ups'))
    
    const constraints: QueryConstraint[] = [orderBy('dueDate', 'asc')]
    if (filterCompleted !== undefined) {
      constraints.unshift(where('completed', '==', filterCompleted))
    }
    
    const q = query(collectionRef, ...constraints)

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const followUps = snapshot.docs.map(doc => {
        const data = doc.data() as FirebaseCRMFollowUp
        return {
          ...data,
          id: doc.id,
          dueDate: timestampToDate(data.dueDate),
          completedAt: data.completedAt ? timestampToDate(data.completedAt) : undefined,
          nextDueDate: data.nextDueDate ? timestampToDate(data.nextDueDate) : undefined,
          reminderTime: data.reminderTime ? timestampToDate(data.reminderTime) : undefined
        } as CRMFollowUp
      })
      callback(followUps)
    }, (err) => {
      console.error('[FirebaseCRM] Error in follow-ups listener:', err)
      error.value = `Real-time sync error: ${err.message}`
    })

    // Store the unsubscribe function
    listeners.value.set(`followups-${cardId}`, unsubscribe)

    return unsubscribe
  }

  /**
   * Subscribe to real-time updates for notes
   */
  const subscribeToNotes = (cardId: string, callback: (notes: CRMNote[]) => void, includePrivate = true): Unsubscribe => {
    if (!firestore) {
      throw new Error('Firebase not initialized')
    }

    const collectionRef = collection(firestore, getCollectionPath(cardId, 'notes'))
    
    const constraints: QueryConstraint[] = [orderBy('createdAt', 'desc')]
    if (!includePrivate && user.value?.uid) {
      constraints.unshift(where('isPrivate', '==', false))
    }
    
    const q = query(collectionRef, ...constraints)

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const notes = snapshot.docs.map(doc => {
        const data = doc.data() as FirebaseCRMNote
        return {
          ...data,
          id: doc.id,
          createdAt: timestampToDate(data.createdAt),
          updatedAt: timestampToDate(data.updatedAt)
        } as CRMNote
      })
      callback(notes)
    }, (err) => {
      console.error('[FirebaseCRM] Error in notes listener:', err)
      error.value = `Real-time sync error: ${err.message}`
    })

    // Store the unsubscribe function
    listeners.value.set(`notes-${cardId}`, unsubscribe)

    return unsubscribe
  }

  /**
   * Add a business card to the CRM
   */
  const addBusinessCard = async (businessCardData: any): Promise<string | null> => {
    if (!firestore || !user.value) {
      console.error('[FirebaseCRM] Firestore not initialized or user not authenticated')
      return null
    }

    try {
      isLoading.value = true
      error.value = null

      // Execute with rate limiting
      return await rateLimit.executeWithRateLimit('create', async () => {
        // Validate and sanitize contact data if present
        let cleanData = { ...businessCardData }
        if (businessCardData.name || businessCardData.email || businessCardData.phone) {
          const contactValidation = validation.validateContact(businessCardData)
          if (contactValidation.sanitizedValue) {
            cleanData = { ...cleanData, ...contactValidation.sanitizedValue }
          }
        }
        
        // Handle image field specifically - convert to URL string if it's an object
        if (cleanData.image && typeof cleanData.image === 'object') {
          // If image has a URL property, use that
          if (cleanData.image.url) {
            cleanData.image = cleanData.image.url
          } else if (cleanData.image.src) {
            cleanData.image = cleanData.image.src
          } else {
            // If it's a complex object without URL, remove it
            delete cleanData.image
          }
        }

        // Remove any undefined or null values
        Object.keys(cleanData).forEach(key => {
          if (cleanData[key] === undefined || cleanData[key] === null) {
            delete cleanData[key]
          }
        })

        // Add the business card to the business-cards collection
        const docRef = await addDoc(collection(firestore, 'business-cards'), {
          ...cleanData,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        })

        console.log('[FirebaseCRM] Business card added:', docRef.id)
        return docRef.id
      })
    } catch (err) {
      handleError('add business card', err)
      return null
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Unsubscribe from all listeners
   */
  const unsubscribeAll = () => {
    listeners.value.forEach(unsubscribe => unsubscribe())
    listeners.value.clear()
  }

  /**
   * Unsubscribe from specific card listeners
   */
  const unsubscribeCard = (cardId: string) => {
    const keys = [`interactions-${cardId}`, `followups-${cardId}`, `notes-${cardId}`]
    keys.forEach(key => {
      const unsubscribe = listeners.value.get(key)
      if (unsubscribe) {
        unsubscribe()
        listeners.value.delete(key)
      }
    })
  }

  return {
    // State
    isLoading: computed(() => isLoading.value),
    error: computed(() => error.value),

    // Business Cards
    addBusinessCard,

    // Interactions
    createInteraction,
    getInteractions,
    updateInteraction,
    deleteInteraction,

    // Follow-ups
    createFollowUp,
    getFollowUps,
    updateFollowUp,
    deleteFollowUp,

    // Notes
    createNote,
    getNotes,
    updateNote,
    deleteNote,

    // Real-time subscriptions
    subscribeToInteractions,
    subscribeToFollowUps,
    subscribeToNotes,
    unsubscribeAll,
    unsubscribeCard,

    // Rate limiting
    getRateLimitStats: (operation: keyof RateLimitOperation) => rateLimit.getUsageStats(operation),
    setRateLimitFeedback: rateLimit.setFeedbackHandler,
    updateRateLimits: rateLimit.updateLimits
  }
}