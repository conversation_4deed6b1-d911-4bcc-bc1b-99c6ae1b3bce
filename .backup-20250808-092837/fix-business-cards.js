#!/usr/bin/env node

/**
 * Fix Business Cards Script
 * This script updates existing business cards to set the required fields for public visibility
 */

const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
// Make sure you have your service account key configured
const serviceAccount = require('../path/to/your/serviceAccountKey.json'); // Update this path

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

const db = admin.firestore();

async function updateBusinessCards() {
  try {
    console.log('🔍 Fetching business cards...');
    
    // Get all business cards
    const businessCardsRef = db.collection('business-cards');
    const snapshot = await businessCardsRef.get();
    
    if (snapshot.empty) {
      console.log('❌ No business cards found');
      return;
    }
    
    console.log(`📄 Found ${snapshot.size} business cards`);
    
    const batch = db.batch();
    let updateCount = 0;
    
    snapshot.forEach((doc) => {
      const data = doc.data();
      const needsUpdate = !data.moderationStatus || data.moderationStatus === 'pending' || !data.isPublic;
      
      if (needsUpdate) {
        console.log(`📝 Updating card: ${doc.id} (${data.name || data.first_name + ' ' + data.last_name || 'Unknown'})`);
        
        batch.update(doc.ref, {
          moderationStatus: 'approved',
          isPublic: true,
          moderatedAt: admin.firestore.FieldValue.serverTimestamp(),
          moderatedBy: 'admin-script',
          moderationNotes: 'Auto-approved by migration script',
          updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });
        
        updateCount++;
      }
    });
    
    if (updateCount > 0) {
      console.log(`💾 Updating ${updateCount} business cards...`);
      await batch.commit();
      console.log('✅ Business cards updated successfully!');
      console.log(`📊 Updated ${updateCount} out of ${snapshot.size} cards`);
    } else {
      console.log('✅ All business cards are already properly configured');
    }
    
  } catch (error) {
    console.error('❌ Error updating business cards:', error);
  } finally {
    process.exit(0);
  }
}

// Run the script
updateBusinessCards();
