<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useCRM } from '~/composables/useCRM';
import ClientManager from '~/components/crm/ClientManager.vue';
import type { ICRMClient } from '~/types/crm';
import { useNotifications } from '~/composables/useNotifications';

definePageMeta({
  requiresAuth: true,
  layout: 'dashboard'
});

const { 
  getClients, 
  deleteClient, 
  clients, 
  loading,
  error 
} = useCRM();

const { showSuccess, showError } = useNotifications();

// Component state
const searchQuery = ref('');
const selectedClient = ref<ICRMClient | null>(null);
const showEditModal = ref(false);
const confirmingDelete = ref<string | null>(null);

// Computed
const filteredClients = computed(() => {
  if (!searchQuery.value) return clients.value;
  
  const query = searchQuery.value.toLowerCase();
  return clients.value.filter(client =>
    client.name.toLowerCase().includes(query) ||
    client.email?.toLowerCase().includes(query) ||
    client.company?.toLowerCase().includes(query) ||
    client.phone?.includes(query)
  );
});

// Methods
onMounted(() => {
  loadClients();
});

const loadClients = async () => {
  try {
    await getClients();
  } catch (err: any) {
    showError(`Failed to load clients: ${err.message}`);
  }
};

const editClient = (client: ICRMClient) => {
  selectedClient.value = client;
  showEditModal.value = true;
};

const handleSave = (updatedClient: ICRMClient) => {
  showEditModal.value = false;
  selectedClient.value = null;
  showSuccess('Client updated successfully!');
  loadClients(); // Refresh list
};

const handleCancel = () => {
  showEditModal.value = false;
  selectedClient.value = null;
};

const handleSaveError = (error: any) => {
  showError(`Failed to update client: ${error.message}`);
};

const confirmDelete = (clientId: string) => {
  confirmingDelete.value = clientId;
};

const cancelDelete = () => {
  confirmingDelete.value = null;
};

const performDelete = async (clientId: string) => {
  try {
    await deleteClient(clientId);
    confirmingDelete.value = null;
    showSuccess('Client deleted successfully');
    loadClients(); // Refresh list
  } catch (err: any) {
    showError(`Failed to delete client: ${err.message}`);
  }
};

const formatDate = (date: any): string => {
  if (!date) return '';
  
  // Handle Firestore Timestamp
  const jsDate = date.toDate ? date.toDate() : new Date(date);
  return jsDate.toLocaleDateString();
};
</script>

<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-6">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                CRM Clients
              </h1>
              <p class="mt-2 text-gray-600 dark:text-gray-400">
                Manage your client contacts and relationships
              </p>
            </div>
            
            <div class="flex space-x-3">
              <NuxtLink
                to="/c/crm/business-cards"
                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600"
              >
                <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                Scan Business Card
              </NuxtLink>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Search and filters -->
      <div class="mb-6">
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search clients..."
            class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>
      </div>
      
      <!-- Loading state -->
      <div v-if="loading" class="flex justify-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
      
      <!-- Error state -->
      <div v-else-if="error" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <p class="text-red-600 dark:text-red-400">{{ error }}</p>
        <button
          @click="loadClients"
          class="mt-2 text-sm text-red-600 hover:text-red-500 underline"
        >
          Try again
        </button>
      </div>
      
      <!-- Empty state -->
      <div v-else-if="filteredClients.length === 0" class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No clients found</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          {{ searchQuery ? 'No clients match your search criteria.' : 'Get started by scanning your first business card.' }}
        </p>
        <div class="mt-6">
          <NuxtLink
            to="/c/crm/business-cards"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600"
          >
            <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            Scan Business Card
          </NuxtLink>
        </div>
      </div>
      
      <!-- Clients grid -->
      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div
          v-for="client in filteredClients"
          :key="client.id"
          class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow"
        >
          <!-- Client info -->
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                {{ client.name }}
              </h3>
              <p v-if="client.title" class="text-sm text-gray-600 dark:text-gray-400">
                {{ client.title }}
              </p>
              <p v-if="client.company" class="text-sm text-gray-600 dark:text-gray-400">
                {{ client.company }}
              </p>
            </div>
            
            <!-- Source badge -->
            <span 
              :class="[
                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                client.source === 'business_card' 
                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
              ]"
            >
              {{ client.source === 'business_card' ? 'Business Card' : 'Manual' }}
            </span>
          </div>
          
          <!-- Contact details -->
          <div class="mt-4 space-y-2">
            <div v-if="client.email" class="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <a :href="`mailto:${client.email}`" class="hover:text-blue-600">
                {{ client.email }}
              </a>
            </div>
            
            <div v-if="client.phone" class="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
              <a :href="`tel:${client.phone}`" class="hover:text-blue-600">
                {{ client.phone }}
              </a>
            </div>
          </div>
          
          <!-- Meta info -->
          <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
              <span>Added {{ formatDate(client.created_at) }}</span>
              
              <!-- Actions -->
              <div class="flex space-x-2">
                <button
                  @click="editClient(client)"
                  class="text-blue-600 hover:text-blue-500"
                  title="Edit client"
                >
                  <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                </button>
                
                <button
                  @click="confirmDelete(client.id)"
                  class="text-red-600 hover:text-red-500"
                  title="Delete client"
                >
                  <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Edit Modal -->
    <div v-if="showEditModal" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Backdrop -->
        <div 
          class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          @click="handleCancel"
        ></div>
        
        <!-- Modal content -->
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full">
          <ClientManager
            :existing-client="selectedClient || undefined"
            @save="handleSave"
            @cancel="handleCancel"
            @error="handleSaveError"
          />
        </div>
      </div>
    </div>
    
    <!-- Delete Confirmation Modal -->
    <div v-if="confirmingDelete" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Backdrop -->
        <div 
          class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          @click="cancelDelete"
        ></div>
        
        <!-- Modal content -->
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Delete Client
            </h3>
            <p class="text-gray-600 dark:text-gray-400 mb-6">
              Are you sure you want to delete this client? This action cannot be undone.
            </p>
            <div class="flex justify-end space-x-3">
              <button
                @click="cancelDelete"
                class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
              >
                Cancel
              </button>
              <button
                @click="performDelete(confirmingDelete)"
                class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>