/**
 * Composable for behavioral analysis and user behavior tracking
 * Provides behavioral targeting data and engagement scoring
 */

import {
  collection,
  doc,
  addDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit as firestoreLimit,
  Timestamp,
  writeBatch,
  type DocumentData
} from 'firebase/firestore';
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useFirebase } from './useFirebase';
import { useCurrentUser } from './useCurrentUser';
import type {
  UserBehavior,
  BehaviorEvent,
  UserDemographics,
  DeviceInfo,
  BehavioralCriteria,
  UserBehaviorPattern
} from '~/types/audience-segmentation';

export function useBehavioralAnalysis() {
  const { firestoreDb } = useFirebase();
  const { currentUser } = useCurrentUser();

  // State
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const currentSession = ref<string>('');
  const behaviorEvents = ref<BehaviorEvent[]>([]);
  const userInterests = ref<string[]>([]);
  const engagementScore = ref(0);
  const deviceInfo = ref<DeviceInfo | null>(null);

  // Behavior tracking
  const sessionStartTime = ref<Date>(new Date());
  const pageViews = ref<string[]>([]);
  const interactions = ref<BehaviorEvent[]>([]);
  const scrollDepth = ref(0);
  const timeOnPage = ref(0);

  /**
   * Initialize behavior tracking session
   */
  const initializeSession = () => {
    currentSession.value = generateSessionId();
    sessionStartTime.value = new Date();
    deviceInfo.value = detectDeviceInfo();
    
    // Track page visibility changes
    if (typeof document !== 'undefined') {
      document.addEventListener('visibilitychange', handleVisibilityChange);
    }

    // Track scroll depth
    if (typeof window !== 'undefined') {
      window.addEventListener('scroll', trackScrollDepth);
      window.addEventListener('beforeunload', flushBehaviorData);
    }
  };

  /**
   * Generate unique session ID
   */
  const generateSessionId = (): string => {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  /**
   * Detect device and browser information
   */
  const detectDeviceInfo = (): DeviceInfo => {
    if (typeof window === 'undefined' || typeof navigator === 'undefined') {
      return {
        type: 'desktop',
        os: 'unknown',
        browser: 'unknown',
        screenSize: { width: 0, height: 0 },
        language: 'en',
        timezone: 'UTC'
      };
    }

    const userAgent = navigator.userAgent.toLowerCase();
    const screenWidth = window.screen.width;
    const screenHeight = window.screen.height;

    // Detect device type
    let deviceType: 'mobile' | 'tablet' | 'desktop' = 'desktop';
    if (/mobile|android|iphone|ipod|blackberry|iemobile|opera mini/i.test(userAgent)) {
      deviceType = 'mobile';
    } else if (/tablet|ipad|playbook|silk/i.test(userAgent)) {
      deviceType = 'tablet';
    }

    // Detect OS
    let os: 'ios' | 'android' | 'windows' | 'macos' | 'linux' | 'other' = 'other';
    if (/windows/i.test(userAgent)) {
      os = 'windows';
    } else if (/macintosh|mac os x/i.test(userAgent)) {
      os = 'macos';
    } else if (/linux/i.test(userAgent)) {
      os = 'linux';
    } else if (/android/i.test(userAgent)) {
      os = 'android';
    } else if (/iphone|ipad|ipod/i.test(userAgent)) {
      os = 'ios';
    }

    // Detect browser
    let browser: 'chrome' | 'safari' | 'firefox' | 'edge' | 'other' = 'other';
    if (/chrome/i.test(userAgent) && !/edge/i.test(userAgent)) {
      browser = 'chrome';
    } else if (/safari/i.test(userAgent) && !/chrome/i.test(userAgent)) {
      browser = 'safari';
    } else if (/firefox/i.test(userAgent)) {
      browser = 'firefox';
    } else if (/edge/i.test(userAgent)) {
      browser = 'edge';
    }

    return {
      type: deviceType,
      os,
      browser,
      screenSize: { width: screenWidth, height: screenHeight },
      language: navigator.language || 'en',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC'
    };
  };

  /**
   * Track page view event
   * @param page Page identifier
   * @param metadata Additional page metadata
   */
  const trackPageView = (page: string, metadata?: Record<string, any>) => {
    const event: BehaviorEvent = {
      type: 'page_view',
      timestamp: Timestamp.now(),
      page,
      metadata
    };

    behaviorEvents.value.push(event);
    pageViews.value.push(page);
    
    // Reset page-specific tracking
    scrollDepth.value = 0;
    timeOnPage.value = Date.now();
  };

  /**
   * Track click event
   * @param elementId Element that was clicked
   * @param page Current page
   * @param metadata Additional click metadata
   */
  const trackClick = (elementId: string, page: string, metadata?: Record<string, any>) => {
    const event: BehaviorEvent = {
      type: 'click',
      timestamp: Timestamp.now(),
      elementId,
      page,
      metadata
    };

    behaviorEvents.value.push(event);
    interactions.value.push(event);
    
    // Update engagement score
    updateEngagementScore();
  };

  /**
   * Track interaction event (hover, focus, etc.)
   * @param elementId Element that was interacted with
   * @param page Current page
   * @param duration Duration of interaction in milliseconds
   * @param metadata Additional interaction metadata
   */
  const trackInteraction = (
    elementId: string, 
    page: string, 
    duration?: number, 
    metadata?: Record<string, any>
  ) => {
    const event: BehaviorEvent = {
      type: 'interaction',
      timestamp: Timestamp.now(),
      elementId,
      page,
      duration,
      metadata
    };

    behaviorEvents.value.push(event);
    interactions.value.push(event);
    
    updateEngagementScore();
  };

  /**
   * Track conversion event
   * @param page Current page
   * @param value Conversion value
   * @param metadata Additional conversion metadata
   */
  const trackConversion = (page: string, value?: number, metadata?: Record<string, any>) => {
    const event: BehaviorEvent = {
      type: 'conversion',
      timestamp: Timestamp.now(),
      page,
      value,
      metadata
    };

    behaviorEvents.value.push(event);
    
    // Significant boost to engagement score for conversions
    engagementScore.value = Math.min(100, engagementScore.value + 25);
    
    // Flush data immediately for conversions
    flushBehaviorData();
  };

  /**
   * Track download event
   * @param page Current page
   * @param metadata Download metadata (file type, size, etc.)
   */
  const trackDownload = (page: string, metadata?: Record<string, any>) => {
    const event: BehaviorEvent = {
      type: 'download',
      timestamp: Timestamp.now(),
      page,
      metadata
    };

    behaviorEvents.value.push(event);
    updateEngagementScore();
  };

  /**
   * Track scroll depth on current page
   */
  const trackScrollDepth = () => {
    if (typeof window === 'undefined' || typeof document === 'undefined') return;

    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const docHeight = Math.max(
      document.body.scrollHeight,
      document.body.offsetHeight,
      document.documentElement.clientHeight,
      document.documentElement.scrollHeight,
      document.documentElement.offsetHeight
    );
    const winHeight = window.innerHeight;
    const scrollPercent = Math.round((scrollTop / (docHeight - winHeight)) * 100);

    if (scrollPercent > scrollDepth.value) {
      scrollDepth.value = Math.min(100, scrollPercent);
      
      // Track significant scroll milestones
      if (scrollPercent >= 25 && scrollPercent % 25 === 0) {
        const event: BehaviorEvent = {
          type: 'scroll',
          timestamp: Timestamp.now(),
          page: window.location.pathname,
          metadata: { scrollPercent }
        };
        behaviorEvents.value.push(event);
        updateEngagementScore();
      }
    }
  };

  /**
   * Handle page visibility changes
   */
  const handleVisibilityChange = () => {
    if (typeof document === 'undefined') return;

    if (document.hidden) {
      // Page became hidden - record time on page
      const currentTime = Date.now();
      const duration = currentTime - timeOnPage.value;
      
      if (duration > 1000) { // Only count if more than 1 second
        const event: BehaviorEvent = {
          type: 'page_view',
          timestamp: Timestamp.now(),
          page: window.location.pathname,
          duration,
          metadata: { visibilityChange: 'hidden' }
        };
        behaviorEvents.value.push(event);
      }
    } else {
      // Page became visible - reset timer
      timeOnPage.value = Date.now();
    }
  };

  /**
   * Update engagement score based on recent activity
   */
  const updateEngagementScore = () => {
    const recentEvents = behaviorEvents.value.filter(event => {
      const eventTime = event.timestamp.toDate();
      const now = new Date();
      const diffMinutes = (now.getTime() - eventTime.getTime()) / (1000 * 60);
      return diffMinutes <= 30; // Last 30 minutes
    });

    // Calculate score based on different event types
    let score = 0;
    recentEvents.forEach(event => {
      switch (event.type) {
        case 'page_view':
          score += 2;
          break;
        case 'click':
          score += 5;
          break;
        case 'interaction':
          score += 3;
          break;
        case 'scroll':
          score += 1;
          break;
        case 'download':
          score += 10;
          break;
        case 'conversion':
          score += 25;
          break;
      }
    });

    // Bonus for session duration
    const sessionDuration = (Date.now() - sessionStartTime.value.getTime()) / (1000 * 60);
    if (sessionDuration > 5) score += 5; // 5+ minutes
    if (sessionDuration > 15) score += 10; // 15+ minutes

    engagementScore.value = Math.min(100, score);
  };

  /**
   * Analyze user interests based on behavior
   * @param events Array of behavior events
   * @returns Array of inferred interests
   */
  const analyzeInterests = (events: BehaviorEvent[]): string[] => {
    const interests = new Set<string>();
    const pageCategories: Record<string, string[]> = {
      '/business-cards': ['business', 'networking', 'professional'],
      '/analytics': ['data', 'analytics', 'business_intelligence'],
      '/ads': ['advertising', 'marketing', 'promotion'],
      '/contact': ['networking', 'communication'],
      '/pricing': ['business', 'financial_planning']
    };

    // Analyze page views for interests
    events.forEach(event => {
      if (event.type === 'page_view' && event.page) {
        Object.entries(pageCategories).forEach(([pattern, categories]) => {
          if (event.page.includes(pattern)) {
            categories.forEach(category => interests.add(category));
          }
        });
      }

      // Analyze click targets for interests
      if (event.type === 'click' && event.metadata?.elementType) {
        const elementType = event.metadata.elementType;
        if (elementType === 'cta_button') interests.add('action_oriented');
        if (elementType === 'nav_link') interests.add('exploratory');
        if (elementType === 'social_share') interests.add('social_sharing');
      }

      // Analyze downloads for interests
      if (event.type === 'download' && event.metadata?.fileType) {
        const fileType = event.metadata.fileType;
        if (fileType === 'pdf') interests.add('documentation');
        if (fileType === 'image') interests.add('visual_content');
        if (fileType === 'vcf') interests.add('contact_management');
      }
    });

    return Array.from(interests);
  };

  /**
   * Create behavioral criteria from user data
   * @param userId User ID to analyze
   * @returns Promise resolving to behavioral criteria
   */
  const createBehavioralCriteria = async (userId: string): Promise<BehavioralCriteria> => {
    if (!firestoreDb) {
      throw new Error('Firestore not initialized');
    }

    try {
      // Fetch user behavior data
      const q = query(
        collection(firestoreDb, 'user_behaviors'),
        where('userId', '==', userId),
        orderBy('timestamp', 'desc'),
        firestoreLimit(100)
      );

      const snapshot = await getDocs(q);
      const behaviors: UserBehavior[] = [];

      snapshot.forEach(doc => {
        behaviors.push({
          userId: doc.id,
          ...doc.data()
        } as UserBehavior);
      });

      if (behaviors.length === 0) {
        return {
          interests: [],
          behaviors: [],
          deviceTypes: ['mobile', 'tablet', 'desktop'],
          engagementLevel: ['medium']
        };
      }

      // Analyze behaviors to create criteria
      const allEvents = behaviors.flatMap(b => b.events);
      const interests = analyzeInterests(allEvents);
      const deviceTypes = [...new Set(behaviors.map(b => b.deviceInfo.type))];
      const avgEngagement = behaviors.reduce((sum, b) => sum + b.engagementScore, 0) / behaviors.length;
      
      let engagementLevel: ('low' | 'medium' | 'high')[];
      if (avgEngagement < 30) {
        engagementLevel = ['low'];
      } else if (avgEngagement < 70) {
        engagementLevel = ['medium'];
      } else {
        engagementLevel = ['high'];
      }

      // Create behavior patterns
      const behaviorPatterns = analyzeBehaviorPatterns(allEvents);

      return {
        interests,
        behaviors: behaviorPatterns,
        deviceTypes: deviceTypes as ('mobile' | 'tablet' | 'desktop')[],
        engagementLevel,
        visitFrequency: determineVisitFrequency(behaviors)
      };
    } catch (err) {
      console.error('Error creating behavioral criteria:', err);
      return {
        interests: [],
        behaviors: [],
        deviceTypes: ['mobile', 'tablet', 'desktop'],
        engagementLevel: ['medium']
      };
    }
  };

  /**
   * Analyze behavior patterns from events
   * @param events Array of behavior events
   * @returns Array of behavior patterns
   */
  const analyzeBehaviorPatterns = (events: BehaviorEvent[]): UserBehaviorPattern[] => {
    const patterns: UserBehaviorPattern[] = [];
    const eventCounts: Record<string, number> = {};
    const pageVisits: Record<string, Date[]> = {};

    // Count event types and page visits
    events.forEach(event => {
      const key = `${event.type}_${event.page}`;
      eventCounts[key] = (eventCounts[key] || 0) + 1;

      if (event.type === 'page_view') {
        if (!pageVisits[event.page]) {
          pageVisits[event.page] = [];
        }
        pageVisits[event.page].push(event.timestamp.toDate());
      }
    });

    // Create patterns for frequent behaviors
    Object.entries(eventCounts).forEach(([key, count]) => {
      if (count >= 3) { // Minimum 3 occurrences to be considered a pattern
        const [type, page] = key.split('_', 2);
        patterns.push({
          type: type as any,
          action: `${type}_on_${page}`,
          frequency: {
            min: Math.floor(count / 7), // Per week
            max: count,
            period: 'week'
          }
        });
      }
    });

    return patterns;
  };

  /**
   * Determine visit frequency based on behavior data
   * @param behaviors Array of user behaviors
   * @returns Visit frequency classification
   */
  const determineVisitFrequency = (behaviors: UserBehavior[]): ('new' | 'returning' | 'frequent')[] => {
    const uniqueDays = new Set(
      behaviors.map(b => b.timestamp.toDate().toDateString())
    );

    const dayCount = uniqueDays.size;
    const totalBehaviors = behaviors.length;

    if (dayCount === 1) {
      return ['new'];
    } else if (dayCount <= 5 || totalBehaviors <= 10) {
      return ['returning'];
    } else {
      return ['frequent'];
    }
  };

  /**
   * Save current behavior data to Firestore
   */
  const flushBehaviorData = async () => {
    if (!firestoreDb || !currentUser.value || behaviorEvents.value.length === 0) {
      return;
    }

    try {
      const behaviorData: Omit<UserBehavior, 'userId'> = {
        sessionId: currentSession.value,
        timestamp: Timestamp.now(),
        events: [...behaviorEvents.value],
        interests: userInterests.value,
        engagementScore: engagementScore.value,
        deviceInfo: deviceInfo.value!
      };

      await addDoc(collection(firestoreDb, 'user_behaviors'), {
        userId: currentUser.value.uid,
        ...behaviorData
      });

      // Clear local events after successful save
      behaviorEvents.value = [];
    } catch (err) {
      console.error('Error saving behavior data:', err);
      // Keep events in case we can retry later
    }
  };

  /**
   * Get behavior-based audience size estimation
   * @param criteria Behavioral criteria
   * @returns Estimated audience size
   */
  const estimateBehavioralAudienceSize = async (criteria: BehavioralCriteria): Promise<number> => {
    if (!firestoreDb) {
      return 0;
    }

    try {
      // This is a simplified estimation - in practice, you'd query actual behavior data
      let baseSize = 100000; // Base population

      // Apply filters based on criteria
      if (criteria.interests && criteria.interests.length > 0) {
        // More specific interests = smaller audience
        const specificity = Math.min(1, criteria.interests.length / 10);
        baseSize *= (1 - specificity * 0.7);
      }

      if (criteria.deviceTypes && criteria.deviceTypes.length < 3) {
        const deviceShares = { mobile: 0.6, desktop: 0.3, tablet: 0.1 };
        const selectedShare = criteria.deviceTypes.reduce((sum, device) => 
          sum + (deviceShares[device] || 0.1), 0);
        baseSize *= selectedShare;
      }

      if (criteria.engagementLevel && criteria.engagementLevel.length < 3) {
        const engagementShares = { low: 0.4, medium: 0.4, high: 0.2 };
        const selectedShare = criteria.engagementLevel.reduce((sum, level) => 
          sum + (engagementShares[level] || 0.33), 0);
        baseSize *= selectedShare;
      }

      return Math.round(Math.max(1000, baseSize)); // Minimum 1k audience
    } catch (err) {
      console.error('Error estimating behavioral audience size:', err);
      return 0;
    }
  };

  // Computed properties
  const sessionDuration = computed(() => {
    return Math.round((Date.now() - sessionStartTime.value.getTime()) / (1000 * 60));
  });

  const uniquePages = computed(() => {
    return [...new Set(pageViews.value)];
  });

  const totalInteractions = computed(() => {
    return interactions.value.length;
  });

  // Lifecycle
  onMounted(() => {
    initializeSession();
  });

  onUnmounted(() => {
    flushBehaviorData();
    
    if (typeof document !== 'undefined') {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    }
    
    if (typeof window !== 'undefined') {
      window.removeEventListener('scroll', trackScrollDepth);
      window.removeEventListener('beforeunload', flushBehaviorData);
    }
  });

  return {
    // State
    isLoading,
    error,
    currentSession,
    behaviorEvents,
    userInterests,
    engagementScore,
    deviceInfo,

    // Tracking methods
    trackPageView,
    trackClick,
    trackInteraction,
    trackConversion,
    trackDownload,

    // Analysis methods
    analyzeInterests,
    createBehavioralCriteria,
    estimateBehavioralAudienceSize,

    // Data management
    flushBehaviorData,
    initializeSession,

    // Computed properties
    sessionDuration,
    uniquePages,
    totalInteractions,
    scrollDepth
  };
}