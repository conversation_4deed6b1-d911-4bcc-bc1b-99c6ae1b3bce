<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useCurrentUser } from '~/composables/useCurrentUser'
import BusinessCardLinker from '~/components/user/BusinessCardLinker.vue'
import CompanyManager from '~/components/user/CompanyManager.vue'
import MobileSimplifiedProfileView from '~/components/mobile/SimplifiedProfileView.vue'

definePageMeta({
  layout: "covalonic",
  middleware: "auth"
});

// Composables
const { currentUser } = useCurrentUser()

// Reactive data
const loading = ref(true)

// Mobile detection
const isMobile = ref(false)

// Check if user is on mobile device
onMounted(() => {
  isMobile.value = window.innerWidth < 768
  
  // Listen for resize events
  const handleResize = () => {
    isMobile.value = window.innerWidth < 768
  }
  
  window.addEventListener('resize', handleResize)
  
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
  })

  // Set loading to false after component mounts
  loading.value = false
})

const getGreeting = () => {
  const hour = new Date().getHours()
  if (hour < 12) return 'Good morning!'
  if (hour < 18) return 'Good afternoon!'
  return 'Good evening!'
}

// Get user display name
const displayName = computed(() => {
  if (!currentUser.value) return 'User'
  
  return currentUser.value.first_name || 
         currentUser.value.firstName || 
         currentUser.value.displayName || 
         currentUser.value.name ||
         'User'
})

// Get user email
const userEmail = computed(() => {
  return currentUser.value?.email || 'No email set'
})

</script>

<template>
  <div class="min-h-screen font-sans relative overflow-hidden">
    <!-- Dark gradient background -->
    <div class="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900"></div>

    <!-- Decorative elements -->
    <div class="absolute top-20 right-20 w-96 h-96 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-3xl"></div>
    <div class="absolute bottom-40 left-20 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl"></div>
    <div class="absolute top-1/2 right-1/3 w-64 h-64 bg-gradient-to-br from-pink-400/15 to-purple-400/15 rounded-full blur-2xl"></div>

    <!-- Mobile Simplified View -->
    <div v-if="isMobile" class="relative z-10 px-4 py-6 max-w-lg mx-auto">
      <MobileSimplifiedProfileView />
    </div>

    <!-- Desktop Full View -->
    <div v-else class="relative z-10 px-4 md:px-6 py-4 md:py-8">
      <div class="max-w-7xl mx-auto">
        <!-- Header Section -->
        <div class="mb-8">
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div class="mb-4 sm:mb-0">
              <h1 class="text-4xl font-bold text-white mb-2">Profile Dashboard</h1>
              <p class="text-lg text-gray-300">Manage your profile and personal settings</p>
            </div>
            
            <!-- Quick Navigation - Desktop -->
            <div class="hidden sm:flex space-x-3">
              <NuxtLink
                to="/c/calendar"
                class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-600/20 to-purple-700/20 hover:from-purple-600/30 hover:to-purple-700/30 text-purple-400 hover:text-purple-300 font-medium rounded-lg transition-all duration-200 border border-purple-400/20"
              >
                <Icon name="mdi:calendar" class="mr-2 h-4 w-4" />
                Calendar
              </NuxtLink>
              
              <NuxtLink
                to="/c/businesscards/create"
                class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-medium rounded-lg transition-all duration-200"
              >
                <Icon name="mdi:card-plus" class="mr-2 h-4 w-4" />
                Create Business Card
              </NuxtLink>
            </div>
          </div>

          <!-- Welcome Message with Time-based Greeting -->
          <div class="mt-6 p-4 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20">
            <div class="flex items-center">
              <Icon name="mdi:account-circle" class="w-8 h-8 text-purple-400 mr-3" />
              <div>
                <p class="text-white font-medium">{{ getGreeting() }} {{ displayName }}</p>
                <p class="text-gray-300 text-sm">Welcome to your personal dashboard</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Profile Overview Cards -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <!-- Profile Information Card -->
          <div class="bg-white/10 backdrop-blur-md rounded-lg border border-white/20 p-4 md:p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-xl font-semibold text-white">Profile Information</h3>
              <NuxtLink
                to="/c/profile/edit"
                class="text-sm text-purple-400 hover:text-purple-300 font-medium"
              >
                Edit Profile
              </NuxtLink>
            </div>

            <div class="space-y-4">
              <div class="flex items-center space-x-4">
                <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
                  {{ displayName.charAt(0).toUpperCase() }}
                </div>
                <div>
                  <p class="text-white font-medium text-lg">{{ displayName }}</p>
                  <p class="text-gray-300">{{ userEmail }}</p>
                </div>
              </div>
              
              <div class="pt-4 border-t border-white/10">
                <div class="grid grid-cols-2 gap-4 text-center">
                  <div class="p-3 bg-purple-500/20 rounded-lg">
                    <p class="text-purple-200 text-sm">Member Since</p>
                    <p class="text-white font-semibold">{{ new Date().getFullYear() }}</p>
                  </div>
                  <div class="p-3 bg-blue-500/20 rounded-lg">
                    <p class="text-blue-200 text-sm">Account Type</p>
                    <p class="text-white font-semibold">{{ currentUser?.role || 'User' }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Business Card Linker -->
          <BusinessCardLinker />
        </div>

        <!-- Company Management and Business Tools -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <!-- Company Manager -->
          <CompanyManager />

          <!-- Business Tools Card -->
          <div class="bg-white/10 backdrop-blur-md rounded-lg border border-white/20 p-4 md:p-6">
            <div class="flex items-center mb-4">
              <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center mr-3">
                <Icon name="mdi:briefcase" class="w-5 h-5 text-white" />
              </div>
              <h3 class="text-xl font-semibold text-white">Business Tools</h3>
            </div>

            <div class="space-y-3">
              <!-- Invoices & Quotes -->
              <NuxtLink
                to="/c/invoices"
                class="group flex items-center justify-between p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-all duration-200 border border-white/5 hover:border-white/20"
              >
                <div class="flex items-center">
                  <Icon name="mdi:receipt" class="w-5 h-5 text-green-400 mr-3" />
                  <div>
                    <p class="text-white font-medium">Invoices & Quotes</p>
                    <p class="text-gray-400 text-sm">Manage billing documents</p>
                  </div>
                </div>
                <Icon name="mdi:chevron-right" class="w-4 h-4 text-gray-400 group-hover:text-white" />
              </NuxtLink>

              <!-- CRM -->
              <NuxtLink
                to="/c/crm"
                class="group flex items-center justify-between p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-all duration-200 border border-white/5 hover:border-white/20"
              >
                <div class="flex items-center">
                  <Icon name="mdi:account-multiple" class="w-5 h-5 text-blue-400 mr-3" />
                  <div>
                    <p class="text-white font-medium">Customer Management</p>
                    <p class="text-gray-400 text-sm">Manage clients and contacts</p>
                  </div>
                </div>
                <Icon name="mdi:chevron-right" class="w-4 h-4 text-gray-400 group-hover:text-white" />
              </NuxtLink>

              <!-- Business Cards -->
              <NuxtLink
                to="/c/crm/business-cards"
                class="group flex items-center justify-between p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-all duration-200 border border-white/5 hover:border-white/20"
              >
                <div class="flex items-center">
                  <Icon name="mdi:card-account-details" class="w-5 h-5 text-purple-400 mr-3" />
                  <div>
                    <p class="text-white font-medium">Digital Business Cards</p>
                    <p class="text-gray-400 text-sm">Create and share cards</p>
                  </div>
                </div>
                <Icon name="mdi:chevron-right" class="w-4 h-4 text-gray-400 group-hover:text-white" />
              </NuxtLink>
            </div>
          </div>
        </div>

        <!-- Quick Actions Grid -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Calendar Access -->
          <NuxtLink
            to="/c/calendar"
            class="group bg-white/10 backdrop-blur-md rounded-lg border border-white/20 p-6 hover:border-white/30 transition-all duration-300 hover:shadow-lg"
          >
            <div class="flex items-center justify-between mb-4">
              <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                <Icon name="mdi:calendar" class="w-6 h-6 text-white" />
              </div>
              <Icon name="mdi:chevron-right" class="w-5 h-5 text-white/60 group-hover:text-white transition-colors" />
            </div>
            <h3 class="text-white font-semibold mb-2">Calendar</h3>
            <p class="text-gray-300 text-sm">Manage your schedule and appointments</p>
          </NuxtLink>

          <!-- Profile Settings -->
          <NuxtLink
            to="/c/profile/settings"
            class="group bg-white/10 backdrop-blur-md rounded-lg border border-white/20 p-6 hover:border-white/30 transition-all duration-300 hover:shadow-lg"
          >
            <div class="flex items-center justify-between mb-4">
              <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Icon name="mdi:cog" class="w-6 h-6 text-white" />
              </div>
              <Icon name="mdi:chevron-right" class="w-5 h-5 text-white/60 group-hover:text-white transition-colors" />
            </div>
            <h3 class="text-white font-semibold mb-2">Account Settings</h3>
            <p class="text-gray-300 text-sm">Privacy, security, and preferences</p>
          </NuxtLink>

          <!-- Business Card Manager -->
          <NuxtLink
            to="/c/crm/business-cards"
            class="group bg-white/10 backdrop-blur-md rounded-lg border border-white/20 p-6 hover:border-white/30 transition-all duration-300 hover:shadow-lg"
          >
            <div class="flex items-center justify-between mb-4">
              <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                <Icon name="mdi:card-account-details" class="w-6 h-6 text-white" />
              </div>
              <Icon name="mdi:chevron-right" class="w-5 h-5 text-white/60 group-hover:text-white transition-colors" />
            </div>
            <h3 class="text-white font-semibold mb-2">Business Cards</h3>
            <p class="text-gray-300 text-sm">Create and manage your digital cards</p>
          </NuxtLink>
        </div>
      </div>
    </div>
    <!-- End Desktop Full View -->
  </div>
</template>