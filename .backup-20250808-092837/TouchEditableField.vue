<template>
  <div
    ref="fieldContainer"
    class="touch-editable-field"
    :class="[
      `field-type-${field.type}`,
      {
        'field-selected': isSelected,
        'field-editing': isEditing,
        'field-dragging': isDragging,
        'field-resizing': isResizing
      }
    ]"
    :style="fieldStyle"
    :data-field-id="field.id"
    @touchstart="handleTouchStart"
    @touchmove="handleTouchMove" 
    @touchend="handleTouchEnd"
    @touchcancel="handleTouchCancel"
  >
    <!-- Text Field -->
    <div
      v-if="field.type === 'text'"
      class="text-field-content"
      :style="textFieldStyle"
    >
      <input
        v-if="isEditing"
        ref="textInput"
        v-model="editingContent"
        class="text-field-input"
        :style="inputStyle"
        @blur="finishTextEditing"
        @keydown.enter="finishTextEditing"
        @keydown.escape="cancelTextEditing"
      />
      <span
        v-else
        class="text-field-display"
        :style="textStyle"
        v-html="formattedContent"
      />
    </div>
    
    <!-- Image Field -->
    <div
      v-else-if="field.type === 'image'"
      class="image-field-content"
    >
      <img
        v-if="field.content"
        :src="field.content"
        :alt="field.alt || 'Business card image'"
        class="field-image"
        :style="imageStyle"
      />
      <div
        v-else
        class="image-placeholder"
        :style="placeholderStyle"
      >
        <Icon name="mdi:image" class="w-8 h-8 text-gray-400" />
        <span class="text-sm text-gray-500">Tap to add image</span>
      </div>
    </div>
    
    <!-- QR Code Field -->
    <div
      v-else-if="field.type === 'qr'"
      class="qr-field-content"
    >
      <div
        class="qr-code-container"
        :style="qrStyle"
      >
        <!-- QR code would be generated here -->
        <div class="qr-placeholder">
          <Icon name="mdi:qrcode" class="w-full h-full text-gray-600" />
        </div>
      </div>
    </div>
    
    <!-- Touch Handles (only visible when selected) -->
    <div
      v-if="isSelected && !isEditing"
      class="touch-handles"
    >
      <!-- Corner handles for resizing -->
      <div
        v-for="handle in resizeHandles"
        :key="handle.position"
        class="resize-handle"
        :class="`handle-${handle.position}`"
        :style="handle.style"
        @touchstart="startResize(handle, $event)"
      />
      
      <!-- Move handle -->
      <div
        class="move-handle"
        @touchstart="startMove"
      >
        <Icon name="mdi:drag" class="w-4 h-4 text-blue-600" />
      </div>
    </div>
    
    <!-- Field type indicator -->
    <div
      v-if="isSelected && !isEditing"
      class="field-type-indicator"
    >
      <Icon 
        :name="fieldTypeIcon" 
        class="w-3 h-3 text-blue-600" 
      />
    </div>
    
    <!-- Touch feedback overlay -->
    <div
      v-if="showTouchFeedback"
      class="touch-feedback-overlay"
      :class="touchFeedbackClass"
    />
  </div>
</template>

<script setup lang="ts">
import type { CardField } from '~/types/business-cards'

interface Props {
  field: CardField
  isSelected: boolean
  scale: number
  readonly?: boolean
}

interface Emits {
  (e: 'select', fieldId: string): void
  (e: 'update', fieldId: string, updates: Partial<CardField>): void
  (e: 'drag-start', fieldId: string, startPos: { x: number; y: number }): void
  (e: 'drag', fieldId: string, delta: { x: number; y: number }): void
  (e: 'drag-end', fieldId: string): void
  (e: 'resize-start', fieldId: string, handle: string): void
  (e: 'resize', fieldId: string, delta: { width: number; height: number }): void
  (e: 'resize-end', fieldId: string): void
  (e: 'edit-start', fieldId: string): void
  (e: 'edit-end', fieldId: string): void
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
})

const emit = defineEmits<Emits>()

// Template refs
const fieldContainer = ref<HTMLElement>()
const textInput = ref<HTMLInputElement>()

// Local state
const isEditing = ref(false)
const isDragging = ref(false)
const isResizing = ref(false)
const editingContent = ref('')
const showTouchFeedback = ref(false)
const touchFeedbackClass = ref('')

// Touch tracking
let touchStartTime = 0
let touchStartPos = { x: 0, y: 0 }
let lastTouchPos = { x: 0, y: 0 }
let initialFieldPos = { x: 0, y: 0 }
let initialFieldSize = { width: 0, height: 0 }
let longPressTimer: NodeJS.Timeout | null = null
let currentHandle = ''

// Haptic feedback
const { 
  triggerHaptic, 
  lightHaptic, 
  mediumHaptic, 
  selectionHaptic,
  longPressHaptic 
} = useHaptics()

// Computed properties
const fieldStyle = computed(() => ({
  position: 'absolute',
  left: `${props.field.x}px`,
  top: `${props.field.y}px`,
  width: `${props.field.width}px`,
  height: `${props.field.height}px`,
  transform: `scale(${props.scale})`,
  transformOrigin: 'top left',
  zIndex: props.isSelected ? 10 : 1
}))

const textFieldStyle = computed(() => ({
  width: '100%',
  height: '100%',
  display: 'flex',
  alignItems: props.field.textAlign === 'center' ? 'center' : 
            props.field.textAlign === 'right' ? 'flex-end' : 'flex-start',
  justifyContent: props.field.textAlign === 'center' ? 'center' :
                 props.field.textAlign === 'right' ? 'flex-end' : 'flex-start',
  backgroundColor: props.field.backgroundColor || 'transparent',
  padding: '4px',
  borderRadius: '4px'
}))

const textStyle = computed(() => ({
  fontSize: `${props.field.fontSize || 16}px`,
  fontFamily: props.field.fontFamily || 'Arial',
  fontWeight: props.field.fontWeight || 'normal',
  fontStyle: props.field.fontStyle || 'normal',
  color: props.field.color || '#000000',
  textAlign: props.field.textAlign || 'left',
  lineHeight: '1.2',
  wordWrap: 'break-word',
  width: '100%'
}))

const inputStyle = computed(() => ({
  ...textStyle.value,
  border: 'none',
  outline: 'none',
  background: 'transparent',
  resize: 'none'
}))

const imageStyle = computed(() => ({
  width: '100%',
  height: '100%',
  objectFit: 'cover',
  borderRadius: '4px'
}))

const placeholderStyle = computed(() => ({
  width: '100%',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: '#f5f5f5',
  border: '2px dashed #d1d5db',
  borderRadius: '4px'
}))

const qrStyle = computed(() => ({
  width: '100%',
  height: '100%',
  backgroundColor: 'white',
  borderRadius: '4px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center'
}))

const fieldTypeIcon = computed(() => {
  switch (props.field.type) {
    case 'text': return 'mdi:text-box'
    case 'image': return 'mdi:image'
    case 'qr': return 'mdi:qrcode'
    default: return 'mdi:help'
  }
})

const formattedContent = computed(() => {
  if (props.field.type === 'text') {
    return props.field.content?.replace(/\n/g, '<br>') || ''
  }
  return props.field.content || ''
})

const resizeHandles = computed(() => [
  { 
    position: 'nw', 
    style: { 
      top: '-6px', 
      left: '-6px',
      cursor: 'nw-resize'
    } 
  },
  { 
    position: 'ne', 
    style: { 
      top: '-6px', 
      right: '-6px',
      cursor: 'ne-resize'
    } 
  },
  { 
    position: 'sw', 
    style: { 
      bottom: '-6px', 
      left: '-6px',
      cursor: 'sw-resize'
    } 
  },
  { 
    position: 'se', 
    style: { 
      bottom: '-6px', 
      right: '-6px',
      cursor: 'se-resize'
    } 
  },
  { 
    position: 'n', 
    style: { 
      top: '-6px', 
      left: '50%', 
      transform: 'translateX(-50%)',
      cursor: 'n-resize'
    } 
  },
  { 
    position: 'e', 
    style: { 
      right: '-6px', 
      top: '50%', 
      transform: 'translateY(-50%)',
      cursor: 'e-resize'
    } 
  },
  { 
    position: 's', 
    style: { 
      bottom: '-6px', 
      left: '50%', 
      transform: 'translateX(-50%)',
      cursor: 's-resize'
    } 
  },
  { 
    position: 'w', 
    style: { 
      left: '-6px', 
      top: '50%', 
      transform: 'translateY(-50%)',
      cursor: 'w-resize'
    } 
  }
])

// Touch event handlers
const handleTouchStart = (event: TouchEvent) => {
  if (props.readonly) return
  
  event.stopPropagation()
  event.preventDefault()
  
  const touch = event.touches[0]
  touchStartTime = Date.now()
  touchStartPos = { x: touch.clientX, y: touch.clientY }
  lastTouchPos = touchStartPos
  initialFieldPos = { x: props.field.x, y: props.field.y }
  
  // Select field
  emit('select', props.field.id)
  selectionHaptic()
  
  // Show touch feedback
  showTouchFeedback.value = true
  touchFeedbackClass.value = 'touch-start'
  
  // Start long press timer for editing
  if (props.field.type === 'text') {
    longPressTimer = setTimeout(() => {
      startTextEditing()
      longPressHaptic()
    }, 700)
  }
}

const handleTouchMove = (event: TouchEvent) => {
  if (props.readonly || !props.isSelected) return
  
  event.preventDefault()
  
  const touch = event.touches[0]
  const currentPos = { x: touch.clientX, y: touch.clientY }
  const deltaX = currentPos.x - touchStartPos.x
  const deltaY = currentPos.y - touchStartPos.y
  const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)
  
  // Cancel long press if moved too much
  if (distance > 10 && longPressTimer) {
    clearTimeout(longPressTimer)
    longPressTimer = null
  }
  
  // Start dragging if moved enough
  if (distance > 15 && !isDragging.value && !isResizing.value) {
    startDragging()
  }
  
  // Continue dragging
  if (isDragging.value) {
    const moveDelta = {
      x: currentPos.x - lastTouchPos.x,
      y: currentPos.y - lastTouchPos.y
    }
    emit('drag', props.field.id, moveDelta)
  }
  
  lastTouchPos = currentPos
  
  // Update touch feedback
  touchFeedbackClass.value = isDragging.value ? 'touch-drag' : 'touch-move'
}

const handleTouchEnd = (event: TouchEvent) => {
  if (props.readonly) return
  
  event.preventDefault()
  
  const touchDuration = Date.now() - touchStartTime
  
  // Clear timers
  if (longPressTimer) {
    clearTimeout(longPressTimer)
    longPressTimer = null
  }
  
  // Handle tap (short touch without movement)
  if (touchDuration < 300 && !isDragging.value && !isResizing.value) {
    handleTap()
  }
  
  // End dragging
  if (isDragging.value) {
    endDragging()
  }
  
  // End resizing
  if (isResizing.value) {
    endResizing()
  }
  
  // Hide touch feedback
  showTouchFeedback.value = false
  touchFeedbackClass.value = ''
}

const handleTouchCancel = () => {
  if (longPressTimer) {
    clearTimeout(longPressTimer)
    longPressTimer = null
  }
  
  isDragging.value = false
  isResizing.value = false
  showTouchFeedback.value = false
  touchFeedbackClass.value = ''
}

const handleTap = () => {
  // Double tap to edit text fields
  if (props.field.type === 'text') {
    // Check for double tap
    const now = Date.now()
    if (now - (handleTap as any).lastTap < 500) {
      startTextEditing()
      mediumHaptic()
    }
    (handleTap as any).lastTap = now
  }
  
  lightHaptic()
}

// Dragging methods
const startDragging = () => {
  if (props.readonly) return
  
  isDragging.value = true
  emit('drag-start', props.field.id, touchStartPos)
  mediumHaptic()
}

const endDragging = () => {
  isDragging.value = false
  emit('drag-end', props.field.id)
  lightHaptic()
}

const startMove = (event: TouchEvent) => {
  event.stopPropagation()
  startDragging()
}

// Resizing methods
const startResize = (handle: any, event: TouchEvent) => {
  if (props.readonly) return
  
  event.stopPropagation()
  event.preventDefault()
  
  isResizing.value = true
  currentHandle = handle.position
  initialFieldSize = { width: props.field.width, height: props.field.height }
  
  emit('resize-start', props.field.id, handle.position)
  mediumHaptic()
}

const endResizing = () => {
  isResizing.value = false
  currentHandle = ''
  emit('resize-end', props.field.id)
  lightHaptic()
}

// Text editing methods
const startTextEditing = () => {
  if (props.readonly || props.field.type !== 'text') return
  
  isEditing.value = true
  editingContent.value = props.field.content || ''
  emit('edit-start', props.field.id)
  
  nextTick(() => {
    if (textInput.value) {
      textInput.value.focus()
      textInput.value.select()
    }
  })
}

const finishTextEditing = () => {
  if (!isEditing.value) return
  
  isEditing.value = false
  
  if (editingContent.value !== props.field.content) {
    emit('update', props.field.id, { content: editingContent.value })
  }
  
  emit('edit-end', props.field.id)
  lightHaptic()
}

const cancelTextEditing = () => {
  isEditing.value = false
  editingContent.value = props.field.content || ''
  emit('edit-end', props.field.id)
}

// Lifecycle
onUnmounted(() => {
  if (longPressTimer) {
    clearTimeout(longPressTimer)
  }
})

// Expose methods
defineExpose({
  startTextEditing,
  finishTextEditing,
  cancelTextEditing
})
</script>

<style scoped>
.touch-editable-field {
  @apply relative cursor-pointer select-none;
  touch-action: none;
  -webkit-user-select: none;
  user-select: none;
}

.field-selected {
  @apply ring-2 ring-blue-500 ring-opacity-50;
}

.field-editing {
  @apply ring-2 ring-green-500 ring-opacity-50;
}

.field-dragging {
  @apply shadow-lg z-20;
  transform: scale(1.05) !important;
}

.field-resizing {
  @apply ring-2 ring-purple-500 ring-opacity-50;
}

/* Text field styles */
.text-field-content {
  @apply rounded;
}

.text-field-input {
  @apply w-full h-full resize-none;
  min-height: inherit;
}

.text-field-display {
  @apply w-full;
}

/* Image field styles */
.image-field-content {
  @apply w-full h-full;
}

.field-image {
  @apply w-full h-full object-cover rounded;
}

.image-placeholder {
  @apply w-full h-full flex flex-col items-center justify-center;
  @apply bg-gray-50 border-2 border-dashed border-gray-300 rounded;
}

/* QR field styles */
.qr-field-content {
  @apply w-full h-full;
}

.qr-code-container {
  @apply w-full h-full bg-white rounded shadow-sm;
}

.qr-placeholder {
  @apply w-full h-full flex items-center justify-center text-gray-400;
}

/* Touch handles */
.touch-handles {
  @apply absolute inset-0 pointer-events-none;
}

.resize-handle {
  @apply absolute w-6 h-6 bg-blue-500 border-2 border-white rounded-full;
  @apply pointer-events-auto touch-manipulation;
  @apply shadow-sm z-10;
}

.resize-handle:active {
  @apply bg-blue-600 scale-110;
}

.move-handle {
  @apply absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2;
  @apply w-8 h-8 bg-blue-500 border-2 border-white rounded-full;
  @apply flex items-center justify-center pointer-events-auto touch-manipulation;
  @apply shadow-sm z-10;
}

.move-handle:active {
  @apply bg-blue-600 scale-110;
}

/* Field type indicator */
.field-type-indicator {
  @apply absolute -top-2 -left-2 w-6 h-6 bg-blue-500 rounded-full;
  @apply flex items-center justify-center;
  @apply border-2 border-white shadow-sm;
}

/* Touch feedback overlay */
.touch-feedback-overlay {
  @apply absolute inset-0 pointer-events-none rounded;
  @apply transition-all duration-200;
}

.touch-feedback-overlay.touch-start {
  @apply bg-blue-500 bg-opacity-10;
}

.touch-feedback-overlay.touch-move {
  @apply bg-blue-500 bg-opacity-20;
}

.touch-feedback-overlay.touch-drag {
  @apply bg-green-500 bg-opacity-20;
}

/* Field type specific styles */
.field-type-text {
  @apply border border-transparent;
}

.field-type-text.field-selected {
  @apply border-blue-300;
}

.field-type-image {
  @apply border border-transparent;
}

.field-type-image.field-selected {
  @apply border-green-300;
}

.field-type-qr {
  @apply border border-transparent;
}

.field-type-qr.field-selected {
  @apply border-purple-300;
}

/* Touch optimizations for mobile */
@media (hover: none) and (pointer: coarse) {
  .resize-handle {
    @apply w-8 h-8;
  }
  
  .move-handle {
    @apply w-10 h-10;
  }
  
  .field-type-indicator {
    @apply w-8 h-8;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .field-selected {
    @apply ring-4 ring-yellow-400;
  }
  
  .resize-handle {
    @apply bg-yellow-400 border-black border-2;
  }
  
  .move-handle {
    @apply bg-yellow-400 border-black border-2;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .touch-editable-field {
    transition: none !important;
  }
  
  .touch-feedback-overlay {
    transition: none !important;
  }
  
  .field-dragging {
    transform: none !important;
  }
}
</style>