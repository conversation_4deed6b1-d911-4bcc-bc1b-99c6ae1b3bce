import { collection, query, where, getDocs, addDoc, updateDoc, doc, serverTimestamp } from 'firebase/firestore'

export default defineEventHandler(async (event) => {
  try {
    // Get request body
    const body = await readBody(event)
    const { type, itemId, itemData, submittedBy, timestamp } = body

    // Validate required fields
    if (!type || !itemId || !submittedBy) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Missing required fields: type, itemId, submittedBy'
      })
    }

    // Get Firebase instance
    const { firestore } = useFirebaseAdmin()
    
    if (!firestore) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Firebase not initialized'
      })
    }

    // Get all admin users
    const admins = await getAdminUsers()
    
    if (admins.length === 0) {
      console.warn('No admin users found to send notifications to')
      return { success: true, message: 'No admins to notify' }
    }

    // Create notification content based on type
    const notificationContent = generateAdminNotificationContent(type, itemData, submittedBy)
    
    // Send notifications to all admins
    const notificationPromises = admins.map(admin => 
      sendNotificationToAdmin(admin.uid, {
        type: 'admin_notification',
        title: notificationContent.title,
        body: notificationContent.body,
        data: {
          notificationType: type,
          itemId,
          submittedBy,
          timestamp: timestamp || new Date().toISOString(),
          actionUrl: `/c/admin/${type.includes('business_card') ? 'business-cards' : 'moderation'}`
        }
      })
    )

    await Promise.all(notificationPromises)

    return {
      success: true,
      message: `Notifications sent to ${admins.length} admin(s)`,
      adminCount: admins.length
    }

  } catch (error: any) {
    console.error('Error sending admin notifications:', error)
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || 'Failed to send admin notifications'
    })
  }
})

/**
 * Get all users with admin role
 */
async function getAdminUsers() {
  const { firestore } = useFirebaseAdmin()
  
  try {
    // Query users collection for admin users
    const usersRef = collection(firestore, 'users')
    const adminQuery = query(usersRef, where('role', '==', 'admin'))
    const querySnapshot = await getDocs(adminQuery)
    
    const admins: { uid: string; email: string; displayName?: string }[] = []
    querySnapshot.forEach((doc) => {
      const userData = doc.data()
      admins.push({
        uid: doc.id,
        email: userData.email,
        displayName: userData.displayName
      })
    })
    
    return admins
  } catch (error) {
    console.error('Error fetching admin users:', error)
    return []
  }
}

/**
 * Send notification to a specific admin user
 */
async function sendNotificationToAdmin(adminId: string, notification: {
  type: string
  title: string
  body: string
  data: Record<string, any>
}) {
  const { firestore } = useFirebaseAdmin()
  
  try {
    // Add notification to Firestore
    const notificationDoc = {
      userId: adminId,
      type: notification.type,
      title: notification.title,
      body: notification.body,
      data: notification.data,
      read: false,
      createdAt: serverTimestamp(),
      deliveryStatus: 'pending'
    }

    const docRef = await addDoc(collection(firestore, 'notifications'), notificationDoc)
    
    // Try to send push notification (if FCM is configured)
    try {
      await sendPushNotificationToAdmin(adminId, {
        title: notification.title,
        body: notification.body,
        data: {
          notificationId: docRef.id,
          ...notification.data
        }
      })
      
      // Update delivery status to delivered
      await updateDoc(doc(firestore, 'notifications', docRef.id), {
        deliveryStatus: 'delivered',
        deliveredAt: serverTimestamp()
      })
    } catch (pushError) {
      console.error('Failed to send push notification to admin:', pushError)
      
      // Update delivery status to failed but don't throw - in-app notification was saved
      await updateDoc(doc(firestore, 'notifications', docRef.id), {
        deliveryStatus: 'failed'
      })
    }

    return { success: true, notificationId: docRef.id }
  } catch (error) {
    console.error('Error sending notification to admin:', error)
    throw error
  }
}

/**
 * Generate notification content based on admin notification type
 */
function generateAdminNotificationContent(type: string, itemData: any, submittedBy: string) {
  const submitterName = itemData?.name || itemData?.first_name || 'Unknown User'
  
  switch (type) {
    case 'new_business_card_pending':
      return {
        title: '📋 New Business Card Awaiting Review',
        body: `${submitterName} has submitted a business card for approval. Review required.`
      }
    case 'new_item_pending':
      return {
        title: '📦 New Item Awaiting Review', 
        body: `New item submitted by ${submitterName} requires approval before going live.`
      }
    default:
      return {
        title: '⚠️ Admin Action Required',
        body: `New content from ${submitterName} requires review and approval.`
      }
  }
}

/**
 * Send push notification to admin (placeholder - implement based on your FCM setup)
 */
async function sendPushNotificationToAdmin(adminId: string, payload: {
  title: string
  body: string
  data: Record<string, string>
}) {
  // This would integrate with your FCM setup
  // For now, just log that we would send a push notification
  console.log(`Would send push notification to admin ${adminId}:`, {
    title: payload.title,
    body: payload.body
  })
  
  // If you have FCM configured, implement actual push notification sending here
  // Example:
  // const message = {
  //   notification: {
  //     title: payload.title,
  //     body: payload.body
  //   },
  //   data: payload.data,
  //   token: await getAdminFCMToken(adminId)
  // }
  // await admin.messaging().send(message)
}