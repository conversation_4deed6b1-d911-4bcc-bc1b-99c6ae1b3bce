/**
 * Native File System Composable
 * Provides native file system access for import/export operations
 */

import { ref, computed, readonly } from 'vue'
import { Filesystem, Directory, Encoding } from '@capacitor/filesystem'
import { Share } from '@capacitor/share'
import { Capacitor } from '@capacitor/core'
import { Haptics, ImpactStyle } from '@capacitor/haptics'

export interface FileSystemOptions {
  directory?: Directory
  encoding?: Encoding
  recursive?: boolean
}

export interface FileInfo {
  name: string
  path: string
  size: number
  type: string
  mtime: number
  uri: string
}

export interface ExportOptions {
  format: 'json' | 'csv' | 'vcard' | 'pdf'
  includeImages?: boolean
  filename?: string
  directory?: Directory
}

export interface ImportOptions {
  allowedTypes?: string[]
  maxFileSize?: number
  parseContent?: boolean
}

export interface ImportResult {
  success: boolean
  fileName: string
  fileSize: number
  content?: any
  error?: string
}

export interface ExportResult {
  success: boolean
  filePath: string
  fileName: string
  fileSize?: number
  error?: string
}

export interface DirectoryContent {
  files: FileInfo[]
  directories: string[]
  totalSize: number
  fileCount: number
}

export const useNativeFileSystem = () => {
  // State
  const isInitialized = ref(false)
  const error = ref<string | null>(null)
  const isProcessing = ref(false)
  const lastOperation = ref<'import' | 'export' | 'delete' | 'read' | null>(null)
  
  // File operation results
  const lastImport = ref<ImportResult | null>(null)
  const lastExport = ref<ExportResult | null>(null)

  /**
   * Check if native file system is available
   */
  const isNativeAvailable = computed(() => {
    return Capacitor.isNativePlatform() && Capacitor.isPluginAvailable('Filesystem')
  })

  /**
   * Initialize file system access
   */
  const initialize = async (): Promise<void> => {
    try {
      error.value = null

      if (!isNativeAvailable.value) {
        throw new Error('Native file system not available on this platform')
      }

      // Test file system access
      await testFileSystemAccess()
      
      isInitialized.value = true

    } catch (err) {
      console.error('File system initialization failed:', err)
      error.value = err instanceof Error ? err.message : 'File system initialization failed'
      throw err
    }
  }

  /**
   * Test file system access
   */
  const testFileSystemAccess = async (): Promise<void> => {
    try {
      // Try to write and read a test file
      const testPath = 'test/access-test.txt'
      const testContent = 'File system access test'
      
      await Filesystem.writeFile({
        path: testPath,
        data: testContent,
        directory: Directory.Cache,
        encoding: Encoding.UTF8
      })

      const readResult = await Filesystem.readFile({
        path: testPath,
        directory: Directory.Cache,
        encoding: Encoding.UTF8
      })

      if (readResult.data !== testContent) {
        throw new Error('File system test failed - content mismatch')
      }

      // Clean up test file
      await Filesystem.deleteFile({
        path: testPath,
        directory: Directory.Cache
      })

    } catch (err) {
      console.error('File system access test failed:', err)
      throw err
    }
  }

  /**
   * Export business cards data
   */
  const exportBusinessCards = async (
    businessCards: any[],
    options: ExportOptions
  ): Promise<ExportResult> => {
    if (!isInitialized.value) {
      await initialize()
    }

    isProcessing.value = true
    lastOperation.value = 'export'
    error.value = null

    try {
      // Provide haptic feedback
      await Haptics.impact({ style: ImpactStyle.Light })

      const { format, includeImages = false, filename, directory = Directory.Documents } = options
      
      let content: string
      let fileExtension: string
      let mimeType: string

      switch (format) {
        case 'json':
          content = JSON.stringify(businessCards, null, 2)
          fileExtension = 'json'
          mimeType = 'application/json'
          break
        case 'csv':
          content = convertToCSV(businessCards)
          fileExtension = 'csv'
          mimeType = 'text/csv'
          break
        case 'vcard':
          content = convertToVCard(businessCards)
          fileExtension = 'vcf'
          mimeType = 'text/vcard'
          break
        case 'pdf':
          content = await convertToPDF(businessCards)
          fileExtension = 'pdf'
          mimeType = 'application/pdf'
          break
        default:
          throw new Error(`Unsupported export format: ${format}`)
      }

      const fileName = filename || `business-cards-${Date.now()}.${fileExtension}`
      const filePath = `exports/${fileName}`

      // Write file
      await Filesystem.writeFile({
        path: filePath,
        data: content,
        directory,
        encoding: format === 'pdf' ? undefined : Encoding.UTF8
      })

      // Get file URI for sharing
      const fileUri = await Filesystem.getUri({
        directory,
        path: filePath
      })

      const result: ExportResult = {
        success: true,
        filePath: fileUri.uri,
        fileName,
        fileSize: new Blob([content]).size
      }

      lastExport.value = result

      // Success haptic feedback
      await Haptics.impact({ style: ImpactStyle.Light })

      return result

    } catch (err) {
      console.error('Business cards export failed:', err)
      const errorMsg = err instanceof Error ? err.message : 'Export failed'
      error.value = errorMsg

      const result: ExportResult = {
        success: false,
        filePath: '',
        fileName: '',
        error: errorMsg
      }

      lastExport.value = result

      // Error haptic feedback
      await Haptics.impact({ style: ImpactStyle.Heavy })

      return result
    } finally {
      isProcessing.value = false
    }
  }

  /**
   * Import business cards from file
   */
  const importBusinessCards = async (
    filePath?: string,
    options: ImportOptions = {}
  ): Promise<ImportResult> => {
    if (!isInitialized.value) {
      await initialize()
    }

    isProcessing.value = true
    lastOperation.value = 'import'
    error.value = null

    try {
      // Provide haptic feedback
      await Haptics.impact({ style: ImpactStyle.Light })

      let fileContent: string
      let fileName: string
      let fileSize: number

      if (filePath) {
        // Read from provided file path
        const readResult = await Filesystem.readFile({
          path: filePath,
          directory: Directory.Documents,
          encoding: Encoding.UTF8
        })
        fileContent = readResult.data
        fileName = filePath.split('/').pop() || 'unknown'
        fileSize = new Blob([fileContent]).size
      } else {
        // This would trigger a file picker in a real implementation
        throw new Error('File picker not implemented - provide filePath')
      }

      const { allowedTypes = ['json', 'csv', 'vcf'], maxFileSize = 10 * 1024 * 1024, parseContent = true } = options

      // Validate file size
      if (fileSize > maxFileSize) {
        throw new Error(`File too large: ${fileSize} bytes (max: ${maxFileSize} bytes)`)
      }

      // Determine file type
      const fileExtension = fileName.split('.').pop()?.toLowerCase()
      if (!fileExtension || !allowedTypes.includes(fileExtension)) {
        throw new Error(`Unsupported file type: ${fileExtension}`)
      }

      let parsedContent: any = null

      if (parseContent) {
        switch (fileExtension) {
          case 'json':
            parsedContent = JSON.parse(fileContent)
            break
          case 'csv':
            parsedContent = parseCSV(fileContent)
            break
          case 'vcf':
            parsedContent = parseVCard(fileContent)
            break
          default:
            throw new Error(`Cannot parse file type: ${fileExtension}`)
        }
      }

      const result: ImportResult = {
        success: true,
        fileName,
        fileSize,
        content: parsedContent
      }

      lastImport.value = result

      // Success haptic feedback
      await Haptics.impact({ style: ImpactStyle.Light })

      return result

    } catch (err) {
      console.error('Business cards import failed:', err)
      const errorMsg = err instanceof Error ? err.message : 'Import failed'
      error.value = errorMsg

      const result: ImportResult = {
        success: false,
        fileName: '',
        fileSize: 0,
        error: errorMsg
      }

      lastImport.value = result

      // Error haptic feedback
      await Haptics.impact({ style: ImpactStyle.Heavy })

      return result
    } finally {
      isProcessing.value = false
    }
  }

  /**
   * List files in directory
   */
  const listFiles = async (
    directory: Directory = Directory.Documents,
    path: string = ''
  ): Promise<DirectoryContent> => {
    try {
      const result = await Filesystem.readdir({
        path,
        directory
      })

      const files: FileInfo[] = []
      const directories: string[] = []
      let totalSize = 0

      for (const item of result.files) {
        try {
          const itemPath = path ? `${path}/${item.name}` : item.name
          const stat = await Filesystem.stat({
            path: itemPath,
            directory
          })

          if (stat.type === 'file') {
            const fileInfo: FileInfo = {
              name: item.name,
              path: itemPath,
              size: stat.size,
              type: stat.type,
              mtime: stat.mtime,
              uri: stat.uri
            }
            files.push(fileInfo)
            totalSize += stat.size
          } else {
            directories.push(item.name)
          }
        } catch (err) {
          console.warn(`Failed to get info for ${item.name}:`, err)
        }
      }

      return {
        files,
        directories,
        totalSize,
        fileCount: files.length
      }

    } catch (err) {
      console.error('Failed to list files:', err)
      throw err
    }
  }

  /**
   * Delete file
   */
  const deleteFile = async (
    path: string,
    directory: Directory = Directory.Documents
  ): Promise<boolean> => {
    try {
      lastOperation.value = 'delete'
      
      await Filesystem.deleteFile({
        path,
        directory
      })

      // Success haptic feedback
      await Haptics.impact({ style: ImpactStyle.Light })

      return true
    } catch (err) {
      console.error('Failed to delete file:', err)
      error.value = err instanceof Error ? err.message : 'File deletion failed'
      
      // Error haptic feedback
      await Haptics.impact({ style: ImpactStyle.Heavy })
      
      return false
    }
  }

  /**
   * Create directory
   */
  const createDirectory = async (
    path: string,
    directory: Directory = Directory.Documents,
    recursive: boolean = true
  ): Promise<boolean> => {
    try {
      await Filesystem.mkdir({
        path,
        directory,
        recursive
      })

      return true
    } catch (err) {
      console.error('Failed to create directory:', err)
      error.value = err instanceof Error ? err.message : 'Directory creation failed'
      return false
    }
  }

  /**
   * Share exported file
   */
  const shareExportedFile = async (filePath: string, title?: string): Promise<boolean> => {
    try {
      await Share.share({
        title: title || 'Exported Business Cards',
        url: filePath,
        dialogTitle: 'Share Export'
      })

      return true
    } catch (err) {
      console.error('Failed to share file:', err)
      error.value = err instanceof Error ? err.message : 'File sharing failed'
      return false
    }
  }

  /**
   * Convert business cards to CSV format
   */
  const convertToCSV = (businessCards: any[]): string => {
    if (businessCards.length === 0) return ''

    const headers = Object.keys(businessCards[0])
    const csvHeaders = headers.join(',')
    
    const csvRows = businessCards.map(card => 
      headers.map(header => {
        const value = card[header]
        // Escape commas and quotes in CSV
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`
        }
        return value || ''
      }).join(',')
    )

    return [csvHeaders, ...csvRows].join('\n')
  }

  /**
   * Convert business cards to vCard format
   */
  const convertToVCard = (businessCards: any[]): string => {
    return businessCards.map(card => {
      let vCard = 'BEGIN:VCARD\nVERSION:3.0\n'
      
      if (card.name) vCard += `FN:${card.name}\n`
      if (card.company) vCard += `ORG:${card.company}\n`
      if (card.phone) vCard += `TEL:${card.phone}\n`
      if (card.email) vCard += `EMAIL:${card.email}\n`
      if (card.website) vCard += `URL:${card.website}\n`
      if (card.address) vCard += `ADR:;;${card.address}\n`
      
      vCard += 'END:VCARD'
      return vCard
    }).join('\n\n')
  }

  /**
   * Convert business cards to PDF format
   */
  const convertToPDF = async (businessCards: any[]): Promise<string> => {
    // This would use a PDF library like jsPDF
    // For now, return a placeholder
    return 'PDF generation not implemented'
  }

  /**
   * Parse CSV content
   */
  const parseCSV = (content: string): any[] => {
    const lines = content.trim().split('\n')
    if (lines.length < 2) return []

    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
    
    return lines.slice(1).map(line => {
      const values = line.split(',').map(v => v.trim().replace(/"/g, ''))
      const obj: any = {}
      
      headers.forEach((header, index) => {
        obj[header] = values[index] || ''
      })
      
      return obj
    })
  }

  /**
   * Parse vCard content
   */
  const parseVCard = (content: string): any[] => {
    const vCards = content.split('BEGIN:VCARD').filter(card => card.trim())
    
    return vCards.map(vCardContent => {
      const lines = vCardContent.split('\n').filter(line => line.trim())
      const card: any = {}
      
      lines.forEach(line => {
        if (line.startsWith('FN:')) card.name = line.substring(3)
        else if (line.startsWith('ORG:')) card.company = line.substring(4)
        else if (line.startsWith('TEL:')) card.phone = line.substring(4)
        else if (line.startsWith('EMAIL:')) card.email = line.substring(6)
        else if (line.startsWith('URL:')) card.website = line.substring(4)
        else if (line.startsWith('ADR:')) {
          const address = line.substring(4).split(';').filter(part => part).join(' ')
          card.address = address
        }
      })
      
      return card
    }).filter(card => card.name) // Only return cards with names
  }

  /**
   * Get storage usage statistics
   */
  const getStorageStats = async (): Promise<{
    totalFiles: number
    totalSize: number
    directories: string[]
  }> => {
    try {
      const documentsContent = await listFiles(Directory.Documents)
      const cacheContent = await listFiles(Directory.Cache)
      
      return {
        totalFiles: documentsContent.fileCount + cacheContent.fileCount,
        totalSize: documentsContent.totalSize + cacheContent.totalSize,
        directories: ['Documents', 'Cache']
      }
    } catch (err) {
      console.error('Failed to get storage stats:', err)
      return { totalFiles: 0, totalSize: 0, directories: [] }
    }
  }

  /**
   * Cleanup temporary files
   */
  const cleanupTempFiles = async (): Promise<number> => {
    try {
      const cacheContent = await listFiles(Directory.Cache)
      let deletedCount = 0
      
      // Delete files older than 24 hours
      const twentyFourHoursAgo = Date.now() - (24 * 60 * 60 * 1000)
      
      for (const file of cacheContent.files) {
        if (file.mtime < twentyFourHoursAgo) {
          const deleted = await deleteFile(file.path, Directory.Cache)
          if (deleted) deletedCount++
        }
      }
      
      return deletedCount
    } catch (err) {
      console.error('Failed to cleanup temp files:', err)
      return 0
    }
  }

  // Computed properties
  const isReady = computed(() => 
    isNativeAvailable.value && isInitialized.value
  )

  const hasRecentImport = computed(() => 
    lastImport.value !== null
  )

  const hasRecentExport = computed(() => 
    lastExport.value !== null
  )

  return {
    // State
    isInitialized: readonly(isInitialized),
    error: readonly(error),
    isProcessing: readonly(isProcessing),
    lastOperation: readonly(lastOperation),
    lastImport: readonly(lastImport),
    lastExport: readonly(lastExport),

    // Computed
    isNativeAvailable,
    isReady,
    hasRecentImport,
    hasRecentExport,

    // Methods
    initialize,
    exportBusinessCards,
    importBusinessCards,
    listFiles,
    deleteFile,
    createDirectory,
    shareExportedFile,
    getStorageStats,
    cleanupTempFiles
  }
}

// Export types for external use
export type {
  FileSystemOptions,
  FileInfo,
  ExportOptions,
  ImportOptions,
  ImportResult,
  ExportResult,
  DirectoryContent
}